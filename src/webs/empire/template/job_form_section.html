
<div id="form_wrapper_job">

    <h2 class="job_post_title">{% if jobs_blocks %}{{ T_candidatura_espontanea }}{% else %}{{ T_trabaja_nosotros }}{% endif %}</h2>

    <div class="job_fields_wrapper">
        <form action="" class="work_suscribe_form">
            <div class="input_element">
                <label for="name_input">{{ T_nombre_y_apellidos }}</label>
                <input type="text" name="name" id="name_input">
            </div>

            <div class="input_element">
                <label for="email_input">{{ T_email }}</label>
                <input type="text" name="email" id="email_input">
            </div>

            <div class="input_element">
                <label for="confirm_email_input">{{ T_confirm_email }}</label>
                <input type="text" name="confirm_email" id="confirm_email_input">
            </div>

            <div class="input_element">
                <label for="contact_input">{{ T_telefono }}</label>
                <input type="text" name="telephone" id="contact_input">
            </div>

            <div class="input_element">
                <label for="vacant_input">{{ T_puesto_deseado }}</label>
                <input type="text" name="vacant_posts" id="vacant_input" {% if not jobs_blocks and content_subtitle %}value="{{ content_subtitle.subtitle|safe }}"{% endif %}>
            </div>

            <div class="input_element">
                <label for="comments_input">{{ T_comentarios }}</label>
                <textarea type="text" name="comments" id="comments_input"></textarea>
            </div>

            <div class="input_element">
                <label for="">C.V.</label>
                <input type="file" name="file" id="file_cv">
            </div>

            <div class="input_element">
                <input type="checkbox" name="privacy" id="privacy_checkbox">

                <a data-fancybox="" data-options="{'caption' : '{{ T_politica_privacidad }}', 'src' : '/{{ language }}/?sectionContent=politica-de-privacidad.html', 'type' : 'iframe', 'width' : '100%', 'max-width' : '100%'}" data-width="1200" class="myFancyPopup fancybox.iframe jobs_policies_link newsletter_popup" href="/{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">
                    <label>{{ T_lopd }}</label>
                </a>

            </div>
        </form>
    </div>

    <div class="buttons_wrapper">
        <div class="submit_send_button">{{ T_enviar }}</div>
    </div>

    <input type="hidden" id="thanks_job" value="{{ T_gracias_contacto }}">

</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script async src="/js/{{ base_web }}/work_with_us.js?v=1.1"></script>

{% if not jobs_blocks %}
    <style>
        .content_subtitle_wrapper .only_preview {
            display: none;
        }
    </style>
{% endif %}

<script>
    $("form.work_suscribe_form").validate({
        rules: {
            name: "required",
            email: {
                required: true,
                email: true
            },
            confirm_email: "required",
            telephone: "required",
            privacy: "required",
            contact: "required",
            file: "required"
        }, highlight: function (element) {
            $(element).addClass('input-error');
        }, unhighlight: function (element) {
            $(element).removeClass('input-error');
        }, messages: {
            name: "{{ T_campo_obligatorio }}",
            email: {
                required: "{{ T_campo_obligatorio }}",
                email: "{{ T_campo_valor_invalido }}"
            },
            confirm_email: "{{ T_campo_obligatorio }}",
            telephone: "{{ T_campo_obligatorio }}",
            privacy: "{{ T_campo_obligatorio }}",
            contact: "{{ T_campo_obligatorio }}",
            file: "{{ T_campo_obligatorio }}"
        }
    });
</script>