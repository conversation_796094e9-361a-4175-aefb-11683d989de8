<article class="entry">
    <h1 class="entry_title">{{ entry.name|safe }}</h1>
    <div class="entry_info">
        <div class="date"><span><i class="fa icon-calendar2"></i>{{ entry.creationDate|safe }}</span></div>
        {% if entry.author %}<div class="author"><span><i class="fa fa-user-o"></i>{{ entry.author|safe }}</span></div>{% endif %}
        <div class="comments"><span><i class="fa fa-comment-o"></i><b>{{ entry.comments|length }}</b></span></div>
    </div>
    <div class="entry_picture">
        <img src="{{ entry.picture|safe }}{% if 'googleusercontent' in item.picture %}=s1140{% endif %}" alt="">
    </div>
    <div class="tags_wrapper">
        <span class="tags">{{ entry.tags|safe }}</span>
    </div>
    <div class="entry_content">{{ entry.description|safe }}</div>
    <div class="entry_share">
        <div class="addthis_inline_share_toolbox_4w52"></div>
    </div>
    <div class="entry_comments">
        <h3>{{ T_comentarios }}</h3>
        {% if entry.comments|length > 0 %}
            <div class="comment_list">
                {% for comment in entry.comments %}
                    <div class="comment">
                        <div class="comment_pic"><img src="{{ comment.pic|safe }}=s100"></div>
                        <div class="name"><span>{{ comment.name|safe }}</span>:</div>
                        <div class="text">{{ comment.description|safe }}</div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        <div class="comment_form">
            <form name="fancy_contact_form" id="fancy_contact_form" method="post" action="/utils/?action=contact">
                <input type="hidden" name="section" id="section-name" value="Comment in {{ sectionName|safe }}"/>

                <div class="text_element">
                    <label for="comments" class="title">{{ T_comentarios }}</label>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""></textarea>
                </div>

                {% if destination_address %}
                    <input type="hidden" name="destination_email" id="destination_email"
                           value="{{ destination_address }}">
                {% endif %}

                <div class="input_element">
                    <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                    <input type="text" id="name" name="name" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="email" class="title">{{ T_email }}</label>
                    <input type="text" id="email" name="email" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="telephone" class="title">{{ T_telefono }}</label>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
                </div>

                <div class="check_element">
                    <input class="check_privacy" id="privacy" name="privacy" type="checkbox"
                           value="privacy"/>
                                <span class="title"><a href="/{{ language }}/?sectionContent=politica-de-privacidad.html"
                                                       class="myFancyPopup fancybox.iframe">{{ T_lopd }}</a></span>
                </div>

                <div id="contact-button-wrapper">
                    <div id="popup_form_button">
                        {{ T_enviar }}
                    </div>
                </div>

                <div class="thanks_popup_wrapper" style="display: none">{{ T_gracias_contacto }}</div>
            </form>
            <script type="text/javascript" src="/static_1/lib/jquery.validate.js" async></script>
            <script type="text/javascript">
                $(window).load(function () {
                    jQuery.validator.addMethod("phone", function (phone_number, element) {
                        phone_number = phone_number.replace(/\s+/g, "");
                        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                                phone_number.match(/^[0-9 \+]\d+$/);
                    }, "Please specify a valid phone number");

                    $("#fancy_contact_form").validate({
                                                          rules: {
                                                              name: "required",
                                                              privacy: "required",
                                                              email: {
                                                                  required: true,
                                                                  email: true
                                                              },
                                                              telephone: {
                                                                  required: true,
                                                                  phone: true
                                                              },
                                                              comments: "required"
                                                          },
                                                          messages: {
                                                              name: "{{ T_campo_obligatorio}}",
                                                              privacy: "{{ T_campo_obligatorio }}",
                                                              email: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  email: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              telephone: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  phone: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              comments: "{{ T_campo_obligatorio|safe }}"
                                                          }

                                                      });

                    $("#popup_form_button").click(function () {


                        if ($("#fancy_contact_form").valid()) {
                            $.post(
                                    "/utils/?action=contact",
                                    {
                                        'name': $(".input_element #name").val(),
                                        'telephone': $(".input_element #telephone").val(),
                                        'email': $(".input_element #email").val(),
                                        'comments': $(".input_element #comments").val(),
                                        'section': $("#section-name").val()
                                    },

                                    function (data) {
                                        $(".input_element #name").val("");
                                        $(".input_element #telephone").val("");
                                        $(".input_element #email").val("");
                                        $(".input_element #comments").val("");
                                        $(".thanks_popup_wrapper").fadeIn("slow");
                                    }
                            );
                        }
                    });
                })
            </script>
        </div>
    </div>
</article><aside class="right_column">

<h3 class="widget_title"><span>{{ T_noticias }}</span></h3>
<div class="news_right_widget">
    {% for item in news_widget %}
        <div class="news_widget_entry">
            <div class="image">
                <a href="/{{ path }}/{{ item.friendlyUrl }}"><img src="{{ item.picture|safe }}=s200" alt=""><span class="plus"></span></a>
            </div><div class="content">
                <div class="title"><a href="/{{ path }}/{{ item.friendlyUrl }}">{{ item.name|safe }}</a></div>
                <div class="links">
                    <div class="date"><span>{{ item.creationDate|safe }}</span></div>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

{% if social %}
<h3 class="widget_title"><span>{{ T_siguenos_en }}</span></h3>
<div class="social_widget">
            {% if social.facebook_id %}<a href="http://www.facebook.com/{{ social.facebook_id }}" target="_blank"><i class="fa fa-facebook" aria-hidden="true"></i></a>{% endif %}
            {% if social.twitter_id %}<a href="https://twitter.com/#!/{{ social.twitter_id }}" target="_blank"><i class="fa fa-twitter" aria-hidden="true"></i></a>{% endif %}
            {% if social.google_plus_id %}<a href="https://plus.google.com/u/0/{{ social.google_plus_id }}" target="_blank" rel="publisher"><i class="fa fa-google-plus" aria-hidden="true"></i></a>{% endif %}
            {% if social.youtube_id %}<a href="https://www.youtube.com/{{ social.youtube_id }}" target="_blank"><i class="fa fa-youtube" aria-hidden="true"></i></a>{% endif %}
            {% if social.pinterest_id %}<a href="http://es.pinterest.com/{{ social.pinterest_id }}" target="_blank"><i class="fa fa-pinterest-p" aria-hidden="true"></i></a>{% endif %}
            {% if social.instagram_id %}<a href="http://www.instagram.com/{{ social.instagram_id }}" target="_blank"><i class="fa fa-instagram" aria-hidden="true"></i></a>{% endif %}
</div>
{% endif %}
</aside>
<script type="text/javascript" src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-5a1825aff91cd51e"></script>