# -*- coding: utf-8 -*-

from collections import OrderedDict

from booking_process.libs.communication import directDataProvider
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2

import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "elsls"

class TemplateHandler(BaseTemplateHandler2):

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).getBookingWidgetOptions(language)
		#options['caption_submit_book'] = True

		return options

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		context = {
			'booking_header': get_section_from_section_spanish_name('booking_header', language)

		}
		context['booking_header_promocode'] = self.getSectionAdvanceProperties(context['booking_header'], language).get('promocode')
		options['custom_title_html'] = buildTemplate('%s/template/header_booking.html' % '/'.join(os.path.abspath(__file__).split("/")[:-1]), context)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		template_path = 'booking/booking_engine_5/_booking_widget.html'
		return self.buildTemplate(template_path, params, allowMobile=False)

	def buildRoomsFromSections(self, language):

		all_rooms =  get_pictures_from_section_name("habitaciones blocks", language)

		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures'] = get_pictures_from_section_name(sect_gallery, language)
			room['name'] = room['title']

		return all_rooms

	def buildPromotions(self, language):
		all_offers = self.buildPromotionsInfo(language)
		for promo in all_offers:
			# if x["name"]:
			# 	x['name'] = x['name'].split("@")[0]

			if promo['name']:
				field = promo['name'].split("@")
				try:
					promo["promo_title"] = field[0]
					promo["promo_subtitle"] = field[1]
					promo["promo_note"] = field[2]
				except:
					promo["promo_title"] = field[0]
					promo["promo_subtitle"] = ""

		return all_offers


	def get_opinions(self, language):
		opiniones = {}
		opinions_section = get_section_from_section_spanish_name('opiniones', language)
		opinions = get_pictures_from_section_name('opiniones', language)
		opinion_link = get_section_from_section_spanish_name('opiniones', language)
		value = 0

		for opinion in opinions:
			if opinion['title']:
				for field in opinion['title'].split("@"):
					if 'name' in field:
						opinion['name'] = field.split("=")[1]
					if 'grade' in field:
						opinion['grade'] = field.split("=")[1]
						value += float(opinion['grade'].replace(",", "."))
					if 'channel' in field:
						opinion['channel'] = field.split("=")[1]


		if opinions_section:
			opiniones['content'] = opinions_section['content']
			opiniones['title'] = opinions_section['title']
			opiniones['subtitle'] = opinions_section['subtitle']

		if len(opinions) > 0:
			opiniones['opiniones'] = opinions
			opiniones['cantidad'] = len(opinions)
			opiniones['media'] = value / len(opinions)
			opiniones['media'] = round(opiniones['media'],1)

		if opinion_link:
			opiniones['link'] = opinion_link['friendlyUrlInternational']

		return opiniones

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {
				'base_web': base_web,
				'footer_columns': get_pictures_from_section_name("footer columns", language),
				'connect': get_section_from_section_spanish_name("conectados", language),
				'promotions': self.buildPromotions(language),
			    'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
			    'newsletter_info': get_section_from_section_spanish_name("Newsletter", language),
				'promotions_title': get_section_from_section_spanish_name("ofertas", language),
				'opinions': self.get_opinions(language),
				'logo_trip': get_pictures_from_section_name("logos_tripadvisor", language),
				'logo_trip_title': get_section_from_section_spanish_name("logos_tripadvisor", language),
				'booking_engine_2': self.buildSearchEngine2(language)
		}

		#booking header
		booking_header_horizontal = get_section_from_section_spanish_name("booking_header_horizontal", language)
		booking_header_subtitle = booking_header_horizontal.get("subtitle")
		promocode_header = self.getSectionAdvanceProperties(booking_header_horizontal, language).get("promocode")
		result_params_dict['booking_header_horizontal'] = {'subtitle': booking_header_subtitle, 'promocode': promocode_header}

		#banner ventajas
		banner_ventajas = get_section_from_section_spanish_name('_banner_ventajas', language)
		if banner_ventajas:
			result_params_dict['banner_ventajas'] = banner_ventajas
		all_advantages_pics = get_pictures_from_section_name('_banner_ventajas', language)
		banner_ventajas_pics = list(filter(lambda x: x.get('description') != 'background', all_advantages_pics))
		if banner_ventajas_pics:
			result_params_dict['banner_ventajas_pics'] = banner_ventajas_pics
		background_pic = list(filter(lambda x: x.get('description') == 'background', all_advantages_pics))
		if background_pic:
			result_params_dict['advantages_background'] = background_pic

		if section_name == 'inicio' or section_type == 'Inicio':
			result_params_dict['datos_ini'] = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
			result_params_dict['content_ini'] = True
			result_params_dict["bottom_popup"] = get_section_from_section_spanish_name("popup inicio footer", language)
			result_params_dict["bottom_popup_text"] = get_section_from_section_spanish_name("promocion pop up", language)

		if section_type != 'Inicio':
			result_params_dict['innersection'] = True

		if section_type != 'Ofertas':
			result_params_dict['promo_banners'] = True
			offersLink = get_section_from_section_spanish_name('ofertas', language)
			result_params_dict['offers_link'] = offersLink.get('friendlyUrlInternational')

		#Content by Subtitle
		actual_section = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
		if actual_section.get('subtitle', False):
			result_params_dict['content_subtitle'] = actual_section

		#Rooms
		if section_name == 'habitaciones' or section_type == 'Habitaciones':
			result_params_dict['room_section'] = True

			habitaciones = get_pictures_from_section_name('habitaciones blocks', language)
			for x in habitaciones:
				link = x['linkUrl']
				x['pictures'] = get_pictures_from_section_name(link, language)

			for habitacion in habitaciones:
				img_key = habitacion['key']
				habitacion["tipo"] = ""

				if img_key:
					all_properties_img = directDataProvider.get("WebPageProperty", {"entityKey": img_key, "languageKey": language})
					for myProperty in all_properties_img:
						if myProperty.value:
							if myProperty.mainKey.lower().strip() == "tipo":
								habitacion['tipo'] = myProperty.value
							if myProperty.mainKey.lower().strip() == 'link':
								habitacion['special_link'] = myProperty.value

			result_params_dict['hoteles'] = habitaciones


		#Promotions
		if section_type == 'Ofertas' or section_name == 'ofertas':

			promos = self.buildPromotionsInfo(language)
			for promo in promos:
				if promo['name']:
					field = promo['name'].split("@")
					try:
						promo["promo_title"] = field[0]
						promo["promo_subtitle"] = field[1]
					except:
						promo["promo_title"] = field[0]
						promo["promo_subtitle"] = ""


			result_params_dict['blocks'] = promos

		if section_name == 'opiniones':
			result_params_dict['opinion_page'] = False

		#Content Sections
		content_sections = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True,
			'Atencion al cliente': True
		}

		if section_type != 'Inicio':
			result_params_dict['innersection'] = True

		if content_sections.get(sectionToUse.get('sectionType', ''), False):
			result_params_dict['content_acess'] = True

		if section_name == "entorno" or section_type == "Extra 1":
			result_params_dict['ent_blocks'] = get_pictures_from_section_name(section_name, language)
			result_params_dict['pictures'] = get_pictures_from_section_name('slider_'+section_name, language)

		if section_name == "opiniones":
			result_params_dict['pictures'] = get_pictures_from_section_name('slider_opiniones', language)

		if section_type == "Galeria de Imagenes":
			result_params_dict['pictures'] = get_pictures_from_section_name('slider_gallery', language)

		if section_name == "agenda":
			all_events =  get_pictures_from_section_name("agenda blocks", language)

			for event in all_events:

				dayEvent = ""
				monthEvent = ""
				yearEvent = ""

				if event['title']:

					splitDate = event['title'].split("/")

					if len(splitDate)==3:

						dayEvent = splitDate[0]
						monthEvent = splitDate[1]
						yearEvent = splitDate[2]


						dictionary = eval('datesDictionary.dates_%s' % language)
						monthNamesShort = dictionary.get("monthNamesShort","")

						if monthNamesShort:
							monthEvent=monthNamesShort[int(monthEvent)-1]


				event['dayEvent'] = dayEvent
				event['monthEvent'] = monthEvent
				event['yearEvent'] = yearEvent


			result_params_dict['all_events'] = all_events

		#Localizacion y contacto
		if section_type == u'Localización':
			additionalParams4Contact = {}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''

			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = get_section_from_section_spanish_name(sectionToUse.get("sectionName", ""), language)
			result_params_dict['subtitle_form'] = result_params_dict['location_html'].get('subtitle', '')
			result_params_dict['content_subtitle'] = None
			result_params_dict['localizacion_access'] = True
			result_params_dict['iframe_google_map'] = get_section_from_section_spanish_name('Iframe google maps', language)

		return result_params_dict

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:
				if not filters.get(x.get('title', ''), False):
					filters[x.get('title', '')] = [x['servingUrl']]
				else:
					filters[x.get('title', '')].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)




	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		sectionToUse = self.getSectionParams(sectionFriendlyUrl, language)
		base_path = os.path.dirname(__file__)
		resultParams = {}


		if sectionToUse:
			name_current_section = sectionToUse['sectionName'].lower().strip()
			type_current_section = sectionToUse['sectionType']
			advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

			if user_agent_is_mobile():

				if advance_properties.get('events', False):
					events_blocks = advance_properties['events'].split("@@")
					events_sections = []
					for x in events_blocks:
						events_sections = get_pictures_from_section_name(x, language)

					resultParams['events_blocks'] = events_sections


				if type_current_section == 'Normal' or type_current_section == 'Extra 1':
					resultParams['section_params'] = sectionToUse
					fullPath = os.path.join(base_path, 'template/default_template.html')
					result = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()) + list(resultParams.items()))
					return buildTemplate(fullPath, result)


		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)