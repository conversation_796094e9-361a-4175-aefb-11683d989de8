
<section id="top_content">
    <div class="top_content_wrapper container12">
{#    <h3 class="section_title">{{ sectionName|safe }}</h3>#}
        <div class="scapes-blocks">
            {% for block in blocks %}
                <div class="block {% cycle 'row1' 'row2' %}">
                    <div class="description">
                        <h3 class="title-module">{{block.promo_title|safe}}</h3>
                        <h4>{{block.promo_subtitle|safe}}</h4>

                        <ul>
                            <li>
                                <a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto offer-btn">
                                    <span class="ver_mas">{{ T_ver_mas }}</span>
                                </a>
                                <a href="#data" class="plus reservation button-promotion">
                                    <span class="ver_mas">{{ T_reservar }}</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %}">
                        <img src="{{block.picture}}=s1000" alt="{{ block.name|safe }}" title="{{ block.name|safe }}">
                    </a>
                </div>

                <div id="event-modal-{{ forloop.counter }}" class="promo-modal" style="display: none">
                    <h3 class="title-module">{{block.promo_title|safe}}</h3>
                    {{block.description|safe}}
                </div>

            {% endfor %}
        </div>
    </div>
</section>

<script type="text/javascript">
    elems = document.getElementsByClassName('escapadas-popup');

    $(elems).each( function (index) {
        $(this).attr('id', $(this).parent().find('.offer-btn').attr('href').substr(1));
    });
</script>