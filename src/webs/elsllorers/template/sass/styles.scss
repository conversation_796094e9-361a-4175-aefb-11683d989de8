@import "compass";

@import "defaults";
@import "booking/selectric";
@import "fonts";
@import "plugins/iconmoon";
@import "template_specific";



.new_booking_inline {
  @import "booking/booking_engine_2";

  /*====== Booking Widget Inline =====*/
  .booking_widget.inline {
    position: relative;
    top: 0;
    height: auto;
    padding-bottom: 13px;
    display: none;

    .selectric .label {
      color: #787878;
    }

    h4.booking_title_2 {
      display: block;
      font-family: yanone, sans-serif;
      font-weight: 300;
      font-size: 34px;
      margin-top: 20px;
      text-transform: capitalize;
      color: white;
      margin-top: 45px;
      margin-left: 30px;
    }

    .destination_wrapper {
      position: absolute;
      width: 255px;
      left: 0;
      top: 75px;
    }

    .entry_date_wrapper {
      margin-right: 10px;
      width: 100px !important;
    }
    .departure_date_wrapper {
      margin-right: 10px;
      width: 100px !important;
    }

    .room_list_wrapper {
      position: relative;
      display: inline-block;
    }

    li.room.room2, li.room.room3, li.room.room4 {
      margin-top: 25px;
    }

    .room_title {
      float: left !important;
      width: auto !important;
      display: block !important;
      top: 47px !important;
      position: relative !important;
    }

    .room {
      padding-left: 0px;
    }

    .wrapper_booking_button {
      float: right;
      width: 204px;
      display: inline-block;
      padding-top: 4px;
    }

    button.submit_button {
      position: absolute;
      height: 55px;
    }

    .wrapper_booking_button .promocode_text {
      color: white;
      position: relative;
      width: 138px;
      font-size: 13px;
      text-decoration: none;
      border-bottom: 1px solid white;
      padding-bottom: 4px;
      display: none;
    }

    .wrapper_booking_button .promocode_text strong {
      color: white;
    }

    .promocode_input {
      position: relative;
      height: 50px;
      display: inline-block !important;
      background: #eaebed;
      font-size: 13px;
      resize: none;
      margin-left: 8px;
      width: 229px !important;
      border-radius: 0!important;
    }

    .hotel_selector {
      z-index: 20;
      left: 0px;
      top: 125px;
      border: 2px solid #C5C330;
    }

    .booking_form {
      background: none;
      float: right;
      padding-top: 0px;
      width: 798px;
      height: auto;
      padding: 0;
      padding: 25px 0;
    }

    .booking_form_title {
      width: auto;
      float: left;
      margin-top: 22px;

      &:before {
        display: none;
      }
    }

    h4.best_price {
      display: none;
    }

    .booking_title_custom {
      font-size: 22px;
      font-weight: 400;
      font-family: 'Roboto', serif;
    }

    .date_box {
      height: 110px;
      border-radius: 0!important;
    }

    .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
      width: 78px;

    }
    .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label {
      color: white;
      text-align: center;
      margin: 0px 0px 13px;
    }

  }

  .en .booking_widget.inline .booking_title_custom {
    font-size: 20px;
  }

  ul.all_hotels.num-ul-4 {
    width: 100%;
    bottom: 0;
    display: block;
    clear: both;

    h3.title_selector {
      float: right;
      background: #C5C330;
      cursor: pointer;
      border-radius: 0px;
      font-weight: lighter;
      text-transform: capitalize;
      font-size: 18px;
      font-family: "Source Sans Pro", sans-serif;
      background-color: #a7a541;
      color: white;
      padding: 4px 15px;
      filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr='#FFA7A541', endColorstr='#FFA7A541');
      background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgi…gd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmFkKSIgLz48L3N2Zz4g");
      background-size: 100%;
      background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #a7a541), color-stop(50%, #c5c42e), color-stop(100%, #a7a541));
      background-image: -moz-linear-gradient(top, #a7a541 0%, #c5c42e 50%, #a7a541 100%);
      background-image: -webkit-linear-gradient(top, #a7a541 0%, #c5c42e 50%, #a7a541 100%);
      background-image: linear-gradient(to bottom, #a7a541 0%, #c5c42e 50%, #a7a541 100%);
      margin-top: 8px;
    }
  }

  #wrapper_booking.inline {
    background: $corporate_1;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 400;
    height: auto;

    .booking_widget.inline {
      .booking_form_title {
        margin-top: 22px;
        background: none;
        margin-right: 40px;

        h4 {
          color: white;
          margin-top: 45px;
          margin-left: 30px;
        }
      }

      .date_box {
        background: #eaebed;

        &:before {
          display: none;
        }

        .date_day {
          .day {
            font-size: 45px;
            margin-top: 7px;
            padding-bottom: 0px;
            border-bottom: 1px solid #797979;
            font-weight: bolder;
            line-height: 50px;
            color: #787878;
          }

          .month {
            padding-top: 8px;
            border-bottom: 1px solid #797979;
            font-size: 14px;
            color: #787878;
          }
        }
      }
    }

    .booking_form .stay_selection {
      display: block;
    }

    .selectricWrapper .selectric {
      background: #eaebed;
      height: 110px !important;
      width: 88px !important;
      border-radius: 0!important;
    }

    .booking_widget.inline .stay_selection .entry_date_wrapper label, .booking_widget.inline .stay_selection .departure_date_wrapper label, .booking_widget.inline .stay_selection .rooms_number_wrapper label, .booking_widget.inline .room .room_title, .booking_widget.inline .room .adults_selector label, .booking_widget.inline .room .children_selector label {
      color: white;
      margin-bottom: 2px !important;
    }

    .selectric:before {
      display: none !important;
    }

    .entry_date_wrapper, .departure_date_wrapper {
      width: 88px !important;
    }

    .stay_selection label {
      margin-bottom: 2px !important;
    }

    .booking_widget .rooms_number .selectric {
      height: 110px !important;
      width: 88px !important;
    }

    .stay_selection .rooms_number_wrapper {
      width: 88px;
    }

    .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper, .selectricWrapper {
      margin-right: 10px;
      width: 88px;
    }

    .room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
      width: 88px;
    }

    .room_title {
      padding-top: 19px;
      padding-right: 15px;
      padding-left: 20px !important;
    }

    .booking_widget .selectric .button, #data .selectric .button {
      display: none !important;
    }

    .selectric .label {
      margin-left: auto !important;
      left: 0;
      right: 0;
      font-size: 47px !important;
    }

    .booking_form .wrapper_booking_button button {
      height: 50px;
      width:229px;
      font-size: 18px;
      margin-top: 15px;
      margin-left: 8px;
      background: $corporate_2;
      border-radius: 0!important;
    }

    .room .adults_selector {
      margin-right: 10px;
    }
    span#full-booking-engine-html {
      margin: auto !important;
    }
  }

  #ui-datepicker-div {

  }
}

.new_wrapper_booking #wrapper_booking {
  top: 115px!important;
}



.new_wrapper_booking, #data {
  @import "booking/booking_engine_5";

  .date_box .date_year {
      display: none;
  }

  .promocode_input {
    background: none;
  }

  .promocode_text {
    display: none;
  }

    /*======== Booking Widget =======*/
  div#wrapper_booking {
    position: absolute;
    height: auto;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index:35;
  }
  .booking_widget{
    position:absolute;
    left:0px;
  }

  #full-booking-engine-html-5{
    width:299px;
  }

  #full-booking-engine-html-5 form.booking_form{
    background:white;
  }

  .booking_form_title .best_price{
    display:none;
    color:white;
    font-size:16px;
    padding:20px;
    font-weight:600;
    text-align: center;
  }
  .promocode_header p.first_offer_name{
    color:white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background:transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    width: 49.8%;
  }

  button.submit_button {
    background: #ee9034!important;
    color: white !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1{
    line-height: 15px!important;
  }

  .wrapper-new-web-support.booking_form_title{
    background: gray!important;
  }

  #full-booking-engine-html-5{
    margin-top: 20px!important;
  }

  #data #full-booking-engine-html-5{
    margin-top: 0!important;
  }

  .date_box.entry_date{
    margin-top:6px;
  }
  .selectricWrapper .selectric{
    margin-top:0px;
  }

  #slider_inner_container #full-booking-engine-html-5{
    margin-top: -17px!important;
  }
}

#data #full-booking-engine-html-5 {
  margin-top: 0!important;
}
