

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default{
    border: 1px solid white!important;
}
.ui-datepicker-title{
  color: white!important;
}
.ui-widget-header {
  background: $corporate_2!important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_2!important;
  color: white;
}

body{
  font-family: "gotham";
  font-size:14px;
}
header{
  padding-bottom:10px;
  background:$corporate-2;
}
#logoDiv{
  margin-top:20px;
}

#social,
#top-sections,
#lang,
.weather-block{
  float:right;
  margin-left:20px;
}
#top-sections,
#lang{
  margin-top:20px;

  a{
    color:white;
    text-decoration:none;

    &:hover{
      color:white;
      text-decoration:none;
    }
  }
}
#social{
  margin-top:12px;
}
#top-sections a{
  font-size:12px;
  font-weight:700;
  color:white;
  text-decoration:none;

  &:hover{
      color:white;
      text-decoration: underline;
    }
  &#section-active{
    color:white;
    text-decoration: underline;
  }
}

.weather-block {
  background: none;
  color: white;
  padding: 2px 5px 2px 5px;
  margin-top: 13px;

  img {
    height: 22px;
  }

  .weather-text {
    float: left;
    margin-top: 7px;
    margin-right: 10px;
  }

  span.weather-location {
    border-right: 1px solid white;
    padding-right: 5px;

  }
}


/*MAIN MENU */

#main_menu {
  clear:both;
  padding-top:20px;
  border-top:1px solid white;
  margin-top:65px;
}

#mainMenuDiv {

}

#mainMenuDiv a {
  padding: 5px 8px 5px;
  display: inline-block;
  font-family:"roboto slab";
  font-size: 14px;
  text-transform: uppercase;
  color: white;
  line-height: 37px;
  font-weight: 400;
  text-decoration: none;
  margin-top: -21px;
  border-top: 3px solid transparent;
}

#mainMenuDiv a:hover {
  border-top: 3px solid white;
}

#section-active a {
  border-top: 3px solid white;
  padding: 5px 8px 5px;
  text-decoration: none;
}

#main-sections{

}
#main-sections-inner{
  height:30px;
}
#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;

}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

#main-menu-book-button a {
  color: $corporate-2 !important;
  padding-right: 0px !important;
}

/*=== Slider ===*/
.tp-bullets {
  display: none !important;
}

.slider_inner_container .slider_image {
  width: 100%;
  height: auto;
  position: fixed;
  top: 0px;
  z-index: -2;
  min-width: 1920px;
}

.slider_inner_container {
  height: 500px;
}

.slider_text {
  width: 750px;
  position: absolute;
  color: white;
  top: 148px;
  left: 370px;

  h1 {
    font-size: 57px;
    line-height: 66px;
    text-transform: uppercase;
    font-family: 'nexaregular';
    font-weight: lighter;
    text-shadow: 1px 0 8px #323232;
  }

  .caption.sfb.start {
    margin-left: 5px;
  }

  .caption.sfb.start:first-of-type {
    position: absolute;
    z-index: -2;
    margin-left: 0px;
  }

  .caption.sfb.start:last-of-type {
    margin-top: 14px;
  }

}

@-moz-document url-prefix() {
  .selectric .label {
    top: -2px;
  }

  .rooms_number .selectric .label {
    top: 23px;
  }

  .official_web {
    margin-top: 12px;
  }
}

/*=============== Slider ================*/
.flex-ini{
  position:relative;
}
.flexsli-ini iframe{
  height:600px;
}
.slider_text{
  position: absolute;
  left: 480px;
  right: 0px;
  top: 200px;

  width: 900px;
  height: 100px;
  z-index: 2;
  margin: auto;
  color: white;
  text-transform: uppercase;
  font-size: 77px;
  font-weight: 200;
  text-align: left;
  line-height: 65px;
}

/*=============== Booking Widget ================*/

div#wrapper_booking {
  position: absolute;
  left: 0px;
  right: 0px;
  top: 75px;
}

.spinner_wrapper {
  text-align: center;
  position: relative !important;
  width: 25px;
  height: 25px;
  margin: auto;
  right: 0px !important;
  bottom: 0px !important;
  padding: 15px;
  display: none;
}

.interior #wrapper_booking {
  height: 500px;
  bottom: auto;
}

.fancybox-inner {
  overflow: visible !important;
}

.border_booking {
  padding: 0px 10px 10px;
  height: auto !important;
  width: 370px !important;
  box-sizing: border-box;
  background: white;
}

#data {
  height: auto !important;
}

/******************TICKS***************/
.ticks-wrapper{
  background: white;
}
#ticks_container {
  text-align: center;
  padding-top: 20px;
  padding-bottom: 20px;
  position: relative;
  background:white;
}

#ticks_container span:before, #ticks_container span:after {
  border-top: 1px solid $gray-3;
  display: block;
  height: 1px;
  content: " ";
  width: 17%;
  position: absolute;
  left: 0;
  top: 2em;
}

#ticks_container span:after {
  right: 0;
  left: auto;
}

.ticks {
  display: inline-block;
  width: 202px;
  text-align: left;
  line-height: 18px;
  text-transform: uppercase;
  padding-left: 25px;
  font-size: 12px;
  color: $gray-2;
  font-weight: lighter;
  margin: 0 5px;
}

#tick1 {
  background: url("/img/elsls/pago-directo.png") no-repeat 0;
}

#tick2 {
  background: url("/img/elsls/sin-gastos.png") no-repeat 0;
}

#tick3 {
  background: url("/img/elsls/pago-seguro.png") no-repeat 0;
}

.nl #tick1 {
  width: 150px;
}


/******************CONTENT***************/

#content{
  padding:40px 0px;
  text-align:center;
  background:white;

  .content_subtitle_wrapper{
    margin:0 auto;
    width:800px;
  }

  .content_subtitle_title{
    font-family:"roboto slab";
    font-size:36px;
    font-weight:300;
    color:$corporate_2;
    margin-bottom:5px;

  }
  .content_subtitle_description h3{
    text-transform:uppercase;
    font-size:16px;
    margin-bottom:10px;
  }
  .content_subtitle_description>span{
    display:inline-block;
    width:80px;
    border:2px solid $corporate_2;
    margin-bottom:10px;
  }

  .content_subtitle_description{
    line-height:23px;
    font-size:13px;
  }
}

/******************CAORUSEL BANNERS***************/

.banners-carrousel{
  background:$gray-4;
  padding:40px 0px;

  h3.content_subtitle_title{
    font-family:"roboto slab";
    font-size:36px;
    font-weight:300;
    color:$corporate_2;
    margin-bottom:20px;
    text-align: center;
  }
}
.promotion-banner{
  float: left;
  position:relative;

  h4{
    position:absolute;
    bottom:-12px;
    padding:10px 0px;
    text-align:center;
    font-size:20px;
    font-family: "Roboto Slab", sans-serif;
    background:white;
    width:100%;
    color:$gray-1;
  }
  img {
    height: 200px;
    max-height: 200px;
    width: 450px;
    object-fit: cover;
  }
}
.bxslider-wrapper{
  position:relative;

  .bx-wrapper{
    margin-bottom:20px;
  }

  .bx-viewport{
    background:none;
    border: none;
    height:215px !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
}
.overlay{
  background:rgba(50,50,50, .4);
  -webkit-transition: all .5s;
  -moz-transition: all .5s;
  -ms-transition: all .5s;
  -o-transition: all .5s;
  transition: all .5s;
  width: 374px;
  height: 166px;
  position: absolute;
  top: 0;
  z-index: 1;



  &:hover{
    background:rgba(50,50,50, 0);
  }
}

.note{
  position: absolute;
  top: 10px;
  right:10px;
  font-size:22px;
  font-family: "Roboto Slab", sans-serif;
  font-weight:700;
  color:white;
  background:$corporate-2;
  text-decoration:none;
  display: inline-block;
  padding: 8px 15px;
  z-index: 1;
}

/******************ADVANTAGES BANNERS***************/

.advantages-banner {
    color: white;

    img{
        &.background-pic {
            position: absolute;
            z-index: -1;
            min-width: 1900px;
        }
    }
    h3 {
        text-align: center;
        text-transform: uppercase;
        padding: 2em 0;
    }
    .advantages-grid {
        display: flex;
        justify-content: space-evenly;
        background: rgba(0,0,0,0.5);
        padding: 2em 0;
        li {
            text-align: center;
            span {
                font-size: 5em;
            }
            h4 {
                font-size: 1em;
                font-family: "Roboto Slab", sans-serif;
                color: white;
            }
        }
    }
    button {
        width: 10%;
        display: block;
        margin: 1.5em auto;
        padding: 1em 0;
        background: rgba(0,0,0,0.5);
        border: none;
        a {
            color: white;
            font-size: 14px;
            text-transform: uppercase;
            text-decoration: none;
        }
    }
}


/******************GRID BANNERS***************/

@mixin centerer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.grid-section{
  padding:70px 0px;
  background:white;
  overflow:hidden;
  
  .grid-wrapper{
    overflow: hidden;
  }

  a{
    display: block;
    width:33.3%;
    overflow: hidden;
    float: left;
  }
  a:last-child{
    width:66.6%;
  }

  .banners{
    float:left;
    width:100%;
    margin:0px !important;
    position:relative;
  }

  a .banner-bg{
      -webkit-transition: all .5s ease-in-out;
      -moz-transition: all .5s ease-in-out;
      -ms-transition: all .5s ease-in-out;
      -o-transition: all .5s ease-in-out;
      transition: all .5s ease-in-out;
      width:100%;
      height: 100%;
      vertical-align: bottom;
    }
    a:hover .banner-bg{
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }

  .banner-5{


    .banner-description{
      padding: 0px 100px;
      box-sizing: border-box;
    }

    img{

    }
    .icon{
      //display:none;
    }
  }
  .banners .banner-description,
  .banners p{
    position: absolute;
    color:white;
    width: 100%;
    text-align: center;
  }
  .banners .banner-description{
    top: 100px;
    font-family:"roboto slab";
    font-size:30px;
    z-index: 1;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .banners p{
    z-index:1;
    top:240px;
    font-style: italic;
    font-size:18px;
    top: 65%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .banners .icon{
    position: absolute;
    z-index: 1;
    @include centerer;
  }
}
.banner-5 .overlay_big{
  width:100%;
}
.overlay_big{
  background:rgba(50,50,50, .4);
  -webkit-transition: all .5s;
  -moz-transition: all .5s;
  -ms-transition: all .5s;
  -o-transition: all .5s;
  transition: all .5s;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: 1;

  &:hover{
    background:rgba(50,50,50, 0);
  }
}


/****************   ENTORNO  *****************/

.ent-blocks {
  overflow: hidden;
  margin: 40px 0;
}

.ent-blocks .block {
  background: #f4f4f4;
  padding-bottom: 15px;
  float:left;
  margin-bottom:20px;

  img {
    width: 366px;
  }
}

.ent-blocks h3 {
  color: white;
  font-size: 30px;
  background: $corporate-2;
  height: 90px;
  line-height: 90px;
  text-align: center;
  margin: 0px;
}

.ent-blocks .block-description {
  padding: 20px;
  height: 99px;
  overflow: hidden;
  margin-bottom: 30px;

  p {
    margin-bottom: 0px;
  }
}

.ent-blocks a {
  background: $gray-2;
  color: white;
  padding: 10px 20px;
  text-decoration:none;

  &:hover {
    background: $corporate-2;
  }

}

.popup-ent h3 {
  color: $corporate-1;
  text-align: center;
  font-size: 26px;
  margin-bottom: 20px;
}


/******************EVENTS***************/

.event-section{
  margin-top: 40px;
}
.know-more-text {
  line-height: 27px;
  font-size: 17px;
}

.event_wrapper {
  color: black;
  margin-bottom: 40px;
  margin-top: 10px;
  padding-bottom: 40px;
  border-bottom: 1px solid rgb(237, 237, 237);

  .event_img {
    width: 281px !important;
    margin: 0px 5px;
    .event_img img {
      width: 281px;
      height: 281px;
    }
  }

  .event_date {
    width: 120px;
    margin: 0px;

    .event_day, .event_month, .event_year {
      background: #7b7b7b;
      margin-bottom: 5px;
      text-align: center;
      color: white;
      padding: 13px 0px;
      font-size: 22px;
    }
    
    .event_img_small{
      height:110px;
      overflow: hidden;
    }
  }

  .event_main_info {
    width: 678px;
    background: #f3f3f3;
    height: 281px;
    padding: 30px;
    box-sizing: border-box;
    position: relative;
    margin: 0.5px 5px;

    .event-title {
      text-transform: uppercase;
      color: $corporate-2;
      font-size: 19px;
      margin-bottom: 15px;
      font-weight: bolder;
    }

    .event-description {
      line-height: 28px;
      font-size: 16px;
      font-weight: lighter;
      color: gray;
    }

    .event-buttons-wrappers {
      position: absolute;
      bottom: 40px;

      a {
        background: $corporate-2;
        padding: 19px 46px;
        text-decoration: none;
        color:white;
        font-size: 17px;

        &:hover {
          opacity: 0.7;
        }
      }
    }

  }

}


/******************OPINIONS BLOCK***************/

.opinion-section{
  padding:60px 0px;
  background:white;

  .bottom-block{
    width:520px;
    float:left;

    header{
      background:$gray-4;
      padding:20px;
      margin-bottom:0px;
      box-sizing:border-box;
      position: relative;

      img{
        position:absolute;
        right: 0px;
        bottom:-15px;
        z-index:1;
      }
    }

    header h3{
      font-family:"roboto slab";
      font-size:20px;
      text-transform:uppercase;
      color:$corporate_2;
      margin-bottom:5px;
    }

  }
  .bottom-block:first-child {
    margin-right: 100px;
  }

  ul{
    a{
      text-decoration:none;
      color:$gray-1;

      &:hover li{
        background:$gray-4;
      }
    }
    li{
      background: #f6f7f8;
      padding: 15px 0;
      margin-bottom:10px;
      position: relative;
      line-height:30px;
    }
    .number{
      color: white;
      background-color: #787878;
      padding: 10px 10px 9px;
      margin-right: 20px;
      margin-left: 15px;
      font-size: 14px;
    }
    .plus{
      display: inline-block;
      float: right;
      margin-right: 20px;
    }
  }

  .content-connect{
    background: #f6f7f8;
    padding:20px;
    line-height:27px;

    li{
      display: inline-block;
      margin-bottom:0px;
      margin-top:20px;
      padding:0px;
    }
  }
}
.opinions-total table .opinion-description{
  vertical-align: middle;
}
/*Widget tripadvisor */

#CDSWIDSSP{
  width:100% !important;
}

.widSSPData{
  padding:0px !important;
}
.widSSPBranding,
.widSSPH18,
.widSSPH11,
.widSSPLegal{
  display: none !important;
}
.widSSPBullet li{
  background: #f6f7f8 !important;
  padding: 15px 15px !important;
  margin-bottom:10px !important;
  position: relative !important;
  line-height:30px !important;
}

#CDSWIDSSP .widSSPData .widSSPOneReview .widSSPBullet li span.widSSPDate{
  display: inline-block !important;
  background: rgb(87, 142, 65);
  color: white !important;
  font-family: 'gotham';
  font-size: 14px;
  text-align: center;
  vertical-align: middle;
  padding: 6px;
}
#CDSWIDSSP .widSSPData .widSSPOneReview .widSSPBullet li span.widSSPQuote{
  font-size: 16px;
  font-family: 'gotham';
  display: inline-block !important;
  margin-left:20px;
}

#CDSWIDSSP .widSSPData .widSSPAll .widSSPReadReview li,
#CDSWIDSSP .widSSPData .widSSPAll .widSSPWriteReview li{
  font-size: 16px !important;
  font-family: 'gotham' !important;
}

#CDSWIDSSP .widSSPData .widSSPAll a:link,
#CDSWIDSSP .widSSPData .widSSPAll a:visited{
  text-decoration:none !important;
  font-size:16px !important;
  color:$corporate-2 !important;
}
#CDSWIDSSP .widSSPData .widSSPAll a:hover,
#CDSWIDSSP .widSSPData .widSSPAll a:hover{
  text-decoration:underline !important;
  font-size:16px !important;
  color:$corporate-2 !important;
}

/************* Tripadvisor logos *************/

.logo-trip-section{
  background:white;
  padding-bottom:40px;

  .content_subtitle_title{
    font-family: "roboto slab";
    font-size: 36px;
    font-weight: 300;
    color: #107c87;
    margin-bottom: 5px;
    text-align: center;
  }
  ul{
    margin-top: 40px;
    text-align: justify;
    justify-content: space-between;
  }
  ul:after {
      content: '';
      display: inline-block;
      width: 100%;
      height: 0;
  }
  li{
    display: inline-block;
    text-align: center;
  }
  li:nth-child(3){
    position: relative;
    top:63px;
  }
  li img{
    vertical-align: middle;
  }
}

/************* Rooms *************/
.hotel {
  background: #F7F6F6;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
  min-height: 360px;
}

.hotel-description {
  padding: 20px 20px;
  font-size: 14px;
  line-height:24px;
}

.hotel h3 {
  line-height: 16px;
  text-align: center;
}

.hotel-description .sub-description-hotel {
  margin-top: 10px;
}

.hotel-description .description-hotel {
  margin-top: 10px;
}

.hotel .hotel-links {
  margin-top:20px;

  a {
    color: white;
    background: $corporate_2;
    text-decoration: none;
    padding: 7px 18px;
    margin-right: 4px;

    &:hover{
      background: $corporate_1;
    }
  }


}

.hotel {
  .image-room-wrapper{
    width:550px;
    height: 250px;
    overflow: hidden;;
  }
  img {
    width: 100%;
    margin-top:-120px;

  }

  h3.title-module {
    text-transform: uppercase;
    text-decoration: none;
    color: $corporate_2;
    font-size: 17px !important;
    margin: 0px !important;
    margin-bottom: 15px !important;
    font-weight: 200;
    line-height: inherit !important;
    min-height: 48px;
  }

  .destino {
    margin-left: 10px;
    font-weight: 500;
  }

  .hotel-description-cutted{
    height: 50px;
    overflow: hidden;
  }

}

.texto_oculto {
  display: none;
}

.hotel.segmentos {
  padding-bottom: 20px;
  min-height: 0px;
}

.hotel.segmentos h3.title-module {
  text-align: center !important;
  font-weight: 400 !important;
}
.modal-room{
  h3{
    font-size:30px;
    color:$corporate-2;
    margin-bottom:10px;
  }
  p{
    line-height:24px;
  }
}

/************************* SCAPES ************************/
a.plus {
  padding: 10px 7px 5px !important;
  text-decoration: none;
}

a.play {
  padding: 10px 9px 5px !important;
}

.scapes-blocks {
  overflow: hidden;
}

.scapes-blocks .block {
  background: #f6f7f8;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
  height: 330px;

  .description {
    padding: 20px;
    position: relative;

    ul {
      position: absolute;
      width: auto;
      right: 0;
      top: 0px;

      text-align: right;
      padding-right: 20px;

      li {
        display: inline;
        a {
          background-color: $corporate-2;

          &:hover {
            background-color: $corporate-1;
          }
        }
        a.plus {
          padding: 6px 7px 6px !important;
          margin-top: 25px;
          width: 67px;
          display: inline-block;
          text-align: center;

          &.alone_one {
            margin-right: 10px;
            margin-top: 35px;
          }
        }
        a.play {
          padding: 10px 9px 5px;

          img {
            margin-top: 2px;
            vertical-align: bottom;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
      text-align: left;
    }
  }
  img {
    height: 250px;
    width: 100%;
    object-fit: cover;
  }
}

.en .scapes-blocks .block .description ul li a.plus {

  width: 77px;
}

a.plus.reservation.button-promotion {
  margin-top: 7px !important;
}

.scapes-bottom-content {
  display: none;
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_2;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {
  padding: 20px;

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}

.scapes-blocks {
  span.ver_mas {
    color: white;
  }

  h3.title-module{
    font-size: 28px !important;
    width: 100% !important;
    margin: 0px 0px 5px !important;
    text-align: left !important;
    line-height: 35px !important;
    color:$corporate_2;
    font-family:"Roboto Slab", sans-serif;
  }
  h4{
    font-size:20px;
    color:$gray-2;
    text-align:left;
  }

}
.promo-modal h3{
  font-size:24px;
  color:$corporate_2;
  font-family:"Roboto Slab", sans-serif;
  margin-bottom:10px;
}

@media screen and (max-height: 570px) {
  .booking_widget {
    top: 140px;
    bottom: auto;
  }
}

//***************** OPINIONS **********************//

.opinions .header, .opinions-total .header {
  background-color: $corporate-2;
  padding: 20px;
  position: relative;

  .opinions-button{
    text-decoration:none;
    color:white;
    font-size:26px;
    font-family: "Roboto Slab", sans-serif;
  }

  img {
    position: absolute;
    top: 20px;
    left: 20px;
  }

  h3 {
    margin-left: 52px;
  }
  p {
    font-size: 14px;
    margin-left: 52px;
    line-height: 14px;
    margin-top: 2px;
  }
}

.opinions-total {
  margin: 40px 0 0 0;
}

.opinions {
  width: 379px !important;
  margin-left: 0px;
  margin-right: 0px;
}

.opinions .value, .opinions-total .value {
  background: $gray-4;
  text-align: center;
  padding: 14px 0;
  border-top: 2px solid white;
  font-size: 16px;
  text-transform: uppercase;
  color: $corporate-1;

  .media {
    font-size: 30px;
    font-family: nexabold;
    color: $gray-4;
    margin-right: 10px;
  }
}

.opinions .coment, .opinions-total .coment {
  background: $gray-4;

  padding: 15px 0;
  border-top: 1px solid white;
  position: relative;

  .plus-link {
    position: absolute;
    right: 15px;
    top: 12px;
    background-color: $corporate-1;
    padding: 8px 8px 0 !important;

    &:hover {
      background-color: $corporate-2;
    }
  }
  span {
    font-size: 12px;

    p {
      display: inline;
    }
  }
  .calification {
    color: white;
    background-color: $gray-2;
    padding: 10px 10px 9px;
    margin-right: 20px;
    margin-left: 15px;
    font-size: 14px;
  }
}

.opinions-total table {
  width: 100%;

  tr {
    background-color: $gray-4;
    border-top: 2px solid white;

    .name {
      text-transform: uppercase;
      width: 250px;
      border-right: 2px solid white;
      padding: 20px;
    }
    .opinion-description {
      width: 800px;
      padding: 20px;
    }
    .calification {
      border-left: 2px solid white;
      vertical-align: middle;
      padding: 20px;

      span {
        color: white;
        background-color: #787878;
        padding: 10px 10px 9px;
        font-size: 14px;
      }
    }
    p {
      margin-bottom: 0;
    }
  }
}

.form-general {
  padding: 20px 20px 0;
  background: $gray-4;
  margin-top: 20px;
  overflow: hidden;

  h3 {
    margin-bottom: 20px;
  }

  li {
    display: inline-block;
    width: 268px;
    margin-bottom: 10px;

    label {
      display: block;
      font-size: 12px;
    }
    input, textarea {
      border: none;
      width: 254px;
      padding: 10px 5px;
    }

    textarea {
      height: 13px;
      width: 524px;
    }

    #check_from, #check_till {
      background: white url("/img/holiy/calendar.png") no-repeat 235px;
    }
    #check_date, #end_date {
      background: white url("/img/holiy/calendar.png") no-repeat 100px;
    }
  }
  li.comment-box {
    width: 100%;
  }
  .short {
    width: 132px;

    input {
      width: 118px;
    }
  }
  a {
    color: white;
    margin-top: 10px;
    display: inline-block;
  }
  .btn-corporate {
    font-size: 14px;
    padding: 5px 10px 2px;
    border-radius: 3px;
    cursor: pointer;
    float: right;
  }
  span a {
    color: $corporate-1;
    font-size: 12px;
  }
  .form-bottom {
    display: inline-block;
    width: 400px;

    p {
      margin-bottom: 0;
      line-height: 15px;
    }
    label.error {
      display: none !important;
    }
  }
  .last {
    margin-top: 38px;

    .form-bottom {
      margin-top: 10px;
    }
  }

  .double_li_form {

    width: 536px;
    input {
      width: 526px;
    }
  }
}

.form-general label.error {
  display: none !important;
}

.input-error {
  outline: 2px solid red !important;
}

.block-left {
  width: 540px;
  float: left;
  padding-right: 10px;
  margin-bottom: 20px;
}

.block-right {
  width: 540px;
  float: right;
  padding-left: 10px;
  margin-bottom: 20px;
}

.form-general.form-opinion li {
  display: block !important;
}

.form-general.form-opinion .btn-corporate {
  float: none !important;
  margin-bottom: 20px;
  background:$corporate_2;
}

//*********** Mis Reservas  ************//

form#my-bookings-form {
  margin-top: 30px;
}

#wrapper_services {
  display: none;
}

h3.section-title{
  font-family:"roboto slab";
  font-size:36px;
  font-weight:300;
  color:$corporate_2;
  margin-bottom:5px;

}
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate-1;
    text-transform: uppercase;
  }
  input, .bordeSelect {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    color: black;
    border: none;
    background: $gray-4;
  }

  .bordeSelect {
    -webkit-appearance: none;
    color: $corporate_2;
    width: 263px !important;
    border-radius: 0px !important;
    height: 25px !important;
    background: #e6e6e6 url(/img/checn/select_down.png) no-repeat 240px;
  }
  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

button#cancelButton {
  width: 260px;
  color: white;
  background-color: $corporate-1;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: $corporate-2;
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;
    margin-left: 470px;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}



//*************** Galeria ****************//

.section_title {
  font-size: 77px;
  font-weight: 200;
  color: #15abc3;
  text-align: center;
  width: 700px;
  margin: 25px auto 55px;
  line-height: 65px;
}

.gallery_1 li .crop {
  width: 100%;
  overflow: hidden;
  img {
    display: block;
    height: auto;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    /*&:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }*/
  }
}

.gallery_1 li {
  margin: 1px;
  float: left;
  height: 212px;
}

ul.gallery_1 {
  margin: 12px 0px 35px;
  display: table;
  box-sizing: border-box;
}

.gallery_1 li:first-of-type {
  height: 426px;
  width: 542px;

  .crop {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.interior .tp-banner-container {
  height: 669px !important;
}

.interior .tp-revslider-mainul {
  position: fixed !important;
  z-index: -30;
}

.gallery_filt_title {
  font-size: 29px !important;
  font-weight: 200 !important;
  color: $corporate_1 !important;
  text-align: left !important;
  width: auto !important;
  line-height: 55px !important;
  border-bottom: 1px solid $corporate_1 !important;
  display: table !important;
  border-radius: 0 !important;
}

.border-gallery {
  border-top: 0px;
}

.wrapper_filt {

  .gallery_1 li {
    width: 360px !important;
    overflow: hidden;
  }
  .gallery_1 li:first-of-type {
    height: 212px;
    width: 283px;

    .crop {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

/************* LOCATION AND CONTACXT SECTION *****************/
.location-info {
  text-align: left;
  padding-left: 10px;
  width: 540px !important
}

.location-info-and-form-wrapper {
  padding: 10px 0;
  background:$gray-4;
  overflow: auto;
  margin-bottom:30px;
}

.location-info-and-form-wrapper h1 {
  color: $corporate_2;
  font-family:"Roboto Slab", sans-serif;
  font-size: 26px;
  text-align: left;
  text-transform: uppercase;
  margin-bottom: 30px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  padding-bottom: 30px;
}

//customer support form
.form-contact {
  padding-right: 10px;
  width: 540px !important;
}

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-top: 25px;
   margin-right: 5px;
}

.form-contact #contactContent span.title {
  margin-top: 25px;

  a {
    color: $corporate_1;
  }
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 5px;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 0 10px 0;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: $corporate-2;
  color: white;
  float: left;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0;
  width: 100%;
  border: 0px;
  background-color: $corporate-2;
  color: white;
  float: left;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 60px;
  margin-top: 20px;
}

.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

.form-contact #contact-button:hover {
  background-color: $corporate-2 !important;
}

/******************FOOTER***************/

footer {
  background-color: $gray-1;
}

.wrapper_footer_columns {

  padding-top: 20px;
}

.footer_column {
  box-sizing:border-box;
  padding:0px 30px;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 13px;
  line-height: 23px;
  color: white;
  width: 284px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  height: 265px;
  border-left: 1px solid white;
  text-align: center;
}
.footer_column_title{
  font-family: "Roboto Slab";
  margin-bottom:10px;
}

.footer_column a {
  font-family: 'gotham';
  font-size: 10px;
  line-height: 23px;
  color: white;
  text-decoration: none;
  text-transform: uppercase;
}

#footer_column_description {
  margin-bottom: 10px;
  font-family: 'gotham';
  font-size: 10px;
}

.footer_column a:hover {
  color: darken($gray-4, 10%);
}

footer .last {
  border-left: 1px solid white;
  width: 282px !important;
  text-align: left;
}

#title_newsletter {
  color: white;
  font-size:13px;
  font-family: "Roboto Slab";
  margin-bottom:10px;
}

#suscEmailLabel {
  margin-bottom: 10px;
  font-family: 'gotham';
  font-size: 10px;
}

#suscEmail {
  width: 100%;
  background-color: white;
  border: none;
  height: 20px;
  color: $corporate-1;
  border-radius: 3px;
  margin: 10px 0;
  margin-top: -5px;
}

.button_newsletter {
  width: 65px;
  height: 22px;
  text-transform: uppercase;
  background: $corporate_2;
  outline: none;
  padding-left: 25px;
  padding-top: 3px;
  font-weight: bolder;
  border: none;
  color: white;
  @include border-radius(4px);
}

div.button_newsletter:hover {
  background-color: darken($white, 11%);
  cursor: pointer;
}

.newsletter_wrapper label.error {
  margin-left: 100px;
}

label.error {
  position: absolute;
  margin-bottom: 8px;
  font-size: 16px;
  margin-top: 3px;
}

.newsletter_container {
  width: auto;
}

.newsletter_checkbox {
  font-size: 9px;

  a {
    text-decoration: underline !important;
    font-size: 9px !important;
  }
}

#form_events .styled-select label.error {
  background: white !important;
}


.footer_column h3 {
  font-size: 13px;
  color: white;
}

#footer {
  color: white;
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  padding-bottom: 20px;

  p {
    text-align: center;
  }
}
.full-copyright{
  text-align:center;
  color:white;
  margin-bottom:10px;


  a{
    color:white;
    text-decoration:none;
  }
}
#footer a {
  text-decoration: none;
  color: white;
}

#footer_bottom_text {
  font-size: 14px;
  line-height: 14px;
}

.copyright-footer img {
  margin: 0 5px;
}

#google_plus_one {
  text-align: center;
}

#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

#facebook_like {
  text-align: center;
  margin-top: 10px;
}

#facebook_like iframe {
  height: 21px;
  width: 103px;
}

#rp-widget {
  margin: 0 auto;
}




/*============== Languages styles ============*/

html:lang(fr) .ticks{
  font-size:11px;
  width:220px;
  margin:0px 0px;
}
html:lang(fr) .booking_widget h4.booking_title_2,
html:lang(fr) #data h4.booking_title_2{
  font-size:30px;
}

html:lang(fr) #wrapper_booking.inline .booking_widget.inline .booking_form_title h4{
  font-size:28px;
}

html:lang(fr) .scapes-blocks .block .description ul{
  right:10px;
}

html:lang(fr) .scapes-blocks .block .description ul li a.plus{
  width:101px;
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate-2;
  left: 0;
  bottom: 0;
  z-index: 1000;
}
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}
.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}
.close_button {
  float: right;
  cursor: pointer;
}
button.bottom_popup_button {
  width: 120px;
  background: white;
  border: 0;
  height: 36px;
  position: absolute;
  color: $corporate-2;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;

}
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}
.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;

  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
  input#id_email {
    height: 26px;
    text-align: center;
    width: 270px;
    font-size: 17px;
    box-shadow: 2px 2px black;
    border: 0px;
    color: $corporate-1;
  }
  button.popup_button {
    margin: 7px auto 0px;
    width: 277px;
    height: 40px;
    background: $corporate-1;
    font-size: 17px;
    border: 0px;
    text-transform: uppercase;
    color: white;
    cursor: pointer;
  }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}
