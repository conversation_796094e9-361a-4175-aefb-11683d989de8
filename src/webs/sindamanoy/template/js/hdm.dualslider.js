/**********************************
Hyperdisk Plugin
Title: Dual Slider
Author: KV
Desc: Vertical slider in 2 oppposite directions
***********************************/

function dualSlider(option){
    this.init(option);
}
dualSlider.prototype={
    init:function(option){
        var self=this;
        $(function(){
            self.setting=$.extend({
                elemContainer:'.dual_slider',
                elemSlide:'.ds_slide',
                elemSlideLeft:'.ds_l',
                elemSlideRight:'.ds_r',
                itemPerSet:2,
                autoplay:false,
                speed:2500,
                delay:5000,
				repeat:true,
				wheelOn:true,
                onLoad:function(){},
                onAfter:function(slide){},
                onBefore:function(slide){}
            },option);
            self.c=$(self.setting.elemContainer);
            self.i=self.c.find(self.setting.elemSlide);
            self.il=self.c.find(self.setting.elemSlideLeft);
            self.ir=self.c.find(self.setting.elemSlideRight);
            self.currentSlide=0;
            self.totalSlide=self.i.length/self.setting.itemPerSet;
            self.iPlay;
            self.isPending=false;
            self.setSize();
            self.setEvent();
            self.setting.onLoad();
            if(self.setting.autoplay){
                self.iPlay=setTimeout(function(){ self.next(); },self.setting.delay);
            }

        })
    },
    setSize:function(){
        var self=this;
        self.il.each(function(idx,elem){ $(elem).css({top:idx*$(elem).parent().height()}) });
        self.ir.each(function(idx,elem){ $(elem).css({top:idx*$(elem).parent().height()*-1})  });
		self.play();
    },
    play:function(num,speed){
        var self=this;
        clearTimeout(self.iPlay);
        if(num==self.currentSlide) return;
        if(num||num==0)self.currentSlide=num;
        //if(self.isPending) return;
        self.isPending=true;
        self.onBefore();
        var speed=speed||self.setting.speed;
        var offsetY=self.currentSlide*self.c.height();
        var offsetYLeft=offsetY*-1;
        var offsetYRight=offsetY;
        self.il.each(function(idx,elem){ $(elem).stop().animate({top:(idx*$(elem).parent().height()) + offsetYLeft},speed) });
        self.ir.each(function(idx,elem){ $(elem).stop().animate({top:(idx*$(elem).parent().height()*-1) + offsetYRight},speed,function(){
                if(idx==self.ir.length-1){
                    self.isPending=false;
                    self.onAfter();
                }
            }
        )});
    },
    next:function(){
        var self=this;
        if(self.isPending) return;
        if(self.currentSlide<self.totalSlide-1){
			self.currentSlide++;
			self.play();
		}else{
			if(self.setting.repeat){
				self.currentSlide=0;
				self.play();
			}
		}
    },
    previous:function(){
        var self=this;
        if(self.isPending) return;
        if(self.currentSlide>0){
			self.currentSlide--;
			self.play();
		}else{
			if(self.setting.repeat){
				self.currentSlide=self.totalSlide-1;
				self.play();
			}
		}
    },
    onBefore:function(){
        var self=this;
        self.setting.onBefore(self.currentSlide);
    },
    onAfter:function(){
        var self=this;
        if(self.setting.autoplay){
            self.iPlay=setTimeout(function(){ self.next(); },self.setting.delay);
        }
        self.setting.onAfter(self.currentSlide);
    },
    setEvent:function(){
        var self=this;
		if(self.setting.wheelOn){
			self.c.mousewheel(function(e, delta, deltaX, deltaY) {
				if($(window).width()<=640) return;
				if(self.isPending) return;
				if(deltaY>0)self.previous(); else self.next();
			});
		}
		self.eWinResize=function(e){ self.play(); }
		$(window).bind('resize',self.eWinResize);
    }
}