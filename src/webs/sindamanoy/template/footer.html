<footer style="{% if footer_pattern %}background-image:url('{{ footer_pattern.0.servingUrl|safe }}');{% endif %}">
    <div class="footer_content container12">
        <div class="wrapper_footer_columns">
            <div class="footer_column column3">
                <h3 class="footer_column_title">{{ footer_columns.0.title|safe }}</h3>

                <nav id="main_menu">
                    <div id="mainMenuDiv">
                        {% include "main_div.html" %}
                    </div>
                </nav>
            </div>
            <div class="footer_column column3">
                <h3 class="footer_column_title">{{ footer_columns.1.title|safe }}</h3>

                <div class="footer_column_description">{{ footer_columns.1.description|safe }}</div>
            </div>
            <div class="footer_column column6">
                <a href="{{ host|safe }}/">
                    <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}"
                         title="{{ hotel_name|safe }}"/>
                </a>
            </div>
        </div>

        <div class="footer_legal_text_wrapper">
            <div class="footer_links_wrapper">
                {% if footer_links %}
                    {% for link in footer_links %}
                        <a class="myFancyPopup fancybox.iframe"
                           href="/{{ language }}/?sectionContent={{ link.linkUrl|safe }}"
                           rel="nofollow">{{ link.title|safe }}</a>&nbsp;-&nbsp;
                    {% endfor %}
                {% endif %}
                <a target="_blank" id="paraty-link" href="http://www.paratytech.com/motor-de-reservas.html"
                   title="{{ T_motor_de_reservas|safe }}">{{ T_motor_de_reservas|safe }}</a></span>&nbsp;-&nbsp;
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a>&nbsp;-&nbsp;
                <a target="_blank" href="/rss.xml">RSS</a>

                <div id="lang" class="lang2">
                    <div href="#popup_lang_content" id="selected-language" class="popup_lang">
                        {% for key, lang in language_codes.items %}{% if key == language %}{{ lang }}
                        {% endif %}{% endfor %}
                    </div>

                    <div id="popup_lang_content" style="display: none">
                        <ul id="language-selector-options">
                            {% for key, language in language_codes.items %}
                                {% if not language_selected == language %}
                                    <li class="language-option-flag">
                                        <a hreflang="{{ key }}" href="
                                                {% if not key == 'es' %}{{ hostWithoutLanguage }}/{{ key }}/{% else %}/es/{% endif %}">{{ language }}</a>
                                    </li>{% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>

            {% if texto_legal %}
                <div class="legal_text">{{ texto_legal|safe }}</div>
            {% endif %}
        </div>
    </div>


</footer>