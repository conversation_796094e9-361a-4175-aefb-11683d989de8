.rooms_wrapper {
  margin-top: 70px;

  .room_element {
    height: auto;
    margin-bottom: 20px;
    width: 100%;
    display: inline-block;

    &:nth-child(even) {
      .room_gallery {
        float: right;
      }

      .room_description_wrapper {
        float: left;
      }
    }

    h2.room_title {
      font-size: $title_size;
      color: $corporate_1;
      margin-bottom: 12px;
      font-family: "<PERSON>";

      span.capacity {
        font-weight: lighter;
        font-size: 16px;
        text-transform: capitalize;
        vertical-align: top;
        margin-top: 6px;
        display: inline-block;

        .capacity_image {
          vertical-align: middle;
          padding-bottom: 4px;
        }
      }
    }

    .room_description {
      font-weight: lighter;
      font-size: $description_size;
      line-height: $line_height;
      color: $corporate_1;

      .hide_me {
        display: none;
      }
    }

    .service_elements li {
      display: inline-block;
      padding-right: 20px;
      font-size: 13px;
      color: $corporate_1;
      font-weight: lighter;

      img {
        vertical-align: middle;
        margin-right: 5px;
      }
    }

    .room_gallery {
      width: 32%;
      height: 337px;
      float: left;
      position: relative;
      overflow: hidden;

      .owl-item {
        height: 337px;
        overflow: hidden;

        img {
          width: auto;
        }
      }

      .plus_image {
        position: absolute;
        top: 20px;
        left: 20px;
        height: 30px;
        width: 30px;
        background: white;
        z-index: 2;
        border-radius: 5px;
        @include transition(opacity, .3s);

        &:hover {
          opacity: .8;
        }

        .fa {
          color: $corporate_1;
          @include center_xy;
        }
      }
    }

    .room_description_wrapper {
      background: #F8F8F8;
      float: right;
      width: 68%;
      padding: 25px 40px;
      height: 337px;
      box-sizing: border-box;
      position: relative;
    }

    .room_buttons_wrapper {
      position: absolute;
      top: 17px;
      right: 40px;

      & > a {
        color: white;
        padding: 10px 20px;
        font-family: "Oswald";
        box-sizing: border-box;
        display: inline-block;
        text-align: center;
        text-transform: uppercase;
        @include transition(opacity, .3s);

        &:hover {
          opacity: .8;
        }
      }

      .room_book {
        background: $corporate_2;
      }

      .link_room {
        background: $corporate_1;
        margin-left: 10px;
      }
    }

    .service_elements {
      display: inline-block;
      width: 100%;
      border-top: 1px solid #CECECE;
      margin-top: 22px;
      padding-top: 22px;

      .service_element {
        display: inline-block;
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }

        & > * {
          display: inline-block;
          vertical-align: middle;
        }

        .service_image {
          margin-right: 5px;
        }

        .service_title {
          font-size: $description_size;
          color: $corporate_1;
        }

        .fa {
          background: $corporate_1;
          width: 35px;
          height: 35px;
          border-radius: 50%;
          position: relative;
          color: white;

          &:before {
            @include center_xy;
          }
        }
      }
    }
  }
}
