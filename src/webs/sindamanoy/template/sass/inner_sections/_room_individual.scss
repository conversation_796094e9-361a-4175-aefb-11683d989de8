.detailed_room_wrapper {
  margin-top: 70px;
  margin-bottom: 70px;
  display: inline-block;
  width: 100%;

  .room_detail_image_wrapper {
    height: 325px;
    position: relative;
    overflow: hidden;
  }

  a.see_more_pictures_detailed {
    position: absolute;
    z-index: 1;
    bottom: 25px;
    right: 25px;
    text-transform: uppercase;
    text-decoration: none;
    color: white;

    &:hover {
      .fa {
        color: $corporate_1;

        &:after {
          left: 0;
          width: 46px;
        }
      }
    }

    span {
      display: inline-block;
      vertical-align: middle;
    }

    .fa {
      border: 2px solid white;
      position: relative;
      vertical-align: middle;
      width: 42px;
      height: 42px;
      margin-left: 10px;
      overflow: hidden;
      @include transition(color, .3s);

      &:before {
        @include center_xy;
        font-size: 21px;
        z-index: 2;
      }

      &:after {
        background: white;
        content: "";
        height: 46px;
        position: absolute;
        right: 0;
        width: 0px;
        z-index: 1;
        @include transition(width, .3s);
      }
    }
  }

  .room_details_text {
    display: block;
    width: 547px;
    margin-top: -125px;
    z-index: 2;
    position: relative;
    background: white;
    padding: 40px;
    float: left;
    box-sizing: border-box;

    .offer_section {
      width: 800px;
    }

    &:after {
      content: "";
      width: 22px;
      height: 22px;
      border-left: 2px solid $corporate_1;
      border-bottom: 2px solid $corporate_1;
      display: inline-block;
      position: absolute;
      left: 0;
      bottom: 0;
    }

    a.button-promotion {
      background: $corporate_2;
      color: white;
      text-decoration: none;
      float: right;
      text-transform: uppercase;
      width: 205px;
      height: 65px;
      box-sizing: border-box;
      text-align: center;
      padding: 22px 0;
      position: relative;
      overflow: hidden;
      font-family: "Oswald";

      &:before {
        background: $corporate_1;
        content: "";
        height: 0;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 1;
        @include transition(height, .3s);
      }

      span {
        position: relative;
        z-index: 2;
      }

      &:hover:before {
        bottom: 0;
        height: 65px;
      }
    }

    h1.room_title {
      font-size: $title_size;
      float: left;
      color: $corporate_1;
      width: 230px;
      font-family: "Oswald";

      &:after {
        content: '';
        display: block;
        width: 65px;
        height: 4px;
        background: $corporate_1;
        margin: 21px 0 27px;
      }
    }

    .room_description {
      margin-top: 8px;
      font-size: $description_size;
      color: $corporate_1;
      clear: both;
    }

    #shareSocialArea {
      float: right;
    }
  }

  .minigallery_room_wrapper {
    display: inline-block;
    width: 593px;
    float: right;
    margin-top: 30px;

    .minigallery_element {
      display: inline-block;
      float: left;
      width: calc(25% - 10px);
      height: 138px;
      margin-right: 13px;
      position: relative;
      overflow: hidden;
      margin-top: 13px;

      &:nth-child(4n), &:last-child {
        margin-right: 0;
      }

      &:nth-child(-n+4) {
        margin-top: 0;
      }

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }
  }

  .services_wrapper {
    display: inline-block;
    width: 100%;
    border-top: 1px solid #CECECE;
    margin-top: 22px;
    padding-top: 22px;

    .service_element {
      display: inline-block;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }

      & > * {
        display: inline-block;
        vertical-align: middle;
      }

      .service_image {
        margin-right: 5px;
      }

      .service_title {
        font-size: $description_size;
        color: $corporate_1;
      }

      .fa {
        background: $corporate_1;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        position: relative;
        color: white;

        &:before {
          @include center_xy;
        }
      }
    }
  }
}