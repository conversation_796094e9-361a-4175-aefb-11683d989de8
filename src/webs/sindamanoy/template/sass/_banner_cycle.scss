.banner_cycle_wrapper {
	.banner {
		position: relative;
		display: block;
		float: left;
		width: 50%;
		height:600px;
		.banner_image {
			position: absolute;top:0;left:0;right:0;bottom:0;
			width:100%;
			height:100%;
			overflow:hidden;
			img {
				@include center_image;
			}
			a {
				position: absolute;top:50%;left:0;right:0;bottom:0;
				@include transition(all, .6s);
				.fa {
					@include center_xy;
					padding:20px 25px;
					font-size: 40px;
					background-color: white;
					color:$corporate_2;
					@include transition(all, .6s);
				}
				&:hover {
					background-color: rgba(0,0,0,0.3);
					.fa {
						color: white;
						background-color: $corporate_1;
					}
				}
			}
		}
		.banner_content {
			position: absolute;top:0;left:0;right:0;bottom:50%;
			background-color: rgba(white,0.8);
			padding: 30px;
			.banner_content_title {
				color: $corporate_2;
				font-size: $title_size;
				margin-bottom: 20px;
			}
			.banner_content_desc {
				font-size: $description_size;
			}
		}
		&:nth-child(even) {
			.banner_image {
				a {
					position: absolute;top:0;left:0;right:0;bottom:50%;
				}
			}
			.banner_content {
				position: absolute;top:50%;left:0;right:0;bottom:0;
				background-color: rgba(black,0.8);
				.banner_content_desc {
					color: white;
				}
			}
		}
	}
}