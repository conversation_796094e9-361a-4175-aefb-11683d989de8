body {
  font-family: "Raleway", sans-serif;

  a {
    text-decoration: none;
  }
}

#lang {
  display: inline-block;

  ul li {
    text-align: left;
    //width: 80px;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;
    display: inline-block;
    color: white;

    a {
      color: white;
      text-decoration: none;
      font-size: 14px;
      text-transform: uppercase;
    }

    &.language_selected a {
      color: $corporate_1;
      font-weight: bold;
    }
  }
}

#lang {
  display: inline-block;
  margin-left: 10px;

  #selected-language {
    padding-right: 8px;
    cursor: pointer;
    text-decoration: none;
    background-position-y: 7px;
    font-size: 14px;

    &:after {
      content: '\f107';
      font-family: 'fontawesome', sans-serif;
    }
  }

  .language-option-flag {
    display: inline-block;
  }

  .language-option-flag a {
    color: #4B86CA;
  }
}

.fancybox-lang {
  width: 400px !important;
  //height: 350px!important;

  .fancybox-outer {
    height: 100% !important;
    padding: 1px !important;
    border-radius: 0 !important;
  }

  .fancybox-inner {
    overflow: visible !important;
    margin: 40px;
  }

  .fancybox-close {
    //display: none;
    background: url("/img/#{$base_web}/cerrar-idioma.png") !important;
    top: 0 !important;
    right: 0 !important;
    width: 42px !important;
    height: 42px !important;

    &:hover {
      opacity: 0.8;
    }
  }

  #popup_lang_content {
    padding: 40px;
    border: 1px solid #ddd;
    text-align: center;
    width: 317px;
    box-sizing: border-box;

    .title {
      font-size: 16px;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: #666;

      b {
        display: block;
        font-size: 28px;
        color: black;
      }
    }

    li {
      display: block;
      text-transform: uppercase;
      margin: 0 auto;
      width: 70%;

      &:first-child a {
        border: none;
      }

      a {
        line-height: 40px;
        border-top: 1px dotted #ccc;
        display: block;
        color: #666;
        text-decoration: none;
        font-size: 16px;
        letter-spacing: 0.05em;

        &:hover {
          color: lighten(#666, 20%);
        }
      }
    }
  }

  #popup_login {
    padding: 40px;
    margin: 40px;
    border: 1px solid #ddd;
    text-align: center;

    .title {
      font-size: 16px;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: #666;

      strong {
        display: block;
        font-size: 28px;
        color: black;
      }
    }

    .login_button {
      background: $corporate-1;
      border: 0;
      color: white;
      padding: 10px 67px;
      margin: 0 auto;
      text-decoration: none;
    }

    .input_login {
      margin-bottom: 10px;

      input {
        border: 1px solid #ccc;
        color: #666;
        width: 176px;
        padding: 7.5px 10px;
        background: transparent;
        font-size: 12px;
      }
    }

    #select-tool {
      margin-bottom: 10px;
      -webkit-appearance: none;
      border-radius: 0;
      border: 1px solid #ccc;
      width: 176px;
      padding: 7.5px 10px;
      color: #666;
      background: url(/img/#{$base_web}/flecha-select-idioma.png) no-repeat 90% center;
      font-size: 12px;
    }

    .others_tools {
      margin-top: 10px;

      .login_button {
        padding: 10px 52px;
      }
    }
  }
}

header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  min-width: 1140px;
  height: 100px;
  z-index: 35;
  #logoDiv {
    background-color: white;
    position: absolute;
    top: 0;
    left: 0;
    a {
      display: inline-block;
      vertical-align: middle;
      height: 100px;
      min-width: 100px;
      padding: 0 30px;
      box-sizing: border-box;
    }
    .phone-action {
      padding: 30px 0;
      box-sizing: border-box;
      text-align: center;
      background-color: $corporate_1;
      .fa {
        color: white;
        font-size: 38px;
      }
    }
  }
  #wrapper-header {
    background-color: white;
    height: 37px;
    text-align: center;
  }
  nav#main_menu {
    display: inline-block;
    background-color: white;
    ul {
      margin-left: 0;
      display: table;
      width: calc(1140px - 345px);
    }
    .main-section-div-wrapper {
      display: table-cell;
      text-align: right;
      vertical-align: middle;
      a {
        display: block;
        padding: 10px;
        text-transform: uppercase;
        font-size: 12px;
        color: lighten($corporate_2, 10%);
        @include transition(all, 0.6s)
      }
      a:hover, &#section-active a {
        color: $corporate_1;
      }
    }
  }
}

@media screen and (max-width: 1450px) {
  header {
    #full_wrapper_booking {
      margin-left: 100px;
    }
  }
}

@media screen and (max-width: 1360px) {
  header {
    #full_wrapper_booking {
      margin-left: 200px;
    }
  }
}

@media screen and (max-width: 1250px) {
  header {
    #full_wrapper_booking {
      margin-left: 300px;
    }
  }
}

#slider_container {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 850px;
  width: 100%;
  .ticks_wrapper {
    position: absolute;
    text-align: center;
    margin: auto;
    bottom: 50px;
    left: 0;
    right: 0;
    z-index: 32;
    .tick {
      margin: 10px 15px;
      display: inline-block;
      img, span {
        display: inline-block;
        vertical-align: middle;
      }
      img {
        height: 20px;
        margin-right: 20px;
      }
      span {
        color: white;
        font-size: 14px;
      }
    }
  }
}

.inner_section #slider_container {
  height: 400px;
  overflow: hidden;
  .inner_slider img {
    @include center_image;
  }
}

.big-img .image_filters_wrapper .filter_element.active {
  color: black;
  background-color: rgba(white, 0.6);
}

.content_subtitle_wrapper {
  background-attachment: fixed;
  color: lighten($corporate_1, 10%);

  .content_subtitle_title {
    text-align: center;
    margin-top: 30px;
    font-size: $title_size;

    &:after {
      content: "";
      width: 30px;
      border-bottom: 6px dotted $corporate_1;
      display: block;
      margin: 5px auto 0;
    }
  }

  .content_subtitle_description {
    font-size: $description_size;
    font-weight: 200;
    padding: 60px 200px;
    text-align: center;
    strong {
      font-weight: 700;
    }
    .btn-content {
      display: inline-block;
      margin: 20px auto;
      padding: 10px 25px;
      background-color: $corporate_2;
      color: white;
      font-weight: 200;
      @include transition(all, .6s);
      &:hover {
        border-radius: 30px;
        background-color: lighten($corporate_2, 10%);
      }
    }
  }

  #fancy_contact {
    text-align: center;
    margin-bottom: 30px;

    .input_element {
      label {
        display: block;
        color: #831b14;
        font-weight: bold;
        margin: 5px 0;
      }

      input, textarea {
        display: block;
        margin: auto;
        width: 300px;
        box-sizing: border-box;
        padding: 10px;
      }

      .check_privacy {
        display: inline-block;
        width: auto;
        vertical-align: middle;
      }

      .title {
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;

        a {
          color: #57120d;
        }
      }
    }

    .thanks_popup_wrapper {
      font-weight: bold;
      margin-top: 5px;
    }

    #contact-button-wrapper {
      #popup_form_button {
        display: inline-block;
        padding: 10px 30px;
        background: #831b14;
        color: white;
        text-transform: uppercase;
        margin-top: 10px;
      }
    }
  }
}

.content_access {
  text-align: center;
  margin: 40px auto 0;
  position: relative;

  .section-title {
    font-size: $title_size;
    color: $corporate_1;

    &:after {
      content: "";
      width: 30px;
      border-bottom: 6px dotted $corporate_1;
      display: block;
      margin: 5px auto 0;
    }
  }

  & > div {
    width: 700px;
    margin: 20px auto 0;
    color: $corporate_1;
    font-size: $description_size;
    text-align: justify;
    line-height: $line_height;
  }

  #my-bookings-form {

    #my-bookings-form-fields {
      text-align: center;

      label {
        display: block;
        text-transform: uppercase;
        font-weight: 100;
      }

      input {
        display: block;
        margin: 10px auto;
        width: 250px;
        padding: 5px 0;
        box-sizing: border-box;
        text-align: center;
      }

      ul {
        li {
          display: inline-block;

          button {
            display: inline-block;
            border: 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            color: white;
            font-size: 16px;
            font-weight: 100;
            padding: 10px 30px;
            cursor: pointer;
            @include transition(opacity, .6s);

            &:hover {
              opacity: .8;
            }

            &.modify-reservation {
              background: $corporate_1;
            }

            &.cancelButton, &.searchForReservation {
              background: $corporate_2;
            }
          }
        }
      }
    }

    #reservation {
      .modify_reservation_widget {
        margin: 0 auto 40px;
        width: 336px;
      }

      .my-bookings-booking-info {
        margin: 0 auto;
      }
    }

    #cancel-button-container button {
      background: $corporate_1;
      display: inline-block;
      border: 0;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      color: white;
      font-size: 16px;
      font-weight: 100;
      padding: 10px 30px;
      margin: 40px auto 0;
      display: none;
    }
  }
}

/*=== Cycle Banners ===*/
.cycle_banners_wrapper {
  display: inline-block;
  width: 100%;
  background-image: url(https://lh3.googleusercontent.com/qN7YLkg8gtywLhQlkwIk9kEdvG4B1rtkYRD5OWyaT-Jb9_9xTrQbGlw6Wgp_nq-ZfOC9NO2_VoGBcVLr2Jm_zrs);
  background-color: #F9F9F9;
  padding: 60px 0;
  margin-top: 70px;

  .cycle_element {
    display: block;
    width: 100%;
    height: 570px;
    position: relative;

    &:nth-child(odd) {
      .cycle_image {
        float: left;
      }

      .cycle_content {
        float: right;
      }

      .owl-dots {
        right: 55px;
      }
    }

    &:nth-child(even) {
      .cycle_image {
        float: right;
      }

      .cycle_content {
        float: left;
      }

      .owl-dots {
        right: 55%;
      }
    }

    .owl-dots {
      position: absolute;
      bottom: 65px;

      .owl-dot {
        display: inline-block;
        border: 1px solid $corporate_1;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        position: relative;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }

        &.active span {
          background: $corporate_1;
        }

        span {
          @include center_xy;
          width: 16px;
          height: 16px;
          background: transparent;
          border-radius: 50%;
          display: inline-block;

        }
      }
    }

    .cycle_image {
      display: inline-block;
      width: 50%;
      height: 570px;
      position: relative;
      overflow: hidden;

      img {
        width: auto;
      }
    }

    .cycle_content {
      display: inline-block;
      width: 50%;
      height: 100%;
      height: 570px;
      background: white;
      box-sizing: border-box;
      padding: 55px;
      position: relative;

      .center_block {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .cycle_title {
        text-transform: uppercase;
        font-size: $title_size;
        color: $corporate_1;
        margin-bottom: 40px;
        font-family: "Oswald";

        .subtitle_offer {
          display: block;
          color: $corporate_2;
          font-size: 24px;
        }
      }

      .cycle_description {
        font-size: $description_size;
        line-height: $line_height;
        text-align: justify;

        hide {
          display: none;
        }
      }

      .links_block {
        display: inline-block;
        position: absolute;
        bottom: 0;

        .booking_button {
          display: inline-block;
          vertical-align: middle;

          a.button-promotion {
            display: inline-block;
            vertical-align: middle;
            background: $corporate_2;
            color: white;
            font: {
              size: 18px;
              weight: lighter;
              family: "Oswald";
            }
            text-transform: uppercase;
            padding: 10px 50px;

            &:hover {
              opacity: .8;
            }
          }
        }

        .see_more {
          display: inline-block;
          vertical-align: middle;
          width: 41px;
          height: 41px;

          a {
            display: inline-block;
            width: 41px;
            height: 41px;
            position: relative;
            background: $corporate_1;

            &:hover {
              opacity: .8;
            }

            .fa {
              @include center_xy;
              color: white;
            }
          }
        }
      }
    }
  }
}

.newsletter_wrapper {
  .container12 {
    width: 100%;
  }
  .newsletter_side {
    display: block;
    float: left;
    width: 30%;
    height: 730px;
    position: relative;
    overflow: hidden;
    .newsletter_block_image {
      display: block;
      width: 250px;
      @include center_y;
      img {
        float: left;
        width: 250px;
      }
    }
    .newsletter_block_image_1 {
      right: 0;
      left: auto;
    }
    .newsletter_block_image_4 {
      right: 250px;
      left: auto;
    }
    .newsletter_block_image_2 {
      right: auto;
      left: 0;
    }
    .newsletter_block_image_3 {
      right: auto;
      left: 250px;
    }
  }
  .newsletter_main {
    display: block;
    position: relative;
    float: left;
    width: 40%;
    height: 730px;
    text-align: center;
    .newsletter_container {
      @include center_y;
      .newsletter_title {
        color: $corporate_2;
        font-size: 38px;
        text-transform: uppercase;
      }
      .newsletter_description {
        color: $corporate_2;
        font-size: 19px;
        text-transform: uppercase;
      }
      .newsletter_form {
        margin-top: 40px;
        input[type=text] {
          display: block;
          width: 270px;
          margin: 10px auto;
          border: 2px solid $corporate_2;
          color: $corporate_2;
          font-weight: 200;
          font-size: $description_size;
          padding: 10px;
        }
        .button_newsletter {
          display: block;
          width: 270px;
          margin: auto;
          padding: 12px;
          background-color: $corporate_2;
          color: white;
          text-transform: uppercase;
          margin-bottom: 10px;
        }
        .check_newsletter {
            font-size: 12px;
            color:$corporate_2;
            a {
                text-decoration: underline;
                color:$corporate_2;
            }
        }
      }
      .social_newsletter {
        display: block;
        width: 270px;
        margin: auto;
        border-top: 1px solid $corporate_2;
        margin-top: 60px;
        padding-top: 60px;
        a {
          display: inline-block;
          padding: 10px;
          text-align: center;
          font-size: 38px;
          background-color: $corporate_1;
          border-radius: 50%;
          box-sizing: border-box;
          width: 65px;
          color: white;
          margin: 5px;
        }
      }
    }
  }
}

footer {
  color: white;
  background-attachment: fixed;
  font-size: 14px;
  padding: 30px 0;
  .footer_column {
    float: none;
    display: inline-block;
    color: white;
    font-size: 14px;
    font-weight: 100;
    vertical-align: top;
    .footer_column_title {
      font-size: 20px;
      padding-bottom: 10px;
      text-transform: uppercase;
    }
    .main-section-div-wrapper {
      display: block;
      a {
        display: block;
        color: white;
        @include transition(all, 0.6s)
      }
      a:hover {
        color: $corporate_1;
        border-left: 10px solid white;
        padding-left: 5px;
      }
    }
  }
  .column6 {
    vertical-align: middle;
    text-align: right;
    padding: 40px 0;
  }
  .footer_legal_text_wrapper {
    padding-top: 40px;
    .footer_links_wrapper, .legal_text {
      width: 45%;
      display: inline-block;
    }
    .footer_links_wrapper {
      a {
        color: white;
      }
      a:hover {
        text-decoration: underline;
      }
    }
    .legal_text {
      float: right;
      text-align: right;
    }
  }
}

#contador_noches {
  display: none;
}