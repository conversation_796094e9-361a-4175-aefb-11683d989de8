<div class="filters_gallery_wrapper">
    <div class="container12">
        {% for k, v in gallery_section.items %}
            <div class="filter_element {% if forloop.first %}active{% endif %}"
                 data-filter="{{ k }}">{{ v.0.title|safe }}</div>
        {% endfor %}
    </div>
</div>

<div class="gallery_filter_wrapper">
    {% for filter, gallery in gallery_section.items %}
        <div class="gallery_filter" data-filter="{{ filter|safe }}"
             {% if not forloop.first %}style="display: none" {% endif %}>
            <div class="gallery_photos">
                {% for x in gallery %}
                    {% if x.linkUrl and "youtube" in x.linkUrl %}
                        <a class="iframe_video">
                            <iframe src="{{ x.linkUrl|safe }}" frameborder="0"></iframe>
                        </a>
                    {% else %}
                        <a href="{{ x.servingUrl|safe }}=s1024" rel="lightbox[gallery_carousel_{{ filter }}]"><img
                                src="{{ x.servingUrl|safe }}=s350-c"></a>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(resize_video);
    $(window).load(function () {
        $(".filters_gallery_wrapper .filter_element").click(function () {
            var filter_active = $(this);
            $(".filters_gallery_wrapper .filter_element.active").removeClass("active");
            $(".gallery_filter").slideUp().promise().done(function () {
                $(".gallery_filter[data-filter=" + filter_active.attr("data-filter") + "]").slideDown();
            });
            filter_active.addClass("active");
        });
    })
    $(window).resize(resize_video);

    function resize_video() {
        $("a.iframe_video").height($("a.iframe_video").width() - 1);
    }
</script>
