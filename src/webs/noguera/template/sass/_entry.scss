.entry {
	padding: 120px 0 60px;
	width:65%;
	float: left;
	color: #4b4b4b;
	box-sizing:border-box;
	.entry_title {
		font-size: 300%;
		color: #4b4b4b;
		font-weight: bold;
		margin-bottom: 30px;
	}
	.entry_author {
		margin: 10px 0 10px;
		.author_pic {
			position: relative;
			display: inline-block;
			vertical-align: middle;
			width: 30px;
			height: 30px;
			border-radius: 50%;
			overflow: hidden;
			img {
				@include center_xy;
			}
		}
		.author_name {
			display: inline-block;
			vertical-align: middle;
			font-weight: bold;
			margin-left: 15px;
			color: black;
			span {
				font-weight: normal;
				color: $corporate_2;
			}
		}
	}
	.entry_info {
		background-color: #efefef;
		border-radius: 5px;
		margin: 10px 0;
		padding: 5px;
		width: 100%;
		font-size: 70%;
		box-sizing: border-box;
		display: table;
		span {
			display: table-cell;
			text-align: center;
			padding: 3px;
			border:1px solid transparent;
			border-right-color: white;
			&:last-of-type {
				border-right-color: transparent;
			}
		}
	}
	.entry_content {
		h1, h2, h3, h4, h5, h6 {
			color: #171c24;
			font-weight: 300;
		}
		h2 {
			font-size: 40px;
			line-height: 1.14;
			margin-bottom: 10px;
			margin-top: 20px;
		}
		p {
			margin: 0 0 20px 0;
			color: #616161;
			font-size: 15px;
			line-height: 1.8;
		}
	}
	.entry_share {
		border-top:1px solid #DDD;
		padding: 30px 0 30px 0;
	}
	.entry_comments {
		h3 {
			text-transform: uppercase;
			text-align: center;
			letter-spacing: 1px;
			font-weight: bold;
			padding: 0 0 30px 0;
			border-bottom:1px solid #DDD;
		}
		.comment_list {
			.comment {
				background-color:#fafafa;
				border-radius: 3px;
				min-height: 70px;
				margin: 10px;
				padding: 20px;
				.comment_pic {
					position: relative;
					display: inline-block;
					vertical-align: middle;
					width: 70px;
					height: 70px;
					float: left;
					margin-right: 20px;
					margin-bottom: 20px;
					border-radius: 50%;
					overflow: hidden;
					img {
						@include center_xy;
					}
				}
				.name {
					margin-top:20px;
					margin-bottom:10px;
					span {
						text-transform: uppercase;
						color: #CCC;
						margin-right: 5px;
					}
				}
				.text {
					font-size: 80%;
				}
			}
		}
		.comment_form {
			font-size: 80%;
			margin-top:30px;
			.text_element {
				width: 100%;
				textarea {
					width: 100%;
					border: 1px solid #ddd;
				}
			}
			.input_element {
				display: inline-block;
				width: calc(100% / 3 - 5px);
				margin-top: 15px;
				margin-right:5px;
				input {
					padding: 7px;
					box-sizing: border-box;
					border: 1px solid #ddd;
					width: calc(100% - 5px);
				}
			}
			.check_element {
				margin-top: 15px;
				a {
					color: $corporate_2;
					&:hover {
						text-decoration: underline;
					}
				}
			}
			label {
				display: block;
				text-transform: uppercase;
			}
			#contact-button-wrapper {
				margin-top: 15px;
				#popup_form_button {
					cursor: pointer;
					display: inline-block;
					background-color: $corporate_2;
					color: white;
					border-radius: 50px;
					padding: 10px 30px;
					text-transform: uppercase;
					letter-spacing: 1px;
				}
			}
		}
	}
}


.right_column {
	width: 30%;
	box-sizing: border-box;
	padding-top: 120px;
	float: right;
	vertical-align: top;
	.widget_title {
		position: relative;
		margin-top: 60px;
		margin-bottom: 10px;
		text-transform: uppercase;
		color: #4b4b4b;
		letter-spacing: 1px;
		span {
			position: relative;
			display: inline-block;
			padding-right: 5px;
			background-color: white;
		}
		&:before {
			content: '';
			display: block;
			position: absolute;
			top: calc(50% - 3px);
			left: 0;
			right: 0;
			height: 3px;
			background-color: #ddd;
		}
		 &:first-of-type {
			margin-top: 0px;
		 }
	}
	.news_right_widget {
		.news_widget_entry {
			margin: auto;
			margin-bottom: 10px;
			padding-bottom:10px;
			display: block;
			width: 90%;
			border-bottom:1px solid #ddd;
			.image {
				position: relative;
				display: inline-block;
				vertical-align: middle;
				float: left;
				width: 100px;
				height: 100px;
				overflow: hidden;
				margin-right: 10px;
				margin-bottom: 10px;
				img {
					@include  center_xy;
					max-width: 100%;
					max-height: 100%;
				}
			}
			.title {
				display: inline-block;
				vertical-align: middle;
				width: calc(100% - 110px);
				font-size: 90%;
				margin-bottom: 10px;
				a {
					color: #4b4b4b;
					&:hover {
						color: $corporate_2;
					}
				}
			}
			.extra_info {
				display: inline-block;
				vertical-align: middle;
				width: calc(100% - 110px);
				font-size: 70%;
				color: #CCC;
				span {
					margin-right: 5px;
					i {
						margin-right: 5px;
						color: #aaa;
					}
				}
			}
		}
	}
	.social_widget {
		padding-top:10px;
		a {
			display: inline-block;
			position: relative;
			width: 30px;
			height: 30px;
			margin-right: 10px;
			border:1px solid #DDD;
			border-radius: 5px;
			i {
				@include center_xy;
				&.fa-facebook {
					color: #44659E;
				}
				&.fa-twitter {
					color: #00B3F0;
				}
				&.fa-google-plus {
					color: #DF371B;
				}
				&.fa-youtube {
					color: #C43030;
				}
				&.fa-pinterest-p {
					color: #C43030;
				}
				&.fa-instagram {
					color: #3C3C3C;
				}
			}
			&:hover {
				background-color: #efefef;
			}
		}
	}
	.instagram_widget {
	  padding-top: 20px;
	  text-align:center;
	  .instagram_picture {
		position: relative;
	    display: inline-block;
		  float: left;
		  margin: 0 5px 5px 0;
		width: calc(100% / 3 - 5px);
		overflow: hidden;

		&:before {
		  content: "";
		  display: block;
		  padding-top: 100%;
		}

		img.instagram_img {
		  @include center_xy;
		  min-width: 100%;
		  min-height: 100%;
		  max-height: 100%;
		  max-width: 323px;
		}
	  }

	  .picture_info {
		position: absolute;
		z-index: 2;
		top: 50%;
		opacity: 0;
		width: 100%;
		margin: auto;
		text-align: center;
		-webkit-transition: all 0.5s;
		-moz-transition: all 0.5s;
		-ms-transition: all 0.5s;
		-o-transition: all 0.5s;
		transition: all 0.5s;

		.comment_info, .likes_info {
		  display: inline-block;
		  font-size: 80%;
		  color: white;

		  img {
			vertical-align: middle;
		  }
		}

		.comment_info {
		  margin-right: 10px;
		}
	  }

	  .black_overlay {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background: rgba(0, 0, 0, 0.4);
		opacity: 0;
		-webkit-transition: all 0.5s;
		-moz-transition: all 0.5s;
		-ms-transition: all 0.5s;
		-o-transition: all 0.5s;
		transition: all 0.5s;
	  }

	  .instagram_picture:hover {
		.black_overlay, .picture_info {
		  opacity: 1;
		}
	  }
	}
	.comments_widget {
		padding-top:20px;
		.comment {
			margin-bottom: 30px;
		}
		.text {
			position: relative;
			font-size: 80%;
			font-style: italic;
			color: #999;
			border-radius: 5px;
			border: 1px solid #ddd;
			margin-bottom: 15px;
			padding: 20px;
			&:before {
				position: absolute;
				bottom: 0px;
				right: 20px;
				content: '';
				display: block;
				border: 10px solid transparent;
				border-top-color: #ddd;
				margin-bottom: -20px;
			}
			&:after {
				position: absolute;
				bottom: 0px;
				right: 20px;
				content: '';
				display: block;
				border: 10px solid transparent;
				border-top-color: white;
				margin-bottom: -19px;
			}
		}
		.name {
			text-align: right;
			font-size: 90%;
			color: #4b4b4b;
		}
	}
}