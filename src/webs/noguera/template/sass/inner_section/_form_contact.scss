.contact_form_wrapper {
  display: inline-block;
  background-color: #efefef;
  width: 100%;
  float: left;
  padding: 80px 0;

  h3 {
    text-transform: uppercase;
    text-align: center;
    font-family: $title_family;
    font-size: $title_size;
    color: $corporate_1;
    margin-bottom: 40px;
    font-weight: 100;
  }

  #contact {
    width: 980px;
    margin: auto;

    .contInput {
      display: inline-block;
      float: left;
      width: 100%;
      margin-bottom: 10px;
      position: relative;

      &:nth-of-type(-n+3) {
        width: calc((100% - 20px)/3);
        margin-right: 10px;
      }

      &:nth-of-type(4), &:nth-of-type(5) {
        width: calc((100% - 10px)/2);
        margin-right: 10px;
        margin-bottom: 20px;
      }

      &:nth-of-type(3), &:nth-of-type(5) {
        margin-right: 0;
      }

      .fa {
        width: 40px;
        height: 40px;
        color: $corporate_1;
        position: absolute;
        top: 0;
        left: 0;

        &:before {
          @include center_xy;
        }
      }

      input {
        width: 100%;
        height: 40px;
        padding-left: 40px;
        border: 0;
        border-bottom: 1px solid $corporate_1;

        &#accept-term {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }
      }

      textarea {
        width: 100%;
        padding-left: 40px;
        padding-top: 13px;
        border-color: $corporate_1;
      }
    }

    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $corporate_1;
    }

    #contact-button {
      display: inline-block;
      width: 100%;
      background: $corporate_1;
      color: white;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      font-family: $title_family;
      font-weight: 100;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}