@import "compass";

@import "defaults";

$corporate_1: #0099e0;
$corporate_2: rgb(45, 48, 71);
$corporate_3: #666;
$corporate_4: #686215;

@import "plugins/lightbox";
@import "plugins/fancybox_2_1_5";
@import "plugins/_jquery_ui_1_8_16_custom";
@import "plugins/owlcarousel";
@import "plugins/mixins";
@import "plugins/fontawesomemin";
@import "plugins/iconmoon";
@import "plugins/effects";
@import "gallerys/gallery_full_width";

@include akrobat;

@import "booking/booking_engine_7";
@import "booking/selectric";
@import "widget/booking_engine";
@import "widget/booking_popup";

@import "entry";
@import "news_widget";
@import "banners/fixed_banners";

@import "template_specific";

#adapting-table {
    .table_content {
        background: $corporate_1;
        padding: 2em 1em;
        min-height: 450px;
        height: auto;
        &:nth-child(even) {
            background-color: $corporate_2;
        }
    }
}

.offer_element {
    .offer_content {
        .offer_link {
            background: $corporate_2;
        }
        .offer_booking {
            &:before {
                background: $corporate_4;
            }
        }
    }
}

.newsletter_and_icons_footer_wrapper {
    .newsletter_wrapper {
        .newsletter_container {
            .newsletter_form {
                .button_newsletter {
                    &:before {
                        background: $corporate_4;
                    }
                }
            }
        }
    }
}

#full_wrapper_booking {
    .wrapper_booking_button {
        .submit_button {
            &:before {
                background: $corporate_4;
            }
        }
    }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .specific_month_selector, .go_back_button {
        strong {
            color: $corporate_1;
        }
    }
}
