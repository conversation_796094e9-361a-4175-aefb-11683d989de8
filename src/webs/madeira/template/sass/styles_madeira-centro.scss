@import "compass";
@import "plugins/lightbox";
@import "plugins/fancybox_2_1_5";
@import "plugins/_jquery_ui_1_8_16_custom";
@import "plugins/fontawesomemin";
@import "plugins/iconmoon";
@import "defaults_madeira-centro";
@import "booking/booking_engine";
@import "booking_engine";
@import "booking/selectric";
@import "template_specific";
@import "ventajas";

#full_wrapper_booking #fecha_entrada input, #fecha_salida input {
    background: white url(/img/madea/calendar-madeira.png) no-repeat 145px !important;
}

#full_wrapper_booking #motor_reserva select {
    background: white url(/img/madea/arrow-madeira.png) no-repeat 108px !important;
}

#full_wrapper_booking {
    #search-button {
        &:lang(fr) {
            font-size: 14px;
        }
    }
}

#wrapper_booking_fancybox {
    .wrapper_booking_button {
        button {
            &:lang(fr) {
                font-size: 13px;
            }
        }
    }
}

.fr {
    .scapes-blocks .block .description .title-module {
        font-size: 16px;
    }
    
    .scapes-blocks .block .description ul {
        width: 210px;
    }
}

.ticks_wrapper {
    display: none;
}

.boking_widget_inline {
    height: auto;
    padding: 0;
}

#slider_inner_container {
    .image-slider-fixed {
        height: 550px;
    }
    
    .slider_text {
        top: 210px;
        
        .caption {
            line-height: 25px;
        }
    }
}

#slider_container {
    //height: calc(100vh - 218px);
    
    .forcefullwidth_wrapper_tp_banner {
        overflow: hidden;
        height: 100% !important;
    }
}

.tp-bullets {
    bottom: 300px !important;
}