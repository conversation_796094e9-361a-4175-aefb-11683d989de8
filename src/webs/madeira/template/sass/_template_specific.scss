/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
body.montesol {
  
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    .header_datepicker {
      background-color: $corporate_1;
    }
    
    .months_selector_container .cheapest_month_selector {
      background-color: $corporate_1;
    }
  }
  
  .datepicker_wrapper_element_2 {
    z-index: 8030;
  }
  
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
    
  }
}

body.madeira-centro {
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    .header_datepicker {
      background-color: $corporate_1;
    }
    
    .months_selector_container .cheapest_month_selector {
      background-color: $corporate_1;
    }
  }
  
  .datepicker_wrapper_element_2 {
    z-index: 8030;
  }
  
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }
}

body {
  font-family: 'Roboto', sans-serif;
  color: $gray-1;
  font-weight: 300;
  line-height: 1.5;
  font-size: 14px;
}

.slider_countdown {
  background: rgba(228, 178, 71, 0.83);
  display: table;
  margin: auto;
  padding: 4px 13px;
  margin-top: 20px;
  position: absolute;
  top: 30%;
  font-size: 16px;
  color: white;
  left: 6%;
  
  div {
    display: inline-block;
    margin-right: 4px;
  }
  
  .date {
    font-size: 27px;
    font-weight: bold;
  }
}

a {
  text-decoration: none;
}

header {
  background: rgba(black, .7);
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 25;
  padding: 0px 0px 10px;
}

.top-row {
  background: url("/img/#{$base_web}/white-pattern.png?v=1") repeat scroll 0% 0% rgba(0, 0, 0, 1);
}

.address-header {
  float: left;
  color: white;
  font-size: 12px;
  margin-top: 9px;
}

//LANGUAGE SELECT
#lang {
  cursor: pointer;
  width: auto;
  height: 35px;
  display: inline-block;
  float: right;
  vertical-align: middle;
  padding-right: 10px;
  padding-left: 3px;
  background: black;
  
  #selected-language {
    box-sizing: border-box;
    padding: 11px 10px 17px 10px;
    height: 35px;
    width: auto;
    display: inline-block;
    font-family: 'Lato', sans-serif;
    color: white;
    font-size: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-align: center;
    line-height: 17px;
  }
  
  .arrow {
    display: inline-block;
    background: url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
    float: right;
    width: 25px;
    height: 35px;
    margin-top: 0px;
    
  }
  
  ul li {
    background: rgba(0, 0, 0, 0.58);
    text-align: left;
    width: 119px;
    font-size: 13px;
    box-sizing: border-box;
    font-family: 'Lato', sans-serif;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    padding-left: 14px;
    color: #3097d1;
    border-top: 1px solid #FFF;
    
    &:hover {
      opacity: 0.8;
    }
    
    a {
      color: white;
      font-weight: 500;
      text-decoration: none !important;
    }
  }
}

#language-selector-options {
  display: none;
  box-shadow: 0 1px 1px #6D6C6C;
  position: absolute;
  z-index: 22;
  margin-left: -4px;
}

.weather {
  width: 105px;
  float: right;
  border-right: 1px solid #c2c2c2;
  
  .grados {
    float: right;
    padding: 10px 0 17px 3px;
    margin-right: 17px;
    box-sizing: border-box;
    width: 40px;
    height: 35px;
    display: inline-block;
    color: white;
    font-size: 14px;
    font-family: 'Lato', sans-serif;
    text-align: center;
    letter-spacing: 1px;
    line-height: 19px;
  }
  
  .img_weather {
    float: right;
    width: 25px;
    height: 35px;
    padding-top: 4px;
    margin-right: 12px;
    box-sizing: border-box;
    
    img {
      width: 23px;
      text-align: center;
      display: block;
      margin: auto;
      margin-top: 5px;
    }
  }
}

#top-sections {
  width: 110px;
  float: right;
  border-right: 1px solid #c2c2c2;
  
  a {
    padding: 10px 0 17px 3px;
    margin-right: 5px;
    box-sizing: border-box;
    height: 35px;
    display: inline-block;
    color: white;
    font-size: 14px;
    font-family: 'Lato', sans-serif;
    text-align: center;
    letter-spacing: 1px;
    line-height: 19px;
  }
}

.button_precheckin {
  float: right;
  display: inline-block;
  margin-right: 10px;
  
  a {
    display: inline-block;
    background-color: $corporate_1;
    color: white;
    padding: 10px 35px;
    text-transform: uppercase;
    font-size: 14px;
  }
}

/*============== MENU ==================*/

#main_menu {
  text-align: right;
  margin-top: 35px;
}

ul#main-sections-inner {
  width: 100%;
  text-align: justify;
}

ul#main-sections-inner .main-section-div-wrapper {
  display: inline-block;
  
  a {
    display: block;
    padding: 14px 2px 3px;
    color: white;
    text-transform: uppercase;
    border-bottom: 3px solid transparent;
    line-height: 19px;
    text-decoration: none;
    font-size: 11px;
    letter-spacing: 2px;
  }
  
  a:hover {
    border-bottom: 3px solid $corporate-2;
  }
}

ul#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/*==== Slider ====*/

#slider_container {
  position: relative;
}

#slider_inner_container {
  position: relative;
  
  .image-slider-fixed {
    height: 700px;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    position: relative;
    
    img {
      width: 100%;
      height: auto;
      position: fixed;
      top: 0px;
      z-index: -2;
      min-width: 1920px;
    }
  }
  
  .slogan-container {
    position: relative;
    height: 500px;
  }
  
  .slider_text {
    display: block;
    box-sizing: border-box;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 57px;
    line-height: 70px;
    text-transform: uppercase;
    font-weight: lighter;
    text-shadow: 1px 0 8px #323232;
    position: absolute;
    top: 100px;
  }
}

.tp-bullets {
  bottom: 190px !important;
}

.tp-bullets .bullet {
  background: url("/img/madea/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/madea/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

#slider_container button {
  width: auto;
  border: none;
  font-weight: normal;
  padding: 6px 15px;
  background-color: $corporate_1;
  color: white;
  font-size: 18px;
  cursor: pointer;
  overflow: visible;
  
  &.button-promotion {
    left: -30px;
    position: absolute;
    padding: 10px;
  }
}

/*==== Booking Widget ====*/

#full_wrapper_booking {
  width: 100%;
  position: absolute;
  bottom: 60px;
  background: url(/img/#{$base_web}/book_pixel.png?v=1) rgba(102, 97, 102, 0.5);
  
}

.boking_widget_inline {
  padding-bottom: 20px;
  height: 100px;
}

.boking_widget_inline fieldset {
  float: left;
}

label#titulo_fecha_entrada, label#titulo_fecha_salida {
  display: none;
}

#contenedor_habitaciones > label {
  display: none;
}

.adultos.numero_personas {
  & > label {
    display: none !important;
  }
}

#titulo_ninos {
  display: none !important;
}

#booking fieldset {
  margin: 5px 0 0;
}

#search-button {
  font-size: 14px;
}

.web_support_label_1,
.web_support_label_2 {
  width: auto;
  display: inline-block;
  margin: 0px 10px;
}

/*============== Content ==================*/

#content {
  padding: 40px 0px;
  background: white;
}

.content_subtitle_wrapper {
  width: 800px;
  margin: 0 auto 40px;
  text-align: center;
  
  .content_subtitle_title {
    text-transform: uppercase;
    font-size: 32px;
    color: $corporate-1;
    margin-bottom: 30px;
  }
  
  .content_subtitle_title:after {
    content: "";
    width: 85px;
    border-bottom: 2px solid $corporate-1;
    display: block;
    margin: 17px auto 0px;
  }
}

/*============== Mini gallery ==================*/

.bx-controls-direction {
  display: none;
}

.bx-wrapper .bx-viewport {
  box-shadow: none;
  border: none;
  text-align: center;
  margin-left: 5px;
}

.bx-wrapper .bx-pager.bx-default-pager a {
  height: 13px;
  width: 13px;
  border-radius: 8px;
  background: $gray-3;
  
  &.active {
    background: $corporate-1;
  }
}

/*============== Full banners ==================*/

.full-banners {
  background: url("/img/madea/bg.jpg?v=1") no-repeat;
  background-size: cover;
  background-position: 0px -300px;
  overflow: hidden;
  padding: 95px 0px;
  text-align: center;
  
  .banner-block {
    width: 275px;
    background: white;
    position: relative;
    margin: 0 10px;
    display: inline-block;;
    text-align: center;
    box-sizing: border-box;
    padding: 20px 20px 40px;
    vertical-align: top;
  }
  
  .banner-block h3 {
    text-transform: uppercase;
    font-size: 17px;
    color: $corporate-1;
    margin: 20px 0px
  }
  
  .banner-block .description {
    margin-bottom: 20px;
  }
  
  .banner-block a {
    display: inline-block;
    background: $corporate-1;
    color: white;
    padding: 5px 20px;
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    width: 118px;
    margin: auto;
    
    &:hover {
      background: $corporate-2;
    }
  }
  
  .banner-block img {
    position: absolute;
    top: -18px;
    left: 0;
    right: 0;
    margin: 0 auto;
  }
}

/*============== Full banners ==================*/

.banners-bottom {
  padding: 40px 0px;
  background: white;
  
  .banner-block {
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0px 20px;
  }
  
  .banner-block h3 {
    font-size: 32px;
    font-weight: 300;
    color: $corporate-1;
    margin-bottom: 30px;
  }
}

/*============== Newsletter ==================*/

.newsletter-full {
  background: white;
  text-align: center;
  overflow: hidden;
  margin: 20px 0px;
  
  #suscEmailLabel, #suscNameLabel {
    display: none !important;
  }
  
  #title_newsletter {
    display: block;
    font-size: 32px;
    color: $corporate-1;
    margin-right: 30px;
    margin-bottom: 9px;
    padding-left: 30px;
  }
  
  #form-newsletter,
  #suscEmail,
  #newsletterButtonExternalDiv,
  #suscName {
    display: inline-block;
    vertical-align: super;
  }
  
  .desc {
    padding: 30px 300px 10px;
    font-size: 10px;
    
    hide {
      display: none;
    }
    
    a {
      color: $corporate_1;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .read_more_desc {
    color: $corporate_1;
    text-transform: uppercase;
    font-size: 10px;
    @extend .fa-angle-down;
    
    &:before {
      @extend .fa;
      margin: 0 5px;
    }
    
    &.active {
      @extend .fa-angle-up;
    }
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  input.error {
    border-color: red !important;
  }
  
  #suscEmail, #suscName {
    height: 32px;
    width: 250px;
    border: 1px solid $gray-3;
    padding: 1.7px 10px;
  }
  
  #newsletterButtonExternalDiv {
    vertical-align: top;
  }
  
  #newsletterButtonExternalDiv #newsletter-button {
    background: $corporate-1;
    color: white;
    text-transform: uppercase;
    padding: 10px 25px;
    font-size: 14px;
    outline: none;
    border: none;
    
  }
  
  .newsletter_checkbox {
    font-size: 12px;
  }
  
  .bordeInput.check_privacy.error + .title a {
    color: red;
  }
  
  .privacy_wrapper {
    font-size: 12px;
    text-decoration: underline;
    margin-top: 3px;
    
    a {
      color: $corporate_1;
    }
  }
}

body #content, body .newsletter-full {
  .check_newsletter {
    display: block;
    width: 600px;
    max-width: 100%;
    box-sizing: border-box;
    margin: 5px auto;
    font-size: 10px;
    
    strong {
      font-weight: bold;
    }
    
    a {
      color: $corporate_1;
      font-size: 10px;
      text-decoration: underline;
      
      &:hover {
        color: $corporate_2;
      }
    }
    
    label {
      font-size: 10px;
    }
  }
  
  .lopd_info {
    border: 1px solid #ededed;
    padding: 10px;
    font-size: 10px;
    line-height: 16px;
    color: #4b4b4b;
    width: 600px;
    max-width: 100%;
    box-sizing: border-box;
    margin: 10px auto;
    text-align: left;
    
    strong {
      font-weight: bold;
    }
    
    a {
      color: $corporate_1;
      text-decoration: underline;
      
      &:hover {
        color: $corporate_2;
      }
    }
  }
}

/*============ Rooms ===========*/

.rooms-wrapper {
  padding: 40px 0px 40px;
}

.rooms-wrapper .block-room {
  overflow: hidden;
  background: $gray-4;
  margin: 10px 0px;
  
  .column-left {
    float: left;
    width: 425px;
    padding: 40px 40px;
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    
    #title {
      margin-bottom: 30px;
      font-family: "Oswald", sans-serif;
      font-size: 24px;
      font-weight: 300;
    }
    
    p {
      text-align: center;
      margin-bottom: 20px;
      color: #676566;
    }
    
    span {
      display: block;
      width: 50px;
      border: 2px solid $corporate-1;
      margin: 0 auto 30px;
    }
    
    .desc {
      height: 130px;
      overflow: hidden;
    }
  }
  
  .column-right {
    float: right;
    width: 715x;
    height: 350px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    
    img {
      width: 700px;
      vertical-align: bottom;
      margin-top: -50px;
    }
  }
  
  .links-buttons {
    margin-top: 30px;
  }
  
  .links-buttons a {
    text-decoration: none;
    background: $corporate-1;
    color: white;
    display: inline-block;
    padding: 7px 15px;
    text-align: center;
    margin: 0px 1px;
    width: 80px;
  }
  
  .links-buttons a:hover {
    background: $corporate-2;
  }
  
  .links-buttons a.see_more {
    display: none;
  }
  
  &#reverse .column-left {
    float: right;
  }
  
  &#reverse .column-right {
    float: left;
  }
}

/************************* SCAPES/OFERTAS ************************/

.scapes-blocks {
  overflow: hidden;
  margin-top: 25px;
}

.scapes-blocks .block {
  background: #f6f7f8;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
  
  a.enlace_offer {
    display: inline-block;
    height: 300px;
    overflow: hidden;
    position: relative;
    width: 100%;
  }
  
  a img {
    margin-bottom: -5px;
    width: 100%;
    position: absolute;
    margin: auto;
    top: 0;
    bottom: 0;
  }
  
  .description {
    padding: 20px;
    position: relative;
    background-color: $gray-4;
    padding-right: 160px;
    
    .title-module {
      font-size: 20px;
      color: $corporate_1;
      font-weight: normal;
      
      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }
    
    ul {
      position: absolute;
      width: 200px;
      right: 0;
      top: 18px;
      text-align: right;
      padding-right: 10px;
      
      li {
        display: inline-block;
      }
      
      li a {
        background: $corporate-1;
        color: white;
        text-decoration: none;
        padding: 8px 15px;
      }
      
      li a:hover {
        background: $corporate-2;
      }
    }
    
    p {
      margin-bottom: 0;
    }
  }
  
  .mobiledescription {
    padding: 2em;
    font-weight: 400;
  }
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_2;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {
  
  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  
  h5 {
    color: $corporate_1;
  }
}

//*************** Location and contact *****************//

#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
  
  .location-info, .form-contact {
    width: 520px !important;
  }
  
}

.location-info-and-form-wrapper h1 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px rgb(190, 190, 190) !important;
  margin-bottom: 30px;
  color: $corporate_1;
}

.location-info-and-form-wrapper {
  p, strong {
    color: dimgrey;
  }
}

.location-info {
  font-size: 17px;
  
  p {
    margin-bottom: 10px;
    font-weight: 300;
  }
  
  strong {
    font-weight: bold;
  }
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  
  iframe {
    margin-bottom: 25px;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

#contactContent .info {
  margin-top: 30px;
  overflow: hidden;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;
  
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
  padding-left: 3px;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #e1e1e1;
  color: dimgrey;
  text-align: left;
  font-size: 15px;
  padding: 5px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
  
}

.form-contact #contact-button:hover {
  background-color: $corporate-2 !important;
}

.iframe_maps {
  display: table;
  padding: 20px 0px;
}

.location-info-and-form-wrapper {
  margin: 20px 0px;
  background: white;
  box-sizing: border-box;
  display: table;
  padding: 20px 0px;
}

#contactContent .contInput {
  width: 94%;
}

.form-contact #contact-button-wrapper {
  margin-right: 0px;
}

.menu_right_wrapper {
  width: 963px !important;
  margin-right: 0;
  float: right;
}

/*============== Footer ==================*/

footer {
  background: #f7f7f7;
  padding-top: 40px;
  clear: both;
}

.wrapper_footer_columns {
  padding-bottom: 30px;
  text-align: center;
}

.footer_column {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 20%;
  display: block;
  float: left;
  
  h3 {
    margin-bottom: 20px;
    font-weight: 400;
    color: rgb(90, 90, 90);
  }
  
  ul li a {
    color: rgb(90, 90, 90);
  }
}

.full-copyright {
  text-align: left;
  padding: 10px 0;
  
  a, span {
    color: $gray-2;
  }
}

div#social_wrapper_footer {
  text-align: right;
  
  .fa {
    font-size: 20px;
    text-align: center;
    width: 35px;
    padding: 7px 0;
    background-color: #DDD;
    color: white;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.8);
    border-radius: 5px;
  }
  
  .fa-facebook {
    background-color: #507CBE;
  }
  
  .fa-twitter {
    background-color: #63CDF1;
  }
  
  .fa-google-plus {
    background-color: #4D4F54;
  }
  
  .fa-youtube {
    background-color: #F16261;
  }
  
  .fa-instagram {
    background-color: #BF8360;
    background: -webkit-radial-gradient(10px 60px, #ffda75 18%, #ff3a49 43%, #ff3a49 50%, #e518a1 70%, #6634e1 100%);
  }
}

div#div-txt-copyright {
  & > p {
    width: 60%;
    float: left;
  }
}

.siguenos_text {
  display: inline-block;
  vertical-align: top;
  margin-top: 7px;
  margin-right: 12px;
}

/*============== Booking Fancybox ==================*/

#wrapper_booking_fancybox {
  
  .booking_form {
    background: #E6E4E4;
  }
  
  .date_box .date_year {
    color: $gray-2;
  }
  
  .wrapper_booking_button .promocode_input {
    width: 150px;
    font-size: 14px;
    line-height: 40px;
  }
  
  .wrapper_booking_button button {
    width: 100px;
  }
  
}

/*============ Configuration blocks ===========*/
.blocks_section_container {
  margin-top: 60px;
  
  .block-activity {
    width: 364px;
    display: inline-block;
    float: none;
    margin-right: 19px;
    margin-bottom: 20px;
    vertical-align: top;
    
    &.block-3 {
      display: inline-block;
      margin-right: 0;
    }
  }
  
  .block_activity_title {
    padding: 30px 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
    font-style: italic;
    text-align: center;
    background: $corporate_1;
  }
  
  .block_description {
    float: left;
    width: 255px;
    height: 154px;
    background-color: rgb(245, 245, 245);
    padding: 20px 10px 10px 10px;
    text-align: center;
    position: relative;
  }
  
  .room_home_description {
    text-align: left;
    font-weight: 200;
    font-size: 12px;
    line-height: 19px;
    height: 45px;
    overflow: hidden;
    margin-top: 5px;
    
    p {
      text-align: center;
    }
  }
  
  //room buttons
  .buttons-rooms {
    margin-top: 15px;
    text-align: center;
    padding-bottom: 15px;
  }
  
  .button-room-more button {
    cursor: pointer;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    color: white;
    border: 0;
    background-color: $corporate_1;
    font-size: 12px;
    text-transform: uppercase;
    width: 100px;
    padding: 5px 20px 5px 20px;
    text-decoration: none;
  }
  
  .button-room-more button:hover {
    color: white;
    background-color: $corporate_2;
  }
  
  .activity_home_image {
    width: 365px;
    height: 120px;
    overflow: hidden;
    display: block;
    position: relative;
  }
  
  .room-img-lupa {
    position: absolute;
    top: 100px;
    right: 0;
    margin-top: -90px !important;
    margin-right: 320px;
  }
}

div#logoDiv {
  width: 135px !important;
  margin-top: 12px;
}

.gallery_1 li .crop img {
  min-width: 100%;
  min-height: 212px;
  height: auto !important;
}

/*===== Logo banners =======*/
.white_background_logo_banners {

}

.logo_banners_wrapper {
  text-align: center;
  width: 300px;
  
  .logo_element {
    vertical-align: middle;
  }
  
  ul.logo_banner_center {
    width: 75%;
    text-align: justify;
    margin: 25px auto 0;
    
    &:after {
      content: ' ';
      display: inline-block;
      width: 100%;
      height: 0;
    }
    
    div {
      display: inline-block;
      text-align: center;
      margin: 10px 0px;
    }
  }
}

/*===== Ticks after booking widget ======*/
.tick_element {
  display: inline-block;
  width: 282px;
  box-sizing: border-box;
  text-align: center;
  padding: 13px 0;
  
  img {
    vertical-align: middle;
    margin-right: 5px;
  }
  
  span.tick_title {
    color: white;
    font-size: 12px;
  }
  
  &.border {
    border-left: solid 1px rgba(255, 255, 255, 0.1);
  }
}

#slider_container .ticks_wrapper, #slider_inner_container .ticks_wrapper {
  position: absolute;
  bottom: 0;
  z-index: 22;
  width: 100%;
  min-width: 1140px;
  background: url(/img/madea/book_pixel.png?v=1);
  height: 57px;
  border-bottom: solid 3px $corporate_1;
}

.social_likes {
  div#facebook_like {
    text-align: right;
    float: left;
    margin-top: 1px;
  }
  
  div#google_plus_one {
    margin-left: 14px;
    display: inline-block;
    float: left;
    
  }
}

/*===== We call you ====*/
.we_call_wrapper {
  padding-bottom: 25px;
  margin-bottom: 20px;
  background: white;
  width: 100%;
  text-align: center;
  
  h4.we_call_title {
    font-size: 30px;
    font-weight: 300;
    color: $corporate_1;
    margin-bottom: 10px;
    
  }
  
  .we_call_description {
    
    color: rgb(142, 142, 142);
    font-weight: lighter;
    margin-bottom: 5px;
  }
  
  form.we_call_you {
    margin-top: 15px;
    
    input {
      width: 210px;
      padding: 10px;
      
      &.input-error {
        border: 1px solid red;
      }
    }
    
    .check_newsletter {
      margin: 5px 0;
      
      input[type=checkbox] {
        width: auto;
      }
      
      a, label {
        color: #323232;
        font-size: 12px;
      }
      
      a {
        text-decoration: underline;
      }
    }
    
    label.error {
      display: none;
    }
  }
  
  button.we_call_button {
    background: $corporate_1;
    vertical-align: bottom;
    color: white;
    text-transform: uppercase;
    padding: 10px 25px;
    font-size: 14px;
    outline: none;
    border: none;
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

// Mis reservas
.content-access {
  margin: 0 auto 40px;
  text-align: center;
  
  .section-title {
    text-transform: uppercase;
    font-size: 32px;
    color: $corporate_1;
    margin-bottom: 30px;
  }
  
  .section-title:after {
    content: "";
    width: 85px;
    border-bottom: 2px solid $corporate_1;
    display: block;
    margin: 17px auto 0px;
  }
  ul{
    display: flex;
    justify-content: center;
    li button{
          border-radius: 0px !important;
          height: 32px !important;
          width: 137px !important;
          color: white;
          margin: auto;
          width: 150px;
          text-transform: uppercase;
          border-width: 0;
          display: block;
          background-color: $corporate_1;
          margin-top: 20px;
    }
    .modify-reservation{
       background-color: black;
    }
  }

  
  #my-bookings-form-search-button {
    border-radius: 0px !important;
    height: 32px !important;
    width: 137px !important;
    background-color: $corporate_1;
    color: white;
    margin: auto;
    width: 150px;
    text-transform: uppercase;
    border-width: 0;
    display: block;
    margin-top: 20px;
  }
  
  #cancel-button-container #cancelButton {
    border-radius: 0px !important;
    height: 32px !important;
    width: 137px !important;
    background-color: $corporate_1;
    color: white;
    margin: auto;
    width: 150px;
    text-transform: uppercase;
    border-width: 0;
    display: none;
    margin-top: 20px;
  }
  
  label {
    display: block;
    margin-top: 5px;
  }
  
  input {
    display: block;
    margin: 0 auto;
    width: 216px;
  }
  
  .my-bookings-booking-info {
    margin: 0 auto !important;
  }
}

// Languages
.fr {
  .rooms-wrapper .block-room .links-buttons a {
    width: 92px;
  }
}

// Fancybox
.booking-fancy {
  
  width: 330px !important;
  
  .fancybox-wrap {
    width: auto !important;
  }
  
  .fancybox-inner {
    width: 300px !important;
  }
  
}

body {
  .landing_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #333;
    opacity: .7;
  }
}


.fancy_landing {
  .fancybox-close {
    display: block !important;
  }
  
  .fancybox-inner {
    overflow: visible !important;
  }
}

#contactContent {
  #contact {
    #privacy, #promotions {
      width: 20px;
    }
  }
}

.newsletter_additional_form {
  margin-top: 20px;
}

.popup_policy_newsletter {
  input {
    display: inline-block;
    width: auto;
  }
  
  a {
    display: inline-block;
  }
}

.fancybox-skin {
  width: 91%;
}

.checking_popup {
  color: white;
  text-align: center;
  position: relative;
  width: 520px;
  padding: 30px 30px;
  box-sizing: border-box;
  background-size: cover;
  
  &:before {
    background: rgba(0, 0, 0, 0.6);
    content: '';
    @include full_size;
  }
  
  .checking_popup_h3 {
    position: relative;
    font-size: 16px;
    margin-bottom: 5%;
    z-index: 2;
  }
  
  .checking_popup_desc {
    position: relative;
    z-index: 2;
    
    div {
      text-align: center;
      margin-top: 12px;
      
      .checkin_online_button {
        button {
          width: auto;
          border: none;
          font-weight: normal;
          padding: 6px 15px;
          background-color: #0099e6;
          color: white;
          font-size: 18px;
          cursor: pointer;
          overflow: visible;
        }
      }
    }
    
    .p1 {
      text-align: center;
      margin-top: 5%;
      margin-bottom: 5%;
    }
    
    .p3 {
      text-align: center;
      margin-bottom: 5%;
      
      .ios_img {
        margin-left: 10%;
      }
    }
  }
}

body .cancel_booking_questions .questions_labels {
  input {
    display: inline-flex;
    border: 1px solid black !important;
  }
}
