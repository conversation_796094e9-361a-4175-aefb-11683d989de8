<div id="newsletter">
    <h2 id="title_newsletter">{{ newsletter_custom.subtitle|safe }}</h2>
    <form id="form-newsletter" name="myForm" method="post" action="/utils?action=newsletter">
        <label for="suscName" id="suscNameLabel" style="display: block;">{{T_introduce_email}}</label>
        <input type="text" id="suscName" class="bordeInput" name="suscName" value="" placeholder="{{ T_nombre_y_apellidos }}">
        <label for="suscEmail" id="suscEmailLabel" style="display: block;">{{T_introduce_email}}</label>
        <input type="text" id="suscEmail" class="bordeInput" name="suscEmail" value="">
        <div id ="newsletterButtonExternalDiv">
            <button type="submit" onclick="return false;" id="newsletter-button">{{T_enviar}}</button>
        </div>
        {% if newsletter_custom_check and newsletter_custom_check.content %}
            {{ newsletter_custom_check.content|safe }}
        {% else %}
        <div class="privacy_wrapper">
            <input class="bordeInput check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
            <span class="title"><a href="/{{language}}/?sectionContent=politica-de-privacidad.html" class="myFancyPopup fancybox.iframe">{{T_lopd}}</a></span>
        </div>
        <div class="check_newsletter">
            <div class="newsletter_checkbox">
                <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="privacy"/>
                <label for="promotions">{{T_acepto_promociones}}</label>
            </div>
        </div>
        {% if newsletter_custom.content %}
            <div class="desc">{{ newsletter_custom.content|safe }}</div>
            {% if '<hide' in newsletter_custom.content %}
                <a href="#title_newsletter" class="read_more_desc">{{ T_ver_mas }}</a>
            {% endif %}
        {% endif %}
        {% endif %}

    <input type="hidden" name="idPartner" value="DEFAULT"></form>
</div>


<script type="text/javascript">

$(document).ready(function(){
    $(".read_more_desc").click(function () {
        $(this).toggleClass("active");
        $(this).prev().find("hide").slideToggle();
    });
    $("#form-newsletter").validate({
        rules: {
	        suscEmail: {
                required: true,
                email: true
            },
            privacy: "required",
            promotions: "required",
            suscName: {
                required: true
            }
		},
        highlight: function(input) {
            $(input).addClass('error');
        },
        errorPlacement: function(error, element){
            //this keeps enable the validation but hides the error message
        }
    });

    $("#newsletter-button").click(function() {
        if ($("#form-newsletter").valid()) {

            $.post("/utils?action=newsletter&language={{ language_code }}",
                {
                    'email': $("#suscEmail").val(),
                    'name': $("#suscName").val()
                },

                function(data) {
                    alert("{{ T_gracias_newsletter }}");
                    $("#suscEmail").val("");
                    $("#suscName").val("");
                }
            );

        } /*else {
             alert("{{ T_email_required }} " )
            console.log("invalid");
        }*/

    });

});
</script>