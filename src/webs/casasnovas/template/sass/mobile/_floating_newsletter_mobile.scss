body .faldon.floating_button.myFancyPopup{
  position: fixed;
  bottom: 16%;
  left: 5px;
  z-index: 99;
  width: 40px;
  height: 40px;
  border: 1px solid white;
  border-radius: 50%;
  background: rgba($corporate_1, 0.8);
  i{
    position: absolute;
    color: white;
    top: 8px;
    left: 7px;
    font-size: 25px;
  }
}

@media screen and (max-width: 360px){
  body .faldon.floating_button.myFancyPopup{
    bottom: 20%;
  }
}
@media screen and (max-width: 375px){
  body .faldon.floating_button.myFancyPopup{
    bottom: 18%;
  }
}

.newsletter_popup_wrapper {
  //position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 1000;
  //height: 210px;
  display: none;
  box-shadow: 1px 1px 5px 2px black;
  font-family: $primary_font;
  color: black;

  .close_button_faldon {
    position: relative;
    @include transition(transform, .4s);
    .close_button_content{
      position: absolute;
      width: 50px;
      height: 50px;
      display: block;
      top: -235px;
      background: rgba($corporate_2, 0.8);
      border-radius: 50%;
      right: 25px;
      z-index: 2;
      i{
        color: white;
        font-size: 35px;
        position: absolute;
        left: 14px;
        top: 10px;
        font-weight: lighter;
      }
    }
  }

  &:after {
    content: "";
    @include full_size;
    background: rgba(white, .5);
    z-index: 1;
  }



  .newsletter_popup, .newsletter_popup_thanks {
    position: relative;
    height: 100%;
    z-index: 5;
    width: 100%;

    .center_block {
      @include center_y;
      width: 100%;
      text-align: center;
      z-index: 3;

      .faldon_title {
        font-family: $primary_font;
        letter-spacing: 0;
        font-weight: 500;
        color: $corporate_2;
        font-size: 22px;
      }

      .faldon_description {
        font-size: 13px;
        color: black;
        line-height: 23px;
        width: 80%;
        margin: 0 auto;
        letter-spacing: 0;
      }

      .faldon_link {
        display: inline-block;
        text-transform: uppercase;
        padding: 7px 15px;
        background: $corporate_1;
        color: white;
        text-decoration: none;
        margin-top: 20px;
        cursor: pointer;
        @include transition(background, .4s);

        &:hover {
          background: darken($corporate_1, 10%);
        }
      }

      .faldon_newsletter {
        margin-top: 10px;
        display: inline-block;
        width: 100%;

        .faldon_send{
          font-weight: 700;
          font-size: 14px;
          padding: 0px 30px 2px;
          width: 200px;
        }

        input#faldon_email {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          text-align: center;
          border: 0;
          height: 32px;
          width: 200px;
          box-sizing: border-box;
          padding-left: 10px;
          background: white;
          margin-bottom: 20px;
          //color: white;

          &::-webkit-input-placeholder {
            /* Chrome/Opera/Safari */
            //color: white;
          }
          &::-moz-placeholder {
            /* Firefox 19+ */
            color: white;
          }
          &:-ms-input-placeholder {
            /* IE 10+ */
            //color: white;
          }
          &:-moz-placeholder {
            /* Firefox 18- */
            //color: white;
          }
        }

        button {
          height: 32px;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 0;
          background: $corporate_2;
          color: white;
          cursor: pointer;
          text-transform: uppercase;
          padding: 0 10px;
          @include transition(background, .4s);

          &:hover {
            background: darken($corporate_1, 10%);
          }
        }

        .check_faldon {
          margin-top: 10px;

          .check_privacy {
            display: inline-block;
            vertical-align: middle;

            &.error + .newsletter_popup {
              color: #ff6b6a !important;
            }
          }

          .newsletter_popup {
            display: inline-block;
            vertical-align: middle;
            color: $corporate_1;
            width: 87%;
            font-size: 10px;
          }
        }

        .faldon_checkbox {
          color: black;
          font-size: 11px;
          letter-spacing: 0;
          .newsletter_popup{
            color:black !important;
          }
        }
      }
    }
  }
}