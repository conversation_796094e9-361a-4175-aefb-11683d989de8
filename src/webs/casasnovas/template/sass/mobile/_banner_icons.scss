.banner_icons_full_wrapper {
  padding: 20px;
  .banner_icons_content {
    text-align: center;
    .content_title {
      @include title_styles;
      font-size: 32px;
      line-height: 38px;
      margin-bottom: 30px;
      .subtitle {
        display: block;
        font-size: 24px;
        line-height: 30px;
        color: $corporate_2;
      }
    }
    .content_text {
      @include text_styles;
    }
  }
  .banner_icons_wrapper {
    padding: 0 20px;
    .owl-stage-outer {
      padding-top: 30px;
    }
    .icon_wrapper {
      display: inline-block;
      text-align: center;
      width: 100%;
      .icon {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: $corporate_1;
        box-shadow: 20px 15px 30px -5px rgba(0, 0, 0, 0.3);
        &:before {
          content: '';
          @include center_xy;
          width: 75px;
          height: 75px;
          border: 2px solid $corporate_2;
          border-radius: 50%;
        }
        i {
          color: white;
          font-size: 32px;
          @include center_xy;
        }
      }
      .icon_text {
        display: block;
        font-size: 14px;
        margin-top: 15px;
        text-align: center;
        color: $black;
      }
    }
    .owl-nav {
      @include center_y;
      left: 0;
      right: 0;
      height: 0;
      .owl-prev, .owl-next {
        @include center_y;
        bottom: auto;
        left: 10px;
        right: auto;
        display: inline-block;
        vertical-align: middle;
        i {
          font-size: 38px;
          color: $corporate_1;
          @include transition(color, .6s);
        }
      }
      .owl-next {
        left: auto;
        right: 10px;
      }
    }
  }
}