.banner_gallery_wrapper {
    @extend .sec_pad;
    @extend .bg_noise;

    .container {
        margin: auto;

        .gallery_filter_wrapper {
            text-align: center;
            padding-bottom: $pad_4x;
            position: relative;

            .filter {
                display: inline-block;
                font-family: $secondary_font;
                font-weight: 700;
                color: $black;
                padding: 0 15px;
                font-size: 18px;
                cursor: pointer;
            }

            &::before, &::after {
                position: absolute;
                content: '';
                height: 1px;
                background-color: #d6d6d6;
                bottom: 40px;
            }

            &::before {
                left: 40px;
                right: calc(50% + 10px);
            }

            &::after {
                right: 40px;
                left: calc(50% + 10px);
            }
        }

        .gallery_wrapper {
            @include clearfix;

            .picture_wrapper {
                float: left;

                &::before {
                    display: none;
                }

                .img_out_box {

                    .img_in_box {
                        position: relative;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }

                        &::before {
                            content: '';
                            @include full_size;
                            background-color: $corporate_1;
                            opacity: 0;
                            transition: all .3s;
                            z-index: 10;
                        }

                        &::after {
                            position: absolute;
                            font-family: "Font Awesome 5 Pro";
                            content: '\f00e';
                            width: 30px;
                            height: 30px;
                            color: white;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            color: white;
                            text-align: center;
                            font-size: 40px;
                            opacity: 0;
                            transition: all .8s;
                            transition-delay: .3s;
                            z-index: 20;
                        }

                        &:hover {

                            &::before {
                                opacity: .7;
                            }

                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }

                .img_corners_wrapper {
                    position: absolute;
                    top: -3px;
                    bottom: -3px;
                    left: -3px;
                    right: -3px;
                    z-index: 10;

                    .item_corner {
                        position: absolute;
                        width: 40px;
                        height: 40px;
                        transition: all .3s;

                        &.top_left {
                            border-bottom: solid 20px transparent;
                            border-left: solid 20px white;
                            border-right: solid 20px transparent;
                            border-top: solid 20px white;
                            top: 0;
                            left: 0;
                        }

                        &.top_right {
                            border-bottom: solid 20px transparent;
                            border-left: solid 20px transparent;
                            border-right: solid 20px white;
                            border-top: solid 20px white;
                            top: 0;
                            right: 0;
                        }

                        &.bottom_left {
                            border-bottom: solid 20px white;
                            border-left: solid 20px white;
                            border-right: solid 20px transparent;
                            border-top: solid 20px transparent;
                            bottom: 0;
                            left: 0;
                        }

                        &.bottom_right {
                            border-bottom: solid 20px white;
                            border-left: solid 20px transparent;
                            border-right: solid 20px white;
                            border-top: solid 20px transparent;
                            bottom: 0;
                            right: 0;
                        }
                    }
                }
            }
        }

        @media (min-width: 1600px) {
            width: 1500px;

            .picture_wrapper {
                width: 500px;
                margin-bottom: 80px;

                .img_out_box {
                    padding: 0 $pad_2x;

                    .img_in_box {
                        height: 400px;
                    }
                }
            }
        }

        @media (min-width: 1240px) and (max-width: 1599px) {

            .picture_wrapper {
                width: 33%;
                margin-bottom: 60px;

                .img_out_box {
                    padding: 0 $pad;

                    .img_in_box {
                        height: 350px;
                    }
                }
            }
        }

        @media (max-width: 1239px) {

            .picture_wrapper {
                width: 33%;
                margin-bottom: 40px;

                .img_out_box {
                    padding: 0 $pad;

                    .img_in_box {
                        height: 300px;
                    }
                }
            }
        }
    }
}





















.gallery_filter_wrapper {
    .gallery_block {
        padding: 0 calc((100% - 1140px) / 2);
        margin-bottom: 50px;
        &:before {
            top: 200px;
        }
        .gallery_title {
            padding: 0 0 30px 0;
            text-transform: uppercase;
            font-size: 34px;
            font-weight: bold;
            color: $corporate_1;
            display: block;
        }

        .gallery_group_wrapper {
            display: table;
            width: 100%;

            .image_wrapper {
                position: relative;
                display: inline-block;
                height: 270px;
                float: left;
                margin-left: 10px;
                margin-bottom: 20px;
                width: calc(25% - 15px);
                overflow: hidden;
                cursor: pointer;
                @extend .fa-search-plus;
                &:before {
                    @extend .fal;
                    z-index: 10;
                    font-size: 40px;
                    color: white;
                    opacity: 0;
                    @include center_xy;
                    @include transition(opacity, 1s);
                }
                &:after {
                    content: '';
                    @include full_size;
                    background-color: rgba($corporate_1, .8);
                    z-index: 5;
                    opacity: 0;
                    visibility: hidden;
                    @include transition(all, .6s);
                }
                .svg_logo {
                    @include center_xy;
                    z-index: 7;
                    height: 50%;
                    opacity: 0;
                    visibility: hidden;
                    @include transition(all, .6s);
                    * {
                        fill: white;
                    }
                }
                .img_info {
                    @include center_xy;
                    display: inline-block;
                    color: white;
                    font-size: 14px;
                    opacity: 0;
                    visibility: hidden;
                    z-index: 8;
                    @include transition(all, .6s);
                }
                img {
                    @include center_image;
                }
                &:first-of-type {
                    width: calc(50% - 10px);
                    height: 560px;
                    margin: 0 10px 0 0;
                }
                &:nth-of-type(3), &:nth-of-type(5) {
                    margin-left: 20px;
                }
                &:hover {
                    &:before, &:after, .banner_info {
                        opacity: 1;
                        visibility: visible;
                    }
                    .svg_logo {
                        opacity: .15;
                        visibility: visible;
                    }
                }
            }
            &:nth-of-type(odd) {
                .image_wrapper {
                    margin-left: 0;
                    margin-right: 10px;
                    &:first-of-type {
                        float: right;
                        margin: 0 0 0 10px;
                    }
                    &:nth-of-type(2), &:nth-of-type(4) {
                        margin-left: 0;
                        margin-right: 20px;
                        &:last-of-type {
                            float: right;
                        }
                    }
                }
            }
        }
    }
}