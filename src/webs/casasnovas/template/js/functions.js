$(window).load(function () {

    ;(function($, win) {
      $.fn.inViewport = function(cb) {
         return this.each(function(i,el){
           function visPx(){
             var H = $(this).height() + 200,
                 r = el.getBoundingClientRect(), t=r.top, b=r.bottom;
             return cb.call(el, Math.max(0, t>0? H-t : (b<H?b:H)));
           } visPx();
           $(win).on("resize scroll", visPx);
         });
      };
    }(jQuery, window));

    var classInView = $(".content_title, .desc, .carousel_wrapper, .gallery_wrapper")
    var classOnScroll = $('.home_section .wrapper_content.banner_section_title, .home_section .wrapper_content.banner_section_title .content_title, .home_section .wrapper_content.banner_section_title .desc')

    classInView.inViewport(function(px){

        if (px) {
            $(this).addClass("inview_class");
            classOnScroll.removeClass("inview_class");
        }
    });


    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });

    $(".myFancyPopupMenu").fancybox({
                                    maxWidth: 1200,
                                    maxHeight: 1200,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });

    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");
        }

        var package_preselection = $(this).attr('data-smartpackage');
        if (package_preselection) {
            $(".paraty-booking-form").each(function () {
                var package_preselection = $("<input type='hidden' name='package_preselection' value='true'></input>");
                package_preselection.addClass('hidden_package_preselection');
                $(this).append(package_preselection);
                $(this).find(".promocode_wrapper").addClass("promocode_hidden");
            });
        } else {
            $(".paraty-booking-form").each(function () {
                $(this).find(".promocode_wrapper").removeClass("promocode_hidden");
            });
        }

        var hidden_name_button = $(this).attr('data-hidden_name'),
            hidden_name = $("<input type='hidden' name='hidden_name'></input>").val(hidden_name_button);
        if (hidden_name_button) {
            $(".paraty-booking-form").each(function () {
                hidden_name.addClass('hidden_name');
                $(this).append(hidden_name);
            });
        } else {
            $(".paraty-booking-form").each(function () {
                $(this).find(".hidden_name").detach();
            });
        }

        smartDatas($(this));
    });

    showMenu();
});


$(function(){
    gallery_inside_content_builder();
})

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed');
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed');
    }
}

$(window).scroll(function() {

    var scroll = $(window).scrollTop();
    var classOnScroll = $('.home_section .wrapper_content.banner_section_title, .home_section .wrapper_content.banner_section_title .content_title, .home_section .wrapper_content.banner_section_title .desc')

    if (scroll >= 500) {
        classOnScroll.addClass("onscroll_class");
    } else {
    	classOnScroll.removeClass("onscroll_class");
    }
});

$(".circulo").click(function () {
    var section_content_position = $("section#content").offset().top;
    $("html, body").stop().animate({scrollTop: 0}, '900', 'swing', function () {
    });
});

function gallery_inside_content_builder() {
    var target_content = $(".banner_section_title");
    if (!target_content.length) {return false;}

    var gallery_builder = target_content.find(".gallery_wrapper"),
        gallery_carousel = target_content.find(".gallery_wrapper.js-owl");

    if (!gallery_builder.length) {return false;}

    var target_images = gallery_builder.find("img");

    var string_corners = $('<div class="img_corners_wrapper">' +
        '<div class="item_corner top_left"></div>' +
        '<div class="item_corner top_right"></div>' +
        '<div class="item_corner bottom_left"></div>' +
        '<div class="item_corner bottom_right"></div>' +
        '</div>');

    target_images.each(function(){
        var image_to_process = $(this).clone();
        var link_container = $("<a class='picture_link' rel='lightbox[content_gallery]'></a>");
        link_container.attr('href', image_to_process.attr('src'));
        var image_wrapper = $('<div class="picture_wrapper"></div>');
        image_wrapper.append(image_to_process);

        image_wrapper.append(string_corners.clone());

        link_container.append(image_wrapper);
        gallery_builder.append(link_container);
    });

    target_images.remove();

    gallery_builder.wrap('<div class="content_js_gallery"></div>');

    if (gallery_carousel.length) {
        gallery_carousel.addClass("owl-carousel");
        gallery_carousel.owlCarousel({
            loop: true,
            nav: false,
            navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
            dots: true,
            items: 3,
            margin: 15,
            smartSpeed: 600,
            fluidSpeed: 600,
            navSpeed: 600,
            dotsSpeed: 600,
            dragEndSpeed: 600,
            autoplay: true
        });
    }
}

function smartDatas(selector) {
    var promocode = selector.attr("data-promocode"),
        smart_promocode = selector.attr('data-smartpromocode'),
        data_ini = selector.attr("data-smartdateini"),
        data_fin = selector.attr("data-smartdatefin"),
        namespace = selector.attr("namespace"),
        data_package_orders = selector.attr('data-smartpackageorder');

    if (namespace){
        $(".hotel_element_available[namespace='" + namespace + "'").trigger('click');
    }

    if (promocode) {
        $("#data .paraty-booking-form input[name=promocode]").val(promocode);
    }

    if (smart_promocode) {
        $("#data .paraty-booking-form input[name=promocode]").val(smart_promocode);
    }

    if(data_ini) {
        $("#data input[name=startDate]").val(data_ini);

        if (data_fin) {
            $("#data input[name=endDate]").val(data_fin);
        } else {
            var end_date = $.datepicker.parseDate("dd/mm/yy", data_ini);
            end_date.setDate(end_date.getDate() + 1);
            end_date = $.datepicker.formatDate("dd/mm/yy", end_date);
            $("#data input[name=endDate]").val(end_date);
            $("#data input[name=endDate]").datepicker("option", "minDate", end_date);
        }
    }

   var packages_orders_input = $("input[name='packages_order']");
    if (!packages_orders_input.length) {
        $(".paraty-booking-form").each(function(){
            $(this).append($("<input type='hidden' name='packages_order' value=''></input>"));
        });
        packages_orders_input = $("input[name='packages_order']");
    }
    if (data_package_orders) {
        packages_orders_input.val(data_package_orders);
    } else {
        packages_orders_input.val("");
    }
}