$(window).load(function(){
    $("header, .breadcrumbs").addClass("showed");

    var inputHome = $("input#home_section_input");

    if (inputHome.length) {
        $('body').addClass('home_section');
    } else {
        $('body').addClass('inner_section');
    }

    if ($(".read_rooms_more_content").length) {
        var btn_room = $(".read_rooms_more_content");
        btn_room.click(function (e) {
            e.preventDefault();
            $(this).closest(".text").toggleClass("active");
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(this).find("span").text($.i18n._("T_ver_menos_info"));
            } else {
                $(this).find("span").text($.i18n._("T_ver_mas_info"));
            }
        });
    }

});


lastScroll = 0;

$(window).scroll(function() {

    var scroll = $(window).scrollTop();

    if (scroll >= 500) {
        $("header").addClass("light");
    } else {
    	$("header").removeClass("light");
    }

    /* asigna clase cuando hace scroll to up */

   var scrollToUp = $(window).scrollTop();
   if(lastScroll - scrollToUp > 0) {
        $("header, .breadcrumbs").addClass("showed");
    } else if (scrollToUp <= 0) {
        $("header, .breadcrumbs").addClass("showed");
    }
    else {
        $("header, .breadcrumbs").removeClass("showed");
    }
   lastScroll = scrollToUp;
});

$(function(){
    gallery_inside_content_builder();
})

function gallery_inside_content_builder() {
    var target_content = $(".section-content");
    if (!target_content.length) {return false;}

    var gallery_builder = target_content.find(".gallery_wrapper"),
        gallery_carousel = target_content.find(".gallery_wrapper.js-owl");

    if (!gallery_builder.length) {return false;}

    var target_images = gallery_builder.find("img");

    var string_corners = $('<div class="img_corners_wrapper">' +
        '<div class="item_corner top_left"></div>' +
        '<div class="item_corner top_right"></div>' +
        '<div class="item_corner bottom_left"></div>' +
        '<div class="item_corner bottom_right"></div>' +
        '</div>');

    target_images.each(function(){
        var image_to_process = $(this).clone();
        var link_container = $("<a class='picture_link' rel='lightbox[content_gallery]'></a>");
        link_container.attr('href', image_to_process.attr('src'));
        var image_wrapper = $('<div class="picture_wrapper"></div>');
        image_wrapper.append(image_to_process);

        image_wrapper.append(string_corners.clone());

        link_container.append(image_wrapper);
        gallery_builder.append(link_container);
    });

    target_images.remove();

    gallery_builder.wrap('<div class="content_js_gallery"></div>');

    if (gallery_carousel.length) {
        gallery_carousel.addClass("owl-carousel");
        gallery_carousel.owlCarousel({
            loop: true,
            nav: false,
            navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
            dots: true,
            items: 1,
            margin: 15,
            smartSpeed: 600,
            fluidSpeed: 600,
            navSpeed: 600,
            dotsSpeed: 600,
            dragEndSpeed: 600,
            autoplay: true
        });
    }
}