<div class="wrapper_content banner_custom_carousel">
    {% for banner in banner_list %}
        <div class="container12">
            <div class="content_wrapper">
                {% if banner.banner_carousel_x1_section %}
                <div class="content_title">
                    <h3 class="title">{{ banner.banner_carousel_x1_section.subtitle|safe }}</h3>
                </div>
                <p class="desc">
                    {{ banner.banner_carousel_x1_section.content|safe }}
                </p>
                {% endif %}
                {% if banner.banner_carousel_x1_url %}
                    <a href="{{ banner.banner_carousel_x1_url }}" class="btn btn_custom dark">{% if banner.banner_carousel_x1_linktext %}{{ banner.banner_carousel_x1_linktext|safe }}{% else %}{{ T_ver_mas }}{% endif %}</a>
                {% endif %}
            </div>
            <div class="carousel_wrapper">
                <div class="owl-carousel {% if banner.banner_carousel_x1_pictures|length > 1 %} custom_nav carousel_{{ banner.banner_carousel_x1_pictures|length }}_items{% endif %}">
                    {% for x in banner.banner_carousel_x1_pictures %}
                        <a href="{{ x.servingUrl|safe }}" class="picture_link"  rel="lightbox[rooms_gallery]">
                            <div class="picture_wrapper">
                                <img src="{{ x.servingUrl|safe }}=s1900" alt="">
                            </div>
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<script type="text/javascript">

    $(window).load(function () {

        {% if is_mobile %}

            $(".banner_custom_carousel .owl-carousel").owlCarousel({
                loop: true,
                nav: false,
                navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
                dots: true,
                items: 1,
                margin: 15,
                autoplay: true,
                animateOut: 'fadeOut',
                touchDrag: false,
                mouseDrag: false,
            });

        {% else %}

            $(".banner_custom_carousel .owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
                dots: true,
                items: 1,
                margin: 15,
                autoplay: true,
                animateOut: 'fadeOut',
                touchDrag: false,
                mouseDrag: false,
            });

        {% endif %}
    });

</script>

