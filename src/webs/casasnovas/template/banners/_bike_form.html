<div class="contact_form_wrapper_bike" id="contact_wrapper">
    <div class="container12">
        {% if T_form_bike %}<h3>{{ T_form_bike }}</h3>{% endif %}
        <form name="contact" id="contact" method="post" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>
            {% if destination_address %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ destination_address }}">
            {% endif %}
            <div class="info">
                <span class="form_title_group">{{ T_informacion_contacto }}</span>
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>
                <div class="contInput">
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}" required/>
                </div>
                <div class="contInput">
                    <input type="text" id="dni" name="dni" class="bordeInput" value="" placeholder="{{ T_DNI }}" required/>
                </div>
                <div class="contInput">
                    <input type="number" id="telephone" name="telephone" class="bordeInput" value="" placeholder="{{ T_telefono }}" required/>
                </div>
                <div class="contInput">
                    <input type="email" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}" required/>
                </div>
                <div class="contInput">
                    <input type="text" id="address" name="address" class="bordeInput" value="" placeholder="{{ T_direccion }}" required/>
                </div>

                <div class="clearfix"></div>

                <span class="form_title_group">{{ T_alquiler_bicicleta }}</span>

                <div class="contInput">
                    <input type="text" id="check_date" name="check_date" class="bordeInput" value="" placeholder="{{ T_fecha_entrada }}" required/>
                </div>
                <div class="contInput">
                    <input type="text" id="end_date" name="end_date" class="bordeInput" value="" placeholder="{{ T_fecha_salida }}" required/>
                </div>
                <div class="contInput">
                            <select id="modelo_bicileta" name="modelo_bicileta">
                                <option value="" disabled selected>{{ T_modelo_bicileta|safe }}</option>
                                <option value="MTB">{{ T_MTB |safe }}</option>
                                <option value="carretera">{{ T_carretera |safe }}</option>
                                <option value="gravel">{{ T_gravel |safe }}</option>
                                <option value="triatlon">{{ T_triatlon |safe }}</option>
                                <option value="trekking">{{ T_trekking |safe }}</option>
                                <option value="urbana">{{ T_urbana |safe }}</option>
                                <option value="otros">{{ T_other }}</option>
                            </select>
                </div>
                <div class="clearfix"></div>
                <div class="contInput">
                            <select id="numero_bicicletas" name="numero_bicicletas">
                                <option value="" disabled selected>{{ T_numero_bicicletas|safe }}</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="3+">{{ T_numero_3 |safe }}</option>
                            </select>
                </div>

                <div class="contInput">
                    <input type="text" id="T_altura" name="T_altura" class="bordeInput" value="" placeholder="{{ T_altura|safe }}" required/>
                </div>

                <div class="clearfix"></div>

                <span class="form_title_group">{{ T_servicios }}</span>

                <div class="contInput">
                            <select id="nutricion_deportiva" name="nutricion_deportiva">
                                <option value="" disabled selected>{{ T_nutricion_deportiva|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="fisioterapia" name="fisioterapia">
                                <option value="" disabled selected>{{ T_fisioterapia|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="guia" name="guia">
                                <option value="" disabled selected>{{ T_guia|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="estudio_biomecanico" name="estudio_biomecanico">
                                <option value="" disabled selected>{{ T_estudio_biomecanico|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="rutas_personalizadas_en_gps" name="rutas_personalizadas_en_gps">
                                <option value="" disabled selected>{{ T_rutas_personalizadas_en_gps|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="dispositivo_gps" name="dispositivo_gps">
                                <option value="" disabled selected>{{ T_dispositivo_gps|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="recibir_info" name="recibir_info">
                                <option value="" disabled selected>{{ T_recibir_info|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>


                <div class="clearfix"></div>

                <span class="form_title_group">{{ T_extras }}</span>

                <div class="contInput">
                            <select id="pedales" name="pedales">
                                <option value="" disabled selected>{{ T_pedales|safe }}</option>
                                <option value="mis_pedales">{{ T_mis_pedales }}</option>
                                <option value="look keo">{{ T_look_keo |safe }}</option>
                                <option value="look_delta">{{ T_look_delta |safe }}</option>
                                <option value="spd">{{ T_spd |safe }}</option>
                                <option value="spd_sl">{{ T_spd_sl |safe }}</option>
                                <option value="speedplay">{{ T_speedplay |safe }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="casco" name="casco">
                                <option value="" disabled selected>{{ T_casco|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="zapatos" name="zapatos">
                               <option value="" disabled selected>{{ T_zapatos|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="contInput">
                            <select id="seguro_accidentes" name="seguro_accidentes">
                               <option value="" disabled selected>{{ T_seguro_accidentes|safe }}</option>
                                <option value="si">{{ T_si }}</option>
                                <option value="no">{{ T_no }}</option>
                            </select>
                </div>
                <div class="section">
                    <div class="contInput area">
                        <textarea type="text" id="comments" name="comments" class="bordeInput" value="" placeholder="{{ T_comentarios |safe }}"></textarea>
                    </div>
                </div>


                <div class="clearfix"></div>


                {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src='https://www.google.com/recaptcha/api.js'></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}
                <div class="contInput policy-terms">
                    <div class="privacy_field_label">{{ T_campos_obligatorios }}</div>
                    {% if is_mobile %}
                        <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                        <span class="title">
                            <a data-fancybox
                               data-options='{"caption" : "{{ T_politica_de_privacidad }}", "src" : "/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                               data-width="1200"
                               href="/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}">{{ T_lopd }}</a>
                        </span>
                    {% else %}
                        <input type="checkbox" id="accept-term" name="accept_term" required/>
                        <a class="myFancyPopup fancybox.iframe"
                           href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                {% endif %}
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
            </div>
        </form>
    </div>
</div>

{% if is_mobile %}
    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
{% endif %}
<script type="text/javascript">
    $(window).load(function () {

        $('#check_date, #end_date').datepicker();


        $("#contact-button").click(function () {
            var form = $(this).closest("#contact");

            if (form.valid()) {
                var form_data = form.serialize();
                if ($("#g-recaptcha-response").val()) {
                $.post(
                    "/utils/?action=contact", form_data,
                    function (data) {
                        alert($.i18n._("gracias_contacto"));
                        form.trigger("reset");
                    }
                );
                } else {
                      $(".g-recaptcha > div").css('border', '1px solid red');
                    }
                }
        });

        {% if not is_mobile and not user_isIpad %}
            function contact_form_fx() {
                $(".contact_form_wrapper .container12").addnimation({parent:$(".contact_form_wrapper"),class:"fadeInUp", reiteration: false});
            }
            contact_form_fx();
            $(window).scroll(contact_form_fx);
        {% endif %}
    });
</script>