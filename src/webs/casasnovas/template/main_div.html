<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container left_menu">
    {% for section in menu_left %}
        <div class="main-section-div-wrapper"
             {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                {% if section.title %}
                    <a {% if section.replace_link %} href="{{ section.replace_link|safe }}" target="_blank" {% elif not section.disabled %}itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}>
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a {% if subsection.replace_link %}href="{{ subsection.replace_link|safe }}" target="_blank"{% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"{% endif %}
                                   {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>
<a href="{{host|safe}}/" id="logoDiv">
        <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
    </a>
<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container right_menu {%if not home %}not_home{% endif %}">
    {% for section in menu_right %}
        <div class="main-section-div-wrapper {% if section.sectionType == 'Ofertas' %}offers{% endif %}"
             {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                {% if section.title %}
                    <a {% if section.replace_link %} href="{{ section.replace_link|safe }}" target="_blank" {% elif not section.disabled %}itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}>
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                                   {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>
<div class="floating_header_links_wrapper">
    <ul>
    {% for link in floating_header_links %}
        <li><a href="{{ link.linkUrl|safe }}" {% if 'http' in link.linkUrl %}target="_black"{% endif %}>{{ link.title|safe }}</a></li>
    {% endfor %}
    </ul>

</div>