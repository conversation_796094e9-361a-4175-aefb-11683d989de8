<div class="section_offers_wrapper">
    <div class="banner_offers_filter">
        <div class="filter_label"><i class="fa fa-align-left"></i><span>{{ T_filtar_categoria }}</span></div>
        <a href="#" class="filter" data-filter="oferta"><i class="fa icon-percent"></i>{{ T_ofertas }}</a>
        <a href="#" class="filter" data-filter="escapada"><i class="fa icon-gifts"></i>{{ T_paquetes }}</a>
    </div>

    <div class="offers_wrapper">
        {% for x in offers %}
            <div class="offer_element {% if x.priority and 'E' in x.priority %}escapada{% else %}oferta{% endif %}">
                <div class="offer_image"><img src="{{ x.picture|safe }}"/></div>
                <div class="offer_content">
                    <div class="offer_center">
                        <div class="offer_title">{{ x.name|safe }}</div>
                        {% if x.description %}
                            <div class="offer_description">{{ x.description|safe }}</div>
                        {% endif %}
                        {% if x.link %}
                            <a class="offer_link" href="{{ x.link|safe }}">{{ T_ver_mas }} <i class="fa fa-angle-right"></i></a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>$(window).load(function () {
    $(".banner_offers_filter .filter").click(function (e) {
       e.preventDefault();
       var filter = $(this).attr("data-filter");
       if($(this).hasClass("active")) {
           $(this).removeClass("active");
           $(".offers_wrapper .offer_element").slideDown()
       } else {
           $(".banner_offers_filter .filter").removeClass("active");
           $(this).addClass("active");
           $(".offers_wrapper .offer_element").slideUp().promise().done(function () {
               $(".offers_wrapper .offer_element." + filter).slideDown();
           });
       }
    });
});</script>