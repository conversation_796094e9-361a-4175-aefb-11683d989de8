<div id="fancy_contact" style="display: none">
    <form name="fancy_contact_form" id="fancy_contact_form" method="post" action="/utils/?action=contact">
        <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>
        {% if destination_address %}
            <input type="hidden" name="destination_email" id="destination_email"
                   value="{{ destination_address }}">
        {% endif %}

        <div class="input_element">
            <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
            <input type="text" id="name" name="name" class="bordeInput" value=""/>
        </div>

        <div class="input_element">
            <label for="email" class="title">{{ T_email }}</label>
            <input type="text" id="email" name="email" class="bordeInput" value=""/>
        </div>

        <div class="input_element">
            <label for="telephone" class="title">{{ T_telefono }}</label>
            <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
        </div>

        <div class="input_element">
            <label for="comments" class="title">{{ T_comentarios }}</label>
            <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                      value=""></textarea>
        </div>

        <div class="input_element">
            <input class="check_privacy" id="privacy" name="privacy" type="checkbox"
                   value="privacy"/>
                        <span class="title"><a href="/{{ language }}/?sectionContent=politica-de-privacidad.html"
                                               class="myFancyPopup fancybox.iframe">{{ T_lopd }}</a></span>
        </div>

        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>

        <div id="contact-button-wrapper">
            <a>
                <div id="popup_form_button">
                    {{ T_enviar }}
                </div>
            </a>
        </div>

        <div class="thanks_popup_wrapper">{{ T_gracias_contacto }}</div>
    </form>
</div>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js" async defer="defers"></script>
<script type="text/javascript">
    $(window).load(function () {
        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");

        $("#fancy_contact_form").validate({
                                              rules: {
                                                  name: "required",
                                                  privacy: "required",
                                                  email: {
                                                      required: true,
                                                      email: true
                                                  },
                                                  telephone: {
                                                      required: true,
                                                      phone: true
                                                  },
                                                  comments: "required"
                                              },
                                              messages: {
                                                  name: "{{ T_campo_obligatorio}}",
                                                  privacy: "{{ T_campo_obligatorio }}",
                                                  email: {
                                                      required: "{{ T_campo_obligatorio|safe }}",
                                                      email: "{{ T_campo_valor_invalido|safe }}"
                                                  },
                                                  telephone: {
                                                      required: "{{ T_campo_obligatorio|safe }}",
                                                      phone: "{{ T_campo_valor_invalido|safe }}"
                                                  },
                                                  comments: "{{ T_campo_obligatorio|safe }}"
                                              }

                                          });

        $("#popup_form_button").click(function () {


            if ($("#fancy_contact_form").valid()) {
                if (!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
                    $.post(
                            "/utils/?action=contact",
                            {
                                'name': $(".input_element #name").val(),
                                'telephone': $(".input_element #telephone").val(),
                                'email': $(".input_element #email").val(),
                                'comments': $(".input_element #comments").val(),
                                'section': $("#section-name").val(),
                                'g-recaptcha-response': $("#g-recaptcha-response").val()
                            },

                            function (data) {
                                $(".input_element #name").val("");
                                $(".input_element #telephone").val("");
                                $(".input_element #email").val("");
                                $(".input_element #comments").val("");
                                $(".thanks_popup_wrapper").fadeIn("slow");
                                setTimeout(function () {
                                    $.fancybox.close();
                                }, 5000);
                            }
                    );
                }
            }
        });
    })

    {% if is_mobile %}
        $(".offer-promotion").fancybox({
            width: 650,
            height: 'auto',
            fitToView: false,
            autoSize: false,
            wrapCSS: "fancy_form_popup"
        });
    {% endif %}
</script>