<div class="news_wrapper">
    {% for news_element in news %}
        <div class="news_element">
            <div class="date_element">{{ news_element.creationDate|safe }}</div>
            {% if news_element.pictures_amount %}<div class="pictures_amount"><a href="javascript:showGallery2([ {% for picture in news_element.pictures %} {href : '{{ picture.servingUrl }}=s1900', title : '{{picture.title|safe}}'}, {% endfor %} ]);" class=""><img class="news_pictures_image" src="/img/{{ base_web }}/pictures_image.png"><span class="amount_text">+{{ news_element.pictures_amount }}</span></a></div>{% endif %}
            <div class="wrapper_text_image_new">
                {% if news_element.picture %}
                <div class="news_image_wrapper">
                    <a href="javascript:showGallery2([ {% for picture in news_element.pictures %} {href : '{{ picture.servingUrl }}=s1900', title : '{{picture.title|safe}}'}, {% endfor %} ]);">
                        <img class="news_image" src="{{ news_element.picture|safe }}">
                    </a>
                </div>
                {% endif %}
                <div class="text_wrapper_news">
                    <div class="news_element_title">{{ news_element.name|safe }}</div>
                    <div class="news_element_description">{{ news_element.description|safe }}</div>
                </div>
            </div>

            <a href="/{{ language }}/{{ T_noticias }}/{{ news_element.friendlyUrl|safe }}">
                <div class="news_more_info">{{ T_ver_mas }}</div>
            </a>

            <div id="shareSocialArea">
                <script type="text/javascript"> var addthis_config = {ui_language: "es"} </script>
                <div class="addthis_toolbox addthis_default_style" addthis:title="{{ news_element.name|safe }}" addthis:description="{{ news_element.share_description|safe }}">
                    <a href="http://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                       class="addthis_button_compact" style="color:#5A5655"><span class="share_text">{{ T_compartir }}</span></a>
                    <span class="addthis_separator">|</span>
                    <a class="addthis_button_facebook"></a>
                    <a class="addthis_button_google"></a>
                    <a class='addthis_button_google_plusone' g:plusone:size='medium' g:plusone:count='false'/>
                    <a class="addthis_button_twitter"></a>
                    <a class="addthis_button_linkedin"></a>
                    <a class="addthis_button_favorites"></a>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>