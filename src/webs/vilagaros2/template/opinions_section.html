<section id="top_content">
    <div class="top_content_wrapper">
        <div class="opinions-total">
            <div class="header">
                <div class="yellow_left">
                    <div class="stars">
                        <i class="fa fa-star" aria-hidden="true"></i>
                        <i class="fa fa-star" aria-hidden="true"></i>
                        <br>
                        <i class="fa fa-star" aria-hidden="true"></i>
                        <i class="fa fa-star" aria-hidden="true"></i>
                        <i class="fa fa-star" aria-hidden="true"></i>
                    </div>
                    <span class="media_opinion">{{opinions_inner.media|safe}}</span>
                    <span class="number_opinions">{{opinions_inner.cantidad|safe}} {{ T_opiniones }}</span>
                </div>
                <h3 class="title-module">{{opinions_inner.subtitle|safe}}</h3>
            </div>


            <table>
                {% for opinion in opinions_inner.opiniones %}
                <tr>
                    <td class="name">{{opinion.grade|safe}}<br/>



                    </td>
                    <td class="opinion-description">{{ opinion.description|safe }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>
    </div>

    <div id="opinions-form" class="form-general form-opinion">
        <h3>{{ T_enviar_opinion }}</h3>

        <form action="/utils/?action=contact" id="opinions-form-inner">
            <ul>
                <li>
                    <label>{{T_nombre_y_apellidos}} (*)</label>
                    <input type="text" id="name" name="name" class="bordeInput" value=""/>
                </li>

                <li>
                    <label>{{T_email}} (*)</label>
                    <input type="text" id="email" name="email" class="bordeInput" value=""/>
                </li>

                <li class="comments">
                    <label>{{ T_opiniones }} (*)</label>
                    <textarea name="opinion" id="opinion" cols="30" rows="10"></textarea>
                </li>
            </ul>
            <a id="opinions-button" class="btn-corporate">{{T_enviar|safe}}</a>
        </form>
    </div>

    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>

    <script>
        $(document).ready(function () {
            if (typeof $.i18n == 'object') {
                if (typeof messages == 'object') {
                    $.i18n.load(messages);
                }
            }
            $("#opinions-form-inner").validate({
                rules: {
                    name: "required",
                    email: "required",
                    opinion: "required"
                }
                , messages: {
                    name: $.i18n._("campo_obligatorio"),
                    email: $.i18n._("campo_obligatorio"),
                    opinion: $.i18n._("campo_obligatorio")
                }
                , highlight: function (element) {
                    $(element).addClass('input-error');
                }, unhighlight: function (element) {
                    $(element).removeClass('input-error');
                }
            });
            $("#opinions-button").click(function () {
                if ($("#opinions-form-inner").valid()) {
                    $.post(
                        "/utils/?action=contact",
                        {
                            'name': $("#name").val(),
                            'email': $("#email").val(),
                            'comments': $("#opinion").val(),
                            'emailSubject': 'Opiniones'
                        },
                        function (data) {
                            alert($.i18n._("gracias_contacto"));
                            $("#name").val("");
                            $("#opinion").val("");
                            $("#email").val("");
                        }
                    );
                }
            });
        });
    </script>
</section>
