$(window).load(function () {
  let flex_desc = $('.flex_block_wrapper').find('.flex_description');

  flex_desc.each(function (){
    let read_more = $(this).parent().find('.read_more');
    let read_less = $(this).parent().find('.read_less');

    read_more.click(function (){
      $(this).hide();
      read_less.show();
      $(this).parents('.content_wrapper').find('.flex_description').addClass('open');
    });

    read_less.click(function (){
      $(this).hide();
      read_more.show();
      $(this).parents('.content_wrapper').find('.flex_description').removeClass('open');
    });
  });



  /*
  flex_desc.each(function (){
    let read_more = $(this).parent().find('.read_more');
    let read_less = $(this).parent().find('.read_less');

    if ($(this).height() > 200) {
      $(this).addClass('max_height');
      read_more.show();
      read_less.hide();

    } else {
      read_more.hide();
      read_less.show();
    }

    read_more.click(function (){
      $(this).parent().find('.flex_description').toggleClass('max_height');
    })
  })*/

  function lazy_load() {
    $("img").each(function () {
      if ($(this).attr("data-src")) {
        $(this).attr("src", $(this).attr("data-src")).on("load", function () {
          fa_spinner = $(this).siblings(".spinner_loading");
          if (fa_spinner.length > 0) {
            fa_spinner.fadeOut();
          }
        });
      }
    })
  }

  lazy_load();
})



$(window).scroll(function() {

    var scroll = $(window).scrollTop();

    if (scroll >= 500) {
        $("header").addClass("fixed");
    } else {
    	$("header").removeClass("fixed");
    }
});