<div class="individual_news_wrapper">
        <div class="news_section_title">{{ T_noticias }}</div>
        <div class="individual_news_picture">
            <img class="individual_image" src="{{ individual_news.picture }}">
        </div>
        <div class="block_right">
            <div class="date_element">{{ individual_news.creationDate|safe }}</div>
            {% if individual_news.pictures_amount %}
                <div class="pictures_amount">
                    <a href="javascript:showGallery2([ {% for picture in individual_news.pictures %} {href : '{{ picture.servingUrl }}=s1900', title : '{{ picture.title|safe }}'}, {% endfor %} ]);" class="">
                        <img class="news_pictures_image" src="/img/{{ base_web }}/pictures_image.png">
                        <span class="amount_text">+{{ individual_news.pictures_amount|safe }}</span>
                    </a>
                </div>
            {% endif %}

            <div class="individual_news_text_wrapper">
                <div class="individual_news_title">{{ individual_news.name|safe }}</div>
                <div class="individual_news_description">{{ individual_news.description|safe }}</div>

                <div id="shareSocialArea">
                    <script type="text/javascript"> var addthis_config = {ui_language: "es"} </script>
                    <div class="addthis_toolbox addthis_default_style" addthis:title="{{ news_element.name|safe }}"
                         addthis:description='{{ news_element.description|safe }}'>
                        <a href="http://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                           class="addthis_button_compact" style="color:#5A5655"><span
                                class="share_text">{{ T_compartir }}</span></a>
                        <span class="addthis_separator">|</span>
                        <a class="addthis_button_facebook"></a>
                        <a class="addthis_button_google"></a>
                        <a class='addthis_button_google_plusone' g:plusone:count='false'/>
                        <a class="addthis_button_twitter"></a>
                        <a class="addthis_button_linkedin"></a>
                        <a class="addthis_button_favorites"></a>
                    </div>
                </div>
            </div>
        </div>
    </div>


<script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>