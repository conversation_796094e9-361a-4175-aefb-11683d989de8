header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 40px 40px 40px;
  box-sizing: border-box;
  font-family: $title_family;
  @include transition(all, 1s);

  &.active {
    position: fixed;
    background: rgba(white, 0.95);
    bottom: 0;
    height: 100vh;
    z-index: 1002;
    .menu_toggle {
      color: $corporate_1;

      span {
        background: $corporate_1;
      }
    }

    #logoDiv {
      img {
        opacity: 0;

        &.black_logo {
          opacity: 1;
        }
      }
    }

    #lang, #phone {
      color: $corporate_1;

      a {
        color: $corporate_1;
      }
    }

    nav#main_menu {
      height: auto;
      overflow: visible;
      opacity: 1;
    }
  }

  .menu_toggle {
    display: inline-block;
    vertical-align: top;
    width: 35px;
    margin: 10px 40px 0 0;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: 0.1px;
    color: white;
    cursor: pointer;
    text-align: center;

    &.active {
      span {
        &:nth-child(1) {
          width: 100%;
          -webkit-transform: translate(0, 10px) rotate(45deg);
          -moz-transform: translate(0, 10px) rotate(45deg);
          -ms-transform: translate(0, 10px) rotate(45deg);
          -o-transform: translate(0, 10px) rotate(45deg);
          transform: translate(0, 10px) rotate(45deg);
        }
        &:nth-child(2) {
          width: 0;
          margin-left: 15px;
          opacity: 0;
        }
        &:nth-child(3) {
          width: 100%;
          -webkit-transform: translate(0, -10px) rotate(-45deg);
          -moz-transform: translate(0, -10px) rotate(-45deg);
          -ms-transform: translate(0, -10px) rotate(-45deg);
          -o-transform: translate(0, -10px) rotate(-45deg);
          transform: translate(0, -10px) rotate(-45deg);
        }
      }
    }
    &:hover {
      span {
        width: 75%;

        &:nth-child(3) {
          width: 100%;
        }
      }
    }
    span {
      display: block;
      background: white;
      height: 2px;
      width: 100%;
      margin: 0;
      @include transition(all, .6s);
      &:nth-child(2) {
        width: 90%;
        margin: 8px 0;
      }
      &:nth-child(3) {
        width: 70%;
        margin-bottom: 5px;
      }
    }
  }

  #logoDiv {
    position: relative;
    display: inline-block;
    width: 62px;
    margin-top: -15px;

    img {
      width: 100%;
      height: auto;
      object-fit: contain;
      @include transition(opacity, 1s);

      &.black_logo {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }
  }

  #lang, #phone {
    float: right;
    color: white;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.8px;

    a {
      color: white;
    }
  }

  #lang {
    margin-left: 50px;

    a {
      display: inline-block;
      padding: 0 5px;

      &.selected {
        font-weight: 700;
      }
    }
  }

  #phone {
    a {
      display:inline-block;
      padding-left: 50px;
    }

    i.fa-phone {
      &:before {
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        -o-transform: rotate(90deg);
        transform: rotate(90deg);
      }
    }

    i {
      margin-right: 10px;

      &:before {
        display: inline-block;
      }
    }
  }

  nav#main_menu {
    @include center_xy;
    width: 900px;
    margin-top: 50px;
    height: 0;
    overflow: hidden;
    opacity: 0;

    .main-section-div-wrapper {
      position: relative;
      z-index: 2;

      a {
        display: inline-block;
        padding: 7px 0;
        font-family: $title_family;
        font-weight: 500;
        letter-spacing: 0.3px;
        color: $corporate_1;
        font-size: 30px;
      }
    }
  }
}