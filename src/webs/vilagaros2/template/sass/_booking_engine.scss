.datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
  background-color: $corporate_2;
}

#full_wrapper_booking.boking_widget_vertical {

  /*======== Booking Widget =======*/
  div#wrapper_booking {
    position: absolute;
    height: auto;
    top: 45px;
    left: 0px;
    right: 0px;
    z-index: 35;

    #booking {
      float: left;
    }
  }
  .booking_widget {
    position: absolute;
    left: 0px;
  }

  #full-booking-engine-html-5 {
    width: 299px;
  }

  .booking_form_title {
    padding: 10px 0;
    text-align: center;
    text-transform: uppercase;
    background: $corporate_1;

    .booking_title_2, .best_price {
      display: block;
      color: white;
      font-size: 22px;
    }
  }

  .rooms_number_wrapper {
    display: none;
  }

  .room_list_wrapper {
    width: 100%;

    .adults_selector {
      width: 50%!important;
    }

    .selectricWrapper {
      width: 100%!important;
    }
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  .adults_selector .selectricItems {
    top: 100% !important
  }

  .booking_form_title .best_price {
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    width: 49.8%;
  }

  button.submit_button {
    background: $corporate_2 !important;
    color: white !important;

    &:hover {
      opacity: .8;
    }
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: $corporate_1 !important;
    margin-top: 0;
    opacity: 1;
  }

  #full-booking-engine-html-5 {
    margin-top: 20px !important;
    border: 11px solid rgba(0, 0, 0, 0.35);
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;
    background: white;
  }
  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }
}

#inline_wrapper_booking {
  /*======== Booking Widget =======*/
  div#wrapper_booking {
    position: absolute;
    height: 70px;
    top: 330px;
    left: 0px;
    right: 0px;
    z-index: 35;
    width: 1121px;
  }

  .promocode_header {
    display: none;
  }

  .booking_widget {
    position: absolute;
    left: 0px;
  }

  #full-booking-engine-html-5 {
    width: 1121px;
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    width: 49.8%;
  }

  button.submit_button {
    background-color: #FCD430 !important;
    color: $corporate-1 !important;
  }
  .submit_button:hover {

  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: gray !important;
  }

  #full-booking-engine-html-5 {
    //margin-top: 20px!important;
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date {
    margin-top: 6px;
  }
  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper {
      display: inline-block;
      vertical-align: top;
      width: 192px;
      float: left;
      border-top: 1px solid lightgrey;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 95px;
  }

  .room_list_wrapper {
    width: 190px;
    display: inline-block;
    vertical-align: top;
    float: left;
    border-bottom: 1px solid lightgrey;
    border-top: 1px solid lightgrey;
    height: 68px;
  }

  .wrapper_booking_button {
    display: inline-block;
    width: 548px;
    float: left;
    border-bottom: 1px solid lightgrey;
    border-left: 1px solid lightgrey;
    height: 69px;

    .promocode_wrapper {
      display: inline-block;
      width: 240px;
      vertical-align: top;
      float: left;
      height: 70px;
    }

    .submit_button {
      width: 263px;
      display: inline-block;
      vertical-align: top;
      float: left;
      height: 70px;
      border: 1px solid lightgrey;
    }
  }
}

#full_wrapper_booking.boking_widget_inline {
  .selectric {
    .label {
      color: black;
      font-weight: 700;
      font-size: 24px;
    }
    .button {
      background: none !important;
      text-shadow: none !important;

      &:before {
        content: "\f0d7";
        font-family: "Font Awesome 5 Pro";
        color: $corporate_2;
        font-size: 18px;
      }
    }
  }

  div#wrapper_booking {
    position: absolute;
    height: auto;
    bottom: 30px;
    left: 0px;
    right: 0px;
    z-index: 1000;
    width: 1140px;
    padding: 10px;
    background: $corporate_1;
  }
  .booking_widget {
    position: absolute;
    left: 0px;
  }

  #full-booking-engine-html-5 {
    width: 1025px;
  }

  .departure_date_wrapper {
    border-right: 0;
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  #full-booking-engine-html-5 .booking_form_title {
    background: rgba(0, 0, 0, 0.4) !important;
    display: inline-block;
    color: white;
    padding: 11px 30px;
    font-size: 18px;
    display: none;
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    width: 49.8%;
  }

  button.submit_button {
    background: $corporate_1 !important;
    color: white !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    display: none !important;
    width: 100%;
    background: rgba(0,0,0,0.4) !important;
    text-align: center;
    box-sizing: border-box;
    margin-top: 0;
    opacity: 1;
    text-transform: uppercase;
    color: white;
    padding: 11px 30px;

    & > div {
      display: inline-block;
      vertical-align: middle;
    }

    .web_support_label_2 {
      &:before {
        display: none;
      }
    }
  }

  #full-booking-engine-html-5 {
    //margin-top: 20px!important;
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date {
    margin-top: 6px;
    background: none;
    font-weight: 700;
    color: black;
  }

  .date_box.departure_date {
    background: none;
    margin-top: 6px;
  }

  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      width: 150px;
      float: left;
      border: none;
      padding: 8px 10px;
      &:before {
        content: '';
        position: absolute;
        left: 5px;
        right: 20px;
        height: 1px;
        bottom: 14px;
        background-color: black;
      }
      .date_day {
        color: black;
        font-weight: 700;
      }
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
      padding: 8px 10px;
    }
  }

  .rooms_number_wrapper {
    position: relative;
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 95px;
    &:before {
      content: '';
      position: absolute;
      left: 5px;
      right: 5px;
      height: 1px;
      bottom: 14px;
      background-color: black;
    }
    .selectric .button {
      width: 25px;
    }
  }

  .room_list_wrapper {
    width: 220px;
    display: inline-block;
    position: relative;
    vertical-align: top;
    float: left;

    .adults_selector, .children_selector {
      position: relative;
      border: none;
      &:before {
        content: '';
        position: absolute;
        left: 5px;
        right: 12px;
        height: 1px;
        bottom: 14px;
        background-color: black;
      }
    }

    .room_list {
      background: white;
      .range-age {
        font-size: 8px;
      }
      .room {
        height: 69px;

        &.room2 {
          border-bottom: 1px solid lightgrey;
        }
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: right;
    border-bottom: 1px solid lightgrey;
    border-left: 1px solid lightgrey;
    height: 69px;

    .promocode_wrapper {
      display: inline-block;
      width: 240px;
      vertical-align: top;
      float: left;
      height: 70px;
    }

    .submit_button {
      width: 220px;
      display: inline-block;
      vertical-align: top;
      margin: 10px 15px 0 0;
      float: left;
      height: 50px;
      background-color: $corporate_1;

      &:hover {
        opacity: .8;
      }
    }
  }
}

.promocode_wrapper.promocode_hidden {
  display: none;
}

.selectricItems {
  overflow: auto !important;
}

/*=== Floating Widget ===*/
#full_wrapper_booking.floating_booking {
  top: 0;
  z-index: 900;
  width: 100%;
  position: fixed !important;
  bottom: inherit !important;
  background: rgba(black, .6);
  -webkit-box-shadow: 1px 1px 1px gray;
  -moz-box-shadow: 1px 1px 1px gray;
  box-shadow: 1px 1px 1px gray;
  bottom: 0;

  &.notfloating {
    display: none !important;
  }

  #engine_label {
    width: 1070px;
  }

  #wrapper_booking {
    padding: 30px 0;
    bottom: 0px !important;
    background: transparent;
  }

  #full_wrapper_booking {
    background: transparent;
  }

  .destination_wrapper input {
    background: white;
  }

  #full_wrapper_booking .date_box {
    background-color: white;
  }

  #full_wrapper_booking .selectric {
    background: white;
  }

  #full_wrapper_booking .boking_widget_inline .room_list_wrapper {
    background: white;
  }

  div#wrapper_booking {
    position: relative;
    height: auto;
    bottom: auto !important;
    top: auto;
    left: auto;
    right: auto;
    z-index: 1000;
    width: 1120px;
    padding: 10px;
    background: transparent;
    box-sizing: border-box;
  }
  .booking_widget {
    position: absolute;
    left: 0px;
  }

  #full-booking-engine-html-5 {
    width: 1013px;
    margin-top: 0 !important;
    border: 0;
  }

  .departure_date_wrapper {
    border-right: 0;
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  #full-booking-engine-html-5 .booking_form_title {
    background: $corporate-2;
    display: inline-block;
    color: white;
    padding: 16px 30px;
    font-size: 18px;
    display: none;
  }

  #full-booking-engine-html-5 .booking_form_title.wrapper-new-web-support {
    display: inline-block;
    width: 100%;
    background: transparent !important;
    text-align: center;
    box-sizing: border-box;
    margin-top: 0;
    opacity: 1;
    text-transform: uppercase;
    padding-top: 7px;

    & > div {
      display: inline-block;
      vertical-align: middle;
    }

    .web_support_label_2 {
      &:before {
        display: none;
      }
    }
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector {
    width: 49.8%;
  }

  button.submit_button {
    background: $corporate_1 !important;
    color: white !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: gray !important;
  }

  #full-booking-engine-html-5 {
    //margin-top: 20px!important;
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date {
    margin-top: 6px;
    background: none;
  }

  .date_box.departure_date {
    background: none;
  }

  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 160px;
      float: left;
      border-top: 1px solid lightgrey;
      padding: 8px 10px;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
      padding: 8px 10px;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 95px;
    border: none;
  }

  .room_list_wrapper {
    width: 220px;
    display: inline-block;
    position: relative;
    vertical-align: top;
    float: left;
    border: none;

    .room_list {
      background: white;
      .range-age {
        font-size: 8px;
      }
      .room {
        height: 69px;

        &.room2 {
          border-bottom: 1px solid lightgrey;
        }
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: right;
    border: none;
    height: 69px;

    .promocode_wrapper {
      display: inline-block;
      width: 210px;
      vertical-align: top;
      float: left;
      height: 70px;
    }

    .submit_button {
      width: 210px;
      display: inline-block;
      vertical-align: top;
      border: none;
      margin: 10px 15px 0 0;
      float: left;
      height: 50px;
      background-color: $corporate_1;

      &:hover {
        opacity: .8;
      }
    }
  }
}

#full_wrapper_booking.boking_widget_inline.boking_widget_inline_inicio div#wrapper_booking {
  bottom: 15px;
  height: auto;
}

body .ui-datepicker {
    border-bottom: 57px solid transparent;
    background-clip: padding-box;
  }

#engine_label {
  background: rgba($corporate_2, .8);
  width: 1110px;
  z-index: 1000;
  padding: 10px 15px;
  top: 0;
  display: block;
  margin: 0 !important;
  position: relative;
  h2 {
    font-weight: 700;
    color: white;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 11px;
    text-align: center;
  }
}

.ticks_widget_wrapper {
  background: rgba($corporate_2, .8);
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  z-index: 1000;
  padding: 10px 15px;
  position: relative;
  .tick {
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
    margin-right: 15px;
    color: white;
    text-align: center;
    &.ticks_widget_title {
      padding-right: 30px;
      font-weight: 700;
      width: 20%;
      text-align: left;
    }
    i {
      display: inline-block;
      vertical-align: middle;
      font-size: 22px;
    }
    .description {
      display: inline-block;
      vertical-align: middle;
      font-size: 12px;
      font-weight: 300;
    }
  }
}


// ------------- booking engine version 7 ------------------//

#full-booking-engine-html-7 {
  form.booking_form.paraty-booking-form {
    background: white;
  }
  .guest_selector{
    display: none;
  }
  .dates_selector_personalized {
    display: none;
  }
  .date_year {
    display: none;
  }
  .date_day {
    border-bottom: none !important;
  }
}
html[lang=en] {
  #full_wrapper_booking.boking_widget_inline .room_list_wrapper {
    width: 250px !important;
  }
  #full_wrapper_booking.floating_booking .room_list_wrapper .adults_selector {
    width: 39.8% !important;
  }
  #full_wrapper_booking.floating_booking .room_list_wrapper .children_selector {
    width: 60% !important;
  }
}
