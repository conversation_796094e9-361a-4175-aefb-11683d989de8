.rooms_wrapper {
  margin: 0 0 120px;

  .container12 {
    .banner {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;

      .room {
        display: flex;
        flex-direction: column;

        .picture_wrapper {
          height: 280px;

          img {
            @include cover_image;
          }
        }

        .content_wrapper {
          flex: 1;
          position: relative;
          padding: 30px 30px 110px;
          border: 1px solid #cccccc;

          .content_title {
            margin-bottom: 20px;

            .title {
              @include title_styles_2;
              text-transform: capitalize;
            }
          }

          .desc {
            @include text_styles;
            font-weight: 400;
            color: #6D6D6D
          }

          .links_wrapper {
            position: absolute;
            bottom: 30px;
            left: 30px;
            right: 30px;

            .link_more {
              @include btn_styles;
              text-transform: none;
              font-size: 16px;
              background-color: $corporate_2;
              letter-spacing: 1px;
            }
          }
        }
      }
    }
  }
}

