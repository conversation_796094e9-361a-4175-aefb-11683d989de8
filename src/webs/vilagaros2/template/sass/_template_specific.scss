/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
.ui-state-default {
  border: 1px solid white !important;
}


.ui-datepicker-title {
  color: white !important;
}


.ui-widget-header {
  background: $corporate_2 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_2 !important;
  color: white;
}


body {
  font-family: 'Raleway', sans-serif;
  font-weight: 400;

  a {
    text-decoration: none;
  }

  strong {
    font-weight: 700;
  }

  .spinner_loading {
    @include center_xy;
    z-index: 1;

    .fa.fa-spinner {
      color: #cacaca;
    }
  }

  /* specific class to enlarge containers12 per design proposal */
  &.big_containers {
    #wrapper_content.container12 {
      width: 100%;

      .content_subtitle_wrapper {
        width: auto;
        padding: 0 calc((100% - 1140px) / 2);
      }
    }
  }

  .container14 {
    margin: 0 auto;
    padding: 0;

    @media (min-width: 1421px) {
      width: 1360px;
    }

    @media (min-width: 1161px) and (max-width: 1420px) {
      width: 1140px;
    }

    @media (max-width: 1160px) {
      width: 1120px;
    }
  }
}

.top_text_wrapper {
  width: 700px;
  margin: 85px auto 0;
  text-align: center;
  font-size: 16px;
  line-height: 30px;
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate_2;
  left: 0;
  bottom: 0;
  padding: 20px 0;
  z-index: 1000;

}

.bottom_popup #wrapper2 .icon {
  position: relative;
  float: left;
  font-size: 50px;
  color: white;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  display: inline-block;
  vertical-align: middle;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.bottom_popup .close_button {
  position: absolute;
  top: 5px;
  right: 5px;

  .fa {
    background-color: rgba(white, .8);
    padding: 3px 6px 5px 5px;
    color: $corporate_2;
    border-radius: 50%;

    &:hover {
      background-color: white;
    }
  }
}

button.bottom_popup_button {
  width: 120px;
  background: $corporate_1;
  border: 0;
  height: 36px;
  color: white;
  display: inline-block;
  vertical-align: middle;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;

  .email, .discount, .compra {
    text-align: center;
  }

  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }

  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  form.form_popup {
    text-align: center;
    padding-top: 50px;

    li {
      text-align: center;
    }

    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }

    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }

  .spinner_wrapper_faldon {
    padding-top: 20px;
  }

  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}

/*===== Top blocks =====*/
.top_blocks_wrapper {
  display: table;
  width: 100%;
  min-width: 1140px;

  .block_element {
    width: calc(100% / 3);
    height: 350px;
    float: left;
    position: relative;
    overflow: hidden;

    .black_overlay {
      background: rgba(0, 0, 0, 0.25);
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 2;
      opacity: 0;
      -webkit-transition: all 0.3s;
      -moz-transition: all 0.3s;
      -ms-transition: all 0.3s;
      -o-transition: all 0.3s;
      transition: all 0.3s;
    }

    &:hover .black_overlay {
      opacity: 1;
    }

    &:before {
      content: "";
      display: block;
      padding-top: 100%; /* initial ratio of 1:1*/
    }

    img.block_image {
      @include center_xy;
    }

    h2.block_title {
      width: 200px;
      margin: auto;
      left: 0px;
      right: 0px;
      padding: 20px 50px;
      @include center_y;
      text-align: center;
      font-weight: bolder;
      color: white;
      font-size: 25px;
      line-height: 20px;
      z-index: 3;
      -webkit-transition-duration: 2s; /* For Safari 3.1 to 6.0 */
      transition-duration: 2s;
    }

    &:hover h2.block_title {
      border: 1px solid $white;
    }

    .blockplus {
      font-weight: 100;
      font-size: 70px;
      display: none;
    }

    &:hover .blockplus {
      display: block;
    }
  }
}

.inner_section .block_element {
  height: 150px;
}

/*=== Banner x3 ===*/
.banners_x3_wrapper {
  display: inline-block;
  width: 100%;
  padding-bottom: 120px;


  .banner_element {
    display: inline-block;
    width: 100%;
    height: 400px;
    float: left;
    position: relative;
    overflow: hidden;

    &:hover {
      .banner_overlay {
        opacity: .4;
      }

      .banner_title {
        border: 1px solid white;

        &:after {
          content: "\f067";
          font-family: 'Font Awesome 5 Pro';
          font-size: 36px;
          font-weight: lighter;
          display: block;
          margin-top: 10px;
        }
      }
    }

    .banner_overlay {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(black, .3);
      opacity: 1;
      @include transition(opacity, .7s);
      z-index: 1;
    }

    .banner_image {
      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .banner_content {
      @include center_xy;
      z-index: 2;
      text-align: center;
      display: table;

      .banner_title {
        color: white;
        text-transform: uppercase;
        font-size: 54px;
        box-sizing: border-box;
        padding: 20px;
        @include transition(all, .7s);
      }
    }
  }

  &.owl-carousel {
    .owl-nav {
      display: flex;
      width: 60px;
      justify-content: space-between;
      left: calc((100% - 60px) / 2);
      position: absolute;
      margin-top: 20px;

      .fal {
        color: $corporate_1;
        font-size: 34px;
        font-weight: bold;

        &:hover {
          color: $corporate_2;
        }
      }
    }
  }
}

/*=== Footer ===*/
footer {
  .wrapper_footer_columns {
    padding: 40px 0;

    .footer_column {
      display: inline-block;
      vertical-align: middle;
      border-right: 2px solid lightgray;
      margin-right: 20px;
      padding-right: 20px;

      &:last-child {
        margin-right: 0;
        padding-right: 0;
        border-right: 0;
      }

      #newsletter {
        display: inline-block;
        position: relative;
        vertical-align: middle;
      }

      .weather {
        display: inline-block;
        vertical-align: middle;

        & * {
          display: inline-block;
          vertical-align: middle;
        }

        .icon_weather {
          &:after {
            content: "";
            height: 26px;
            width: 2px;
            display: inline-block;
            vertical-align: middle;
            background: #d3d3d3;
            margin: 0 10px;
          }

          img {
            height: 60px;
          }
        }

        .degrees_weather {
          font-weight: bold;
          font-size: 22px;
        }

        .location_weather {
          color: #848484;
          margin-left: 10px;
          text-transform: uppercase;
        }
      }

      &:first-child {
        width: 50%;
      }
    }
  }

  h3 {
    color: $gray-1;
    margin-bottom: 30px;
  }

  .newsletter_tit, .siguenos_tit {
    display: block;
    float: left;
    color: $gray-1;
    font-size: 0.9em;
  }

  .newsletter_tit {
    padding: 15px 15px 15px 0;
    display: block;
    font-size: 18px;
    color: $corporate_1;
    vertical-align: middle;
    float: none;
  }

  .siguenos_tit {
    padding: 30px 10px;
  }

  #newsletter {

    #title_newsletter, #suscEmailLabel {
      display: none !important;
    }

    .last {
      margin: 20px 0px;
    }

    #form-newsletter {

      input[type=text], button {
        display: inline-block;
        float: left;
      }

      input[type=text] {
        border: 2px solid $corporate_2;
        font-size: 0.9em;
        padding: 5px 10px;
        width: 200px;
        height: 42px;
      }

      button {
        border: 2px solid $corporate_2;
        background-color: $corporate_2;
        padding: 15px 10px;
        color: $white;
        font-size: 0px;
        height: 56px;
        cursor: pointer;
        width: 50px;
        font-family: 'Font Awesome 5 Pro';
        right: 0;

        &:before {
          font-family: 'Font Awesome 5 Pro';
          content: "\f054";
          font-size: 16px;
        }
      }

      .newsletter_checkbox {
        clear: both;
        padding: 2px 0;
        font-size: 12px;
        margin-top: 4px;
        text-align: initial;

        a {
          text-decoration: underline !important;
          color: $corporate_1;
        }
      }

      input#privacy, input#promotions {
        float: left;
      }

      input#promotions {
        margin-bottom: 15px;
      }
    }
  }

  .full-copyright {
    background: #434343;
    border-top: 1px solid #999;
    border-bottom: 1px solid #222;
    color: $white;
    padding: 30px 0px;
    text-align: center;
    font-size: 0.7em;

    a {
      color: $white;
      text-decoration: none;
    }

    .rrss_footer {
      padding-top: 20px;

      .rrss_footer_title {
        font-size: 18px;
        color: $corporate_2;

      }

      #social_footer {
        a {
          display: inline-block;
          vertical-align: middle;

          i {
            font-size: 26px;
            color: $corporate_2;
            padding: 10px 10px 0;
            @include transition(al, .6s);
          }

          &:hover {
            i {
              color: white;
            }
          }
        }
      }
    }
  }

  .network-wrapper {
    margin-top: 20px;

    #facebook_like, #google_plus_one {
      display: inline-block;
      vertical-align: middle;
    }
  }

  #social {
    a {
      display: inline-block;
      color: white;
      width: 60px;
      height: 60px;
      position: relative;
      border-radius: 50%;
      background: $corporate_2;

      .fa {
        @include center_xy;
        color: white;
        font-size: 24px;
      }
    }
  }
}

/*=== Content Subtitle ===*/
.content_subtitle_wrapper {
  display: inline-block;
  width: 100%;
  margin: 85px 0;

  .content_title {
    font-family: 'Montserrat', sans-serif;
    color: $corporate_1;
    font-size: $title_size;
    font-weight: 700;
    text-align: center;

    span, strong {
      display: block;
      font-size: 150%;
      font-weight: 300;
      color: #CCC;
    }
  }

  .content_subtitle_description {
    padding: 0 50px;
    margin: 20px auto 0;
    text-align: center;
    line-height: 30px;
    color: #999;
    font-size: $description_size;

    & strong {
      color: #333;
    }
  }
}

/*=== Automatic Content ===*/
.automatic_content_wrapper {
  padding: 60px 0;

  .section-title {
    color: $corporate_1;
    font-size: $title_size;
    text-align: center;
  }

  & > div {
    width: 700px;
    margin: 20px auto 0;
    text-align: center;
    font-size: $description_size;
  }
}

#my-bookings-form-fields {
  display: table;
  margin: auto;
  margin-top: 20px;

  label#my-bookings-email-label, label#my-bookings-localizador-label {
    font-weight: lighter;
    margin-right: 15px;
  }

  #emailInput {
    padding: 10px;
    margin-right: 25px;
  }

  input#localizadorInput {
    padding: 10px;
  }

  & > ul {
    margin-top: 20px;
    text-align: center;

    & > li {
      display: inline-block;

    }
  }

  .modify-reservation {
    background: $corporate_1;
  }

  .searchForReservation {
    background: $corporate_2;
  }

  .modify-reservation, .searchForReservation {
    color: white;
    font-weight: 300;
    font-size: 15px;
    text-transform: uppercase;
    padding: 10px 20px;
    border: 0;
  }
}

#reservation {
  .modify_reservation_widget {
    margin: 0 auto 20px;
    width: 326px;
  }
}

#cancel-button-container {
  display: table;
  margin: auto;
  margin-top: 20px;

  button {
    color: white;
    font-weight: 300;
    font-size: 15px;
    text-transform: uppercase;
    padding: 10px 20px;
    border: 0;
    background: $corporate_2;
    display: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.my-bookings-booking-info {
  margin: auto !important;
  display: table;
}

.fResumenReserva {
  border: 1px solid $corporate_1;

  .txtCosteTotal {
    color: $corporate_1;
  }
}

/*=== Flex Block ===*/
.flex_block_wrapper {
  display: inline-block;
  width: 100%;
  height: 525px;
  overflow: hidden;
  margin-bottom: 10px;
  position: relative;

  .flex_block_content {
    .flex_element {
      display: none;
      height: 525px;
      width: 100%;

      &:nth-child(1) {
        display: inline-block;
      }

      .flex_content {
        width: 49.5%;
        height: 100%;
        float: left;
        background: $corporate_3;
        position: relative;

        .center_block {
          @include center_y;
          padding: 0 55px;

          .flex_title {
            font-size: $title_size;
            color: $corporate_1;
            margin-bottom: 30px;

            span {
              display: block;
              color: #ccc;
            }
          }

          .flex_description {
            font-size: $description_size;
            line-height: 30px;
          }

          .flex_link {
            @include btn_primary;
            display: inline-block;
            box-sizing: border-box;
            padding: 10px 25px;
            margin-top: 30px;
            @include transition(all, .2s);
          }
        }
      }

      .flex_image {
        width: 50.5%;
        height: 100%;
        float: right;
        position: relative;
        overflow: hidden;

        &:before {
          content: "";
          display: inline-block;
          width: 10px;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          background: rgba(white, .6);
          z-index: 1;
        }

        &:hover {
          .overlay_image {
            opacity: 1;
          }
        }

        .overlay_image {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(black, .6);
          z-index: 1;
          opacity: 0;
          @include transition(opacity, .6s);

          &:before {
            font-family: 'Font Awesome 5 Pro';
            content: "\f067";
            @include center_xy;
            color: white;
            font-size: 44px;
            z-index: 1;
            border: 1px solid white;
            border-radius: 50%;
            padding: 40px 43px;
          }
        }

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }
    }

    .flex-direction-nav {
      position: absolute;
      left: 460px;
      bottom: 20px;

      li {
        display: inline-block;
        margin: 0 10px;

        .fa {
          color: $corporate_1;
          font-size: 34px;

          &:hover {
            color: $corporate_2;
          }
        }
      }
    }
  }
}

/*=== Banners x2 ===*/
.bannersx2_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 85px;

  .bannerx2_text {
    padding: 50px 0;

    h3 {
      font-family: 'Montserrat', sans-serif;
      color: $corporate_1;
      font-size: $title_size;
      font-weight: 700;
      text-align: center;
    }

    p {
      padding-top: 30px;
    }
  }

  .bannerx2_element {
    display: inline-block;
    width: 49.5%;
    float: left;
    background: $corporate_3;

    &:nth-child(even) {
      float: right;
    }

    .banner_link {
      .banner_image {
        &:before {
          font-family: 'Font Awesome 5 Pro';
          content: "\f067";
          @include center_xy;
          color: white;
          font-size: 24px;
          z-index: 1;
          border: 1px solid white;
          border-radius: 50%;
          padding: 20px 23px;
          opacity: 0;
          @include transition(opacity, .6s);
        }

        &:after {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          left: 0;
          background: rgba(black, .6);
          opacity: 0;
          @include transition(opacity, .6s);
        }
      }

      &:hover {
        .banner_image {
          &:before, &:after {
            opacity: 1;
          }
        }
      }
    }

    .banner_image {
      position: relative;
      height: 290px;
      overflow: hidden;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .banner_content {
      padding: 20px;
      box-sizing: border-box;
      color: $corporate_1;

      .banner_title {
        font-size: 24px;
        margin-bottom: 20px;

        span {
          color: #ccc;
          display: block;
        }
      }

      .banner_description {
        font-size: $description_size;
      }
    }
  }
}

/*===== Maps Footer ====*/
.maps_footer {
  height: 430px;
  overflow: hidden;
  display: block;
  width: 100%;
  position: relative;

  .iframe_wrapper {
    width: 80%;
    display: inline-block;
    text-align: left;
    float: right;

    iframe {
      width: 120%;
      height: 430px;
    }
  }

  .picture_iframe {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 50%;
    display: inline-block;
    float: left;
    height: 430px;
    background: $corporate_1;
    -webkit-transform: skew(-25deg);
    -moz-transform: skew(-25deg);
    -ms-transform: skew(-25deg);
    -o-transform: skew(-25deg);
    transform: skew(-25deg);

    &:after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 10%;
      right: -10%;
      background: rgba(245, 245, 245, 0.56);
      -webkit-transform: skew(0deg);
      -moz-transform: skew(0deg);
      -ms-transform: skew(0deg);
      -o-transform: skew(0deg);
      transform: skew(0deg);
    }
  }

  .text_iframe {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 41%;
    height: 430px;
    padding: 50px 0px;
    box-sizing: border-box;
    border-left: 100px solid $corporate_1;

    .location_title {
      color: $white;
      font-size: 39px;
      text-transform: uppercase;
      margin-bottom: 40px;
    }

    .location_description {
      color: $white;
    }

    .mapForm {
      position: absolute;
      bottom: 30px;
      right: 0px;
      padding: 20px 0;
      width: 100%;
    }

    input {
      border: 1px solid $white;
      background-color: $corporate_1;
      height: 72px;
      margin-right: 0px;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      padding: 0 30px;
      font-size: 20px;
      color: $white;
      vertical-align: middle;
      width: 295px;
      float: left;

      &::-webkit-input-placeholder {
        color: $white;
        font-weight: lighter;
      }

      &::-moz-placeholder {
        color: $white;
        font-weight: lighter;
      }

      &:-ms-input-placeholder {
        color: $white;
        font-weight: lighter;
      }

      &:-moz-placeholder {
        color: $white;
        font-weight: lighter;
      }
    }

    .go {
      float: left;
      height: 72px;
      width: 72px;
      background: $white;
      display: block;
      border: 0;
      overflow: hidden;
      position: relative;
      font-size: 22px;
      cursor: pointer;

      &:after {
        font-family: 'Font Awesome 5 Pro';
        content: "\f054";
        @include center_xy;
        color: $corporate_1;
      }

      &:hover {
        opacity: .8;
      }
    }
  }
}

/*=== Slider Container ===*/
.home {
  #slider_container {
    height: 100vh;

    .forcefullwidth_wrapper_tp_banner {
      height: 100% !important;
      overflow: hidden;
    }
  }
}

#slider_container {
  position: relative;

  .tp-revslider-slidesli {
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      height: calc(100vh - 250px);
      background: linear-gradient(rgba(#222222, 0.7), rgba(#222222, 0));
      z-index: 1;
      pointer-events: none;
    }
  }

  .caption.fade {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    @include transform(translate(-50%, -50%) !important);
    width: 415px;
    text-align: center;
    color: white;
    font-weight: 500;
    z-index: 2;

    .title {
      display: block;
      font-family: $title_family;
      font-size: 50px;
      letter-spacing: 2px;
      line-height: 60px;
    }

    .desc {
      display: block;
      font-family: $text_family;
      font-size: 20px;
      letter-spacing: 1px;
      line-height: 22px;
    }

    a {
      display: inline-block;
      font-family: $title_family;
      font-size: 13px;
      letter-spacing: 1.3px;
      color: white;
      position: relative;
      margin-top: 10px;

      &::before {
        content: "";
        display: block;
        @include center_y;
        right: calc(100% + 5px);
        width: 20px;
        height: 1px;
        background-color: white;
      }

      &::after {
        content: "\f105";
        display: inline-block;
        vertical-align: middle;
        font-family: "Font Awesome 5 Pro";
        font-weight: 300;
        font-size: 20px;
        @include center_y;
        left: calc(100% + 5px);
      }
    }
  }

  #video_slider_volume {
    position: absolute;
    left: 17%;
    top: 120px;
    cursor: pointer;
    z-index: 10;

    i {
      background-color: $corporate_2;
      color: $corporate_1;
      padding: 5px 10px;
      font-size: 18px;
      border-radius: 50%;
    }
  }

  .tp-bullets {
    bottom: 260px !important;
  }

  .slide_inner {
    position: relative;
    height: 330px;
    overflow: hidden;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(rgba(#222222, 0.7), rgba(#222222, 0));
      z-index: 1;
      pointer-events: none;
    }

    img {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }

  .slider_offer_wrapper {
    position: absolute;
    top: 0;
    background: rgba($corporate_2, .8);
    z-index: 20;
    width: 100%;

    .slider_offer_content {
      position: relative;
    }

    .slider_element {
      display: none;
      height: 85px;
      width: 100%;
      position: relative;
      color: white;

      &:first-child {
        display: inline-block;
      }

      .slider_center {
        @include center_xy;
        width: 1040px;

        .slider_title {
          font-size: 22px;
          font-weight: bold;
        }

        .slider_link {
          @include center_y;
          right: 0;
          color: $corporate_2;
          display: inline-block;
          background: white;
          padding: 10px 20px;
          text-transform: uppercase;

          &:hover {
            opacity: .8;
          }
        }
      }
    }

    .fa {
      color: white;
      font-size: 24px;
    }

    .flex-nav-prev {
      @include center_y;
      left: 0;

      .flex-disabled {
        display: none;
      }
    }

    .flex-nav-next {
      @include center_y;
      right: 0;

      .flex-disabled {
        display: none;
      }
    }
  }
}

/*====== Location =====*/
.location_section_wrapper {
  display: block;
  width: 100%;
  margin: 55px 0;

  h3.location_title {
    color: $corporate_1;
    font-size: $title_size;

    &:after {
      content: '';
      display: block;
      width: 65px;
      height: 4px;
      background: $corporate_1;
      margin: 21px 0 27px;
    }
  }

  .location_wrapper_text {
    display: inline-block;
    vertical-align: top;
    margin-right: 50px;
    background: white;
    width: 50%;
    z-index: 2;
    position: relative;
    box-sizing: border-box;
  }

  .location_content {
    margin-top: 10px;
    line-height: 30px;
    font-size: $description_size;
    color: #4e4e4e;

    strong {
      display: block;
      color: $corporate_1;
    }
  }

  .ifrma_map_wrapper {
    display: inline-block;
    vertical-align: middle;
    width: 45%;

    iframe {
      width: 100%;
    }
  }
}

#contactContent .info {
  padding-left: 0 !important;
}

.contact_iframe_background {

  padding: 59px 0;

  h1#title {
    display: none;
  }

  .contact_form_title {
    text-align: center;
    font-size: 16px;
    padding-bottom: 30px;

    strong {
      font-size: 24px;
    }
  }

  div#google-plus, .fb_iframe_widget {
    display: none;
  }

  //.contact_form {
  //  background: white;
  //  width: 100%;
  //  float: left;
  //  padding: 0 41px;
  //  box-sizing: border-box;
  //
  //  label.title {
  //    display: block;
  //    clear: both;
  //    width: 100% !important;
  //    font-weight: 400;
  //    margin-bottom: 15px;
  //    color: #585858;
  //    font-size: 17px;
  //  }
  //
  //  .bordeInput {
  //    margin-left: 0 !important;
  //    width: 100% !important;
  //    box-sizing: border-box;
  //    border: 0 !important;
  //    background: #eeeeee;
  //    height: 40px;
  //    padding-left: 30px;
  //  }
  //
  //  textarea.bordeInput {
  //    padding-top: 20px;
  //  }
  //
  //  div#contact-button {
  //    width: 155px !important;
  //    height: 42px !important;
  //    background: $corporate_1 !important;
  //    text-transform: uppercase;
  //    text-align: center;
  //    box-sizing: border-box;
  //    padding: 11px 0 !important;
  //    border-radius: 0 !important;
  //
  //    &:hover {
  //      opacity: .8;
  //    }
  //  }
  //
  //  div#contact-button-wrapper {
  //    padding-right: 0 !important;
  //  }
  //
  //  input#privacy, input#has_reservation {
  //    display: inline-block;
  //    float: left;
  //    width: auto !important;
  //    vertical-align: middle;
  //    height: auto;
  //    margin-right: 10px;
  //    margin-top: 4px;
  //  }
  //
  //  input#privacy + .title {
  //    margin-top: 0;
  //    width: auto;
  //  }
  //
  //  input#privacy + span a {
  //    font-size: 11px;
  //    margin-bottom: 15px;
  //    color: #585858;
  //    text-decoration: none;
  //  }
  //
  //  span.title {
  //    width: auto !important;
  //
  //    a {
  //      font-size: 11px;
  //      margin-bottom: 15px;
  //      color: #585858;
  //      text-decoration: none;
  //    }
  //  }
  //
  //  .has_reservation_wrapper {
  //    display: block;
  //    margin-top: 7px;
  //
  //    span {
  //      font-size: 11px;
  //      margin-bottom: 15px;
  //      color: #585858;
  //      text-decoration: none;
  //    }
  //  }
  //}
  .contact_form {
    padding: 100px 0;
    overflow: hidden;

    h3 {
      //@include title_styles;
      text-align: center;
      padding-bottom: 50px;
    }

    #contact {
      display: table;
      margin: auto;

      .info {
        display: table;
        position: relative;
      }

      .contInput {
        display: inline-block;
        float: left;
        padding: 10px 0 10px 20px;
        position: relative;
        box-sizing: border-box;

        &:nth-of-type(-n+4) {
          width: calc((100% - 5px) / 2);
          padding-top: 20px;
        }

        &:nth-of-type(5) {
          width: 100%;
        }

        &:nth-of-type(3), &:nth-of-type(5) {
          margin-right: 0;
        }


        input:not([type="checkbox"]), textarea {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background: white;
          padding: 15px 25px;
          font-size: 14px;
          width: 100%;
          margin-bottom: 20px;
          border: 1px solid #cccccc;
          box-sizing: border-box;
          font-family: $text_family !important;

          &:focus {
            outline: 3px solid #cccccc;
          }
        }

        textarea {
          height: 150px;
        }

        input, textarea {
          &.error {
            outline: 1px solid red;
          }
        }

        #accept-term, &#privacity {
          @include checkbox_styles;
        }
      }

      .policy-terms, .has_reservation_wrapper {
        display: inline-block;
        width: auto;
        float: left;
        color: $black;
        font-size: 12px;
        margin: 20px 50px;
      }

      a.myFancyPopup {
        display: inline-block;
        vertical-align: middle;
        font-size: 11px;
        color: #585858;
      }

      input#privacy, input#has_reservation {
        display: inline-block;
        float: left;
        width: auto !important;
        vertical-align: middle;
        height: auto;
        margin-right: 10px;
        margin-top: 4px;
      }

      input#privacy + .title {
        margin-top: 0;
        width: auto;
      }

      input#privacy + span a {
        font-size: 11px;
        margin-bottom: 15px;
        color: #585858;
        text-decoration: none;
      }

      span.title {
        width: auto !important;

        a {
          font-size: 11px;
          margin-bottom: 15px;
          color: #585858;
          text-decoration: none;
        }
      }

      .has_reservation_wrapper {
        display: block;
        margin-top: 7px;

        span {
          font-size: 11px;
          margin-bottom: 15px;
          color: #585858;
          text-decoration: none;
        }
      }

      #contact-button {
        @include btn_styles;
        position: absolute;
        right: 0;
        bottom: 20px;
        border: 1px solid $corporate_1;
        @include transition(all, .2s);

        &:hover {
          color: $corporate_1;
          border: 1px solid $corporate_1;
          background-color: transparent !important;
          font-weight: 500;
        }
      }
    }
  }
}

#contactContent .title {
  font-weight: 300;
  margin-bottom: 10px;
}

.iframe_wrapper {
  width: 50%;
  float: right;
  overflow: hidden;
}

div#contact-button {
  width: 125px;
  height: 47px;
  display: inline-block;
  vertical-align: top;
  float: left;
  color: white;
  background: $corporate_1;
  font-size: 17px;
}

/*=== Offers ===*/
.section_offers_wrapper {
  width: 1140px;
  overflow-x: hidden;
}

.offers_wrapper {
  display: inline-block;
  width: 1160px;

  .offer_element {
    display: inline-block;
    width: 560px;
    height: 380px;
    float: left;
    position: relative;
    margin-bottom: 25px;
    margin-right: 20px;

    &:hover {
      .offer_content {
        &:before {
          opacity: 1;
        }
      }
    }

    .offer_image {
      display: inline-block;
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .offer_content {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      &:before {
        content: "";
        background: rgba(black, .6);
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        @include transition(opacity);
      }

      .offer_center {
        position: absolute;
        top: 54%;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        width: 100%;
        text-align: center;

        .offer_title {
          font-size: $title_size;
          color: #1f2931;
          background-color: rgba(255, 255, 255, 0.76);
          padding: 15px 0;

          small {
            font-size: 24px;
          }
        }

        .offer_link {
          @include btn_primary;
          display: inline-block;
          margin-top: 30px;
          border: 3px solid $corporate_2;
          @include transition(all, .2s);

          &:hover {
            color: white;
            border: 3px solid rgba(221, 116, 24, 0.5);
          }
        }
      }
    }
  }
}

/*=== Events Blocks ===*/
.events_wrapper {
  display: inline-block;
  width: 100%;
  background: $corporate_3;
  padding: 60px 0;

  .event_element {
    display: inline-block;
    width: 100%;
    margin-bottom: 40px;
    background: white;

    &:last-child {
      margin-bottom: 0;
    }

    .event_gallery {
      width: 100%;
      height: 380px;
      overflow: hidden;
      position: relative;

      .image_element {
        height: 390px;
        position: relative;
      }

      a:not(.flex-prev):not(.flex-next) {
        display: inline-block;
        width: 100%;
        height: 100%;
        float: left;
        position: relative;
        overflow: hidden;

        //&:first-child {
        //  width: 50%;
        //  height: 100%;
        //}

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }

      .flex-prev, .flex-next {
        position: absolute;
        top: 50%;
        z-index: 2;
        left: 30px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);

        .fa {
          color: white;
          font-size: 70px;
        }
      }

      .flex-next {
        right: 30px;
        left: auto;
      }
    }

    .event_content {
      box-sizing: border-box;
      padding: 40px;

      .event_title {
        font-size: $title_size;

        &:after {
          content: "";
          height: 3px;
          width: 90px;
          background: $corporate_2;
          display: block;
          margin: 20px 0;
        }
      }

      .event_description {
        font-size: $description_size;
        color: #999;
        line-height: 30px;

        .icons_full_wrapper {
          margin: 20px 0;

          h4 {
            text-align: center;
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 30px;
          }

          .icons_wrapper {
            display: flex;
            flex-flow: row wrap;
            justify-content: center;


            .icon_box {
              text-align: center;
              width: 25%;
              margin-bottom: 20px;
              margin-right: 10px;

              i {
                font-size: 50px;
                color: $corporate_2;
                display: block;
                font-weight: 300;
              }
            }
          }
        }
      }
    }

    .event_links {
      width: 100%;
      text-align: center;
      margin-bottom: 40px;

      a {
        @include btn_primary;
        display: inline-block;
        cursor: pointer;
        @include transition(all, 0.2s);

        &:nth-child(1) {
          @include btn_outline;
          font-weight: 500;

          &:hover {
            background-color: $corporate_1;
            color: white;
          }
        }

        &:hover {
          background-color: $corporate_1;
          border: 1px solid $corporate_1;
          color: white;
        }
      }

      span {
        display: block;
        position: absolute;
        left: 0;
        right: 0;
        font-size: 14px;
        padding-top: 8px;
      }
    }

    .contact_iframe_background {
      padding: 30px 0;

      .contact_form_title {
        font-size: 30px;
      }
    }
  }
}

/*===== Room detailed ====*/
.detailed_room_wrapper {
  margin-top: 30px;
  margin-bottom: 3px;

  .room_detail_image_wrapper {
    height: 675px;
    position: relative;
    overflow: hidden;

    .room_detail_image {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }

  a.see_more_pictures_detailed {
    position: absolute;
    z-index: 1;
    bottom: 25px;
    right: 25px;
    text-transform: uppercase;
    text-decoration: none;
    color: white;

    span {
      display: inline-block;
      vertical-align: middle;
    }

    .fa {
      border: 2px solid white;
      position: relative;
      vertical-align: middle;
      width: 42px;
      height: 42px;
      margin-left: 10px;

      &:before {
        @include center_xy;
        font-size: 21px;
      }
    }
  }

  .room_details_text {
    display: block;
    width: 825px;
    margin-top: -125px;
    z-index: 2;
    position: relative;
    background: white;
    margin-left: 30px;
    padding: 40px;

    a.button-promotion, a.offer-promotion {
      @include btn_primary;
      position: absolute;
      right: 60px;
      top: 39px;
      width: 163px;
      box-sizing: border-box;
      text-align: center;
      font-weight: 500;
      @include transition(all, .2s);
    }

    h1.room_title {
      font-size: $title_size;
      color: $corporate_1;

      &:after {
        content: '';
        display: block;
        width: 65px;
        height: 4px;
        background: $corporate_1;
        margin: 21px 0 27px;
      }
    }

    .room_description {
      margin-top: 8px;
      font-size: $description_size;
      line-height: 30px;
    }

    #shareSocialArea {
      float: right;
    }
  }
}

.fancy_form_popup {
  .fancybox-outer {
    padding: 15px !important;
  }

  #fancy_contact {
    display: inline-block;

    .input_element {
      display: inline-block;
      width: 100%;
      text-align: center;
      margin-bottom: 10px;

      label {
        display: block;
        margin-bottom: 10px;

        &.error {
          color: lighten(red, 20%);
          margin-top: 10px;
          margin-bottom: 0;

          &[for=privacy] {
            display: inline-block;
          }
        }
      }

      span.title a {
        color: #444;
      }

      .bordeInput {
        display: block;
        margin: 0 auto;
        border: 1px solid rgba($corporate_1, .2);
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 300px;
        box-sizing: border-box;
        font-size: 12px;
        padding: 10px;

      }
    }

    #contact-button-wrapper {
      text-align: center;

      #popup_form_button {
        display: inline-block;
        background: $corporate_1;
        color: white;
        text-transform: uppercase;
        padding: 10px 35px;
        font-weight: 100;
        cursor: pointer;
        @include transition(opacity, .6s);

        &:hover {
          opacity: .8;
        }
      }
    }

    .thanks_popup_wrapper {
      text-align: center;
      margin-top: 5px;
      display: none;
    }

    .g-recaptcha {
      & > div {
        margin: 10px auto;
      }
    }
  }
}

/*========= Sections United =======*/
.sections_united_wrapper {
  background: white;
  margin: 22px 0;
  padding: 20px 0;
  width: 100%;
  display: block;
  box-sizing: border-box;

  .sections_united_menu {
    width: 100%;
    box-sizing: border-box;
    margin: 0 30px;

    h3.section_name {
      font-size: 18px;
      text-transform: uppercase;
      margin-bottom: 20px;
      color: $corporate_1;
      text-align: center;
    }
  }

  .sections_united_sections {
    width: 100%;
    box-sizing: border-box;
    padding-left: 30px;
    border-top: 2px solid #CCCCCC;
    padding-top: 20px;

    .united_section_element {
      width: 100%;

      h3.united_title {
        font-size: $title_size;
        color: $corporate_1;
        margin-bottom: 20px;
      }

      .image {
        position: relative;
        width: 100%;
        height: 400px;
        overflow: hidden;

        img {
          @include center_image;
        }
      }

      .united_description {
        margin-top: 25px;
        line-height: 30px;
        font-size: $description_size;
        margin-bottom: 10px;
        color: #999;
      }

      .pedir_presupuesto {
        display: block;
        background-color: $corporate_2;
        border: 1px solid $corporate_1;
        color: white;
        text-align: center;
        padding: 5px 0;
      }
    }

    .owl-nav {
      position: absolute;
      top: 270px;
      left: 0;
      right: 0;

      .owl-prev, .owl-next {
        @include center_y;
        left: -25px;
        right: auto;
        @include transition(all, .6s);

        i {
          @include center_y;
          color: $corporate_1;
          font-size: 32px;
        }

        &:hover {
          i {
            color: $corporate_2;
          }
        }
      }

      .owl-next {
        right: -25px;
        left: auto;
      }
    }
  }
}

#united_form {
  display: none;
  padding: 30px;

  #eventForm {
    display: inline-block;

    ul {
      li {
        display: inline-block;
        width: 49%;
        float: left;
        margin-bottom: 10px;

        &:nth-child(even) {
          float: right;
        }

        &:last-child {
          float: none;
          width: 100%;
        }

        label {
          display: block;
          font-weight: bold;
          font-size: 16px;
          color: $corporate_1;
          margin-bottom: 10px;
        }

        input {
          display: inline-block;
          width: 100%;
          height: 35px;
          border-radius: 5px;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 1px solid #d3d3d3;
          box-sizing: border-box;
          padding-left: 10px;
        }

        textarea {
          display: inline-block;
          width: 100%;
          height: 100px;
          border-radius: 5px;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 1px solid #d3d3d3;
          box-sizing: border-box;
          padding-left: 10px;
        }

        select {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          box-sizing: border-box;
          display: inline-block;
          height: 35px;
          width: 100%;
          padding-left: 10px;
          background: white;
          border: 1px solid #d3d3d3;

          &:after {
            font-family: 'Font Awesome 5 Pro';
            content: "\f107";
          }
        }

        .select_event {
          position: relative;

          &:after {
            font-family: 'Font Awesome 5 Pro';
            content: "\f107";
            @include center_y;
            right: 10px;
          }
        }
      }
    }

    .privacy_wrapper {
      float: left;

      input {
        display: inline-block;
        vertical-align: middle;
      }

      span {
        display: inline-block;
        vertical-align: middle;

        a {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }

    .btn-corporate {
      float: right;
      background: $corporate_2;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;

      &:hover {
        opacity: .8;
      }
    }

    label.error {
      color: red;
    }
  }
}

/*======= Opinions ======*/
.opinions .header, .opinions-total .header {
  background-color: $corporate_2;
  position: relative;
  display: table;
  width: 100%;

  img {
    position: relative;
    display: inline-block;
    float: left;
    margin-right: 15px;
    height: 50px;
  }

  p {
    color: rgb(90, 90, 90);
    font-size: 14px;
    line-height: 14px;
    margin-top: 2px;
  }
}

.opinions-total {
  margin: 0px 0 60px 0;

  h3.title-module {
    display: inline-block;
    color: white;
    margin-left: 90px;
    text-align: left;
    font-size: 42px;
    margin-top: 25px;
  }
}

#opinions-form {
  background: $gray-4;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  margin-bottom: 40px;

  h3 {
    color: $corporate_1;
    text-align: center;
  }

  li {
    width: 50%;
    display: inline-block;
    float: left;

    label {
      margin-bottom: 10px;

      &.error {
        color: red;
      }
    }

    input {
      width: 98%;
      box-sizing: border-box;
      border: 0;
      height: 30px;
    }

    &.comments {
      width: 100%;

      textarea {
        width: 99%;
        box-sizing: border-box;
        border: 0;
      }
    }
  }
}

.opinions {
  width: 379px !important;
  margin-left: 0px;
  margin-right: 0px;
}

.opinions .value, .opinions-total .value {
  background: #f6f7f8;
  text-align: center;
  padding: 14px 0;
  border-top: 2px solid white;
  font-size: 16px;
  text-transform: uppercase;
  color: $corporate-1;

  .media {
    font-size: 30px;
    color: $corporate-1;
    margin-right: 10px;
  }
}

.opinions .coment, .opinions-total .coment {
  background: #f6f7f8;

  padding: 15px 0;
  border-top: 1px solid white;
  position: relative;

  .plus-link {
    position: absolute;
    right: 15px;
    top: 12px;
    background-color: $corporate-1;
    padding: 8px 8px 0 !important;

    &:hover {
      background-color: pink;
    }
  }

  span {
    font-size: 12px;

    p {
      display: inline;
    }
  }

  .calification {
    color: white;
    background-color: $gray-2;
    padding: 10px 10px 9px;
    margin-right: 20px;
    margin-left: 15px;
    font-size: 14px;
  }
}

.opinions-total table {
  width: 100%;

  tr {
    background-color: #f6f7f8;
    border-top: 2px solid white;

    .name {
      text-transform: uppercase;
      width: 95px;
      border-right: 2px solid white;
      color: white;
      background: $corporate_1;
      font-weight: bolder;
      box-sizing: border-box;
      text-align: center;
      font-size: 30px;
      margin: auto;
      height: 20px;
      padding: 0 !important;
      vertical-align: middle;
    }

    .opinion-description {
      width: 900px;
      padding: 20px;
      color: #86858a;
      font-weight: lighter;

      strong {
        font-size: 14px;
      }

      .opinion_title {
        .name {
          display: inline-block;
          font-size: 16px;
          background: transparent;
          font-weight: normal;
          color: #86858a;
          width: auto;
          height: auto;
          text-transform: none;
          border-right: 0;
          vertical-align: top;
          font-weight: 300;
        }

        .date {
          display: inline-block;
        }

        .location {
          display: inline-block;
        }
      }
    }

    .calification {
      border-left: 2px solid white;
      vertical-align: middle;
      padding: 20px;
      text-align: center;

      span {
        color: white;
        background-color: #787878;
        padding: 10px 10px 9px;
        font-size: 14px;
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.opinions-button {
  position: absolute;
  right: 30px;
  top: 30px;
  display: block;
  color: white;
  text-transform: uppercase;
  padding: 2px;
  border-bottom: 1px solid white;
  margin-top: 8px;
  text-decoration: none;

}

.yellow_left {
  display: table;
  float: left;
  background: $corporate_1;
  padding: 25px 25px 23px;

  &:after {
    content: '';
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-left: 15px solid $corporate_1;
    border-bottom: 15px solid transparent;
    position: absolute;
    top: 34px;
    bottom: 0px;
    margin-left: 25px;
    z-index: 4;
  }

  .stars {
    display: inline-block;
    text-align: center;
    padding-right: 15px;
    vertical-align: middle;

    .fa {
      color: white;
    }
  }
}

span.media_opinion {
  color: white;
  border-right: 2px solid white;
  border-left: 2px solid white;
  padding-left: 15px;
  padding-right: 15px;
  font-size: 45px;
  padding-top: 5px;
  vertical-align: middle;
}

span.number_opinions {
  margin-left: 22px;
  color: white;
  display: inline-block;
  position: relative;
  text-transform: uppercase;
  vertical-align: middle;
}

/*===== Opinions form =====*/
#opinions-form {
  h3 {
    font-size: 30px;
  }

  #opinions-button {
    height: 30px;
    text-align: center;
    box-sizing: border-box;
    background: $corporate_2;
    padding: 5px 20px;
    color: white;
    text-transform: uppercase;
    cursor: pointer;
  }

  label {
    display: block;
    text-transform: capitalize;
    margin-top: 11px;
    margin-bottom: 3px;
  }

  input, textarea {
    padding: 5px 15px;
    width: 280px;
  }

  textarea {
    margin-bottom: 20px;
    border: 1px solid lightgray;
  }
}

/*==== News section ====*/
.news_wrapper {
  display: table;
  padding-top: 10px;
  margin-bottom: 40px;
  margin-top: -35px;

  .news_element {
    display: block;
    width: 50%;
    float: left;
    box-sizing: border-box;
    margin-bottom: 30px;

    &:before {
      content: '';
      width: 100%;
      display: block;
      height: 25px;
      border-top: 1px solid #c7c7c7;
      border-right: 1px solid white;
    }

    &:nth-of-type(odd) {
      border-right: 1px solid #C7C7C7;

      #shareSocialArea {
        margin-right: 30px;
      }
    }

    &:nth-of-type(even) {
      padding-right: 0;

      .date_element, .news_element_description, .news_image_wrapper, .news_more_info {
        margin-left: 30px;
      }
    }

    .date_element {
      background: #7c7b79;
      color: white;
      display: table;
      padding: 5px 8px;
      margin-bottom: 17px;
      float: left;
    }

    .pictures_amount {
      float: left;
      height: 29px;
      background: $corporate_2;

      span.amount_text {
        vertical-align: top;
        color: white;
        margin-top: 4px;
        display: inline-block;
        padding-right: 7px;
        padding-left: 6px;
        border-left: 1px solid white;
      }

      img.news_pictures_image {
        margin-left: 4px;
        padding-right: 3px;
      }
    }

    .news_image_wrapper {
      width: 120px;
      overflow: hidden;
      height: 120px;
      float: left;
      margin-right: 20px;
      margin-bottom: 20px;
    }

    .text_wrapper_news {
      .news_element_title {
        color: $corporate_1;
        font-size: 13px;
        font-weight: 500;
        line-height: 23px;
        padding-right: 40px;
      }

      .news_element_description {
        font-size: 13px;
        line-height: 25px;
        display: block;
        color: #3e3e3e;
        padding-right: 40px;
        height: 125px;
        overflow: hidden;

        hotel {
          color: $corporate_2;
          font-size: 13px;
          font-weight: 500;
          line-height: 23px;
          padding-right: 40px;
          display: block;
        }
      }
    }

    .news_more_info {
      width: 120px;
      height: 30px;
      color: white;
      background: $corporate_2;
      font-size: 16px;
      text-transform: uppercase;
      text-align: center;
      box-sizing: border-box;
      padding: 6px 0;
      display: inline-block;
      cursor: pointer;
      margin-top: 20px;

      &:hover {
        opacity: .8;
      }
    }

    div#shareSocialArea {
      float: right;
      margin-top: 25px;
      font-size: 15px;
      color: $corporate_2;

      .share_text {
        color: $corporate_2;
      }

      a {
        text-decoration: none;
      }
    }

    .wrapper_text_image_new {
      display: block;
      width: 100%;
      clear: both;
    }
  }
}

/*===== Individual news ====*/
.individual_news_wrapper {
  display: table;
  width: 100%;
  margin-top: 70px;
  margin-bottom: 66px;

  a {
    text-decoration: none;
  }

  .date_element {
    background: #7c7b79;
    color: white;
    display: table;
    padding: 5px 8px;
    margin-bottom: 17px;
    float: left;
  }

  .pictures_amount {
    float: left;
    height: 29px;
    background: $corporate_2;

    span.amount_text {
      vertical-align: top;
      color: white;
      margin-top: 4px;
      display: inline-block;
      padding-right: 7px;
      padding-left: 6px;
      border-left: 1px solid white;
    }

    img.news_pictures_image {
      margin-left: 4px;
      padding-right: 3px;
    }
  }

  .individual_news_picture {
    float: left;
    width: 400px;

    img.individual_image {
      width: 100%;
    }
  }

  .block_right {
    float: right;
    width: 700px;

    .individual_news_title {
      display: block;
      clear: both;
      color: $corporate_1;
      font-size: 13px;
      font-weight: 700;
      line-height: 23px;
    }

    .individual_news_description {
      font-size: 13px;
      line-height: 25px;
      display: block;
      color: #3e3e3e;

      hotel {
        display: block;
        clear: both;
        color: $corporate_2;
        font-size: 13px;
        font-weight: 700;
        line-height: 23px;
      }
    }
  }

  .news_section_title {
    text-align: center;
    font-weight: bolder;
    color: $corporate_1;
    margin-bottom: 27px;
    text-transform: uppercase;
    border-bottom: 1px solid #c7c7c7;
    padding-bottom: 30px;
  }

  div#shareSocialArea {
    margin-top: 30px;
    font-size: 13px;
  }
}

.additional_banner_wrapper {
  background: $corporate_3;

  .addtional_banner_content {
    padding: 40px 0;

    .additional_title {
      font-size: $title_size;
      margin-bottom: 30px;
    }

    .additional_description {
      font-size: $description_size;
      line-height: 30px;
    }
  }
}

/*=== Cycle Banners ===*/
.cycle_banner_wrapper {
  width: 100%;
  display: inline-block;
  margin-bottom: 60px;

  .cycle_element {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    margin-bottom: 20px;
    background: #eaeaea;

    &:last-child {
      margin-bottom: 0;
    }

    &:nth-child(even) {
      flex-direction: row-reverse;
    }

    &:nth-child(odd) {
      flex-direction: row;
    }

    .cycle_image {
      width: 30%;
      height: 400px;

      img {
        @include cover_image;
      }
    }

    .cycle_content {
      width: 70%;
      position: relative;
      box-sizing: border-box;
      padding: 40px;

      .cycle_title {
        font-size: $title_size;
        margin-bottom: 20px;
      }

      .cycle_description {
        font-size: $description_size;
        line-height: 25px;

        .icons_full_wrapper {
          margin: 20px 0;

          h4 {
            font-weight: 700;
            margin-bottom: 20px;
          }

          .icons_wrapper {
            display: flex;
            flex-flow: row wrap;
            justify-content: center;


            .icon_box {
              text-align: center;
              width: 25%;
              margin-bottom: 20px;
              margin-right: 10px;

              i {
                font-size: 50px;
                color: $corporate_2;
                display: block;
                font-weight: 300;
              }
            }
          }
        }
      }

      .more_information {
        text-align: center;
        margin-top: 15px;
        display: inline-block;
        margin-right: 10px;

        a {
          @include btn_primary;
          display: inline-block;
          margin-bottom: 16px;
          width: 230px;
          @include transition(all, 0.2s);

          .fa-file-pdf:before {
            color: $corporate_2;
            margin-right: 5px;
          }

          span {
            color: black;
            font-weight: bold;
            border-bottom: 2px solid black;
            font-family: $text_family;
            white-space: nowrap;
          }

        }

        .btn_hover {
          @include btn_outline;

          &:hover {
            background-color: $corporate_2;
            color: white;
          }
        }
      }
    }
  }
}

/*=== Iconos otros servicios ===*/
.banner_icons_wrapper {
  .icons_full_wrapper {
    margin: 20px 0;

    h4 {
      text-align: center;
      font-weight: 700;
      margin-bottom: 20px;
      font-size: 30px;
    }

    .icons_wrapper {
      display: flex;
      flex-flow: row wrap;
      justify-content: center;


      .icon_box {
        text-align: center;
        width: 25%;
        margin-bottom: 20px;
        margin-right: 10px;

        i {
          font-size: 50px;
          color: $corporate_2;
          display: block;
          font-weight: 300;
        }
      }
    }
  }
}

.newsletter_additional_form {
  margin-top: 30px;

  #send_newsletter_additional {
    display: inline-block;
    padding: 10px 25px;
    background-color: $corporate_2;
    color: white;
  }
}

.big-img .image_filters_wrapper .filter_element.active {
  color: $corporate_2;
}

.modify_reservation_widget #contenedor_habitaciones {
  display: none !important;
}

.info {
  #privacy {

  }

  .title {
    a {
      text-decoration: underline !important;
    }
  }

  #promotions {
    height: auto;
    float: left;
    display: inline-block;
    margin-right: 10px;
    margin-top: 3px;
  }

  label[for=promotions], label[for=has_reservation] {
    font-size: 11px;
    color: #585858;
    //display: inline-block;
  }

  .checkbox_wrapper {
    display: inline-block;

    .checkbox_container {
      margin-top: 5px;

      .error {
        display: block;
        color: red;
      }
    }
  }
}

/* Banner landing */

.banner_landing_full_wrapper {
  margin-top: 50px;

  .image {
    position: relative;
    display: inline-block;
    width: 600px;
    height: 770px;
    overflow: hidden;

    img {
      @include center_image;
    }

    .banenr_landing_circle {
      position: absolute;
      bottom: 20px;
      left: 20px;
      background-color: rgba($corporate_2, .8);
      border-radius: 50%;
      width: 250px;
      height: 250px;
      text-align: center;
      color: white;
      font-size: 20px;
      z-index: 5;

      .content {
        display: inline-block;
        vertical-align: middle;

        .banenr_landing_circle_title {
          padding-bottom: 10px;

          big {
            display: block;
            font-size: 24px;
          }
        }

        .banenr_landing_circle_text {
          display: inline-block;
          border-top: 1px solid white;
          padding-top: 10px;
        }
      }

      &:before {
        content: '';
        height: 250px;
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  .banner_landing_wrapper {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    vertical-align: top;
    background-color: white;
    margin-left: -100px;
    margin-top: 50px;
    width: 600px;
    padding: 50px;
    box-shadow: 1px 1px 5px 3px rgba(0, 0, 0, 0.2);

    h3 {
      position: relative;
      font-size: 38px;
      font-weight: 300;
      line-height: 42px;
      color: $corporate_2;
      padding-bottom: 15px;

      &:before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100px;
        height: 5px;
        background-color: $corporate_2;
      }
    }

    p {
      padding-top: 15px;

      i {
        padding: 5px;
      }

      .landing_block_subtitle {
        display: block;
        font-size: 20px;
        color: $corporate_2;
        padding: 10px;
      }
    }

    a {
      background: #DD7521;
      display: inline-block;
      color: white;
      text-decoration: none;
      text-transform: uppercase;
      box-sizing: border-box;
      text-align: center;
      margin-top: 20px;
      padding: 22px 30px;
      @include transition(all, .6s);

      &:hover {
        opacity: .8;
      }
    }
  }
}

/* Extra text */

.extra_text_wrapper {
  background-color: #F7F7F7;
  padding: 25px 0;
  margin-top: 50px;

  .extra_text_title {
    font-size: 32px;
    padding-bottom: 20px;
  }
}

.individual_offer_section {
  #slider_container {
    display: none !important;
  }
}

// Default cookies popup override
body {
  .cookies_container {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    bottom: auto;
    @include transform(translate(-50%, -50%));
    width: 790px;
    background: linear-gradient(rgba($corporate_2, 0.95) 0%, rgba($corporate_2, 0.95) 23%, $corporate_3 23%, $corporate_3 100%);
    border-radius: 15px;

    .texto_cookie {
      color: $corporate_1;
      letter-spacing: 1.5px;
      text-align: left;

      b {
        color: white;
        margin-bottom: 20px;
      }

      a {
        color: $corporate_1 !important;
      }
    }

    .buttons_container {
      button {
        outline: none;
      }

      .accept {
        background: rgba($corporate_2, 0.95);
        color: white;
        border-radius: 5px;
        @include transition(all, 0.3s);

        &:hover {
          background: rgba($corporate_2, 0.6);
        }
      }

      .modify {
        color: $corporate_1;
      }
    }
  }

  .cookies_popup {
    top: 50%;
  }

  .cookies_overlay {
    position: fixed;
  }
}

.automatic_floating_picture {
  bottom: 32px !important;
}

@media (max-width: 1500px) {
  .automatic_floating_picture {
    bottom: 195px !important;
    z-index: 999 !important;
  }
}

body.individual_offer_section {
  header {
    background: rgba(black, 0.5);

    &.active {
      background: rgba(white, 0.95);
    }
  }

  #wrapper_content {
    margin-top: 180px;
  }
}