<div class="flex_block_wrapper effects_sass" sass_effect="slide_up_effect">
    <div class="flex_block_content">
        <ul class="slides">
            {% for x in flex_block %}
                <li class="flex_element">
                    <div class="flex_content">
                        <div class="center_block">
                            <div class="flex_title">{{ x.title|safe }}</div>
                            <div class="flex_description">{{ x.description|safe }}</div>
                            {% if x.linkUrl %}
                                <a class="flex_link" href="{{ x.linkUrl|safe }}">{{ T_ver_mas }}</a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex_image">
                        {% if x.linkUrl %}
                            <div class="overlay_image"></div>
                        {% endif %}
                        <div class="spinner_loading">
                            <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                        </div>
                        <img data-src="{{ x.servingUrl|safe }}=s900"/>
                    </div>
                </li>
            {% endfor %}

        </ul>
    </div>
</div>
<script async>
    $(window).load(function () {
        slider_params = {
            controlNav: false,
            prevText: '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
            nextText: '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
            directionNav: true,
            animation: "slide",
            itemMargin: 0
        };
        $(".flex_block_content").flexslider(slider_params);
    });
</script>