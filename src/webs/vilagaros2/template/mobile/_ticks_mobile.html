{% if ticks_mobile %}
<div class="ticks_mobile">
   {% if ticks_widget_title %}
       <span class="ticks_widget_title tick">{{ ticks_widget_title|safe }}</span>
   {% endif %}
    {% for tick in ticks_mobile %}
        {% if tick.title and not 'title' in tick.title %}
            <div class="tick_wrapper">
                <i class="{% if "fa" in tick.title %}fa {% endif %}{{ tick.title|safe }}"></i>
                {% if tick.description %}
                    <div class="tick_description">
                        {{ tick.description|safe }}
                    </div>
                {% endif %}
            </div>
        {% endif %}
    {% endfor %}
</div>

<script>
    $('.hidden_booking_widget #form').append($('.ticks_mobile').detach()).addClass('with_ticks');
</script>
{% endif %}