<div class="cycle_wrapper_events">
    {% for x in events %}
        <div class="cycle_element">
            <div class="gallery_wrapper">
                <div class="owl-carousel">
                    {% for img in x.pictures %}
                        <div class="picture_wrapper">
                            <img src="{{ img }}"/>
                        </div>
                    {% endfor %}
                </div>
            </div>
            <div class="content_wrapper">
                <div class="content_title">
                    <h3 class="cycle_title">
                        {{ x.subtitle|safe }}
                    </h3>
                </div>
                <div class="desc">
                    {{ x.content|safe }}
                </div>
                <div class="links_wrapper">
                    {% if x.carta %}
                        <a class="btn_primary" href="{{ x.carta|safe }}" target="_blank">
                            {{ T_consultar_carta }}
                        </a>
                    {% endif %}
                    {% if x.availability %}
                        <span>{{ x.availability|safe }}</span>
                    {% endif %}
                    {% if x.contact_form %}
                        <a class="btn_contact">
                            {{ x.contact_form|safe }}
                        </a>
                    {% endif %}
                </div>
            </div>
            {% if x.contact_form %}
                <div class="contact_form_popup">
                    <style type="text/css">
                        .contact_form_popup #contact label {
                            text-transform: capitalize !important;
                        }
                    </style>
                    {{ contact_html|safe }}
                </div>
            {% endif %}
        </div>
    {% endfor %}
</div>
<!-- lightbox -->
<script>
  $(function () {
    $(".cycle_wrapper_events .owl-carousel").owlCarousel({
      loop: true,
      nav: true,
      dots: false,
      items: 1,
      navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
      autoplay: false
    });

    $(".cycle_wrapper_events .links_wrapper .btn_contact").click(function () {
      $.fancybox.open($(".contact_form_popup"), {wrapCSS: 'popup-start'});
    });
  })
</script>