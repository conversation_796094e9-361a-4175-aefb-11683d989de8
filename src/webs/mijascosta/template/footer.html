<section class="newsletter_wrapper effects_sass" sass_effect="slide_up_effect">

    <div class="container12">
        <div class="black_overlay"></div>
        <img class="newsletter_background" data-src="{{ newsletter_section.pictures.0 }}=s1900" lazy="true">

        <div class="newsletter_text_wrapper">
            <h3 class="newsletter_title_element">{{ newsletter_section.subtitle|safe }}</h3>

            <div class="newsletter_description_element">{{ newsletter_section.content|safe }}</div>
        </div>

        {{ newsletter|safe }}

        <div class="social_elements">
            <div id="social">
                {% if facebook_id %}
                    <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank">
                        <i class="fa fa-facebook"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank">
                        <i class="fa fa-twitter"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{ youtube_id }}" target="_blank">
                        <i class="fa fa-youtube"></i>
                    </a>
                {% endif %}

                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank"><i
                            class="fa fa-instagram"></i></a>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<footer>
    <div class="full-copyright">
        <div class="hotel_name_element">{{ hotel_name|safe }}</div>
        <div class="footer-copyright-links container12">
            {% for x in policies_section %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>
        </div>
        {% if texto_legal %}
            <div id="div-txt-copyright" class="footer-copyright container12">
                {{ texto_legal|safe }}
            </div>
        {% endif %}
    </div>
</footer>