{% spaceless %}
<!DOCTYPE html>
<html lang="{{ language_code }}">
<head>
    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
        {{ sectionName|safe }} {% else %}  {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% if namespace %}
          <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
    {% else  %}
          <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}

    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/css/{{base_web}}/styles_landing.css?v=1.5"/>

    {{ jquery|safe }}
</head>
<body class="{{ language }}" style="background: url({{ image_background.servingUrl|safe }}=s1900);background-attachment: fixed;background-size: cover;">
    {% if ticks %}
        <div class="ticks_wrapper">
            {% for x in ticks %}
                <div class="tick_element">
                    <div class="tick_image"><img src="{{ x.servingUrl|safe }}=s80-c"/></div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    <div id="logotype">
        <img src="{{ logotype|safe }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
    </div>

    {{ newsletter|safe }}
</body>
</html>
{% endspaceless %}
