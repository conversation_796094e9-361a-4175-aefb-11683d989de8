{# Rooms #}
<section class="rooms_wrapper">
    {% for x in rooms %}
        <article class="room_element">
            <div class="exceded">
                <ul class="slides">
                    {% for picture in x.pictures %}
                        <li>
                            <a href="javascript:showGalleryFixed([ {% for picture in x.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ x.title|safe }}'}, {% endfor %} ]);">
                                <span class="plus_image">
                                    <i class="fa fa-camera" aria-hidden="true"></i>
                                </span>
                                <img class="room_image" src="{{ picture.servingUrl }}">
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
            <div class="room_description_wrapper">
                <h3 class="room_title">{{ x.title|safe }} {% if x.capacity %}|
                    <span class="capacity">{{ T_max_capacity }}: {% for y in x.capacity %}<img class="capacity_image"
                                                                                               src="/img/{{ base_web }}/capacity_image.png"> {% endfor %}</span>{% endif %}
                </h3>

                <div class="room_description">{{ x.description|safe }}</div>
                {% if x.linkUrl %}
                    <a href="{{ x.linkUrl|safe }}" class="see_more_room">{{ T_ver_mas }}</a>
                {% endif %}
                <div class="room_buttons_wrapper">
                    <a href="#data" class="button-promotion room_book">{{ T_reservar }}</a>
                </div>
            </div>
        </article>
    {% endfor %}
</section>
<script async>
    function roomsPreparation() {
        $(".room_element").each(function () {
            var description_room_height = $(this).find(".room_description_wrapper").outerHeight();
            if (description_room_height > 278) {
                $(this).find(".exceded .slides li").height(description_room_height);
            }

            $(this).find(".exceded").flexslider({
                                                    controlNav: false,
                                                    directionNav: true,
                                                    prevText: "<img src='/img/{{ base_web }}/left_room.png' alt='left' title='left'>",
                                                    nextText: "<img src='/img/{{ base_web }}/right_room.png' alt='right' title='rigth'>",
                                                    animation: false,
                                                    itemWidth: 365,
                                                    slideshow: false,
                                                    itemMargin: 0,
                                                    minItems: 1,
                                                    maxItems: 1
                                                })
        });
    }

    $(function () {
        setTimeout(roomsPreparation(), 1500)
    })
</script>