//Base web (change too in templateHandler and in config.rb)
$base_web: "triuo";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #e9ac50;
$corporate_2: #a61d33;
$corporate_3: #787878;
$corporate_4: rgb(135, 135, 135);


// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $gray-2;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

