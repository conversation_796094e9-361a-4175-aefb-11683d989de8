{% if name_hotel == "triunfo" %}

<header id="header">
    <div id="wrapper-header" class="container12">

         <div class="column4 social-container">

             <div id="social">

                    {%if facebook_id %}
                        <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img class="img-social-facebook" src="/img/{{ base_web }}/social/facebook.png" width="32" height="32" alt="{{T_siguenos_en}} facebook" title="{{T_siguenos_en}} facebook"> </a>
                    {% endif %}
                    {% if twitter_id %}
                        <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img class="img-social-twitter" src="/img/{{ base_web }}/social/twitter.png" width="32" height="32" alt="{{T_siguenos_en}} twitter" title="{{T_siguenos_en}} twitter"> </a>
                    {% endif %}
                    {% if google_plus_id %}
                        <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank"> <img class="img-social-google" src="/img/{{ base_web }}/social/googleplus.png" width="32" height="32" alt="{{T_siguenos_en}} google plus" title="{{T_siguenos_en}} google plus"> </a>
                    {% endif %}
                    {% if youtube_id %}
                        <a href="https://www.youtube.com/{{youtube_id}}" target="_blank"> <img class="img-social-youtube" src="/img/{{ base_web }}/social/youtube.png" width="32" height="32" alt="{{T_siguenos_en}} youtube" title="{{T_siguenos_en}} youtube"> </a>
                    {% endif %}
                    {% if flickr_id %}
                        <a href="http://www.flickr.com/photos/{{flickr_id}}/" target="_blank"> <img src="/img/{{ base_web }}/social/flickr.png" width="32" height="32" alt="{{ T_siguenos_en }} flickr" title="{{ T_siguenos_en }} flickr"> </a>
                    {% endif %}
             </div>
         </div>

         <div id="logoDiv" class="column4">
             <a href="{{host|safe}}/">
                 <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
             </a>
         </div>

         <div class="column4 right-header-container">
                 <div id="lang" class="lang2">

                    <span id="selected-language" class="selected-language2"><span style="font-weight: lighter"> {{ T_idioma }}:</span> {% if language_selected|safe == 'Español' %}Castellano{% else %}{{ language_selected|safe }}{% endif %}</span>
                    <span class="arrow"></span>

                    <ul id="language-selector-options" class="language-selector-options2">
                        {% for key, language in language_codes.items %}
                            <li class="language-option-flag">
                                <a hreflang="{{ key }}" href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{% if language|safe == 'Español' %}Castellano{% else %}{{ language }}{% endif %}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
         </div>

    </div>

    <nav id="main_menu">
        <div itemscope="itemscope" itemtype="//schema.org/SiteNavigationElement" id="mainMenuDiv" class="container12">
            {% include "main_div.html" %}
        </div>
    </nav>
</header>
{% endif %}

{% if name_hotel == "campanario" %}

<header id="header">
    <div id="wrapper-header" class="container12">
         <div id="logoDiv" class="column2">

             <a href="{{host|safe}}/">
                 <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
             </a>
         </div>

        <div class="top-header column10">

            <div class="column2 right-header-container">
                 <div id="lang" class="lang2">

                    <span id="selected-language" class="selected-language2"><span style="font-weight: lighter"> {{ T_idioma }}:</span> {% if language_selected|safe == 'Español' %}Castellano{% else %}{{ language_selected|safe }}{% endif %}</span>
                    <span class="arrow"></span>

                    <ul id="language-selector-options" class="language-selector-options2">
                        {% for key, language in language_codes.items %}
                            <li class="language-option-flag">
                                <a hreflang="{{ key }}" href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{% if language|safe == 'Español' %}Castellano{% else %}{{ language }}{% endif %}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <div class="tick_wrapper">
                <div class="ticks">
                    <img src="/img/{{ base_web }}/gastos.png" width="32" height="32"
                         alt="{{ T_sin_gastos }}" title="{{ T_sin_gastos }}"><span
                        class="tick_gastos">{{ T_sin_gastos }}</span>
                </div>
                <div class="ticks">
                    <img src="/img/{{ base_web }}/segura.png" width="32" height="32"
                         alt="{{ T_reserva_segura }}" title="{{ T_reserva_segura }}"><span
                        class="tick_segura">{{ T_reserva_segura }}</span>
                </div>
                <div class="ticks">
                    <img src="/img/{{ base_web }}/pago.png" width="32" height="32"
                         alt="{{ T_pago_directo }}" title="{{ T_pago_directo }}"><span
                        class="tick_pago">{{ T_pago_directo }}</span>
                </div>
            </div>

            <div class="column2 social-container">
                <div id="social">
                    {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img class="img-social-facebook" src="/img/{{ base_web }}/social/facebook_campanario.png" width="32" height="32" alt="{{T_siguenos_en}} facebook" title="{{T_siguenos_en}} facebook"> </a>
                    {% endif %}
                    {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img class="img-social-twitter" src="/img/{{ base_web }}/social/twitter_campanario.png" width="32" height="32" alt="{{T_siguenos_en}} twitter" title="{{T_siguenos_en}} twitter"> </a>
                    {% endif %}
                    {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank"> <img class="img-social-google" src="/img/{{ base_web }}/social/google_campanario.png" width="32" height="32" alt="{{T_siguenos_en}} google plus" title="{{T_siguenos_en}} google plus"> </a>
                    {% endif %}
                </div>
            </div>

        </div>

        <nav id="main_menu">
            <div itemscope="itemscope" itemtype="//schema.org/SiteNavigationElement" id="mainMenuDiv">
                {% include "main_div.html" %}
            </div>
        </nav>

    </div>
</header>
{% endif %}