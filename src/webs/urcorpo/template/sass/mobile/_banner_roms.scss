.banner_rooms_wrapper {
  padding: $mobile_padding;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  .banner_rooms {
    padding-bottom: 40px;
    .room {
      .room_content {
        position: relative;
        background-color: $grey;
        padding: 30px 20px;
        text-align: center;
        .title {
          @include rooms_title_styles;
          margin: 0 0 20px;
          padding-left: 0;
        }
        .text {
          @include text_styles;
          font-size: 17px;
          line-height: 23px;
          .hide_in_web {
            display: none;
          }
        }
        .links {
          .btn_personalized_1 {
            margin: 30px 10px 0;
          }
        }
      }
      .room_pic {
        overflow: hidden;
        width: 100%;
        height: 230px;
        position: relative;
        .owl-stage-outer, .owl-stage, .owl-item {
          height: 100%;
          overflow: hidden;
        }
        .owl-nav {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 40px;
          .owl-prev, .owl-next {
            @include owl_nav_styles;
            position: absolute;
            left: 15px;
            top: 0;
            i {
              color: white;
              font-size: 26px;
            }
          }
          .owl-next {
            left: auto;
            right: 15px;
          }
        }
        img {
          @include center_image;
          width: auto;
        }
      }
    }
    > .owl-nav {
      @include center_x;
      width: 100%;
      bottom: 5px;
      &:after {
        content: '';
        width: 1px;
        height: 60%;
        background-color: $corporate_2;
        @include center_xy;
      }
      .owl-prev, .owl-next {
        @include owl_nav_styles;
        width: 50%;
        text-align: center;
        padding: 10px 5px;
        font-size: 11px;
        background-color: $grey;
        color: $corporate_1;
        span {
          display: inline-block;
          vertical-align: middle;
          position: relative;
          z-index: 2;
          margin-left: 5px;
        }
        i {
          display: inline-block;
          vertical-align: middle;
          font-size: 16px;
        }
      }
      .owl-next {
        span {
          margin-right: 5px;
          margin-left: 0;
        }
      }
    }
  }
}