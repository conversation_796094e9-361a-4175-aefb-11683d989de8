.news_widget_wrapper_blog {
  width: 100%;
  overflow: hidden;
  padding: $mobile_padding;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  .entry_widget{
    position: relative;
    .image{
      img{
        width: 100%;
      }
    }
    .read_more{
      font-size: 15px;
      font-weight: 400;
      position: absolute;
      //top: 170px;
      @include center_x;
      display: inline-block;
      background-color: #B1C719;
      border-radius: 5px;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-transform: uppercase;
      transform: translate(-50%, -60%);
      .fa.fa-plus{
        margin-right: 5px;
      }
      }
    .title{
      text-align: left;
      font-weight: bold;
      padding: 30px 0px 15px;
      font-size: 150%;
      a{
        color: $corporate_1;
      }

    }
    .date{
      text-align: left;
      padding: 0 10px;
      color: #CCC;
      font-size: 70%;
      .fa-calendar-o{
        margin-right: 5px;
        }
      }
    }
    .content{
      position: relative;

      .content_short{
        margin: 15px 0 20px;
      }
    }
  }
