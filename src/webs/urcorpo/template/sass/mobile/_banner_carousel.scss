.banner_carousel_full_wrapper {
  padding: $mobile_padding;
  z-index: 1;
  overflow: hidden;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  .svg_logo {
    position: absolute;
    top: 20px;
    right: -10%;
    z-index: -1;
    height: 85%;
    * {
      fill: rgba(0, 0, 0, .05);
    }
  }
  .banner_carousel_wrapper {
    .banner {
      overflow: hidden;
      .img_wrapper {
        position: relative;
        width: 100%;
        height: 220px;
        overflow: hidden;
        img {
          @include center_image;
          width: auto;
        }
      }

      .banner_content {
        position: relative;
        padding: 30px 20px 50px;
        text-align: center;
        background-color: white;
        .banner_icon {
          @include icon_styles;
          @include center_x;
          top: -30px;
        }
        .title {
          @include banner_title_styles;
          margin: 0;
        }
        .text {
          @include text_styles;
          padding: 20px;
        }
        .icon_link {
          @include center_x;
          bottom: 30px;
        }
      }
      &.first_banner {
        display: none;
      }
    }

    .owl-nav {
      text-align: center;
      .owl-prev, .owl-next {
        @include owl_nav_styles;
        margin: 0 35px 0 0;
        i {
          font-size: 48px;
        }
      }
      .owl-next {
        margin: 0 0 0 35px;
      }
    }
  }
  .banner_carousel_content {
    margin: $mobile_padding 0 30px;
    text-align: center;
    .content_title {
      @include rooms_title_styles;
      font-weight: 300;
      padding-left: 0;
      margin: 0 0 20px 0;
    }
    .content_text {
      @include text_styles;
    }
  }
}