.banner_map_wrapper, .modal {
  text-align: center;
  .title {
    display: inline-block;
    vertical-align: top;
    margin-right: 40px;
    font-family: "DIN Alternate", sans-serif;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    big {
      font-size: 50px;
      font-weight: 700;
    }
    &:before {
      content: '';
      position: absolute;
      top: -30px;
      right: 0;
      z-index: -1;
      display: block;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $corporate_3;
      opacity: .4;
    }
  }
  .banner_map {
    width: 100%;
    position: relative;
    .background_wrapper {
      position: relative;
      width: 100%;
      height: 600px;
      overflow: hidden;
      .background {
        @include center_image;
      }
    }
    .hotel {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: white;
      background-repeat: no-repeat;
      background-size: 60%;
      background-position: center;
      &:before {
        content: '';
        @include center_xy;
        z-index: 1;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        @include linear_gradient($corporate_2, rgba($corporate_2, .5));
        opacity: 0;
        @include transition(opasity, .6s);
      }
      .svg_logo {
        @include center_xy;
        z-index: 2;
        width: 70%;
        opacity: 0;
        @include transition(opasity, .6s);
        * {
          fill: white;
        }
      }
      &:hover {
        background-image: none !important;
        .hotel_info {
          z-index: 2;
          opacity: 1;
        }
        &:before, .svg_logo {
          opacity: 1;
        }
      }
      &:after {
        content: '';
        width: 1px;
        height: 40px;
        @include center_x;
        top: -70%;
        background: white;
      }
      &.hotel_right {
        .hotel_info {
          left: auto;
          right: 100%;
          margin-left: 0;
          padding-left: 0;
          margin-right: -30px;
          padding-right: 60px;
        }
      }
      &.hotel_bottom {
        .hotel_info {
          top: auto;
          bottom: 0;
        }
      }
      img.logo {
        @include center_xy;
        max-height: 80%;
        max-width: 80%;
        opacity: 0;
        &.logo_black {
          opacity: 1;
        }
      }
      .hotel_info {
        position: absolute;
        top: 0;
        left: 100%;
        margin-left: -30px;
        padding-left: 60px; // 30px overlap
        width: 300px;
        opacity: 0;
        z-index: -100;
        filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.3));
        @include transition(opacity, 1s);
        .hotel_info_pic {
          position: relative;
          width: 100%;
          height: 110px;
          overflow: hidden;
          a {
            position: absolute;
            z-index: 1;
            top: 15px;
            left: 15px;
            color: white;
            font-size: 30px;
          }
          img {
            @include center_image;
            max-width: 130%;
          }
        }
        .hotel_info_name {
          background: $corporate_1;
          color: white;
          padding: 10px 20px;
          font-weight: bold;
          font-style: italic;
          text-align: left;
        }
        .hotel_info_desc {
          background: white;
          text-align: left;
          color: $corporate_1;
          padding: 10px 20px;
          font-size: 13px;
          a {
            color: $corporate_3;
            font-weight: 700;
          }
          ul {
            li {
              display: list-item;
              list-style: disc;
              margin-bottom: 10px;
              margin-left: 20px;
              position: relative;
            }
          }
        }

        .button_promotion {
          width: 100%;
        }
      }
    }
    .hotel#map_mint-valleescondido {
      &:after {
        content: '';
        width: 1px;
        height: 30px;
        @include center_x;
        top: 100%;
        background: white;
      }
    }
  }
}

.banner_map_wrapper {
  margin-top: 50px;
}

.modal {
  .banner_map {
    width: 80vw;
    .background {
      vertical-align: middle;
    }
    .hotel {
      .hotel_info {
        z-index: 2;
        opacity: 1;
      }
    }
  }
}