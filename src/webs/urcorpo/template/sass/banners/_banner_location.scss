.banner_location {
  position: relative;
  overflow: hidden;
  .banner_location_wrapper {
    iframe {
      width: 100%;
      display: block;
    }
    .map_info {
      position: absolute;
      top: 0;
      bottom: 0;
      left: calc((100% - 1140px) / 2);
      padding: 50px 80px 50px 30px;
      background-color: rgba($corporate_1, .8);
    }
    .title {
      text-align: left;
      color: white;
      font-size: 26px;

      strong {
        font-size: 38px;
        font-weight: 400;
        font-style: italic;
      }
    }
    .banner_location_content {
      color: white;
      line-height: 25px;
      font-size: 19px;
      font-weight: 400;
      padding: 35px 0;
    }
    .link_map {
      padding: 10px 40px;
      font-size: 20px;
      font-weight: 700;
    }
  }
  .map_widget {
    position: relative;
    background-color: $corporate_1;
    padding-bottom: 100px;
    h3 {
      text-align: center;
      color: white;
      font-size: 26px;
      padding-top: 10px;
    }
    .form_banner_map {
      @include center_xy;
      bottom: 20px;
      .place {
        display: inline-block;
        vertical-align: middle;
        padding: 10px 20px;
        text-align: center;
        font-size: 16px;
        font-weight: 300;
        height: 45px;
        border: none;
        background-color: #E9E9E9;
      }

      .go {
        padding: 10px 60px;
        font-size: 17px;
        margin: 20px 0 20px 5px;
        height: 45px;
        background: white;
      }
      .go_map {
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        > i {
          color: $corporate_2;
          font-size: 36px;
          margin-left: 10px;
        }
      }
    }
  }
}