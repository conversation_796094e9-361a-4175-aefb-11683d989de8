.news_widget_wrapper {
  @include base_banner_styles;
  .news_filter {
    display: flex;
    border-bottom: 2px solid $corporate_2;
    margin-bottom: 20px;
    .filter {
      display: inline-block;
      width: 100%;
      padding: 15px;
      color: $corporate_1;
      text-align: center;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 20px;
      cursor: pointer;
      &.active {
        background-color: $corporate_2;
        color: white;
      }
      &:not(:last-of-type) {
        margin-right: 20px;
      }
    }
  }
  .new_block {
    @include display_flex;
    justify-content: center;
    .entry_widget {
      display: inline-block;
      width: calc((100% / 3) - 20px);
      margin-bottom: 30px;
      &:not(:nth-of-type(3n)):not(:last-of-type) {
        margin-right: 30px;
      }
      .image {
        position: relative;
        width: 100%;
        height: 250px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .new_content {
        background-color: $grey;
        padding: 20px 0 0;
        text-align: center;
        .title {
          @include banner_title_styles;
        }
        .text {
          @include text_styles;
          padding: 20px;
          font-size: 16px;
        }
        .links {
          display: flex;
          width: 100%;
          background-color: $corporate_2;
          color: white;
          padding: 10px 20px;
          .comments, .author, .date {
            position: relative;
            display: inline-block;
            width: 100%;
            &:not(:last-of-type):before {
              content: '';
              position: absolute;
              top: -5px;
              bottom: -5px;
              right: 0;
              width: 1px;
              background-color: white;
            }
          }
        }
      }
    }
  }
}