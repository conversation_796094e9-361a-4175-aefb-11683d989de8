<div class="banner_offers_full_wrapper">
    <div class="banner_offer_wrapper container12">
        {% for offer in offers %}
            <div class="banner {% if offer.priority and 'P' in offer.priority %}paquete{% else %}oferta{% endif %}">
                <div class="img_wrapper">
                    <img data-src="{{ offer.picture }}=s500-c" alt="{{ offer.name }}" lazy="true">
                    <div class="center_xy"><span>{% if offer.picDesc %}{{ offer.picDesc|safe }}{% endif %}</span></div>
                </div>
                <div class="banner_content">
                    <h4 class="title">{{ offer.name|safe }}</h4>
                    <div class="text">{{ offer.description|safe }}</div>
                    <a href="#data" class="button_promotion btn_personalized_1"
                       data-not_in="{{ offer.not_in }}">{{ T_reservar }}</a>
                    {% if offer.linkUrl %}
                        <a href="{{ offer.linkUrl }}" class="btn btn_secondary">{{ T_ver_mas }}</a>{% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>