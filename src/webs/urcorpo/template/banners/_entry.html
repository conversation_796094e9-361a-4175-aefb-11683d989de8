<article class="entry">
    <h1 class="entry_title">{{ entry.name|safe }}</h1>
    <div class="entry_picture">
        <img src="{{ entry.picture|safe }}=s1140" alt="">
    </div>
    <div class="entry_info">
        <span class="comments">{{ entry.comments|length }} {{ T_comentarios }}</span>
        {% if entry.creationDate %}<span class="date">{{ entry.creationDate|safe }}</span>{% endif %}
        {% if entry.author %}<div class="author">{{ entry.author|safe }}</div>{% endif %}
    </div>
    <div class="entry_content">{{ entry.description|safe }}</div>
    <div class="entry_comments">
        <h3>{{ T_comentarios }}</h3>
        {% if entry.comments|length > 0 %}
            <div class="comment_list">
                {% for comment in entry.comments %}
                    <div class="comment">
                        <div class="name"><span>{{ comment.name|safe }}</span>:</div>
                        <div class="text">{{ comment.description|safe }}</div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        <div class="comment_form">
            <form name="fancy_contact_form" id="fancy_contact_form" method="post" action="/utils/?action=contact">
                <input type="hidden" name="section" id="section-name" value="Comment in {{ sectionName|safe }}"/>

                <div class="text_element">
                    <label for="comments" class="title">{{ T_comentarios }}</label>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""></textarea>
                </div>

                {% if destination_address %}
                    <input type="hidden" name="destination_email" id="destination_email"
                           value="{{ destination_address }}">
                {% endif %}

                <div class="input_element">
                    <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                    <input type="text" id="name" name="name" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="email" class="title">{{ T_email }}</label>
                    <input type="text" id="email" name="email" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="telephone" class="title">{{ T_telefono }}</label>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
                </div>

                <div class="check_element">
                    <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                    <span class="title">
                        <a data-fancybox data-options='{"caption" : "{{ T_lopd }}", "src" : "/{{language}}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" class="myFancyPopup fancybox.iframe newsletter_popup" href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                    </span>
                </div>

                <div id="contact-button-wrapper">
                    <div id="popup_form_button" class="btn_personalized_1">
                        {{ T_enviar }}
                    </div>
                </div>

                <div class="thanks_popup_wrapper" style="display: none">{{ T_gracias_contacto }}</div>
            </form>
            <script type="text/javascript" src="/static_1/lib/jquery.validate.js" async></script>
            <script type="text/javascript">
                $(window).load(function () {
                    jQuery.validator.addMethod("phone", function (phone_number, element) {
                        phone_number = phone_number.replace(/\s+/g, "");
                        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                                phone_number.match(/^[0-9 \+]\d+$/);
                    }, "Please specify a valid phone number");

                    $("#fancy_contact_form").validate({
                                                          rules: {
                                                              name: "required",
                                                              privacy: "required",
                                                              email: {
                                                                  required: true,
                                                                  email: true
                                                              },
                                                              telephone: {
                                                                  required: true,
                                                                  phone: true
                                                              },
                                                              comments: "required"
                                                          },
                                                          messages: {
                                                              name: "{{ T_campo_obligatorio}}",
                                                              privacy: "{{ T_campo_obligatorio }}",
                                                              email: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  email: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              telephone: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  phone: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              comments: "{{ T_campo_obligatorio|safe }}"
                                                          }

                                                      });

                    $("#popup_form_button").click(function () {


                        if ($("#fancy_contact_form").valid()) {
                            $.post(
                                    "/utils/?action=contact",
                                    {
                                        'name': $(".input_element #name").val(),
                                        'telephone': $(".input_element #telephone").val(),
                                        'email': $(".input_element #email").val(),
                                        'comments': $(".input_element #comments").val(),
                                        'section': $("#section-name").val()
                                    },

                                    function (data) {
                                        $(".input_element #name").val("");
                                        $(".input_element #telephone").val("");
                                        $(".input_element #email").val("");
                                        $(".input_element #comments").val("");
                                        $(".thanks_popup_wrapper").fadeIn("slow");
                                    }
                            );
                        }
                    });
                })
            </script>
        </div>
    </div>
</article>