{% if mobile_version %}
    <div class="content_subtitle_wrapper">
        <h3 class="subtitle_title"><span>{{ content_subtitle.subtitle|safe }}</span></h3>
        <div class="subtitle_description">
             {{ content_subtitle.content|safe }}
        </div>
    </div>
{% endif %}

<form name="contact" class="form_unete" id="apply-jobs" method="post" action="/utils/?action=work_with_us" enctype="multipart/form-data">

<input type="hidden" name="action" id="action" value="contact"/>
<div class="info">

		<input type="hidden" name="section" id="section-name" value="{{ sectionName|safe}}"/>
        {% if destination_address %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ destination_address }}">
        {% endif %}

		<div class="contInput">
			<label for="name" class="title">{{T_nombre_y_apellidos}}</label>
			<input type="text" id="name" name="name" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="telephone" class="title">{{T_telefono}}</label>
			<input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="address" class="title">{{T_direccion}}</label>
			<input type="text" id="address" name="address" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="city" class="title">{{T_ciudad}}</label>
			<input type="text" id="city" name="city" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="experience" class="title">{{ T_experiencia }}</label>
			<input type="text" id="experience" name="experience" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="aviability" class="title">{{ T_disponibilidad_incorporacion }}</label>
			<input type="text" id="aviability" name="aviability" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="own_car" class="title">{{ T_vehiculo_propio }}</label>
			<input type="text" id="own_car" name="own_car" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
			<label for="postal_code" class="title">{{T_codigo_postal}}</label>
			<input type="text" id="postal_code" name="postal_code" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="email" class="title">{{T_email}}</label>
			<input type="text" id="email" name="email" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="email" class="title">{{T_confirm_email}}</label>
			<input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value=""/>
		</div>

        <div class="contInput">
             <label class="title">{{T_adjunta_cv}} (Max. 3Mb)</label>
             <input type="file" id="file_cv" name="file" value="" style="width: 320px;"/>
             <input type="hidden" id="url_4_upload" name="url_4_upload" value="{{ upload_url_cvs|safe }}"/>

        </div>

        <span style="text-align: left;display: block"><input class="bordeInput" id="privacy" name="privacy" type="checkbox" value="privacy"/><a class="myFancyPopup fancybox.iframe privacylink" {% if mobile_version %} data-fancybox data-type="iframe" {% endif %} title="Politica de privacidad" href="/es/?sectionContent=politica-de-privacidad.html" rel="nofollow" style="color: #006eb4;font-size: 12px;font-family: 'Source Sans Pro';line-height: 22px;font-weight: 400;display: block;">{{T_declaro_condiciones}}</a></span>

        <button style="display: block;clear:both;" id="contact-button" class="btn-corporate" onclick="return false">
            {{T_enviar}}
        </button>

	<br clear="all">
</div>
</form>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

  $(function () {
    $("#apply-jobs").validate({
        rules: {
            name: "required",
            email: {
                required: true,
                email: true
            },
            emailConfirmation: {
                required: true,
                equalTo: "#email",
                email: true
            },
            telephone: {
                required: true
            },
            privacy: "required",
            file:"required"
        },
        messages: {
            name: "{{ T_campo_obligatorio}}",
            email: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}"
            },
            emailConfirmation: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}",
                equalTo: "{{T_not_confirmed_email_warning|safe}}"
            },
            telephone: {
                required: "{{ T_campo_obligatorio|safe }}"
            },
            file:"{{ T_campo_obligatorio}}",
            privacy: "{{ T_campo_obligatorio}}"
        }

    });

    function envia_form_work(id_form, clase_form) {

        var url_for_upload = "";

        $(clase_form + " " + ".btn-corporate").css("display", "none");

        $.ajax({
            url: "/get_upload_url",
            type: 'GET',
            data: formData,
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                url_for_upload = returndata;
            }
        });


        if ($(id_form).valid()) {

            var url_cv_download = "";

            var confirma_no_cv = 1;

            if ($(clase_form + " " + "#file_cv").length && url_for_upload && $(clase_form + " " + "#file_cv").val()) {
                //upload the file to get a url
                confirma_no_cv = 0;

                //grab all form data
                var formData = new FormData($(id_form)[0]);

                $.ajax({
                    url: url_for_upload,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        url_cv_download = returndata;
                    }
                });
            }

            if (!confirma_no_cv && (!url_cv_download || url_cv_download == "NO_FILE_UPLOAD")) {
                alert("ERROR UPLOADING CURRICULUM VITAE");
                window.location.reload();
                return false;
            }

            var send_email = 1;
            if (confirma_no_cv) {
                if (confirm($.i18n._("confirm_no_cv"))) {
                }
                else {
                    send_email = 0;
                }
            }

            if (send_email) {
                $.post(
                    "/utils/?action=work_with_us",
                    {
                        'name': $("#name").val(),
						'telephone': $("#telephone").val(),
						'address': $("#address").val(),
						'city': $("#city").val(),
						'experience': $("#experience").val(),
						'aviability': $("#aviability").val(),
						'own_car': $("#own_car").val(),
						'postal_code': $("#postal_code").val(),
						'email': $("#email").val(),
						'section': $("#section-name").val(),
						'destination_email': $("#destination_email").val(),
						'privacy': $("#privacy").val(),
                        'url_file_download': url_cv_download
                    },

                    function (data) {
                        //alert($.i18n._("gracias_contacto"));
                        window.location.reload();
                        alert("{{ T_gracias_contacto }}");
						$("#name").val("");
						$("#telephone").val("");
						$("#address").val("");
						$("#city").val("");
						$("#experience").val("");
						$("#aviability").val("");
						$("#own_car").val("");
						$("#postal_code").val("");
						$("#email").val("");
						$("#emailConfirmation").val("");
                    }
                );
            }
        }
        else {
            $("label.error").css("display", "none");
        }

        $(clase_form + " " + ".btn-corporate").css("display", "");
    }

    $("#contact-button").click(function () {
        //id form,class form
        envia_form_work('#apply-jobs', '.form_unete');
    });
});


</script>

