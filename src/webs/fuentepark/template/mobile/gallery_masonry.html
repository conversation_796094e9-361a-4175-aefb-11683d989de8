{% if title %}
    <div id="title_line" class="container12">
        <h2 class="gallery_title">{{ title|safe }}</h2>
    </div>
{% endif %}

{% if pictures %}
    <div class="gallery_wrapper">
        <div class="gallery_masonry">
            {% for picture in pictures[:3] %}
                <div class="crop gallery_image_container">
                    <a  href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe}}" class="swipebox">
                        <img src="{{ picture.servingUrl }}" title="{{ picture.name|safe}}" alt="{{ picture.name|safe}}" />
                    </a>
                </div>
            {% endfor %}
        </div>
        {% if pictures|length > 3 %}
            <div class="gallery_container {% if pictures|length > 3 and gallery_carousel %}owl-carousel{% endif %}">
                {% for picture in pictures[3:] %}
                    <div class="crop gallery_image_container">
                        <a  href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe}}" class="swipebox">
                            <img src="{{ picture.servingUrl }}" title="{{ picture.name|safe}}" alt="{{ picture.name|safe}}" />
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
{% endif %}

<script>
$(window).load(function () {
    $(".gallery_container.owl-carousel").owlCarousel({
        loop: false,
        margin: 0,
        autoplay: false,
        items : 1,
        dots: false,
        nav: true,
        navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
    });
});
</script>