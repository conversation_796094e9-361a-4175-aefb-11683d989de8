# -*- coding: utf-8 -*-
from booking_process.constants.advance_configs_names import PROMOCODE_IN_EMAIL
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.templates.template_utils import buildTemplate
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "fuenk"

class TemplateHandler(BaseTemplateHandler2):

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).getBookingWidgetOptions(language)
		options['custom_caption_rooms'] = get_web_dictionary(language)['T_apartamentos']
		options['custom_caption_room'] = get_web_dictionary(language)['T_apartamento']
		options['select_options'] = []

		return options

	def buildSearchEngine(self, language, selectOptions=None):

		params = self.getBookingWidgetOptions(language, selectOptions)
		params['caption_submit_book'] = True
		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		context = {
			'booking_header': get_section_from_section_spanish_name('booking_header', language)

		}
		context['booking_header_promocode'] = self.getSectionAdvanceProperties(context['booking_header'], language).get('promocode')
		options['custom_title_html'] = buildTemplate('%s/template/header_booking.html' % '/'.join(os.path.abspath(__file__).split("/")[:-1]), context)
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		template_path = 'booking/booking_engine_5/_booking_widget.html'
		return self.buildTemplate(template_path, params, allowMobile=False)


	def get_revolution_full_screen(self):
		return "on"

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		section_pictures = []
		bg_picture = {}

		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType'].lower().strip() if sectionToUse['sectionType'] else ""
			section_pictures = get_pictures_from_section_name(section_name, language)

			for picture in section_pictures:
				picture_title = picture.get('title')

				if picture_title and picture_title == 'bg_image':
					bg_picture = picture
					section_pictures = [picture for picture in section_pictures if picture.get('title') != 'bg_image']

		result_params_dict = {'base_web': base_web,
							  'newsletter_info': get_section_from_section_spanish_name("Newsletter", language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'inner_slider_pictures': section_pictures,
		                      'inner_slider_pictures2': self.getPicturesProperties(language,section_name,['bg_body']),
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'booking_engine_2': self.buildSearchEngine2(language)
							  }

		result_params_dict["bottom_popup"] = get_section_from_section_spanish_name("popup inicio footer", language)
		result_params_dict["bottom_popup_text"] = get_section_from_section_spanish_name("promocion pop up", language)
		result_params_dict['bottom_popup_background'] = get_pictures_from_section_name("promocion pop up", language)
		result_params_dict['bottom_popup_promocode'] = get_config_property_value(PROMOCODE_IN_EMAIL)


		if section_name == 'inicio':
			result_params_dict['block_booking'] = True
			result_params_dict['footer_index'] = True
			result_params_dict['content_index'] = True
			result_params_dict['home_section'] = True

		if section_name == "apartamentos" or section_name == "servicios" or section_name == "entorno" or section_name == "centro zen" or section_name == "sostenibilidad":
			result_params_dict['slider'] = True

		if section_name == u'localización y contacto':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html=get_section_from_section_spanish_name(u"Localización", language)
			iframe_google_map=get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form = sectionToUse['subtitle']


			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form

		background_body = ""

		if section_name is not "inicio":
			backgrounds_for_sections = {
				"apartamentos": self.buildBackground("'/img/" + base_web + "/background/habitaciones.jpg'"),
				"ofertas": self.buildBackground("'/img/" + base_web + "/background/ofertas.jpg'"),
				"servicios": self.buildBackground("'/img/" + base_web + "/background/servicios-localizacion.jpg'"),
				"entorno": self.buildBackground("'/img/" + base_web + "/background/entorno.jpg'"),
				u"localización y contacto": self.buildBackground("'/img/" + base_web + "/background/servicios-localizacion.jpg'"),
				u"imágenes": self.buildBackground("'/img/" + base_web + "/background/imagenes.jpg'"),
				"mis reservas": self.buildBackground("'/img/" + base_web + "/background/misreser.jpg'"),
			}

			background_body = backgrounds_for_sections.get(section_name,  self.buildBackground("'/img/" + base_web + "/background/bg_default.jpg'"))

			if bg_picture and bg_picture.get('servingUrl'):
				background_body = self.buildBackground(bg_picture.get('servingUrl'))

		if section_type == "inicio":
			background_body = self.buildBackground()

		result_params_dict['background_body'] = background_body

		if section_name == 'ofertas':
			all_promotions = super(TemplateHandler, self).buildPromotionsInfo(language)
			result_params_dict['promotions'] = all_promotions

		if section_type == "lopd":
			result_params_dict['newsletter_lopd'] = True

		if section_name == 'mis reservas' or section_name == u'imágenes':
			result_params_dict['my_booking'] = True

		#booking header
		booking_header_horizontal = get_section_from_section_spanish_name("booking_header_horizontal", language)
		booking_header_subtitle = booking_header_horizontal.get("subtitle")
		promocode_header = self.getSectionAdvanceProperties(booking_header_horizontal, language).get("promocode")
		#result_params_dict['booking_header_horizontal'] = {'subtitle': booking_header_subtitle, 'promocode': promocode_header}

		if result_params_dict.get("inner_slider_pictures2"):
			for img in result_params_dict['inner_slider_pictures2']:
				if img.get('bg_body'):
					result_params_dict['bg_body'] = img['servingUrl']
		return result_params_dict

	def getTemplateUrl(self, section=None):

		section_name = section['sectionName'].lower().strip()

		if section_name == "inicio":
			template_name = 'fuentepark/template/index.html'

		elif section_name == "ofertas":
			template_name = 'fuentepark/template/promotions.html'

		else:
			template_name = 'fuentepark/template/inner_sections.html'

		return template_name


	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):

		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_1.html',
		}

		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		section_name = section['sectionName']
		language_dict = get_web_dictionary(language)
		section = get_section_from_section_spanish_name(section_name, language)
		section_advance_properties = self.getSectionAdvanceProperties(section, language)

		if section_type == u'Galeria de Imagenes':
			result = {
				'pictures': get_pictures_from_section_name(section_name, language),
			}

			if section:
				result['title'] = section.get('subtitle', '')

			if section_advance_properties:
				result['gallery_carousel'] = section_advance_properties.get('carousel')

			result.update(language_dict)

			return self.buildTemplate_2("mobile/gallery_masonry.html", result, False, "fuentepark")

	def buildBackground(self, backgroundUrl = None):
		if backgroundUrl:
			return "background: url(" + backgroundUrl + ")  no-repeat center center fixed;-webkit-background-size: cover;  -moz-background-size: cover;  -o-background-size: cover;background-size: cover;"

		return "background: none"

