{% if form_proposta != "2" %}
<div class="form_proposa_wrapper">
    <div class="form_header">
        <div class="headerstep step_1 active"><h2>1º {{ T_descripcion_evento|safe }}</h2></div>
        <div class="headerstep step_2"><h2>2º {{ T_info_contacto|safe }}</h2></div>
    </div>
    <form onsubmit="return false" name="contact_proposa" id="contact_proposa" method="post" action="/utils/?action=contact">
        <input type="hidden" name="action" id="action" value="contact"/>
        {% if destination_email %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ destination_email|safe }}">
        {% endif %}

        <div class="step step_1">
            <div class="half_form_big">
                <div class="contInput select i100">
                    <label>{{ T_hotel }}*</label>
                    <select name="hotel" id="hotel">
                            <option value="" disabled {% if not inner_namespace %}selected{% endif %} style="display:none"></option>
                        {% for group, value in selecthoteles.items() %}
                            <optgroup label="{{ group|safe }}">
                                {% for country, info in value.items() %}
                                    {% for hotel in info.group_list %}
                                            <option value="{{ hotel.name|safe }}" class="{{ hotel.id|safe }}" data-events="{{ hotel.events|safe }}" {% if inner_namespace == hotel.id %}selected{% endif %}>{{ hotel.name|safe }} - {{ hotel.destiny_label|safe }}</option>
                                    {% endfor %}
                                {% endfor %}
                            </optgroup>
                        {% endfor %}
                    </select>
                </div><!--
                --><div class="contInput select i60">
                    <label>{{ T_tipo_evento }}*</label>
                    <select name="tipo" id="tipo">
                        <option disabled selected></option>
                        {% for event in tipo_eventos %}
                            <option value="{{ event.description|safe }}" class="{{ event.title|safe }}">{{ event.description|safe }}</option>
                        {% endfor %}
                    </select>
                </div><!--
                --><div class="contInput date i40">
                    <label>{{ T_fecha_evento }}*</label>
                    <input type="text" id="event_date" name="event_date">
                </div><!--
                --><div class="contInput i100">
                    <label>{{ T_nombre_evento }}*</label>
                    <input type="text" id="event_name" name="event_name">
                </div><!--
                --><div class="contInput select i60">
                    <label>{{ T_obligaciones_evento }}*</label>
                    <select name="obligaciones" id="obligaciones">
                        <option disabled selected></option>
                        {% for obligacion in obligaciones %}
                            <option value="{{ obligacion.description|safe }}" class="{{ obligacion.title|safe }}">{{ obligacion.description|safe }}</option>
                        {% endfor %}
                    </select>
                </div><!--
                --><div class="contInput i20">
                    <label>{{ T_n_participantes }}</label>
                    <input type="text" id="event_pax" name="event_pax">
                </div><!--
                --><div class="contInput i20">
                    <label>{{ T_n_habitaciones }}</label>
                    <input type="text" id="event_rooms" name="event_rooms">
                </div>
            </div><!--
            --><div class="half_form_short">
                <div class="contInput i100">
                    <label>{{ T_info_adicional }}</label>
                    <textarea placeholder="{{ T_info_adicional_ex }}" id="extra_info" name="extra_info"></textarea>
                </div>

                {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src='https://www.google.com/recaptcha/api.js'></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}

                <div class="contInput button i100">
                    <button class="next_step btn_black">{{ T_sig_paso }}</button>
                </div>
            </div>
        </div>
        <div class="step step_2">
            <div class="half_form_big">
                <div class="contInput i60">
                    <label>{{ T_full_nombre }}*</label>
                    <input type="text" id="full_name" name="full_name">
                </div><!--
                --><div class="contInput select i40">
                    <label>{{ T_pre_idioma }}</label>
                    <select name="language" id="language">
                        {% for key, value in language_codes.items() %}
                            <option value="{{ value }}" {% if key == language %}selected{% endif %}>{{ value }}</option>
                       {% endfor %}
                    </select>
                </div><!--
                --><div class="contInput i70">
                    <label>{{ T_email }}*</label>
                    <input type="text" id="email" name="email">
                </div><!--
                --><div class="contInput i30">
                    <label>{{ T_contact_phone }}*</label>
                    <input type="text" id="phone" name="phone">
                </div><!--
                --><div class="contInput i100">
                    <label>{{ T_contact_name }}</label>
                    <input type="text" id="comercial_name" name="comercial_name" placeholder="{{ T_contact_name_ext }}">
                </div><!--
                -->
            </div><!--
            --><div class="half_form_short">
                <div class="contInput i100">
                    <p>{{ T_extra_text }}</p>
                    <div class="check_newsletter">
                        <div class="newsletter_checkbox">
                            <input class="check_privacy" id="privacy_form" name="privacy_form" type="checkbox"/>
                            <a href="/{{language}}/?sectionContent=politica-de-privacidad.html" class="myFancyPopup fancybox.iframe newsletter_popup">{{T_lopd}}</a>
                        </div>

                        <div class="newsletter_checkbox">
                            <input class="check_privacy" id="form_promotions" name="form_promotions" type="checkbox"/>
                            <label for="form_promotions">{{T_acepto_promociones}}</label>
                        </div>
                    </div>
                </div>
                <div class="contInput button i100">
                    <button class="prev_step">{{ T_prev_paso }}</button>
                    <button class="btn_black" id="send_proposa">{{ T_enviar }}</button>
                </div>
            </div>
        </div>

    </form>
</div>

<script src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.css">

<script type="text/javascript">
    $(window).on("load", function () {

        var d = new Date(),
            day = d.getDate() < 10 ? "0"+d.getDate() : d.getDate(),
            month = d.getMonth()+1 < 10 ? "0"+(d.getMonth()+1) : d.getMonth()+1,
            year = d.getFullYear(),
            startDate = day + "/" + month + "/" + year;
        var endDate = $.datepicker.parseDate("dd/mm/yy", startDate);
            endDate.setDate(endDate.getDate() + 1);
        $('#event_date').daterangepicker({
            "autoApply": true,
            "linkedCalendars": false,
            "endDate": endDate,
            "minDate": startDate,
            "locale": {
                format: 'DD/MM/YYYY'
            }
        }, function (start, end, label) {

        });


        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact_proposa").validate({
                                   rules: {
                                       hotel: "required",
                                       tipo: "required",
                                       event_date: "required",
                                       event_name: "required",
                                       obligaciones: "required",
                                       privacy_form: "required",
                                       full_name: "required",
                                       email: {
                                           required: true,
                                           email: true
                                       },
                                       phone: {
                                           required: true,
                                           phone: true
                                       }
                                   },
                                   messages: {
                                       hotel: "{{ T_campo_obligatorio}}",
                                       tipo: "{{ T_campo_obligatorio}}",
                                       event_date: "{{ T_campo_obligatorio}}",
                                       event_name: "{{ T_campo_obligatorio}}",
                                       obligaciones: "{{ T_campo_obligatorio}}",
                                       privacy_form: "{{ T_campo_obligatorio}}: ",
                                       full_name: "{{ T_campo_obligatorio}}",
                                       email: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           email: "{{ T_campo_valor_invalido|safe }}"
                                       },
                                       phone: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           phone: "{{ T_campo_valor_invalido|safe }}"
                                       }
                                   }

                               });

        $("#send_proposa").click(function () {
            if ($("#contact_proposa").valid()) {
                var data_to_send = {
                    'name': $("#full_name").val(),
                    'telephone': $("#phone").val(),
                    'email': $("#email").val(),
                    'hotelQuery': $("#hotel").val(),
                    'event': $("#event_name").val(),
                    'event_type': $("#tipo").val(),
                    'event_date': $("#event_date").val(),
                    'num_persons': $("#event_pax").val(),
                    'comments': $("#extra_info").val(),
                    'destination_email': $("#destination_email").val(),
                    'g-recaptcha-response': $("#g-recaptcha-response").val(),
                    'language': $("#language").val(),
                    'commercial_name': $("#comercial_name").val(),
                    'obligations': $("#obligaciones").val(),
                    'total_rooms': $("#event_rooms").val()
                };

                if ("{{ custom_destination_mail }}" && "{{ emailSubjectCustomized }}")
                {
                    data_to_send['email_sender_custom'] = "{{ custom_destination_mail }}";
                    data_to_send['emailSubjectCustomized'] = "{{ emailSubjectCustomized }}";

                }

                if ($("#form_promotions").is(":checked")) {
                    data_to_send['newsletter'] = true;
                }

                if ($("#g-recaptcha-response").val()) {
                $.post(
                        "/utils/?action=contact", data_to_send,
                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                            $("#full_name").val("");
                            $("#phone").val("");
                            $("#email").val("");
                            $("#hotel").val(0);
                            $("#obligaciones").val(0);
                            $("#tipo").val(0);
                            $("#extra_info").val("");
                            $("#event_name").val("");
                            $("#event_date").val("");
                            $("#event_pax").val("");
                            $("#event_rooms").val("");
                            $("#comercial_name").val("");
                        }
                );
                } else {
                    $(".g-recaptcha > div").css('border', '1px solid red');
                }
            }
        });

        if($("#hotel").val() != ""){
            filter_events($("#hotel"));
        }
        $("#hotel").change(function () {
            filter_events($(this));
        });
        $(".next_step").click(function (e) {
            e.preventDefault();
            if ($("#contact_proposa").valid()) {
                $(".step.step_1").slideUp();
                $(".step.step_2").slideDown();
                $(".headerstep").toggleClass("active");
            }
        });
        $(".prev_step").click(function (e) {
            e.preventDefault();
            $(".step.step_1").slideDown();
            $(".step.step_2").slideUp();
            $(".headerstep").toggleClass("active");
        });
        var filter_e = getUrlParameter('filter_event');
        if (filter_e) {
            $("." + filter_e).attr("selected","selected");
        }
    });
    function filter_events(target) {
        if(target.find("option:selected").attr("data-events")){
            var available_events = target.find("option:selected").attr("data-events").split(",");
            var element_selected = false;
            if(available_events.length > 0) {
                $("#tipo option").hide();
                $("#tipo").val("");
                $.each( available_events, function( index, value ){
                    $("#tipo option.event"+value).show();
                    if (getUrlParameter('filter_event') === ("event" + value) && target.find("option:selected").hasClass("{{ inner_namespace }}") && !element_selected) {
                        $("#tipo").val($("#tipo option.event"+value).val());
                        element_selected = true
                    }
                });
                //$("#tipo option:first-of-type").show();
                //$("#tipo").val(available_events[0]);
            } else {
                $("#tipo option").show();
            }
        }
    }
    function getUrlParameter(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    };
</script>
{% endif %}
{% if form_proposta == "2" %}
<div class="form_proposa_wrapper voucher">
    <div class="form_header">
        <div class="headerstep step_1 active"><h2>1º {{ T_detalles_voucher|safe }}</h2></div>
        <div class="headerstep step_2"><h2>2º {{ T_info_contacto|safe }}</h2></div>
    </div>
    <form onsubmit="return false" name="contact_voucher" id="contact_voucher" method="post" action="/utils/?action=contact">
        <input type="hidden" name="action" id="action" value="contact"/>
        {% if destination_email %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ destination_email|safe }}">
        {% endif %}

        <div class="step step_1">
            <div class="half_form_big">
                <div class="contInput select i70">
                    <label>{{ T_hotel }}*</label>
                    <select name="hotel" id="hotel">
                            <option value="" disabled {% if not inner_namespace %}selected{% endif %} style="display:none"></option>
                        {% for hotel in selecthoteles_list %}
                            <option value="{{ hotel.name|safe }}" class="{{ hotel.id|safe }}" data-events="{{ hotel.events|safe }}" {% if inner_namespace == hotel.id %}selected{% endif %}>{{ hotel.name|safe }} - {{ hotel.destiny_label|safe }}</option>
                        {% endfor %}
                        <option value="{{ T_todos_hoteles_2 }} Luna Hotels & Resorts">{{ T_todos_hoteles_2 }} Luna Hotels & Resorts</option>
                    </select>
                </div><!--
                 --><div class="contInput i30">
                    <label>{{ T_valor_voucher }}*</label>
                    <select name="voucher_select" id="voucher_select">
                        <option value="50€">50€</option>
                        <option value="100€">100€</option>
                        <option value="250€">250€</option>
                        <option value="{{ T_other }}">{{ T_other }}</option>
                    </select>
                    <input type="text" id="voucher_val" name="voucher_val" style="display: none;">
                </div><!--
                --><div class="contInput i100">
                    <label>{{ T_nombre_remitente }}</label>
                    <input type="text" id="rem_name" name="rem_name">
                </div><!--
                --><div class="contInput i100">
                    <label>{{ T_nombre_destinatario }}</label>
                    <input type="text" id="dest_name" name="dest_name">
                </div>
            </div>
                <div class="half_form_short">
                <div class="contInput i100">
                    <label>{{ T_info_adicional }}</label>
                    <textarea placeholder="{{ T_info_adicional_2 }}" id="extra_info" name="extra_info"></textarea>
                </div>

                <div class="contInput button i100">
                    <button class="next_step btn_black">{{ T_sig_paso }}</button>
                </div>
            </div>
        {% if extra_form_info %}
            <div class="list_wrapper">
                {{ extra_form_info|safe }}
            </div>
        {% endif %}
        </div>
        <div class="step step_2">
            <div class="half_form_big">
                <div class="contInput i60">
                    <label>{{ T_full_nombre }}*</label>
                    <input type="text" id="full_name" name="full_name">
                </div><!--
                --><div class="contInput select i40">
                    <label>{{ T_pre_idioma }}</label>
                    <select name="language" id="language">
                        {% for key, value in language_codes.items() %}
                            <option value="{{ value }}" {% if key == language %}selected{% endif %}>{{ value }}</option>
                       {% endfor %}
                    </select>
                </div><!--
                --><div class="contInput i70">
                    <label>{{ T_email }}*</label>
                    <input type="text" id="email" name="email">
                </div><!--
                --><div class="contInput i30">
                    <label>{{ T_contact_phone }}*</label>
                    <input type="text" id="phone" name="phone">
                </div><!--
                --><div class="contInput i100">
                    <label>{{ T_contact_name }}</label>
                    <input type="text" id="comercial_name" name="comercial_name" placeholder="{{ T_contact_name_ext }}">
                </div><!--
                -->
            </div><!--
            --><div class="half_form_short">
                <div class="contInput i100">
                    <p>{{ T_extra_text }}</p>
                </div>
                <div class="contInput button i100">
                    <button class="prev_step">{{ T_prev_paso }}</button>
                    <button class="btn_black" id="send_proposa">{{ T_enviar }}</button>
                </div>
            </div>
        </div>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.css">

<script type="text/javascript">
    $(window).on("load", function () {
        var d = new Date(),
            day = d.getDate() < 10 ? "0"+d.getDate() : d.getDate(),
            month = d.getMonth()+1 < 10 ? "0"+(d.getMonth()+1) : d.getMonth()+1,
            year = d.getFullYear(),
            startDate = day + "/" + month + "/" + year;
        var endDate = $.datepicker.parseDate("dd/mm/yy", startDate);
            endDate.setDate(endDate.getDate() + 1);
        $('#event_date').daterangepicker({
            "autoApply": true,
            "linkedCalendars": false,
            "endDate": endDate,
            "minDate": startDate,
            "locale": {
                format: 'DD/MM/YYYY'
            }
        }, function (start, end, label) {

        });
        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");
        $("#contact_voucher").validate({
                                   rules: {
                                       hotel: "required",
                                       voucher_select: "required",
                                       voucher_val: {
                                           required: true
                                       },
                                       full_name: "required",
                                       email: {
                                           required: true,
                                           email: true
                                       },
                                       phone: {
                                           required: true,
                                           phone: true
                                       }
                                   },
                                   messages: {
                                       hotel: "{{ T_campo_obligatorio}}",
                                       voucher_select: "{{ T_campo_obligatorio}}",
                                       full_name: "{{ T_campo_obligatorio}}",
                                       email: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           email: "{{ T_campo_valor_invalido|safe }}"
                                       },
                                       phone: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           phone: "{{ T_campo_valor_invalido|safe }}"
                                       }
                                   }

                               });
        $("#send_proposa").click(function () {
            if ($("#contact_voucher").valid()) {
                var data_to_send = {
                    'name': $("#full_name").val() + "\n" + "Idioma preferencial: " + $("#language").val(),
                    'telephone': $("#phone").val() + "\n" + "Nome comercial: " + $("#comercial_name").val(),
                    'email': $("#email").val(),
                    'hotelQuery': $("#hotel").val(),
                    'voucher_select': $("#voucher_select").val(),
                    'voucher_val': $("#voucher_val").val(),
                    'rem_name': $("#rem_name").val(),
                    'dest_name': $("#dest_name").val(),
                    'comments': $("#extra_info").val(),
                    'destination_email': $("#destination_email").val()
                };

                if ($("#form_promotions").is(":checked")) {
                    data_to_send['newsletter'] = true;
                }

                $.post(
                        "/utils/?action=contact", data_to_send,
                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                            $("#full_name").val("");
                            $("#phone").val("");
                            $("#email").val("");
                            $("#hotel").val(0);
                            $("#rem_name").val(0);
                            $("#dest_name").val(0);
                            $("#extra_info").val("");
                            $("#voucher_select").val("");
                            $("#voucher_val").val("");
                            $("#event_pax").val("");
                            $("#event_rooms").val("");
                            $("#comercial_name").val("");
                        }
                );
            }
        });

        if($("#hotel").val() != ""){
            filter_events($("#hotel"));
        }
        $("#hotel").change(function () {
            filter_events($(this));
        });
        $(".next_step").click(function (e) {
            e.preventDefault();
            if ($("#contact_voucher").valid()) {
                $(".step.step_1").slideUp();
                $(".step.step_2").slideDown();
                $(".headerstep").toggleClass("active");
            }
        });
        $(".prev_step").click(function (e) {
            e.preventDefault();
            $(".step.step_1").slideDown();
            $(".step.step_2").slideUp();
            $(".headerstep").toggleClass("active");
        });
        var filter_e = getUrlParameter('filter_event');
        if (filter_e) {
            $("." + filter_e).attr("selected","selected");
        }
        $('#voucher_select').change(function (){
            if ($(this).val() == '{{ T_other }}' ) {
                $('#voucher_val').show();
            } else {
                $('#voucher_val').hide();
            }
        })
    });
    function filter_events(target) {
        if(target.find("option:selected").attr("data-events")){
            var available_events = target.find("option:selected").attr("data-events").split(",");
            var element_selected = false;
            if(available_events.length > 0) {
                $("#tipo option").hide();
                $("#tipo").val("");
                $.each( available_events, function( index, value ){
                    $("#tipo option.event"+value).show();
                    if (getUrlParameter('filter_event') === ("event" + value) && target.find("option:selected").hasClass("{{ inner_namespace }}") && !element_selected) {
                        $("#tipo").val($("#tipo option.event"+value).val());
                        element_selected = true
                    }
                });
                //$("#tipo option:first-of-type").show();
                //$("#tipo").val(available_events[0]);
            } else {
                $("#tipo option").show();
            }
        }
    }
    function getUrlParameter(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    };
</script>
{% endif %}