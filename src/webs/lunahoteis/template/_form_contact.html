<div class="contact_form_wrapper effects_sass" sass_effect="slide_up_effect">
    <div class="container12">
        <h2>{{ T_formulario_contacto }}</h2>

        <form name="contact" id="contact" method="POST" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>

            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="surname" name="surname" class="bordeInput" value=""
                           placeholder="{{ T_apellidos }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-phone" aria-hidden="true"></i>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                           placeholder="{{ T_telefono }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-envelope-o" aria-hidden="true"></i>
                    <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-envelope-o" aria-hidden="true"></i>
                    <input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value=""
                           placeholder="{{ T_confirm_email }}"/>
                </div>
                <div class="contInput area">
                    <i class="fa fa-comment" aria-hidden="true"></i>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""
                              placeholder="{{ T_comentarios }}"></textarea>
                </div>
                {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src="https://www.google.com/recaptcha/api.js" async defer></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
                <div class="contInput policy-terms">
                    <input type="checkbox" id="accept-term" name="accept_term"/>
                    <a class="myFancyPopup fancybox.iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

$(document).ready(function(){

    jQuery.validator.addMethod("phone", function(phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
            phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");


    $("#contact").validate({
       rules: {
           name: "required",
           surname: "required",
           email: {
               required: true,
               email: true
           },
           emailConfirmation: {
               required: true,
               equalTo: "#email",
               email: true
           },
           telephone: {
               required: true,
               phone: true
           },
           comments: "required",
           accept_term: "required"
       },
       messages: {
           name: "{{ T_campo_obligatorio}}",
           surname: "{{ T_campo_obligatorio}}",
           email: {
               required: "{{ T_campo_obligatorio|safe }}",
               email: "{{ T_campo_valor_invalido|safe }}"
           },
           emailConfirmation: {
               required: "{{ T_campo_obligatorio|safe }}",
               email: "{{ T_campo_valor_invalido|safe }}",
               equalTo: "{{T_not_confirmed_email_warning|safe}}"
           },
           telephone: {
               required: "{{ T_campo_obligatorio|safe }}",
               phone: "{{ T_campo_valor_invalido|safe }}"
           },
           comments: "{{ T_campo_obligatorio|safe }}",
           accept_term: "{{ T_campo_obligatorio|safe }}"
       }
    });

    $("#contact-button").click(function(){
        if ( $("#contact").valid() ) {
            if(!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
                $.post(
                    "/utils/?action=contact",
                    {
                        'name': $("#name").val(),
                        'surname': $("#surname").val(),
                        'telephone': $("#telephone").val(),
                        'email': $("#email").val(),
                        'comments': $("#comments").val(),
                        'section': $("#section-name").val(),
                        {% if captcha_box %}'g-recaptcha-response': $("#g-recaptcha-response").val(),{% endif %}
                    },

                    function (data) {
                        alert("{{ T_gracias_contacto }}");
                        $("#name").val("");
                        $("#surname").val("");
                        $("#telephone").val("");
                        $("#email").val("");
                        $("#emailConfirmation").val("");
                        $("#comments").val("");
                        $("#accept-term").prop('checked', false);
                        grecaptcha.reset();
                    }
                );
            }
        }
    })



    //check backgrounds inputs color
    $("form#contact input[type=text], form#contact textarea").each(function(){
        var bck = $(this).css('background');
        var bck_color = $(this).css('background-color');

        if (bck.indexOf("white") != -1 || bck_color.indexOf("white") != -1 || bck_color.indexOf("FFF") != -1 || bck.indexOf("FFF") != -1 || bck_color.indexOf("255, 255, 255") != -1 || bck.indexOf("255, 255, 255") != -1){
            $(this).css('color', 'black');
        }
    });
});
</script>