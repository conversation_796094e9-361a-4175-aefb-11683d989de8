<div class="owlslider owl-carousel main-owlslider">
        {% for picture in pictures %}
            {% if picture.allowInMobile %}
                <div>
                    <img data-src="{{ picture.servingUrl }}" class="owl-lazy">
                    {% if picture.description %}
                        <div class="description_mobile">{{ picture.description|safe }}</div>
                    {% endif %}
                </div>
            {% endif %}
        {% endfor %}
</div>
<div class="scrolldown"><i class="fa fa-angle-double-down"></i></div>
<script>
    $(function () {
        $(".main-owlslider .owl-lazy").each(function () {
            $(this).attr("data-src", $(this).attr("data-src") + "=s" + ($(window).height()));
        });
        var owl_carousel = $(".main-owlslider");
        owl_carousel.owlCarousel({
            loop: true,
            nav: false,
            dots: false,
            items: 1,
            video: true,
            lazyLoad: true,
            margin: 0,
            autoplay: true,
            onInitialized: function() {
                if ($(".owl-item.active video", this.$element).length) {
                  owl_carousel.trigger('stop.owl.autoplay')
                  $(".owl-item.active video", this.$element).on('ended', function() {
                    owl_carousel.trigger('play.owl.autoplay')
                  });
                }
            },
            onTranslated: function() {
                if ($(".owl-item.active video", this.$element).length) {
                  owl_carousel.trigger('stop.owl.autoplay')
                  $(".owl-item.active video", this.$element)[0].play();
                  $(".owl-item.active video", this.$element).on('ended', function() {
                    owl_carousel.trigger('play.owl.autoplay')
                  });
                }
            }
        });
    })
</script>