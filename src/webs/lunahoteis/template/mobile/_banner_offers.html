{% if banner_offers %}
    <div class="banner_offers_wrapper">
        <div class="offers">
            {% for offer in offers_in_banner %}
                <div class="offer">
                    <div class="offer_image">
                        <img data-src="{{ offer.picture|safe }}=s380-c" alt="{{ hotel_name|safe }} - {{ banner_offers.name|safe }}" lazy="true">
                    </div>
                    <div class="offer_label"><span>{{ offer.label|safe }}</span></div>
                    <div class="offer_content">
                        <h2 class="offer_title">{{ offer.name|safe }}</h2>
                        <div class="offer_desc">{{ offer.description|safe }}</div>
                        {% if offer.linkUrl %}
                            <a href="{{ offer.linkUrl|safe }}" class="offer_link">{% if offer.button_text %}{{ offer.button_text|safe }}{% else %}{{ T_leer_mas }}{% endif %}</a>
                        {% else %}
                            <a href="#data" class="offer_link button_promotion">{{ T_reservar }}</a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
        <div class="banner_content">
            <h2 class="banner_title">{{ banner_offers.subtitle|safe }}</h2>
            <div class="banner_desc">{{ banner_offers.content|safe }}</div>
        </div>
    </div>
{% endif %}