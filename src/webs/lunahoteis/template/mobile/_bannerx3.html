{% if bannerx3 %}
    <div class="bannerx3_full_wrapper">
        {% if bannerx3_content.subtitle or bannerx3_content.content %}
            <div class="bannerx3_content">
                {% if bannerx3_content.subtitle %}<h3>{{ bannerx3_content.subtitle|safe }}</h3>{% endif %}
                {% if bannerx3_content.content %}<span>{{ bannerx3_content.content|safe }}</span>{% endif %}
            </div>
        {% endif %}
        <div class="bannerx3_wrapper owl-carousel">
            {% for banner in bannerx3 %}
                <div class="banner">
                    <span class="info_link"></span>
                    <div class="banner_image">
                        <img data-src="{{ banner.servingUrl|safe }}=s800"
                             alt="{{ hotel_name|safe }} - {{ banner.title|safe }}" lazy="true">
                        {% if banner.title %}<div class="image_title">{{ banner.title|safe }}</div>{% endif %}
                    </div>
                    <div class="banner_content">
                        <div class="banner_title">{{ banner.title|safe }}</div>
                        <div class="banner_desc">{{ banner.description|safe }}</div>
                        {% if banner.linkUrl %}
                            <div class="banner_link">
                                <a href="{{ banner.linkUrl|safe }}" class="read_more">{{ T_leer_mas }}</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
<script>
    $(window).on("load", function () {

        $(".bannerx3_wrapper.owl-carousel").owlCarousel({
            loop: true,
            nav: false,
            dots: true,
            items: 1,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            autoplay: true
        });

        $(".info_link").click(function () {
            $(".bannerx3_wrapper .banner").toggleClass("showed");
        })
    });
</script>