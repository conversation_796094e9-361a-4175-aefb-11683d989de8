<div class="offer_wrapper">
    <div class="offer_header">
        <div class="left_block">
            {% if offer.price %}
                <div class="price">{{ offer.price|safe }}</div>
            {% endif %}
            <h1>{{ offer.subtitle|safe }}</h1>
            {% if offer_subtitle %}
            {{ offer_subtitle|safe }}
            {% endif %}
        </div>
        <div class="offer_image">
            {% if offer.pictures.0 %}
                <img src="{{ offer.pictures.0 }}=s1900" alt="{{ hotel_name|safe }} - {{ offer.subtitle|safe }}">
            {% endif %}
        </div>
    </div>
    <div class="offer_content">
        {{ offer.content|safe }}

        {% if offer.external_link %}
            <div class="external_link_wrapper">
                <a href="{{ offer.external_link|safe }}" {% if "http" in offer.external_link %}target="_blank" {% endif %} class="btn_external_link">
                    {% if offer.external_link_text %}
                        {{ offer.external_link_text|safe }}
                    {% else %}
                        {{ T_ver_mas }}
                    {% endif %}
                </a>
            </div>

            <script async>
                $(function () {
                    var external_link = $(".external_link_wrapper a"),
                        external_link_width = external_link.outerWidth(),
                        booking_button = $(".offer_extra .button_promotion"),
                        booking_button_width = booking_button.outerWidth();

                    if (external_link_width > booking_button_width) {
                        booking_button.css('width', external_link_width);
                    } else {
                        external_link.css('width', booking_button_width);
                    }
                });
            </script>
        {% endif %}

        <div class="offer_extra">
            {% if offer.mail_button %}
                <a href="mailto:{{ offer.mail_button|safe }}" class="offer_link contact_link">{{ T_contactenos }}</a>
            {% endif %}

            {% if offer.newsletter_promotion %}
                <a href="#data" class="offer_link newsletter_promotion">{{ T_suscribete }}</a>
            {% endif %}

            {% if not offer.mail_button and not offer.newsletter_promotion %}
                <a href="#data" class="offer_link button_promotion">{{ T_reservar }}</a>
            {% endif %}
        </div>
    </div>
</div>
{% if hotel_list_offer %}
    <h2 class="hotel_list_title">{% if is_exp %}{{ T_hoteles_experiencia }}{% else %}{{ T_hoteles_ofertas }}{% endif %}</h2>
    <div class="hotels_list_wrapper">
        {% for x in hotel_list_offer %}
            <div class="hotel">
                <div class="hotel_image">
                    <img src="{{ x.image|safe }}=s275-c" alt="{{ x.value|safe }}">
                    <a href="{% if not "pt" in language %}/{{ language }}{% endif %}/{{ x.link|safe }}"><span>{{ T_ver_hotel }}</span></a>
                </div>
                <div class="hotel_info">
                    <h2>{{ x.destiny_label|safe }}, {{ x.country_label|safe }}, {{ x.group_label|safe }}</h2>
                    <h3 class="{% if not x.starts %}no_stars{% endif %}">
                        {% if x.starts %}<span class="rank">{% for start in range(x.starts) %}<i class="fa fa-star"></i>{% endfor %}</span>{% endif %}
                        {{ x.name|safe }}
                    </h3>
                    <p>{{ x.description|safe }}</p>
                    <a href="javaScript:booking_click('{{x.id|safe}}')" class="center_x" style="left: 50%;width: max-content;">{{ T_reservar }}</a>
                </div>
            </div>
        {% endfor %}
    </div>
    <script>
        var hotel_max_height = 0;
        var hotel_block_max_height = 0;
        $(".hotel").each(function () {
            hotel_max_height = Math.max(hotel_max_height, $(this).height());
            hotel_block_max_height = Math.max(hotel_block_max_height, $(this).find(".hotel_info").outerHeight());
        }).height(hotel_max_height);

        $(".hotel .hotel_info").outerHeight(hotel_block_max_height);

        var height_title = $(".left_block h1").height(),
            height_block = $(".left_block").height()/2;

        $(".subtitle_offer_wrapper.in_title").css({top: height_title + 40 + height_block});

    </script>
{% endif %}