.banner_exp3_wrapper {
    display:table;
    padding-top: 60px;
    width:100%;
    .text_wrapper {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 30px;
      width: 300px;
      float: left;
      .title {
        color: $corporate_2;
        letter-spacing: 1px;
        line-height: 24px;
        font-size: 20px;
        margin-bottom: 20px;
      }
      .description {
        padding-bottom: 20px;
        font-weight: 300;
        font-size: 12px;
        line-height: 20px;
      }
    }
    .images_wrapper {
      display: inline-block;
      vertical-align: middle;
      width: 680px;
      text-align: right;
      float: right;
      video {
        background-color: $corporate_2;
        display: inline-block;
        vertical-align: top;
        width: 495px;
        height: 365px;
        float: left;
      }
      a {
        background-color: $corporate_2;
      }
      a:nth-child(1) {
        position:relative;
        display: inline-block;
        vertical-align: top;
        margin-bottom: 5px;
        margin-right: 5px;
        width: 495px;
        height: 365px;
        overflow:hidden;
        float: left;
        img {
          @include center_xy;
          max-width: none;
          min-height: 100%;
        }
      }
      a:nth-child(2),
      a:nth-child(3) {
        position:relative;
        display: inline-block;
        vertical-align: top;
        margin-bottom: 5px;
        width: 180px;
        height: 180px;
        overflow:hidden;
        float: right;
        img {
          @include center_xy;
          max-width: none;
          min-height: 100%;
        }
      }
      a:nth-child(4) {
        display: block;
        background-color: transparent;
        text-decoration: underline;
        color: $corporate_2;
        font-weight: 300;
        font-size: 12px;
        line-height: 20px;
        margin: 0 auto 10px;
        clear: both;
      }
    }
    .extra_links {
      display: inline-block;
      width: 330px;
    }
    .extra_link {
      display: inline-block;
      border: 1px solid black;
      letter-spacing: 1px;
      padding: 10px;
      font-size: 12px;
      color: $corporate_1;
      margin: 10px 10px 30px;
      cursor: pointer;
      @include transition(all, .6s);
      &:hover {
        border-color: $corporate_1;
        background-color: $corporate_1;
        color: white;
      }
      &.btn_black {
        margin: 10px 0 30px;
        text-transform: uppercase;
        background-color: $corporate_2;
        color: white;
        &:hover {
          border-color: $corporate_1;
          background-color: $corporate_1;
          color: white;
        }
      }
    }
  }