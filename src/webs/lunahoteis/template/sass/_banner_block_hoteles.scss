.banner_block_hoteles_wrapper {
  margin: 30px auto 100px;
  h2 {
    font-size: 20px;
    margin-bottom: 30px;
    box-sizing: border-box;
    text-transform: uppercase;
    width: 100%;
    text-align: center;
    padding: 0;
    span span {
      display: block;
      text-transform: none;
      font-size: 15px;
      opacity: .8;
    }
  }
  .banner_block_hoteles_content {
    display: inline-block;
    font-size: 12px;
    opacity: .8;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 0 60px;
    margin-bottom: 80px;
  }
  .block {
    position: relative;
    background-color: #EFEFEF;
    margin: 20px auto;
    .block_gallery {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 500px;
      height: 300px;
      overflow: hidden;
      .block_carousel {
        @include center_x;
        top: 30px;
        width: 360px;
        bottom: 30px;
        .owl-item {
          height: 240px;
          overflow: hidden;

          img {
            @include center_image;
            width: auto;
          }
        }

        .expand {
          position: absolute;
          top: 0;
          right: 0;
          width: 30px;
          height: 30px;
          box-sizing: border-box;
          overflow: hidden;
          background-color: rgba(50, 50, 50, .8);
          i.fa {
            @include center_xy;
            color: white;
          }
        }
        .owl-prev, .owl-next {
          font-size: 50px;
          color: $corporate_2;
          width: 21px;
          height: 21px;
          @include center_y;
        }
        .owl-prev {
          left: -30px;
          border-left: 1px solid #333;
          border-bottom: 1px solid #333;
          -webkit-transform: rotate(45deg) translate(0, -50%);
          -moz-transform: rotate(45deg) translate(0, -50%);
          -ms-transform: rotate(45deg) translate(0, -50%);
          -o-transform: rotate(45deg) translate(0, -50%);
          transform: rotate(45deg) translate(0, -50%);
        }
        .owl-next {
          right: -30px;
          border-right: 1px solid #333;
          border-bottom: 1px solid #333;
          -webkit-transform: rotate(-45deg) translate(0, -50%);
          -moz-transform: rotate(-45deg) translate(0, -50%);
          -ms-transform: rotate(-45deg) translate(0, -50%);
          -o-transform: rotate(-45deg) translate(0, -50%);
          transform: rotate(-45deg) translate(0, -50%);
        }
      }
    }
    .block_content {
      display: inline-block;
      vertical-align: top;
      padding: 30px 0;
      width: 630px;
      .block_title {
        color: $corporate_2;
        font-size: 16px;
        font-weight: 400;
        span {
          display: block;
          font-weight: 100;
          font-size: 80%;
        }
        &:after {
          content: '';
          display: block;
          height: 2px;
          width: 30px;
          background-color: $corporate_2;
          margin: 10px 0 20px;
        }
      }
      .block_desc {
        color: $corporate_2;
        font-size: 12px;
        line-height: 18px;
        font-weight: lighter;
        width: 400px;
        a {
          text-decoration: underline;
          color: #333;
        }
        @include transition(all, .6s);
        ul {
          padding-left: 20px;
          list-style-type: disc;
        }
        .hide_toggle {
          cursor: pointer;
          padding-top: 5px;
        }
        table {
          width: 100%;
          tr {
            th, td {
              text-align: center;
              padding: 10px 10px 0;
            }
            &:first-of-type {
              border-bottom: 1px solid black;
            }
          }
          th {
            font-size: 14px;
            color: $corporate_1;
            img {
              max-height: 40px;
            }
            span {
              font-size: 80%;
            }
          }
          td {
            font-size: 14px;
          }
        }
      }
      .block_links {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 200px;
        text-align: right;
        a {
          display: block;
          text-align: center;
          border: 1px solid #4B4B4B;
          color: #4B4B4B;
          padding: 5px 0;
          letter-spacing: 1px;
          font-size: 12px;
          margin: 5px 0 0 0;
          background-color: white;
          @include transition(all, .6s);
          &.btn_black {
            background-color: #4B4B4B;
            color: white;
          }
          &:hover {
            background-color: $corporate_1;
            color: white;
          }
        }
      }
      .read_more {
        display: block;
        color: $corporate_1;
        font-size: 12px;
        line-height: 18px;
        font-weight: 300;
        text-decoration: underline;

        &.read_less {
          color: $corporate_2;
          font-size: 30px;
          text-decoration: none;
        }
      }
    }
  }
}