header {
    background-color: white;
    border-bottom: 1px solid $corporate_2;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 1001;
    
    &.luna-serra, &.luna-abrantes, &.luna-carqueijais, &.luna-arcos {
        nav#main_menu #main-sections-inner .main-section-div-wrapper a {
            padding: 13px 13px 12px;
        }
    }
    
    .central_booking_wrapper {
        display: inline-block;
        color: #787878;
        text-align: center;
        font-size: 12px;
        vertical-align: top;
        margin-top: 10px;
        text-transform: uppercase;
        margin-left: 20px;
        padding: 5px 0 10px 20px;
        border-left: 1px solid #4b4b4b;
        
        span {
            display: block;
            font-size: 14px;
            font-weight: bold;
        }
    }
    
    #wrapper-header {
        height: 80px;

        #logoDiv {
            img.logo {
                height: 40px;
                //width: 88px; //Requested set specific width by Carlos
                
                margin-top: 10px;
            }
            
            .inner_logo {
                display: inline-block;
                margin-left: 20px;
                padding-left: 20px;
                border-left: 1px solid #4b4b4b;
                margin-top: 10px;
                cursor: pointer;
                
                img {
                    margin-top: 0;
                    height: 35px;
                    padding: 5px 0 0;
                }
            }
        }
        
        .right_header {
            font-size: 12px;
            text-align: right;
            
            #top-sections, #lang {
                display: inline-block;
                vertical-align: middle;
            }
            
            #top-sections {
                margin-top: 30px;
                
                a {
                    display: inline-block;
                    text-transform: uppercase;
                    padding: 0 10px;
                    color: $gray-2;
                    border-right: 1px solid $gray-2;
                    @include transition(color, .4s);
                    
                    &:first-of-type {
                        border-left: 1px solid $gray-2;
                    }
                    
                    &:hover {
                        color: black;
                    }
                }
            }
            
            #lang {
                position: relative;
                margin-top: 30px;
                padding: 0 10px;
                display: inline-block;
                
                #selected-language {
                    color: $gray-2;
                    
                    i.fa {
                        margin: 0 0 0 5px;
                    }
                }
                
                #language-selector-options {
                    display: none;
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background-color: $corporate_2;
                    
                    a {
                        display: block;
                        color: white;
                        text-align: center;
                        padding: 5px;
                    }
                }
                
                &:hover {
                    background-color: $corporate_2;
                    color: white;
                    
                    #language-selector-options {
                        display: block;
                        
                        a:hover {
                            background-color: $corporate_1;
                        }
                    }
                }
            }
        }
        
    }
    
    nav#main_menu {
        margin: 10px 10px 0;
        text-align: center;
        border-top: 1px solid $corporate_2;
        
        #main-sections-inner {
            .main-section-div-wrapper {
                display: inline-block;
                margin: 0;
                
                a {
                    display: block;
                    color: $corporate_2;
                    letter-spacing: 1px;
                    line-height: 24px;
                    font-size: 18px;
                    padding: 13px 10px 12px;
                    @include transition(all, .4s);
                    
                    span span {
                        font-size: 70%;
                        line-height: 90%;
                        letter-spacing: 0;
                        display: block;
                    }
                }
                
                &:hover, &#section-active {
                    a {
                        background-color: $corporate_2;
                        color: white;
                    }
                }
                
                &.replace_link {
                    a {
                        color: $corporate_1;
                    }
                    
                    &:hover, &#section-active {
                        background-color: $corporate_1;
                        color: white;
                        
                        a {
                            background-color: $corporate_1;
                            color: white;
                        }
                    }
                }
            }
        }
    }
}

#slider_container {
    position: relative;
    
    &:not(.slider_container_inner) {
        height: 70vh;
        
        .forcefullwidth_wrapper_tp_banner {
            height: 100% !important;
            overflow: hidden !important;
            
            .tp-banner-container {
                height: 70vh !important;
            }
        }
    }
    
    &.slider_container_inner {
        height: 77px;
    }
    
    .tp-simpleresponsive .button {
        background: none;
    }
    
    .tparrows {
        background: $corporate_2;
        -webkit-transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        -o-transform: rotate(45deg);
        transform: rotate(45deg);
        
        .tp-arr-allwrapper:after {
            font-family: "fontawesome", sans-serif;
            font-size: 20px;
            color: white;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%) rotate(-45deg);
            -moz-transform: translate(-50%, -50%) rotate(-45deg);
            -ms-transform: translate(-50%, -50%) rotate(-45deg);
            -o-transform: translate(-50%, -50%) rotate(-45deg);
            transform: translate(-50%, -50%) rotate(-45deg);
        }
        
        &.tp-leftarrow {
            .tp-arr-allwrapper:after {
                content: '\f104';
            }
            
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: -50px;
                height: 1px;
                background-color: $corporate_2;
            }
            
            &:after {
                content: '';
                position: absolute;
                top: -50px;
                right: 0;
                bottom: 0;
                width: 1px;
                background-color: $corporate_2;
            }
        }
        
        &.tp-rightarrow {
            .tp-arr-allwrapper:after {
                content: '\f105';
            }
            
            &:before {
                content: '';
                position: absolute;
                bottom: 0;
                right: 0;
                left: -50px;
                height: 1px;
                background-color: $corporate_2;
            }
            
            &:after {
                content: '';
                position: absolute;
                bottom: -50px;
                left: 0;
                top: 0;
                width: 1px;
                background-color: $corporate_2;
            }
        }
    }
    
    .tp-bullets {
        display: none;
    }
}