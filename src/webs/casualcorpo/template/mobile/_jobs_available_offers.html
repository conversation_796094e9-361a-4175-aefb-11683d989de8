{% if not hide_form %}
    <div id="filter_wrapper_banner">
        <div id="close_filters">
            <i class="fal fa-times"></i>
        </div>

        <div class="banner_title">
            <i class="fas fa-sliders-h"></i>
            {{ T_filtrar }}
        </div>

        <div class="availabler_filters_wrapper">

            {% if job_destinies %}
                <div class="destiny_filters_wrapper filter_block">
                    <div class="filter_title">
                        {{ T_filtar_destino }}
                        <i class="fas more fa-plus-circle"></i>
                        <i class="fal less fa-minus-circle"></i>
                    </div>
                    <div class="options_list">
                        {% for destiny in job_destinies %}
                            <div class='option_element'>
                                <input type='checkbox' id="destiny_filter_{{ loop.index }}" name='destinies_filter' value="{{ destiny.destiny_class_name|safe }}">
                                <label for="destiny_filter_{{ loop.index }}">{{ destiny.title|safe }} ({{ destiny.jobs_number }} {% if destiny.jobs_number == 1 %}{{ T_oferta }}{% else %}{{ T_ofertas }}{% endif %})</label>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <div class="laboral_offer_type filter_block">
                <div class="filter_title">
                    {{ T_oferta_laboral }}
                    <i class="fas more fa-plus-circle"></i>
                    <i class="fal less fa-minus-circle"></i>
                </div>
                <div class="options_list"></div>
            </div>
        </div>

        <div class="filters_buttons_wrapper">
            <div id="clear_filters_button">{{ T_borrar_filtros }}</div>
            <div id="apply_filters_button">{{ T_aplicar_filtros }}</div>
        </div>
    </div>

    <div class="job_offers_found">
        <span class="jobs_number"></span> {{ T_trabajos_encontrados }}
    </div>

    <div class="work_wrapper">
        <div class="filters_button work_filters_button">
            <i class="fas fa-sliders-h"></i>
            {{ T_filtrar }}
        </div>

        <div class="available_offers_wrapper">
            {% for job_element in job_promotions %}
                <div class="job_element" {% if job_element.destiny_info and job_element.destiny_info[0].title %}data-destiny="{{ job_element.destiny_info[0].title|safe }}"{% endif %}
                     {% if job_element.namespace and job_element.namespace[0] %}data-namespace="{{ job_element.namespace[0]|safe }}"{% endif %}
                     type="{{ job_element.type|safe }}"
                     {% if job_element.destiny_info and job_element.destiny_info.0.destiny_class_name %}destiny="{{ job_element.destiny_info.0.destiny_class_name|safe }}"{% endif %}>
                    <div class="job_header">
                        <h4 class="destiny_title">
                            {% for destiny in job_element.destiny_info %}
                                <span>
                                    {{ destiny.title|safe }}
                                </span>
                            {% endfor %}
                        </h4>
                        <h3 class="job_title">{{ job_element.title|safe }}</h3>
                        <div class="hotels_wrapper">
                            {% for destiny_element in job_element.destiny_info %}
                                {% for hotel_title in job_element.hotels_available %}
                                    <span class="hotel_name">
                                        {{ hotel_title|safe }}
                                    </span>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    </div>
                    <div class="job_description">{{ job_element.description|safe }}</div>
                    <div class="read_more">
                        <p class="more">{{ T_leer_mas }}</p>
                        <p class="less">{{ T_leer_menos }}</p>
                    </div>
                    <button class="subscribe_job"
                            {% if job_element.job_classname %}data-job_name="{{ job_element.job_classname|safe }}"{% endif %}>{{ T_inscribirme }}</button>
                    {% if job_element.popup_info %}
                        <div class="see_more_job_description">{{ T_ver_mas }}</div>
                    {% endif %}

                    <div class="more_info_job_wrapper">
                        {{ job_element.popup_info|safe }}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

{% endif %}

<script type="text/javascript" src="/js/{{ base_web }}/mobile/job_application.js?v=1.04"></script>