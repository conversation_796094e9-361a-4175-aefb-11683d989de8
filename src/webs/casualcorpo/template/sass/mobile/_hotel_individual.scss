.hotel_content_wrapper {
  @include base_mobile_styles;
  padding: 30px;

  .content_title {
    width: 100%;
    padding-bottom: 20px;

    .hotel_subtitle {
      @include title_styles;
      font-size: 35px;
      line-height: 42px;
      font-weight: 700;
      padding-bottom: 0;

      span {
        color: $color_text;
      }

      span[font-family] {
        font-family: $text_family !important;
      }
    }

    .destiny {
      display: block;
      font-weight: 600;
      color: $color_text;
      font-size: 17px;
      line-height: 25px;
      text-align: left;

      i {
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }

  .hotel_sections_links {
    display: none;
    /*@include display_flex;
    width: 100%;
    background-color: white;
    padding-bottom: 20px;
    @include transition(all, .6s);

    .link {
      position: relative;
      width: 50%;
      font-size: 17px;
      line-height: 26px;
      font-weight: 600;
      text-transform: capitalize;
      border-bottom: 1px solid $grey;
      text-align: center;
      padding: 15px;
      cursor: pointer;
      @include transition(all, .6s);

      &:before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: -2px;
        height: 5px;
        max-height: 0;
        overflow: hidden;
        visibility: hidden;
        background-color: $corporate_2;
        @include transition(all, .6s);
      }

      &.active {
        color: $corporate_2;
        font-weight: 700;

        &:before {
          max-height: 5px;
          visibility: visible;
        }
      }
    }*/
  }

  .hotel_content {
    .hotel_description {
      @include text_styles;
      padding-bottom: 20px;

      hide {
        display: none;
      }

      .open_hotel_description_modal {
        text-transform: uppercase;
        font-weight: 600;

        &::after {
          display: inline-flex;
          vertical-align: middle;
          font-family: "Font Awesome 5 Pro";
          content: '\f054';
          margin-left: 7px;
        }
      }
    }

    .hotel_info {
      display: block;
      font-weight: 600;
      font-size: 14px;
      color: $color_text;
      line-height: 23px;
      text-align: left;

      i {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
        color: $corporate_2;
      }

      .see_map {
        display: block;
        text-transform: uppercase;
        color: $corporate_2;
        cursor: pointer;
        margin-bottom: 20px;
        padding-left: 20px;
      }

      &.extra_link {
        color: $corporate_2;
        text-decoration: underline;
      }
    }

    .logo_kayak {
      display: block;
      text-align: left;

      img {
        height: 70px;
        margin-top: 20px;
      }
    }
  }

  .hotel_main_img {
    position: relative;
    display: block;
    width: 100%;
    margin-top: 40px;
    z-index: 1;

    &:before {
      content: '';
      position: absolute;
      z-index: -1;
      height: 200px;
      left: -40px;
      right: -40px;
      bottom: -40px;
      background-color: $lightgrey;
    }

    .img_wrapper {
      position: relative;
      display: block;
      width: 100%;
      height: 300px;
      overflow: hidden;

      img {
        @include cover_image;
      }
    }
  }
}