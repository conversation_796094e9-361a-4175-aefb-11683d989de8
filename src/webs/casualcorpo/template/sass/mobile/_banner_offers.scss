.banner_offers_full_wrapper {
  @include base_mobile_styles;
  margin: 30px 0;
  z-index: 1;

  .bg_img_wrapper {
    position: absolute;
    z-index: -1;
    left: 0;
    right: 0;
    top: 0;
    height: 65%;
    width: 100%;
    overflow: hidden;

    &:before {
      content: "";
      @include full_size;
      z-index: 5;
      background: rgba($black, 0.6);
    }

    img {
      @include cover_image;
      width: 500%; // Dirty fix to show only 1 of 5 pictures
    }
  }

  .banner_offers_content {
    padding: 20px 20px 40px;
    text-align: left;

    .content_title {
      @include title_styles_2;
      padding-bottom: 20px;
      font-size: 45px;
      line-height: 48px;

      small {
        font-size: 20px;
      }
    }

    .content_text {
      @include text_styles($color: white);
      padding-bottom: 20px;
    }

    .btn_personalized_1, .btn_personalized_2 {
      margin: 15px 15px 0 0;
    }
  }

  .banner_offers_wrapper {
    .hotels_group_wrapper {
      padding: 0 20px;

      .offer {
        @include full_size;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-end;
        position: relative;
        height: 300px;
        padding: 20px;
        @include transition(all, .6s);

        &:before {
          content: '';
          @include full_size;
          z-index: 2;
          background: linear-gradient(to top, rgba($black, .8) 5%, rgba($black, .6) 10%, rgba($black, .5) 20%, rgba($black, 0) 100%);
        }

        img {
          @include center-image;
          object-fit: cover;
          z-index: 1;
        }

        .offer_content {
          z-index: 2;
          padding: 0;
          text-align: left;

          .title {
            position: relative;
            z-index: 5;
            display: block;
            width: 100%;
            color: white;
            font-size: 22px;
            line-height: 28px;
            font-weight: 700;
            font-family: $title_family;
            padding-bottom: 10px;
          }

          .text {
            position: relative;
            z-index: 5;
            display: block;
            width: 100%;
            color: white;
            font-size: 16px;
            line-height: 19px;
            font-weight: 400;
          }
        }

        .hidden_content {
          z-index: 2;

          .btn_personalized_1, .btn_personalized_2 {
            margin: 15px 15px 0 0;
          }
        }
      }
    }

    .owl-nav {
      @include center_y;
      z-index: 99;
      left: 0;
      right: 0;
      height: 0;

      .owl-prev, .owl-next {
        @include center_y;
        @include owl_nav_styles;
        left: -10px;

        i {
          color: $corporate_2;
          font-size: 28px;
        }
      }

      .owl-next {
        left: auto;
        right: -10px;
      }
    }
  }
}