.banner_next_hotels_wrapper {
  margin: 70px 0;

  .container12 {
    padding: 30px;

    .banner {
      .card {
        position: relative;
        width: 100%;

        .label_closed {
          position: absolute;
          top: 20px;
          left: 0;
          background-color: $corporate_4;
          font-size: 12px;
          font-weight: 400;
          text-transform: uppercase;
          color: $black;
          line-height: 19px;
          padding: 0 0 0 10px;
          height: 20px;
          z-index: 10;
          opacity: 1;
          transition: all .4s;

          &::before {
            position: absolute;
            content: '';
            top: 0;
            bottom: 0;
            right: 0;
            width: 30px;
            background-color: $corporate_4;
            transform: skew(-10deg) translateX(10px);
            z-index: -1;
          }
        }

        .picture_wrapper {
          position: relative;
          height: 360px;

          img {
            @include cover_image;
          }
        }

        .content_wrapper {
          @include full_size;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          z-index: 2;
          text-align: center;
          padding: 30px;

          .content_title {
            margin-bottom: 15px;
            transition: all .4s;

            .title {
              font-family: $title_family;
              font-size: 24px;
              font-weight: 800;
              line-height: 30px;
              color: white;
              display: block;
              margin: 0;

              span,
              .subtitle {
                font-weight: 300;
                display: block;
              }
            }
          }

          .desc {
            @include text_styles(15px, white);
            text-align: center;
          }

          .btn_personalized_2 {
            margin-top: 15px;
            padding: 10px 40px;
            opacity: 1;
          }
        }

        &::before {
          position: absolute;
          content: '';
          top: calc(100% - 200px);
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(0deg, rgba(0, 0, 0, 0.4738270308123249) 0%, rgba(0, 0, 0, 0) 100%);
          z-index: 1;
          transition: all .6s;
        }

        &::after {
          position: absolute;
          content: '';
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background: black;
          opacity: 0.3;
          z-index: 1;
          transition: all .6s;
        }


        &:nth-child(3n),
        &:last-child {
          margin-right: 0;
        }
      }

      .owl-nav {
        @include center_y;
        z-index: 99;
        left: 0;
        right: 0;
        height: 0;

        .owl-prev, .owl-next {
          @include center_y;
          @include owl_nav_styles;
          left: 10px;

          i {
            font-size: 28px;
          }
        }

        .owl-next {
          left: auto;
          right: 10px;
        }
      }
    }
  }
}