.contact_form_wrapper {
  position: relative;
  display: block;
  width: 100%;
  background: $lightgrey;
  overflow: hidden;
  padding-bottom: 20px;
  margin-bottom: -20px;
  margin-top: 30px;

  h3 {
    @include title_styles;
    padding: 30px;
    font-size: 35px;
    line-height: 43px;
    text-align: left;
    font-weight: bold;
  }
}

#contact {
  display: block;
  margin: 0 auto;

  .top_form {
    width: 100%;
    border-radius: 5px;
    font-size: 14px;
    padding: 0;
    color: #4B4B4B;

    .event_date_container {
      .event_date {
        padding-left: 30px;
      }

      span {
        font-family: $text_family;
        font-size: 12px;
        position: absolute;
        top: 0;
        right: 23%;
        transform: translateX(50%);

      }
    }

    .top_checkbox_wrapper {
      padding: 0;
    }

    .selector_hotel, .selector_city, .selector_job, .extra_select_wrapper .extra_select {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      padding: 0;
      margin: 0 auto 20px auto;
      width: 100%;
      background-color: white;

      &:before {
        content: '\f107';
        font-family: "Fontawesome", sans-serif;
        color: $corporate_1;
        @include center_y;
        right: 10px;
        z-index: 1;
      }

      select {
        position: relative;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        border: 1px solid transparent;
        border-radius: 0;
        box-shadow: 0 0 0 rgba(0, 0, 0, 0);
        padding: 7px 7px 7px 40px;
        margin: 0;
        width: 100%;
        font-family: $text_family;
        font-size: 15px;
        height: 50px;
      }
    }

    .extra_select_wrapper {
      display: block;
      text-align: center;

      label {
        display: inline-block;
        color: #111;
        padding-bottom: 15px;
      }
    }

    span {
      display: inline-block;
      vertical-align: middle;
      padding: 17px 10px 17px 0;
      color: #111;
    }

    input[type=checkbox] {
      display: inline-block;
      vertical-align: middle;
      width: auto;
      height: auto;
    }
  }

  label {
    padding: 15px 0 0;
    display: block;
    font-size: 14px;
    color: lighten($corporate_2, 30%);

    &.error {
      position: absolute;
      bottom: -20px;
      padding: 5px 10px;
      color: #943E46;
      background-color: #f8d7da;
      border-color: #f5c6cb;
      border-radius: 5px;
      z-index: 2;
    }

    &[for=promotions] {
      display: inline;
      vertical-align: middle;
      padding: 0;
      font-size: 12px;
      color: #111;
    }
  }


  .contInput {
    display: block;
    width: 100%;
    padding: 10px 0;
    position: relative;

    &:last-of-type {
      padding: 0;
      margin-bottom: 15px;
    }

    input[type=file] {
      /*position: absolute;
      left: 20px;
      right: 20px;
      top: 5px;
      padding: 10px;
      background: white;*/
    }

    .fa {
      top: 5px;
    }

    &.cv_field {
      width: 100%;
      height: 30px;
      overflow: hidden;
      margin: 20px 0;
      background-color: transparent;

      i {
        position: relative;
        z-index: 1;
      }
    }

    .fa {
      width: 40px;
      height: 40px;
      color: $corporate_1;
      position: absolute;
      top: 15px;

      &.fa-file-text-o {
        @include center_y;
      }

      &:before {
        @include center_xy;
      }
    }

    input#uploadFile {
      width: 100%;
      background: none;
      z-index: 1;
      position: relative;
      font-size: 13px;
    }

    #file_cv {
      background-color: transparent;
      position: absolute;
      inset: 0;
      z-index: 2;
      opacity: 0;
    }

    input {
      width: 100%;
      height: 50px;
      padding: 5px 5px 5px 40px;
      border: 0;
      background: #f3f3f3;
      margin-top: 0;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px;

      &#accept-term, &#promotions {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }

      &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &::-moz-placeholder { /* Firefox 19+ */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &:-ms-input-placeholder { /* IE 10+ */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &:-moz-placeholder { /* Firefox 18- */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }
    }

    textarea {
      width: 100%;
      padding-left: 40px;
      padding-top: 20px;
      border-color: transparent;
      resize: none;
      background: white;
      margin-top: 0;

      &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &::-moz-placeholder { /* Firefox 19+ */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &:-ms-input-placeholder { /* IE 10+ */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }

      &:-moz-placeholder { /* Firefox 18- */
        font-family: $text_family;
        color: $black;
        font-weight: 400;
      }
    }

    &.sup_checkboxes {
      vertical-align: middle;
      padding-top: 0;
    }
  }

  .g-recaptcha.error {
      outline: 1px solid red;
    }

  .field_contain_text, .check_element {
    position: relative;
  }

  .policy-terms {
    text-align: left;
    padding: 10px;
  }

  a.myFancyPopup {
    display: inline-block;
    vertical-align: middle;
    color: #111;
    font-size: 12px;
  }

  #contact-button {
    width: 100%;
    height: 50px;
    background: $corporate_1;
    color: white;
    text-transform: uppercase;
    font-size: 16px;
    font-family: $text_family;
    font-weight: 600;
    margin-bottom: 10px;
    cursor: pointer;
    display: flex !important;
    justify-content: center;
    align-items: center;
    padding: 0 !important;

    &:hover {
      background-color: darken($corporate_1, 10%);
    }
  }

  .privacy_policy_form {
    width: calc(100% - 40px);
    margin: 10px auto 0;
    font-family: $text_family;
    font-size: 12px;

    a {
      color: $corporate_1;
    }
  }

  .g-recaptcha {
    display: table;
    margin: 0 auto;
  }
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-family: $text_family;
  color: $black;
}

::-moz-placeholder { /* Firefox 19+ */
  font-family: $text_family;
  color: $black;
}

:-ms-input-placeholder { /* IE 10+ */
  font-family: $text_family;
  color: $black;
}

:-moz-placeholder { /* Firefox 18- */
  font-family: $text_family;
  color: $black;
}