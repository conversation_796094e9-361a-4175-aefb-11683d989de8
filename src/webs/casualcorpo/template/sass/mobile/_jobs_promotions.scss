#filter_wrapper_banner {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  bottom: -100%;
  background: white;
  z-index: 101;
  padding: 20px;
  transition: all 0.5s;
  text-align: left;

  &.active {
    top: 0;
    bottom: 0;
  }

  #close_filters {
    position: absolute;
    right: 25px;
    top: 8px;
    font-size: 29px;
    color: $corporate_2;
    z-index: 1;
  }

  .banner_title {
    text-transform: uppercase;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #424242;

    i {
      margin-right: 10px;
      font-size: 23px;
      font-weight: 100;
      color: $corporate_2;
      vertical-align: middle;
    }
  }

  .availabler_filters_wrapper {
    border: 1px solid #cacaca;
    max-height: 80vh;
    overflow: auto;

    .filter_title {
      border-bottom: 1px solid #cacaca;
      padding: 15px;
      text-transform: uppercase;
      color: $gray-1;
      font-size: 14px;
      position: relative;

      .more, .less {
        position: absolute;
        right: 10px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        color: $corporate_2;
        -webkit-transition: all 0.5s;
        -moz-transition: all 0.5s;
        -ms-transition: all 0.5s;
        -o-transition: all 0.5s;
        transition: all 0.5s;
      }

      .less {
        opacity: 0;
      }
    }

    .destiny_filters_wrapper {
      .filter_title {
        border-top: 0;
      }
    }

    .offers_filters_wrapper {
      .filter_title {
        border-bottom: 0;
      }
    }

    .options_list {
      padding: 0 15px;
      max-height: 0;
      overflow: hidden;
      -webkit-transition: all 0.5s;
      -moz-transition: all 0.5s;
      -ms-transition: all 0.5s;
      -o-transition: all 0.5s;
      transition: all 0.5s;

      &.active {
        padding: 8px 15px;
        max-height: 600px;
      }

      .option_element {
        color: $gray-2;
        font-weight: bold;
        text-transform: uppercase;
        margin-bottom: 5px;

        input {
          margin: 0;
          vertical-align: middle;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          width: 15px;
          height: 15px;
          position: relative;
          border: 0;

          & + label {
            &:before {
              content: '';
              top: 10px;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              position: absolute;
              left: -26px;
              width: 16px;
              height: 16px;
              border-radius: 20px;
              border: 1px solid;
            }
          }

          &:focus {
            outline: 0;
          }

          &:checked + label {
            color: $corporate_2;
            &:after {
              content: '';
              top: 10px;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              position: absolute;
              left: -22px;
              width: 10px;
              height: 10px;
              border-radius: 20px;
              background: $corporate_2;
            }
          }
        }


        label {
          margin-left: 10px;
          position: relative;
          font-size: 14px;
          line-height: 2;
        }
      }
    }

    .filter_block {
      &.active {
        .filter_title {
          .less {
            opacity: 1;
          }

          .more {
            opacity: 0;
          }
        }

        .options_list {
          padding: 8px 15px;
          max-height: 600px;
          background: #f4f4f4;
          box-shadow: inset 0px -1px 0 #cacaca;
        }

        &.offers_filters_wrapper {
          .filter_title {
            border-bottom: 1px solid #cacaca;
          }

          .options_list {
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
          }
        }
      }
    }
  }

  .filters_buttons_wrapper {
    margin-top: 20px;

    #clear_filters_button, #apply_filters_button {
      display: inline-block;
      float: left;
      width: 48%;
      font-size: 11px;
      border: 1px solid #9b9b9b;
      text-align: center;
      padding: 10px 0;
      color: #424242;
      font-weight: bold;
      text-transform: uppercase;
    }

    #apply_filters_button {
      float: right;
      color: white;
      border: 1px solid $corporate_2;
      background: $corporate_2;
    }
  }
}

.promotions_list_popup {
  ul {
    list-style: circle;
  }
}

.job_offers_found {
  margin: 30px 20px;
  font-style: italic;
  font-size: 22px;
  line-height: 28px;
  color: $corporate_2;
}

.work_wrapper {
  margin: 0 20px;

  .job_element {
    border: 1px solid #cacaca;
    margin-bottom: 20px;
    padding: 20px;
    display: table;
    text-align: left;

    &.hide {
      display: none;
    }

    .destiny_title {
      text-transform: uppercase;
      color: #424242;
      font-size: 12px;
      letter-spacing: 0.3px;
      font-weight: 600;
      margin: 0;
    }

    .job_title {
      margin: 0;
      font-size: 24px;
      line-height: 25px;
      color: $black;
      margin-bottom: 5px;
    }
    .read_more {
      display: none;
    }
    .job_description {
      font-size: 12px;
      color: #424242;
      margin-top: 20px;
      line-height: 20px;
      letter-spacing: 0.2px;
      &.exceded {
        max-height: 80px;
        overflow: hidden;
        @include transition(max-height, 0.5s);

        & + a + .read_more, & + .read_more {
          display: inline-block;
          color: $corporate_1;
          text-decoration: none;
          margin-top: 20px;
          @include transition(color, .6s);
          font-weight: 700;
          font-size: 15px;
          letter-spacing: 0.6px;
          margin-left: 30px;
          height: 20px;
          vertical-align: top;
          overflow: hidden;
          cursor: pointer;

          &:hover {
            color: $corporate_2;
          }

          .more {
            transition: margin 0.5s;
          }

          &.active {
            .more {
              margin-top: -20px;
            }
          }
        }

        & + .read_more {
          margin-left: 0;
        }

        &.visible {
          max-height: 1300px;
        }
      }
    }

    .job_header {
      padding-bottom: 20px;
      border-bottom: 1px solid #cacaca;
    }

    .subscribe_job {
      background: $corporate_2;
      border: 0;
      border-radius: 4px;
      margin: 15px 0 10px;
      padding: 10px 40px;
      font-family: "Open Sans", sans-serif;
      font-weight: bold;
      font-size: 16px;
      color: white;
      text-align: center;
      text-transform: uppercase;
    }

    .hotels_wrapper {
      font-size: 11px;
      color: $corporate_2;

      .hotel_name {
        margin-right: 10px;
      }
    }

    .more_info_job_wrapper {
      display: none;
    }

    .see_more_job_description {
      display: block;
      color: $corporate_2;
      font-weight: bold;
      font-size: 12px;
    }
  }

  .work_filters_button {
    width: 100%;
    margin-bottom: 20px;
    border: 1px solid #cacaca;
    padding: 15px;
    text-align: center;
    background: white;
    text-transform: uppercase;
    font-weight: bold;
    color: #424242;
    font-size: 12px;
    letter-spacing: 0.6px;
    box-sizing: border-box;

    i {
      color: $corporate_2;
      margin-right: 10px;
      font-size: 20px;
      font-weight: 300;
      vertical-align: middle;
    }
  }
}


//*====== Popups styles ======*//
#popup_v3_overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 100%;
  right: 0;
  background: rgba(255, 255, 255, 0.97);
  z-index: 101;
  transition: left 0.5s;

  &.opened {
    left: 0;
  }
}

#popup_website_v3 {
  position: fixed;
  top: 50%;
  left: 150%;
  transform: translate(-50%, -50%);
  z-index: 102;
  width: 80%;
  padding: 30px;
  transition: left 0.5s;
  background: transparent;

  &.opened {
    left: 50%;
  }

  #close_button_popup_v3 {
    position: absolute;
    top: 10px;
    right: 10px;

    i {
      font-size: 15px;
    }
  }

  .popup_content {
    font-size: 13px;
    max-height: 80vh;
    overflow: auto;

    .promotions_list_popup {
      .title_promotion {
        font-size: 16px;
        margin-bottom: 8px;
        display: grid;
        grid-template-columns: auto 20px;
        align-items: center;

        i {
          color: $corporate_2;
        }
      }

      .description_promotion {
        display: none;
        margin-bottom: 8px;
        margin-left: 8px;
      }
    }

    .popup_hotel_info_title {
      font-size: 18px;
      color: $corporate_2;
      font-weight: bold;
      font-style: italic;
      margin-bottom: 16px;
    }
  }
}