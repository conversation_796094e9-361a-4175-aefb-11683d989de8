.jobs_full_wrapper {
  display: table;
  width: 1140px;
  margin: 0 auto 70px;
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }

  .work_wrapper {
    width: 745px;
    float: right;
    padding-left: 30px;

    .job_offers_found {
      margin-bottom: 15px;
      font-style: italic;
      line-height: 32px;
      font-weight: 700;
      display: block;
      color: $corporate_2;
      font-size: 36px;
      padding-bottom: 8px;
    }

    .available_offers_wrapper {
      height: 1490px;
      overflow: hidden;

      .job_element {
        @include display_flex;
        width: 100%;
        border: 1px solid rgba(grey, .4);
        margin-bottom: 30px;

        &.hide {
          display: none;
        }

        .image_wrapper {
          position: relative;
          display: inline-block;
          width: 40%;
          height: 300px;
          overflow: hidden;

          img {
            @include center_image;
            width: auto;
            max-height: 130%;
          }
        }

        .job_content {
          width: 100%;
          display: inline-block;
          background-color: white;
          padding: 22px;
          position: relative;

          .job_header {
            border-bottom: 1px solid rgba(grey, .4);
            padding: 0 0 15px;
            margin-bottom: 15px;
            text-align: left;
            width: 60%;

            .destiny_title {
              letter-spacing: 0.3px;
              font-weight: 400;
              margin: 0;
              text-transform: uppercase;
              color: #424242;
              font-size: 13px;
            }

            .job_title {
              margin: 0;
              font-size: 24px;
              line-height: 30px;
              color: $black;
              margin-bottom: 5px;
              font-weight: 700;
            }

            .hotels_wrapper {
              font-size: 12px;
              color: $corporate_2;

              .hotel_name {
                margin-right: 10px;
                display: inline-block;
              }
            }
          }
          .read_more {
            display: none;
          }
          .job_description {
            font-weight: 300;
            position: relative;
            line-height: 20px;
            margin-top: 10px;
            font-size: 12px;
            color: #2d2d2d;
            width: 60%;

            .link {
              color: $corporate_2;
              display: block;
              text-decoration: underline;
            }
            &.exceded {
              max-height: 80px;
              overflow: hidden;
              @include transition(max-height, 0.5s);

              & + a + .read_more, & + .read_more {
                display: inline-block;
                color: $corporate_2;
                text-decoration: none;
                margin-top: 20px;
                @include transition(color, .6s);
                font-weight: 700;
                font-size: 15px;
                letter-spacing: 0.6px;
                margin-left: 30px;
                height: 20px;
                vertical-align: top;
                overflow: hidden;
                cursor: pointer;

                &:hover {
                  color: $corporate_1;
                }

                .more {
                  transition: margin 0.5s;
                }

                &.active {
                  .more {
                    margin-top: -20px;
                  }
                }
              }

              & + .read_more {
                margin-left: 0;
                float: right;
              }

              &.visible {
                max-height: 1300px;
              }
            }
          }

          .subscribe_job_wrapper {
            display: block;
            text-align: center;

            .subscribe_job, .link_job {
              display: inline-block;
              background-color: $corporate_2;
              border: 0;
              border-radius: 4px;
              margin: 0 0 15px;
              padding: 10px 40px;
              font-family: $text_family;
              font-weight: 700;
              font-size: 20px;
              letter-spacing: 1.3px;
              text-transform: uppercase;
              color: white;
              cursor: pointer;
              @include transition(background-color, 0.4s);

              &:hover {
                background-color: $corporate_1;
              }
            }
          }

          .see_more_job_description {
            color: $corporate_2;
            display: block;
            text-decoration: none;
            text-align: center;
            @include transition(color, .6s);
            letter-spacing: 0.6px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            cursor: pointer;

            &:hover {
              color: $corporate_1;
            }
          }

          .more_info_job_wrapper {
            display: none;
          }

          .buttons_wrapper {
            position: absolute;
            bottom: 28px;
            right: 15px;
            top: 50%;
            @include transform(translateY(-50%))
          }
        }
      }
    }
  }
}