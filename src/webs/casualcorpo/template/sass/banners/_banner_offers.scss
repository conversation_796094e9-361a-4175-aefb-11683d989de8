.banner_offers_full_wrapper {
  position: relative;
  z-index: 1;
  padding: 50px 0 50px;
  background-color: white;
  overflow: hidden;

  .bg_img_wrapper {
    position: absolute;
    z-index: -1;
    left: 0;
    right: 0;
    top: 0;
    height: 65%;
    width: 100%;
    overflow: hidden;

    &:before {
      content: "";
      @include full_size;
      z-index: 5;
      background: linear-gradient(to right, rgba($black, .6) 2%, rgba($black, .5) 10%, rgba($black, .3) 20%, rgba($black, 0) 100%);
    }

    img {
      @include cover_image;
    }
  }

  .banner_offers_content {
    padding-left: 50px;
    width: 55%;

    .content_title {
      @include title_styles_2;
      font-size: 80px;
      line-height: 80px;
      padding-bottom: 20px;
    }

    .content_text {
      @include text_styles($color: white);
      padding-bottom: 20px;
    }

    .btn_personalized_2 {
      margin-left: 20px;
    }
  }


  .banner_offers_wrapper {
    width: 100%;
    margin-top: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 0 50px;

    &.owl-carousel {
      display: block;
      width: calc(100% - 50px);
      transform: translateX(50px);
      padding: 0;

      .owl-stage-outer {
        padding: 50px 100px 50px 0;

        .owl-stage {
          padding-left: 0 !important;

          .owl-item {
            @include transition(all, .6s);
            @include transform(scale(1));

            .offer {
              width: 100%;
            }

            &:hover, &:focus {
              z-index: 10;
              @include transform(scale(1.1));

              & ~ .owl-item {
                @include transform(translateX(5%));
              }
            }
          }
        }

        &:before {
          content: '';
          z-index: 1;
          width: 190px;
          position: absolute;
          top: 50px;
          bottom: 50px;
          right: 0;
        }

        &:after {
          content: '';
          z-index: 1;
          width: 190px;
          position: absolute;
          top: 50px;
          bottom: 50px;
          right: 0;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
        }
      }

      .owl-nav {
        @include center_y;
        z-index: 99;
        left: 0;
        right: 0;
        height: 0;

        .owl-prev, .owl-next {
          @include center_y;
          @include owl_nav_styles;
          left: 15px;

          i {
            color: $corporate_2;
          }
        }

        .owl-prev {
          display: none;
        }

        .owl-next {
          left: auto;
          right: 15px;
        }
      }
    }

    .offer {
      display: inline-flex;
      flex-wrap: wrap;
      padding: 30px;
      position: relative;
      align-items: flex-end;
      justify-content: flex-start;
      width: calc(100% / 3);
      height: 340px;
      @include transition(all, .6s);

      &:before {
        content: '';
        @include full_size;
        z-index: 2;
        background: linear-gradient(to top, rgba($black, .8) 5%, rgba($black, .6) 10%, rgba($black, .5) 20%, rgba($black, 0) 100%);
      }

      .picture_wrapper {
        @include full_size;
        z-index: 1;

        img {
          @include cover_image;

        }
      }

      .offer_content {
        .title {
          position: relative;
          z-index: 5;
          display: block;
          width: 100%;
          color: white;
          font-size: 29px;
          line-height: 35px;
          font-weight: 700;
          font-family: $title_family;
          margin-bottom: 15px;
        }

        .text {
          position: relative;
          z-index: 5;
          display: block;
          width: 100%;
          color: white;
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
        }
      }

      .hidden_content {
        position: absolute;
        top: 20%;
        left: 0;
        right: 0;
        z-index: 4;
        opacity: 0;
        visibility: hidden;
        text-align: center;
        transition: all .4s;
        @include transform(scale(.7));

        a {
          min-width: 300px;

          &.btn_personalized_2 {
            margin-top: 20px;
          }
        }
      }

      &:hover {
        .hidden_content {
          opacity: 1;
          transition-delay: .3s;
          visibility: visible;
        }
      }
    }

    &:not(.owl-carousel) {
      .offer {
        z-index: 1;

        .offer_content {
          z-index: 2;
        }

        &::before,
        .picture_wrapper,
        .offer_content,
        .hidden_content {
          transition: all .4s;
          @include transform(scale(1));
        }

        &:hover {
          z-index: 2;

          &::before,
          .picture_wrapper,
          .offer_content,
          .hidden_content {
            @include transform(scale(1.15));
          }

          .hidden_content {
            transition-delay: .3s;
          }
        }
      }
    }
  }
}