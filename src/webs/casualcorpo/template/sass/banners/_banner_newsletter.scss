.newsletter_full_wrapper {
  position: relative;
  background-color: $corporate-2;
  margin-top: 20px;

  .newsletter_wrapper {
    @include base_banner_styles;
    position: relative;
    z-index: 1;

    .container12 {
      position: relative;
      width: 100%;
    }

    .newsletter_title {
      font-size: 33px;
      line-height: 57px;
      font-weight: 700;
      color: white;
      padding-bottom: 15px;
      text-align: left;
    }

    .newsletter_description {
      @include text_styles(white);
    }

    .newsletter_form {
      padding-top: 15px;
      text-align: left;
      position: relative;
      display: inline-block;
      //width: 670px;

      input#suscEmail {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        text-align: left;
        background-color: transparent;
        border: 0;
        border-bottom: 2px solid rgba(white, .8);
        background-clip: padding-box;
        padding: 5px 25px 5px 5px;
        width: 450px;
        height: 53px;
        @include newsletter_placeholder;
        color: white;
        margin: 0 30px 0 0;

        &:focus {
          outline: 0;
        }

        &::-webkit-input-placeholder {
          @include newsletter_placeholder;
        }

        &:-moz-placeholder {
          @include newsletter_placeholder;
        }

        &::-moz-placeholder {
          @include newsletter_placeholder;
        }

        &:-ms-input-placeholder {
          @include newsletter_placeholder;
        }

        &::placeholder {
          @include newsletter_placeholder;
        }
      }

      .button_newsletter {
        @include btn_styles_2;
        background-color: white;
        color: $corporate_2;
        height: 53px;
        padding: 14px 50px;
        border: solid 2px white;
        display: inline-flex;
        align-items: center;

        &:before {
          display: none;
        }

        &:hover {
          background-color: transparentize(white, 0.2);
          color: $corporate_2;
        }
      }

      .check_newsletter {
        display: block;
        width: 100%;
        text-align: left;
        margin-top: 10px;

        &:not(:last-of-type) {
          margin-top: 20px;
        }

        .newsletter_checkbox {
          input.check_privacy {
            @include checkbox_styles;
          }

          > a, > label {
            display: inline-block;
            vertical-align: top;
            max-width: 60%;
            margin-left: 15px;
            font-size: 15px;
            line-height: 18px;
            font-weight: 400;
            letter-spacing: 1.2px;
            color: white;

            a {
              color: inherit;
            }
          }
        }
      }
    }
  }


  .bg_colors {
    background-color: $corporate_4;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 500px;

    &::before,
    &::after {
      position: absolute;
      content: '';
      top: 0;
      bottom: 0;
    }

    &::before {
      width: 150px;
      right: 100px;
      background-color: $corporate_1;
    }

    &::after {
      width: 100px;
      right: 0;
      background-color: $corporate_3;
    }
  }
}