.banner_hotel_contacts {
  width: 1140px;
  margin: 0 auto 70px;

  .contacts_wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: stretch;
    gap: 20px;
    margin-top: 100px;

    .hotel_contact_wrapper {
      width: calc((100% - 40px) / 3);
      padding: 20px;
      border: 1px solid $black;
      color: $black;

      .icon {
        display: block;
        width: 30px;

        i {
          font-size: 20px;
          color: $corporate_2;
        }
      }

      .desc {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0;
      }

      .hotel_title {
        margin-bottom: 40px;
        font-weight: 700;
        font-size: 22px;
        letter-spacing: 0;

        small {
          display: block;
          font-weight: 500;
          font-size: 17px;
        }
      }

      .btn_checkin_online {
        font-size: 18px;
        letter-spacing: 0.23px;
        width: 100%;
      }

      .hotel_address {
        margin-bottom: 40px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        min-height: 60px;

        .btn_see_map {
          display: table;
          margin-top: 5px;
          font-weight: 700;
          text-transform: uppercase;
          color: $corporate_2;
          cursor: pointer;
        }
      }

      .hotel_contacts {
        .hotel_contact {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 10px;
          color: $corporate_2;
          cursor: pointer;
        }
      }
    }

    &.checkin {
      margin-top: 50px;

      .hotel_contact_wrapper {
        padding: 25px;

        .hotel_title {
          margin-bottom: 20px;
        }
      }
    }
  }
}