.hotel_content_wrapper {
  @include base_banner_styles;
  @include display_flex;

  .content_title {
    text-align: left;
    width: 100%;
    padding-bottom: 10px;

    .hotel_subtitle {
      @include title_styles;
      font-size: 60px;
      font-weight: 700;
      padding-bottom: 15px;

      span[font-family] {
        font-family: $text_family !important;
      }
    }

    .destiny {
      display: block;
      font-weight: 600;
      color: $color_text;
      font-size: 17px;
      line-height: 25px;

      i {
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }

  .hotel_sections_links {
    display: flex;
    width: 100%;
    background-color: white;
    padding-bottom: 30px;
    @include transition(all, .6s);

    .link {
      position: relative;
      width: 100%;
      font-size: 17px;
      line-height: 26px;
      font-weight: 600;
      text-transform: capitalize;
      border-bottom: 1px solid $grey;
      text-align: center;
      padding: 15px;
      cursor: pointer;
      @include transition(all, .6s);

      &:hover {
        color: $corporate_2;
      }

      &:before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: -2px;
        height: 5px;
        max-height: 0;
        overflow: hidden;
        visibility: hidden;
        background-color: $corporate_2;
        @include transition(all, .6s);
      }

      &.active {
        color: $corporate_2;
        font-weight: 700;

        &:before {
          max-height: 5px;
          visibility: visible;
        }
      }
    }

    &.fixed {
      position: fixed;
      z-index: 99;
      left: 0;
      right: 0;
      top: 93px;
      box-shadow: 1px 5px 8px 0 rgba(0, 0, 0, 0.1);
      padding: 10px calc((100% - 1140px) / 2);
      border-radius: $border_radius;

      .link {
        padding: 10px 15px;
      }
    }
  }

  .hotel_content {
    display: inline-block;
    width: calc(100% - 679px);
    padding-right: 80px;

    .hotel_description {
      @include text_styles;
      padding-bottom: 30px;

      hide {
        display: none;
      }

      .open_hotel_description_modal {
        text-transform: uppercase;
        font-weight: 400;

        &::after {
          display: inline-flex;
          vertical-align: middle;
          font-family: "Font Awesome 5 Pro";
          content: '\f054';
          margin-left: 7px;
        }
      }
    }

    .hotel_info {
      display: block;
      font-weight: 600;
      font-size: 14px;
      color: $color_text;
      line-height: 29px;

      i {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
        color: $corporate_2;
      }

      .see_map {
        display: block;
        text-transform: uppercase;
        color: $corporate_2;
        cursor: pointer;
        margin-bottom: 20px;
        padding-left: 20px;
      }

      &.extra_link {
        color: $corporate_2;
        text-decoration: underline;
      }
    }

    .logo_kayak {
      img {
        height: 70px;
        margin-top: 20px;
      }
    }
  }

  .hotel_main_img {
    position: relative;
    display: inline-block;
    width: 679px;
    z-index: 1;

    &:before {
      content: '';
      position: absolute;
      z-index: -1;
      width: calc(100% - 10px);
      height: 277px;
      left: -20px;
      top: 150px;
      background-color: $lightgrey;
    }

    .img_wrapper {
      position: relative;
      display: block;
      width: 100%;
      height: 404px;
      overflow: hidden;

      img {
        @include cover_image;
      }
    }
  }
}

.lightbox {
  display: none;
}