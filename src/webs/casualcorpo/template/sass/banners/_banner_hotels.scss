.banner_hotels_full_wrapper {
  position: relative;
  z-index: 1;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding-bottom: 50px;

  .top_content {
    width: 100%;
    padding: 0 50px;
    margin-bottom: -20px;
    position: relative;
    z-index: 2;

    .content_title {
      @include banner_title_styles(26px, white);
      margin-bottom: 20px;
    }

    .filter_hotels_wrapper {
      width: 100%;
      display: flex;
      justify-content: flex-start;

      .filter_item {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        text-align: center;
        color: $black;
        font-size: 15px;
        line-height: 20px;
        font-weight: 300;
        letter-spacing: .25px;
        background-color: white;
        font-family: $btn_family;
        padding: 14px 40px;
        border-radius: 4px;
        text-transform: none;
        border: solid 1px $black;
        cursor: pointer;
        margin-right: 10px;
        transition: all .2s;

        &:hover,
        &.active {
          border: solid 2px;
          font-weight: 700;
        }
      }
    }
  }

  .banner_hotels_wrapper {
    position: relative;
    width: calc(100% - 50px);
    transition: all .6s;

    &:after {
      content: '';
      position: absolute;
      top: 50px;
      bottom: 0;
      right: 0;
      z-index: 1;
      width: 190px;
      background: linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }

    .owl-stage-outer {
      padding: 60px 100px 23px 0;

      .owl-stage {
        padding-left: 0 !important;

        .owl-item {
          &.active {
            position: relative;
            z-index: 20;
            max-width: 600px;
            transition: all .8s;
          }

          &.hide_item {
            margin-right: 0 !important;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
          }
        }
      }
    }

    &:hover, &:focus-within {
      .banner {
        transform: translateX(-15%);
      }
    }

    .owl-item {
      @include transition(all, .6s);
      @include transform(scale(1));

      .banner {
        display: inline-flex;
        flex-wrap: wrap;
        margin-right: 10px;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 100%;
        height: 402px;
        overflow: hidden;
        @include transform(scale(1));
        @include transition(all, .6s);

        &:before {
          content: '';
          @include full_size;
          z-index: 2;
          background: linear-gradient(to top, rgba($black, .8) 5%, rgba($black, .6) 10%, rgba($black, .5) 20%, rgba($black, 0) 100%);
        }

        .label_closed {
          position: absolute;
          top: 20px;
          left: 0;
          background-color: $corporate_4;
          font-size: 12px;
          font-weight: 400;
          text-transform: uppercase;
          color: $black;
          line-height: 19px;
          padding: 0 0 0 10px;
          height: 20px;
          z-index: 10;

          &::before {
            position: absolute;
            content: '';
            top: 0;
            bottom: 0;
            right: 0;
            width: 30px;
            background-color: $corporate_4;
            transform: skew(-10deg) translateX(10px);
            z-index: -1;
          }
        }

        img {
          @include center_image;
          z-index: 1;
          width: auto;
        }

        .title {
          display: block;
          position: absolute;
          left: 15px;
          right: 15px;
          bottom: 15px;
          color: white;
          font-weight: 400;
          font-size: 15px;
          letter-spacing: 0;
          line-height: 24px;
          text-align: center;
          text-transform: uppercase;
          z-index: 3;

          span {
            display: block;
            font-weight: 700;
            font-size: 22px;
            letter-spacing: 1.1px;
            line-height: 24px;
            text-transform: uppercase;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          small {
            display: block;
            font-weight: 700;
            font-size: 14px;
            letter-spacing: 0.7px;
            text-transform: none;
          }
        }

        .hidden_content {
          position: relative;
          z-index: 4;
          opacity: 0;
          visibility: hidden;
          text-align: center;
          transition: all .4s;
          @include transform(scale(.7));

          a {
            width: 100%;

            &.btn_personalized_2 {
              margin-top: 20px;
            }
          }
        }
      }

      &:hover, &:focus {
        z-index: 10;
        @include transform(scale(1.15) translateX(30px));

        & ~ .owl-item {
          @include transform(translateX(80px));
        }

        .hidden_content {
          opacity: 1;
          transition-delay: .3s;
          visibility: visible;
        }
      }
    }


    .owl-nav {
      @include center_y;
      z-index: 99;
      left: 0;
      right: 0;
      height: 0;

      .owl-prev, .owl-next {
        @include center_y;
        @include owl_nav_styles;
        left: 15px;

        i {
          color: $corporate_2;
        }
      }

      .owl-next {
        left: auto;
        right: 15px;
      }
    }

    &.hide {
      position: absolute;
      top: 86px;
      z-index: -1;
      opacity: 0;
    }
  }

  .bottom_links {
    padding-right: 40px;
    display: block;
    text-align: right;
  }
}