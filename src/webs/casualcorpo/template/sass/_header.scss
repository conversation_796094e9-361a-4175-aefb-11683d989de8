header {
  background-color: white;
  position: relative;
  z-index: 1001;
  width: 100%;
  @include display_flex;
  align-items: center;
  padding: 0 40px;
  height: 120px;
  min-width: 1140px;
  box-shadow: 1px 5px 8px 0 rgba(0, 0, 0, 0.1);
  transition: background .4s;

  @media (max-width: 1240px) {
    padding: 0 15px;
  }

  &.menu_open {
    position: relative;

    .left_header .top_sections .top_section#open_main_menu .burguer_icon {
      opacity: 0;
    }

    .left_header .top_sections #close_main_menu {
      opacity: 1;
      transform: translateY(4px);
    }
  }

  .left_header {
    display: inline-flex;
    align-items: center;
    width: 75%;

    #logoDiv {
      display: inline-block;
      vertical-align: middle;
      width: 242px;
      margin-right: 40px;

      img {
        max-width: 100%;
      }

      /*@media (max-width: 1570px) {
        width: 230px;
        margin-right: 10px;
      }*/

      @media (min-width: 1441px) and (max-width: 1570px) {
        width: 230px;
        margin-right: 10px;
      }

      @media (min-width: 1241px) and (max-width: 1440px) {
        width: 200px;
        margin-right: 10px;
      }

      @media (max-width: 1240px) {
        width: 180px;
        margin-right: 10px;
      }
    }


    .top_sections {
      display: inline-block;
      vertical-align: middle;

      #close_main_menu {
        position: absolute;
        top: 30px;
        left: 325px;
        font-size: 46px;
        width: 50px;
        height: 50px;
        cursor: pointer;
        z-index: 50;
        opacity: 0;
        transform: translateY(-100%);

        &:hover {
          span {
            background-color: $corporate_1;
          }
        }

        span {
          display: block;
          width: 100%;
          height: 2px;
          background-color: $black;
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          @include transition(all, .6s);

          &:nth-child(1) {
            transform: translateY(-50%) rotate(45deg);
          }

          &:nth-child(2) {
            transform: translateY(-50%) rotate(-45deg);
          }
        }
      }

      .my_bookings_header, .services_header {
        position: fixed;
        left: 0;
        right: 0;
        top: 120px;
        z-index: 10;
        padding: 0 calc((100% - 1140px) / 2);
        background-color: rgba(white, 0.95);
        height: 100vh;
        max-height: 0;
        overflow: hidden;
        @include transition(all, .6s);
        background: rgb(255, 255, 255);
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.90) 0vh, rgba(255, 255, 255, 0.90) 40vh, rgba(216, 4, 114, 0.90) 40vh, rgba(216, 4, 114, 0.90) 100vh);

        &.active {
          max-height: calc(100vh - 120px);
          padding: 0 calc((100% - 1140px) / 2) calc(100vh - 440px);
          border-top: 1px solid rgb(20, 20, 20);
          overflow-y: auto;
        }

        .my_bookings_list, .services_list {
          display: flex;
          justify-content: center;
          height: 100%;
          align-items: center;
          text-transform: uppercase;

          .section_link {
            color: black;
            font-weight: bold;
            font-size: 22px;
            padding: 50px;
            width: 420px;
            opacity: 1;
            transition: .6s all;

            &:hover {
              opacity: 0.4;
            }

            &:not(:first-of-type) {
              border-left: 1px solid black;
            }
          }
        }
      }

      .all_hotels_header {
        position: fixed;
        left: 0;
        right: 0;
        top: 120px;
        z-index: 10;
        padding: 0 calc((100% - 1140px) / 2);
        background-color: rgba(white, 0.95);
        height: 100vh;
        max-height: 0;
        overflow: hidden;
        @include transition(all, .6s);


        .destinies {
          @include display_flex;
          position: relative;
          padding-top: 30px;

          .destiny_element {
            display: inline-block;
            width: calc(100% / 3 - 20px);
            padding: 15px 0;

            &:not(:nth-of-type(3n)) {
              margin-right: 30px;
            }

            .destiny_title_wrapper {
              position: relative;
              border-bottom: 1px solid #C4C4C4;
              margin-bottom: 5px;

              .destiny {
                position: relative;
                cursor: pointer;
                display: inline-flex;
                text-transform: uppercase;
                color: $corporate_1;
                margin-bottom: 5px;
                padding-bottom: 5px;
                font-size: 22px;
                line-height: 27px;
                font-weight: 700;
                background-clip: padding-box;
                @include transition(all, .6s);

                &::after {
                  display: inline-block;
                  vertical-align: middle;
                  font-family: 'Font Awesome 5 Pro';
                  content: '\f054';
                  font-weight: 100;
                  font-size: 18px;
                  margin-left: 7px;
                  transition: all .4s;
                }
              }


              span {
                color: $corporate_1;
                font-size: 18px;
                line-height: 24px;
                font-weight: 500;
                opacity: 0;
                margin-left: 0;
                @include transition(all, .4s);
              }

              &:hover {
                .destiny {
                  &::after {
                    margin-left: 10px;
                  }
                }

                span {
                  margin-left: 10px;
                  opacity: 1;
                  transform: translateX(10px);
                }
              }
            }

            .hotels_list {
              width: 100%;
              text-align: left;

              .hotel_element {
                font-size: 15px;
                line-height: 22px;
                color: $black;
                display: block;
                @include transition(all, .6s);

                &:hover {
                  color: $corporate_1;
                }

                &.section_active {
                  font-weight: 700;
                  color: $corporate_1;
                }

                &.no_info {
                  color: #ACABAB;
                }

                &.has_label {
                  .label {
                    display: inline-block;
                    background-color: $corporate_4;
                    font-size: 11px;
                    font-weight: 400;
                    text-transform: uppercase;
                    color: $black;
                    line-height: 19px;
                    padding: 1px 0 2px 10px;
                    height: 20px;
                    position: relative;
                    margin-left: 7px;

                    &::before {
                      position: absolute;
                      content: '';
                      top: 0;
                      bottom: 0;
                      right: 0;
                      width: 30px;
                      background-color: $corporate_4;
                      transform: skew(-10deg) translateX(10px);
                      z-index: -1;
                    }
                  }
                }
              }
            }
          }
        }

        &.active {
          max-height: calc(100vh - 120px);
          padding: 40px calc((100% - 1140px) / 2);
          border-top: 1px solid $black;
          overflow-y: auto;
        }
      }

      .top_section {
        display: inline-block;
        margin-right: 25px;
        text-transform: none;
        text-decoration: none;
        color: $black;
        font-family: $text_family;
        font-size: 20px;
        line-height: 25px;
        font-weight: 400;
        cursor: pointer;
        @include transition(all, .6s);

        @media (min-width: 1441px) and (max-width: 1570px) {
          margin-right: 15px;
          font-size: 18px;
        }

        @media (min-width: 1241px) and (max-width: 1440px) {
          margin-right: 10px;
          font-size: 14px;
        }

        @media (max-width: 1240px) {
          margin-right: 7px;
          font-size: 13px;
        }

        &#open_main_menu {
          height: 30px;
          display: inline-flex;
          align-items: center;

          .burguer_icon {
            height: 2px;
            width: 30px;
            background-color: $black;
            border-radius: 2px;
            transform: translateY(-6px);

            &::before,
            &::after {
              position: absolute;
              content: '';
              left: 0;
              right: 0;
              height: 2px;
              width: 30px;
              background-color: $black;
            }

            &::before {
              top: -8px;
            }

            &::after {
              bottom: -8px;
            }
          }
        }

        &:last-of-type {
          margin-right: 0;
        }

        &:hover {
          color: $corporate_1;
        }

        &.section_active {
          font-weight: 700;
          color: $corporate_1;
        }

        &#open_header_hotels, &#open_header_my_bookings, &#open_header_services {
          position: relative;
          cursor: pointer;

          &::after {
            display: inline-block;
            vertical-align: middle;
            content: "\f078";
            font-weight: 300;
            font-size: 18px;
            margin-left: 5px;
            @include fa_default_style;
            transition: all .4s;
          }

          &.active {
            &::after {
              content: "\f077";
            }
          }
        }

        &#open_header_my_bookings {
          text-transform: capitalize;
        }
      }
    }
  }


  .right_header {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    padding-right: 100px;
    z-index: 15;

    .header_item {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 30px;

      &.phone_contact_wrapper {
        background-color: $corporate_2;
        position: relative;
        z-index: 1;

        .phone_contact {
          position: relative;
          color: $black;
          font-weight: 700;
          font-size: 21px;
          line-height: 25px;
          @include transition(all, .6s);


          &:hover {
            color: white;
          }

          &::before {
            display: inline-block;
            vertical-align: middle;
            font-family: 'Font Awesome 5 Pro';
            content: '\f095';
            font-size: 23px;
            font-weight: 100;
            -webkit-transform: scaleX(-1);
            transform: scaleX(-1);
          }
        }
      }

      &.language_selector {
        background-color: $corporate_4;
        position: relative;
        z-index: 1;

        #header_language_selector {
          display: block;
          color: black;
          font-weight: 700;
          font-size: 21px;
          line-height: 25px;
          text-transform: uppercase;
          cursor: pointer;
          @include transition(all, .6s);

          span, i {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px;
          }
        }

        .header_lang {
          position: absolute;
          text-align: left;
          background-color: $corporate-4;
          left: 0;
          right: 0;
          top: calc(100% - 40px);
          max-height: 0;
          opacity: 0;
          padding-right: 35px;
          padding-bottom: 20px;
          overflow: hidden;
          @include transition(max-height, .6s);

          a {
            padding: 5px 0 5px 65px;
            display: block;
            text-transform: uppercase;
            font-size: 21px;
            font-weight: 700;
            color: $color_text;
            letter-spacing: 0.6px;
            opacity: .5;
            text-decoration: none;
            @include box-sizing(border-box);
            @include transition(font-weight, .4s);

            &.selected {
              color: $color_text;
              display: none;
            }

            &:hover {
              font-weight: bold;
            }
          }
        }

        &:hover {
          .header_lang {
            max-height: 300px;
            opacity: 1;
          }
        }
      }
    }

    &::before,
    &::after {
      position: absolute;
      content: '';
      top: 0;
      bottom: 0;
    }

    &::before {
      left: 0;
      right: 0;
      background-color: $corporate_1;
    }

    &:after {
      width: 35px;
      right: 0;
      background-color: $corporate_3;
    }

    @media (min-width: 1441px) and (max-width: 1570px) {
      .header_item.phone_contact_wrapper .phone_contact,
      .header_item.language_selector #header_language_selector,
      .header_item.language_selector .header_lang a {
        font-size: 18px;
      }
    }

    @media (min-width: 1241px) and (max-width: 1440px) {
      .header_item.phone_contact_wrapper .phone_contact,
      .header_item.language_selector #header_language_selector,
      .header_item.language_selector .header_lang a {
        font-size: 14px;
      }
    }

    @media (max-width: 1240px) {
      .header_item.phone_contact_wrapper .phone_contact,
      .header_item.language_selector #header_language_selector,
      .header_item.language_selector .header_lang a {
        font-size: 13px;
      }
    }
  }

  #main_menu {
    position: fixed;
    left: 0;
    right: 0;
    top: 120px;
    z-index: 10;
    padding: 0 calc((100% - 1140px) / 2);
    height: 100vh;
    max-height: 0;
    overflow: hidden;
    @include transition(all, .6s);

    #main-sections-inner {
      position: relative;
      width: 800px;
      height: 100%;
      padding-top: 30px;
      margin: auto;
      overflow-y: auto;
      padding-bottom: 100px;

      .main-section-div-wrapper {
        display: block;
        text-align: center;

        &#section-active {
          a {
            font-weight: 700;
            color: $black;
          }
        }

        a {
          display: block;
          padding: 0 10px 10px;
          font-size: 22px;
          line-height: 28px;
          font-weight: 700;
          color: $black;
          border-bottom: 1px solid #D6D6D6;
          background-clip: padding-box;
          text-transform: uppercase;

          &:not(.with_subsections) {
            border-bottom: none;
            cursor: pointer;
            @include transition(color, .6s);

            &:hover {
              color: $corporate_1;
            }
          }

          @media (min-width: 1500px) {
            margin-bottom: 20px;
          }

          @media (max-width: 1499px) {
            margin-bottom: 10px;
          }
        }

        .subsections {
          @include display_flex;
          width: 100%;
          padding-bottom: 20px;
          justify-content: center;

          .main-section-subsection {
            display: inline-block;
            text-align: center;
            width: calc(100% / 3);
            padding: 5px 15px;

            a {
              display: inline-block;
              font-size: 22px;
              line-height: 28px;
              font-weight: 400;
              color: $black;
              cursor: pointer;
              @include transition(color, .6s);
              text-transform: none;

              &:hover {
                color: $corporate_1;
              }

              &#subsection-active {
                font-weight: 700;
                color: $corporate_1;
              }
            }
          }
        }
      }
    }

    &.active {
      border-top: 1px solid $black;
      max-height: 100vh;
      padding: 40px calc((100% - 1140px) / 2);
    }

    &::before {
      @include full_size;
      content: '';
      opacity: .95;
      background-color: white;
    }
  }
}
