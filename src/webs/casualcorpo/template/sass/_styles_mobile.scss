$fontawesome5: true;
$is_mobile: true;
$mobile_padding: 20px;

@mixin base_mobile_styles() {
  position: relative;
  padding: $mobile_padding;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  * {
    box-sizing: border-box;
  }
}

@import "defaults";
@import "styles_mobile/2/2";

body {
  padding-bottom: 120px;
  font-family: $text_family;

  * {
    -webkit-font-smoothing: antialiased;
  }

  &.hide_engine {
    #booking_engine_popup {
      display: none;
    }
  }

  a {
    text-decoration: none;
    color: $corporate_2;
  }

  strong, b {
    font-weight: 700;
  }

  &.checkin_section {
    .newsletter_full_wrapper,
    .mobile_engine_action {
      display: none;
    }
  }

  /*Datepicker styles*/
  .ui-datepicker-inline.ui-datepicker {
    .ui-datepicker-header {
      .ui-datepicker-prev, .ui-datepicker-next {
        top: 5px;
      }

      .ui-datepicker-title {
        color: #303948 !important;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .ui-widget-content .ui-state-active {
    border: 0;
    background: $corporate_2 !important;
    color: white !important;
  }

  .ui-datepicker-start_date .ui-state-default {
    border: 0;
    background: $corporate_2 !important;
    color: white !important;
  }

  /*******/


  #full_wrapper_booking {
    .selected_hotel,
    .selected_hotel.accepted {
      font-size: 12px !important;
      margin-left: 20px !important;
    }

    .destination_wrapper {
      background: white !important;
      margin-bottom: 10px;
      width: calc(100% - 20px);
      position: relative;
      border: none !important;

      .group_element {
        .group_label {
          margin: 0 !important;
        }


        .group_options, {
          background-color: transparent;

          .hotel_element {
            font-weight: 300;
            margin-bottom: 10px;

            &.has_label {
              .label {
                background-color: #E0DE08;
                font-size: 12px;
                font-weight: 400;
                text-transform: uppercase;
                color: #141414;
                padding: 2px 5px;
                margin-left: 5px;
              }
            }

            &.disabled_hotel {
              color: #ACABAB;
            }
          }
        }

        &:first-of-type {
          padding-top: 10px !important;
        }

        &.active {
          .group_options {
            margin-top: 0 !important;
          }
        }
      }

      select {
        width: 100%;
        height: 45px;
        padding-left: 35px;
        box-sizing: border-box;
      }
    }

    .dates_selector_wrapper {
      border-bottom: none;
    }

    .promocode_wrapper {
      border-top: none !important;
    }

    .room_list_wrapper {
      background-color: rgba(48, 57, 72, 0.95);

      .room_list {
        .room .children_selector {
          .children_label {
            .range-age {
              display: block;
              font-size: 10px;
            }
          }
        }
      }
    }

    .entry_label_calendar, .departure_label_calendar {
      background-color: $corporate_2;
    }

    .wrapper_booking_button {
      .promocode_wrapper .promocode_input {
        color: $black !important;

        &::placeholder {
          color: $black !important;

        }
      }

      .submit_button {
        background-color: $corporate-1 !important;
        padding: 6px 0 15px 0;
        border-radius: 4px !important;
      }
    }
  }

  .fancybox-container {

    .fancybox-bg {
      opacity: .7;
    }

    .fancybox-slide {
      padding: 0;

      .fancybox-content {
        padding: 30px;
        height: 100vh !important;
        width: 100vw !important;

        iframe {
          border: 0;
        }
      }
    }
  }

  header {
    .mailto {
      float: right;

      i {
        color: $corporate_2;
      }
    }

    .logo {
      img {
        max-height: 75px;
      }
    }
  }


  .main_menu {
    background-color: $corporate_2;
    z-index: 102;


    .main_ul {
      padding: 20px 0 50px;

      li {
        a {
          font-size: 20px;
          font-weight: bold;
          text-transform: uppercase;
          padding: 10px 20px;
        }

        &:nth-last-of-type(3), &:nth-last-of-type(4) {
          .submenu_list .submenu_element a {
            font-size: 15px !important;
            padding-bottom: 2px;
            letter-spacing: 0;
            font-weight: 400 !important;
            text-transform: none !important;
          }
        }

        &.section_subcontent {
          .submenu_main {
            .submenu_main_link {
              text-decoration: none;
              padding: 0;
              pointer-events: initial;
            }

            span {
              font-size: 20px;
              font-weight: bold;
              text-transform: uppercase;
              padding: 10px 20px;
            }

            i {
              font-size: 15px;
              color: $corporate_2;
              width: 20px;
              height: 20px;
              top: 10px;
              background-color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
            }

            &.open {
              i {
                background-color: transparent;
                border: 1px solid white;
                transform: none;

                &::before {
                  content: ''!important;
                  width: 10px;
                  height: 2px;
                  background-color: white;
                }
              }
            }
          }

          .submenu_list {
            &.level1 {
              > .submenu_element {
                a {
                  font-size: 20px;
                  font-weight: bold;
                  text-transform: uppercase;
                }
              }

              .submenu_main {
                i {
                  display: none;
                }
              }
            }

            &.level2 {
              display: block !important;

              a {
                font-size: 15px;
                padding-bottom: 2px;
                letter-spacing: 0;
                font-weight: 400;
                text-transform: none;
              }
            }
          }

          &.policies_links {
            display: none;
          }
        }
      }
    }

    .social_menu {
      background-color: transparent;
      text-align: left;

      a.mailto {
        float: none;
        position: absolute;
        right: 0;

        i {
          font-weight: 100;
          background-color: transparent;
        }
      }
      a i {
        background-color: transparent;
      }
    }

    ul li a, ul li span {
      font-family: $text_family;
      -webkit-font-smoothing: antialiased;
      letter-spacing: 0;
      font-weight: 600;
    }

    .main_ul, .social_menu {
      opacity: 0;
      transition: .1s;

      .submenu_list {
        a,
        .submenu_main > span {
          color: #fff;
          font-size: 15px;
          padding-bottom: 2px;
          letter-spacing: 0;
          font-weight: 600;
        }
      }
    }


  }

  .main_menu.open_menu {
    .main_ul, .social_menu {
      opacity: 1;
    }

    .social_menu, ul.main_ul {
      li,
      .submenu_element,
      .submenu_main,
      .submenu_list {
        @include animation_duration_steps(30, 0.45s, 0.05s);
      }
    }
  }

  nav {
    a, a i {
      color: $corporate_2;
    }
  }

  .main-owlslider {
    .owl-item {
      &:before {
        content: '';
        @include full_size;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
      }
    }

    .description_text {
      @include center_xy;
      top: 35%;
      width: 80%;
      z-index: 30;
      font-size: 16px;
      line-height: 24px;
      color: white;
      font-family: $text_family;
      text-align: left;

      .hotel_logo {
        width: 120px;
        display: block;
        margin-bottom: 20px;

        img {
          position: relative;
          top: auto;
          left: auto;
          @include transform(none);
          min-height: auto;
          min-width: auto;
          max-width: 100%;
        }
      }

      .title {
        @include title_styles_2;
        margin-bottom: 20px;
      }

      .subtitle {
        display: block;
        margin-bottom: 10px;
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
      }


      .btn_personalized_1, .btn_personalized_2 {
        margin: 15px 15px 0 0;
      }
    }
  }


  .section_content {
    padding-top: 20px;

    .hotel_info_slider {
      position: relative;
      display: block;
      height: 200px;
      width: 100%;
      margin-top: -60px;
      z-index: 1;

      img {
        @include cover_image;
      }

      .slider_description {
        position: absolute;
        left: 30px;
        right: 30px;
        bottom: 50px;
        color: white;
        text-align: left;
      }
    }

    .normal_section_mobile {
      .section_title {
        padding: 30px;
      }

      .section-content {
        padding: 0 30px;
      }
    }

    .content_subtitle_wrapper {
      .content_subtitle_content {
        padding: 20px 30px 40px;

        .content_subtitle_title {
          text-align: left;
          font-weight: bold;
        }

        .content_subtitle_description {
          padding: 0;
          text-align: left;
          hide {
            display: none;
          }
        }
      }
    }

    .location_content {
      .section-title {
        display: none;
      }

      .section-subtitle {
        text-align: center !important;
      }
    }

    > h1, h1.section-title, .content_subtitle_title, .section_title, h2.section_title, .location_content .section-subtitle {
      @include title_styles;
      padding-top: 30px;
      font-size: 35px;
      line-height: 43px;
      text-align: left;
      font-weight: bold;

      span {
        color: $color_text;
      }
    }

    div.content, div.content_subtitle_description, .section-content, .contact_content_element, .default_reservation_text {
      @include text_styles;
      font-size: 16px;
      line-height: 26px;
      text-align: left;
      width: auto;
      padding: 20px 20px;
    }

    #welcomepickups {
        width: 90%;
        margin: 0 auto;
    }
  }

  .btn_personalized_1 {
    @include btn_styles;
  }

  .btn_personalized_2 {
    @include btn_styles_2;
  }

  @import "mobile/banner_destinies";
  @import "mobile/banner_newsletter";
  @import "mobile/banner_hotels";
  @import "mobile/banner_offers";
  @import "mobile/banner_hotels_destiny";
  @import "mobile/banner_hotel_contacts";
  @import "mobile/hotel_individual";
  @import "mobile/services_icons_hotel";
  @import "mobile/hotel_gallery";
  @import "mobile/hotel_rooms";
  @import "mobile/hotel_offers";
  @import "mobile/hotels_section";
  @import "mobile/banner_icons";
  @import "mobile/banner_cycle";
  @import "mobile/offers";
  @import "mobile/offers_carousel";
  @import "mobile/news_mobile";
  @import "mobile/form_contact_mobile";
  @import "mobile/banner_next_hotels";
  @import "mobile/jobs_promotions";
  @import "mobile/becasual_banner_mobile";
  @import "modal";
  @import "mobile/extra_footer";
  @import "mobile/submenu_mobile";
  @import "mobile/banner_hotel_transfer";

  .hotels_section_wrapper {
    .since_price {
      left: 15px !important;
      bottom: 15px !important;
      transform: none !important;
    }
  }

  .fancybox-inner {
    iframe {
      width: 100%;
      height: 100%;
    }
  }

  .location_wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;

    .banner_hotel_contacts {
      order: 1;
    }

    form {
      order: 2;
    }

    .map {
      order: 3;
    }


    form#contact {
      #contact-button {
        background-color: $corporate_2;
      }

      .check_element {
        a {
          color: $corporate_2;
        }
      }
    }
  }

  .my_reservation_section {
    .my-reservation-form {
      margin-top: 40px;

      #my-bookings-form-fields {
        .selectHotel {
          background-color: #eaeaea !important;
          border-color: #eaeaea !important;
        }
      }

      #my-bookings-form-search-button {
        border: none;
        background-color: $corporate_2 !important;
      }

      input[type="text"] {
        color: $black;
        font-weight: 400;

        &::-webkit-input-placeholder {
          color: $black;
          font-weight: 400;
        }

        &:-moz-placeholder {
          color: $black;
          font-weight: 400;
        }

        &::-moz-placeholder {
          color: $black;
          font-weight: 400;
        }

        &:-ms-input-placeholder {
          color: $black;
          font-weight: 400;
        }

        &::placeholder {
          color: $black;
          font-weight: 400;
        }
      }
    }

    #cancelButton {
      background: $corporate_2;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 40px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_2, 10%);
      }
    }
  }


  .banner_club_wrapper {
    .banner_club_button {
      display: block;

      .pic {
        width: 100%;
        height: 240px;

        img {
          width: 100%;
        }
      }
    }

    .banner_club {
      display: block;
      width: 100%;
      padding-left: 0;

      .title {
        color: $black;
        padding: 10px 20px;
        text-align: center;
      }

      .icon {
        width: calc((100% / 3) - 10px);
        padding: 10px 0;

        span, i {
          color: $black;
        }
      }
    }
  }

  .rooms_wrapper .room_block, .promotions_wrapper {
    .room_info {
      h1, h3 {
        @include title_styles;
        text-align: center;
        font-size: 22px;
        padding: 10px;
        line-height: 24px;

        .subtitle {
          font-size: 20px;
          line-height: 24px;
        }
      }

      .room_description, .desc {
        @include text_styles;
        line-height: 20px;
        font-size: 13px;
      }
    }
  }

  .rooms_wrapper {
    iframe {
      width: 100%;
    }
  }

  .gallery_divided_title span {
    font-family: $title_family;
    font-size: 20px;
    font-weight: 400;
    bottom: 23px;
  }

  .minigallery_wrapper {
    .owl-prev, .owl-next {
      background: rgba($corporate_2, .8);
    }
  }

  .form_subtitle {
    @include title_styles;
    margin-top: 20px;
  }

  &.only_injection_section {

  }

  .breadcrumbs {
    letter-spacing: 1px;
    font-weight: 500;
    background-color: $corporate_2;

    &::-webkit-scrollbar {
      display: none;
    }

    a {
      font-weight: 800;
    }

    &.moved {
      padding-left: 20px;
      transform: translateY(150px);
      position: relative;
      top: 15px;
      overflow-x: scroll;

      a {
        font-size: 11px;
        padding: 5px;

        i {
          font-size: 10px;
        }
      }
    }
  }

  #my-bookings-form-fields {
    .selectHotel {
      font-size: 15px;
      padding: 1em;
      border-width: 0;
      background-color: white;
      box-sizing: border-box;
      width: 100%;
      border-radius: 5px;
      text-align: left;
      margin: 1em auto 0;
      border: 0.5em solid #F9F9F9;

    }

    #my-bookings-form-search-button, #my-bookings-form-modify-button {
      display: block;
      border: none;
      padding: 10px 0;
      box-sizing: border-box;
      font-size: 22px;
      text-transform: uppercase;
      width: 100%;
      border-radius: 5px;
      margin: auto;
      background-color: $corporate_1;
      color: white;
      margin-bottom: 10px;
    }
  }

  #booking_engine_popup.engine_v2 {
    .logo_wrapper {
      max-width: 100%;
      padding: 5px 17px 3px;

      .logotype_mobile {
        max-height: 70px;
        max-width: 100%;
      }
    }

    .mobile_engine_action {
      border-radius: 0 !important;
      bottom: 60px !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
      opacity: 1;
      transition: none;
    }

    &.active {
      .mobile_engine_action {
        opacity: 0;
        z-index: 0;
      }
    }
  }

  #booking_engine_popup .mobile_engine_v2 #full_wrapper_booking #occupancy_popup {
    .modification_buttons .minus::before {
      -webkit-mask-image: initial;
      mask-image: initial;
      border-top: 1px solid #0268D7;
      background: none;
      position: relative;
      top: 7px;
    }

    .room_list .room.active .pets_selector {
      padding: 20px;

      .pets_counter {
        display: none !important;
      }

      select {
        font-size: 18px;
        position: relative;
        right: -180px;
      }
    }
  }

  .gift_bono_content {
    .buy_bono {
      top: 760px !important;
    }

    .step_wrapper {
      .hotel_selector_wrapper {
        .booking-form-field {
          #hotel_selector {
            width: 100%;
            font-size: 17px;
            font-weight: 400;
            font-family: $text_family;
          }

          .select2-container {
            display: none;
          }
        }
      }
    }
  }
}

.loading_animation_popup .spincircle:after {
  border: 6px solid $corporate_2 !important;
  border-color: transparent transparent $corporate_2 transparent !important;
}


.fancybox-slide--image .fancybox-image {
  height: auto;
}

.rooms_background {
  z-index: -1;
}

.aunoa-wraper #aunoa-webchat {
  bottom: 120px !important;
  right: 0 !important;
  z-index: 100 !important;
}

.bonos_section {
  .breadcrumbs.moved {
    top: 53px;
  }

  .section_content .hotel_info_slider {
    margin-top: -8px;

    img {
      object-fit: scale-down;
    }
  }
}

.mobile_engine_action.engine_v2 {
  padding: 15px 0;
}

footer {
  background: $black;
  padding: 20px;

  .footer_links_wrapper {
    color: white;
    text-align: center;
    line-height: 21px;

    a {
      font-size: 13px;
      color: white;
      text-transform: uppercase;
    }
  }
}

.mobile_engine_v2 {
  .booking_engine_mobile {
    #full_wrapper_booking {
      #wrapper_booking {
        #booking {
          .booking_title {
            .title_label {
              font-size: 16px;
            }
          }

          .grouped_selector {
            .hotel_selector {
              .selected_hotel {
                position: relative;
                font-size: 16px !important;

                &::before {
                  content: "";
                  position: absolute;
                  top: -40px;
                  left: 0;
                  right: 0;
                  bottom: 0;
                }
              }

              .destination_wrapper {
                .group_element {
                  .group_label, .destiny_b0, .hotel_element {
                    font-size: 16px;
                  }
                }
                .select_hotel_error {
                    padding: 0 20px 0 25px;
                    position: relative;
                    top: 10px;
                    color: red;
                    font-size: 12px;
                    font-family: $text_family;
                }
              }
            }
          }

          .dates_selector_wrapper {
            .stay_selection {
              .entry_date_wrapper, .departure_date_wrapper {
                .label_field {
                  font-size: 14px;
                }

                .booking_value {
                  font-size: 16px;
                }
              }
            }
          }

          .room_list_wrapper {
            .total_occupancy_wrapper {
              .rooms_number, .occupancy_number {
                .label_field {
                  font-size: 14px;
                }

                .booking_value {
                  font-size: 16px;
                }
              }
            }
          }

          .wrapper_booking_button {
            .promocode_wrapper {
              &::before {
                content: "PROMOCODE";
                font-family: $font_2;
                font-size: 16px;
                font-weight: 500;
                position: absolute;
                top: -15px;
                left: 18px;
              }
            }
          }
        }
      }
    }
  }
}