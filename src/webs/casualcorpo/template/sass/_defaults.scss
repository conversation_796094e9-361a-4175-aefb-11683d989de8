//Base web (change too in config.rb)
$base_web: "casuo";

$is_mobile: false !default;

//@import url('https://fonts.googleapis.com/css?family=Caveat+Brush|Montserrat300,400,600,700&,900display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;300;400;500;600;700;800&display=swap');

// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #D80472;
$corporate_2: #00A5D2;
$corporate_3: #3FAB37;
$corporate_4: #E0DE08;
$black: #141414;
$lightgrey: #f3f3f3;
$grey: #999999;
$grey2: #707070;
$grey3: rgba(48, 57, 72, 0.22);
$color_text: $black;

$title_family: "Montserrat", Sans-serif;
$text_family: "Montserrat", Sans-serif;
$btn_family: $text_family;
$font_2: 'Open Sans', sans-serif;

$border_radius: 10px;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

@mixin ellipsis($lines) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $lines; /* -> number of lines to show, NO height required in the container <- */
    -webkit-box-orient: vertical;

    hr {
        display: none;
    }
}

@mixin title_styles() {
  font-family: $title_family;
  font-size: 68px;
  font-weight: 400;
  line-height: 73px;
  color: $black;
  text-align: left;
  padding-bottom: 15px;
  display: block;
  margin: 0;

  @if $is_mobile {
    font-size: 46px;
    line-height: 52px;
  }
}

@mixin title_styles_2() {
  font-family: $title_family;
  font-weight: 700;
  color: white;
  text-align: left;
  display: block;
  margin: 0;
  font-size: 90px;
  line-height: 96px;

  small {
    display: block;
    font-weight: 400;
    font-size: 36px;
    font-family: $text_family;
    text-transform: uppercase;
  }

  @if $is_mobile {
    font-size: 54px;
    line-height: 60px;

    small {
      font-size: 26px;
      line-height: 32px;
    }
  }
}

@mixin banner_title_styles($size: 22px, $color: $color_text) {
  display: block;
  font-family: $text_family;
  font-size: $size;
  font-weight: 700;
  line-height: calc(#{$size} + 8px);
  color: $black;
  text-align: left;
  margin: 0;
}

@mixin text_styles($size: 17px, $color: $color_text) {
  display: block;
  font-family: $text_family;
  font-weight: 400;
  font-size: $size;
  line-height: calc(#{$size} + 10px);
  color: $color;
  text-align: left;
}

@mixin btn_styles() {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  color: white;
  font-size: 21px;
  line-height: 26px;
  font-weight: 700;
  letter-spacing: .25px;
  background-color: $corporate_1;
  font-family: $btn_family;
  padding: 10px 30px;
  border-radius: 4px;
  text-transform: none;
  border: none;
  margin: 0;
  overflow: hidden;
  cursor: pointer;
  @include transition(all, .6s);

  @media (max-width: 575px) {
    font-size: 18px;
    padding: 8px;
    min-width: 200px;
  }

  &:focus {
    outline: 0;
  }

  @if not $is_mobile {
    &:hover {
      background-color: #E34E9B;
      color: white;
    }

    &.white:hover {
      background-color: white;
    }
  }
}

@mixin btn_styles_2() {
  @include btn_styles;
  background-color: $corporate_2;

  &:before {
    display: inline-block;
    vertical-align: middle;
    content: '\f067';
    font-family: "Font Awesome 5 Pro";
    margin-right: 7px;
  }

  &:focus {
    outline: 0;
  }

  @if not $is_mobile {
    &:hover {
      background-color: #4CC3DF;
      color: white;
    }

    &.white:hover {
      background-color: white;
    }
  }
}

@mixin base_banner_styles() {
  position: relative;
  padding: 60px calc((100% - 1140px) / 2);
  width: 100%;
  overflow: hidden;
  justify-content: center;
}

@mixin owl_nav_styles($color: white) {
  font-size: 42px;
  color: $color;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  @include transition(all, .6s);

  &.disabled {
    opacity: 0;
    cursor: default;
  }

  i {
    font-weight: 400;
  }

  @if not $is_mobile {
    &:not(.disabled):hover {
      color: $corporate_1;
    }
  }
}

@mixin owl_dots_styles($color: $grey2) {
  display: inline-block;
  vertical-align: middle;
  float: none;
  margin-right: 10px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba($color, .5);
  @include transition(all, .2s);

  &:hover, &.active, &.selected {
    background-color: $color;
  }
}

@mixin newsletter_placeholder() {
  font-size: 21px;
  color: white;
  line-height: 20px;
  letter-spacing: 1.7px;
  font-weight: 400;
  font-family: $text_family;
  text-transform: none;
  opacity: 1;

  @if $is_mobile {
    font-size: 15px;
    line-height: 18px;
  }
}

@mixin box_shadow() {
  -webkit-box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.5);
  box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.5);
}

@mixin checkbox_styles() {
  -moz-appearance: none;
  -webkit-appearance: none;
  display: inline-block;
  vertical-align: top;
  width: 11px;
  height: 11px;
  border: none;
  box-sizing: border-box;
  background-color: white;
  cursor: pointer;
  position: relative;

  &:checked {
    &::before {
      position: absolute;
      font-family: "Font Awesome 5 Pro";
      content: '\f00c';
      color: $corporate-1;
      font-size: 16px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  &:focus {
    outline: none;
  }
}

@mixin fa_default_style() {
  font-family: "Font Awesome 5 Pro";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

@mixin animation_duration_steps ($items, $start_duration, $delay) {
  @for $i from 1 through $items {
    #{'&:nth-child(' + $i + ')'} {
      animation-duration: $start_duration + $delay * $i;
    }
  }
}