$widget_height: 94px;
$width_s: 160px;
$width_m: 230px;
$width_l: 270px;
$horizontal_padding: 15px;
$vertical_padding: 30px;
$widget_bg: rgba(255, 255, 255, 0.95);
$label_color: $black;
$option_color: $black;
$separator_color: $grey;
$border_radius: 10px;

@mixin input_base_styles() {
  position: relative;
  display: inline-block;
  text-align: left;
  float: left;
  background-color: $widget_bg !important;
  height: $widget_height;
  width: $width_l;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
}

@mixin label_styles() {
  display: block;
  font-size: 11px;
  line-height: 16px;
  color: $label_color;
  font-weight: 400;
  letter-spacing: 0.22px;
  font-family: $text_family;
  text-transform: uppercase;
  padding-bottom: 0;
  margin: 0;
  text-align: left;
}


@mixin option_styles() {
  position: relative;
  display: block;
  font-size: 17px;
  line-height: 22px;
  letter-spacing: 0.38px;
  color: $option_color;
  font-weight: 700;
  margin: 0;
  font-family: $title_family;
  text-transform: uppercase;
  text-align: left;
  cursor: pointer;
}

@mixin separator() {
  margin: 0;
  &:after {
    content: '';
    @include center_y;
    left: -1px;
    height: calc(#{$widget_height} - (#{$vertical_padding} - 5px));
    width: 1px;
    background: $separator_color;
  }
}

@mixin promocode_placeholder() {
  font-size: 10px;
  line-height: 13px;
  font-weight: 400;
  font-family: $text_family;
  text-align: center;
  color: $label_color;
}