<div id="occupancy_wrapper" class="hide">
    <div class="title_wrapper">{{ T_habitaciones_y_ocuapcion }}</div>
    <div class="occupancy_options">
        <div class="rooms_amount">
            <label for="">{{ T_habitaciones }}</label>
            <div class="modification_buttons">
                <i class="fal minus fa-minus-circle disabled"></i>
                <input type="text" target="numRooms" value="1" min-value="1" max-value="3" readonly>
                <i class="fal plus fa-plus-circle"></i>
            </div>
        </div>

        {% for room_index in [1,2,3,4,5,6,7,8] %}
            <div class="room_element_options room_num_{{ room_index }} {% if not loop.first %}hide{% endif %}">
                <p class="room_title">{{ T_habitacion }} {{ room_index }}</p>
                <div class="guest_controll adults_wrapper">
                    <label for="">{{ T_adultos }}</label>
                    <div class="modification_buttons">
                        <i class="fal minus fa-minus-circle"></i>
                        <input type="text" class="input_simulator" target="adultsRoom{{ room_index }}" value="2" min-value="1" max-value="8" readonly>
                        <i class="fal plus fa-plus-circle"></i>
                    </div>
                </div>

                <div class="guest_controll kids_wrapper">
                    <label for="">{{ T_ninos }}</label>
                    <div class="modification_buttons">
                        <i class="fal minus fa-minus-circle disabled"></i>
                        <input type="text" class="input_simulator" target="childrenRoom{{ room_index }}" value="0" min-value="0" max-value="4" readonly>
                        <i class="fal plus fa-plus-circle"></i>
                    </div>

{#                    {% include "booking_engine/_ages_selector.html" %}#}
                </div>

                {% if showBabies %}
                    <div class="guest_controll babies_wrapper">
                        <label for="">{{ T_bebes }}</label>
                        <div class="modification_buttons">
                            <i class="fal minus fa-minus-circle disabled"></i>
                            <input type="text" class="input_simulator" target="babiesRoom{{ room_index }}" value="0" min-value="0" max-value="1" readonly>
                            <i class="fal plus fa-plus-circle"></i>
                        </div>
                    </div>
                {% endif %}

                {% if showPets %}
                    <div class="guest_controll pets_wrapper">
                        <label for="">{{ T_mascotas }}</label>
                        <div class="modification_buttons">
                            <i class="fal minus fa-minus-circle disabled"></i>
                            <input type="text" class="input_simulator" target="petsRoom{{ room_index }}" value="0" min-value="0" max-value="{{ showPets }}" readonly>
                            <i class="fal plus fa-plus-circle"></i>
                        </div>
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>