<div class="jobs_full_wrapper">

        <div id="filter_wrapper_banner">
            <div class="banner_title">
                <i class="fas fa-sliders-h"></i>
                {{ T_filtrar_su_busqueda }}
            </div>

            <div class="availabler_filters_wrapper">
                <div class="destiny_filters_wrapper filter_block">
                    <div class="filter_title">
                        {{ T_por_destino }}
                        <i class="fas more fa-plus-circle"></i>
                        <i class="fal less fa-minus-circle"></i>
                    </div>
                    <div class="options_list">
                        {% for destiny in job_destinies %}
                            <div class='option_element'>
                                <input type='checkbox' id="destiny_filter_{{ loop.index }}" name='destinies_filter' value="{{ destiny.destiny_class_name|safe }}">
                                <label for="destiny_filter_{{ loop.index }}">{{ destiny.title|safe }} ({{ destiny.jobs_number }} {% if destiny.jobs_number == 1 %}{{ T_oferta }}{% else %}{{ T_ofertas }}{% endif %})</label>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <div class="laboral_offer_type filter_block">
                    <div class="filter_title">
                        {{ T_oferta_laboral }}
                        <i class="fas more fa-plus-circle"></i>
                        <i class="fal less fa-minus-circle"></i>
                    </div>
                    <div class="options_list"></div>
                </div>
            </div>

            <div id="clear_filters_button">{{ T_borrar_filtros }}</div>
        </div>

    <div class="work_wrapper">
        <div class="job_offers_found">
            <span class="jobs_number"></span> {{ T_trabajos_encontrados }}
        </div>
        <div class="available_offers_wrapper">
            {% for job_element in job_promotions %}
                <div class="job_element {% if loop.index > 7 %}start_hide{% endif %}" {% if job_element.destiny_info and job_element.destiny_info[0].title %}data-destiny="{{ job_element.destiny_info[0].title|safe }}"{% endif %}
                     {% if job_element.namespace and job_element.namespace[0] %}data-namespace="{{ job_element.namespace[0]|safe }}"{% endif %}
                     type="{{ job_element.type|safe }}"
                     {% if job_element.destiny_info and job_element.destiny_info.0.destiny_class_name %}destiny="{{ job_element.destiny_info.0.destiny_class_name|safe }}"{% endif %}>
{#                    <div class="image_wrapper">#}
{#                        <img src="{{ job_element.servingUrl }}">#}
{#                    </div>#}
                    <div class="job_content">
                        <div class="job_header">
                            <h4 class="destiny_title">
                                {% for destiny in job_element.destiny_info %}
                                    <span>
                                        {{ destiny.title|safe }}
                                    </span>
                                {% endfor %}
                            </h4>
                            <h3 class="job_title">{{ job_element.title|safe }}</h3>
                            <div class="hotels_wrapper">
                                {% for hotel_title in job_element.hotels_available %}
                                    <span class="hotel_name">
                                        {{ hotel_title|safe }}
                                    </span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="job_description">
                            {{ job_element.description|safe }}
                        </div>
                        <div class="read_more">
                            <p class="more">{{ T_leer_mas }}</p>
                            <p class="less">{{ T_leer_menos }}</p>
                        </div>

                        <div class="buttons_wrapper">
                            <div class="subscribe_job_wrapper">
                              {% if job_element.custom_link %}
                                <a class="link_job" href="{{ job_element.custom_link }}" {% if 'http' in job_element.custom_link %}target="_blank"{% endif %}>{{ T_inscribirme }}</a>
                              {% else %}
                                <button class="subscribe_job" {% if job_element.job_classname %}data-job_name="{{ job_element.job_classname|safe }}"{% endif %}>{{ T_inscribirme }}</button>
                              {% endif %}
                            </div>
                            {% if job_element.popup_info %}
                                <div class="see_more_job_description">{{ T_ver_mas }}</div>
                            {% endif %}
                        </div>

                        <div class="more_info_job_wrapper">
                            {{ job_element.popup_info|safe }}
                        </div>

                    </div>
                </div>
            {% endfor %}
        </div>
        <a class="btn_personalized_2 btn_show_more_jobs">
            {{ T_ver_mas }}
        </a>
    </div>
</div>
<script type="text/javascript" src="/js/{{ base_web }}/job_application.js?v=1.04"></script>
<script>
    $(function () {


        $('.jobs_full_wrapper .btn_show_more_jobs').on('click', function () {
            let offers_wrapper = $('.jobs_full_wrapper .available_offers_wrapper');
            let hided_offers = $('.jobs_full_wrapper .job_element.start_hide');

            if (offers_wrapper.hasClass("all_offers")) {
                hided_offers.css('visibility', 'hidden')

                offers_wrapper.animate({height: '1490px'}, 'slow')
                offers_wrapper.removeClass("all_offers");
                $(this).text("{{ T_ver_mas }}");
            }
            else {
                hided_offers.css('visibility', 'visible')

                let curr_height = offers_wrapper.height(),
                    autoHeight = offers_wrapper.css('height', 'auto').height();

                offers_wrapper.height(curr_height).animate({height: autoHeight}, 'slow');
                offers_wrapper.addClass("all_offers");
                $(this).text("{{ T_ver_menos }}");
            }
        });
    });
</script>
