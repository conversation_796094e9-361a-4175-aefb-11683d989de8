<div class="banner_hotels_destiny_full_wrapper {% if banner_hotels_destiny|length > 1 %}has_carousel{% endif %}">
    {% if content_subtitle and content_subtitle.title %}
        <h3 class="content_title">
            {{ T_hotels_at }} {{ content_subtitle.title|safe }}
        </h3>
    {% endif %}
    <div class="banner_hotels_destiny_wrapper {% if is_mobile %}mobile{% endif %} {% if (is_mobile and banner_hotels_destiny|length > 1) or not is_mobile %}owl-carousel{% endif %}">
        {% for hotel in banner_hotels_destiny %}
            <div class="banner">
                {% if hotel.main_image %}
                    <div class="hotel_gallery owl-carousel">
                        <div class="img_wrapper">
                            <img src="{{ hotel.main_image|safe }}=s600" alt="{{ hotel.name|striptags|safe }}">
                        </div>
                        {% for pic in hotel.gallery %}
                            <div class="img_wrapper">
                                <img src="{{ pic.servingUrl|safe }}=s600" {% if pic.altText %}alt="{{ pic.altText|safe }}"{% endif %}>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="hotel_content">
                    {% if hotel.name %}
                        <span class="title">{{ hotel.subtitle|safe }}</span>
                    {% endif %}
                    {% if hotel.description %}
                        <div class="text">
                            {{ hotel.description|safe }}
                        </div>
                    {% endif %}
                    <div class="buttons_wrapper">
                        {% if not hotel.no_info %}
                            <a href="{{ hotel.friendlyUrlInternational|safe }}" class="btn_personalized_2">{{ T_ver_hotel }}</a>
                        {% endif %}
                        {% if not hotel.closed_label and not hotel.closed_message %}
                            <a href="#data" class="button-promotion btn_personalized_1" data-namespace="{{ hotel.namespace|safe }}">
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).on('load', function () {
        $(".banner_hotels_destiny_wrapper.owl-carousel").owlCarousel({
            loop: false,
            nav: {% if is_mobile %} false {% else %} true {% endif %},
            navText: ['<i class="far fa-chevron-left"></i>', '<i class="far fa-chevron-right"></i>'],
            dots: false,
            items: {% if is_mobile %}1{% else %}2{% endif %},
            margin: {% if is_mobile %}30{% else %}20{% endif %},
            navSpeed: 500,
            autoplay: true,
            mouseDrag: {% if banner_hotels_destiny|length < 3 %} false {% else %} true {% endif %},
            autoHeight: {% if is_mobile %} true {% else %} false {% endif %},
            stagePadding: {% if is_mobile %}30{% else %}0{% endif %}
        });

        $(".hotel_gallery.owl-carousel").owlCarousel({
            loop: true,
            nav: false,
            dots: true,
            items: 1,
            navSpeed: 500,
            autoplay: false,
            mouseDrag: {% if is_mobile %} false {% else %} true {% endif %},
            touchDrag: {% if is_mobile %} false {% else %} true {% endif %},
        });
    });
</script>