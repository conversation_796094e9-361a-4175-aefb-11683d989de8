<div class="news_widget_wrapper">
    <div class="entry_widget big_entry">
        <div class="image">
            {% if news.0 and news.0.picture %}
            <a href="/{{ path }}/{{ news.0.friendlyUrl }}">
                <img src="{{ news.0.picture|safe }}{% if 'cdn2.paraty' in news.0.picture %}=s800{% endif %}" alt="">
            </a>
            {% endif %}
        </div>
        <div class="content">
            {% if news.0.creationDate %}
                <div class="date"><span>{{ news.0.creationDate|safe }}</span></div>
            {% endif %}
            <div class="title">{{ news.0.name|safe }}</div>
            <div class="entry_desc">{{ news.0.shortDesc|safe }}</div>
            <a href="/{{ path }}/{{ news.0.friendlyUrl }}" class="btn_personalized_2"><span>{{ T_leer_mas }}</span></a>
            <div class="tags">{% for tag in news.0.tag_list %}{{ tag|safe }}{% endfor %}</div>
        </div>
    </div>
    <div class="news_list">
        {% for item in news %}
            <div class="entry_widget {% if loop.index < 7 %}visible{% endif %}">
                <div class="image">
                    <a href="/{{ path }}/{{ item.friendlyUrl }}">
                        <img src="{{ item.picture|safe }}{% if item.picture and 'cdn2.paraty' in item.picture %}=s400-c{% endif %}">
                    </a>
                </div>
                <div class="content">
                    {% if item.creationDate %}
                        <div class="date"><span>{{ item.creationDate|safe }}</span></div>
                    {% endif %}
                     <a href="/{{ path }}/{{ item.friendlyUrl }}">
                    <div class="title">{{ item.name|safe }}</div>
                         </a>
                    <div class="tags">{% for tag in item.tag_list %}<span>{{ tag|safe }}</span>{% endfor %}</div>
                </div>
            </div>
        {% endfor %}
    </div>
    {% if news|length > 7 %}
        <div class="buttons_wrapper">
            <a class="btn_personalized_2 btn_more_news">
                {{ T_load_more }}
            </a>
        </div>
    {% endif %}
</div>

<script>
    $(function () {
        $('.news_widget_wrapper .btn_more_news').on('click', function () {
            let news = $('.news_widget_wrapper .news_list .entry_widget:not(".visible")');
            if (news.length <= 6) {
                news.addClass('visible');
                $(this).fadeOut();
            } else {
                news.slice(0, 6).addClass('visible');
            }
        });
    });
</script>