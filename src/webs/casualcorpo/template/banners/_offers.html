<div class="offers_full_wrapper">
    {% if hotels_grouped %}
        <div class="offers_filters_wrapper">
            <div class="offer_filter destiny">
                <i class="fal fa-map-marker-alt"></i>
                <select name="offer_destiny_filter" id="offer_destiny_filter">
                    <option value="all_destinies" selected>{{ T_todos_destinos }}</option>
                    {% for destiny, destiny_info in hotels_grouped.items() %}
                        <option value="{{ destiny_info.class_name|safe }}">{{ destiny|safe }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="offer_filter hotels">
                <i class="fal fa-hotel"></i>
                <select name="offer_hotels_filter" id="offer_hotels_filter">
                    <option value="all_hotels" selected>{{ T_todos_hoteles_2 }}</option>
                    {% for destiny, destiny_info in hotels_grouped.items() %}
                        {% for hotel in destiny_info.hoteles %}
                            <option value="{{ hotel.namespace|safe }}" class="{{ destiny_info.class_name|safe }}">{{ hotel.name|safe }}</option>
                        {% endfor %}
                    {% endfor %}
                </select>
            </div>
        </div>
    {% endif %}
    <div class="offers_wrapper">
        {% for offer in offers %}
            <div class="offer {{ offer.namespace }} {{ offer.destiny }} all_destinies all_hotels" {% if offer.startDate %}data-startDate="{{ offer.startDate }}"{% endif %}{% if offer.endDate %} data-endDate="{{ offer.endDate }}"{% endif %}>
                {% if offer.picture %}
                    <div class="offer_pic">
                        <img src="{{ offer.picture|safe }}">
                        <div class="pic_content">
                            {% if offer.name %}
                                <span class="title">{{ offer.name|safe }}</span>
                            {% endif %}
                            {% if offer.picDesc %}
                                <div class="text">{{ offer.picDesc|safe }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                <div class="offer_content">
                    {% if offer.description %}
                        <div class="text">{{ offer.description|safe }}</div>
                    {% endif %}
                    {% if not is_mobile %}
                        <a class="offer_button btn_personalized_2" data-namespace="{{ offer.namespace|safe }}">{{ C_ver_disponibilidad|safe }}</a>
                    {% endif %}
                    <a href="#data" class="button-promotion btn_personalized_1" data-namespace="{{ offer.namespace|safe }}">
                        {{ T_reservar }}
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<input type="hidden" name="btn_text" value="{{ C_ver_disponibilidad|safe }}">
<input type="hidden" name="book_text" value="{{ T_reservar }}">
{% if not is_mobile %}
<script type="text/javascript" src="/js/{{ base_web }}/offers.js?v=1.1"></script>
{% endif %}

<script>
    $(window).on("load", function() {
        $("#offer_destiny_filter, #offer_hotels_filter").change(function () {
            const destiny_selected = $("#offer_destiny_filter").val();
            let hotel_selected = $("#offer_hotels_filter").val();

            if (destiny_selected !== "all_destinies") {
                $("#offer_hotels_filter option").removeAttr("disabled")
                $("#offer_hotels_filter option:not(." + destiny_selected + ")").attr("disabled", "disabled");
                $("#offer_hotels_filter option." + destiny_selected).first().attr("selected", "selected");
                hotel_selected = $("#offer_hotels_filter").val();
            } else {
                $("#offer_hotels_filter option").removeAttr("disabled");
            }
            const target_classes = `.${destiny_selected}.${hotel_selected}`;

            $(".offers_wrapper .offer").slideUp().promise().done(function () {
                $(".offers_wrapper .offer" + target_classes).slideDown();
            })
        });
    })
</script>