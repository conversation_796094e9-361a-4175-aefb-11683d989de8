<div class="banner_hotels_full_wrapper">
    <div class="top_content">
        <h3 class="content_title">{{ T_hoteles }}</h3>
        <ul class="filter_hotels_wrapper">
            <li class="filter_item active" data-filter="open">
                {{ T_todos_hoteles_3 }}
            </li>
            <li class="filter_item" data-filter="closed">
                {{ T_nuevas_aperturas }}
            </li>
        </ul>
    </div>
    <div class="banner_hotels_wrapper open owl-carousel">
        {% for hotel in banner_all_hotels %}
            {% if not hotel.closed_label %}
                <div class="banner">
                    {% if hotel.closed_label %}
                        <span class="label_closed">{{ hotel.closed_label|safe }}</span>
                    {% endif %}
                    {% if hotel.main_image %}
                        <img src="{{ hotel.main_image|safe }}=s400" alt="{{ hotel.name|striptags|safe }}">
                    {% endif %}
                    {% if hotel.name %}
                        <span class="title">{{ hotel.subtitle|safe }}</span>
                    {% endif %}
                    <div class="hidden_content">
                        {% if not hotel.closed_label %}
                            <a href="#data" class="button-promotion btn_personalized_1" data-namespace="{{ hotel.namespace|safe }}"
                            {% if default_promocode %}data-smartpromocode="{{ default_promocode|safe }}"{% endif %}
                            >
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                        {% if hotel.link and not hotel.no_info %}
                            <br>
                            <a href="{{ hotel.link|safe }}" class="btn_personalized_2">
                                {{ T_ver_hotel }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>
    <div class="banner_hotels_wrapper close owl-carousel hide">
        {% for hotel in banner_all_hotels %}
            {% if hotel.closed_label %}
                <div class="banner">
                    {% if hotel.closed_label %}
                        <span class="label_closed">{{ hotel.closed_label|safe }}</span>
                    {% endif %}
                    {% if hotel.main_image %}
                        <img src="{{ hotel.main_image|safe }}=s400" alt="{{ hotel.name|striptags|safe }}">
                    {% endif %}
                    {% if hotel.name %}
                        <span class="title">{{ hotel.subtitle|safe }}</span>
                    {% endif %}
                    <div class="hidden_content">
                        {% if not hotel.closed_label %}
                            <a href="#data" class="button-promotion btn_personalized_1" data-namespace="{{ hotel.namespace|safe }}">
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                        {% if hotel.link and not hotel.no_info %}
                            <br>
                            <a href="{{ hotel.link|safe }}" class="btn_personalized_2">
                                {{ T_ver_hotel }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>
    {% if all_hotels_link %}
        <div class="bottom_links">
            <a href="{{ all_hotels_link }}" class="btn_personalized_2">{{ T_ver_todos }}</a>
        </div>
    {% endif %}
</div>

<script>
  $(window).on('load', function () {
    $(".banner_hotels_wrapper.owl-carousel").owlCarousel({
      loop: false,
      nav: true,
      dots: false,
      {% if  is_mobile %}
        items: 1,
      {% endif %}
      navText: ['<i class="far fa-chevron-left"></i>', '<i class="far fa-chevron-right"></i>'],
      margin: 10,
      navSpeed: 1000,
      autoHeight: false,
      autoplay: false,
      {% if not is_mobile %}
        stagePadding: 100,
        responsive: {
          // breakpoint from 0 up
          0: {
            items: 4,
          },
          // breakpoint from 1400 up
          1500: {
            items: 6,
          }
        }
      {% endif %}

    });
  });

  let filter = $('.banner_hotels_full_wrapper .top_content .filter_hotels_wrapper .filter_item');
  let item = $('.banner_hotels_full_wrapper .banner_hotels_wrapper .banner');

  filter.click(function () {
    let filter_data = $(this).data('filter');
    $(this).addClass('active').siblings().removeClass('active');

    item.each(function () {
      if (filter_data == 'open') {
          $('.banner_hotels_wrapper.open').removeClass('hide');
          $('.banner_hotels_wrapper.close').addClass('hide');
      } else {
          $('.banner_hotels_wrapper.open').addClass('hide');
          $('.banner_hotels_wrapper.close').removeClass('hide');
      }
    })
  });
</script>