<div class="banner_gallery_full_wrapper main_layer" id="banner_gallery_full_wrapper">
    <h3 class="content_title">
        {{ C_galeria|safe }}
    </h3>
    <div class="banner_gallery_wrapper owl-carousel">
        {% for banner in hotel_gallery %}
            {% if loop.first %}
                <div class="banner_gallery_group_wrapper">
            {% endif %}
            {% if banner.title == 'video' %}
                <a data-src="{{ banner.linkUrl|safe }}" data-fancybox data-type="iframe" class="myFancyPopup fancybox.iframe banner">
                   <span class="img_content">
                       <i class="far fa-play-circle play_icon"></i>
                   </span>
                    <img src="{% if is_mobile %}{{ banner.servingUrl }}=s600{% else %}{{ banner.servingUrl }}=s1900{% endif %}" {% if banner.altText %}alt="{{ banner.altText|safe }}"{% endif %}>
                </a>
            {% else %}
                <a {% if is_mobile %}href="{{ banner.servingUrl|safe }}=s600" data-lightbox="banner_gallery" {% else %}href="{{ banner.servingUrl|safe }}=s1900" rel="lightbox[banner_gallery]"{% endif %}
               class="banner">
                        <span class="img_content">
                            <i class="fal fa-search-plus serarch_icon"></i>
                        </span>
                <img src="{{ banner.servingUrl|safe }}=s{% if is_mobile %}5{% else %}19{% endif %}00"
                     {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                </a>
            {% endif %}
            {% if loop.index %4 == 0 and not (loop.first or loop.last) %}</div>
                <div class="banner_gallery_group_wrapper">{% endif %}
            {% if loop.last %}</div>{% endif %}
        {% endfor %}
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js"></script>
<script>
    $(window).on('load', function () {
        $(".banner_gallery_wrapper").owlCarousel({
            loop: false,
            nav: true,
            startPosition: 0,
            dots: true,
            items: 1,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            navSpeed: 500,
            autoplay: false,
        });
    });
</script>