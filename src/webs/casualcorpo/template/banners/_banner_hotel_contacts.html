<div class="banner_hotel_contacts">
    <div class="buttons_wrapper">
        <a class="btn_personalized_2 btn_show_contacts">
            {{ banner_hotel_contacts.subtitle|safe }}
        </a>
    </div>
    <div class="contacts_wrapper" style="display: none;">
        {% for hotel in all_hotels %}
            <div class="hotel_contact_wrapper">
                <div class="hotel_title">
                    {{ hotel.subtitle|safe }}
                </div>
                {% if hotel.address or hotel.map %}
                    <div class="hotel_address">
                        <div class="icon">
                            <i class="fal fa-map-marker-alt"></i>
                        </div>
                        <div class="desc">
                            {% if hotel.address %}
                                <div class="address">{{ hotel.address|safe }}</div>
                            {% endif %}
                            {% if hotel.map %}
                                <span class="open_modal btn_see_map">{{ T_ver_mapa }}</span>
                                {% if is_mobile %}
                                    <modal style="display: none">{{ hotel.map|safe }}</modal>
                                {% else %}
                                    <hide style="display: none">{{ hotel.map|safe }}</hide>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                <div class="hotel_contacts">
                    {% if hotel.phone %}
                        <a href="tel:{{ hotel.phone|safe }}" class="hotel_contact phone">
                            <div class="icon">
                                <i class="fal fa-phone"></i>
                            </div>
                            <div class="desc">
                                {{ hotel.phone|safe }}
                            </div>
                        </a>
                    {% endif %}
                    {% if hotel.whatsapp %}
                        <a href="https://wa.me/{{ hotel.whatsapp|replace('+', '')|replace(' ', '')|safe }}?text={{T_hola}}" target="_blank" class="hotel_contact whatsapp">
                            <div class="icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="desc">
                                {{ hotel.whatsapp|safe }}
                            </div>
                        </a>
                    {% endif %}
                    {% if hotel.email %}
                        <a href="mailto:{{ hotel.email|safe }}" class="hotel_contact email">
                            <div class="icon">
                                <i class="fal fa-envelope"></i>
                            </div>
                            <div class="desc">
                                {{ hotel.email|safe }}
                            </div>
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>
<script>
    $(function () {
        $('.banner_hotel_contacts .btn_show_contacts').on('click', function () {
            $('.banner_hotel_contacts .contacts_wrapper').slideToggle();
        });
    });
</script>