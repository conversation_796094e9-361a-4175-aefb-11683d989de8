{% if content_subtitle %}
    {% if section_namespace %}
        <input id="section_namespace" type="hidden" value="{{ section_namespace }}">
    {% endif %}
    <div class="hotel_content_wrapper main_layer" id="hotel_content_wrapper">
        {% if content_subtitle.subtitle %}
            <div class="content_title">
                <h1 class="hotel_subtitle">
                    {{ content_subtitle.subtitle|safe }}
                </h1>
                {% if destiny %}
                    <span class="destiny">
                        <i class="fas fa-map-marker"></i>
                        {{ destiny|safe }}
                        {% if country %} ({{ country|safe }}){% endif %}
                    </span>
                {% endif %}
            </div>
        {% endif %}
        <div class="hotel_sections_links" id="hotel_sections_links">
            <span class="link active" data-target="hotel_content_wrapper">{% if hotel_traduction %} {{ hotel_traduction }}{% else %}{{ C_hotel|safe }}{% endif %}</span>
            {% if services_icons_hotel %}
                <span class="link" data-target="services_icons_hotel_full_wrapper">{{ T_servicios }}</span>
            {% endif %}
            {% if hotel_gallery %}
                <span class="link" data-target="banner_gallery_full_wrapper">{{ C_galeria|safe }}</span>
            {% endif %}
            {% if hotel_rooms %}
                <span class="link" data-target="hotel_rooms_full_wrapper">{{ T_habitaciones }}</span>
            {% endif %}
            {% if hotel_offers %}
                <span class="link" data-target="hotel_offers_full_wrapper">{{ T_ofertas }}</span>
            {% endif %}
        </div>

        <div class="hotel_content">
            {% if content_subtitle.content or closed_message %}
                <div class="hotel_description">
                    {{ content_subtitle.content|safe }}
                    {% if '<hide>' in content_subtitle.content %}
                        <a href="#" class="open_hotel_description_modal">{{ T_leer_mas }}</a>
                    {% endif %}
                </div>
            {% endif %}
            {% if address %}
                <span class="hotel_info address">
                    <i class="fal fa-map-marker-alt"></i>{{ address|safe }}
                    {% if hotel_iframe_map %}
                        <span class="open_modal see_map">{{ T_ver_mapa }}</span>
                        <hide style="display: none">
                            {{ hotel_iframe_map|safe }}
                        </hide>
                    {% endif %}
                </span>
            {% endif %}
            {% if phone %}
                <span class="hotel_info phone"><i class="fal fa-phone"></i>{{ phone|safe }}</span>
            {% endif %}
            {% if whatsapp %}
                <span class="hotel_info whatsapp"><i class="fab fa-whatsapp"></i>{{ whatsapp|safe }}</span>
            {% endif %}
            {% if email %}
                <span class="hotel_info email"><i class="fal fa-envelope"></i>{{ email|safe }}</span>
            {% endif %}
            {% if register_number %}
                <span class="hotel_info register"><i class="fal fa-cabinet-filing"></i>{{ register_number|safe }}</span>
            {% endif %}
            {% if extra_url and extra_url_text %}
                <a class="hotel_info extra_link" href="{{ extra_url|safe }}" target="_blank"><i class="fal fa-file"></i>{{ extra_url_text|safe }}</a>
            {% endif %}
            {% if logo_kayak %}
                <a class="logo_kayak" href="{{ logo_kayak }}" target="_blank"><img src="https://storage.googleapis.com/cdn.paraty.es/casual-corporativa/files/DARK_MEDIUM_TRAVEL_AWARDS.png"></a>
            {% endif %} 
        </div>

        {% if hotel_main_img %}
            <div class="hotel_main_img">
                <a href="{{ hotel_main_img|safe }}" class="img_wrapper" {% if is_mobile %}data-fancybox{% else %}rel="lightbox"{% endif %}>
                    <img src="{{ hotel_main_img|safe }}=s800">
                </a>
            </div>
        {% endif %}
    </div>
{% endif %}

{% if services_icons_hotel %}
    {% include "banners/_services_icons_hotel.html" %}
{% endif %}

{% if banner_cycle_pics %}
    {% include "banners/_banner_cycle.html" %}
{% endif %}

{% if hotel_gallery %}
    {% include "banners/_hotel_gallery.html" %}
{% endif %}

{% if hotel_rooms %}
    {% include "banners/_hotel_rooms.html" %}
{% endif %}

{% if hotel_offers %}
    {% include "banners/_hotel_offers.html" %}
{% endif %}

<script>
  $(window).on('load', function () {
    $("#hotel_sections_links .link").click(function () {
      const target = $(this).data("target");
      $("#hotel_sections_links .link").removeClass("active");
      $(this).addClass("active");

      let scrollTop;

      {% if is_mobile %}
        scrollTop = $("#" + target).offset().top;
      {% else %}
        scrollTop = $("#" + target).offset().top - $("#full_wrapper_booking").outerHeight() - $("#hotel_sections_links").outerHeight();
      {% endif %}

      $('html, body').stop().animate({
        scrollTop: scrollTop
      }, $(document).width() < 1180 ? 0 : 1500);
    })

    {% if not is_mobile %}
      fixHotelLinks();
      changeActiveHotelSectionLink();
    {% endif %}


    $(".open_hotel_description_modal").click(function (e) {
      e.preventDefault();
      let modal_content = $(this).closest('.hotel_description').html();
      $(".modal .content").html(modal_content);
      $(".modal").addClass("active");
    });
  })

    {% if not is_mobile %}
        $(window).scroll(function () {
            fixHotelLinks();
            changeActiveHotelSectionLink();
        });
    {% endif %}

    function fixHotelLinks() {
        const actual_position = $(window).scrollTop();
        const hotel_links_position = $("#hotel_content_wrapper").offset().top + $("#hotel_content_wrapper .content_title").outerHeight() - $("#full_wrapper_booking").outerHeight();
        const hotel_sections_links = $("#hotel_sections_links");
        const hotel_links_fixed = hotel_sections_links.hasClass('fixed');

        if ((actual_position > hotel_links_position) && (!hotel_links_fixed)) {
            hotel_sections_links.addClass('fixed');
        }
        if ((actual_position < hotel_links_position) && (hotel_links_fixed)) {
            hotel_sections_links.removeClass("fixed");
        }
    }

    function changeActiveHotelSectionLink() {
        $("#hotel_sections_links .link").each(function () {
            const link_target = $(this).data("target");
            const target_top = $("#" + link_target).offset().top - $("#full_wrapper_booking").outerHeight() - $("#hotel_sections_links").outerHeight() - 10;
            const actual_position = $(window).scrollTop();

            if (actual_position > target_top) {
                $("#hotel_sections_links .link").removeClass("to_active");
                $(this).addClass("to_active");
            }
        })

        $("#hotel_sections_links .link.active").removeClass("active");
        $("#hotel_sections_links .link.to_active").addClass("active");
    }
</script>