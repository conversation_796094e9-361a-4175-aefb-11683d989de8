<div class="contact_form_wrapper">
    <h3>{{ T_formulario_contacto }}</h3>
    <form name="contact" id="contact" method="post" action="/utils/?action=contact">
        <input type="hidden" name="action" id="action" value="contact"/>
        <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}">

        {% if destination_email %}
            <input type="hidden" name="destination_email" id="destemail" value="{{destination_email}}">
        {% endif %}
        <div class="info">
            <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

            <div class="contInput">
                <i class="fa fa-user" aria-hidden="true"></i>
                <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
            </div>
            <div class="contInput">
                <i class="fa fa-user" aria-hidden="true"></i>
                <input type="text" id="surname" name="surname" class="bordeInput" value=""
                       placeholder="{{ T_apellidos }}"/>
            </div>
            <div class="contInput">
                <i class="fa fa-phone" aria-hidden="true"></i>
                <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                       placeholder="{{ T_telefono }}"/>
            </div>
            <div class="contInput">
                <i class="fa fa-envelope-o" aria-hidden="true"></i>
                <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
            </div>
            <div class="contInput area">
                <i class="fa fa-comment" aria-hidden="true"></i>
                <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                          value=""
                          placeholder="{{ T_comentarios }}"></textarea>
            </div>

            {% if captcha_box %}
                <script src='https://www.google.com/recaptcha/api.js?hl={{ language_code }}'></script>
                <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
            {% endif %}

            <div data-role="fieldContain" class="check_element">
                <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                <span class="title">
                    <a class="myFancyPopup fancybox.iframe" data-fancybox
                       data-options='{"caption" : "{{ T_politica_de_privacidad }}", "src" : "/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                       data-width="1200"
                       href="/{{ language_code }}/?sectionContent={% if custom_lopd_link%}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}">{{ T_lopd }}</a>
                </span>
            </div>

            <div data-role="fieldContain" class="check_element">
                <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="promotions"/>
                <span class="title">
                    <label for="promotions">{{T_acepto_promociones}}</label>
                </span>
            </div>

            <div class="contact_button_wrapper">
                <a data-role="button" data-theme="b">
                    <div id="contact-button" class="btn_personalized_1">
                        {{ T_enviar }}
                    </div>
                </a>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">
$(window).load(function () {
    jQuery.validator.addMethod("phone", function (phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");
    $("#contact").validate({
        rules: {
            hotel: "required",
            name: "required",
            email: {
                required: true,
                email: true
            },
            emailConfirmation: {
                required: true,
                equalTo: "#email",
                email: true
            },
            telephone: {
                required: true,
                phone: true
            },
            comments: "required",
            privacy: "required"
        },
        messages: {
            hotel: "{{ T_campo_obligatorio}}",
            name: "{{ T_campo_obligatorio}}",
            email: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}"
            },
            emailConfirmation: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}",
                equalTo: "{{T_not_confirmed_email_warning|safe}}"
            },
            telephone: {
                required: "{{ T_campo_obligatorio|safe }}",
                phone: "{{ T_campo_valor_invalido|safe }}"
            },
            comments: "{{ T_campo_obligatorio|safe }}",
            privacy: "{{ T_campo_obligatorio|safe }}"
        }
    });
    sending_form = false;
    $("#contact-button").click(function () {
        if(!sending_form){
            sending_form = true;
            if ($("#contact").valid()) {
                var params = {
                    'name': $("#name").val(),
                    'telephone': $("#telephone").val(),
                    'email': $("#email").val(),
                    'comments': $("#comments").val(),
                    'section': $("#section-name").val(),
                    'newsletter': $("#promotions").val(),
                    'hotelQuery': $("#hotel").val(),
                    'has_resevation': $("#has_reservation").val(),
                    'destination_email': $("#destemail").val()
                };
                if (recaptcha_valid()) {
                    params['g-recaptcha-response'] = $("#g-recaptcha-response").val();
                }
                if (recaptcha_valid() || !has_recaptcha()) {
                    if ($("#motivo_consulta").val()) {
                        params['destination_email'] = $("#motivo_consulta").val();
                    }
                    $.post(
                        "/utils/?action=contact", params,
                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                            $("#name").val("");
                            $("#telephone").val("");
                            $("#email").val("");
                            $("#emailConfirmation").val("");
                            $("#comments").val("");
                            sending_form = false;
                        }
                    );
                } else {
                    sending_form = false;
                }


            } else {
                    sending_form = false;
                }
        }
    });

    function has_recaptcha() {
        return $(".g-recaptcha").length;
    }

    function recaptcha_valid() {
        return !has_recaptcha() || $("#g-recaptcha-response").val()
    }
});
</script>