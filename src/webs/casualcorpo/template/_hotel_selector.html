<div class="destination_wrapper">
    <div class="destination_field">
        <label for="destination">{{ T_seleccionar }}</label>
        <i class="fal fa-search icon_search"></i>
        <input class="destination" readonly="readonly" id="destination" type="text" name="destination"
               placeholder="{{ T_hotel }}/ {{ T_destino }}"/>
        <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder"
               class="default_destination_placeholder" value="{{ T_hotel }}/ {{ T_destino }}"/>
    </div>
</div>

<div class="hotel_selector" style="display: none;">
    <div class="close_hotel_selector"><span></span><span></span></div>
    <div class="hotel_selector_inner">
        {% for destiny, destiny_element in hotels_grouped.items() %}
            <div class="destiny_element">
                <div class="destiny"
                     {% if destiny_element.allow_b0 %}
                         data-namespace="{% for hotel in destiny_element.hoteles %}{% if not hotel.disabled_hotel and not hotel.no_info and not hotel.booking_disabled %}{% if hotel.remote %}r__{% endif %}{{ hotel.id }};{% endif %}{% endfor %}"
                         data-url_booking="/booking0"
                         data-destiny="{{destiny_element.data_name|safe|lower}}"
                     {% endif %}
                >
                    {{ destiny|safe }}
                </div>
                <div class="hotels_list">
                    {% for hotel in destiny_element.hoteles %}
                        {% if hotel.namespace and not hotel.no_info and not hotel.booking_disabled %}
                            <span id="{{ hotel.namespace }}" class="hotel_element hotel_selector_option {{ hotel.namespace }} {% if hotel.closed_label or hotel.info_label %}has_label {% endif %} {% if hotel.no_info %}no_info{% endif %}">
                                <span class="title_selector">{{ hotel.name|safe }}</span>
                                {% if hotel.closed_label %}
                                    <span class="label">{{ hotel.closed_label|safe }}</span>
                                {% endif %}
                                {% if hotel.info_label %}
                                    <span class="label">{{ hotel.info_label|safe }}</span>
                                {% endif %}
                                <input type="hidden" id="url_booking_{{ hotel.namespace }}" value="{{ hotel.url_booking }}">
                                <input type="hidden" id="namespace_{{ hotel.namespace }}" value="{{ hotel.namespace }}">
                                {% if hotel.multiple_rooms %}
                                    <input type="hidden" id="multiple_rooms" class="multiple_rooms" value="{{ hotel.multiple_rooms }}" >
                                {% endif %}
                            </span>
                        {% elif hotel.namespace %}
                            <span class="hotel_element {{ hotel.namespace }} {% if hotel.closed_label %}has_label{% endif %} {% if hotel.no_info %} no_info{% endif %}{% if hotel.booking_disabled %} booking_disabled{% endif %}">
                                <span class="title_selector">{{ hotel.name|safe }}</span>
                                {% if hotel.closed_label %}
                                    <span class="label">{{ hotel.closed_label|safe }}</span>
                                {% endif %}
                            </span>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>
