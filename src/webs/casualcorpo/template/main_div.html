<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        <div class="main-section-div-wrapper" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
            {% if section.subsections %}
                <a class="with_subsections">{{ section.title|safe }}</a>
            {% else %}
                {% if section.title %}
                    {% if section.replace_link %}
                        <a itemprop="url" href="{{ section.replace_link }}" target="_blank">
                            <span itemprop="name">{{ section.title|safe }}</span>
                        </a>
                    {% else %}
                        <a itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}">
                            <span itemprop="name">{{ section.title|safe }}</span>
                        </a>
                    {% endif %}
                {% endif %}
            {% endif %}
            {% if section.subsections %}
                <ul class="subsections">
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a {% if subsection.replace_link %} href="{{ subsection.replace_link|safe }}" target="_blank" {% elif not subsection.disabled %}itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"{% endif %} {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>