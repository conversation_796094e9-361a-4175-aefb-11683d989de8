$widget_height: 90px;
$horizontal_padding: 0;
$top_padding: 20px;
$bottom_padding: 20px;
$widget_bg: white;
$label_color: #a3a3a3;
$option_color: $corporate_2;

$box_shadow: 1px 1px 15px 3px rgba(0, 0, 0, .3);

@mixin label_styles() {
  display: block;
  font-size: 14px;
  color: $label_color;
  font-weight: 400;
  font-family: $text_family;
  text-transform: uppercase;
  padding-bottom: 0;
  text-align: left;
}

@mixin option_styles() {
  display: inline-block;
  font-size: 18px;
  color: $option_color;
  font-weight: 700;
  font-family: $text_family;
  text-transform: lowercase;
  padding-bottom: 5px;
  border-bottom: 1px solid $corporate_1;
  cursor: pointer;
}

@mixin icon_down() {
  position: relative;
  &:after {
    content: '\f078';
    position: absolute;
    right: 0;
    bottom: 3px;
    font-family: "Font Awesome 5 Pro";
    font-size: 12px;
    color: $label_color;
  }
}

@mixin promocode_placeholder() {
  font-size: 16px;
  line-height: 20px;
  font-weight: 400;
  font-family: $text_family;
  text-align: center;
  color: $label_color;
}