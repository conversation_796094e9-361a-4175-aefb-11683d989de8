@import url('https://fonts.googleapis.com/css?family=Caveat:400,700&display=swap');
$font-special-landing: 'Caveat', sans-serif;

body #slider_container.landing {
  #full_wrapper_booking.landing {
    display: none;
  }
  .inner_slider:before {
    z-index: 10;
  }
}

.landing_general_wrapper {
  display: table;
  width: 100%;

  .left_image {
    overflow: hidden;
    width: calc((100% / 2) - 10px);
    position: absolute;
    top: 0;
    bottom: 0;

    img {
      @include center_image;
    }

    &:before {
      content: "";
      background: url("/static_1/images/spiwak-landing-recursos-02.png");
      background-repeat: repeat-y;
      display: inline-block;
      position: absolute;
      width: 100%;
      height: 100%;
      left: 90%;
      top: 0;
      z-index: 2;
    }
  }
  .landing_wrapper {
    width: calc((100% / 2) - 10px);
    padding: 10% 0 0 0;
    float: right;

    .slider_countdown.landing {
      position: relative;
      color: black;
      bottom: unset;
      .seconds {
        display: none;
      }

      .days .date:after, .hours .date:after {
        content: ":";
        display: inline-block;
      }

      .date {
        font-weight: 400 !important;
        font-family: $font-special-landing;
      }

      .title_format {
        color: $corporate_1;
      }
    }

    .landing_video {
      position: fixed;
      z-index: 1020;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: black;
      .close_landing_video {
        display: block;
        position: absolute;
        z-index: 1021;
        font-size: 50px;
        color: white;
        cursor: pointer;
        top: 50px;
        right: 40px;
      }
    }

    .content_subtitle_title {
      font-size: 28px;
      position: relative;
      &:before, &:after {
        content: "";
        width: 60px;
        height: 2px;
        background: $corporate_1;
        display: block;
        top: 20px;
        position: absolute;
      }
      &:before {
        left: 150px;
      }
      &:after {
        right: 150px;
      }

      .title_edited {
        font-size: 70px;
        font-family: $font-special-landing;
        position: relative;
        margin: auto;
        width: 410px;
        &:before {
          content: "";
          background: url("/static_1/images/spiwak-landing-recursos-03.png");
          background-repeat: no-repeat;
          display: inline-block;
          position: absolute;
          width: 100%;
          height: 100px;
          left: 10px;
          top: 5px;
          z-index: -1;
        }
      }
    }

    .content_subtitle_title {
      font-family: $text_family;
    }

    .content_subtitle_description {
      .night_text {
        position: relative;
        &:before {
          content: "";
          background: url("/static_1/images/spiwak-landing-recursos-04.png");
          background-repeat: no-repeat;
          display: inline-block;
          position: absolute;
          width: 100%;
          height: 100px;
          left: 70px;
          top: -8px;
          z-index: -1;
        }
      }
      .btn_wrapper {
        display: flex;
        justify-content: center;
        .btn_personalized_1 {
          white-space: unset;
          display: flex;
          align-items: center;
          font-size: 18px;
          padding: 10px 45px;
          font-weight: 600;
          width: 210px;
          justify-content: center;
          &.whatsapp_btn {
            padding: 15px 35px 15px 60px;
            display: flex;
            font-size: 14px;
            align-items: center;
            i {
              position: absolute;
              left: 20px;
              &:before {
                font-size: 30px;
              }
            }
          }
          &.video_btn {

          }
        }
      }
      .back_offer_btn {
        text-decoration: none;
        border: none;
        font-size: 18px;
        margin-top: 15px;
        color: gray;
        cursor: pointer;
        &:before {
          content: "\f060";
          display: inline-block;
          font-size: 20px;
          font-family: "Font Awesome 5 Pro", sans-serif;
          margin-right: 20px;
        }
      }
    }
  }
}





