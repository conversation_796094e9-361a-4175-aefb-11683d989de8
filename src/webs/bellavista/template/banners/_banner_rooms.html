<div class="banner_rooms_wrapper">
    <div class="banner_rooms">
        {% for room in rooms %}
            {% set outer_loop = loop %}
            <div class="room">
                <div class="room_pic owl-carousel">
                    {% for img in room.images %}
                        <div class="image">
                            {% if not is_mobile %}
                                <a href="{{ img.servingUrl }}=s1900" rel="lightbox[{{ room.key|safe }}]" class="gallery_icon">
                                    <i class="fal fa-search-plus"></i>
                                </a>
                            {% endif %}
                        {% if room.tour_virtual %}
                            <a href="#hidden_tour_{{ outer_loop.index }}" class="tour-virtual-popup">
                                <div class="tour_virtual_icon">
                                    <i class="icon-360"></i>
                                    <span class="hidden_tooltip">
                                        {% if room.tour_virtual[0]%}
                                            {{ room.tour_virtual[0]|safe }}
                                        {% else %}
                                            {{ T_tour_virtual|safe }}
                                        {% endif %}
                                    </span>
                                </div>
                            </a>
                        {% endif %}
                        {% if room.link_tour %}
                            <a href="{{ room.link_tour }}" class="tour-virtual-link" target="_blank" rel="noopener noreferrer">
                                <div class="tour_virtual_icon">
                                    <i class="icon-360"></i>
                                </div>
                            </a>
                        {% endif %}
                            <img src="{{ img.servingUrl }}=s800" {% if img.altText %}alt="{{ img.altText|safe }}"{% endif %}>
                        </div>
                    {% endfor %}
                </div>
                {% if room.tour_virtual %}
                    <div class="hidden_tour_virtual" id="hidden_tour_{{ outer_loop.index }}" style="display: none;">
                        {{ room.tour_virtual[1]|safe }}
                    </div>
                {% endif %}
                <div class="room_content">
                    {% if room.title %}
                        <div class="content_title">
                            <h3 class="title">{{ room.title|safe }}</h3>
                        </div>
                    {% endif %}
                    {% if room.description %}
                        <div class="desc">{{ room.description|safe }}</div>
                    {% endif %}
                    <div class="links_wrapper">
                        <a href="#data" class="button-promotion btn_primary">
                           {{ T_reservar }}
                        </a>
                    </div>
                    {% if room.room_icons_list|length > 1 %}
                        <div class="room_icons owl-carousel">
                            {% for service in room.room_icons_list %}
                                <span class="tooltip">
                                    <i class="fa {{ service.ico|safe }}"></i>
                                    <span class="tooltiptext">{{ service.description|safe }}</span>
                                </span>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
   {% if not is_mobile %}
       $(".banner_rooms .room").each(function () {
            var content_height = $(this).find(".room_content").outerHeight(),
                image_height = content_height + 140;
            $(this).find(".room_pic").outerHeight(image_height);
        });
    {% endif %}

   $(".room_pic.owl-carousel").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i>', '<i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
        mouseDrag: true
    });

   $(".room_icons.owl-carousel").owlCarousel({
        loop: false,
        nav: true,
        dots: false,
        items: 4,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i>', '<i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
       mouseDrag: false
    });

    {% if not is_mobile and not user_isIpad %}
        function banner_room_fx() {
            $(".banner_rooms_wrapper .owl-item").addnimation({parent:$(".banner_rooms_wrapper"), class:"fadeInUpBig", reiteration: false});
        }
        banner_room_fx();
        $(window).scroll(banner_room_fx);
    {% endif %}
});
</script>