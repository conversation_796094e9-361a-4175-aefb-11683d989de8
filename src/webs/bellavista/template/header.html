<header>
    <div id="wrapper-header">
        <div class="container_fluid">
            <div class="top_options_wrapper">
                <div class="top_right_options">
                    {% if header_weather %}
                        <div class="weather-block">
                            <span class="city">{{ header_weather|safe }}</span>
                            <span class="weather_1 weather_wrapper"></span>
                        </div>
                        <script>$(function () {
                            $.simpleWeather({
                                location: '{{ header_weather|safe }}',
                                woeid: '',
                                unit: 'c',
                                success: function (weather) {
                                    weather.code = (weather.code).slice(0, 2);
                                    let img = '<img class="weather-icon" src="/static_1/images/weather/1/' + weather.code + '.png" alt="weather" title="weather">',
                                        temp = '<p class="number"><span class="deegrees">' + weather.temp + '&deg;C</span></p>';
                                    $(".weather-block").prepend(img);
                                    $(".weather_1").html(temp);
                                },
                                error: function (error) {
                                    $("#weather_1").html('<p>' + error + '</p>');
                                }
                            });
                        });</script>
                    {% endif %}
                    <div class="contact_details_wrapper">
                        {% if phone_contact %}
                        <a class="contact_phone">{{ phone_contact|safe }}</a>
                        {% endif %}
                        {% if email_header %}<a href="mailto:{{ email_header|safe }}" class="email_booking">{{ email_header|safe }}</a>{% endif %}
                    </div>
                    <a href="#data" class="button_promotion btn_white_outline">{{ T_reservar }}</a>
                    <div id="lang">
                        <span class="language_selected">{{ language_selected }}</span>
                        <div class="language_selector">{% for key, language in language_codes.items() %}
                            <a hreflang="{{ key }}" href="{% if key == lang_default %}/{% else %}{{ hostWithoutLanguage }}/{{ key }}/{% endif %}">{{ key }}</a>{% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<div class="toggle_component">
    <div class="menu_toggle_wrapper">
        <div class="menu_toggle">
            <span></span>
        </div>
    </div>
    <div id="logoDiv" class="logo_wrapper">
        <a href="{{host|safe}}/">
            <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
        </a>
    </div>
</div>
<nav id="main_menu">
    <div class="menu_toggle_wrapper">
        <div class="menu_toggle">
             <span class="top"></span>
             <span class="middle"></span>
             <span class="bottom"></span>
        </div>
    </div>
    <div id="mainMenuDiv">{% include "main_div.html" %}</div>
</nav>
