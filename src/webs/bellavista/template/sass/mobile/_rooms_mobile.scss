
.rooms_wrapper {
    padding: 0 20px 20px;
    box-sizing: border-box;

    * {
        box-sizing: border-box;
    }

    .room {
        margin-top: 20px;

        .room_pictures {
            .img_wrapper {
                position: relative;
                width: 100%;
                height: 200px;
                overflow: hidden;

                img {
                    @include cover_image;
                }
            }
            .tour-virtual-popup,
            .tour-virtual-link {
                width: 60px;
                padding: 10px;
                position: absolute;
                top: 20px;
                right: 35px;
                font-size: 50px;

                .icon-360 {
                    background: white;
                    border-radius: 50%;
                    padding: 10px;
                    box-shadow: 3px 5px 10px rgba(0, 0, 0, 0.5);
                }

                .hidden_tooltip {
                    display: none;
                }
            }
        }

        .room_content {
            padding: 20px;
            background-color: white;
            border: 1px solid $corporate_2;
            border-top: 0;
            text-align: center;

            .title {
              @include title_styles;
              font-size: 18px;
              line-height: 20px;
              text-align: left;
              padding: 0 0 20px;
            }

            .text {
                text-align: left;
                font-size: 18px;
                line-height: 21px;
                font-weight: 300;
                text-rendering: auto;
                -webkit-font-smoothing: antialiased;
                color: #666;
                max-height: 65px;
                @include ellipsis(3);
                @include transition(all, 1s);

                .intro {
                    color: $grey;
                    font-size: 15px;
                    line-height: 19px;
                    font-weight: 300;
                    margin: 5px 0;
                }

                ul {
                    margin: 0;
                    padding: 0 0 0 20px;

                    br {
                        display: none;
                    }

                    li {
                        list-style: disc;
                    }
                }

                .see-more-room, .see-less-room {
                    display: none;
                }

                .services-hidden {
                    display: block !important;
                    max-height: 0;
                    overflow: hidden;
                    @include transition(all, .6s);
                }

                &.active .services-hidden {
                    max-height: 500px;
                }
            }
          
            .buttons_wrapper {
              padding: 10px 20px 0;
              overflow: hidden;
              position: relative;
              
              .read_rooms_more_content.btn_personalized_2 {
                font-family: $secondary_font;
                font-size: 16px;
                line-height: 23px;
                height: 50px;
                text-transform: uppercase;
                color: $black;
                margin-top: 15px;
                padding: 10px 20px;
                overflow: hidden;
                display: block;
                position: relative;
                @include transition(all, 0.6s);
                
                span {
                 @include center_xy;
                }
                
                .read_less {
                  @include transition(all, 0.6s);
                  opacity: 0;
                }
                
                .read_more {
                  @include transition(all, 0.6s);
                  opacity: 1;
                }

                &::before,
                &::after {
                  content: '';
                  position: absolute;
                  top: 0;
                  width: 81px;
                  height: 30px;
                  border: 2px solid transparent;
                }

                &::after {
                  border-color: $black transparent transparent $black;
                  left: 50px;
                }
                
                &::before {
                  border-color: transparent $black $black transparent;
                  top: auto;
                  right: 50px;
                  bottom: 0;
                }

                &.active {
                  .read_less {
                    @include transition(all, 0.6s);
                    opacity: 1;
                  }
                  .read_more {
                    @include transition(all, 0.6s);
                    opacity: 0;
                  }
                }
              }
              .button-promotion, .btn_personalized_3  {
                margin-top: 15px;
                font-size: 14px;
                font-weight: 600;
                height: 50px;
                padding: 15px 0;
                letter-spacing: 1px;
                color: white;
                text-transform: uppercase;
                background-color: $corporate_2;
                .contact_form {
                  color: white;
                }
              }
            }
          &.active {
            .text {
              max-height: 1000px;
              display: block;
              @include transition(all, 1s);
            }
          }
        }

        .owl-nav {
            @include center_y;
            left: 0;
            right: 0;
            height: 0;
            .owl-prev, .owl-next {
              @include center_y;
              left: 10px;
              right: auto;
              display: inline-block;
              font-size: 44px;
              color: white;
              font-weight: lighter;
            }
            .owl-next {
              left: auto;
              right: 10px;
            }
            position: relative;

            .owl-prev {
                top: -70px;

                i {
                    display: none;
                }

                &:before {
                    content: '\f104';
                    font-family: "Font Awesome 5 Pro";
                }
            }

            .owl-next {
                top: -70px;

                i {
                    display: none;
                }

                &:before {
                    content: '\f105';
                    font-family: "Font Awesome 5 Pro";
                }
            }
        }

        .hidden_tour_virtual {
            position: fixed;
            margin: auto;
            top: 0%;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            justify-content: center;
            align-items: center;
            z-index: 999;
            display: flex;
            justify-content: center;
            align-items: center;

            .close {
                background: white;
                position: relative;
                top: -265px;
                width: 100%;
                .close-popup {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: stretch;
                    justify-content: flex-end;
                    i {
                        margin-right: 15px;
                        margin-top: 3px;
                    }
                }
            }

            .tour_virtual {
                padding: 15px;
                background: white;
                position: absolute;
                width: 100%;
                iframe {
                    width: 100%;
                }
            }
        }
    }
}
.banner_icons_wrapper {
  padding: 20px 0;
  .banner_icons.owl-carousel {
    .icon {
      max-width: 100%;
      .icon_text {
        display: block;
        margin-left: 0;
        white-space: normal;
      }
    }
    .owl-nav {
      @include center_y;
      left: 0;
      right: 0;
      top: 10%;
      .owl-next, .owl-prev {
        color: $grey_2;
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        -webkit-transition: all 0.6s;
        -moz-transition: all 0.6s;
        -ms-transition: all 0.6s;
        -o-transition: all 0.6s;
        transition: all 0.6s;
        position: absolute;
        left: -10px;
        top: 0;
        font-size: 26px;
      }
      .owl-next {
        left: auto;
        right: -10px;
      }
    }
  }
}