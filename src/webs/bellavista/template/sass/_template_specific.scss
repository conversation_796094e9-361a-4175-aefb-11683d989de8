body {
  @include text_styles;

  &.fixed {
    header {
      position: fixed !important;
    }
  }

  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);

    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  .container14 {
    margin: 0 auto;
    padding: 0;

    @media (min-width: 1421px) {
      width: 1360px;
    }

    @media (min-width: 1161px) and (max-width: 1420px) {
      width: 1140px;
    }

    @media (max-width: 1160px) {
      width: 1120px;
    }
  }

  .container12 {
    @media (max-width: 1160px) {
      width: 1120px;
    }
  }

  #slider_container {
    position: relative;
    z-index: 1;

    .logos_slider_wrapper {
      position: absolute;
      top: 50%;
      left: 75px;
      transform: translateY(-50%);
      z-index: 20;

      .logo_slider {
        display: block;

        &:first-child {
          img {
            width: 150px;
            margin-left: -25px;
          }

        }

        img {
          width: 80px;
          filter: drop-shadow(4px 4px 3px $black);
        }
      }

      .logo_slider + .logo_slider {
        margin-top: 10px;
      }
    }

    .inner_slider {
      width: 100%;
      height: 400px;
      position: relative;
      overflow: hidden;

      img {
        @include cover_image;
      }
    }

    .tp-revslider-slidesli {
      &::before {
        @include full_size;
        content: '';
        background-color: rgba($black, 0.3);
        z-index: 1;
      }

      .cartela_slider {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: $secondary_font;
        color: white;
        z-index: 2;
        text-align: center;


        .cartela_title {
          font-size: 70px;
          line-height: 80px;
          letter-spacing: 0;
          font-weight: 700;
          margin-bottom: 20px;

          .subtitle {
            font-size: 45px;
            line-height: 48px;
            font-weight: 400;
            letter-spacing: 2.5px;
            display: block;
          }
        }

        .cartela_desc {
          @include text_styles;
          @include ellipsis(2);
          margin-bottom: 20px;
          color: white;
        }
      }
    }

    .tparrows {
      opacity: 1;
    }

    .tp-bullets {
      display: none;
    }
  }

  .content_subtitle_wrapper {
    padding: 100px calc((100% - 1140px) / 2);

    .content_subtitle_title {
      @include title_styles;
    }

    .content_subtitle_description {
      @include text_styles;
      padding-top: 50px;
    }
  }

  /* Mis reservas */
  .content_access {
    background-color: $lightgrey;
    @include sec_pad;
    margin-bottom: 0;

    #my-bookings-form {
      max-width: 500px;
      margin: auto;

      #reservation {

        .modify_reservation_widget {
          margin: auto;
          margin-top: 40px;
          margin-bottom: 0;
          padding: 20px;

          #info_ninos {
            display: none !important;
          }

          #contenedor_opciones {
            margin: 0 auto 10px;

            .ninos-con-babies {
              margin-right: 15px;
            }
          }

          #contenedor_fechas {
            text-align: center;

            #fecha_entrada,
            #fecha_salida {
              display: inline-block;
              float: none;
            }
          }

          #contenedor_habitaciones {
            text-align: center;

            label {
              display: inline-block;
              float: none;
            }

            select {
              display: inline-block;
              float: none;
            }
          }

          #envio {
            text-align: center;
          }
        }

        .my-bookings-booking-info {
          margin: 40px auto 0;

          .fResumenReserva {
            margin: auto;
          }
        }
      }

      #modify-button-container {
        display: none;
      }

      #my-bookings-form-fields {

        label {
          display: block;
          text-align: center;
          text-transform: uppercase;
          color: #4B4B4B;
          font-weight: 100;
        }

        input,
        select {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          display: block;
          margin: 15px auto;
          height: 40px;
          border-radius: 0;
          text-align: center;
          font-size: 14px;
          width: 100%;
          border: solid 2px darken($lightgrey, 2%);
        }

        select {
          padding: 0 0 0 15px;
        }


        ul {
          display: flex;
          flex-flow: row nowrap;
          text-align: center;
          margin-top: 30px;

          li {
            display: inline-block;
            width: 50%;
            vertical-align: middle;

            button {
              height: 40px;
              text-transform: uppercase;
              font-size: 16px;
              color: white;
              border: 0;
              cursor: pointer;
              width: 100%;
              font-weight: 100;
              background: $corporate_1;
              @include transition(background, .4s);

              &.modify-reservation {
                background: $corporate_1;

                &:hover {
                  background: darken($corporate_1, 10%);
                }
              }

              &.searchForReservation {
                background: $corporate_2;

                &:hover {
                  background: darken($corporate_2, 10%);
                }
              }
            }
          }
        }
      }

      #cancelButton {
        display: none;
        background: $corporate_2;
        height: 40px;
        text-transform: uppercase;
        font-size: 16px;
        color: white;
        border: 0;
        cursor: pointer;
        width: 200px;
        font-weight: 100;
        margin: 40px auto 0;
        @include transition(background, .4s);

        &:hover {
          background: darken($corporate_2, 10%);
        }
      }
    }
  }

  /* Iframe Map */

  .iframe_map_wrapper {
    iframe {
      width: 100%;
    }
  }

  .automatic_floating_picture {
    bottom: 90px;
    top: auto;
    z-index: 10;
    display: flex;
    flex-direction: column-reverse;

    a.right img {
      max-width: 150px;
    }
  }
}

