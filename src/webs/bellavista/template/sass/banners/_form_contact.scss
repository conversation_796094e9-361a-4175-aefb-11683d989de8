.contact_form_wrapper {
    background-color: $lightgrey;
    @include sec_pad;
    margin-bottom: 0;
    border-bottom: 2px solid white;
    overflow: hidden;
    

    h3 {
        @include title_styles;
        text-align: center;
        padding-bottom: 50px;
    }

    #contact {
        display: table;
        margin: auto;

        .info {
            display: table;
            position: relative;
        }

        .contInput {
            display: inline-block;
            float: left;
            padding: 10px 0 10px 20px;
            position: relative;

            &:nth-of-type(-n+3) {
                width: calc((100% - 5px) / 3);
                padding-top: 20px;
            }

            &:nth-of-type(4) {
                width: 100%;
            }

            &:nth-of-type(3),
            &:nth-of-type(5) {
                margin-right: 0;
            }

            input:not([type="checkbox"]) {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                display: block;
                margin: 15px auto;
                height: 40px;
                border-radius: 0;
                font-size: 14px;
                width: 100%;
                border: solid 2px darken($lightgrey, 2%);
                box-shadow: none;
            }

            textarea {
                border-radius: 0;
                font-size: 14px;
                width: 100%;
                border: solid 2px darken($lightgrey, 2%);
                box-shadow: none;
                min-height: 150px;
            }

            input,
            textarea {
                padding: 15px;
                &.error {
                    outline: 1px solid red;
                }
            }

            #accept-term,
            &#privacity {
                width: auto;
                height: auto;
                display: inline-block;
                vertical-align: middle;
            }

            &.policy-terms {
                margin: 0;
            }

            input[type= "checkbox" ] {
                -webkit-appearance: none;
                border: solid 2px $corporate_1;
                border-radius: 0;
                height: 15px;
                width: 15px;
                vertical-align: middle;
                padding: 5px;

                &:checked {
                    background-color: $corporate_1;
                }
            }
        }



        .policy-terms {
            display: inline-block;
            width: auto;
            float: left;
            color: $black;
            font-size: 12px;
            margin: 0;
        }

        a.myFancyPopup {
            display: inline-block;
            vertical-align: middle;
            color: $black;
        }

        #contact-button {
            @extend .btn_primary;
            position: absolute;
            right: 10px;
            bottom: 0;
            padding: 15px;
        }
    }

    label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
    }
}