.offers_wrapper {
    @include sec_pad;
    padding-top: 20px;

    .container14 {
        display: flex;
        flex-flow: row wrap;
        
        .offer {
            width: calc((100% / 3) - 20px);
            box-shadow: $shadow;
            margin-bottom: 20px;
        
            .offer_pic {
                position: relative;
                width: 100%;
                height: 220px;
                overflow: hidden;
            
                img {
                    @include cover_image;
                }
            
                .icon_link {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    z-index: 5;
                }
            }
        
            .offer_content {
                position: relative;
                background-color: white;
                text-align: center;
                padding: 50px 20px 100px;
            
                .icon_styles_2 {
                    position: absolute;
                    top: 0;
                
                    i {
                        border-radius: 50%;
                        font-size: 30px;
                        color: white;
                    
                        &::before {
                            position: absolute;
                            z-index: 1;
                            transform: translate(-50%, -50%);
                        }
                    
                    
                        &::after {
                            position: absolute;
                            content: '';
                            background-color: $corporate_2;
                            border-radius: 50%;
                            width: 60px;
                            height: 60px;
                            transform: translate(-50%, -50%);
                        }
                    }
                }
                
                .content_title {
                    margin-bottom: 20px;
                    
                    .title {
                        font-size: 35px;
                        line-height: 38px;
                        
                        small, span, .subtitle {
                            font-size: 22px;
                            line-height: 24px;
                        }
                    }
                }
            }
    
            .links {
                position: absolute;
                bottom: 20px;
                left: 20px;
                right: 20px;
                display: flex;
                flex-flow: row nowrap;
                justify-content: center;
        
                > a {
                    flex: auto;
                    margin: 0 5px;
                    padding: 10px 0;
                }
            }
        
            &:not(:nth-of-type(3n)):not(:last-of-type) {
                margin-right: 20px;
            }
        }
    }
}
