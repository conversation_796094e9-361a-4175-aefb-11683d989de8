.banner_images_wrapper {
  @include sec_pad;
  
  .banner {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 380px);
    grid-gap: 40px;
    
    .picture_wrapper {
      &:nth-child(2) {
        grid-area: 1 / 2 / 3 / 3;
      }
      
      &::before {
        @include full_size;
        content: '';
        background: black;
        opacity: 0;
        transition: all .4s;
      }
      
      .picture_title {
        position: absolute;
        top: calc(50% - 20px);
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        opacity: 0;
        color: white;
        white-space: nowrap;
        transition: all .4s;
        
        .title {
          font-family: $secondary_font;
          color: white;
          font-size: 70px;
          line-height: 50px;
          position: relative;
          @include icon_arrow(20px, 25px);
          
          a {
            color: white;
          }
        }
      }
      
      &:hover {
        &::before {
          opacity: .5;
        }
        
        .picture_title {
          top: 50%;
          opacity: 1;
        }
      }
    }
  }
}