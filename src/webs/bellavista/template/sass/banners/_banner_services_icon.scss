.services_icon_block {
  position: relative;
  padding: 60px calc((100% - 1140px) / 2);
  overflow: hidden;
  @include display_flex;
  align-items: center;
  width: 100%;
  padding-top: 0;
  margin-bottom: 50px;

  .title {
    @include title_styles;
    padding-bottom: 30px;
    width: 100%;
  }

  .icon {
    display: inline-block;
    width: calc(100% / 3);
    margin-bottom: 30px;

    i, span {
      display: inline-block;
      vertical-align: middle;
    }

    i {
      width: 50px;
      font-size: 30px;
      text-align: center;
      color: $corporate_2;
    }

    span {
      width: calc(100% - 50px);
      font-size: 17px;
      letter-spacing: 1px;
      padding-left: 22px;
    }
  }
}