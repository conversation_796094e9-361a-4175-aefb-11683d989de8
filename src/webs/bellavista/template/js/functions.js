$(window).load(function () {
    $(".main-section-div-wrapper.has_subsection .open_subsection").click(function () {
        $(".main-section-div-wrapper.has_subsection .open_subsection").toggleClass( "opened" );
        $(".main-section-div-wrapper.has_subsection .sub_sections_wrapper").slideToggle( "fast" );
    });

    $(".menu_toggle").click(function () {
        $(this).toggleClass("opened");
        $("nav#main_menu").toggleClass("opened");
        $("body").toggleClass("fixed");
    });

    $("iframe").each(function(){
       if ($(this).attr("lazy-src")) {
           $(this).attr("src", $(this).attr("lazy-src"));
       }
    });

    $(".tour-virtual-popup").click(function(event) {
        event.preventDefault();
        const target_popup_id = $(this).attr('href').substring(1);
        const target_popup = $(`.hidden_tour_virtual#${target_popup_id}`);
        const iframe = target_popup.find('iframe');

        if (!iframe.attr('src')) {
           iframe.attr('src', iframe.data('onclick-src'));
        }

        $.fancybox.open(target_popup);
    });


    /*$("#lang .language_selected").hover(function () {
        $(".language_selector").slideToggle();
    });*/

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");
        }

    });

    showMenu();
});

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if (actual_position > (slider_height / 4)) {
        $("header, .toggle_component").addClass('fixed_top');
    }

    if (actual_position < (slider_height / 4)) {
        $("header, .toggle_component").removeClass('fixed_top');
    }
}

