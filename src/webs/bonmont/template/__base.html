<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">
<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,700" rel="stylesheet">
    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }} - {{ hotel_name|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}

    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="format-detection" content="telephone=no">

    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link media="screen" rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css"/>
    <!-- lightbox -->
    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>
    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css?v=1.111">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=2.234"/>

    <!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

    <!--[if IE 9]>

    <![endif]-->


    <!--[if lte IE 7]>
        <script type="text/javascript">
        alert('{{ T_explorer6_no_soportado }}');
        </script>
        <![endif]-->

    <!--[if lte IE 8]>
        <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
        <![endif]-->

    {% if gtm_tag %}
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-{{ gtm_tag }}');</script>
    <!-- End Google Tag Manager -->
    {% endif %}

    {{ jquery|safe }}

    {{ extra_head|safe }}
</head>

<body itemscope itemtype="//schema.org/Hotel" {% if not home %}class="inner_section" {% endif %}>
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}


{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">

{% if lang_management %}
    <input type="hidden" id="lang_management" value="{{ lang_management }}">
{% endif %}
{% if lang_default %}
    <input type="hidden" id="lang_default" value="{{ lang_default }}">
{% endif %}
{% block content %}
    <!--EDIT HERE YOUR PAGE-->
{% endblock %}
{% block additional_js %}

    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1.1"></script>

    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js"></script>
    <script>$(function () {
        DP_extend_info.config.booking_version = '7';
        DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            return "<div class='day'>" + dateComponents[0] + "</div>/<div class='month'>" + dateComponents[1] + "</div>/<div class='year'>" + dateComponents[2] + "</div> ";
        };
        DP_extend_info.init();
    });
    $(window).load(function () {
        $(".ticks_wrapper").prepend($(".wrapper-new-web-support").html());
    });
    </script>

    <script src="/static_1/scripts/hotel_selector_2.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_7.js?v=1.2"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js?v=1"></script>
    <script async type="text/javascript" type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1"></script>
    <script async type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js"></script>


    {% if google_analytics_id %}
        <!-- Google Analytics -->
        <script>
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                    (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                        m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

            ga('create', '{{ google_analytics_id }}', 'auto');
            ga('send', 'pageview');

            try {
                ga(function (tracker) {
                    client_id = tracker.get('clientId');

                    $(".paraty-booking-form").each(function () {
                        var analytics_user_input = $('<input>').attr({
                                                                         type: 'hidden',
                                                                         id: '_ga',
                                                                         name: '_ga',
                                                                         value: client_id
                                                                     });

                        $(this).append(analytics_user_input)
                    })
                });
            } catch (err) {
                console.log("Google Analytics defined incorrectly (May use a old version)");
            }
        </script>
        <!-- End Google Analytics -->
    {% endif %}
    <div style="display: none;">
        <div id="data">
            <div id="wrapper_booking_fancybox">
                <div id="booking_widget_popup" class="booking_widget_fancybox">
                    {{ booking_engine_2|safe }}
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function(){
            $("#booking label.dates_selector_label").html("<span>{{ T_entrada }}</span><span>{{ T_salida }}</span>");
            $(".promocode_input").attr("placeholder", "{{ T_promocode }}");
            $(".submit_button").html("<span>" + $(".submit_button").text() + "</span>")
        })
    </script>
{% endblock %}
{{ extra_content_website|safe }}
</body>
</html>