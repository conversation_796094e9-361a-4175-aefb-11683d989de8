.destiny_wrapper {
  padding-top: 0;
  background-color:#f6f6f6;
  .destiny {
    display: table;
    vertical-align: top;
    margin: 0;
    width: 100%;
    .destiny_image {
      position: relative;
      width: 100%;
      height: 400px;
      overflow: hidden;
      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }
    .destiny_info {
      background-color: white;
      padding: 30px;
      font-size: 12px;
      font-family: "Open Sans", sans-serif;
      position: relative;
      color: #4b4b4b;
      margin: -250px 50px 30px 50px;
      .new_icon {
        position: absolute;
        top: 20px;
        right: 20px;
      }
      h2 {
        color: $corporate_1;
        text-transform: uppercase;
        font-weight: lighter;
        font-size: 20px;
        margin-bottom:5px;
        br {display: none;}
      }
      p {
        font-size: 12px;
      }
      a.toggle_hotels {
        display: inline-block;
        color: $corporate_2;
        border-bottom: 1px solid $corporate_2;
        text-transform: uppercase;
        margin: 20px 0 0;
        &:after {
          content: '\f107';
          font-family: "FontAwesome", sans-serif;
          font-size: 130%;
          margin-right: -20px;
          float: right;
        }
      }
    }
  }
}