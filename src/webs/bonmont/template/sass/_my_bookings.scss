body {
  .modify_reservation_widget {
    width: 445px;
    margin: 0 auto 20px;
    padding: 10px 0;
    text-align: left;
    border-radius: 5px;
    border-width: 0;

    .numero_personas {
      float: left;
    }

    #motor_reserva {
      #contenedor_fechas {
        text-align: center;

        .colocar_fechas {
          position: relative;
          display: inline-block;
          width: 200px;
          float: none;
          background-color: white;
          padding: 5px;
          margin:0 auto 10px;
          border-radius: 5px;

          &#contador_noches {
            display: none;
          }
          label {
            color: #999;
          }
          input {
            margin: auto;
            border-width: 0;
            font-size: 16px;
            background: transparent;
          }
          &:before {
            content: '\f133';
            @extend .fa;
            position: absolute;
            bottom:10px;
            right: 10px;
            color: #DDD;
          }
        }
      }

      #contenedor_habitaciones {
        position: relative;
        text-align: center;
        background-color: white;
        width: 215px;
        padding: 5px;
        margin:0 auto 10px;
        border-radius: 5px;
        label, select {
          display: inline-block;
          float: none;
        }
        label {
          color: #999;
        }
        select {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border-width: 0;
          background: transparent;
        }
        &:before {
          content: '\f0dc';
          @extend .fa;
          font-size: 10px;
          position: absolute;
          bottom: 15px;
          right: 10px;
          color: #CCC;
        }
      }

      #contenedor_opciones {
        margin: auto;
        width: 400px;
        margin-bottom: 10px;
        #hab1, #hab2, #hab3 {
          width: 370px;
          margin: 0 auto 10px;
          float: none;
          clear: both;
          text-align: center;
          label {
            color: #999;
          }
          .numero_personas {
            position: relative;
            display: inline-block;
            background-color: white;
            padding: 5px;
            margin-bottom: 10px;
            border-radius: 5px;
            &:before {
              content: '\f0dc';
              @extend .fa;
              font-size: 10px;
              position: absolute;
              bottom: 15px;
              right: 10px;
              color: #CCC;
            }
            select {
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              border-width: 0;
              position: relative;
              z-index: 1;
              background-color: transparent;
            }
            &.ninos-con-babies {
              margin-right: 15px;
            }
            #info_ninos {
              left: 0;
              top: 20px;
            }
          }
        }
      }

      #envio {
        input {
          border-width: 0;
          width: 250px;
          padding: 23px 15px;
          text-transform: uppercase;
          text-align: center;
          margin: 0 0 0 20px;
          float: left;
          border-radius: 5px 0 0 5px;
          outline: none;
        }
        button {
          padding: 13px 40px;
          text-transform: uppercase;
          border-radius: 0 5px 5px 0;
          &:hover {
            background-color: #0096c6;
          }
        }
      }
    }
  }
}
