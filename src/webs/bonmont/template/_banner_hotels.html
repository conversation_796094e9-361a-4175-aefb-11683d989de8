<div class="banner_hotels_wrapper">
    <div class="destiny_wrapper">
    {% for desinty in banner_hotels_destinos %}
        <div class="destiny" data-filter="{{ desinty.class_name }}" style="width: calc((100% - 5px) / {{ banner_hotels_destinos|length }});margin-left: calc((100% - 5px) / {{ banner_hotels_destinos|length }} + 5px)">
            <div class="image">
                <img src="{{ desinty.servingUrl|safe }}=s1900" alt="{{ T_hotels_at }} {{ desinty.title|safe }}">
            </div>
            <div class="title">
                <div class="info">
                    <div class="center_xy">
                        <div class="num">1</div>
                        <div class="label">{{ T_alojamientos|safe }}</div>
{#                        <a class="link" href="{{ desinty.linkUrl }}">{{ T_todos_hoteles_2|safe }}</a>#}
                    </div>
                    {% include "_imago.html" %}
                </div>
                <div class="name"><span>{{ desinty.title|safe }}</span></div>
            </div>
        </div>
    {% endfor %}
    </div>
    <div class="slider_hotel_wrapper">
        {% for group, value in banner_hotels.items() %}
        <div class="hotel_slider {{ value.group_class|safe }}" data-num="{{ value.group_list|length }}">
            <div class="owl-carousel">
                {% for hotel in value.group_list %}
                    <div class="hotel">
                        <div class="hotel_content">
                            <div class="hotel_name"><span>{{ hotel.name|safe }}</span></div>
                            <div class="hotel_desc">{{ hotel.description|safe }}</div>
                        </div>
                        <div class="hotel_image">
                            <img src="{{ hotel.image|safe }}=s800" alt="{{ hotel.name|safe }}, {{ hotel.destiny_label|safe }}">
                            <div class="info">
                                {% include "_imago.html" %}
                            </div>
                            <div class="center_y">
                                <a class="buttonpromotion" href='javaScript:booking_click("{{hotel.namespace|safe}}")'>{{ T_book_now }}</a>
                                <a href="{{ hotel.image|safe }}=s1900" class="link" rel="lightbox[gallery_{{hotel.namespace|safe}}]"><i class="fa fa-camera"></i></a>
                                <a href="#" class="link open_map" data-map="map_hotels" data-lat="{{ hotel.lat }}" data-lng="{{ hotel.lng }}"><i class="fa icon-map"></i></a>
                                <a href="{{ hotel.link|safe }}" class="link"><i class="fa icon-eye"></i></a>
                            </div>
                            <div class="gallery_hidden" style="display: none;">
                                {% for pic in hotel.gallery %}
                                {% if not forloop.first %}
                                    <a href="{{ pic.servingUrl|safe }}=s1900" rel="lightbox[gallery_{{hotel.namespace|safe}}]"></a>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
        <div class="mapwrapper map_hotels" style="display: none">
            <a href="#" class="link open_map" data-map="map_hotels"><i class="fa fa-times"></i></a>
            {% include "_map.html" %}
        </div>
    </div>

    <script>
        $(window).load(function () {
            var owl_params = {
                loop: true,
                nav: true,
                dots: false,
                items: 1,
                margin: 0,
                navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                autoplay: false
            };
            $(".hotel_slider .owl-carousel").owlCarousel(owl_params);

            $(".open_map").click(function (e) {
                e.preventDefault();
                var target = $(this);
                $("."+$(this).attr("data-map")).toggle(function () {
                    var center_lat = target.attr('data-lat');
                    var center_lng = target.attr('data-lng');
                    console.log(center_lat);
                    console.log(center_lng);
                    initMap(center_lat,center_lng);
                });
            });
            $(".destiny").click(function () {
                $(".destiny").removeClass("opened");
                $(this).addClass("opened");
                var filter_slider = $(this).attr("data-filter");
                $(this).find(".num").html( $(".hotel_slider." + filter_slider).attr("data-num") );

                $(".hotel_slider").slideUp().promise().done(function () {
                    $(".hotel_slider." + filter_slider).slideDown();
                });
            });

            var filter_rand = Math.floor((Math.random() * $(".destiny").length ) + 1);
            $(".destiny:nth-child("+filter_rand+")").trigger('click');

        });
    </script>
</div>