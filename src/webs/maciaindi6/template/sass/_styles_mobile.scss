$fontawesome5: true;
$is_mobile: true;
$mobile_padding: 20px;

body * {
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

}

@mixin title_styles_mobile($font_size: 38px) {
  font-size: 38px;
  font-family: 'Lato', Sans-Serif;
  text-transform: uppercase;
  color: #4a4a4a;
  text-align: left;
  font-weight: 100;

  small, big, span, strong {
    font-weight: 700;
    display: block;
  }
}

@mixin base_mobile_styles() {
  position: relative;
  padding: $mobile_padding;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  * {
    box-sizing: border-box;
  }
}

@mixin text_styles_mobile() {
  @include text_styles;
  text-align: left;
  font-weight: 500;
  color: $grey;
  width: auto;
  padding: 0 20px 20px;
  margin-top: 0;
  font-size: 18px;
  line-height: 21px;
}

@import "defaults";
@import "styles_mobile/2/2";

//CORPORATE COLORS
$grey: #666;

body {
  font-family: $text_family;

  a {
    text-decoration: none;
    color: $corporate_2;
  }

  .main_menu {
    background-color: #33658E;

    .main_ul {
      li {
        border-color: lighten($corporate_2, 10%);

        a {
          color: white;
          font-weight: 500;
        }
      }
    }

    .social_menu {
      a {
        &.mailto i {
          padding: 12px 0 8px;
        }

        i {
          background: none;
        }
      }

      .lang_header {
        position: relative;
        margin-right: 10px;
        width: 40px;

        .lang_selected {
          pointer-events: none;
          @include center_xy;
          top: 46%;
          color: white;
          font-size: 16px;
          font-weight: lighter;

          &:after {
            content: "";
            width: 102%;
            display: block;
            height: 2px;
            background: white;
          }
        }

        .lang_selector {
          width: 100%;
          height: 100%;
          opacity: 0;
        }
      }
    }
  }

  nav {
    display: none;

    a {
      color: $corporate_3;

      &.mailto {
        color: $corporate_3;

        i {
          font-family: "Font Awesome 5 Pro" !important;
          font-weight: 500;

          &::before {
            content: '\f0e0';
          }
        }
      }
    }
  }


  header {
    .mailto {
      width: 60px;

      i {
        color: $corporate_2;
        font-weight: 900;
        overflow: hidden;
      }
    }

    .telefono {
      position: relative;
      margin-left: -30px;

      i {
        color: $corporate_2;
      }
    }

    .logo {
      left: 100px;

      img {
        max-height: 50px;
      }
    }

    .mail_header {
      margin-right: 30px;
    }

    .whatsapp {
      display: none;

      i {
        color: $grey_2;
      }
    }

    .mail_header, .lang_header, .whatsapp_custom {
      float: right;
      width: 40px;
      height: 80px;
      position: relative;

      i {
        font-size: 32px;
        color: $grey_2;
        @include center_xy;
      }
    }

    .lang_header {
      position: relative;
      margin-right: 10px;

      .lang_selected {
        pointer-events: none;
        @include center_xy;
        top: 46%;
        color: $grey_2;
        font-size: 16px;
        font-weight: lighter;

        &:after {
          content: "";
          width: 102%;
          display: block;
          height: 2px;
          background: $grey_2;
        }
      }

      .lang_selector {
        width: 100%;
        height: 100%;
        opacity: 0;
      }
    }
  }

  strong, b {
    font-weight: 700;
  }

  .breadcrumbs {
    background-color: $grey_2;
    padding-left: 20px;

    a {
      font-family: $title_family;
      padding: 10px 2px;
    }
  }

  .section_content h1,
  .section_content .normal_section_mobile h2.section_title,
  .content_title .title,
  .content_title h1.title,
  .content_title h2.title,
  .content_title h3.title,
  .content_title h4.title,
  .location_content .section-subtitle {
    @include title_styles_mobile(28px);
    padding: 20px;

    small {
      line-height: 25px;
    }
  }

  #full_wrapper_booking {
    .entry_label_calendar, .departure_label_calendar {
      background-color: $corporate_1;
    }

    .room_list_wrapper .room_list_wrapper_close {
      background-color: $corporate_2;
    }

    .wrapper_booking_button {
      .submit_button {
        background-color: $corporate_2;
      }
    }
  }

  .mobile_engine {
    bottom: 0;

    .mobile_engine_action {
      left: 0;
      right: 0;
      width: 100%;
      border-radius: 0;
      box-shadow: none;
      bottom: 0;
      font-family: $title_family;
      font-size: 14px;
      font-weight: 700;
      padding: 15px 0;
      text-transform: uppercase;
      background-color: $corporate_2;
    }


    &.open {

      .mobile_engine_action {
        background-color: $corporate_2;
        color: white;
        left: auto;
        right: 10px;
        bottom: 235px;
        z-index: 10;

        &::after {
          display: none;
        }
      }
    }
  }

  .main-owlslider {
    height: calc(100vh - 130px);

    .owl-item {
      &:before {
        content: '';
        @include full_size;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
      }
    }

    .description_text {
      @include center_xy;
      width: 80%;
      z-index: 30;
      text-align: center;
      color: white;
      font-family: $title_family;
      font-size: 16px;
      text-transform: uppercase;
      font-weight: 600;
      line-height: 38px;

      big {
        font-size: 40px;
      }

      strong {
        font-size: 35px;
        font-weight: 700;
      }

      .btn_personalized_2 {
        margin-top: 10px;
        padding: 15px 30px;
        font-size: 18px;
        text-transform: uppercase;
      }


    }
  }

  .section_content {
    .location_content .section-title {
      display: none;
    }

    .location_content {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;

      .map {
        order: 2;
      }

      .location_content {
        order: 1;
      }

      #contact {
        order: 3;
      }
    }

    &.home {
      h1 {
        @include title_styles($ta: left);
        font-size: 30px;
        padding: 20px;

        .subtitle {
          text-align: center;
        }

        .hotel_title {
          font-weight: 400;
          text-transform: uppercase;
          font-size: 40px;
        }
      }
    }

    div.content, div.content_subtitle_description, .section-content, .contact_content_element, .default_reservation_text {
      @include text_styles_mobile;
    }

    .banner_table_wrapper {
      padding-bottom: 30px;
      position: relative;

      &::before {
        position: absolute;
        content: '';
        top: 70px;
        bottom: 20px;
        right: 0;
        width: 50px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
      }

      .container12 {
        overflow: auto;
        white-space: nowrap;

        #salones-table {
          table {
            border-right: 30px solid white;

            tr {
              th, td {
                font-size: 14px;
                padding: 5px;

                div {
                  img {
                    width: 40px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .btn_personalized_1 {
    @include btn_styles;
  }

  .btn_personalized_2 {
    @include btn_styles_2;
  }

  @import "mobile/banner_roms";
  @import "mobile/banner_image";
  @import "mobile/banner_block";
  @import "mobile/banner_accordion";
  @import "mobile/banner_gallery";
  @import "mobile/banner_map";
  @import "mobile/banner_testimonials";
  @import "mobile/banner_rating";
  @import "mobile/banner_icons";

  @import "mobile/cycle_banner";

  .cycle_banner_wrapper {
    .banner {
      .img_wrapper {
        .owl-item {
          overflow: hidden;

          .img_container {
            .gallery_icon {
              font-size: 30px;
            }

            img {
              width: auto;
            }
          }
        }
      }
    }
  }

  .rooms_wrapper .room_block, .promotions_wrapper {
    .room_info, .offer_content {
      h1, h3 {
        @include banner_title_styles;
        text-align: center;
        font-size: 22px;
        padding: 10px;
        line-height: 24px;

        .subtitle {
          font-size: 20px;
          line-height: 24px;
        }
      }

      .room_description, .desc {
        @include text_styles;
        line-height: 20px;
        font-size: 13px;
      }
    }
  }

  .gallery_divided_title span {
    font-family: $title_family;
    font-size: 20px;
    font-weight: 400;
    bottom: 23px;
  }

  .minigallery_wrapper {
    .owl-prev, .owl-next {
      background: rgba($corporate_2, .8);
    }
  }

  .form_subtitle {
    @include banner_title_styles;
    margin-top: 20px;
  }

  #my-bookings-form-fields {
    .selectHotel {
      font-size: 15px;
      padding: 1em;
      border-width: 0;
      background-color: white;
      box-sizing: border-box;
      width: 100%;
      border-radius: 5px;
      text-align: left;
      margin: 1em auto 0;
      border: 0.5em solid #F9F9F9;

    }

    #my-bookings-form-search-button {
      display: block;
      padding: 10px 0;
      box-sizing: border-box;
      font-size: 22px;
      text-transform: uppercase;
      width: 100%;
      border-radius: 5px;
      margin: auto;
      background-color: $corporate_1;
      color: white;
    }
  }

  .services_icons_list_wrapper {
    padding: 70px 0 0;
    transform: translateY(-50px);

    .icons_wrapper {
      display: flex;
      flex-flow: row wrap;
      justify-content: center;

      .icon {
        display: block;
        text-align: center;
        margin-bottom: 40px;

        .ico {
          margin-bottom: 10px;

          i {
            width: 120px;
            height: 120px;
            font-size: 90px;
            position: relative;
            color: $corporate_1;

            &::before {
              @include center_xy;
            }
          }
        }

        .title {
          color: $black;
          font-weight: 500;
        }
      }
    }

    .owl-nav {
      position: absolute;
      left: 40px;
      right: 40px;
      top: 50px;
      bottom: auto;
      display: flex;
      justify-content: space-between;
    }
  }

  .offers_section {
    padding-top: 20px;

    .offers_subtitle {
      text-transform: uppercase;
    }
  }

  .offers_filter_wrapper {
    @include center_x;
    position: fixed;
    top: 100px;
    z-index: 95;
    width: 200px;

    .fa {
      @include center_y;
      right: 10px;
      color: $corporate_2;
      font-weight: 300;
      font-size: 18px;

      &:before {
        content: '\f107';
      }
    }

    select {
      background: white;
      color: $corporate_2;
      font-size: 14px;
      text-transform: uppercase;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border-radius: 0;
      width: 100%;
      border: 1px solid $corporate_2;
      padding: 10px 30px 10px 15px;
      box-sizing: border-box;
    }
  }

  .promotions_wrapper {
    background-color: white;
    position: relative;
    top: 0;

    .owl-stage-outer {
      overflow-y: visible;
    }

    .owl-stage {
      padding: 25px 0;
    }

    .owl-item {
      overflow: visible !important;
      min-height: calc(100vh - 50px);

      .offer_content {
        border-radius: 0;
        width: 70%;
        height: 0;
        min-height: 100% !important;
        text-align: center;
        box-shadow: 0px 0px 17px 10px rgba(0, 0, 0, 0.11);
        padding-bottom: 0;
        margin-bottom: 110px;
        margin-top: 0;
        left: 50% !important;

        &:before {
          position: absolute;
          content: '';
          height: 50px;
          background-color: white;
          bottom: 0;
          left: 0;
          right: 0;
          transform: translateY(90%);
        }

        h3 {
          font-size: $font_md;
          font-family: $text_family;
          line-height: 28px;
          padding: 0 20px 0;

          &:after {
            content: "";
            width: 40px;
            display: block;
            height: 2px;
            background-color: $corporate_1;
            margin: auto;
            margin-top: 10px;

          }
        }

        .hotel_name {
          font-size: 15px;
        }

        .picture {
          height: 220px;
          border-radius: 0;

          img {
            @include cover_image;
          }
        }

        .desc {
          line-height: 20px;
          text-align: left;
          border-radius: 0;
          padding: 10px !important;
          margin: 0;
        }

        .offer_links_wrapper {
          overflow: visible;
          width: 100%;
          top: 0;
          position: relative;
          margin: auto !important;
          margin-bottom: 50px !important;
          animation-name: none !important;

          &.btn_half {
            width: 100%;

            .btn {
              display: inline-block;
              width: 45%;
              transform: translate(10%, 0);
            }

            a {
              background: white;
              color: $corporate_2;
              border: 1px solid $corporate_2;
              padding: 9px 0 !important;
            }
          }

          .button-promotion, .button_promotion, .booking_button, .offer_link {
            margin-top: 15px;
            font-size: 18px;
            font-weight: 600;
            padding: 10px 0;
            letter-spacing: 1px;
            color: white;
            border-radius: 0;
            width: 50%;
            text-transform: uppercase;
            background-color: $corporate_2;

            i {
              display: none;
            }
          }
        }

        &:first-child {

        }
      }

      &.active {
        z-index: 1;
      }
    }

    .offer_content {
      height: auto;
    }

    .owl-nav {
      @include center_y;
      left: 0;
      right: 0;

      .owl-next, .owl-prev {
        color: $gray_2;
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
        -webkit-transition: all 0.6s;
        -moz-transition: all 0.6s;
        -ms-transition: all 0.6s;
        -o-transition: all 0.6s;
        transition: all 0.6s;
        position: absolute;
        left: 5px;
        top: 0;
        font-size: 0;

        &:before {
          content: "\f104";
          font-family: "Font awesome 5 Pro";
          font-size: 44px;
          font-weight: 100;
          color: #E7E4E1;
        }

        &.disabled {
          display: none;
        }
      }

      .owl-next {
        left: auto;
        right: 5px;

        &:before {
          content: "\f105";
        }
      }
    }
  }

  .my_reservation_section {
    .my-reservation-form {
      a {
        &:not(.modify_booking) {
          background-color: $corporate_2;
        }
      }
    }
  }

  .automatic_floating_picture {
    display: none;
  }

  @import "mobile/rooms_mobile";
  @import "mobile/banner_eventos_reuniones";

  .info input {
    border-radius: 0;
  }

  .my_reservation_section {
    .section-title {
      small {
        line-height: 35px;
      }
    }

    .my-reservation-form {
      display: flex;
      flex-flow: column nowrap;

      input[type=text] {
        order: 1;
        border-radius: 0;
      }

      a[data-role=button] {
        &.modify_booking {
          order: 2;
        }

        order: 3;
        border-radius: 0;
      }
    }
  }
}

#full_wrapper_booking .room_list_wrapper {
  .room_list .room label {
    padding-right: 40px;
  }

  .rooms_wrapper {
    width: 100%;
  }
}

.fancybox-slide--iframe .fancybox-content {
  height: inherit !important;
}


#contact {
  .check_element {
    .title {
      a {
        color: $corporate_2;
      }
    }
  }

  .contact_button_wrapper {
    #contact-button {
      border-radius: 0;
      background-color: $corporate_2;
    }
  }
}


.section_content {
  .normal_section_mobile {
    .section-subtitle {
      text-align: left;
      padding: 0 20px;
    }
  }
}