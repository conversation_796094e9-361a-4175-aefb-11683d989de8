.cycle_banner_wrapper {
  @include base_banner_styles;

  .banner {
    @include display_flex;
    width: 100%;
    align-items: center;

    &:nth-of-type(even) {
      flex-direction: row-reverse;
    }

    &:not(:last-of-type) {
      margin-bottom: 30px;
    }

    .img_wrapper {
      position: relative;
      display: inline-block;
      width: 50%;
      height: 384px;
      overflow: hidden;

      .owl-item {
        height: 384px;
        .img_container {
          .gallery_icon {
            display: inline-block;
            position: absolute;
            z-index: 5;
            right: 15px;
            top: 15px;
            color: white;
            font-size: 36px;
          }

          img {
            @include center_image;
          }
        }
      }

      .owl-nav {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 50px;

        .owl-prev, .owl-next {
          @include owl_nav_styles(white);
          position: absolute;
          left: 15px;
          top: 0;
          font-size: 26px;
        }

        .owl-next {
          left: auto;
          right: 15px;
        }
      }
    }

    .banner_content {
      position: relative;
      display: inline-block;
      width: 50%;
      overflow: hidden;
      background-color: $lightgrey;
      padding: 40px;
      text-align: right;

      .title {
        text-transform: uppercase;
        font-size: 35px;
        line-height: 52px;
        color: $grey;
        font-family: $title_family;
        font-weight: 700;
        text-align: left;

        .subtitle {
          text-transform: none;
          font-size: 16px;
          line-height: 25px;
          font-family: $title_family_2;
          color: $corporate_1;
          display: block;
        }
      }

      .text {
        @include text_styles;
        font-size: 14px;
        line-height: 22px;
        text-align: left;
        padding: 20px 0 40px;
      }

      .link {
        position: relative;
        color: $corporate_1;
        text-transform: uppercase;
        font-weight: 700;
        font-size: 15px;
        line-height: 18px;
        @include transition(all, .6s);

        &:after {
          position: absolute;
          content: '\f054';
          font-size: 0.9em;
          right: -20px;
          top: 2px;
          font-weight: lighter;
          color: $corporate_1;
          font-family: 'Font Awesome 5 Pro';
          transition: all .5s;
        }

        &:hover {
          &:after {
            right: -25px;
          }
        }
      }
    }
  }
}