.section_offers_wrapper {
  @include base_banner_styles;
  padding-top: 0;
}

.content_offers_title {
  margin-bottom: 80px;

  .title {
    @include title_styles_offers(24px);
    color: #363434;
    font-family: $text_family;
    font-weight: 500;
    text-align: center;
  }
}

.banner_offers {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  padding: 40px 0 0;

  &.ofertas {
    .offer.paquete {
      display: none;
    }
  }

  &.paquetes {
    border-bottom: solid 1px #acacac;

    .offer.oferta {
      display: none;
    }
  }

  .offer {
    width: calc((100% / 3) - 30px);
    background-color: white;
    position: relative;
    box-shadow: 0px 0px 17px 10px rgba(0, 0, 0, 0.11);
    overflow: visible;
    margin: 0 15px 100px;


    &:hover {
      .offer_image {
        &:before {
          opacity: 1;
        }

        .center_xy {
          transform: scale(1);

          span {
            opacity: 1;
          }
        }
      }
    }

    .picture_wrapper {
      height: 250px;
      overflow: hidden;

      img {
        @include cover_image;
      }

      .picture_description {
        position: absolute;
        top: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        background-color: $corporate_1;

        span {
          color: white;
          font-size: 12px;

          strong {
            display: block
          }
        }
      }
    }

    .content_wrapper {
      text-align: center;
      padding: 40px 20px 80px;
      position: relative;

      .icon_wrapper {
        position: relative;
        background: #E5D69A;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;

        i {
          color: white;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 30px;
        }
      }

      .content_title {
        margin-bottom: 20px;
        position: relative;

        .title {
          padding-bottom: 20px;
          @include title_styles_offers(35px);
          font-size: 30px;
          line-height: 35px;
        }

        &::before {
          position: absolute;
          content: '';
          height: 2px;
          bottom: 0;
          width: 50px;
          left: 50%;
          background-color: #C3A3BC;
          transform: translateX(-50%);
        }
      }

      .desc {
        font-size: 14px;
        margin-bottom: 20px;
        color: #4a4a4a;
      }
    }

    .btn_wrapper {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      bottom: 0;
      overflow: hidden;

      &.btn_half {
        width: 100%;

        .btn {
          display: inline-block;
          width: 45%;
          transform: translate(10%, 0);
          padding: 15px 30px !important;
        }

        a {
          background: white;
          color: $corporate_2;
          border: 1px solid $corporate_2;
          padding: 9px 0 !important;
        }
      }

      .btn {
        display: block;
      }
    }

    &::before {
      position: absolute;
      content: '';
      height: 50px;
      background-color: white;
      bottom: 0;
      left: 0;
      right: 0;
      transform: translateY(100%);
    }
  }
}

///To check
.offers_filter {
  display: none;
  margin-bottom: 40px;

  .filter_selector,
  .filter_remove {
    position: relative;
    display: inline-block;
    font-size: 14px;
    border: 1px solid $corporate_1;
  }

  .filter_selector {
    padding: 10px;
    width: 150px;

    &:before {
      font-family: "Font Awesome 5 Pro";
      content: "\f078";
      font-size: 35px;
      @include center_y;
      color: $corporate_1;
      left: auto;
      right: 10px;
    }

    &.active {
      border-color: $black;

      &:before {
        color: $black;
        content: "\f077";
      }
    }
  }

  .filter_remove {
    position: relative;
    float: right;
    padding: 10px 30px 10px 10px;
    width: 150px;
    color: #DFE2E3;
    border: 1px solid #DFE2E3;

    &:before {
      position: absolute;
      font-family: "Font Awesome 5 Pro";
      content: "\f00d";
      color: DFE2E3;
      right: 10px;
    }

    &.active {
      border: none;
      background-color: $corporate_1;
      color: white;

      &:before {
        color: white;
      }
    }
  }

  .filter_list_wrapper {
    margin-top: 10px;

    .filter_list {
      display: none;
      padding: 5px 0;
      background-color: $lightgrey;
      margin: 2px 0;

      .filter_element {
        position: relative;
        display: inline-block;
        font-size: 14px;
        padding: 5px 10px 5px 35px;

        &.active {

          &:before {
            font-family: "Font Awesome 5 Pro";
            content: "\f00c";
            font-weight: 700;
            color: #cdacc5;
            position: absolute;
            top: 6px;
            bottom: 0;
            left: 12px;
            right: 0;
          }
        }

        i.fa {
          display: inline-block;
          vertical-align: middle;
          color: $corporate_2;
          margin-right: 5px;
          font-size: 150%;
        }

        &:after {
          content: '';
          display: block;
          @include center_y;
          left: 10px;
          border: 1px solid #999;
          width: 13px;
          height: 13px;
        }
      }
    }
  }
}

#ui-datepicker-div {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.fancybox-outer {
  background: white;
}

.contact_form_popup {
  width: 100%;
  margin: auto;
  display: none;
  position: relative;
  background-image: url("/img/#{$base_web}/main_logo_dark.png");
  background-size: auto 100%;
  background-position: 5%;
  height: 70px;
  background-repeat: no-repeat;
  margin-bottom: 20px;


  .content_title {
    @include title_styles_offers(35px);
    padding-bottom: 15px;
    text-align: center;
    margin-bottom: 40px;
    margin-top: 10px;

    small {
      display: block;
      vertical-align: middle;
      line-height: 28px;
      width: 270px;
      font-size: 22px;
      color: black;
      font-family: $text_family;
      font-weight: 400;
      letter-spacing: 2px;
      margin: auto;
      text-align: center;
      margin-top: 25px;
    }
  }

  .contact_banner_form {
    width: 75%;
    margin: auto;
    position: relative;

    .field {
      margin-bottom: 25px;
      display: inline-block;
      position: relative;

      &:nth-of-type(1) {
        float: left;
        width: 45%;
      }

      &:nth-of-type(2) {
        float: right;
        width: 45%;
      }

      &:nth-of-type(3), &:nth-of-type(4) {
        width: 30%;
        margin-right: 26px;
      }

      &:nth-of-type(5) {
        width: 30%;
      }

      &:nth-of-type(6) {
        width: 100%;
      }

      label {
        font-size: 13px;
        color: $corporate_1;
        display: block;
        position: absolute;
        top: 45px;
      }

      input, textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: $lightgrey;
        padding: 15px;
        font-size: 18px;
        border-width: 0;
        width: 100%;
        height: 75px;

        &::-webkit-input-placeholder {
          color: #707070;
          font-weight: 500;
        }

        &:-moz-placeholder {
          /* Firefox 18- */
          color: #707070;
        }

        &::-moz-placeholder {
          /* Firefox 19+ */
          color: #707070;
        }

        &:-ms-input-placeholder {
          color: #707070;
        }
      }

      textarea {
        resize: none;
        width: 100%;
        @include input-placeholder {
          font-family: $text_family;
        }
      }
    }
  }

  .contInput.policy-terms {
    display: inline-block;
    vertical-align: middle;

    label.error {
      color: red;
    }
  }

  .g-recaptcha {
    float: none !important;
    margin-bottom: 30px;
  }

  .send_button_element {
    opacity: 100%;
    @include transition(opacity, .6s);
    position: absolute;
    bottom: 0px;
    right: 0;
    text-transform: capitalize;
    font-size: 28px;
    font-weight: 700;
    background-color: $corporate_2;
    color: white;
    padding: 20px 50px;
  }
}

.secondary_popup {
  width: auto;
  padding: 10px;
  text-align: center;
  vertical-align: middle;
  display: block;
  margin-top: 15px;
  font-family: $text_family;
}