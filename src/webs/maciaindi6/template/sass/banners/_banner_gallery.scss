.banner_gallery_wrapper {
  @include before_bg;
  @include base_banner_styles;
  display: table;

  &:before {
    top: 180px;
  }

  .banner {
    position: relative;
    display: inline-block;
    float: left;
    height: 270px;
    margin-left: 20px;
    width: calc(25% - 15px);
    overflow: hidden;
    cursor: pointer;
    margin-bottom: 10px;

    img {
      @include center_image;
      width: auto;
    }

    .img_content {
      @include full_size;
      z-index: 2;
      background-color: rgba($corporate_1, .8);
      opacity: 0;
      visibility: hidden;
      @include transition(all, .6s);

      .hotel_logo {
        @include center_xy;
        min-width: auto;
        min-height: auto;
        max-width: 100%;
        width: 240px;
        opacity: .4;
      }

      .serarch_icon {
        @include center_xy;
        z-index: 4;
        color: white;
        font-size: 64px;
      }
    }

    &:first-of-type, &:nth-of-type(5n) {
      width: calc(50% - 10px);
      height: 560px;
      margin-left: 0;
    }

    &:nth-of-type(3n) {
      margin-left: 20px;
    }

    &:nth-of-type(4n) {
      width: calc(50% - 10px);
      margin-top: 20px;
    }

    &:hover {
      .img_content {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .owl-nav {
    text-align: center;
    margin-top: 30px;

    .owl-prev, .owl-next {
      @include owl_nav_styles;
      margin: 0 35px 0 0;

      i {
        font-size: 48px;
      }
    }

    .owl-next {
      margin: 0 0 0 35px;
    }
  }
}