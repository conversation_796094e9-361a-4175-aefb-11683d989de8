.banner_testimonials_wrapper {
  @include base_banner_styles;

  .content_title {
    @include title_styles;
  }

  .owl-carousel {
    .owl-item {
      .testimonial_wrapper {
        text-align: center;
        padding: 50px 0 20px;

        .testimonial_avatar {
          text-align: center;

          img {
            display: inline-block;
            max-width: 100px;
          }
        }
        .desc {
          @include text_styles;
          line-height: 19px;
          padding: 0 20px 25px;
        }

        .testimonial_reviews {
          .review {
            font-size: 18px;
            line-height: 20px;
            color: $color_text;
            font-weight: 300;
          }

          .flag_lang {
            height: 15px;
            width: auto;
            display: block;
            margin: 5px auto;
          }

          .rating {
            font-size: 24px;
            color: $corporate_1;
            font-family: $title_family;
            font-weight: 400;
          }

          .date {
            font-size: 12px;
            font-weight: 400;
            color: $corporate_1;
          }
        }

        .avatar_wrapper {
          position: absolute;
          width: auto;
          height: 80%;
          @include center_xy;

          * {
            filter: grayscale(100%);
          }

          img {
            position: relative;
            max-height: 100%;
            max-width: 100%;
            object-fit: contain;
          }

          i {
            font-size: 200px;
            opacity: .1;

          }
        }
      }
    }
    .owl-dots {
      text-align: center;
      width: 100%;

      .owl-dot {
        @include owl_dots_styles;
      }
    }
  }
}