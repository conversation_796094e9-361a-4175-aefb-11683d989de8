.gallery_filter_wrapper {
  .gallery_block {
    @include base_banner_styles;

    &:first-of-type {
      padding-top: 0;
    }

    .gallery_title {
      padding-bottom: 15px;
      text-transform: uppercase;
      font-family: $title_family;
      font-size: 30px;
      line-height: 45px;
      font-weight: bold;
      color: $grey;
      display: block;
    }

    .gallery_group_wrapper {
      display: table;
      width: 100%;

      .image_wrapper {
        position: relative;
        display: inline-block;
        height: 270px;
        float: left;
        margin-left: 10px;
        margin-bottom: 20px;
        width: calc(25% - 15px);
        overflow: hidden;
        cursor: pointer;
        @extend .fa-search-plus;

        &:before {
          @extend .fal;
          z-index: 10;
          font-size: 40px;
          color: white;
          opacity: 0;
          @include center_xy;
          @include transition(opacity, 1s);
        }

        &:after {
          content: '';
          @include full_size;
          background-color: rgba($corporate_1, .8);
          z-index: 5;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
        }

        .hotel_logo {
          @include center_xy;
          z-index: 10;
          min-width: auto;
          min-height: auto;
          max-width: 100%;
          width: 240px;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
        }

        .img_info {
          @include center_xy;
          display: inline-block;
          color: white;
          font-size: 14px;
          opacity: 0;
          visibility: hidden;
          z-index: 8;
          @include transition(all, .6s);
        }

        img {
          @include center_image;
        }

        &:first-of-type {
          width: calc(50% - 10px);
          height: 560px;
          margin: 0 10px 0 0;
        }

        &:nth-of-type(3), &:nth-of-type(5) {
          margin-left: 20px;
        }

        &:hover {
          &:before, &:after, .banner_info {
            opacity: 1;
            visibility: visible;
          }

          .hotel_logo {
            opacity: .4;
            visibility: visible;
          }
        }
      }

      &:nth-of-type(odd) {
        .image_wrapper {
          margin-left: 0;
          margin-right: 10px;

          &:first-of-type {
            float: right;
            margin: 0 0 0 10px;
          }

          &:nth-of-type(2), &:nth-of-type(4) {
            margin-left: 0;
            margin-right: 20px;

            &:last-of-type {
              float: right;
            }
          }
        }
      }
    }
  }
}