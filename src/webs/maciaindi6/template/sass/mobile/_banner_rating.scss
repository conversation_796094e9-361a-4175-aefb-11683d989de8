.banner_rating_wrapper {
  @include base_mobile_styles;
  border-bottom: 1px solid $corporate_3;
  margin-bottom: 20px;

  .rating_title {
    margin: auto;
    display: block;
    width: max-content;
    text-align: center;
    font-family: $title_family;
    font-size: 20px;
    color: #707070;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 20px;
  }
}

.rating_logos_wrapper {
  @include display_flex;
  justify-content: center;
  align-items: center;
  $circle_color: #00AA6C;

  .rating {
    position: relative;
    margin-right: 20px;
    @include display_flex;
    align-items: center;

    img {
      display: inline-block;
      vertical-align: middle;
      font-size: 25px;
      color: #000000;
      margin-right: 5px;
    }

    .circle {
      display: inline-block;
      vertical-align: middle;
      width: 16px;
      height: 16px;
      margin-right: 2px;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      border: 1px solid $circle_color;

      &::before {
        position: absolute;
        content: '';
        background-color: $circle_color;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
      }

      &.half {
        &::before {
          transform: translateX(-50%);
        }
      }
    }

    &.booking {
      .circle {
        border: 1px solid #06367c;

        &:before {
          background-color: #06367c;
        }
      }
    }

    &.hotels {
      .circle {
        border: 1px solid #ec2c44;

        &:before {
          background-color: #ec2c44;
        }
      }
    }

    &.holidaycheck {
      .circle {
        border: 1px solid #fbd822;

        &:before {
          background-color: #fbd822;
        }
      }
    }

    &.t1 {
      .circle:not(:first-of-type) {
        &:before {
          background-color: transparent;
        }
      }
    }

    &.t1-5 {
      .circle {
        &:nth-last-of-type(-n+3) {
          &:before {
            background-color: transparent;
          }
        }

        &:nth-of-type(2) {
          &:before {
            transform: translateX(-50%);
          }
        }
      }
    }

    &.t2 {
      .circle {
        &:nth-last-of-type(-n+3) {
          &:before {
            background-color: transparent;
          }
        }
      }
    }

    &.t2-5 {
      .circle {
        &:nth-last-of-type(-n+2) {
          &:before {
            background-color: transparent;
          }
        }

        &:nth-of-type(3) {
          &:before {
            transform: translateX(-50%);
          }
        }
      }
    }

    &.t3 {
      .circle {
        &:nth-last-of-type(-n+2) {
          &:before {
            background-color: transparent;
          }
        }
      }
    }

    &.t3-5 {
      .circle {
        &:nth-last-of-type(-n+1) {
          &:before {
            background-color: transparent;
          }
        }

        &:nth-of-type(4) {
          &:before {
            transform: translateX(-50%);
          }
        }
      }
    }

    &.t4 {
      .circle {
        &:nth-last-of-type(-n+1) {
          &:before {
            background-color: transparent;
          }
        }
      }
    }

    &.t4-5 {
      .circle {
        &:nth-of-type(5) {
          &:before {
            transform: translateX(-50%);
          }
        }
      }
    }

    &.t5 {
      .circle {
        &:before {
          background-color: $circle_color;
        }
      }

      &.booking {
        .circle {
          &:before {
            background-color: #06367c;
          }
        }
      }

      &.hotels {
        .circle {
          &:before {
            background-color: #ec2c44;
          }
        }
      }

      &.holidaycheck {
        .circle {
          &:before {
            background-color: #fbd822;
          }
        }
      }
    }

    &:last-of-type {
      margin-right: 0;
    }
  }
}