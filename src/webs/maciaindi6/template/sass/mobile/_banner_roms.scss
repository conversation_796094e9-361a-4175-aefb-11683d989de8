.banner_rooms_wrapper {
  @include base_mobile_styles;
  padding: 20px 0;

  .banner_rooms {
    .room {
      .room_content {
        position: relative;
        background-color: $lightgrey;
        padding: 20px;
        text-align: left;
        margin-bottom: 20px;

        .title {
          @include banner_title_styles;
          margin-bottom: 20px;
        }

        .text {
          @include text_styles;
          //@include text_styles_mobile;
          text-align: left;
          display: -webkit-box;
          -webkit-line-clamp: 5;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 15px;
          font-size: 18px;
          line-height: 21px;
          font-weight: 500;
          text-rendering: auto;
          -webkit-font-smoothing: antialiased;
          color: #666;

          .hide_in_web {
            display: none;
          }
        }

        .see_more_rooms {
          display: inline-block;
          position: relative;
          color: $corporate_1;
          @include transition(all, .6s);

          &:before {
            position: absolute;
            content: '\f054';
            font-size: 0.8em;
            right: -17px;
            top: 4px;
            font-weight: lighter;
            color: $corporate_1;
            font-family: 'Font Awesome 5 Pro';
            @include transition(right, .4s);
          }

          &:hover {
            &:before {
              right: -22px;
            }
          }
        }

        .links {
          .btn_personalized_1 {
            margin: 30px 0 0;
          }
        }
      }

      .room_pic {
        overflow: hidden;
        width: 100%;
        height: 300px;
        position: relative;

        .gallery_icon {
          display: inline-block;
          position: absolute;
          z-index: 5;
          right: 15px;
          top: 15px;
          color: white;
          font-size: 36px;
        }

        .owl-stage-outer, .owl-stage, .owl-item {
          height: 100%;
          overflow: hidden;

          .image {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;

            img {
              @include cover_image;
            }
          }
        }

        .owl-nav {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 50px;

          .owl-prev, .owl-next {
            @include owl_nav_styles(white);
            position: absolute;
            left: 15px;
            top: 0;
            font-size: 26px;
          }

          .owl-next {
            left: auto;
            right: 15px;
          }
        }
      }
    }

    > .owl-nav {
      width: 90%;
      display: block;
      position: relative;
      margin: 10px auto 0;

      &:after {
        content: '';
        width: 1px;
        height: 70%;
        background-color: $corporate_1;
        @include center_xy;
      }

      .owl-prev, .owl-next {
        @include owl_nav_styles;
        width: 50%;
        text-align: center;
        padding: 10px;
        font-size: 12px;
        background-color: $lightgrey;
        color: $corporate_3;

        span {
          display: inline-block;
          vertical-align: middle;
          position: relative;
          z-index: 2;
          margin-left: 10px;
        }

        i {
          display: inline-block;
          vertical-align: middle;
          font-size: 18px;
          color: $corporate_1;
        }
      }

      .owl-next {
        span {
          margin-right: 10px;
          margin-left: 0;
        }
      }
    }
  }
}