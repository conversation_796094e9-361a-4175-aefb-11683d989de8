<footer>
    <div id="footer_to_top">
        <i class="far fa-arrow-up"></i>
    </div>
    <div class="footer_content">
        <div class="footer_columns_wrapper">
            {% for foo in footer_column %}
                <div class="hotels_column">
                    {% if foo.title %}
                        <div class="content_title">
                            <h4 class="title">{{ foo.title|safe }}</h4>
                        </div>
                    {% endif %}
                    {% if foo.servingUrl %}
                        <div class="logo_wrapper">
                            <a href="{{host|safe}}/"><img src="{{ foo.servingUrl|safe }}" alt=""></a>
                        </div>
                    {% endif %}
                    {% if foo.description %}
                        <div class="desc">
                            {{ foo.description|safe }}
                        </div>
                    {% endif %}
                    {% if foo.rrss %}
                        <div class="footer_rrss">
                            {% if facebook_id %}
                                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank" rel="nofollow">
                                    <i class="fa fa-facebook" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                            {% if twitter_id %}
                                <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank" rel="nofollow">
                                    <i class="fa fa-twitter" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                            {% if google_plus_id %}
                                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="nofollow">
                                    <i class="fa fa-google-plus" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                            {% if youtube_id %}
                                <a href="https://www.youtube.com/{{youtube_id}}" target="_blank" rel="nofollow">
                                    <i class="fa fa-youtube" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                            {% if pinterest_id %}
                                <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank" rel="nofollow">
                                    <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                            {% if instagram_id %}
                                <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank" rel="nofollow">
                                    <i class="fa fa-instagram" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
            {% if footer_columns_logos %}
                <div class="hotels_column footer_columns_logos">
                    {% for logo in footer_columns_logos %}
                        {% if logo.servingUrl %}
                            <a {% if logo.linkUrl %}href="{{ logo.linkUrl|safe }}" {% if "http" in logo.linkUrl %}target="_blank" {% endif %} {% endif %}  class="logo">
                                <img src="{{ logo.servingUrl|safe }}" {% if logo.altText %}alt="{{ logo.altText|safe }}" {% endif %}>
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        <div class="footer_legal_text_wrapper">
            <div class="container12">
                <div class="footer_links_wrapper">
                    {% for x in policies_section %}
                        <a href="{% if x.custom_link %}{{ x.custom_link }}{% else %}/{{ language }}/?sectionContent={{ x.friendlyUrl }}{% endif %}"
                           class="{% if not x.custom_link %}myFancyPopup fancybox.iframe{% endif %}" rel="nofollow">{{ x.title|safe }}</a>
                    {% endfor %}
                    {% if derecho_obligaciones %}
                        <a class="myFancyPopup fancybox.iframe"  href="/{{language}}/?sectionContent={{ derecho_obligaciones.friendlyUrl|safe }}" rel="nofollow">{{ derecho_obligaciones.title|safe }}</a> |
                    {% endif %}
                    {% if extra_footer_link %}
                        {% for link in extra_footer_link %}
                            <a href="{{ link.linkUrl }}" class="extra_footer_link" {% if 'http' in link.linkUrl %}target="_blank"{% endif %}>{{ link.title|safe }}</a>
                        {% endfor %}
                    {% endif %}
                    <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
                       title="{{ T_motor_de_reservas }}" rel="nofollow">{{ T_motor_de_reservas }}</a>
                    <a target="_blank" href="/sitemap.xml" title="" rel="nofollow">Site Map</a>
                    <a target="_blank" href="/rss.xml" rel="nofollow">RSS</a>
                </div>

                {% if texto_legal %}
                    <div class="legal_text">{{ texto_legal|safe }}</div>
                {% endif %}
            </div>
        </div>
    </div>
</footer>