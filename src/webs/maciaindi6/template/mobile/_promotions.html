{% if section_defaults %}
    <div class="content_title offers_section">
        {% if section_defaults.title %}
            <div class="title">
                {{ section_defaults.title|safe }}
            </div>
        {% endif %}
        {% if section_defaults.subtitle %}
            <div class="offers_subtitle section-subtitle">
                {{ section_defaults.subtitle|safe }}
            </div>
        {% endif %}
    </div>
{% endif %}
{% if elements %}
    <div class="owlslider owl-carousel promotions_wrapper">
    {% for promotion in elements %}
        <div class="offer">
            <div class="offer_content">
                <div class="picture"><img src="{{promotion.pictures.0}}"></div>
                {% if use_h2 %}
                    <h2>{{promotion.name|safe}}</h2>
                {% else %}
                    <h3>{{promotion.name|safe}}</h3>
                {% endif %}
                <div class="desc">{{promotion.description|safe}}</div>
                <div class="offer_links_wrapper {% if (promotion.linkUrl and "http" in promotion.linkUrl or promotion.extra_properties.customized_url) and not promotion.extra_properties.no_booking %}btn_half{% endif %}">
                    {% if promotion.linkUrl or promotion.extra_properties.customized_url%}
                        <a href="{{promotion.extra_properties.customized_url if promotion.extra_properties.customized_url else promotion.linkUrl}}" class="offer_link">
                            {% if promotion.title %}{{ promotion.title|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                        </a>
                    {% endif %}
                    {% if not promotion.extra_properties.no_booking %}
                        <div class="button-promotion" {% if promotion.extra_properties.hide_button_mobile %}style="display:none;" {% endif %} {% if promotion.smartDatasAttributes %}{{ promotion.smartDatasAttributes }}{% endif %}><i class="fa fa-calendar"></i>{{T_reservar}}</div>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endfor %}
    </div>
{% else %}
    <div class="no_offers_wrapper" style="margin-top: 20px">{{ T_no_offers }}</div>
{% endif %}

<script>
$(window).on("load", function () {
    /* Main slider*/
    offerowl_params = {
        loop: {% if elements|length > 1 %}true{% else %}false{% endif %},
        nav: true,
        dots: false,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".promotions_wrapper").owlCarousel(offerowl_params);
});
</script>
<style>
    body {
        padding-bottom: 0;
    }
</style>

{% if custom_elements_promotions %}
    {{ custom_elements_promotions|safe }}
{% endif %}