<div class="banner_map_wrapper">
    {% if banner_map_content %}
        <div class="banner_map_content">
            {% if banner_map_content.subtitle %}<h3 class="content_title">{{ banner_map_content.subtitle|safe }}</h3>{% endif %}
            {% if banner_map_content.content %}<div class="content_text">{{ banner_map_content.content|safe }}</div>{% endif %}
            {% if banner_map_icons %}
                <div class="banner_map_icons">
                    {% for icon in banner_map_icons %}
                        {% if not 'how_to_get_button' in icon.title %}
                        <div class="icon">
                            {% if icon.title %}<i class="{{ icon.title|safe }}"></i>{% endif %}
                            {% if icon.description %}<span class="icon_text">{{ icon.description|safe }}</span>{% endif %}
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
            {% if not banner_map_image and iframe_map_location %}
                {% if banner_map_button %}
                    <a href="{{ banner_map_button_link }}" class="link_map">{{ banner_map_button|safe }}</a>
                {% endif %}
                <div class="map_widget" style="display: none">
                    <input type="text" class="place" placeholder="{{ T_desde }}..."><a class="go">{{ T_como_llegar }}</a>
                    <div class="go_map"><i class="fal fa-chevron-up"></i></div>
                </div>
            {% endif %}
        </div>
    {% endif %}
    {% if banner_map_image %}
        <div class="iframe_map_location">
            <a {% if banner_map_image.linkUrl %}href="{{ banner_map_image.linkUrl|safe }}"{% if "http" in banner_map_image.linkUrl %}target="_blank"{% endif %}{% endif %}>{% if banner_map_image.servingUrl %}<img src="{{ banner_map_image.servingUrl|safe }}=s1900" alt="">{% endif %}</a>
        </div>
    {% else %}
        <div class="iframe_map_location">
            {{ iframe_map_location|safe }}
        </div>
    {% endif %}
</div>

<script async src="/static_1/lib/jquery.simpleweather/jquery.simpleWeather.min.js?v=1.1"></script>
<script>
    $(window).load(function () {
        /*
        $(".link_map").click(function () {
            mapaUrl = "https://www.google.com/maps?saddr=@place@&daddr={% if banner_map_location %}{{ banner_map_location|safe }}{% else %}Hotel+Spiwak,+Av.+6+D+36N-18{% endif %}&output=embed";
            newurl = mapaUrl.replace("@place@", $(".place").val());

            $(".iframe_map_location iframe").attr("src",newurl);
        });
        */

        {% if banner_map_weather_city %}
            var temp_container = $(".banner_map_icons .icon .weather_temp");
            if (temp_container.length) {
                $.simpleWeather({
                    location: '{{ banner_map_weather_city|safe }}',
                    woeid: '',
                    unit: 'c',
                    success: function (weather) {
                        var weather_temp = Math.round(weather.temp);
                        temp_container.html(weather_temp + "º");
                    },
                    error: function (error) {
                        temp_container.html(error);
                    }
                });
            }
        {% endif %}

        {% if not is_mobile and not user_isIpad %}
            function banner_map_fx() {
                $(".banner_map_wrapper").addnimation({parent:$(".banner_map_wrapper"), class:"fadeInUp", reiteration: false});
            }
            banner_map_fx();
            $(window).scroll(banner_map_fx);
        {% endif %}
    });
</script>