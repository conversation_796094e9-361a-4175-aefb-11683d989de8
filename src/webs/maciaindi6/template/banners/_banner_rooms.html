<div class="banner_rooms_wrapper">
    <div class="banner_rooms owl-carousel">
        {% for room in banner_rooms_pics %}
            <div class="room">
                <div class="room_pic owl-carousel">
                    <div class="image">
                        {% if not is_mobile %}
                            <a href="{{ room.servingUrl }}=s1900" rel="lightbox[{{ room.key|safe }}]" class="gallery_icon">
                                <i class="fal fa-search-plus"></i>
                            </a>
                        {% endif %}
                        <img src="{{ room.servingUrl }}=s800" alt="{{ room.title|safe }}">
                    </div>
                    {% for img in room.pictures %}
                        <div class="image">
                            {% if not is_mobile %}
                                <a href="{{ img.servingUrl }}=s1900" rel="lightbox[{{ room.key|safe }}]" class="gallery_icon">
                                    <i class="fal fa-search-plus"></i>
                                </a>
                            {% endif %}
                            <img src="{{ img.servingUrl }}=s800" {% if img.altText %}alt="{{ img.altText|safe }}"{% endif %}>
                        </div>
                    {% endfor %}
                </div>
                <div class="room_content">
                    {% if room.title %}<h3 class="title">{{ room.title|safe }}</h3>{% endif %}
                    {% if room.description %}<div class="text">{{ room.description|safe }}</div>{% endif %}
                    {% if rooms_section_url %}
                        <div class="link_seemore">
                            <a href="{{ rooms_section_url|safe }}?room={{ room.filter_url|safe }}" class="see_more_rooms">
                                {{ T_ver_mas }}
                            </a>
                        </div>
                    {% endif %}
                    <div class="links">
                        <a href="#data" class="button-promotion btn_personalized_1">
                           {{ T_reservar }}
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
   $(".banner_rooms.owl-carousel").owlCarousel({
        loop: false,
        nav: true,
        dots: true,
        items: 1,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i><span>{{ T_previous_room }}</span>', '<span>{{ T_next_room }}</span><i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
        autoHeight: true,
        mouseDrag: false,
        touchDrag: false
    });

   {% if not is_mobile %}
       $(".banner_rooms .room").each(function () {
            var content_height = $(this).find(".room_content").outerHeight(),
                image_height = content_height + 140;
            $(this).find(".room_pic").outerHeight(image_height);
        });
    {% endif %}

   $(".room_pic.owl-carousel").owlCarousel({
        loop: true,
        nav: true,
        dots: true,
        items: 1,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i>', '<i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
        mouseDrag: true
    });

    {% if not is_mobile and not user_isIpad %}
        function banner_room_fx() {
            $(".banner_rooms_wrapper .owl-item").addnimation({parent:$(".banner_rooms_wrapper"), class:"fadeInUpBig", reiteration: false});
        }
        banner_room_fx();
        $(window).scroll(banner_room_fx);
    {% endif %}
});</script>