<div class="section_offers_wrapper">
    <div class="banner_offers offers">
        {% for offer in offers %}
            <div class="offer">
                <div class="picture_wrapper">
                    <img data-src="{{ offer.picture }}=s500" alt="{{ offer.name }}" lazy="true">
                    {% if offer.picDesc %}
                        <div class="pic_info">
                            {{ offer.picDesc|safe }}
                        </div>
                    {% endif %}
                </div>
                <div class="content_wrapper">
                    <div class="content_title">
                        <h3 class="title">{{ offer.name|safe }}</h3>
                    </div>
                    <div class="desc">{{ offer.description|safe }}</div>
                </div>
                <div class="btn_wrapper {% if (offer.linkUrl and "http" in offer.linkUrl or offer.extra_properties.customized_url) and not offer.extra_properties.no_booking %}btn_half{% endif %}">
                    {% if offer.linkUrl or offer.extra_properties.customized_url %}
                        <a href="{{ offer.extra_properties.customized_url if offer.extra_properties.customized_url else offer.linkUrl }}" class="btn_personalized_1 btn">
                            {% if offer.title %}{{ offer.title|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                        </a>
                    {% endif %}
                    {% if not offer.extra_properties.no_booking %}
                        <div class="button_promotion btn_personalized_1 btn" {% if offer.extra_properties.hide_button_mobile %}style="display:none;" {% endif %} {% if offer.smartDatasAttributes %}{{ offer.smartDatasAttributes }}{% endif %}>{{T_reservar}}</div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
    {% if packs %}
        {% if offers_packs_title  %}
            <h4 class="offers_packs_title">{{ offers_packs_title|safe }}</h4>
        {% endif %}

        <div class="banner_offers packs">
            {% for offer in packs %}
                <div class="offer">
                    <div class="picture_wrapper">
                        <img data-src="{{ offer.picture }}=s500" alt="{{ offer.name }}" lazy="true">
                        {% if offer.picDesc %}
                            <div class="pic_info">
                                {{ offer.picDesc|safe }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="content_wrapper">
                        <div class="content_title">
                            <h3 class="title">{{ offer.name|safe }}</h3>
                        </div>
                        <div class="desc">{{ offer.description|safe }}</div>
                    </div>
                    <div class="btn_wrapper {% if (offer.linkUrl and "http" in offer.linkUrl or offer.extra_properties.customized_url) and not offer.extra_properties.no_booking %}btn_half{% endif %}">
                        {% if offer.linkUrl or offer.extra_properties.customized_url %}
                            <a href="{{ offer.extra_properties.customized_url if offer.extra_properties.customized_url else offer.linkUrl }}" {% if offer.linkUrl and "http" in offer.linkUrl or offer.extra_properties.customized_url %}target="_blank"{% endif %} class="btn_personalized_1 btn">
                                {% if offer.title %}{{ offer.title|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                            </a>
                        {% endif %}
                        {% if not offer.extra_properties.no_booking %}
                            <a href="#data" class="button_promotion btn_personalized_1 btn"
                               {% if offer.smartDatasAttributes %}{{ offer.smartDatasAttributes|safe }}{% endif %}>
                            {{ T_reservar }}</a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
