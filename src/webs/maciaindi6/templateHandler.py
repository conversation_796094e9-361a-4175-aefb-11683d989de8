# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.utils.booking.normalizationUtils import normalizeFor<PERSON>lass<PERSON>ame
from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY, WHATSAPP_MOBILE_ID, EMAIL_CONTACT_FORMS, \
	EMAIL_BOOKING, ROOMS_ICONS, CONTACT_PHONES, LANGUAGE
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.hotel_data import get_host
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV
from booking_process.utils.language.language_utils import get_language_code, get_web_dictionary, get_language_title
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

TEMPLATE_NAME = "maciaindi6"
#Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4]+TEMPLATE_NAME[-1:]

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		languageCode = get_language_code(language)
		params = {
			'base_web': base_web,
			'derecho_obligaciones': get_section_from_section_spanish_name("Derechos y obligaciones del usuario", language)
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True,
				'is_mobile': True
			}
			params_mobile["custom_element_home"] += "<script async type='text/javascript' src='/js/" + base_web + "/functions_mobile.js?v=1.10'></script>"

			email_header = get_config_property_value(EMAIL_CONTACT_FORMS)

			header_args = {
				'email_header': email_header if email_header else get_config_property_value(EMAIL_BOOKING),
				'whatsapp_mobile_id': get_config_property_value(WHATSAPP_MOBILE_ID),
			}
			params_mobile['custom_header'] = self.buildTemplate_2("mobile/_custom_header.html", header_args, False, TEMPLATE_NAME)

			custom_social_header_args = {
				'language_codes': self.getOrderedLanguages(),
				'language': get_language_code(language),
				'host': get_host(languageCode, True),
				'hostWithoutLanguage': get_host(languageCode, False),
			}

			social_ids = self.getSocialDictionary()
			custom_social_header_args.update(social_ids)

			params_mobile['custom_social_menu'] = self.buildTemplate_2("mobile/_custom_social_menu.html", custom_social_header_args, False, TEMPLATE_NAME)

			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=False),
			'phone_contact': get_config_property_value(CONTACT_PHONES),
			'language_selected': get_language_code(language),
			'footer_widget': get_section_from_section_spanish_name("_footer_widget", language),
			'extra_footer_link': self.getPicturesProperties(language, "_extra_footer_link"),
			'email_in_header': get_pictures_from_section_name("_email_in_header", language),
			'popup_inicio': get_pictures_from_section_name("_popup_inicio_automatico_2", language)
		}

		emails_contact = get_config_property_value(EMAIL_BOOKING).split(";")
		if emails_contact:
			result_params_dict['email_contact'] = emails_contact[0]

		footer_columns = self.getPicturesProperties(language, '_footer_columns')
		result_params_dict['footer_column'] = []
		result_params_dict['footer_columns_logos'] = []
		for column in footer_columns:
			if column.get("title") == "logo":
				result_params_dict['footer_columns_logos'].append(column)
			else:
				result_params_dict['footer_column'].append(column)

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section
			content_images = get_pictures_from_section_name(section_name, language)
			if content_images:
				result_params_dict['content_subtitle_images'] = filter(
					lambda x: x.get('title') != 'slider' and x.get('title') != 'ico', content_images)

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		if section_type == "Inicio":
			result['home'] = True

		elif section_type == "Habitaciones":
			result['rooms'] = self.getRooms(language)

		elif section_type == "Ofertas":
			result['offers'], result['packs'] = self.getOffers(language)

		elif section_type == 'Galeria de Imagenes':
			gallery_pics = get_pictures_from_section_name(section.get("sectionName"), language)
			group = OrderedDict()
			for pic in gallery_pics:
				if pic.get('title') != 'slider':
					group.setdefault(pic.get('title'), [])
					group[pic.get('title')].append(pic)
					pic_properties = self.getSectionAdvanceProperties(pic, language)
					if pic_properties.get('video'):
						pic['video'] = pic_properties['video']
						if 'youtube' in pic['video']:
							pic['youtube'] = True
			result['gallery_filter'] = group
			result['content_access'] = False

		elif section_type == "Mis Reservas":
			result['my_bookings_section'] = True
			result['my_bookings_properties'] = self.getPicturesProperties(language, section.get("sectionName"))

		elif section_type == u"Localización":
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get("banner_rooms"):
			rooms_section_list = get_sections_from_type('Habitaciones', language)
			if rooms_section_list[0]:
				result['rooms_section_url'] = rooms_section_list[0].get('friendlyUrlInternational')
			result['banner_rooms_pics'] = self.getPicturesProperties(language, advance_properties.get("banner_rooms"))
			for room in result['banner_rooms_pics']:
				if room.get("gallery"):
					room["pictures"] = get_pictures_from_section_name(room.get("gallery"), language)
				if room.get('title'):
					room['filter_url'] = normalizeForClassName(room['title'])

		if advance_properties.get("banner_images"):
			result['banner_image_content'] = get_section_from_section_spanish_name(advance_properties['banner_images'], language)
			result['banner_image_pics'] = self.getPicturesProperties(language, advance_properties['banner_images'])

		if advance_properties.get("banner_accordion"):
			banner_accordion_pics = self.getPicturesProperties(language, advance_properties.get("banner_accordion"))
			banner_accordion_group = OrderedDict()
			for pic in banner_accordion_pics:
				filter_pics = pic.get('filter', '')
				filter_clean = normalizeForClassName(filter_pics)
				banner_accordion_group.setdefault(filter_clean, {'pics': []})
				banner_accordion_group[filter_clean]['filter_name'] = filter_pics
				banner_accordion_group[filter_clean]['pics'].append(pic)

			result['banner_accordion_pics'] = banner_accordion_group

		if advance_properties.get("banner_gallery"):
			result['banner_gallery_pics'] = get_pictures_from_section_name(advance_properties.get("banner_gallery"), language)

		if advance_properties.get("cycle_banner"):
			cycle_banner_pics = self.getPicturesProperties(language, advance_properties.get("cycle_banner"))
			for pic in cycle_banner_pics:
				if pic.get('gallery'):
					pic['gallery'] = get_pictures_from_section_name(pic['gallery'], language)
			result['cycle_banner_pics'] = cycle_banner_pics

		if advance_properties.get("banner_block"):
			result['banner_block_pics'] = self.getPicturesProperties(language, advance_properties.get("banner_block"))

		if advance_properties.get("banner_icons"):
			result['banner_icons_content'] = get_section_from_section_spanish_name(advance_properties.get("banner_icons"), language)
			result['banner_icons_pics'] = get_pictures_from_section_name(advance_properties.get("banner_icons"), language)

		if advance_properties.get("banner_map"):
			result['iframe_map_location'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')
			result['banner_map_content'] = get_section_from_section_spanish_name(advance_properties['banner_map'], language)
			banner_map_pics = self.getPicturesProperties(language, advance_properties['banner_map'])
			result['banner_map_icons'] = []
			for pic in banner_map_pics:
				if pic.get("title") == "location":
					result['banner_map_location'] = pic.get("description", "").strip()
				elif pic.get('title') == "map":
					result['banner_map_image'] = pic
				elif pic.get('title') == "how_to_get_button":
					result['banner_map_button'] = pic.get("description")
					result['banner_map_button_link'] = pic.get("linkUrl")
				else:
					result['banner_map_icons'].append(pic)
				if pic.get("weather_city"):
					result['banner_map_weather_city'] = pic['weather_city']

		if advance_properties.get("banner_testimonials"):
			list_properties = ['country', 'date', 'icon', 'rating']
			result['banner_testimonials_pic_properties'] = self.getPicturesProperties(language, advance_properties[
				'banner_testimonials'], list_properties)
			result['banner_testimonials_section'] = get_section_from_section_spanish_name(
				advance_properties['banner_testimonials'], language)

		if advance_properties.get("banner_rating"):
			result['banner_rating_content'] = get_section_from_section_spanish_name(advance_properties.get("banner_rating"), language)
			result['banner_rating_logos'] = self.getPicturesProperties(language, advance_properties.get("banner_rating"))

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result['minigallery_for_mobile'] = minigallery_images
			mini_dict = {'minigallery': minigallery_images,'num_items': 5,'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		if advance_properties.get("packs_title"):
			result['offers_packs_title'] = advance_properties.get("packs_title")

		if advance_properties.get("form_title"):
			result['contact_form_title'] = advance_properties.get("form_title")

		if advance_properties.get('destination_address'):
			result['destination_address'] = advance_properties.get('destination_address')

		if advance_properties.get('thanks_content'):
			result['thanks_content'] = advance_properties.get('thanks_content')

		if get_config_property_value(PUBLIC_CAPTCHA_KEY):
			result['public_captcha'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

		if advance_properties.get("banner_eventos_reuniones"):
			events_pictures = self.getPicturesProperties(language, advance_properties.get("banner_eventos_reuniones"))
			room_icons_section = "_room_icons"
			room_icons = self.getPicturesProperties(language, room_icons_section)
			if events_pictures:
				result["banner_eventos_reuniones_properties"] = events_pictures
				for pic in events_pictures:
					if pic.get("price"):
						pic["price"] = unescape(pic.get("price"))

					if pic.get('icons'):
						room_icons_keys = pic.get('icons').split(';')
						if room_icons_keys and room_icons:
							pic['room_icons'] = []
							room_icons_filtered = list(filter(lambda x: x.get("title") in room_icons_keys, room_icons))
							for icon in room_icons_filtered:
								if icon.get("icon"):
									icon["icon"] = icon.get("icon").split(";")[0]
									pic['room_icons'].append(icon)



		if advance_properties.get("contact_form"):
			result['contact_form'] = True
			result['contact_form_title'] = get_section_from_section_spanish_name("_contact_form_title", language).get('content', '')
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

		if advance_properties.get("banner_table"):
			result["banner_table_section"] = get_section_from_section_spanish_name(advance_properties['banner_table'], language)

		if advance_properties.get("services_icons_list"):
			result['services_icons_list_section'] = get_section_from_section_spanish_name(advance_properties['services_icons_list'], language)
			result['services_icons_list_properties'] = self.getSectionAdvanceProperties(result['services_icons_list_section'],  language)
			result['services_icons_list_pics'] = get_pictures_from_section_name(advance_properties['services_icons_list'], language)

		return result

	def getRooms(self, language):
		rooms_info = self.getPicturesProperties(language, "_habitaciones_blocks")
		room_icons_config = get_config_property_value(ROOMS_ICONS)
		room_icons_section = "_room_icons"
		if room_icons_config:
			room_icons_section = room_icons_config.split(";")[0]

		romm_icons = self.getPicturesProperties(language, room_icons_section)
		rooms = []

		for room in rooms_info:
			room['images'] = get_pictures_from_section_name(room.get('gallery', ''), language)

			if room.get('title'):
				room['filter_url'] = normalizeForClassName(room['title'])

			room['room_icons'] = []
			room_icons = room.get('icons', '').split(';')
			for x in room_icons:
				for y in romm_icons:
					if x == y.get('title'):
						service_config = y.get('icon').split(";") if y.get('icon') else ''
						if service_config:
							serviceToAppend = {'ico': service_config[0], 'description': y.get('description')}
							if len(service_config) > 1:
								serviceToAppend = {'ico': service_config[0], 'color': service_config[1],
												   'description': y.get('description')}
							room['room_icons'].append(serviceToAppend)
						break

			rooms.append(room)

		return rooms

	def getOffers(self, language):
		all_offers = self.buildPromotionsInfo(language)
		offers = []
		packs = []
		for offer in all_offers:
			if offer.get("priority") and "P" in offer['priority']:
				packs.append(offer)
			else:
				offers.append(offer)

		return offers, packs

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			result['disable_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

				additionalParams['get_subtitle'] = True

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		section_name = section['sectionName']
		language_dict = get_web_dictionary(language)
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if section_type == "Habitaciones":
			section_type = section['sectionType']
			language_dict = get_web_dictionary(language)

			if section_type == "Habitaciones":
				rooms_block = get_section_from_section_spanish_name('_habitaciones_blocks', language)
				rooms_block_pics = self.getPicturesProperties(language, '_habitaciones_blocks')
				for room in rooms_block_pics:
					if room.get('gallery'):
						room['images'] = get_pictures_from_section_name(room['gallery'], language)
				params = {
					'rooms_block': rooms_block,
					'rooms_block_pics': rooms_block_pics,
					'section_content': section,
					'ind_namespace': DEV_NAMESPACE if DEV else get_namespace(),
					'language_code': get_language_code(language),
					'banner_rooms_btn_booking': True
				}
				if advance_properties.get("banner_icons"):
					params['banner_icons_content'] = get_section_from_section_spanish_name(advance_properties.get("banner_icons"), language)
					params['banner_icons_pics'] = get_pictures_from_section_name(advance_properties.get("banner_icons"), language)

				if advance_properties.get("banner_table"):
					params["banner_table_section"] = get_section_from_section_spanish_name(advance_properties['banner_table'], language)

				params.update(language_dict)
				return self.buildTemplate_2("mobile/_rooms_mobile.html", params, False, TEMPLATE_NAME)

		elif section_type == "Ofertas":
			offers_dict = dict(get_web_dictionary(language))
			offers_dict['section_defaults'] = section
			offers_dict['elements'] = self.buildPromotionsInfo(language)
			return self.buildTemplate_2("mobile/_promotions.html", offers_dict, False, TEMPLATE_NAME)

		elif section_type == u"Localización":
			mini_dict = {
				"iframe_google_maps": get_section_from_section_spanish_name("Iframe google maps", language).get('content', ''),
				"captcha_box": get_config_property_value(PUBLIC_CAPTCHA_KEY),
				"section_content": get_section_from_section_spanish_name(section_name, language),
				"language_code": get_language_code(language)
			}
			mini_dict.update(language_dict)
			return self.buildTemplate_2("mobile/_location.html", mini_dict, False, TEMPLATE_NAME)

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		if extra_banners.get("minigallery_for_mobile"):
			args = {
				'minigallery_mobile': extra_banners["minigallery_for_mobile"]
			}
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

		result += "</div>"

		return result

	def getOrderedLanguages(self):
		languageProperty = get_config_property_value(LANGUAGE)

		languages = languageProperty.split('-')
		language_titles = map(get_language_title, languages)
		languageCodes = map(get_language_code, languages)

		dict_languages = OrderedDict(zip(languageCodes, language_titles))

		return dict_languages