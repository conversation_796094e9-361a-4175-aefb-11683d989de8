<div class="buttons_filter_wrapper"><div class="package_filter"><i class="fa fa-dropbox"></i>{{ T_paquetes }}</div><div class="offers_filter active"><i class="icon-guarantee"></i>{{ T_ofertas }}</div></div>

<div class="owlslider owl-carousel promotions_wrapper promotions">
{% for promotion in promotions %}
    <div class="offer">
        <div class="offer_background" style="background-image: url('{{promotion.servingUrl}}');"></div>
        <div class="offer_content">
            <div class="picture"><img src="{{promotion.servingUrl}}"></div>
            <div class="offer_booking">
                <a href="{{promotion.link|safe}}" class="offer_link"><i class="fa fa-plus"></i></a>
                <div class="button-promotion"><i class="fa fa-calendar"></i>{{T_reservar}}</div>
            </div>

            <h3>{{promotion.name|safe}}</h3>
            <div class="desc">{{promotion.description|safe}}</div>
        </div>
    </div>
{% endfor %}
</div>

<div class="owlslider owl-carousel promotions_wrapper packages">
{% for promotion in package_info %}
    <div class="offer">
        <div class="offer_background" style="background-image: url('{{promotion.servingUrl}}');"></div>
        <div class="offer_content">
            <div class="picture"><img src="{{promotion.servingUrl}}"></div>
            <a href="{{promotion.link|safe}}" class="offer_link"><i class="fa fa-plus"></i></a>
            <div class="button-promotion"><i class="fa fa-calendar"></i>{{T_reservar}}</div>
            <h3>{{promotion.title|safe}}</h3>
            <div class="desc">{{promotion.description|safe}}</div>

        </div>
    </div>
{% endfor %}
</div>

<script>
$(window).scroll(function () {
    console.log($(".packages").offset().top - 150 +" > "+ $(window).scrollTop());
    if($(".packages").offset().top - 150 < $(window).scrollTop()){
        if($(".offers_filter").hasClass("active") ){
            $(".offers_filter").removeClass("active");
            $(".package_filter").addClass("active");
        }
    } else {
        if($(".package_filter").hasClass("active") ){
            $(".offers_filter").addClass("active");
            $(".package_filter").removeClass("active");
        }
    }
});
$(window).load(function () {
    /* Main slider*/
    offerowl_params = {
        loop: false,
        nav: true,
        dots: false,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".promotions_wrapper").owlCarousel(offerowl_params);

    $(".offers_filter").click(function () {
        var scrollTo = $(".promotions").offset().top - 115;
        $("html, body").animate({scrollTop:scrollTo}, 1000, 'swing');
    });

    $(".package_filter").click(function () {
        var scrollTo = $(".packages").offset().top - 115;
        $("html, body").animate({scrollTop:scrollTo}, 1000, 'swing');
    });

});
</script>
<style>
    body {
        padding-bottom: 0;
    }
</style>