@import "defaults";
@import "styles_mobile/2/2";

/*
 * promotions template
 */
.buttons_filter_wrapper {
  position: fixed;top: 50%;left:-60px;z-index: 15;-ms-transform: rotate(-90deg);-webkit-transform: rotate(-90deg);transform: rotate(-90deg);
  div {
    display: inline-block;
    position: relative;
    background-color: rgba(0,0,0,0.6);
    vertical-align: top;
    border: 2px solid white;
    padding: 5px 12px 5px 30px;
    font-size: 10px;
    color: rgba(white,0.6);
    text-transform: uppercase;
    i {
      @include center_y;
      left:5px;
      font-size: 200%;
    }
    &.active {
      background-color: $corporate_1;
      color: white;
    }
  }
}

.promotions_wrapper {
    .offer_content {
        text-align: left !important;
    }
}

.mobile_engine.open.has_web_support {
  z-index: 21;
  height: 345px;
  .mobile_engine_action {
    bottom: 345px;
    background: $corporate_1;
  }
  #full_wrapper_booking {
    .wrapper-new-web-support.booking_form_title {
      top: 149%;
      text-align: center;
      display: block;
      a {
        color: white;
      }
      .web_support_label_2 {
        color: white !important;
        .web_support_number {
          color: white !important;
        }
      }
    }
    .wrapper_booking_button {
      .submit_button {
        background: $corporate_1;
      }
    }
  }
}

.owl-carousel .owl-stage-outer {
padding-bottom: 60px;
}