//////////////////////////////////////////////
//GRAL STYLING
//////////////////////////////////////////////

body {
  position: relative;
  font-family: 'nexaregular';
  font-size: 12px;
  background-color: white;
  color: white;

  a {
    color: white;
    text-decoration: none;
  }

}

.boking_widget_inline .wrapper-new-web-support {
  display: block !important;
  font-weight: lighter;
  opacity: 1;
  border-top: 1px solid;
  width: 500px;
  margin: 0 auto;
  margin-top: 10px;
  .web_support_label_1, .web_support_label_2 {
    display: inline-block;
  }
  .web_support_label_2 {
    &:before {
        font-size: 11px;
        vertical-align: text-bottom;
        margin-left: 2px;
    }
  }
}

.title-section-wrapper {
  color: #b3894f;
  text-align: center;
  font-size: 55px;
  font-weight: bolder;
  text-transform: uppercase;
  margin: 50px auto;

  .subtitle1 {
    font-family: nexabold;
    line-height: 59px;
  }

  .subtitle2 {
    font-weight: 100;
  }
}

#main_content p {
  padding: 10px 0px;
}

.description-section-wrapper {
  color: #8e8d8d;
  text-align: center;
  font-size: 17px;
  margin-bottom: 50px;

  p {
    font-weight: lighter;
    line-height: 29px;
  }
}

p.negrita {
  font-size: 18px;
  line-height: 29px;
  font-family: nexabold;
  color: rgb(81, 81, 81);
}

.fancy-title {
  text-align: center;
  font-size: 25px;
  margin-bottom: 10px;
  color: #af8551;
}

.popup_info_es, .popup_info_en {
  display: none;
}

.wrapper-new-web-support {
  line-height: 16px;
}

//////////////////////////////////////////////
//HEADER
//////////////////////////////////////////////

header {
  position: absolute;
  top: 0;
  z-index: 100;
  width: 100%;
  text-align: center;
  margin-top: 30px;

  &:not(.floating) {
    &:before {
      content: '';
      position: absolute;
      top: -30px;
      left: 0;
      width: 100%;
      z-index: -1;
      height: 300px;
      background-image: linear-gradient(to bottom, rgba(black, .5), rgba(#333, .4), rgba(#666, .3), rgba(#999, .2), transparent);
    }
  }

  &.floating {
    background: white;
    position: fixed;
    margin-top: 0;
    padding: 15px 0;
    #logoDiv {
      margin-top: 0;
      width: 120px;
      img {
        display: none;
      }
      img.floating_logo {
        display: block;
      }
    }
    .booking_info, .booking_info a,
    #mainMenuDiv a,
    #lang #selected-language, #lang .arrow {
      color: #333;
    }
    #mainMenuDiv a:hover, #section-active a, .main-section-div-wrapper a:hover {
      border-color: $corporate_1 !important;
    }
    #lang #selected-language, #lang .arrow {
      border-color: #333;
    }

    #lang-wrapper {
      display: inline-block;
      width: auto;
      padding-left: 0;
      float: right;
      margin-top: 10px;
    }

    #main_menu {
      padding-bottom: 20px;
      padding-left: 0;
      display: inline-block;
      width: 950px;
      margin-left: 0;
    }
    #mainMenuDiv #main-sections {
      display: inline-block;
      vertical-align: top;
    }
    #mainMenuDiv a {
      margin-top: -20px;
    }
    #main_menu .button-promotion {
      display: inline-block;
      vertical-align: top;
      padding: 15px 25px !important;
      margin-left: 10px;
      background: $corporate_1;
      font-family: nexabold;
      color: white !important;
      &:hover {
        background: $corporate_2;
      }
    }

    .booking_info {
      display: none;
    }
  }
  #main_menu {
    text-align: right;
  }
  #main_menu .button-promotion {
    display: none;
  }
  #logoDiv {
    margin-top: -15px;
    img.floating_logo {
      display: none;
    }
  }
  #wrapper-header {
    width: 1300px;
  }
}


#top-sections {
  text-align: left;
  font-family: nexaregular, sans-serif;
  font-size: 13px;
}

#lang-wrapper {
  text-align: right;
}

//LANGUAGE SELECT
.booking_info {
  display: inline-block;
  vertical-align: middle;
  padding: 0 10px;
}

#lang {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  position: relative;

  #selected-language {
    padding: 10px 40px 10px 10px;
    box-sizing: border-box;
    height: 35px;
    display: inline-block;
    color: white;
    font-size: 15px;
    font-family: nexabold;
    font-weight: 300;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 2px solid white;
  }

  .arrow {
    @include center_y;
    right: 0;
    display: inline-block;
    width: 35px;
    height: 20px;
    border-left: 1px solid white;
    @extend .fa-angle-down;
    &:before {
      @extend .fa;
      @include center_xy;
      font-size: 25px;
    }
  }

  ul li {
    background: #ffffff;
    text-align: center;
    width: 80px;
    font-size: 16px;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}

#language-selector-options {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1001;

  a {
    color: $gray-2;

    &:hover {
      color: $corporate-1;
    }
  }
}

//WEATHER
.weather {
  margin-right: 10px;

  width: 90px;
  float: right;

  .grados {
    float: right;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 17px 10px 17px 3px;
    box-sizing: border-box;
    width: 45px;
    height: 45px;
    display: inline-block;
    color: white;
    font-size: 15px;
    font-family: nexabold;
    letter-spacing: 1px;
  }
  .img_weather {
    float: right;
    background-color: rgba(255, 255, 255, 1);
    width: 45px;
    height: 45px;
    padding-top: 4px;
    box-sizing: border-box;

  }

}

//////////////////////////////////////////////
//HORIZONTAL BOOKING ENGINE
//////////////////////////////////////////////

#horizontal_booking_container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(0, 0, 0, 0.6);
  padding-bottom: 70px;
}

/* A few more changes in the new booking engine */

#booking-horizontal {

  .boking_widget_inline .room_list_wrapper {

    margin-left: 0px !important;
    margin-right: 0px !important;

  }

  .date_box .date_year, .date_box .date_day {
    color: white !important;

  }

  .date_box {
    background-color: transparent;
    border-radius: 0px;
    border: 2px solid white;
  }

  .boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label {
    color: white !important;
    font-size: 12px !important;
  }

  .selectric {
    background-color: transparent;
    color: white;
    border-radius: 0px;
    border: 2px solid white;

    .label {
      color: white !important;
    }

    .button {
      border-left: 1px solid white;
      border-radius: 0px;
      background-color: transparent !important;
    }

  }

  .boking_widget_inline .wrapper_booking_button {

    margin-top: 1px;
    float: left;
    margin-left: 20px;
  }

  .wrapper_booking_button button {
    cursor: pointer;
    background: $corporate_1 !important;
    border-radius: 0px;
    border: 2px solid white;

  }

  .wrapper_booking_button button:hover {

    background: $corporate_2 !important;

  }

  .wrapper_booking_button .promocode_input {
    background-color: transparent !important;
    width: 175px !important;
    border-radius: 0px;
    border: 2px solid white;
    color: white !important;

  }

  .promocode_input::-webkit-input-placeholder {
    color: white !important;
  }
  .promocode_input::-moz-placeholder {
    color: white !important;
  }
  .promocode_input:-moz-placeholder {
    color: white !important;
  }
  .promocode_input:-ms-input-placeholder {
    color: white !important;
  }

  //A few more changes for the booking engine HORIZONTAL

  .boking_widget_inline {
    background-color: transparent !important;
  }

  .booking_form_title {
    background-color: transparent !important;
  }

  .booking_form {
    background-color: transparent !important;
  }

  .boking_widget_inline .room .room_title, .boking_widget_inline .room .adults_selector label, .boking_widget_inline .room .children_selector label {
    color: white !important;
    font-size: 12px;
  }

  .boking_widget_inline .stay_selection {
    //with type room selector must be: margin-left 0px
    //margin-left: 0px !important;
    margin-left: 111px !important;
    margin-top: 5px;
  }

}

.destination_wrapper {
  margin-top: 5px;
  margin-right: -9px;
  margin-left: 22px;

  .right_arrow {
    cursor: pointer;
  }

  div#placeholder {
    color: white;
  }

  input {
    color: white;
  }

  input::-webkit-input-placeholder {
    color: white;
  }
  input:-moz-placeholder {
    color: white;
  }
  input::-moz-placeholder {
    color: white;
  }
  input:-ms-input-placeholder {
    color: white;
  }

}

.destination_wrapper input {
  height: 38px;
  width: 175px;
  padding-left: 10px;
  background: transparent;
  margin-right: 20px;
  border: 2px solid white;
  border-radius: 0px;
  cursor: pointer;

  div#placeholder {
    color: white !important;
  }
}

.destination_wrapper label {
  color: white;
  margin-right: 15px;
}

.roomtype_selector {
  left: 168px;
  top: 71px;
}

.roomtype_selector .title_selector {
  background: rgba(0, 0, 0, 0.53);
  padding-right: 27px !important;
  padding-left: 29px;
}

.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: -75px;
  bottom: 24px;
}

//booking engine in fancybox (for selects!)

.fancybox-inner {
  overflow: visible !important;

  .destination_wrapper {
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    input::-webkit-input-placeholder {
      color: rgb(175, 133, 83);
    }
    input:-moz-placeholder {
      color: rgb(175, 133, 83);
    }
    input::-moz-placeholder {
      color: rgb(175, 133, 83);
    }
    input:-ms-input-placeholder {
      color: rgb(175, 133, 83);
    }

    label {
      color: rgb(131, 131, 131);
    }

    input {
      width: 94% !important;
      background: white;
      color: rgb(175, 133, 83);

    }

    .right_arrow {
      background: rgb(175, 134, 82) url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center !important;
      margin-right: -22px;
    }

  }

  .promocode_input {
    width: 120px;
    font-size: 12px;
  }
  .wrapper_booking_button button {
    font-size: 13px;
    width: 125px;
  }

  .booking_form {
    background: #ededed !important;
  }

  .selectric {
    border-radius: 0px !important;
  }

  .date_box {
    border-radius: 0px !important;
    .date_year {
      color: rgb(176, 135, 87);
    }
  }

  .wrapper_booking_button .promocode_input {
    border-radius: 0px !important;
  }

  .wrapper_booking_button button {
    border-radius: 0px !important;
  }

  .button {
    border-radius: 0px !important;
  }
}

//////////////////////////////

/** CALENDAR DATEPICKER**/

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .header_datepicker {
        background: $corporate_3;
    }
}

.date_box .date_year {
  color: white;
}

#wrapper_booking_fancybox {
  .date_box .date_year {
    color: grey;
  }
  .date_day {
    margin-top: 4px;
  }
}

//////////////////////////////////////////////
//Slider revolution new arrows
//////////////////////////////////////////////
.tp-leftarrow.default {
  background: url(/img/#{$base_web}/left_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}

.tp-rightarrow.default {
  background: url(/img/#{$base_web}/right_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}


.tp-bullets {
  display: none;
}

#slider_container {
  position: relative;

  .caption {
    text-shadow: rgba(80, 80, 80, 0.4) 1px 0px 10px;
  }
}

.slider_inner {
  height: 600px !important;
}

.forcefullwidth_wrapper_tp_banner {
  min-height: 650px !important;
}

.tp-banner-container {
  min-height: 650px;
}

#botton-slider {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 30;
  position: absolute;
  bottom: 0px;

  display: block;
  width: 205px;
  height: 45px;
  box-sizing: border-box;
  left: 50%;
  text-align: center;
  padding-top: 20px;
  margin-left: -103px;

  img {

  }
}

.cartela_home {
  position: relative;
  margin-top: 70px;

  .slider-text-1, .slider-text-2, .slider-text-3, .slider-text-4 {
    position: absolute;
    width: 100%;
  }

  .slider-text-1 {
    top: 29px;
  }
  .slider-text-2 {
    top: 90px;
  }
  .slider-text-3 {
    top: 177px;
    left: 358px;
    width: 19%;
  }
  .slider-text-4 {
    top: 177px;
    right: 355px;
    width: 19%;
  }
}

//////////////////////////////////////////////
/* MAIN DISTRIBUTED MENU */
//////////////////////////////////////////////

#mainMenuDiv {
  font-family: nexaregular, sans-serif;
  font-size: 13px;
  z-index: 99;
  position: relative;
  top: 27px;
  clear: both;
}

#mainMenuDiv a {
  padding: 2px 5px 2px;
  text-decoration: none;
  text-transform: uppercase;
  color: $white;
  display: inline-block;
  font-size: 12px;
  border-top: 2px solid transparent !important;
}

#mainMenuDiv a:hover, #section-active a, .main-section-div-wrapper a:hover {
  border-top: 2px solid white !important;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  @include center_x;
  top: 100%;
  background-color: rgba($corporate_2, .8);
  text-align: center;
  padding: 10px;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
  white-space: nowrap;
}

#main-sections-inner {
  text-align: justify;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  position: relative;
  display: inline-block
}

.main-section-div-wrapper a {
  line-height: 38px;
  text-transform: uppercase;

}

/////////////////////////////////////
/* BIG BANNERS LINES */
/////////////////////////////////////
#wrapper_main_content {
  display: block;
  overflow: visible;
}

#wrapper-main-banners {
  margin-bottom: 10px;
  overflow: auto;
}

.wrapper-big-banner {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%;

}

.wrapper-big-banner:before {
  content: '';
  display: block;
  padding-top: 100%; /* initial ratio of 1:1*/
}

.big-banner-title {
  margin-top: 10px;
  line-height: 60px;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 40px;
  text-transform: uppercase;
}

.square-icono h4.big-banner-title {
  font-weight: 100;
}

.big-banner-moreinfo {
  color: white;
  text-decoration: none;
  font-size: 19px;
  margin-top: 15px;
  display: block;
}

.span-underline {
  width: 60px;
  border-bottom: 3px solid rgba(255, 255, 255, 0.50);;
  text-align: center;
  display: block;
  margin: 0 auto;
}

.banner-special-wrapper {
  margin-bottom: 10px;
  overflow: auto;
}

img.icono-bigbanner {
  margin-bottom: 20px;
}

#triangulo-left {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-right: 60px solid rgb(175, 134, 82);
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  margin-left: -9.0%;
  z-index: 4;
}

#triangulo-right {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-left: 60px solid black;
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  right: -9.0%;
  z-index: 4;
}

.banner-link {
  z-index: 1 !important;
}

.wrapper-big-banner.square-background {
  z-index: 3;
}

.container-text-banner {
  position: absolute;
  top: 39%;
  bottom: 0px;
  vertical-align: middle;
  left: 0px;
  right: 0px;
  text-align: center;
}

.square-icono .container-text-banner {
  padding: 0px 40px;
  top: 20% !important;
}

div#wrapper-main-bannersX2 {
  margin-bottom: 10px;
  overflow: auto;

  .wrapper-bannerX2 {
    width: 50%;
    float: left;
    position: relative;
    overflow: hidden;

    &:before {
      content: "";
      display: block;
      padding-top: 50%;
    }
  }

  .banner-text-inside {
    position: absolute;
    top: 15%;
    vertical-align: middle;
    right: 63px;
    text-align: center;
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.59);
    width: 37%;
    height: 63%;
    padding: 0px 15px;

    .big-banner-title {
      font-size: 40px;
    }

    .big-banner-description {
      margin-top: 8px;
      font-size: 33px;
      font-weight: 100;
      line-height: 40px;
    }
  }
}

/////////////////////////////////////
/******************FOOTER***************/
/////////////////////////////////////
footer {
  background-color: #323336;
  clear: both;
}

.wrapper_footer_columns {
  margin: 0px auto;
  padding-top: 20px;
}

.footer_column {
  font-family: nexaregular, sans-serif;
  font-size: 13px;
  line-height: 23px;
  color: white;
  padding: 0 20px;
  height: 225px;
  border-left: 1px solid white;
  text-align: center;
  box-sizing: border-box;
  &:first-of-type {
    border-left-width: 0;
  }
  #newsletter {
    text-align: left;
  }
}

footer #social {
  text-align: center;
  clear: both;
  padding: 30px 0 0;
  a {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    background: $corporate_1;
    border-radius: 5px;
    i {
      @include center_xy;
      color: white;
      font-size: 18px;
    }
    &:hover {
      background: $corporate_2;
    }
  }
}

.footer_column a {
  font-size: 14px;
  line-height: 23px;
  color: white;
  text-decoration: none;
  font-weight: 100;
}

#footer_column_description {
  margin-bottom: 10px;
}

.footer_column a:hover {
  color: darken($gray-4, 10%);
}

.footer_column_title {
  font-weight: 500;
  color: white;
  font-size: 18px;
  margin-bottom: 5px;
}

.copyright-footer {
  line-height: 46px;
}

#newsletter h2 {
  font-weight: 500;
  color: white;
  font-size: 18px;
}

label#suscEmailLabel {
  font-size: 14px;
  font-weight: 100;
}

#newsletter input {
  width: 98%;
  background-color: white;
  border: none;
  height: 24px;
  color: $corporate-1;
  border-radius: 3px;
  font-size: 14px;
  margin: 10px 0;
}

#newsletter button {
  width: 90px;
  height: 25px;
  text-transform: uppercase;
  background: rgb(175, 133, 81);
  outline: none;
  border: none;
  color: white;
  font-size: 14px;
}

#newsletter button:hover {
  background-color: rgb(148, 108, 74);
  cursor: pointer;
}

input#promotions, input#privacy {
  width: auto;
  height: auto;
}

.newsletter_checkbox {
  font-size: 12px;

  a {
    text-decoration: underline !important;
    font-size: 12px;
  }
}

#newsletter label.error {
  margin-left: 100px;
}

label.error {
  position: absolute;
  margin-bottom: 8px;
  font-size: 16px;
  margin-top: 3px;
}

#form_events .styled-select label.error {
  background: white !important;
}

#social {
  margin-top: 20px;
}

#social span {
  position: relative;
  top: -44px;
  font-weight: 100;
  right: 350px;
}

#social img {
  margin-left: 6px;
  padding-bottom: 2px;
  height: 28px;
  width: 28px;

  &:hover {
    opacity: 0.5;
  }
}

.footer_column h3 {
  font-size: 19px;
  color: white;
}

#footer {
  color: white;
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  padding-bottom: 20px;

  p {
    text-align: center;
  }
}

#footer a {
  text-decoration: none;
  color: white;
}

#footer_bottom_text {
  font-size: 14px;
  line-height: 14px;
}

.copyright-footer img {
  margin: 0 5px;
}

#google_plus_one {
  text-align: center;
}

#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

#facebook_like {
  text-align: center;
  margin-top: 10px;
}

#facebook_like iframe {
  height: 21px;
  width: 103px;
}

//////////////
//Events
//////////////
.know-more-text {
  line-height: 27px;
  font-size: 17px;
}

.event_wrapper {
  color: black;
  margin-bottom: 40px;
  margin-top: 10px;
  padding-bottom: 40px;
  border-bottom: 1px solid rgb(237, 237, 237);

  .event_img {
    width: 281px !important;
    margin: 0px 5px;
    .event_img img {
      width: 281px;
      height: 281px;
    }
  }

  .event_date {
    width: 120px;
    margin: 0px;

    .event_day, .event_month, .event_year {
      background: #7b7b7b;
      margin-bottom: 5px;
      text-align: center;
      color: white;
      padding: 13px 0px;
      font-size: 22px;
    }
  }

  .event_main_info {
    width: 678px;
    background: #f3f3f3;
    height: 281px;
    padding: 30px;
    box-sizing: border-box;
    position: relative;
    margin: 0.5px 5px;

    .event-title {
      text-transform: uppercase;
      color: rgb(175, 133, 83);
      font-size: 19px;
      margin-bottom: 15px;
      font-weight: bolder;
    }

    .event-description {
      line-height: 28px;
      font-size: 16px;
      font-weight: lighter;
      color: gray;
    }

    .event-buttons-wrappers {
      position: absolute;
      bottom: 40px;

      a {
        background: #af8553;
        padding: 19px 46px;
        font-size: 17px;

        &:hover {
          opacity: 0.7;
        }
      }
    }

  }

}

//////////////////////////
////Galeria
//////////////////////////

.filter-offers {
  font-size: 40px;
  text-align: center;

  .active {
    background: #8c6b40;
  }

  .filter-hotel {
    margin-right: 0.5%;
    float: left;
  }

  .filter-apartamentos {
    float: left;
  }

  li {
    cursor: pointer;
    background: #b48952;
    width: 49.75%;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 24px;
    padding: 16px 0px;
    font-weight: bold;
    &:hover {
      background: #8c6b40;
    }
  }

}

.gallery-images {
  margin-bottom: 60px;
}

ul.gallery_1 li {
  padding: 2px 1px !important;
  width: 25% !important;
  height: 190px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  cursor: pointer;
}

//////////////////////////////
////Mis Reservas
/////////////////////////////

.my-booking-text {
  color: black;
  text-align: center;
  line-height: 29px;
  font-size: 16px;
}

form#my-bookings-form {
  width: 100%;
  margin: 50px auto;
  text-align: center;

  label {
    color: black;
    text-align: left;
    font-size: 16px;
  }

  input {
    text-align: center;
    margin-bottom: 15px;
    margin-top: 5px;
    font-size: 16px;
    display: initial;
    width: 100%;
    border: 0px !important;
    height: 30px;
    background-color: #e1e1e1;
    color: dimgrey;
  }

  button#my-bookings-form-search-button, button#cancelButton {
    cursor: pointer;
    background: #af8553;
    border-radius: 0px !important;
    border: 2px solid white;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    color: white;
    font-size: 16px;
    text-transform: uppercase;
    height: 40px;
    width: 110px;
    padding: 9px 14px 10px;

    &:hover {
      background: #7c5a3e;
    }
  }

  button#cancelButton {
    width: 215px;
    display: none;
  }
}

.fResumenReserva {
  border: 3px solid #AF8553 !important;
  background: rgba(175, 133, 83, 0.59) !important;
  padding: 4px 10px 20px 10px !important;

}

form#my-bookings-form button#cancelButton {
  width: 213px !important;
  margin: 22px auto !important;
}

.alpha {
  margin-left: -104px !important;

  .txtCosteTotal {
    color: rgb(108, 86, 60) !important;
  }
}

////*Gallery Mosaic*///////

.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 137%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;

  img {
    width: 100%;
    height: 97%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 0px auto 70px;
}

///////////////////////////
//Media Querys
///////////////////////////

@media (max-width: 1760px) {
  .big-banner-title {
    font-size: 30px;
  }

  .banner-text-inside {

    .big-banner-title {
      font-size: 27px !important;
      line-height: 42px;
    }

    .big-banner-description {
      font-size: 22px !important;
    }
  }
}

@media (max-width: 1480px) {
  div#wrapper-main-bannersX2 .banner-text-inside .big-banner-description {
    line-height: 25px;
  }
  .banner-text-inside {
  }
}

@media(max-width: 1360px) {
  .banner-text-inside {
  }
}

@media (max-width: 1370px) {
  .big-banner-title {
    font-size: 20px;
    line-height: 40px;
  }

  .banner-text-inside {
  }

  div#wrapper-main-bannersX2 .banner-text-inside .big-banner-title {
  }
}

@media (max-width: 1233px) {
  .banner-text-inside .big-banner-title {
    line-height: 30px;
  }
}

@media (min-width: 1920px) {
  #triangulo-left {
    margin-left: -6%;
  }

  #triangulo-right {
    margin-right: 2%;
  }
}

//Special Changes

#bannerx2-opac {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}

#bannerx2-opac:hover {
  opacity: 0.5;
}

.arrow-wrapper {
  position: relative;
  overflow: visible;
  float: left;
  width: 25%;
}

.arrow-wrapper:before {
  content: "";
  display: block;
}

.wrapper-big-banner {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
  overflow: hidden;
}

.zoom {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.wrapper-for-hide {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%; /* desired width */
  overflow-y: hidden;
  overflow-x: hidden;
}

.wrapper-for-hide:before {
  content: "";
  display: block;
}

.hidden-menu {
  z-index: 999;
  display: none;
  background: white;
  position: fixed;
  width: 100%;
  min-width: 1140px;
  top: 0px;
  height: 90px;
  box-shadow: 0px 1px 7px black;

  #logoDiv {
    width: 100px;
    margin-top: -6px;
    img {
      margin-top: 20px;
      margin-left: -30px;
    }
  }

  #main-sections {
    margin-top: 20px;

    #main-sections-inner {
      width: 1000px;
    }

    .main-section-div-wrapper a {
      color: rgb(175, 133, 81);
      padding: 5px 8px 7px;
      text-decoration: none;
      text-transform: uppercase;
      display: inline-block;
      border-top: 2px solid white !important;
      &:hover {
        border-top: 2px solid rgb(175, 133, 81) !important;

      }
    }

    #section-active a {
      color: rgb(175, 133, 81);
      border-top: 2px solid rgb(175, 133, 81) !important;
    }
  }

  #lang {
    margin-top: 20px;
    margin-left: 20px;

    .arrow {
      display: inline-block;
      background: rgba(175, 133, 83, 0.62) url(/img/park2/flecha_dorada_lang.png) no-repeat center center !important;
      float: right;
      width: 45px;
      height: 45.4px;
      margin-top: 0px;
    }

    #selected-language {
      background: rgba(0, 0, 0, 0.2);
    }

    ul li {
      background: #ffffff;
      text-align: center;
      width: 80px;
      font-size: 16px;
      padding: 5px;
      cursor: pointer;
      display: block;
      border-bottom: 1px solid #EEE;
      color: #666;
      border-top: 1px solid #FFF;

      &:hover {
        border-bottom: 1px solid rgba(128, 128, 128, 0.33);
        background: #f0f0f0;
        width: 80px;
      }

      a {
        color: #666 !important;
        text-decoration: none !important;
      }
    }
  }
  .booking_top_button {
    cursor: pointer;
    background: #af8553 !important;
    border-radius: 0px !important;
    border: 2px solid white;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    color: white;
    float: right;
    font-size: 16px;
    text-transform: uppercase;
    height: 21px;
    width: 110px;
    margin-top: 18px;
    padding: 15px 14px 10px;
    margin-left: 20px;

    &:hover {
      background: rgb(126, 96, 59) !important;
    }
  }

}

.vertical-align {
  vertical-align: middle;
  display: table-cell;
}

.vertical-align-wrapper {
  height: 100%;
  width: 100%;
  display: table;
}

/******************** tiny carousel ********************/

.carousel {
  overflow: hidden;
  clear: both;
  text-align: center;
  height: 100%;
  margin: 0 auto;
  width: 1140px;
}

.carousel #carousel_title {
  color: $corporate_2;
  padding: 5px;
  margin-bottom: 10px;
  font-size: 35px;
  margin-top: 30px;
}

.carousel .viewport {
  width: 1080px;
  height: 175px;
  overflow: hidden;
  position: relative;
  float: left;
  margin-left: -2px;
}

.carousel .disable {
  visibility: hidden;
}

.carousel .overview {
  list-style: none;
  position: absolute;
  left: 0;
  top: 0;
  padding: 0;
  margin: 0;
}

.carousel .overview li {
  float: left;
  margin: 0 1px;
  height: 100%;
  text-align: center;
  font-size: 12px;
  width: 214px;
  position: relative;

  &:hover img {
    opacity: 0.6;
    filter: alpha(opacity=60);
  }
}

.carousel .overview li img {
  height: 175px;
  width: 358px;
  margin-bottom: -6px;
}

.carousel .overview li img:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.carousel .buttons {
  float: left;
  text-indent: -999em;
  width: 29px;
  height: 175px;
  overflow: hidden;
  position: relative;
  margin: 0;
}

.carousel .prev {
  background: $corporate_1 url("/img/park2/flecha_izquierda.png") no-repeat center;
  margin-right: 3px !important;
  margin-left: 0px;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;

  &:hover {
    background: $corporate_2 url("/img/park2/flecha_izquierda.png") no-repeat center;
  }
}

.carousel .next {
  background: $corporate_1 url("/img/park2/flecha_derecha.png") no-repeat center;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;

  &:hover {
    background: $corporate_2 url("/img/park2/flecha_derecha.png") no-repeat center;
  }
}

.carousel .disable {
  visibility: visible;
}

.bannerTitle {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.65);
  text-align: center;
  padding: 15px 15px;
  font-size: 14px;
  height: auto;

}

.gallery-carousel {
  margin-bottom: 50px;
}

$corporate_1: #bc9362;
$corporate_2: #2f6160;

.cycle_blocks_home_wrapper {
  .cycle_block_home {
    position: relative;
    overflow: hidden;
    img {
      @include center-image;
      width: auto;
    }
    .cycle_blocks_home_description {
      position: relative;
      z-index: 2;
      width: 50%;
      height: 395px;
      background-color: rgba($corporate_1, .9);
      .cycle_blocks_home_text {
        border-top: 5px solid $corporate_2;
        border-bottom: 5px solid $corporate_2;
        padding: 15px 5px;
        margin-top: -30px;
        width: 90%;
        text-align: center;
        @include center_xy;
        h3 {
          font-size: 50px;
          font-family: nexabold;
        }
        p {
          font-size: 20px;
          padding: 10px 70px !important;
        }
        .links {
          position: absolute;
          right: 0;
          left: 0;
          bottom: -32px;
          a {
            padding: 7px 20px;
            display: inline-block;
            vertical-align: middle;
            background: $corporate_2;
            color: white;
            border: 3px solid $corporate_2;
            text-transform: uppercase;
            font-weight: bold;
            font-family: nexabold;
            &:hover {
              background: $corporate_1;
              color: white;
            }
            &.button-promotion {
              background: white;
              color: $corporate_2;
              &:hover {
                background: $corporate_1;
                color: white;
              }
            }
          }
        }
      }
      &:before {
        content: '';
        @include center_y;
        z-index: 5;
        right: -60px;
        border-style: solid;
        border-width: 30px;
        border-color: transparent transparent transparent rgba($corporate_1, .9);
      }
    }
    &:nth-child(2) .cycle_blocks_home_description {
      float: right;
      &:before {
        right: auto;
        left: -60px;
        border-color: transparent rgba($corporate_1, .9) transparent transparent;
      }
    }
  }
}

.carousel_offers_wrapper {
  .offer {
    position: relative;
    overflow: hidden;
    .offer_pic {
      @include full_size;
      img {
        @include center-image;
        width: auto;
      }
    }
    .offer_content {
      position: relative;
      z-index: 2;
      width: 50%;
      float: right;
      height: 395px;
      background-color: rgba($corporate_2, .9);
      .desc {
        border-top: 5px solid $corporate_1;
        border-bottom: 5px solid $corporate_1;
        padding: 15px 5px;
        margin-top: -30px;
        width: 90%;
        text-align: center;
        @include center_xy;
        h3 {
          font-size: 50px;
          font-family: nexabold;
        }
        p {
          font-size: 20px;
          padding: 10px 70px !important;
        }
        .links {
          position: absolute;
          right: 0;
          left: 0;
          bottom: -32px;
          a {
            padding: 7px 20px;
            display: inline-block;
            vertical-align: middle;
            background: $corporate_1;
            border: 3px solid $corporate_1;
            color: white;
            text-transform: uppercase;
            font-weight: bold;
            font-family: nexabold;
            &:hover {
              background: $corporate_2;
              color: white;
            }
            &.button-promotion {
              background-color: white;
              color: $corporate_1;
              &:hover {
                background: $corporate_2;
                color: white;
              }
            }
          }
        }
      }
      &:before {
        content: '';
        @include center_y;
        z-index: 5;
        left: -60px;
        border-style: solid;
        border-width: 30px;
        border-color: transparent rgba($corporate_2, .9) transparent transparent;
      }
    }
  }
  .owl-nav {
    @include center_y;
    left: 0;
    right: 0;
    .owl-prev,
    .owl-next {
      position: relative;
      font-size: 30px;
      float: left;
      width: 35px;
      height: 40px;
      background: rgba($corporate_1, .8);
      i.fa {
        @include center_xy;
      }
    }
    .owl-next {
      float: right;
    }
  }
}

.banner_services_wrapper {
  position: relative;
  height: 320px;
  margin-top: -50px;
  .banner_services_content {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    background: $corporate_1;
    height: 320px;
    width: 50%;
    .desc {
      @include center_y;
      right: 0;
      width: 570px;
      h3 {
        color: white;
        text-align: center;
        font-size: 25px;
        &:before,
        &:after {
          content: '';
          display: block;
          margin: 15px auto 20px;
          width: 50px;
          height: 3px;
          background: #2f6160;
        }
      }
      p {
        text-align: center;
        color: white;
        font-size: 18px;
        padding: 30px 120px !important;
        strong {
          font-family: nexabold;
        }
      }
    }
    &:before {
      content: '';
      @include center_y;
      z-index: 5;
      right: -60px;
      border-style: solid;
      border-width: 30px;
      border-color: transparent transparent transparent $corporate_1;
    }
  }
  .banner_services {
    display: inline-block;
    vertical-align: middle;
    width: 570px;
    text-align: center;
    .service {
      display: inline-block;
      width: 95px;
      height: 95px;
      position: relative;
      &:hover {
        .ico {
          margin-top: 100%;
          bottom: auto;
        }
      }
      .ico {
        @include full_size;
        z-index: 2;
        height: 100%;
        background: $corporate_2;
        @include transition(all, .6s);
        i.fa {
          @include center_xy;
          font-size: 50px;
          color: white;
        }
      }
      .title {
        @include full_size;
        color: #333;
        .back_color {
          @include full_size;
          background: rgba($corporate_2, .6);
          opacity: .3;
        }
        span {
          display: block;
          width: 100%;
          @include center_xy;
          font-size: 12px;
          line-height: 18px;
          font-family: nexabold;
          text-transform: uppercase;
        }
      }
    }
  }
}


#my-bookings-form {
    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
      }

      input, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 300px;
        margin: 10px auto;
        height: 40px;
        border-radius: 0;
        text-align: center;
        font-size: 14px;
        border: 1px solid #DDD;
      }

      select {
        padding: 0 0 0 15px;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            height: 40px;
            text-transform: uppercase;
            //font-family: $title_family;
            font-size: 16px;
            color: white;
            border: 0;
            cursor: pointer;
            width: 100%;
            font-weight: 100;
            @include transition(background, .4s);

            &.modify-reservation {
              background: darken($corporate_3, 10%);

              &:hover {
                background: darken($corporate_3, 20%);
              }
            }

            &.searchForReservation {
              background: $corporate_4;

              &:hover {
                background: darken($corporate_4, 10%);
              }
            }
          }
        }
      }
    }

    #cancelButton {
      display: none;
      background: $corporate_3;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 40px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_3, 10%);
      }
    }
    .my-bookings-booking-info {
        margin: 0 auto !important;
    }
  }

.radio_wrapper_cancellation {
    .label_cancellation {
        color: black;
    }
}

.banner_icons_wrapper {
  padding: 0 calc((100% - 1140px) / 2) 100px;
  .banner_icons {
    text-align: center;
    .icon {
      display: inline-block;
      vertical-align: top;
      box-sizing: border-box;
      padding: 0 30px;
      width: calc(100% / 7);
      i.fa {
        display: block;
        margin: auto;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 1px solid #636363;
        color: #636363;
        position: relative;
        font-size: 50px;
        margin-bottom: 20px;
        &:before {
          @include center_xy;
        }
      }
      span {
        color: #636363;
        font-size: 14px;
      }
    }
  }
}

.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  color: #bdbdbd;
}

.popup_cancel_modify_booking_wrapper {
  color: black;
}