.offer-wrapper {
  height: 380px;
  width: 375px !important;
  position: relative;
  margin-bottom: 50px;
  margin-left: 2.5px;
  margin-right: 2.5px;

  img.offer-main-image {
    height: 50%;
    width: 100%;
  }

  .offer-button-wrapper {
    width: 100%;
    position: absolute;
    text-align: center;
    margin-top: -32px;

    a.button-promotion {
      cursor: pointer;
      background: #af8553 !important;
      border-radius: 0px !important;
      -moz-border-radius: 5px;
      -webkit-border-radius: 5px;
      color: white;
      text-transform: uppercase;
      padding: 21px 39px 21px;
      font-size: 17px;

      &:hover {
        background-color: rgb(148, 108, 74) !important;
      }
    }

    a img {
      vertical-align: middle;
      height: 60px;
      width: 60px;
      padding-bottom: 4px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
  .offer-text-wrapper {
    height: 50%;
    padding: 42px 30px;
    background: #ededed;
    box-sizing: border-box;
    font-size: 17px;
    text-align: center;
    color: #787878;

    p.titulo-oferta {
      color: rgb(175, 133, 83);
      text-transform: uppercase;
      font-family: nexabold;
    }

    p.entradilla-oferta {
      line-height: 26px;
    }
  }
}

.filter-promotions {
  font-size: 40px;
  text-align: center;

  li {
    cursor: pointer;
    background: #b48952;
    width: 49.75%;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 24px;
    padding: 16px 0px;

    &:hover {
      background: #8c6b40;
    }
  }

  .active {
    background: #8c6b40;
  }

  .filter-promos {
    margin-right: 0.5%;
    float: left;
  }

  .filter-packs {
    float: left;
  }
}

.offer-popup {
  h4 {
    text-align: center;
    font-size: 25px;
    margin-bottom: 10px;
    color: #af8551;
  }
  p {
    line-height: 27px;
    font-size: 17px;
  }
}