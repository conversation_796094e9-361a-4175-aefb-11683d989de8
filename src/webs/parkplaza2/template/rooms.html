<div class="rooms container12">
    {% for room in rooms %}
        <div class="room-block column4 {% cycle 'block-1' 'block-2' 'block-3' %}">
            <div class="wrapper-transparent-container">
                <div class="wrapper-transparent">
                    <div class="transparent-container-room">
                        <img class="ico-type" src="/img/{{ base_web }}/{{ tipo_ico }}">
                        <p class="separator"></p>
                        <h3>{{ room.title|safe }}</h3>
                        <p class="separator"></p>
                        <div class="icons-room">
                            <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.name|safe }}'}, {% endfor %} ]);"><img
                                    src="/img/{{ base_web }}/ico_FOTOS{% if 'b51' in tipo_ico %}2{% endif %}.png"></a>
                            <a href="#room-popup-{{ forloop.counter }}" class="myFancyPopupAuto"
                               onClick="flexsliderStart{{ forloop.counter }}();"><img
                                    src="/img/{{ base_web }}/ico_MAS_peq{% if 'b51' in tipo_ico %}2{% endif %}.png"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gallery-img">
                <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.name|safe }}'}, {% endfor %} ]);">
                    <img src="{{ room.pictures.0.servingUrl }}"/>
                </a>
            </div>
            <div class="buttons">
                <div class="section-box">
                    <a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                       class="button-promotion">{{ T_reservar }}</a>
                </div>
            </div>
        </div>
        <div id="room-popup-{{ forloop.counter }}" class="popup-rooms" style="display:none">
            <div class="imagen" id="imagen-gallery-{{ forloop.counter }}">
                <ul class="slides">
                    {% for picture in room.pictures %}
                        <li><img src="{{ picture.servingUrl }}=s1000"/></li>
                    {% endfor %}
                </ul>
            </div>
            <script type="text/javascript">
                function flexsliderStart{{forloop.counter}}() {

                    $('#imagen-gallery-{{forloop.counter}}').flexslider({
                        itemWidth: 650,
                        controlNav: false,
                        directionNav: true,
                        animation: "slide",
                        prevText: "<img src='/img/{{ base_web }}/flecha_izq.png'>",
                        nextText: "<img src='/img/{{ base_web }}/flecha_der.png'>"
                    });
                }
            </script>
            <h3 class="room-title-popup">{{ room.title|safe }}</h3>
            {{ room.description|safe }}
        </div>
    {% endfor %}
</div>