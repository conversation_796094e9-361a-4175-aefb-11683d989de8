<div class="minigallery_wrapper">
    {% if minigallery.subtitle %}<h2>{{ minigallery.subtitle|safe }}</h2>{% endif %}
    <div class="minigallery_content owl-carousel">
        {% for x in minigallery_pictures %}
            <a class="slider_element {% if x.title %}slider_element_title{% endif %}" {% if x.linkUrl %}href="{{ x.linkUrl|safe }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% else %}href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[minigallery]"{% endif %}>
                <img class="center_image" data-src="{{ x.servingUrl|safe }}" alt="{{ x.name|safe }}"
                     lazy="true"/>
                {% if x.title %}<span class="title">{{ x.title|safe }}</span>{% endif %}
            </a>
        {% endfor %}

    </div>
</div>

<script>
    $(window).load(function () {
        owl_params = {
            loop: true,
            nav: true,
            dots: false,
            items: 3,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            margin: 20,
            autoplay: true
        };

        $(".minigallery_content").owlCarousel(owl_params);
    })
</script>