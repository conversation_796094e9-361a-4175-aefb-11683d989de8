<div id="logo">
	<a href="{{host|safe}}">
		<img src="{{ logotype }}=s300"  alt="{{hotel_name|safe}}"/>
	</a>
</div><!-- end logo -->
<div id="free_wifi">
    <img src="/img/delco/wifi.jpg" alt="logo wifi"/>
</div>
<div id="language-selector">
	{% for key, value in language_codes.items %}
	<a href="{% if not key == 'es' %}{{ hostWithoutLanguage}}/{{ key }}/{% else %}/{% endif %}">
		<span {% if  key == language %} class="selected" {% endif %}>{{ key }}</span>
		{% if not forloop.last %} | {% endif %}
	</a>
	{% endfor %}
</div><!-- end language selector -->

{% if topSections %}
<div id="top-sections">
    {% for section in top_sections %}
    <a href="{{host|safe}}/{{section.friendlyUrl}}" class="link-top-sections">
        <span>{{ section.title|safe}}</span>
        {% if not forloop.last %} <span class="top-section-separator">| </span>{% endif %}
    </a>
    {% endfor %}
</div>
{% endif %}

<!--  -->
<div id="whatsapp">
	{{ T_telefono }}:  <span class="bigger">{{ whatsapp_id }}</span>
</div>
<!-- end whatsapp -->

{% if facebook_id or flickr_id or twitter_id or youtube_id %}
<div id="social-links-box">
	<p id="follow-us">{{ T_siguenos_en }}</p>
	<div class="social_small">
		<ul class="social-links">
			{% if facebook_id %}
			<li><a
				href="http://www.facebook.com/{{facebook_id}}" class="facebook"
				undefined="">Facebook</a>
			</li>
			{% endif%}
				
			{% if flickr_id %}
			<li>
				<a href="http://www.flickr.com/photos/{{flickr_id}}/"
				class="flickr" undefined="">Flickr</a>
			</li>
			{% endif%}
			
			{% if twitter_id %}
			<li>
				<a href="https://twitter.com/#!/{{twitter_id}}" class="twitter" undefined="">Twitter</a>
			</li>
			{% endif %}
			
			{% if youtube_id %}
			<li>
				<a href="http://www.youtube.com/user/{{youtube_id}}" class="youtube" undefined="">YouTube</a>
			</li>
			{% endif %}
		</ul>
	</div>
</div><!-- end social link box -->
{% endif %}


<ul id="main-sections">
	{% for section in main_sections %}
		<li>
		<a href="{{ host|safe }}/{{section.friendlyUrl}}" {% if sectionToUse.title == section.title %}class="section-active" {% endif %}>
			{{section.title|safe}}
		</a>			
		</li>
	{% endfor %}
</ul><!-- end main-sections -->
