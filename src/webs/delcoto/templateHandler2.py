import os

from booking_process.constants.advance_configs_names import TICKS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from webs.baseTemplateHandler import BaseTemplateHandler

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

class TemplateHandler(BaseTemplateHandler):

	def getAdditionalParams(self, currentSectionName, language, allSections):

		langs = self.getLanguages()

		additional_params_dict = {
			'langs': langs,
			'ticks': get_config_property_value(TICKS).split(";"),
			'topSections': True,
			'news': True,
		}

		return additional_params_dict
	
	def getTemplateUrl(self, section=None):
		return thisUrl

