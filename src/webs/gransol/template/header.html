<div class="superheader">
    <div class="wrapper-superheader container12">

        <div id="top-sections">
            {% for section in top_sections %}
                <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                    <span>{{ section.title|safe }}</span>
                </a>
                {% if not forloop.last %}<a>|</a>{% endif %}
            {% endfor %}
        </div>

        <div class="phone column2">
            {{phones.0|safe}}
        </div>

        <div class="oficial column3">
            {{web_oficial.content|safe}}
        </div>

    </div>
</div>

<header>
    <div id="wrapper-header" class="container12">


        <div id="logoDiv" class="column4">
            <a href="{{host|safe}}/">
                <img src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <div class="right-header column8">

           <div id="social">
                {%if facebook_id %}
                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/facebook.png" width="32" height="32"> </a>
                {% endif %}
                {% if twitter_id %}
                <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/twitter.png" width="32" height="32"> </a>
                {% endif %}
                {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/googleplus.png" width="32" height="32"> </a>
                {% endif %}
                {% if youtube_id %}
                <a href="https://www.youtube.com/user/{{youtube_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/youtube.png" width="32" height="32"> </a>
                {% endif %}
           </div>

           <div id="lang" class="column1">
                <span id="selected-language">{{ language_selected }}</span>
                <span class="arrow"></span>

                <ul id="language-selector-options">
                {% for key, language in languages.items %}
                     {% if not language_selected == language %} <li class="language-option-flag">
                          <a hreflang="{{key}}" href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{{ language }}</a>
                      </li>
                    {% endif %}
                {% endfor %}
                </ul>
           </div>


           <nav id="main_menu">
                <div id="mainMenuDiv">
                    {% include "main_div.html" %}
                </div>
           </nav>

        </div>
    </div>

</header>
