{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}

<section id="slider_container">
    {{ revolution_slider|safe }}
    <div id="wrapper_booking" class="container12">
        <div class="booking_widget">
            {{ booking_engine }}
            <div id="ticks">
                <ul>
                    <li><img src="/img/grans/pago-directo.png?v=1"></li>
                    <li><img src="/img/grans/sin-gastos.png?v=1"></li>
                    <li><img src="/img/grans/pago-seguro.png?v=1"></li>
                </ul>
            </div>
        </div>
    </div>
</section>

{% endblock %}
{% if show_banners %}
<section class="top-banners">
    <div class="wrapper-banners container12">
        <div class="banners">
            {% for banner in banners1 %}
            <div class="banner block-{{forloop.counter}}">
                <a href="{{banner.linkUrl|safe}}"><img src="{{banner.servingUrl|safe}}"/></a>
                <div class="banner-description">
                    <a href="{{banner.linkUrl|safe}}"><h3 class="title-banner">{{banner.title|safe}}</h3></a>
                    <a href="{{banner.linkUrl|safe}}">{{banner.description|safe}}</a>
                </div>
                <a href="{{banner.linkUrl|safe}}"><img class="arrow-banner" src="/img/granl/arrow-{{language}}.png"/></a>
                <img class="shadow" src="/img/granl/shadow.png"/>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% endblock %}


{% include "footer.html" %}


{% endblock %}