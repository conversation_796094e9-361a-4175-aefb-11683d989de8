{% extends empty_wrapper_for_mobile|default:"index.html" %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
        <div class="main-content-promotions">
{% for currentItem in  promotions %}
    {% if currentItem.visibleInWeb %}

        <div class="promotion_wrapper_1 {% cycle 'odd' 'even' %}">

            {% if currentItem.picture %}
                <div class="contFotoRooms">

                        <img src="{{ currentItem.picture }}=s400" alt="foto" />

                 </div>
                 <div class="block_description">
                        <div class="promotion-title"><h4 class="promotions_title">{{ currentItem.name|safe }}</h4></div>

                        <div class="promotions_description">
                            {{ currentItem.description|safe }}
                        </div>

                        <div class="promotions-buttons-wrapper">
                            <a class="promotion-view-more myFancyPopupAuto" href="#promo_full_description_{{ forloop.counter}}">
                                {{ T_ver_mas }}
                            </a>

                            {% if "noreservar" in  currentItem.priority|safe|lower   %}

                            {%  else  %}
                                <a class="cboxelement button-promotion" id="button2_promotions" href="{% if i_am_mobile %}/{% else %}#data{%  endif %}">
                                {{ T_reservar }}
                                </a>

                            {% endif %}

                        </div>
                 </div>

                <div class="promo_full_description" id="promo_full_description_{{ forloop.counter }}" style="display: none">
                    <h4 class="title-full-promo">{{ currentItem.name|safe }}</h4>
                    {{ currentItem.description|safe }}
                </div>
            {% endif %}
        </div>

    {% endif %}
{% endfor %}

        </div>
    </div>
</section>

{% endblock %}