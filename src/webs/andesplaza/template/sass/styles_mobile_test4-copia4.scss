$fontawesome5: true;
@import "plugins/mixins";

/* Font familys */
$primary_font: 'Quicksand', sans-serif;
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=<PERSON>ushan+Script&display=swap');
$secondary_font: '<PERSON><PERSON><PERSON>', cursive;
$secondary_letter_spacing: 0;
/* End */

@import "defaults";
@import "template_mixins";
@import "styles_mobile/2/2";

/* Font familys */
$primary_font: 'Quicksand', sans-serif;
@import url('https://fonts.googleapis.com/css2?family=<PERSON><PERSON><PERSON>+Script&display=swap');
$secondary_font: '<PERSON><PERSON><PERSON>', cursive;
$title_font_weight: 500;
/* End */


body {
  @include text_styles;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0px;
  overflow-x: hidden;

  @media (max-width: 575px) {
    line-height: 24px !important;
  }
  
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 0;
  }
  
  a {
    color: $links_color;
    text-decoration: none;
    font-weight: 500;
    letter-spacing: 1px;
    
    img {
      border: 0;
    }
  }
  
  img {
    height: auto;
    max-width: 100%;
  }

  .section_content {
    font-size: 16px;
  }
  
  header {
    .logo {
      img {
        margin-top: 5px;
      }
    }
  }
  
  .container12, .container14, .container_fluid {
    width: 100vw;
    padding: 0 20px;
  }
  
  .desc {
    margin: 15px 0 30px 0;
    
    strong {
      font-weight: 700;
    }
  }
  
  .lead {
    @include text_styles;
    text-align: left;
  }
  
  .default_content_wrapper {
    padding-bottom: 0;
  }
  
  .section_content {
    padding-top: 0;
  }
  
  .section_content .normal_section_mobile .section-content {
    padding: 20px;
    line-height: 26px;
  }
  
  .section_content h1,
  .section_content .normal_section_mobile h2.section_title {
    @include title_styles;
    padding: 40px 20px 20px;
  }
  
  .location_wrapper {
    display: flex;
    flex-direction: column;
    
    .map {
      outline: 1px solid;
      order: 3;

  
      .faqs_section & {
        display: none;
      }
    }
    
    .location_content {
      padding: 20px;
      
      .section-title {
        display: none;
      }
    
      .section-subtitle {
        @include title_styles;
        padding: 40px 20px 20px;
      }
    }
  }
  
  .content_title .title {
    @include title_styles;
    font-family: $secondary_font;
  }
  
  .my_reservation_section {
  
    .my-reservation-form {
      a {
        background-color: $corporate_2!important;
      
        &.modify_booking {
          background-color: $corporate_1!important;
        }
      }
    }
  }
  
  @import "mobile/banner_bienvenida";
  @import "mobile/banner_ventajas";
  @import "mobile/banner_destacados";
  @import "mobile/banner_experiencias";
  @import "mobile/banner_anchor_links";
  @import "mobile/banner_news";
  @import "mobile/banner_stories";
  @import "mobile/banner_social";
  @import "mobile/banner_calidad";
  @import "mobile/banner_cycle";
  @import "mobile/bannerx3";
  @import "mobile/banner_gallery";
  
  .rooms_background {
    background-color: transparent;
  }

  .news_wrapper {
    .entry_widget {
      .title {
        @include title_styles;
        padding: 30px 20px 30px !important;
          text-align: center;
          margin-bottom: 0;
      }

      .content {
        @extend .desc;
        font-size: 16px;
        line-height: 25px !important;
        padding-top: 0;
        margin-top: 0;
        text-align: center;
      }
    }
  }
  .rooms_wrapper {
    
    .room_block {
      margin: 20px;
      border-radius: 0;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
      
      .room_info {
        h1 {
          @include title_styles;
          padding: 30px 20px 30px !important;
          text-align: center;
          margin-bottom: 0;
        }
        
        .room_description {
          @extend .desc;
          font-size: 16px;
          line-height: 25px!important;
          padding-top: 0;
          margin-top: 0;
          text-align: center;
        }
      }
      
      .buttons {
        position: absolute;
        left: 0;
        right: 0;
        transform: none;
        border-radius: 0;
        display: flex;
        flex-flow: row nowrap;
        
        .room_link {
          width: 25%;
          border-radius: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          
          &:hover {
            background-color: $corporate_2 !important;
          }
        }
        
        .button-promotion {
          width: 75%;
          border-radius: 0;
          @extend .btn_primary;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          
          &:hover {
            background-color: $corporate_1 !important;
          }
        }
      }
    }
  }
  
  .promotions_wrapper {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 60px;
    
    .owl-item {
      
      .offer_content {
        border-radius: 0;
        width: 85%;
        text-align: center;
        top: 42%;
        
        h3 {
          @include title_styles;
          padding: 40px 20px 20px;
        }
        
        .picture {
          border-radius: 0;
        }
        
        .desc {
          margin: 0 0 115px;
          line-height: 20px;
          border-radius: 0;
          padding: 10px 10px 0 !important;
          font-size: 16px;
          @include ellipsis(7);
        }
        
        .offer_links_wrapper {
          overflow: visible;
          display: flex;
          align-items: center;
          
          .button-promotion {
            border-radius: 0;
            
            i {
              float: none;
              margin-right: 5px;
            }
          }
        }
      }
      
      &.active {
        
        z-index: 1;
      }
    }
    
    .owl-nav {
      display: none;
    }
  }
  
  
  .breadcrumbs {
    position: absolute;
    top: -10px;
    background-color: transparent;
    transition: all .6s;
    transition-delay: .5s;
    
    &.showed {
      position: fixed;
      top: 80px;
      left: 0;
      right: 0;
      z-index: 99;
      white-space: nowrap;
      background-color: $black;
    }
  }
  
  .mobile_engine {
    
    .booking_engine_mobile {
      
      #full_wrapper_booking {
        font-family: $primary_font;
        background-color: $lightgrey;
        
        .booking_form_title {
          padding-left: 0;
          margin-left: -15px;
          
          .booking_title_1, .booking_title_2, .best_price {
            font-weight: 700;
            color: $black;
          }
        }
        
        .destination_wrapper {
          background: white !important;
          margin-bottom: 10px;
          width: 100%;
          position: relative;
          
          &:before {
            content: '\f279';
            display: block;
            font-family: "Font Awesome 5 Pro", sans-serif;
            font-size: 14px;
            color: #666;
            @include center_y;
            left: 7px;
            z-index: 2;
          }
          
          &:after {
            content: '\f078';
            display: block;
            font-family: "Font Awesome 5 Pro", sans-serif;
            font-size: 18px;
            color: #666;
            @include center_y;
            right: 7px;
            z-index: 2;
          }
          
          select {
            width: 100%;
            height: 45px;
            padding-left: 35px;
            box-sizing: border-box;
          }
        }
        
        .dates_selector_personalized {
          width: calc(100% - 5px);
          
          .dates_selector_label {
            position: absolute;
            top: 8px;
            font-weight: 700;
            color: white;
          }
          
          .start_end_date_wrapper {
            
            .day {
              font-size: 60px;
              line-height: 60px;
              margin-bottom: 4px;
            }
            
            .month, .year {
              font-size: 13px;
            }
            
            .day, .month, .year {
              color: $black;
              font-family: $primary_font !important;
            }
            
            .start_date_personalized {
              width: 50%;
              left: 0;
            }
            
            .end_date_personalized {
              width: 50%;
              right: 0;
            }
          }
        }
        
        .wrapper_booking_button {
          
          .guest_selector {
            width: 50%;
          }
          
          .promocode_wrapper {
            width: 50%;
            background-color: white;
            
            .promocode_label {
            }
            
            input.promocode_input {
              color: $black;
              border: dashed 2px $grey;
              
              &::placeholder {
                color: $black;
              }
            }
          }
          
          .submit_button {
            width: 100%;
            background-color: $corporate_1;
            position: relative;
            font-size: $font_body_size;
            font-weight: 700;
            text-transform: uppercase;
          }
        }

        .room_list_wrapper {
          background-color: $black;
          label{
            color: $black;
          }
          .rooms_number_wrapper{
            background: white;
            &:before{
              color: $black;
            }
          }

          .room_list {
            background: white;

            .adults_selector, .children_selector {
              &:before {
                color: $black;
              }
            }
          }
          
          .rooms_wrapper {
            width: 100%;
            
            .room_list_wrapper_close {
              background-color: $corporate_2;
            }
          }
        }
      }
    }
    
    .mobile_engine_action {
      left: 0;
      right: 0;
      width: 100%;
      border-radius: 0;
      box-shadow: none;
      bottom: 60px;
      font-size: $font_body_size;
      font-weight: 700;
      text-transform: uppercase;
    }
    
    
    &.open {
      background-color: $lightgrey;
      
      .mobile_engine_action {
        background-color: $corporate_2;
        color: white;
        left: auto;
        right: 10px;
        bottom: 295px;
        z-index: 10;
        
        &::after {
          display: none;
        }
      }
    }
  }
  
  .main-owlslider {
    top: -80px;
    height: 100vh;
    margin-bottom: -70px;
    
    .owl-item {
      position: relative;
      
      .description_text {
        @include full_size;
        display: flex;
        justify-content: center;
        
        
        .cartela_slider {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: white;
          width: 100%;
          z-index: 2;
          
          .cartela_title {
            font-family: $secondary_font;
            font-size: 56px;
            line-height: 60px;
            font-weight: 500;
            margin-bottom: 30px;
            letter-spacing: 0;
      
            small {
              display: block;
              font-family: $primary_font;
              font-size: 26px;
              line-height: 28px;
              font-weight: 400;
        
              &.subtitle {
                margin-top: 15px;
              }
            }
          }
          
          
  
          &::before {
            @include full_size;
            background-color: #000000;
            opacity: .5;
            z-index: 1;
          }
        }
      }
    }
  }
  
  .main_menu {
    .social_menu {
      a {
        width: 30px;
        
        &.mailto {
          float: right;
          transform: translateX(-20px);
        }
        
        &:not(:first-of-type) {
          float: left;
          
          i {
            color: white!important;
          }
        }
        
        i {
          background-color: transparent;
        }
      }
    }
    
    .main_ul {
      
      li {
        border-color: lighten($corporate_2, 10%);
        
        a {
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}

.owl-carousel {
  .owl-dots {
    @include owl_dots_styles_mobile($corporate_2);
    bottom: -40px;
  }
}

#filter_wrapper_banner {
  width: 100%;
  border: 1px solid #cacaca;
  position: relative;
  margin-bottom: 20px;
  
  .banner_title, .filter_block {
    border-bottom: 1px solid #cacaca;
    padding: 15px;
    text-transform: uppercase;
    color: $black;
    font-size: 17px;
    letter-spacing: 1px;
    line-height: 20px;
    position: relative;
  }
  
  .options_list {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
    
    &.active {
      padding: 8px 15px;
      max-height: 600px;
    }
    
    .option_element {
      color: grey;
      font-weight: bold;
      text-transform: uppercase;
      margin-bottom: 5px;
      text-align: left;
      
      input {
        margin: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 15px;
        height: 15px;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        visibility: hidden;
        opacity: 0;
        
        & + label {
          &:before {
            content: '';
            position: absolute;
            left: -16px;
            width: 22px;
            height: 22px;
            border-radius: 20px;
            border: 1px solid $corporate_2;
            background: white;
          }
        }
        
        &:focus {
          outline: 0;
        }
        
        &:checked + label {
          color: $corporate_2;
          
          &:after {
            content: '';
            position: absolute;
            left: -12px;
            top: 12px;
            width: 16px;
            height: 16px;
            border-radius: 20px;
            background: $corporate_2;
          }
        }
      }
      
      label {
        margin-left: 10px;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        line-height: 20px;
        width: 250px;
        color: $corporate_3;
        cursor: pointer;
        font-size: 17px;
        font-weight: 400;
        letter-spacing: 1px;
        padding: 8px 3px 8px 28px;
      }
    }
  }
  
  .filter_block {
    padding: 0;
    
    &.active {
      .more {
        opacity: 0;
      }
      
      .less {
        opacity: 1;
      }
      
      .options_list {
        padding: 15px 15px 9px;
        background: #f4f4f4;
        max-height: 1100px;
      }
    }
  }
  
  .banner_title {
    font-size: 16px;
    letter-spacing: 0.4px;
    font-weight: 400;
    padding: 20px;
    
    i {
      margin-right: 10px;
      color: $corporate_2;
      font-size: 17px;
      font-weight: 300;
    }
  }
  
  .filter_block:last-of-type {
    border-bottom: 0;
  }
  
  .filter_title {
    position: relative;
    padding: 21px 25px;
    cursor: pointer;
    
    i {
      position: absolute;
      right: 15px;
      top: 50%;
      width: 23px;
      height: 23px;
      @include transform(translateY(-50%));
      color: $corporate_2;
      
      &:before {
        @include center_xy;
      }
      
      &.less {
        opacity: 0;
      }
    }
    
    .more {
      background: $corporate_2;
      border-radius: 50%;
      
      &:before {
        content: '\f067';
        font-weight: 300;
        color: white;
      }
    }
    
    .less {
      font-size: 20px;
    }
  }
}

#clear_filters_button {
  margin: 20px auto 40px;
}

.faqs_wrapper {
  color: gray;
  margin-bottom: 30px;
  
  .general_title_block {
    margin-bottom: 20px;
    
    .main_title {
      color: $corporate_1;
    }
  }
  
  .questions_wrapper {
    margin: 0 20px;
    padding: 7px 0;
    
    .filters_button {
      margin: 0 0 10px;
      border: 1px solid #d0d0d0;
    }
    
    .question_element {
      //border: 1px solid #d0d0d0;
      margin-bottom: 10px;
      display: block;
      
      &.hide {
        max-height: 0;
        overflow: hidden;
        border: 0;
        margin: 0;
      }
      
      .question {
        padding: 14px 50px 14px 20px;
        position: relative;
        font-weight: bold;
        border: 1px solid #d0d0d0;
        font-size: 14px;
        color: #424242;
        margin: 0;
        
        &:after {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          content: "\f13a";
          color: #2074ca;
          font-family: "Font Awesome 5 Pro";
          font-weight: 300;
          transition: all 0.5s;
        }
        
        i {
          position: absolute;
          right: 20px;
          top: 50%;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          -webkit-transition: all 0.3s;
          -moz-transition: all 0.3s;
          -ms-transition: all 0.3s;
          -o-transition: all 0.3s;
          transition: all 0.3s;
          color: lightblue;
        }
        
        .less {
          opacity: 0;
        }
      }
      
      &.active {
        .question:after {
          @include transform(rotate(180deg));
          top: 31%;
        }
      }
      
      .answer {
        margin: 0 7px;
        padding: 0 18px;
        background: white;
        max-height: 0;
        overflow: hidden;
        -webkit-transition: all 0.5s;
        -moz-transition: all 0.5s;
        -ms-transition: all 0.5s;
        -o-transition: all 0.5s;
        transition: all 0.5s;
        font-size: 12px;
        letter-spacing: 0.2px;
        color: #424242;
        line-height: 20px;
        
        .faqs_link {
          display: block;
          text-align: center;
          text-transform: uppercase;
          font-size: 12px;
          margin-top: 15px;
          color: lightblue;
          font-weight: 600;
          letter-spacing: 0.2px;
        }
      }
      
      &.active {
        .answer {
          padding: 10px 18px;
          max-height: 500px;
        }
        
        .less {
          opacity: 1;
        }
        
        .more {
          opacity: 0;
        }
      }
    }
  }
}

.black_overlay{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.2);
  }

.normal_section_mobile {
  .section-content {
    .faq_element {
      margin-bottom: 20px;

      &:first-of-type {
        margin-bottom: 20px;
      }

      .faq_title {
        text-align: left;
        font-weight: bold;
        cursor: pointer;

        .fa {
          float: right;
          color: $corporate_1;
          @include transition(transform, .6s);

          &.rotate {
            -webkit-transform: rotateZ(45deg);
            -moz-transform: rotateZ(45deg);
            -ms-transform: rotateZ(45deg);
            -o-transform: rotateZ(45deg);
            transform: rotateZ(45deg);
            @include transition(transform, .6s);
          }
        }
      }

      .faq_description {
        text-align: justify;
        display: none;
      }
    }
  }
}

@import "mobile/banner_iframe";