.banner_destacados_wrapper {
  padding: 0 0 70px;
  margin: 0 0 70px;
  
  .top_banner {
    margin-bottom: 50px;
    
    .content_title {
      text-align: center;
    }
  }
  
  .gallery_wrapper {
    width: calc(100% - 30px);
    margin: 0 auto;

    .owl-stage-outer {
      .owl-stage {
        display: flex;
        align-items: center;
        
        .owl-item {
          .item {
            position: relative;
            height: 400px;
            overflow: hidden;
            //transition: all .5s;
            
            img {
              @include cover_image;
            }

            .content_wrapper {
              z-index: 1;
              position: absolute;
              height: auto;
              bottom: 30px;
              left: 30px;
              right: 30px;
              width: 310px;
              min-width: 310px;
              color: white;
              overflow: hidden;
    
              .content_title {
                margin-bottom: 0;
                
                .title {
                  font-size: 22px;
                  line-height: 25px;
                  color: white;
        
                  small {
                    font-size: 12px;
                    line-height: 14px;
                    color: white;
                    font-weight: bold;
                    display: none;
                  }
                }
              }
              
              .desc {
                line-height: 24px;
                max-height: 0;
                overflow: hidden;
                margin-bottom: 0;
                transition: all .7s;
              }
            }
  
            .links_wrapper {
              z-index: 1;
              display: inline-block;
              position: absolute;
              bottom: 30px;
              right: 30px;
              transform: translateX(calc(100% + 35px));
              transition: all .5s;
            }
  
            &::before {
              position: absolute;
              content: '';
              top: -300px;
              bottom: 0;
              left: 0;
              right: 0;
              background: rgb(0,0,0);
              background: linear-gradient(0deg, rgba(0,0,0,0.8183648459383753) 0%, rgba(0,0,0,0) 100%);
              opacity: .6;
              transition: all .5s;
            }
          }
          
          &.active {
            width: 380px!important;
            transition: width 0.5s;
            
            &.center {
              width: 600px!important;
  
              .item {
                height: 500px;
    
                img {
                  @include cover_image;
                }
                
                .content_wrapper {
                  .content_title {
                    .title {
                      font-size: 38px;
                      line-height: 40px;
                      
                      small {
                        display: block;
                      }
                    }
                  }
                }
                
                .links_wrapper {
                  transform: translateX(0);
                  transition-delay: .6s;
                }
                
                
  
                &::before {
                  top: 300px;
                  opacity: 1;
                }
              }
              
              &:hover {
                .item {
                  .content_wrapper {
                    .desc {
                      max-height: 500px;
                      overflow: hidden;
                      margin-bottom: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .owl-nav {
      @include owl_nav_styles_2;
    }
    
    .owl-dots {
      @include owl_dots_styles($corporate_2);
    }
  }
}