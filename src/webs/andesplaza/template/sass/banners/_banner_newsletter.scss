.banner_newsletter_wrapper {
  @include sec_pad;
  background-repeat: repeat-x;
  background-position: top center;
  
  .form_wrapper {
    position: relative;
    
    .newsletter_wrapper {
      background-image: none!important;
      
      .newsletter_container {
        position: relative;
        
        .newsletter_title {
          @include title_styles;
          text-align: center;
        }
        
        .newsletter_description {
          display: none;
        }
        
        .newsletter_form {
          position: relative;
          margin-top: 20px;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          
          .input_email {
            border: solid 1px $grey;
            text-align: center;
            padding: 8px 25px 5px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 300;
            text-transform: uppercase;
            background-color: transparent;
            width: 955px;
            height: 50px;
            font-family: $primary_font;
            margin-bottom: 20px;
            @include flex_xy;
          }
  
          .button_newsletter {
            position: absolute;
            top: 0;
            left: 960px;
            display: inline-flex;
            align-items: center;
            flex-wrap: nowrap;
            border: none;
            text-align: center;
            background-color: $corporate_1;
            color: white;
            padding: 0 25px 0 0;
            height: 50px;
            border-radius: 35px;
            text-decoration: none;
            font-weight: 500;
            text-transform: uppercase;
            font-size: $font_body_size;
            font-family: $primary_font;
            transition: all .5s;
            cursor: pointer;
            
            &::before {
              display: inline-block;
              content: '\f0e0';
              font-family: 'Font Awesome 5 Pro';
              color: white;
              font-size: 18px;
              border-right: solid 1px white;
              height: 50px;
              width: 60px;
              overflow: hidden;
              @include flex_xy;
              padding-left: 3px;
              margin-right: 15px;
              opacity: 1;
              transition: all .5s;
            }
  
            &::after {
              display: inline-block;
              content: '';
              background-image: url("https://storage.googleapis.com/cdn.paraty.es/em-ande/files/arrow_right_white.svg");
              width: 0;
              height: 25px;
              background-size: 100% auto;
              background-position: 50% 50%;
              z-index: 1;
              background-repeat: no-repeat;
              margin-left: 0;
              overflow: hidden;
              transition: all .5s;
            }
  
            &:hover {
              padding-left: 30px;
              
              &::before {
                width: 0;
                opacity: 0;
                padding-left: 0;
                margin-right: 0;
                border: none;
              }
              
              &::after {
                width: 25px !important;
                margin-left: 15px;
              }
            }
          }
          
          /*
          .button_newsletter {
            @extend .btn_primary;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            
            &::before {
              position: absolute;
              content: '\f0e0';
              height: 50px;
              width: 70px;
              transform: translateX(-100%);
              background: $corporate_1;
              font-family: 'Font Awesome 5 Pro';
              top: 0;
              left: 0;
              bottom: 0;
              color: white;
              font-size: 28px;
              font-weight: 100;
              border-top-left-radius: 35px;
              border-bottom-left-radius: 35px;
              @include flex_xy;
            }
  
            &::after {
              position: absolute;
              content: '';
              width: 1px;
              background-color: white;
              top: 0;
              bottom: 0;
              left: 0;
            }
          }
          */
          
          .check_newsletter {
            width: 100%;
            line-height: 16px;
            margin-bottom: 10px;
            margin-left: 90px;
            
            .newsletter_checkbox {
              padding-left: 40px;
              position: relative;
              
              .check_privacy {
                position: absolute;
                top: 0;
                left: 0;
              }
              
              a,
              label {
                font-family: $primary_font;
                font-size: 12px;
                font-weight: 300;
              }
            }
          }
        }
        
        .social_newsletter {
          display: flex;
          justify-content: center;
          margin-top: 10px;
          
          a {
            display: block;
            margin: 0 10px;
            
            i {
              font-weight: 100;
              font-size: 30px ;
              color: $corporate_2;
            }
          }
        }
      }
    }
  
    .gallery_wrapper {
      width: 1000px;
      position: absolute;
      top: 120px;
      right: calc((100% - 1140px) / 2);
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 20px;
      
      .picture_wrapper {
        height: 250px;
      }
    }
  }
}

