.offers_wrapper {
  @include sec_pad;
  margin-top: 0;
  padding-top: 0;
  position: relative;
  
  .container12 {
    position: relative;
    z-index: 1;
    
    .cards_wrapper {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20px;
      
      
      .card {
        position: relative;
        overflow: hidden;
        background-color: $lightgrey;
        
        .picture_wrapper {
          display: block;
          height: 265px;
        }
        
        .content_wrapper {
          display: block;
          overflow: hidden;
          text-align: left;
          padding: 30px 30px 100px;
          
          .content_title {
            margin-bottom: 20px;
            
            .title {
              font-size: 28px;
              line-height: 30px;
            }
          }
        }
        
        .links_wrapper {
          position: absolute;
          bottom: 30px;
          left: 30px;
          right: 30px;
          
          .link_more {
            margin-bottom: 10px;
            color: $corporate_1;
          }
        }
      }
    }
  }
}