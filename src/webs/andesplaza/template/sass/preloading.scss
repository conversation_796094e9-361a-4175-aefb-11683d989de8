@keyframes full_width {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.loading_site {
  .center_element {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    text-align: center;
    transform: translateY(-50%);

    .logo_loading {
      max-height: 100px;
    }

    .loading_progress_bar {
      width: 300px;
      height: 5px;
      margin: 40px auto 0;
      background: #dfdfdf;
      border-radius: 30px;
      position: relative;
      overflow: hidden;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        border-radius: 30px;
        width: 100%;
        background: #002954;
        animation-name: full_width;
        animation-duration: 3s;
      }
    }
  }

  .overlay_radient {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    opacity: 0.9;
  }
}