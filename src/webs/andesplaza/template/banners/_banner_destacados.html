<div class="banner_destacados_wrapper">
    <div class="container14">
        <div class="top_banner">
            {% if banner_destacados_sec.subtitle %}
            <div class="content_title">
                <h3 class="title">
                    {{ banner_destacados_sec.subtitle|safe }}
                </h3>
            </div>
            {% endif %}
        </div>
        <div class="gallery_wrapper owl-carousel">
            {% for item in banner_destacados_pics %}
            <div class="item">
                <img src="{{ item.servingUrl|safe }}=s1900" {% if item.altText %}alt="{{ item.altText|safe }}"{% endif %}>
                <div class="content_wrapper">
                    {% if item.title %}
                    <div class="content_title">
                        <h4 class="title">
                            {{ item.title|safe }}
                        </h4>
                    </div>
                    {% endif %}
                    {% if item.description %}
                    <div class="desc">
                        {{ item.description|safe }}
                    </div>
                    {% endif %}
                </div>
                {% if item.linkUrl %}
                <div class="links_wrapper">
                    <a href="{{ item.linkUrl }}" class="btn_primary {% if item.has_icon %}has_icon{% endif %}">
                        {% if item.has_icon %}
                        <i class="{{ item.has_icon|safe }}"></i>
                        {% endif %}
                        {% if item.link_text %}
                        {{ item.link_text|safe }}
                        {% else %}
                        {{ T_saber_mas }}
                        {% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>
<script>
    $(window).load(function () {

        {% if is_mobile %}

            $(".banner_destacados_wrapper .owl-carousel").owlCarousel({
                loop: true,
                center: true,
                nav: false,
                dots: true,
                items: 1,
                navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
                autoplay: true,
                autoHeight: true
            });

        {% else %}

            $(".banner_destacados_wrapper .owl-carousel").owlCarousel({
                loop: true,
                center: true,
                nav: true,
                dots: true,
                items: 3,
                navText: ['<span></span>', '<span></span>'],
                autoplay: false
            });

        {% endif %}
    });
</script>





