<div class="banner_blog_wrapper">
    <div class="container12">
        <div class="top_banner">
            <div id="social">
                {% if facebook_id %}
                    <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{ youtube_id }}" target="_blank">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>
        </div>
        <div class="cards_wrapper">
            {% for entry in news_entries %}
                <div class="card">
                    {% if entry.picture %}
                        <div class="picture_wrapper">
                            <img src="{{ entry.picture|safe }}" {% if entry.altText %}alt="{{ entry.altText|safe }}"{% endif %}>
                        </div>
                    {% endif %}
                    <div class="content_wrapper">
                        {% if entry.creationDate %}
                        <span class="entry_date">{{ entry.creationDate|safe }}</span>
                        {% endif %} 
                        {% if entry.name %}
                            <div class="content_title">
                                <h4 class="title">
                                    {{ entry.name|safe }}
                                </h4>
                            </div>
                        {% endif %}
                    </div>
                    <div class="links_wrapper">
                        <a href="/{% if language_code != lang_default %}{{ language_code }}/{% endif %}{{ T_noticias_link|lower|safe }}/{{ entry.friendlyUrl }}" class="btn_primary has_icon">
                            <i class="fas fa-plus"></i>
                            {{ T_leer_mas }}
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
