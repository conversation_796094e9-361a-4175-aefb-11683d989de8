<div class="rooms_wrapper">
    {% for room in rooms %}
        <div class="room">
            <div class="room_gallery {% if room.gallery %}owl-carousel{% endif %}">
                {% if room.servingUrl %}
                    <div class="room_img">
                        <img src="{{ room.servingUrl|safe }}=s1000" {% if room.altText %}alt="{{ room.altText|safe }}" {% endif %}>
                    </div>
                {% endif %}
                {% for image in room.gallery %}
                    <div class="room_img">
                        <img src="{{ image.servingUrl|safe }}=s1000" {% if image.altText %}alt="{{ image.altText|safe }}" {% endif %}>
                    </div>
                {% endfor %}
            </div>
            {% if room.title or room.description %}
                <div class="room_content">
                    {% if room.title %}
                        <div class="content_title">
                            <h3 class="title">{{ room.title|safe }}</h3>
                        </div>
                    {% endif %}
                    {% if room.description %}
                        <div class="desc">{{ room.description|safe }}</div>
                    {% endif %}
                    {% if room.room_icons %}
                        <div class="room_icons">
                        {% for icon in room.room_icons %}
                            <span class="tooltip">
                                <img src="{{ icon.servingUrl|safe }}" class="icon_img" {% if icon.altText %}alt="{{ icon.altText|safe }}" {% endif %}>
                                {% if icon.description %}
                                    <span class="tooltiptext">{{ icon.description|safe }}</span>
                                {% endif %}
                            </span>
                        {% endfor %}
                        </div>
                    {% endif %}
                    <div class="links_wrapper">
                        <a href="#data" class="button_promotion btn_primary">
                            {{ T_reservar }}
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(function () {
        if ($(".room_gallery.owl-carousel").length) {
            $(".room_gallery.owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                dots: false,
                items: 1,
                navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
                margin: 0,
                autoHeight: false,
                autoplay: false
            });
        }
    })
</script>