<div class="normal_section_mobile">
  {% if section_params.title %}
    <h2 class="section_title">{{section_params.title|safe}}</h2>
  {% endif %}

  <div class="section-content">
     {{section_params.content|safe}}
  </div>

  {# Blocks #}
{% if blocks_mobile %}
    <div class="gray_background">
        <div class="destinations_elements_wrapper container12">
            {% for x in blocks_mobile %}

                <div class="dest_wrapper" id="destinationblock-{{ forloop.counter }}">

                    <div class="title-dest">{{ x.title|safe }}</div>

                    <ul class="slides">
                        <li>
                            <a href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[gallery_{{ x.title|safe }}]" class="swipebox">
                                <img class="room_picture" src="{{ x.servingUrl|safe }}=s1900">
                            </a>
                        </li>
                    </ul>

                    <span class="separator"></span>
                    <div class="desc-dest" id="desc-dest-{{ forloop.counter }}">
                          <span id="real_desc_dest_{{ forloop.counter }}" class="real_desc_dest">
                            {{ x.description|safe }}
                          </span>
                    </div>

                    {% if x.linkUrl %}
                    <div class="see_more">
                        <a href="{{ x.linkUrl|safe }}">{{ T_ver_mas }}</a>
                    </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}

{# Images Carousel #}
{% if mini_gallery %}
    <div class="mini_gallery_wrapper">
        <div class="flexslider_mini_gallery">
            <ul class="slides">
                {% for x in mini_gallery %}
                    <li>
                        <div class="text-bannerx2">
                            <a href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[gallery]" class="swipebox">
                                <img src="{{ x.servingUrl|safe }}">
                            </a>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>

        <script>
            $(function () {
                $(".flexslider_mini_gallery").flexslider({
                    controlNav: false
                });
            });
        </script>
    </div>
{% endif %}

</div>