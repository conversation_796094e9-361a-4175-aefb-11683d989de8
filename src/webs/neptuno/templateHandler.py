# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.constants.advance_configs_names import CONTACT_PHONES
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "nepto"


class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def get_hotelname(self):

		host = os.environ.get('HTTP_HOST')
		name = ''

		if "hotel" in host or "localhost" in host:
			name = "hotelneptuno"
		else:
			name = "apartamentosneptuno"

		return name


	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {
			'base_web': base_web,
			'footer_columns': get_pictures_from_section_name("footer columns", language),
			'bannersx2': get_pictures_from_section_name('bannersx2', language),
			'phones': get_config_property_value(CONTACT_PHONES),
			'location_html': get_section_from_section_spanish_name("contacto", language),
			'web_oficial_txt': get_section_from_section_spanish_name('web oficial', language),
			'namehotel': self.get_hotelname
		}

		if section_type == 'Inicio':
			result_params_dict['bannersx4'] = get_pictures_from_section_name('bannersx4', language)
			result_params_dict['datos_ini'] = True
			result_params_dict["bottom_popup"] = get_section_from_section_spanish_name("popup inicio footer", language)
			result_params_dict["bottom_popup_text"] = get_section_from_section_spanish_name("promocion pop up", language)

		else:
			result_params_dict['inner_access'] = True

		actual_section = get_section_from_section_spanish_name(sectionToUse.get('sectionName', ''), language)
		if actual_section.get('subtitle', False):
			result_params_dict['content_subtitle'] = actual_section

		#Banners x3
		bannersx3 = {'title': get_section_from_section_spanish_name('bannersx3', language),
					'pictures': get_pictures_from_section_name('bannersx3', language)}

		#Section by automatic content
		automatic_content = {
			'Mis Reservas': True,
			'Galeria de Imagenes': True
		}

		if automatic_content.get(section_type, False):
			result_params_dict['content_access'] = True

		result_params_dict['bannersx3'] = bannersx3

		#Mis reservas
		if section_type == 'Mis Reservas':
			result_params_dict['my_booking_access'] = True

		if section_type != 'Inicio' and result_params_dict.get('content_subtitle', False):
			result_params_dict['bannersx3'] = None

		if section_name == 'servicios':
			result_params_dict['hide_hover_on_banners'] = True
			result_params_dict["servicios_blocks"] = get_pictures_from_section_name("servicios_blocks", language)

		if section_type == 'Galeria de Imagenes':
			result_params_dict['pictures'] = get_pictures_from_section_name("slider_gallery", language)

		if advance_properties.get('bannersx4', False):
			result_params_dict['bannersx4_bottom'] = get_pictures_from_section_name(advance_properties['bannersx4'], language)

		if advance_properties.get('bannersx3', False):
			result_params_dict['bannersx4_bottom'] = get_pictures_from_section_name(advance_properties['bannersx3'], language)
			result_params_dict['center_bannerx4_bottom'] = True

		if advance_properties.get('bannersx3_2'):
			result_params_dict['bannersx3_2'] = get_pictures_from_section_name(advance_properties['bannersx3_2'], language)

		if advance_properties.get('blocks', False):
			result_params_dict['block_advanced'] = get_pictures_from_section_name(advance_properties['blocks'], language)

		if advance_properties.get('cycle_banners'):
			result_params_dict['cycle_banners'] = get_pictures_from_section_name(advance_properties['cycle_banners'], language)
			cycle_path = os.path.dirname(__file__) + '/template/cycle_banners.html'
			cycle_banners_html = buildTemplate(cycle_path, result_params_dict)
			result_params_dict['content_subtitle']['content'] = result_params_dict['content_subtitle']['content'].replace('@@cycle_banner@@', cycle_banners_html)

		if advance_properties.get('fullgallery'):
			result_params_dict['full_gallery_width'] = get_pictures_from_section_name(advance_properties['fullgallery'], language)

		if section_name == 'blog':
			#Split
			spliter = get_pictures_from_section_name("blog", language)
			for x in spliter:
				if x['title']:
					infoBlog = x['title'].split("@@")

					if len(infoBlog)> 1:
						x['date'] = infoBlog[1]
						x['title']  = infoBlog[0]

			result_params_dict['blog'] = spliter
			result_params_dict['pictures'] = get_pictures_from_section_name("blog_slider", language)

		if section_type == u'Extra 1':
			result_params_dict['normal_content'] = ''
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html = get_section_from_section_spanish_name(u"cómo llegar", language)

			subtitle_form = sectionToUse['subtitle']

			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['subtitle_form'] = subtitle_form
			result_params_dict['localizacion_access'] = True
			result_params_dict['location_title'] = get_section_from_section_spanish_name(u'cómo llegar', language)
			result_params_dict['location_links'] = get_pictures_from_section_name('location links', language)
			result_params_dict['iframe_google_map'] = get_section_from_section_spanish_name('Iframe google maps', language)
			result_params_dict['content_subtitle'] = False

		if section_name == u'contacto':
			result_params_dict['normal_content'] = ''
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['custom_checkbox'] = get_section_from_section_spanish_name("Clausula informativa Atención al cliente", language)

			sectionTemplate = 'secciones/contact_custom_check.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			result_params_dict['contact_html'] = contact_html
			result_params_dict['contact_page'] = get_section_from_section_spanish_name(u'contacto', language)

		if section_type == 'Habitaciones':
			result_params_dict['rooms'] = get_pictures_from_section_name('habitaciones_blocks', language)
			for x in result_params_dict['rooms']:
				x['pictures'] = get_pictures_from_section_name(x['linkUrl'], language)

		if section_type == 'Ofertas':
			result_params_dict['promotions'] = super(TemplateHandler, self).buildPromotionsInfo(language)

		if advance_properties.get("minigallery", False):
			result_params_dict["minigallery"] = get_pictures_from_section_name(advance_properties["minigallery"],language)

		return result_params_dict

	def getTemplateUrl(self, section=None):

		section_name = ""
		if section:
			section_name = section['sectionName'].lower().strip()

		if section_name == "blog":
			template_name = 'neptuno/template/blog.html'

		else:
			template_name = 'neptuno/template/index.html'

		return template_name

	def get_revolution_initial_height(self):
		return "565"

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		context = {
			'booking_header': get_section_from_section_spanish_name('booking_header', language)

		}
		context['booking_header_promocode'] = self.getSectionAdvanceProperties(context['booking_header'], language).get('promocode')
		options['custom_title_html'] = buildTemplate('%s/template/header_booking.html' % '/'.join(os.path.abspath(__file__).split("/")[:-1]), context)
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		template_path = 'booking/booking_engine_5/_booking_widget.html'
		return self.buildTemplate(template_path, params)

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			result['pictures_dict'] = True
			result['old_gallery'] = True


			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:

				nameFilter = x.get('title', "")
				if not nameFilter:
					nameFilter = ""

				if x.get('linkUrl', False):
					x['servingUrl'] = x['linkUrl']

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [{'servingUrl': x['servingUrl'], 'description': x['description']}]
				else:
					filters[nameFilter].append({'servingUrl': x['servingUrl'], 'description': x['description']})
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)


	def get_revolution_delay(self):
			'''Delay time of revolution'''
			delay = '9000'
			return delay

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		sectionToUse = self.getSectionParams(sectionFriendlyUrl, language)
		base_path = os.path.dirname(__file__)
		resultParams = {}


		if sectionToUse:
			name_current_section = sectionToUse['sectionName'].lower().strip()
			type_current_section = sectionToUse['sectionType']
			advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

			if user_agent_is_mobile():

				if type_current_section == "Normal":

					blocks_banner_type = [advance_properties.get("bannersx4", ""), advance_properties.get("bannersx3", ""),
										  name_current_section + "_blocks", advance_properties.get("blocks", ""), advance_properties.get('bannersx3_2')]
					blocks_banner = []

					for type in blocks_banner_type:
						for x in get_pictures_from_section_name(type, language):
							blocks_banner.append(x)

					resultParams['blocks_mobile'] = blocks_banner
					resultParams['section_params'] = sectionToUse

					resultParams['mini_gallery'] = get_pictures_from_section_name(advance_properties.get('fullgallery', ""), language)

					fullPath = os.path.join(base_path, 'template/mobile/blocks_gallery_mobile.html')
					result = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()) + list(resultParams.items()))
					return buildTemplate(fullPath, result)




		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)