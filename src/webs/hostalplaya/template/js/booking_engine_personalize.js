$(window).load(function () {
    prepare_guests_selector();
    fade_out_widget();
    label_personlaize_promocode();
});

function label_personlaize_promocode() {
    $("#full_wrapper_booking .wrapper_booking_button label.promocode_label").click(function () {
        $(this).fadeOut();
        $(this).parent().find("input.promocode_input").focus();
    });
    $("#full_wrapper_booking .wrapper_booking_button input.promocode_input").blur(function () {
        if ($("#full_wrapper_booking input.promocode_input").val() == "") {
            $(this).parent().find("label.promocode_label").fadeIn();
        }
    })
}

function prepareRoomNumbers2(labelName, roomName, roomNamePlural, defaultValue) {

    var newStringOption = "";

    $(".rooms_number[name='numRooms'] option").remove();
    //$("#selector_habitaciones[name='numRooms']").append('<option value="' + defaultValue + '">' + labelName + '</option>');
    for (x = 1; x < 4; x++) {

        if (x == 1) newStringOption = roomName;
        else newStringOption = roomNamePlural;

        if (x == 1) {
            $(".rooms_number[name='numRooms']").append('<option value="' + x + '" selected="selected">' + x + " " + newStringOption + ' </option>');
        } else {
            $(".rooms_number[name='numRooms']").append('<option value="' + x + '">' + x + " " + newStringOption + ' </option>');
        }

    }

    $(".rooms_number[name='numRooms']").selectric('refresh');
}





function prepare_guests_selector() {
   $('body').on('click', '.guest_selector, .close_guesst_button, .save_guest_button', function() {
      toggle_guest_selector();
   });

   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

   //Close room button
   for (var x=2;x<=3;x++){
      var close_button_element = $("<div class='remove_room_element'></div>");
      $(".room_list .room" + x).append(close_button_element);
   }

   $(".remove_room_element").click(function(){
      var actual_room_numbers = $("select.rooms_number").val();
      if (actual_room_numbers > 1){
         var target_room_number = parseInt(actual_room_numbers) - 1;
         $("select.rooms_number option").removeAttr('selected');
         $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
         $(".room" + actual_room_numbers).hide();
         $("select.rooms_number").val(target_room_number);
         $("select.rooms_number").selectric("refresh");
      }
      set_occupancy_number()
   });
}

function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}



function set_occupancy_number(){
   var number_of_rooms = $("select[name='numRooms']").val(),
       adults_number = 0,
       kids_number = 0

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
         }
      }
   }

   var target_placeholder = $(".guest_selector .placeholder_text"),
       placeholder_string = "";

   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);

   placeholder_string += "<span class='guest_adults'>" + adults_number + "</span>";

   if(!$(".adults_only_selector").length){
      placeholder_string += " - " + kids_number + " ";
   }

   target_placeholder.html(placeholder_string);
}