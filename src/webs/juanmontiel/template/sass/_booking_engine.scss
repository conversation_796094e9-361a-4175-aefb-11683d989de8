
/*=============== Booking Widget ================*/
div#wrapper_booking {
  position: absolute;
  top: 150px;
  left: 0px;
  right: 0px;
  height: 295px;
}

.spinner_wrapper {
  text-align: center;
  position: relative !important;
  width: 25px;
  height: 25px;
  margin: auto;
  right: 0px !important;
  bottom: 0px !important;
  padding: 15px;
  display: none;
}

.interior #wrapper_booking {
  height: 600px;
  bottom: auto;
}

.fancybox-inner {
  overflow: initial;
}

.border_booking {
  height: auto !important;
  width: 235px !important;
  box-sizing: border-box;
  background: white;
}

.adults_selector .label, .children_selector .label, .babies_selector .label {
  width: 70%;
}

#data, .landing_booking_popup {
  height: auto !important;
}

.booking_widget {
  //top: 0px;
}

.interior {
  .booking_widget {
    top: 0px;
    margin-top: 105px;
  }
}

.booking_widget, #data, .landing_booking_popup {
  margin: auto;
  height: 287px;
  width: 260px;
  top: 0;

  .room {
    padding-left: 0;
    margin-left: 12px;
    .room_title {
      display: none;
    }
    .babies_selector {
      margin-left: 2px;
    }
  }

  .month {
    overflow: hidden;
  }

  .booking_form {
    height: auto;
    box-sizing: border-box;
    margin: 0px !important;
    padding: 0px;
    width: 100%;

    .stay_selection {
      display: table;
      text-align: center;
      margin: 0 auto;
      padding-top: 20px;

      label {
        text-transform: uppercase;
        font-size: 10px;
      }
    }

    .wrapper_booking_button button {
      border-radius: 0px;
      width: 211px !important;
      float: none;
      margin: 8px auto 0px;
      display: block;
      font-weight: lighter;
      font-size: 20px;
      font-family: 'Source Sans Pro', sans-serif;
      background: #ffd600;
      color: white;
      height: 30px;
      margin-bottom: 12px;
      &:hover {
        opacity: .8;
      }
    }
  }

  .best_price {
    display: none;
  }

  h4.booking_title_2 {
    display: block;
    font-family: yanone, sans-serif;
    text-transform: uppercase;
    font-weight: 300;
    font-size: 16px;
    line-height: 25px;
  }

  .booking_form_title:before {
    border-top: 8px solid $corporate_1;
    bottom: -7px;
  }

  .booking_form_title {
    background: $corporate_1;
    width: 100%;
    height: 41px;
    box-sizing: border-box;

    &:not(.wrapper-new-web-support) {
      //cursor: pointer;
      padding: 9px;
      height: 40px;
    }
    &:hover:not(.wrapper-new-web-support) {
      //background: $corporate_1;

      &:before {
        // border-top: 8px solid $corporate_1;
      }
    }
  }

  .date_box {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    .date_year {
      color: rgb(136, 130, 130);
    }

    &:before {
      border-bottom: 8px solid rgb(234, 234, 234);
    }

    .date_day .day {
      border-bottom: 1px solid rgb(136, 130, 130);
      font-weight: lighter;
      line-height: 50px;
      font-size: 39px;
      color: rgb(136, 130, 130);
      font-family: yanone, sans-serif;
    }

    .date_day .month {
      border-bottom: 1px solid rgb(136, 130, 130);
      color: rgb(136, 130, 130);
    }
  }

  .selectric {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    .label {
      color: rgb(136, 130, 130) !important;
      font-family: yanone, sans-serif;
      font-size: 22px !important;
    }

    &:before {
      border-bottom: 8px solid #EAEAEA;
      top: -6px;
    }

    .button {
      background: url(/static_1/images/booking/arrow_down_small.png) no-repeat center center !important;
    }
  }

  .selectricItems {
    li.selected {
      background: #EFEFEF;
      color: #444;
      border-top-color: #E0E0E0;
    }

    li {
      font-weight: 500;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
      color: #a5a5a5;
      font-size: 25px;
      line-height: 40px;
      border-bottom: 1px solid #fff;
      padding: 10px 0px;
      width: 100%;
      text-align: center;

      &:hover {
        background: #EFEFEF;
        color: #444;
        border-top-color: #E0E0E0;
      }
    }

    ul {
      z-index: 40;
    }

    .room {
      padding-top: 17px;
    }
  }

  .room_title {
    padding-left: 11px;
  }

  .adults_selector {
    padding-left: 12px;
  }

  input.promocode_input {
    background: rgb(234, 234, 234);
    width: 211px !important;
    margin-left: 24px;
    border-radius: 0;
    height: 30px;
    display: block !important;
  }

  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label, .wrapper_booking_button .promocode_text {
    color: rgb(134, 134, 134);
  }

  .promocode_text {
    font-family: yanone, sans-serif;
  }
}

.booking_widget {
  //right: 83px;
}

.selectricOpen .selectricItems {
  z-index: 22222;
}

.room_list .selectricOpen .selectricItems {
  top: 35px !important;
  overflow: auto;
}

.rooms_number_wrapper .selectricOpen .selectricItems {
  top: 75px !important;
}

.wrapper-new-web-support {
  opacity: 1 !important;
  border-radius: 0px !important;

  &:before {
    content: none;
  }
}

.date_box, .selectric {
  height: 75px !important;
  width: 66px;
}

.date_box .date_day .month {
  font-size: 12px;
}

.booking_widget .date_box .date_day .day, #data .date_box .date_day .day, .landing_booking_popup .date_box .date_day .day {
  font-size: 23px;
  line-height: 27px !important;
  padding-bottom: 0px;
  padding-top: 2px;
}

.date_box .date_year {
  font-size: 12px;
}

.selectric .more_room_button {
  top: 3px;
}

.selectric .less_room_button {
  bottom: 3px;
}

.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper, .room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
  width: 68px;
}

.selectricWrapper {
  width: 65px;
  height: 35px;
}

.room_list .selectric {
  height: 35px !important;
}

.wrapper_booking_button .promocode_text {
  text-transform: uppercase;
  cursor: pointer;
  font-size: 10px;
  text-decoration: none;
  margin-top: 10px;

  strong {
    color: initial;
    font-size: 10px !important;
    font-weight: lighter;
    color: rgb(134, 134, 134);
  }
}

.wrapper_booking_button .promocode_input {
  font-size: 13px;
}

.room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  font-size: 10px;
  text-transform: uppercase;
  margin: 6px 0px 9px;
}

.stay_selection label {
  margin: 0px auto 11px !important;
}

.booking_form_title {
  padding-top: 9px;
  padding-bottom: 11px;
}

.room .room_title {
  top: 28px;
}

.wrapper-new-web-support {
  height: auto !important;
  font-size: 13px;
  background-color: $corporate_1 !important;
}

.wrapper-new-web-support .web_support_number {
  font-size: 15px !important;
}
.rooms_number_wrapper .selectric {
  .label {
    overflow: visible;
    &:before, &:after {
      content: "";
      display: block;
      width: 35px;
      height: 35px;
      @include center_x;
    }
    &:before {
      top: -20px;
      background: url(/static_1/images/booking/arrow_up.png) no-repeat center center !important;
    }
    &:after {
      bottom: -20px;
      background: url(/static_1/images/booking/arrow_down_small.png) no-repeat center center !important;
    }
  }
  .button {
    display: none;
  }
}

.landing_booking_popup .wrapper-new-web-support {
  display: none;
}

div#wrapper_booking.inner_widget {
  height: 180px;
  top: 145px;
  bottom: 20px;
  z-index: 1;

  .booking_widget {
    width: 927px;
    height: auto;
    @include center_x;

    #full-booking-engine-html {
      .booking_form_title {
        display: none;
      }
      .paraty-booking-form {
        box-sizing: border-box;
        padding: 20px;

        .stay_selection {
          display: inline-block;
          vertical-align: top;
          padding-top: 0;
        }

        .room_list_wrapper {
          display: inline-block;
          vertical-align: top;
          margin-top: 15px;
        }

        .wrapper_booking_button {
          display: inline-block;
          vertical-align: top;
          width: auto;
          margin-left: 15px;
          margin-top: 45px;

          .promocode_text {
            display: none;
          }

          .promocode_input {
            margin-top: 0;
            margin-left: 0;
            display: inline-block!important;
            vertical-align: middle;
          }

          .submit_button {
            display: inline-block;
            margin-top: 0;
            margin-bottom: 0;
            vertical-align: middle;
          }
        }
      }
    }
  }
}
body .datepicker_wrapper_element {
  z-index: 9000;
  .header_datepicker {
    background: $corporate_1;
  }
  .ui-datepicker-calendar {
    .ui-datepicker-current-day .ui-state-active {
      background: $corporate_1 !important;
      color: white !important;
    }
    .ui-datepicker-current-day.highlight .ui-state-active {
      background: lighten($corporate_1, 20%) !important;
      color: white !important;
    }
    .highlight .ui-state-default {
      background: lighten($corporate_1, 30%) !important;
      color: white !important;
    }
    .highlight.last-highlight-selection .ui-state-default {
      background: lighten($corporate_1, 20%) !important;
      color: white !important;
    }
  }
}

.calendar_popup_wrapper table.calendar td .day-content .price {
  display: none;
}