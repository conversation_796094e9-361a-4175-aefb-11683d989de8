# -*- coding: utf-8 -*-
import logging
import os
from random import shuffle
from flask import request

from paraty_commons_3.email_utils import sendEmail
from booking_process.constants.advance_configs_names import DEFAULT_WEB_LANGUAGE, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getLogotype
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_section_from_section_spanish_name_with_properties
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.session import session_manager
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection, BaseTemplateHandler2

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

# Change this value too in default.scss and in config.rb!!
base_web = "datar"


class TemplateHandler(BaseTemplateHandler2WithRedirection):

    def renderSectionPage(self, language, country):
        sections = self.getSections(language)
        sectionToUse = self.getCurrenSection(sections)

        advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

        if sectionToUse and sectionToUse['sectionType'] == 'Landing':

            params = self.getCommonParams(language, sections)

            params['content'] = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)

            if 'carta-magos' == advance_properties.get('template', ''):
                if advance_properties.get('button', False):
                    params['button'] = advance_properties.get('button')
                fullPath = os.path.join(os.path.dirname(__file__), "template/landing_reyes.html")
            else:

                if advance_properties.get('button_form', False):
                    params['button_form'] = advance_properties.get('button_form')

                if advance_properties.get('landing_block1', False):
                    params['landing_block1_content'] = get_section_from_section_spanish_name(
                        advance_properties.get('landing_block1'), language)
                    params['landing_block1'] = get_pictures_from_section_name(advance_properties.get('landing_block1'),
                                                                               language)

                if advance_properties.get('landing_block2', False):
                    params['landing_block2_content'] = get_section_from_section_spanish_name(
                        advance_properties.get('landing_block2'), language)
                    params['landing_block2'] = self.getPicturesProperties(language,
                                                                          advance_properties.get('landing_block2'),
                                                                          ['icon'])

                if advance_properties.get('landking_coments', False):
                    params['landing_coments'] = get_pictures_from_section_name(
                        advance_properties.get('landing_coments'), language)

                if advance_properties.get('contact_phone', False):
                    params['custom_phone'] = advance_properties['contact_phone']

                if advance_properties.get('prefix_phone', False):
                    params['prefix_phone'] = advance_properties['prefix_phone']

                fullPath = os.path.join(os.path.dirname(__file__), "template/landing.html")

            webSite = buildTemplate(fullPath, params)

            return webSite

        return super(TemplateHandler, self).renderSectionPage(language, country)

    def getAdditionalParams(self, currentSectionName, language, allSections):

        section_to_use = self.getCurrenSection(allSections)
        section_name = section_to_use['sectionName'].lower().strip() if section_to_use else None
        section_type = section_to_use['sectionType'] if section_to_use else None

        advance_properties = self.getSectionAdvanceProperties(section_to_use, language)

        additional_params = {
            # 'slider_revolution': self.getMainGallery(language),
            'texto_legal': get_section_from_section_spanish_name("texto legal", language),
            'base_web': base_web,
            'texto_popup_idioma': get_section_from_section_spanish_name("texto popup idioma", language),
            'texto_popup_login': get_section_from_section_spanish_name("login", language),
            'actual_language': language,
            'lopd': get_section_from_section_spanish_name("politica de privacidad", language),
            'aviso_legal': get_section_from_section_spanish_name("aviso legal", language),
            'aviso_cookie': get_section_from_section_spanish_name("aviso de cookies", language),
            'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
            'validation_text': get_section_from_section_spanish_name("validacion_formulario", language),
            'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=False),
            'popup_loading': get_section_from_section_spanish_name("_popup_loading", language),
            'captcha_box': get_config_property_value(PUBLIC_CAPTCHA_KEY)
        }

        bottom_popup = get_section_from_section_spanish_name("popup inicio footer", language)
        if bottom_popup:
            if self.getSectionAdvanceProperties(bottom_popup, language).get("hide form"):
                bottom_popup['hide_form'] = True

            popup_pictures = get_pictures_from_section_name("popup inicio footer", language)
            bottom_popup['popup_pictures'] = popup_pictures

            additional_params['bottom_popup'] = bottom_popup


        popup_loading_pictures = get_pictures_from_section_name("_popup_loading", language)
        if popup_loading_pictures:
            for pic in popup_loading_pictures:
                if pic.get("title") == "background":
                    additional_params['popup_loading_background'] = pic

                elif pic.get("title") == "logo":
                    additional_params['popup_loading_logo'] = pic

            if not additional_params.get("popup_loading_background"):
                additional_params['popup_loading_background'] = popup_loading_pictures[0]

        # Revolution slider overwrite
        additional_params['revolution_slider'] = self.buildRevolutionSlider(language,
                                                                            additionalParams=get_web_dictionary(
                                                                                language),
                                                                            specificTemplate='../webs/paratytech/template/slider_revolution.html')

        if advance_properties.get("bannersx3"):
            additional_params['bannersx3'] = get_pictures_from_section_name(advance_properties.get("bannersx3"),
                                                                             language)

        if section_type == "Inicio":
            additional_params['ini'] = True

            # Bannerx5
            list_params_banners = ['txt_know_more', 'link_know_more', 'link_video', 'txt_video', 'highlight',
                                   'redirection']
            bannerx5 = self.getPicturesProperties(language, advance_properties.get("bannerx5"), list_params_banners)
            for x in bannerx5:
                if x['linkUrl']:
                    x['gallery'] = self.getPicturesProperties(language, x['linkUrl'].replace(".html", ""),
                                                              list_params_banners)

            additional_params['bannerx5'] = bannerx5

            # Bannerx4
            bannerx4 = get_pictures_from_section_name(advance_properties.get("bannerx4"), language)
            additional_params['bannerx4'] = bannerx4

            # Slider with only text
            slider_text = get_pictures_from_section_name(advance_properties.get("slider_text"), language)
            additional_params['paralax'] = get_pictures_from_section_name(advance_properties.get("paralax"), language)
            additional_params['slider_text'] = slider_text

            additional_params['customers_pictures_text'] = get_section_from_section_spanish_name("nosotros_blue",
                                                                                                 language)
            additional_params['customers_pictures'] = get_pictures_from_section_name("nosotros_blue", language)

            # Banner Partner
            banner_partner = get_section_from_section_spanish_name(advance_properties.get("partners"), language)
            additional_params['banner_partner'] = banner_partner

            # Wordpress
            wordpress_block = get_section_from_section_spanish_name("wordpress_inicio", language)
            wordpress_post = self.getPicturesProperties(language, "wordpress_inicio",
                                                        ['txt_read_more', 'link_read_more'])

            additional_params['wordpress_block'] = wordpress_block
            # additional_params['wordpress_post'] = wordpress_post
            wordpress_urls = {
                'url': 'https://paratyinternational.blog/',
                'SPANISH_url': 'http://blog.paraty.es/'
            }
            additional_params['wordpress_post'] = self.get_wordpress_post(wordpress_urls, language)

            # Newsletter placeholder
            additional_params['placeholder_blog'] = get_section_from_section_spanish_name("newsletter_blog", language)
            additional_params['placeholder_web'] = get_section_from_section_spanish_name("newsletter_web",
                                                                                         language).get("subtitle")

        elif section_name == "trabaja con nosotros" or advance_properties.get("work_section"):
            additional_params['trabaja'] = True
            if advance_properties.get("button_email"):
                additional_params['button_email'] = advance_properties.get("button_email")
            else:
                additional_params['button_email'] = "<EMAIL>"
            if advance_properties.get("black_block"):
                additional_params['black_sections'] = get_pictures_from_section_name(
                    advance_properties.get("black_block"), language)
            if advance_properties.get("button_offer"):
                additional_params['button_offer'] = advance_properties.get("button_offer")

        elif section_type == "Extra 1":
            if advance_properties.get("enlaces"):
                additional_params['links_new'] = self.getPicturesProperties(language, advance_properties.get("enlaces"),
                                                                            ['link_text', 'popup_form',
                                                                             'download_button', 'video',
                                                                             'custom_iframe'])

        elif section_type == u"Localización":
            additional_params['blocks_contact'] = self.getPicturesProperties(language, "blocks_contacto", ['maps'])
            additional_params['slider_contact'] = get_pictures_from_section_name("slider_contacto", language)
            additional_params['validation_text'] = get_section_from_section_spanish_name("validacion_formulario",
                                                                                         language)

        else:
            additional_params['order'] = advance_properties.get("order")
            if advance_properties.get("link_view_web"):
                additional_params['link_view_web'] = advance_properties.get("link_view_web")
                additional_params['txt_view_web'] = advance_properties.get("txt_view_web")

            if advance_properties.get("link_video"):
                additional_params['link_video'] = advance_properties.get("link_video")
                additional_params['txt_video'] = advance_properties.get("txt_video")

            if advance_properties.get("specific_block"):
                additional_params['specific_block'] = advance_properties.get("specific_block")

            if advance_properties.get("txt_vista_general"):
                additional_params['txt_vista_general'] = advance_properties.get("txt_vista_general")

            if advance_properties.get("black_block"):
                sections = advance_properties.get("black_block").split("@@")
                black_sections = [get_section_from_section_spanish_name(x, language) for x in sections]
                for x in black_sections:
                    if self.getSectionAdvanceProperties(x, language).get("text"):
                        x['text'] = self.getSectionAdvanceProperties(x, language).get("text")
                        x['additional_text'] = get_pictures_from_section_name(x.get('title'), language)
                    else:
                        pictures_black = get_pictures_from_section_name(x.get('title'), language)
                        picture_maxi = list(filter(lambda p: p['title'] == "maxi", pictures_black))
                        picture_mini = list(filter(lambda p: p['title'] == None, pictures_black))
                        x['maxi'] = picture_maxi[0] if len(picture_maxi) > 0 else ''
                        x['mini'] = picture_mini[0] if len(picture_mini) > 0 else ''
                additional_params['black_sections'] = black_sections
                if advance_properties.get("black_block_fit"):
                    additional_params['black_sections_fit'] = True


            if advance_properties.get("mini_gallery"):
                additional_params['mini_gallery'] = get_pictures_from_section_name(
                    advance_properties.get("mini_gallery"), language)

            if advance_properties.get("our_tem"):
                additional_params['our_tem_sec'] = get_section_from_section_spanish_name(
                    advance_properties.get("our_tem"), language)
                additional_params['our_tem'] = get_pictures_from_section_name(advance_properties.get("our_tem"),
                                                                               language)
                shuffle(additional_params['our_tem'])

            if advance_properties.get("blue_block"):
                additional_params['blue_block'] = get_section_from_section_spanish_name(advance_properties.get("blue_block"), language)
                additional_params['blue_block_pictures'] = get_pictures_from_section_name(advance_properties.get("blue_block"), language)
                additional_params['blue_icons'] = self.getSectionAdvanceProperties(additional_params['blue_block'], language).get('icons', '')
                additional_params['animation_icon_disable'] = self.getSectionAdvanceProperties(additional_params['blue_block'], language).get('animation_icon_disable', '')

            if advance_properties.get("blue_block_funciones"):
                additional_params['blue_block_funciones'] = get_section_from_section_spanish_name(advance_properties.get("blue_block_funciones"), language)
                additional_params['blue_block_funciones_pictures'] = get_pictures_from_section_name(advance_properties.get("blue_block_funciones"), language)
                additional_params['blue_block_funciones_icons'] = self.getSectionAdvanceProperties(additional_params['blue_block_funciones'], language).get('icons', '')
                additional_params['animation_icon_funciones_disable'] = self.getSectionAdvanceProperties(additional_params['blue_block_funciones'], language).get(
                    'animation_icon_disable', '')

            if advance_properties.get("white_block"):
                additional_params['white_block'] = get_section_from_section_spanish_name(
                    advance_properties.get("white_block"), language)
                if self.getSectionAdvanceProperties(additional_params['white_block'], language).get("eur"):
                    additional_params['eur'] = self.getSectionAdvanceProperties(additional_params['white_block'],
                                                                                language).get("eur").split("@@")
                if self.getSectionAdvanceProperties(additional_params['white_block'], language).get("gbp"):
                    additional_params['gbp'] = self.getSectionAdvanceProperties(additional_params['white_block'],
                                                                                language).get("gbp").split("@@")
                if self.getSectionAdvanceProperties(additional_params['white_block'], language).get("usd"):
                    additional_params['usd'] = self.getSectionAdvanceProperties(additional_params['white_block'],
                                                                                language).get("usd").split("@@")

                if self.getSectionAdvanceProperties(additional_params['white_block'], language).get("gallery"):
                    white_gallery = get_pictures_from_section_name(advance_properties.get("white_block"), language)
                    additional_params['white_gallery'] = white_gallery

            if section_type == "Extra 2":
                additional_params['extra_2'] = True
                additional_params['validation_text'] = get_section_from_section_spanish_name("validacion_formulario",
                                                                                             language)
                if advance_properties.get("enlaces"):
                    additional_params['links_new'] = self.getPicturesProperties(language,
                                                                                advance_properties.get("enlaces"),
                                                                                ['links_new', 'video'])

                if advance_properties.get("input_empresa"):
                    additional_params['input_empresa'] = advance_properties.get("input_empresa")

                if advance_properties.get("thank_popup"):
                    additional_params['validation_text'] = get_section_from_section_spanish_name(
                        advance_properties.get("thank_popup"), language)

                if advance_properties.get("thank_section"):
                    additional_params['custom_message'] = advance_properties.get("thank_section")

                if advance_properties.get("acumba_api"):
                    additional_params['acumba_api'] = advance_properties.get("acumba_api")

        if section_type != "Inicio":
            additional_params['main_content'] = section_to_use
            images_section = get_pictures_from_section_name(section_name, language)
            for x in images_section:
                if x.get('title') == "header":
                    additional_params['background_header'] = x
                elif x.get('title') == "principal":
                    additional_params['principal_img'] = x['servingUrl']
                elif x.get('title') == "collage":
                    additional_params['collage'] = x['servingUrl']

            if advance_properties.get("link_view_web"):
                additional_params['link_view_web'] = advance_properties.get("link_view_web")
                additional_params['txt_view_web'] = advance_properties.get("txt_view_web")

            if advance_properties.get("link_video"):
                additional_params['link_video'] = advance_properties.get("link_video")
                additional_params['txt_video'] = advance_properties.get("txt_video")

            additional_params['floating_menu'] = advance_properties.get("floating_menu", "")

            if advance_properties.get("floating_menu_custom"):
                menu_custom_pics = self.getPicturesProperties(language, advance_properties.get("floating_menu_custom"))
                additional_params['floating_menu_custom'] = list(filter(lambda p: p['altText'] == "link", menu_custom_pics))
                additional_params['floating_menu_custom_logo'] = list(filter(lambda p: p['title'] == "logo", menu_custom_pics))


        if 'suscripcion' in section_name:

            if "fitur" in section_name:
                additional_params['feria'] = True

            additional_params['suscripcion'] = True
            additional_params['suscripcion_map'] = get_section_from_section_spanish_name("Suscripcion google maps",
                                                                                         language)

        # Footer
        footer_elements = get_pictures_from_section_name("secciones_footer", language)

        additional_params['footer_elements'] = footer_elements

        if advance_properties.get("no_index") or section_to_use.get('private'):
            additional_params['no_index'] = True

        self.process_advance_properties(additional_params, advance_properties, language)

        return additional_params

    def process_advance_properties(self, additional_params, advance_properties, language):
        # Download files form
        if advance_properties.get('download_form'):
            additional_params['form_file_download'] = advance_properties['download_form']
            if advance_properties.get('download_button'):
                additional_params['download_button'] = advance_properties['download_button']

        if advance_properties.get("newsletter_block"):
            additional_params['newsletter_block'] = advance_properties.get("newsletter_block")
            additional_params['newsletter_button'] = advance_properties.get("newsletter_button")
            if advance_properties.get("clients_api"):
                additional_params['clients_api'] = advance_properties.get("clients_api")

            if advance_properties.get("no_name"):
                additional_params['no_name'] = advance_properties.get("no_name")

    def _get_mobile_template_url(self, section=None):

        return os.path.join(os.path.dirname(__file__), "template/index.html")

    def getTemplateUrl(self, section=None):
        return thisUrl

    def get_revolution_initial_height(self):
        return "480"

    def get_revolution_full_screen(self):
        return "on"

class thanksEmailHandler(BaseTemplateHandler2):
    def get(self, *args):
        client_emil = request.values.get('email')
        client_name = request.values.get('name')
        custom_message = request.values.get('custom_message')
        language_sended = request.values.get('language')

        if not client_emil:
            return

        context = {}
        context['logotype'] = getLogotype()
        self.setLanguageInSession()

        # If path contains specific language, we use it
        if not session_manager.get('language'):

            # by default in SPANISH, Unless indicated otherwise (advantage configuration)
            default_language_in_manager = get_config_property_value(DEFAULT_WEB_LANGUAGE)
            if default_language_in_manager:
                logging.info("Setting explicit language from manager: %s", default_language_in_manager)
                session_manager.set('language', default_language_in_manager)
            elif language_sended:
                logging.info("Setting explicit language from AJAX: %s", language_sended)
                session_manager.set('language', language_sended)
            else:
                session_manager.set('language', SPANISH)
            self.setLanguageInSession()

        language = session_manager.get('language')
        if language_sended:
            language = language_sended

        if custom_message:
            context['thanks_email_section'] = get_section_from_section_spanish_name_with_properties(custom_message, language)
        else:
            context['thanks_email_section'] = get_section_from_section_spanish_name_with_properties('thanks email', language)

        if context['thanks_email_section']:
            context['thanks_email_section']['subtitle'] = context['thanks_email_section']['subtitle'].replace(
                "@@name@@", client_name)

        context = dict(list(context.items()) + list(get_web_dictionary(language).items()))

        subject = context['T_thanks']
        if context['thanks_email_section'].get('subject'):
            subject = unescape(context['thanks_email_section']['subject'])

        fullPath = os.path.join(os.path.dirname(__file__), "template/email/thanks_email.html")
        template_rendered = buildTemplate(fullPath, context)

        sendEmail(client_emil, subject, template_rendered, template_rendered)

        return
# return self.response.out.write(template_rendered)
