<div class="news_sections_wrapper">
    <div class="container12">
        {% for x in links_new %}
            <div class="news_element col-2" num_id="{{ forloop.counter }}">
                {% if not x.servingUrl %}
                    <div class="icon_new">
                        <img src="/img/datar/icono-seccion-prensa.png" alt=""/>
                    </div>
                {% else %}
                    <div class="image_new">
                        <img src="{{ x.servingUrl|safe }}=s1900" alt="{{ x.altText }}"/>
                    </div>
                {% endif %}
                <div class="new_text {% if x.servingUrl %}cover{% endif %}">
                    {% if x.title %}
                        <div class="date_text">{{ x.title|safe }}</div>
                    {% endif %}
                    <div class="new_description">{{ x.description|safe }}</div>
                    {% if x.linkUrl %}
                        <div class="link">
                            {% if x.popup_form %}
                                <a href="#popup_form_new_{{ forloop.counter }}"
                                   class="popup_form_new_link">{{ x.link_text|safe }}</a>
                                <div id="popup_form_new_{{ forloop.counter }}" style="display: none" num_elem="{{ forloop.counter }}">
                                    <div class="form_contact_wrapper file_download_form">
                                        <form name="contact" class="contact_file_download" method="post"
                                              action="/utils/?action=contact">
                                            <input type="hidden" name="section" id="section-name"
                                                   value="{{ sectionName|safe }}"/>

                                            <div class="row form" style="padding-top:0;padding-bottom:0;">
                                                <div class="box">
                                                    <input class="name form-control contacto-form" id="name" name="name"
                                                           type="text" placeholder="{{ T_nombre_y_apellidos }}">
                                                    <input class="mail form-control contacto-form" id="email"
                                                           name="email" type="text" placeholder="{{ T_email }}">
                                                    <input class="business form-control contacto-form" id="business"
                                                           name="business" type="text" placeholder="{{ T_empresa }}">
                                                </div>

                                                <div class="lopd_button">
                                                    <input type="checkbox" name="privacy" value="privacy" id="privacy"/>
                                                    <a href="#lopd_content">{{ lopd.subtitle|safe }}</a>
                                                </div>

                                                <div id="lopd_content"
                                                     style="display: none">{{ lopd.content|safe }}</div>

                                                <div style="float:right" id="contact-button-wrapper">
                                                    <div id="file-contact-button" data-file="{{ x.linkUrl|safe }}"
                                                         style="width:auto;padding-top:8px;padding-bottom:8px;padding-left:10px; padding-right:10px;background: white; cursor: pointer">
                                                        {% if x.download_button %}{{ x.download_button|safe }}{% else %}
                                                            {{ T_enviar }}{% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            {% else %}
                                <a href="{{ x.linkUrl|safe }}" target="_blank">{{ x.link_text|safe }}</a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if x.video %}
                <div style="display: none;" class="hidden_see_more_info" id="hidden_num_{{ forloop.counter }}">
                    <video width="800" controls>
                        <source src="{{ x.video|safe }}">
                    </video>
                    <div class="buttons_wrapper">
                        <a href="{{ x.video }}" target="_blank" class="button_element">{{ T_abrir_nueva_pestana }}</a>
                    </div>
                </div>
            {% endif %}
            
            {% if x.custom_iframe %}
                <div style="display: none;" class="hidden_see_more_info" id="hidden_num_{{ forloop.counter }}">
                    <iframe width="860" height="500" src="{{ x.custom_iframe|safe }}"></iframe>
                </div>
            {% endif %}

        {% endfor %}
    </div>
</div>


<script>
    $(".news_element").each(function(){
        var data_file = $(this).find("#file-contact-button");
        $(data_file).click(function(){
            var element_number = $(this).closest(".file_download_form").parent().attr('num_elem');
            perform_file_download_send("/utils/?action=promopopup&language=" + $("input[name='actual_language']").val(), data_file.attr("data-file"), element_number);
        });
    });

    function perform_file_download_send(url_target, link_file, element_number) {
        if ($("#popup_form_new_" + element_number).find(".contact_file_download").valid()) {
            $.post(url_target,
                   {
                       'name': $(".fancybox-form-new #name").val(),
                       'surname': $(".fancybox-form-new #surname").val(),
                       'company': $(".fancybox-form-new #business").val(),
                       'email': $(".fancybox-form-new #email").val(),
                       'list_id': '{% if actual_language == 'SPANISH' %}143915{% else %}143911{% endif %}'
                   },

                   function (data) {
                       _send_thanks_email($(".fancybox-form-new #name").val(), $(".fancybox-form-new #email").val());
                       $(".fancybox-form-new #name").val("");
                       $(".fancybox-form-new #surname").val("");
                       $(".fancybox-form-new #business").val("");
                       $(".fancybox-form-new #email").val("");

                       if (!(link_file.indexOf('.pdf') > -1)) {
                           $.fancybox($("#hidden_num_" + element_number), {
                               'wrapCSS': 'hidden_download_popup'
                           });
                       } else {
                           download_file('/file-download?file=' + link_file, 'data');
                       }

                       _send_event_ping('Newsletter', 'Suscribe', 'Ebook Agosto 2017');

                       alert("{{ T_thanks }}");
                   }
            );
        }
    }

    function _send_thanks_email(name, email) {
        $.post('/thanks-email-user', {'name': name, 'email': email});
    }
</script>