<div class="bannerx3_wrapper">
    {% for banner in bannersx3 %}
        <a class="bannerx3" {% if banner.linkUrl %} href="{{ banner.linkUrl|safe }}" {% endif %}>
            {% if banner.servingUrl %}
                <img src="{{ banner.servingUrl }}=s700">
            {% endif %}
            {% if banner.title %}<span class="title">{{ banner.title|safe }}</span>{% endif %}
        </a>
    {% endfor %}
</div>

<script>
    $(window).load(function () {
            $(".bannerx3_wrapper .bannerx3").each(function (index) {
                $(this).css({"opacity": "1","animation-delay": index * 0.7 + 1.5 + "s","animation-duration": 1 + "s"}).addClass("animated").addClass("fadeInUpBig");
            })
        }
    );
</script>