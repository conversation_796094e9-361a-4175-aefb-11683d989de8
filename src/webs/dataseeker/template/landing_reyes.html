{%spaceless%}
<!DOCTYPE html>
<html lang="{{ language_code }}">
<head>
    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
        {{ sectionName|safe }} {% else %}  {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    <link rel="icon" href="/static_1/images/favicon.ico?v=1" type="image/x-icon">
    <link href="https://fonts.googleapis.com/css?family=Lobster+Two" rel="stylesheet">

    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    {% if no_index %}
        <meta name="robots" content="noindex,follow"/>
    {% else %}
        <meta name="robots" content="index,follow"/>
    {% endif %}
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="dc.title"
          content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
              {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description"
          content="{% if sectionName %} {{ sectionName|safe }} - {% endif %}{{ description|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>

    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="theme-color" content="#446ca9">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="google-site-verification" content="HqkoUPsBetQJ-gL3pZcsFoVTnQ7xHA2wBD2R9JnveZQ"/>

    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/css/datar/styles_landing_reyes.css?v=1.1"/>
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window,document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
     fbq('init', '1487324217988751');
    fbq('track', 'PageView');
    </script>
    <noscript>
     <img height="1" width="1"
    src="https://www.facebook.com/tr?id=1487324217988751&ev=PageView
    &noscript=1"/>
    </noscript>
    <!-- End Facebook Pixel Code -->
</head>
<body class="{{ language }}">
<section id="background_fixed" style="background-image: url('{{content.pictures.0|safe}}=s1900');"></section>

<section class="main container12">
    <div id="logo">
        <img src="/img/datar/logo-reyes-reservas.png" alt=""/>
    </div>
    <div class="title">{{content.subtitle|safe}}</div>
    <div class="description">{{content.content|safe}}</div>
    <div class="carta">
        {% include "_contact_form_landing_reyes.html" %}
    </div>
    <div id="contact-button-wrapper">
    <a>
        <div id="contact-button">
            {% if button %}{{button|safe}}{% else %}{{ T_enviar }}{%endif%}
        </div>
    </a>
</div>
</section>

<footer>
    <div class="container12">
        <div id="logo">
            <img src="{{ logotype }}" alt=""/>
        </div>
        <div class="phone"><a href="tel:+34952230887"><i class="fa fa-headphones"></i>(+34) <strong>952 230 887</strong></a></div>
        <div class="email"><a href="mailto:<EMAIL>"><i class="fa fa-envelope"></i><EMAIL></strong></a></div>
    </div>
</footer>

    <!--[if lt IE 7]>
	<script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script>
    <![endif]-->
    <!--[if lte IE 8]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <!-- jquery -->
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script async src="/static_1/lib/owlcarousel/owl.carousel.min.js?v=1"></script>

    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>


{% if google_analytics_id %}
    <!-- Google Analytics -->
    <script>
        (function (i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function () {
                        (i[r].q = i[r].q || []).push(arguments)
                    }, i[r].l = 1 * new Date();
            a = s.createElement(o),
                    m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

        ga('create', '{{ google_analytics_id }}', 'auto');
        ga('send', 'pageview');

    </script>
    <!-- End Google Analytics -->
{% endif %}

<script type="text/javascript">
$(window).load(function () {
    owl_params1_block1 = {
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".landing_block1_logos").owlCarousel(owl_params1_block1);
    owl_params2_block1 = {
        loop: false,
        nav: false,
        dots: true,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".landing_block1_videos").owlCarousel(owl_params2_block1);
    owl_params2_block1 = {
        loop: true,
        nav: false,
        dots: true,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false,
        dotsData: true
    };
    var owl_carousel = $(".landing_comments_wrapper .comments-slider").owlCarousel(owl_params2_block1);

    //lazy load
    $("iframe, img").each(function () {
       $(this).attr("src",$(this).attr("data-src"));
    });

    jQuery.validator.addMethod("phone", function (phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");

    $("#contact").validate({
        rules: {
            name: "required",
            empresa: "required",
            email: {
                required: true,
                email: true
            },
            telephone: {
                required: true,
                phone: true
            },
            comments: "required"
        },

        messages: {
            name: "{{ T_campo_obligatorio}}",
            empresa: "{{ T_campo_obligatorio}}",
            {% if privacy_checkbox %}privacy: "{{ T_campo_obligatorio }}",{% endif %}
            email: {
                required: "{{ T_campo_obligatorio|safe }}",
                email: "{{ T_campo_valor_invalido|safe }}"
            },
            telephone: {
                required: "{{ T_campo_obligatorio|safe }}",
                phone: "{{ T_campo_valor_invalido|safe }}"
            },
            comments: "{{ T_campo_obligatorio|safe }}"
        }
    });

    if (typeof neverDeclared == "undefined") {

        $("#contact-button").click(function () {

            if ($("#contact").valid()) {
                if (!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
                    $.post(
                            "/utils/?action=contact",
                            {
                                'emailSubject': 'Carta a los reyes de las reservas',
                                'name': $("#name").val(),
                                'check_date': $("#contact_booking_start").val(),
                                'end_date': $("#contact_booking_end").val(),
                                'telephone': $("#telephone").val(),
                                'email': $("#email").val(),
                                'comments': $("#comments").val()
                            },

                            function (data) {
                                alert("{{ T_gracias_contacto }}");
                                $("#name").val("");
                                $("#telephone").val("");
                                $("#email").val("");
                                $("#emailConfirmation").val("");
                                $("#comments").val("");
                                register_adwords_conversion();
                                register_analytics_event();
                            }
                    );
                }
            }
        });
    }
    neverDeclared = true;
});

function register_adwords_conversion(){
    try{
        goog_report_conversion()
    }catch(e){
        console.log("Something was wrong trying to submit the conversion to adwords")
    }

}

function register_analytics_event(){
    ga('send', 'event', "Contact", "Send Form", "Contact us section");
}
</script>

{% if adwords_id %}
<script type="text/javascript">
    /* <![CDATA[ */
    goog_snippet_vars = function () {
        var w = window;
        w.google_conversion_id = {{ adwords_id.0 }};
        w.google_conversion_label = "{{ adwords_id.1 }}";
        w.google_remarketing_only = false;
    }

    goog_report_conversion = function (url) {
        goog_snippet_vars();
        window.google_conversion_format = "3";
        var opt = new Object();
        opt.onload_callback = function () {
            if (typeof(url) != 'undefined') {
                window.location = url;
            }
        }
        var conv_handler = window['google_trackConversion'];
        if (typeof(conv_handler) == 'function') {
            conv_handler(opt);
        }
    }
    /* ]]> */
</script>
<script type="text/javascript"
        src="//www.googleadservices.com/pagead/conversion_async.js">
</script>
{% endif %}
</body>
</html>
{%endspaceless%}