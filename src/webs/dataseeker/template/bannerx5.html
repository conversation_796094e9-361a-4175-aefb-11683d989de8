{% if bannerx5 %}
    <div class="bannerx5_wrapper container12">
        {% for x in bannerx5 %}
            <div class="bannerx5_element {% if forloop.counter < 3 %}col-2 banner-{% cycle "left" "right" %}{% else %}col-3 banner-{% cycle "left" "mid" "right" %}{% endif %}">
                {% if x.gallery and not x.redirection %}
                    <div class="bannerx5_gallery">
                        <ul class="slides">
                            {% for y in x.gallery %}
                                <li class="gallery_element">
                                    <div class="element_text">
                                        <div class="title">{{ y.title|safe }}</div>
                                        <div class="description">{{ y.description|safe }}</div>
                                    </div>
                                    <div class="element_img">
                                        <a {% if y.linkUrl %}href="{{ host|safe }}/{{ seoLinkString }}{{ y.linkUrl|safe }}"{% endif %}>
                                            <img data-src="{{ y.servingUrl|safe }}=s800" alt="{{ y.altText }}"/>
                                        </a>

                                        <div class="links">
                                            {% if y.highlight %}
                                                <div class="highlight">{{ y.highlight|safe }}</div>
                                            {% endif %}
                                            {% if y.txt_know_more %}
                                                <a href="{{ y.link_know_more|safe }}" class="know_more">{{ y.txt_know_more|safe }}</a>
                                            {% endif %}
                                            {% if y.link_video %}
                                                <a class="link_video" href="#hidden_video_{{ forloop.counter }}">{{ y.txt_video|safe }}</a>
                                                <div id="hidden_video_{{ forloop.counter }}" style="display: none">
                                                    <iframe width="560" height="315" src="{{ y.link_video|safe }}" frameborder="0" allowfullscreen></iframe>
                                                </div>
                                            {% endif %}
                                        </div>
                                     </div>
                                </li>
                            {% endfor %}
                            
                        </ul>
                    </div>
                {% else %}
                    <div class="bannerx5_text">
                        <div class="title">{{ x.title|safe }}</div>
                        <div class="description">{{ x.description|safe }}</div>
                    </div>
                    <div class="bannerx5_img">
                        <a {% if x.linkUrl %}href="{{ host|safe }}/{{ seoLinkString }}{{ x.linkUrl|safe }}"{% endif %}>
                            <img data-src="{{ x.servingUrl|safe }}=s1900" alt="{{ x.altText }}"/>
                        </a>
                        <div class="links">
                            {% if x.highlight %}
                                <div class="highlight">{{ x.highlight|safe }}</div>
                            {% endif %}
                            {% if x.txt_know_more %}
                                <a href="{{ x.link_know_more|safe }}" class="know_more">{{ x.txt_know_more|safe }}</a>
                            {% endif %}
                            {% if x.link_video %}
                                <a class="link_video" href="#hidden_video_{{ forloop.counter }}">{{ x.txt_video|safe }}</a>
                                <div id="hidden_video_{{ forloop.counter }}" style="display: none">
                                    <iframe width="560" height="315" src="{{ x.link_video|safe }}?showinfo=0" frameborder="0" allowfullscreen></iframe>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        {% endfor %}

    </div>

    <script>
        $(window).load(function () {
            function bannerx5_fx() {
                $(".bannerx5_wrapper .bannerx5_element").addnimation({parent:$(".bannerx5_wrapper"),class:"zoomInUp", classOut:"zoomOutDown"});
            }
            bannerx5_fx();
            $(window).scroll(bannerx5_fx);
        });
    </script>
{% endif %}