.our_team {
  margin-top: 80px;
  margin-bottom: 70px;
  padding: 0 5px;
  h3 {
    color: $corporate-4;
    font-size: 52px;
    font-weight: 100;
    line-height: 56px;
    margin-bottom: 70px;
    text-align: center;
  }
  .team_mate {
    background-color: #000;
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: calc((100% - 30px) / 3);
    height: 250px;
    margin: 0 5px 6px;
    .pic {
      @include full_size;
      @include transition(all, 2s);
      img {
        @include center-xy;
        max-height: 100%;
        max-width: 100%;
        &.background_blur {
          max-height: none;
          max-width: none;
          min-height: 100%;
          min-width: 100%;
          -webkit-filter: grayscale(30%) blur(5px);
          filter: grayscale(30%) blur(5px);
        }
      }
    }
    .card_front {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(rgba(black,0), rgba(black,0.8), rgba(black,0.8));
      padding: 20px;
      color: white;
      font-weight: lighter;
      transform: rotateY(0deg);
      @include transition(all, .6s);
      i.fa {
        position: absolute;
        right: 20px;
        bottom: 20px;
        color: $corporate_4;
      }
    }
    .card_back {
      opacity: 0;
      @include full_size;
      background: linear-gradient(rgba(black,0), rgba(black,0.8), rgba(black,0.8));
      padding: 20px;
      color: white;
      font-weight: lighter;
      transform: rotateY(180deg);
      @include transition(all, .6s);
      .slogan {
        font-size: 30px;
        font-weight: 100;
        line-height: 35px;
        margin-bottom: 30px;
        text-align: center;
        i.fa {
          color: $corporate_4;
          margin: 0 15px;
        }
      }
      .title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        color: white;
        font-weight: lighter;
        span {
          color: $corporate_1;
        }
        i.fa {
          position: absolute;
          right: 20px;
          bottom: 20px;
          color: $corporate_4;
        }
      }
    }
    &:hover {
      .pic {
        opacity: .3;
        transform: scale(1.2,1.2);
      }
      .card_front {
        opacity: 0;
        transform: rotateY(180deg);
      }
      .card_back {
        opacity: 1;
        transform: rotateY(0deg);
      }
    }
  }
}

@media (max-width: 1140px) {
  .container12 {
    max-width: 100%;
  }
}
@media (max-width: 1000px) {
  .our_team .team_mate {
    width: calc((100% - 20px) / 2);
  }
}
@media (max-width: 700px) {
  .our_team .team_mate {
    display: block;
    width: 370px;
    margin: 0 auto 10px;
  }
}