{% extends "__base.html" %}

{% block content %}
    <!--Header-->
    {% include "header.html" %}

    {% if ini %}
        {% block "slider" %}

            <section id="slider_container">
                {{ revolution_slider|safe }}
                <!--<div class="icon_down"></div>-->
            </section>

        {% endblock %}
    {% endif %}

    {% if bannersx3 %}{% include "_bannersx3.html" %}{% endif %}

    {% if ini %}

        {% include "bannerx5.html" %}

        {% if bannerx4 %}
            <div class="bannerx4_wrapper">
                <div class="container12">
                    {% for x in bannerx4 %}
                        <div class="bannerx4_element col-md-3">
                            <div class="bannerx4_img">
                                <img data-src="{{ x.servingUrl|safe }}=s800" alt="{{ x.altText }}"/>
                            </div>
                            <div class="bannerx4_title">
                                <span>{{ x.title|safe }}</span>
                            </div>
                            <div class="bannerx4_description">
                                <span>{{ x.description|safe }}</span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <script>
                $(window).load(function () {
                    function bannerx4_fx() {
                        for (x = 1; x <= {{ bannerx4|length }}; x++) {
                            $(".bannerx4_wrapper .bannerx4_element:nth-child(" + x + ")").addnimation({parent:$(".bannerx4_wrapper"), class:"fadeInUpBig", delay: x * 200});
                        }
                    }
                    bannerx4_fx();
                    $(window).scroll(bannerx4_fx);
                });
            </script>
        {% endif %}

        {% if slider_text %}
            <div class="slider_text_wrapper"
                 style="background: url('{{ paralax.0.servingUrl|safe }}=s1900');background-attachment: fixed !important; position: relative;background-size: cover;">
                <div class="icon_block">
                    <i class="fa fa-quote-left"></i>
                </div>
                <div class="slider_text_content">
                    <ul class="slides">
                        {% for x in slider_text %}
                            <li class="slider_text_element">
                                <div class="slider_text_title">{{ x.title|safe }}</div>
                                <div class="slider_text_description">{{ x.description|safe }}</div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="customers_line_wrappers">
                {% if customers_pictures_text and customers_pictures_text.subtitle %}<h2 class="customers_line_title">{{ customers_pictures_text.subtitle|safe }}</h2>{% endif %}
                {% if customers_pictures %}<div class="customers_line owl-carousel">
                    {% for x in customers_pictures %}
                        <div class="images">
                            <img src="{{ x.servingUrl|safe }}=s1900" alt="{{ x.altText }}"/>
                        </div>
                    {% endfor %}
                </div>
                <script>
                $(window).load(function () {
                    $(".customers_line").owlCarousel({
                        loop: true,
                        nav: false,
                        dots: true,
                        items: 3,
                        slideBy: 3,
                        margin: 5,
                        autoplay: true,
                        responsive : {
                            0 : {
                                items : 1,
                                slideBy : 1,
                                margin: 0
                            },
                            480 : {
                                items : 3,
                                slideBy : 3,
                                margin: 5
                            },
                        }
                    });
                });
                </script>
                {% endif %}
                </div>
            </div>
        {% endif %}

        {% include "banner_partner.html" %}

        {% include "wordpress.html" %}

        {% if newsletter %}{{ newsletter|safe }}{% endif %}

    {% else %}

        {% include "submenu_flotante.html" %}
        {% include "submenu_flotante_custom.html" %}

        <div class="main_content_wrapper" id="vista_general" style="background: #fafafa">
            <div class="container12">
                <div class="main_content_title">{{ main_content.subtitle|safe }}</div>
                <div class="main_content_description">{{ main_content.content|safe }}</div>
                {% if link_view_web  or link_video %}
                    <div class="links">
                        {% if link_view_web %}
                            <a href="{{ link_view_web|safe }}" class="know_more">{{ txt_view_web|safe }}</a>
                        {% endif %}
                        {#                {% if x.link_know_more %}#}
                        {#                    <a href="{{ link_know_more|safe }}" class="know_more">{{ txt_know_more|safe }}</a>#}
                        {#                {% endif %}#}
                        {% if link_video %}
                            <a class="link_video" href="#hidden_video_{{ forloop.counter }}">{{ txt_video|safe }}</a>
                            <div id="hidden_video_{{ forloop.counter }}" style="display: none">
                                <iframe width="560" height="315" src="{{ link_video|safe }}?showinfo=0" frameborder="0"
                                        allowfullscreen></iframe>
                            </div>
                        {% endif %}

                    </div>
                {% endif %}
                {% if newsletter_block %}
                    <div class="newsletter_additional_wrapper clearfix">
                        <form class="newsletter_additional_form" id="newsletter_additional_form" action="/utils?action=promopopup" method="post">
                            <input type="hidden" value="{{ actual_language }}" class="language">
                            {% if clients_api_secure %}
                                <input type="hidden" value="{{ clients_api_secure|safe }}" class="custom_api_secure">
                            {% endif %}

                            <div class="input_wrapper">
                                <input type="email" name="email_additional" id="email_additional" placeholder="{{ T_email }}" required>
                            </div>

                            {% if not no_name %}
                                <div class="input_wrapper">
                                <input type="text" name="name_additional" id="name_additional"
                                       placeholder="{{ T_nombre }}" required>
                                </div>
                                <div class="input_wrapper">
                                    <input type="text" name="surname_additional" id="surname_additional"
                                           placeholder="{{ T_apellidos }}" required>
                                </div>
                            {% endif %}

                            <input type="submit" id="send_newsletter_additional" value="{% if newsletter_button %}{{ newsletter_button|safe }}{% else %}{{ T_enviar }}{% endif %}">

                            <div class="popup_policy_newsletter">
                                <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy" required/>
                                <a href="/{{language}}/?sectionContent=politica-de-privacidad.html" class="myFancyPopup fancybox.iframe">{{T_lopd}}</a>
                            </div>

                            <div class="popup_policy_newsletter">
                                <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="promotions" required/>
                                <a><label for="promotions">{{T_acepto_promociones}}</label></a>
                            </div>
                        </form>
                    </div>

                    <script>
                        $(window).load(function () {
                            $("#newsletter_additional_form").validate({
                                messages: {
                                    name_additional: "{{ T_campo_obligatorio}}",
                                    surname_additional: "{{ T_campo_obligatorio}}",
                                    email_additional: {
                                        required: "{{ T_campo_obligatorio|safe }}",
                                        email: "{{ T_campo_valor_invalido|safe }}"
                                    }
                                },
                                submitHandler: function (form) {
                                    var form_newsletter = $("#newsletter_additional_form");
                                    var data = {
                                        "email": form_newsletter.find("#email_additional").val(),
                                    {% if not no_name %}
                                        "name": form_newsletter.find("#name_additional").val(),
                                        "surname": form_newsletter.find("#surname_additional").val(),
                                    {% endif %}
                                        "language": form_newsletter.find(".language").val()
                                    };
                                    if (form_newsletter.find(".custom_api_secure").length) {
                                        data['custom_api_secure'] = form_newsletter.find(".custom_api_secure").val();
                                    }
                                    $.post($(form).attr("action"), data, function () {
                                        alert("{{ T_gracias_newsletter }}");
                                        form.reset();
                                        //window.location.href = $("#submit_newsletter").attr("href");
                                    })
                                }
                            });


                        });
                    </script>
                {% endif %}
                {% if principal_img %}
                    <div class="main_content_img">
                        <img data-src="{{ principal_img|safe }}=s800" alt="{{ principal_img.altText }}"/>
                    </div>
                {% else %}
                    <div class="empty_block"></div>
                {% endif %}
            </div>
        </div>

        {% if form_file_download %}
            {% include "_form_file_download.html" %}
        {% endif %}


        {% if suscripcion %}
            {% include "suscripcion_form_contact.html" %}
        {% endif %}

        {% include "blue_sections_funcione.html" %}

        {% if order != "blue-black" %}

            {% include "black_sections.html" %}

            {% if extra_2 and links_new %}
                {% include "news.html" %}
            {% endif %}

            {% if mini_gallery %}
                <div class="mini_gallery_wrapper">
                    {% for x in mini_gallery %}
                        <div class="mini_gallery_element">
                            <img data-src="{{ x.servingUrl|safe }}=s1900"/>
                        </div>
                    {% endfor %}

                </div>
            {% endif %}

            {% include "blue_sections.html" %}

        {% else %}

            {% include "blue_sections.html" %}

            {% if mini_gallery %}
                <div class="mini_gallery_wrapper">
                    {% for x in mini_gallery %}
                        <div class="mini_gallery_element">
                            <img data-src="{{ x.servingUrl|safe }}=s1900"/>
                        </div>
                    {% endfor %}

                </div>
            {% endif %}

            {% include "black_sections.html" %}

        {% endif %}

        {% if suscripcion %}
            {% include "suscripcion_map.html" %}
        {% endif %}

        {% if links_new and not extra_2 %}
            {% include "news.html" %}
        {% endif %}

        {% if blocks_contact and slider_contact %}
            {% include "contact.html" %}
        {% endif %}

        {% include "white_sections.html" %}

    {% endif %}

    {% include "_our_team.html" %}

    {% include "form_contact.html" %}

    {% include "footer.html" %}

{% endblock %}
