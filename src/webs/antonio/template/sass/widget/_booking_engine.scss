#full_wrapper_booking {
  position: absolute;
  padding: 0;
  width: 1140px;
  min-width: 1140px;
  background: white;
  margin: 0 auto -50px;
  box-shadow: 0 0 15px rgba(0,0,0,.3);
  z-index: 1000;
  left: 0;
  right: 0;
  bottom: 50px;

  #menu_controller {
    display: none;
  }

  /*======== Booking Widget =======*/

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    display: table;
    width: 100%;
    margin: auto !important;
    position: relative;
    background: white;
    height: 130px;

    .promocode_header {
      display: none;
    }

    .nights_number_wrapper_personalized {
      display: none;
      background: $corporate_1;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    background: $corporate_1;
    position: relative;
    width: 100%;
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: black;
  }
  .dates_selector_personalized {
    margin: 0;
    padding-top: 20px;
  }
  .start_end_date_wrapper {
    height: auto;
    padding: 0;
    background: none;
    width: auto;
    color: white;
    font-size:0;
    .start_date_personalized, .end_date_personalized {
      font-size: 14pt;
      font-weight: lighter;
      position: relative;
      padding: 10px 60px 0 17px;
      height: 100px;
      display:inline-block;
      div {
        display: inline-block;
        padding: 0 3px;
      }
      .day {
        font-size: 48pt;
        font-weight: normal;
      }
      &:after {
        content:'\f107';
        display: inline-block;
        font-family: "fontawesome", sans-serif;
        color: lightgrey;
        position: absolute;bottom: 15px;right:15px;
      }
    }
  }

  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector,
  .room_list_wrapper .children_selector,
  .room_list_wrapper .babies_selector {
    width: 50% !important;
    height: auto;
    float: left;
    box-sizing: border-box;
  }
  .room_list_wrapper .adults_selector {
    width: 40% !important;
  }
  .room_list_wrapper .children_selector {
    width: 60% !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: #7fcfeb;
    color: white;
    opacity: 1;
    margin-top: 0;
    font-size: 13px !important;
    display: inline-block;
    position:  absolute;
    text-align: center;
    width: 30%;
    left: 400px;
    height: 50px;
    padding-top: 9px;
    padding-left: 40px;

    &:before {
        content: "\e90b";
        font-family: "icomoon", sans-serif;
        font-size: 35px;
        color: white;
        position: absolute;
        left: 30px;
        top: 6px;
    }

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;
    background: transparent;


    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {
    background: transparent;

  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 190px;
    height: 83px;
    margin-top: 20px;
    margin-right: 0;
    border-left: 1px solid lightgrey;
    border-right: 1px solid lightgrey;
    background: transparent;
    position: relative;

    .rooms_number {
      height: 100px;
      padding: 10px 15px 20px;
      box-sizing: border-box;
      background-position-y: 40%;
      .label {
        color: white;
        font-family: "Montserrat", serif;
        font-size: 48pt;
        line-height: 48pt;
      }
      .button {
        display: none;
      }
      .selectricItems {
        overflow: inherit !important;
        box-shadow: 0 0 30px rgba(0,0,0,.3);
        margin-top: -7px;
        border-width: 0;
        border-top:2px solid $corporate_1;
        li {
          border-width: 0;
          background: white;
          &:hover {
            background-color: $corporate_2;
            color:white;
          }
          &.selected {
            background-color: $corporate_1;
            color:white;
          }
        }
      }
    }

    &:after {
      content:'\f107';
      display: inline-block;
      font-family: "fontawesome", sans-serif;
      color: lightgrey;
      position: absolute;bottom: -5px;right:15px;
    }
  }

  .room_list_wrapper {
    display: none;
    vertical-align: top;
    float: left;
    width: 230px;
    position: absolute;
    left: 680px;
    top: 122px;
    padding: 10px;
    box-shadow: 0 0 30px rgba(0,0,0,0.3);
    border-top:2px solid $corporate_1;
    background-color: white;
    z-index: 1;

    .selectricItems {
      width: 105px !important;
    }

    .room {
      position: relative;
      background: white;
      margin-bottom: 5px;
      height: 45px;

      &.room1, &.room2, &.room3 {
         overflow:visible !important;
        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 45px;
          border: 1px solid lightgrey;
        }
        .adults_selector {
          border-right-width: 0;
        }
        .babies_selector {
          border-left-width: 0;
        }
        .remove_room_element {
          @include center_y;
          right: 5px;
          border-radius: 50%;
          width: 15px;
          height: 15px;
          &:before{
            @include center_xy;
            content: '\f00d';
            color:lightgrey;font-size: 10px;
            font-family: "fontawesome", sans-serif;
          }
        }

        .selectric {
          height: 20px;

          .label {
            line-height: 20px;
          }

          .button {
            margin-top: 0;
            display: none;
          }
        }
      }

      &.room3, &.room2 {
        border-bottom-width: 0;
        border-top-width: 0;
        height: 35px;

        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 35px;
        }
      }

      &.room3 {
        border-top: 0;
      }
    }

      .buttons_container_guests {
        margin-top: 10px;
        padding-top: 10px;
        border-top:1px solid lightgrey;
        .close_guesst_button, .save_guest_button {
          display: inline-block;
          position: relative;
          padding: 10px;
          cursor: pointer;
          color:white;
          font-size: 10px;
          text-transform: uppercase;
          text-align: center;
          width: 50%;
          background-color: $corporate_1;
          span {
            position: relative;
            z-index: 2;
          }
          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background: #2D9E48;
            width: 0;
            z-index: 1;
            @include transition(width, .4s);
          }

          &:hover:before {
            width: 100%;
          }
        }
        .close_guesst_button {
          background-color: $corporate_2;
          &:before {
            background: #E5392E;
          }
        }
      }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: right;
    height: 47px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: block;
      vertical-align: top;
      width: 230px;
      height: auto;
      background: #efefef;white-space: nowrap;
      border-top-width: 0;
      position: relative;
      text-transform: uppercase;
      padding: 16px 15px 16px;
    }

    .submit_button {
      width: 230px;
      height: 58px;
      display: inline-block;
      vertical-align: top;
      float: left;
      color: white;
      font-size: 18pt;
      background: $corporate_2;
      font-weight: 300;
      letter-spacing: 2px;
      position: relative;
      padding:16px 10px 15px;
      @include transition(border-radius, .6s);

      &:hover:before {
        width: 100%;
      }

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        background: darken($corporate_1, 10%);
        width: 0;
        z-index: 1;
        @include transition(width, .4s);
      }

      span {
        position: relative;
        z-index: 2;
      }
    }
  }
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 200px;
  height: 73px;
  box-sizing: border-box;
  cursor: pointer;
  background: transparent;
  margin-top: 35px;
  position: relative;

  span.placeholder_text {
    @include center_y;
    color: white;
    font-size: 16pt;
    line-height: 16pt;
    left: 10px;
    font-weight: 300;
    display: block;
    padding-left: 33px;
    box-sizing: border-box;
    background: none;
    background-position-y: 0;
    .guest_adults {
        font-size: 48pt;
        line-height: 48pt;
    }
    &.selected_value {
      color: #585d63;
      font-size: 21px;
      padding-top: 3px;
      background-position-y: 8px;
      font-weight: 600;
    }
  }

  & > label {
    text-transform: uppercase;
    font-size: 10px;
    cursor: pointer;
  }

  b.button {
    display: none;
  }
  &:after {
    content:'\f107';
    display: inline-block;
    font-family: "fontawesome", sans-serif;
    color: lightgrey;
    position: absolute;bottom: -5px;right:15px;
  }
}

input.promocode_input {
  margin-top: 0;
  color: black;
  text-transform: uppercase;
  font-size: 12pt;
  padding: 10px 5px;
  background: white;
  text-align: center;

  &::-webkit-input-placeholder {
    color: lightgrey;
    font-size: 12pt;
    font-weight: 300;
    text-transform: uppercase;
  }
  &::-moz-placeholder {
    color: lightgrey;
    font-size: 12pt;
    font-weight: 300;
    text-transform: uppercase;
  }
  &:-ms-input-placeholder {
    color: lightgrey;
    font-size: 12pt;
    font-weight: 300;
    text-transform: uppercase;
  }
  &:-moz-placeholder {
    color: lightgrey;
    font-size: 12pt;
    font-weight: 300;
    text-transform: uppercase;
  }
}

.selectricWrapper .selectric .button, .guest_selector .button {
  background: none;
}

.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

#booking .room_list label {
  display: block !important;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 115px !important;
      margin-left: -10px !important;
    }
  }
}

#booking label {
  display: none;
  cursor: pointer;
}
#booking label.dates_selector_label, #booking label.rooms_label, #booking .guest_selector label {
  display: block;
  position: absolute;
  top:10px;
  left: 15px;
  font-size: 12px;
  font-family: "Montserrat", Sans-Serif;
  color: white;
}
#booking label.rooms_label {
  top: -10px;
}
#booking .guest_selector label {
  top: -25px;
}

.hotel_selector {
  display: none;
}


.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: 300;
    font-size: 13px;
    padding-left: 15px;
    cursor: pointer;
    color: black;
    width: 220px;
  }
  .destination_field {
    position: relative;
  }
  .destination_field:after {
    background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
    color: #585d63;
    font-size: 23px;
    margin-left: 0;
    text-indent: 999px;
    font-weight: 600;
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    content: '';
    display: block;
  }
}

div#full_wrapper_booking.inner_engine,
div#full_wrapper_booking.floating_booking.showed {
  .dates_selector_personalized {
    padding-top: 4px;
  }
  .rooms_number_wrapper {
    margin-top: 4px;
  }
  .ticks_wrapper {
    display: none;
   }
  .rooms_number_wrapper {
    width: 150px;
  }
  .room_list_wrapper {
    left: 628px;
    top:102px;
  }
  .guest_selector {
    width: 230px;
    margin-top: 15px;
  }
  .wrapper_booking_button  {
    .promocode_wrapper {
      padding:11px 20px;
      .promocode_input {
        padding: 10px 5px;
      }
    }
    .submit_button {
      padding: 12px 10px;

    }
  }
}

div#full_wrapper_booking.inner_engine:not(.floating_booking) {
  bottom: 0;
  background-color: $corporate_1;
  padding-top:10px;

  #booking label.dates_selector_label, #booking label.rooms_label, #booking .guest_selector label {
    top: 0;
  }
  #booking .guest_selector label {
    top: -10px;
  }
  #booking .guest_selector {
    margin-top: 15px;
  }
  .wrapper_booking_button  {
    margin-top:-10px;
    .promocode_wrapper {
      padding:13px 20px;
      .promocode_input {
        padding: 10px 5px;
      }
    }
    .submit_button {
      padding: 14px 10px 15px;
    }
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  background-color: $corporate_1;
  width: 100%;

  #booking label.dates_selector_label, #booking label.rooms_label, #booking .guest_selector label {
    display: none;
  }
  .container12 {
    position: relative;
    #full-booking-engine-html-7 {
      margin: 0 0 0 50px !important;
      .start_end_date_wrapper .start_date_personalized, .start_end_date_wrapper .end_date_personalized, .rooms_number_wrapper, .guest_selector {
        border-bottom-width: 0;
      }
    }
  }

  .has_transition_600 {
    -webkit-transition: all 600ms cubic-bezier(0.165, 0.840, 0.440, 1.000);
    transition: all 600ms cubic-bezier(0.165, 0.840, 0.440, 1.000);
    will-change: transform, opacity;
  }

  #menu_controller {
    @include center_y;
    width: auto;
    left: 0;
    cursor: pointer;
    padding: 15px 10px;
    z-index: 999999;
    display: block;

    &.opened #lines {
      -webkit-transform: rotateZ(45deg);
      transform: rotateZ(45deg);
    }

    &:hover, &.opened {
      border: 1px solid white;
    }

    &:hover hr._1, &.opened hr._1 {
      width: 25px;
      -webkit-transform: rotate(90deg) translate3d(7px, -1px, 0) !important;
      transform: rotate(90deg) translate3d(7px, 0px, 0) !important;
    }

    &:hover hr._2, &.opened hr._2 {
      opacity: 0;
    }

    &:hover hr._3, &.opened hr._3 {
      -webkit-transform: translate3d(0px, -9px, 0) !important;
      transform: translate3d(0px, -9px, 0) !important;
      width: 25px;
    }

    hr {
      width: 25px;
      height: 0;
      border: none;
      border-bottom: 1px solid white;
      margin: 0;
      margin-top: 6px;
      margin-left: 0;
    }

    hr {
      border-bottom: 2px solid white;
    }

    hr.hidden {
      transform: scale(0, 1);
    }

    hr:first-child {
      margin-top: 0;
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  border-radius: 0;
  border-top:2px solid $corporate_1;
  margin-top: 0;
  box-shadow: 0 0 30px rgba(0,0,0,.3);
  &.datepicker_wrapper_up {
    margin-bottom: -35px;
    border-bottom:2px solid $corporate_1;
    border-top-width:0;
    &:after, &:before {
      position: absolute;
      left: 0;
      margin: 0 auto;
      right: 0;
      top:auto;
      bottom: -24px;
      content: "";
      z-index: 9;
      width: 0;
      height: 0;
      border: 12px solid transparent;
      border-top-color: white;
    }

    &:before {
      bottom: -30px;
      border: 14px solid transparent;
      border-top-color: $corporate_1;
    }
  }
  .header_datepicker {
    background-color: $corporate_1;
    .close_button_datepicker {
        border-width:0;
          &:before{
            @include center_xy;
            content: '\f00d';
            color:white;font-size: 10px;
            font-family: "fontawesome", sans-serif;
          }
    }
  }
  .ui-datepicker-header {
    .ui-corner-all {
      background-color: $corporate_2 !important;
    }
  }
  .specific_month_selector, .go_back_button {
    background-color: $corporate_2;
    color: white;
    border-radius: 0;
    strong {
      color: $corporate_3;
    }
  }
  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    .ui-widget-content .ui-state-default {
      line-height: 44px;
    }
  }
  .datepicker_ext_inf_sd {
    .ui-widget-header  {
        .ui-datepicker-prev {
            background-color: $corporate_2 !important;
        }
    }
  }
}