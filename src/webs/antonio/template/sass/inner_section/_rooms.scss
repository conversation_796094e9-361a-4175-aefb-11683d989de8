.rooms_wrapper {
  float: left;
  display: inline-block;
  width: 100%;
  background: white;
  padding: 80px 0 0;

  .filter_rooms_wrapper {
    text-align: center;

    .filter_element {
      display: inline-block;
      padding: 10px 30px;
      background: rgba(white, .3);
      cursor: pointer;
      -webkit-transition: background .4s, color .4s;
      -moz-transition: background .4s, color .4s;
      -ms-transition: background .4s, color .4s;
      -o-transition: background .4s, color .4s;
      transition: background .4s, color .4s;
      font-family: $title_family;
      margin-right: 3px;
      color: $corporate_1;

      &.active, &:hover {
        background: white;
      }
      &.active {
        position: relative;
        border-bottom:2px solid $corporate_1;
        border-top-width:0;
      }
    }
  }

  .rooms_list_wrapper {
    .room_element {
      text-align: center;
      margin-top: 60px;

      .room_content {
        font-size: $description_size;
        line-height: $line_height;
        letter-spacing: 1px;
        width: 910px;
        margin: 40px auto 0;
        font-weight: 300;
        @include transition(all,.3s);
        color: $corporate_3;

        .title_room {
          border-bottom: 1px solid $corporate_1;
          padding-bottom: 20px;
          margin-bottom: 20px;
          text-transform: uppercase;
          line-height: 40px;
          font-size: 30pt;
          font-family: $title_family;
          font-weight: 100;
          span {
            display: block;
            color: $corporate_1;
            font-size: 60%;
          }
        }

        small {
          font-size: 24px;
          color: white;
          display: block;
          font-family: $title_family;
        }

        hr {
          border: 0;
          height: 1px;
          background: white;
          margin: 20px auto;
        }

        .description_room {
          font-size: $description_size;
          line-height: $line_height;
        }
      }

      .button-promotion, .room_external_link {
        display: inline-block;
        background: $corporate_1;
        color: white;
        margin-top: 40px;
        padding: 25px 80px;
        text-transform: uppercase;
        -webkit-transition: background .4s, color .4s;
        -moz-transition: background .4s, color .4s;
        -ms-transition: background .4s, color .4s;
        -o-transition: background .4s, color .4s;
        transition: background .4s, color .4s;
        position: relative;
        font-family: $title_family;

        &:hover {
          color: white;

          &:before {
            width: 100%;
          }
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          background: darken($corporate_3, 10%);
          width: 0;
          z-index: 1;
          @include transition(width, .4s);
        }

        span {
          position: relative;
          z-index: 2;
        }
      }

      .room_gallery {
        margin-top: 80px;
        position: relative;
        background: $corporate_2;
        /* fit gallery on iPad */
        width: 100%;
        overflow: hidden;

        &.no_services {
          margin-bottom: 5px;

          &:before, &:after {
            display: none;
          }
        }

        &:before, &:after {
          position: absolute;
          bottom: 0;
          border: 25px solid transparent;
          border-bottom-color: white;
          width: 100%;
          z-index: 10;
        }

        &:before {
          content: "";
          right: 50%;

        }

        &:after {
          content: "";
          left: 50%;
        }

        .room_carousel {
          .owl-item {
            display: inline-block;

            .gallery_element {
              display: inline-block;
              position: relative;
              width: 100%;
              overflow: hidden;
              @include transition(height, .4s);

              &:hover {
                img {
                filter: grayscale(100%);
                opacity: .4;
                }
              }

              img {
                width: auto;
                min-width: 105%;
                min-height: 105%;
                filter: grayscale(0);
                opacity: 1;
                -webkit-transition: opacity .4s, filter .4s;
                -moz-transition: opacity .4s, filter .4s;
                -ms-transition: opacity .4s, filter .4s;
                -o-transition: opacity .4s, filter .4s;
                transition: opacity .4s, filter .4s;
              }
            }
          }

          .owl-nav {
            & > div {
              @include center_y;
              color: darken($corporate_3, 10%);
              font-size: 24px;

              &.owl-next {
                right: 10px;
              }

              &.owl-prev {
                left: 10px;
              }
            }
          }
        }
      }

      .services_wrapper {
        display: inline-block;
        width: 100%;
        float: left;
        padding: 55px 0 60px;
        background: white;

        .services_title {
          font-family: $title_family;
          font-size: $title_size;
          text-transform: uppercase;
          width: 400px;
          margin: auto;
          font-weight: 100;
          color: $corporate_3;

          &:after {
            content: "";
            height: 100px;
            margin: 40px auto;
            width: 1px;
            background: $corporate_3;
            display: block;
          }
        }

        .services_container {
          display: table;
          width: 1000px;
          margin: auto;
          text-align: left;
          .service_element {
            width: calc((100% - 20px)/3);
            float: left;
            margin: 10px 10px 5px 0;
            padding-left: 90px;

            &:nth-child(3n) {
              margin-right: 0;
            }

            .fa {
              color: $corporate_1;
              margin-right: 5px;
              font-size: 25px;
              vertical-align: middle;
            }

            span {
              display: inline-block;
              font-size: $description_size;
            }
          }
        }

      }
      svg {
        position:absolute;
        bottom:0;
        right:0;
        height:100%;
        z-index: 2;
        width:auto;
        * {
          fill: rgba($corporate_3,.8)
        }
      }
    }
  }
}