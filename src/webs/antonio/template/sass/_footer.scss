.footer_columns_wrapper {
  background-color: $corporate_1;
  clear: both;
  padding: 30px 0;
  .column {
    border-right: 1px solid #AAA;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    &:last-of-type {
      border-right-width: 0;
    }
    .title {
      margin-bottom: 20px;
      color: white;
      font-family: $title_family;
      font-weight: lighter;
      font-size: 40px;
    }
    .desc {
      color: white;
      line-height: 30px;
      text-transform:uppercase;
      .line_break {
        height: 1px;
        width: 100px;
        margin: 15px auto;
        background-color: white;
      }
      a {
        color: white;
        @include transition(all, .6s);
        &:after {
          content: '';
          display: block;
          margin: auto;
          height: 1px;
          width: 0;
          background-color: $corporate_1;
          @include transition(all, .6s);
        }
        &:hover {
          &:after {
             width: 100px;
           }
        }
      }
    }
  }
}

footer {
  background: $corporate_3;
  padding: 45px 0;
  display: inline-block;
  width: 100%;
  font-family: $title_family;

  .wrapper_footer_columns {
    display: inline-block;
    width: 100%;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 1px;
      background: white;
    }

    &:after {
      content: "";
      display: block;
      width: 95%;
      height: 1px;
      margin: auto;
      background: white;
      clear: both;
    }

    .footer_column {
      display: inline-block;
      width: 50%;
      float: left;
      text-align: center;
      position: relative;
      color: white;
      text-transform: uppercase;
      font-weight: 400;
      padding: 0 30px 40px;

      .footer_column_title {
        font-size: $title_size;
        font-weight: 100;
        margin-bottom: 30px;
        font-family: $title_family;
      }

      .footer_column_description {
        display: inline-block;
        width: 100%;

        a {
          color: white;
          margin-bottom: 6px;
          display: inline-block;

          &:last-of-type {
            margin-bottom: 0;
          }
        }

        hr {
          width: 100px;
          height: 1px;
          background: white;
          border: 0;
          margin: 15px auto;
        }
      }
    }
  }

  .menu_footer {
    text-align: center;

    a {
      display: inline-block;
      color: white;
      text-transform: uppercase;
      font-family: $title_family;
      font-weight: 100;
      font-size: 14px;
      vertical-align: middle;
      @include transition(all, .6s);

      &:last-of-type:after {
        display: none;
      }

      &:after {
        content: "";
        color: white;
        width: 3px;
        height: 3px;
        background-color: white;
        border-radius: 50%;
        display: inline-block;
        vertical-align: middle;
        margin: 0 10px;
      }
      &:hover {
        color: $corporate_1;
      }
    }
  }

  .footer_links_wrapper {
    width:100%;
    a {
      font-size: 12px;
    }
  }
  hr.separator {
    margin: 30px auto;
    width: 600px;
    background: white;
    height: 1px;
    border: 0;
  }

  .footer_legal_text_wrapper {
    text-align: center;
    font-size: 14px;
    color: white;

    a {
      color: white;
      font-weight: 400;
      margin: 0 5px;
      display: inline-block;
      @include transition(all, .6s);
      &:hover {
        color: $corporate_1;
      }
    }
  }
}