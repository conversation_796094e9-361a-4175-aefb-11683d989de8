@import "defaults";
@import "styles_mobile/1/1";
@import "styles_mobile/1/carousel_icon";
@import "styles_mobile/1/bannersx2";
@import "mobile/maps";

body {
  &.only_cancell {
    padding: 0;

    header, .breadcrumbs, .mobile_engine, nav, .main_menu {
      display: none;
    }
  }
}

.main_menu {
    background-color: #232323;
}

.popup_inicio {
  padding: 0;
}

.filter_offers_group_wrapper {
  @include center_x;
  z-index: 20;
  width: 70%;
  display: inline-block;

  &:before {
    content: "\f078";
    font-family: "FontAwesome";
    color: $corporate_1;
    font-size: 12px;
    @include center_y;
    right: 10px;
  }

  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 100%;
    background: white;
    border: 0;
    padding: 5px 10px;
    box-sizing: border-box;
  }
}

.individual_room_services {
  margin-bottom: 50px;
  .services_title {
    font-size: 20px;
    font-weight: bold;
    margin-top: 20px;
  }
}

.carousel_icon {
  bottom: 0em;
  top: 1em;
  .owl-item {
    box-sizing: border-box;
    .icon {
      @include center_xy;
      margin-left: -50px;
    }
    span, i {
      color: white;
      margin: auto;
    }
  }
  .owl-item:nth-child(even) {
    background-color: $corporate_1;
  }
  .owl-item:nth-child(odd) {
    background-color: $corporate_2;
  }
}

.room_external_link {
  display: table;
  margin: auto;
  background: $corporate_1;
  color: white;
  margin-top: 20px;
  padding: 8px 70px;
  font-size: 20px;
  text-transform: uppercase;
  border-radius: 5px;
}

.banners_block_wrapper {
  display: inline-block;
  width: 100%;
  margin-top: 40px;

  .banner_element {
    display: inline-block;
    width: 100%;
    margin-bottom: 20px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .banner_title {
      font-size: 22px;
      text-transform: uppercase;
      color: $corporate_1;
      margin-bottom: 10px;
    }

    .banner_description {
      padding: 0px 10px;
      margin-top: 10px;
    }
  }
}
#hotelSelect {
      display: block;
      width: 100%;
      margin: 1em auto;
      background-color: #f0f0f0;
      padding: 1.5em;
      border-width: 0;
}

#my-bookings-form-search-button {
  width: 100%;
  color: white;
  background-color: $corporate-1;
  padding: 1em 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
  border-radius: 5px;

  &:hover {
    opacity: .8;
  }
}


#full_wrapper_booking {
  .web_support_number, .web_support_label_1 {
    font-size: 12px!important;
  }
}