/* Base web (change too in templateHandler and in config.rb) */
$base_web: "antoo";
/* colors definitions */
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

/* corporative colors definitions */
$corporate_1: #019ed3;
$corporate_2: #7fcfea;
$corporate_3: #232323;

$title_size: 40px;
$title_family: "Montserrat";
$description_size: 14px;
$line_height: 28px;

/* colors for booking widget*/
$booking_widget_color_1: $white;
$booking_widget_color_2: $corporate_1;
$booking_widget_color_3: gray;
$booking_widget_color_4: gray;