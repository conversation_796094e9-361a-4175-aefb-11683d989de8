<div class="rooms_wrapper">

    <div class="rooms_list_wrapper">
        {% if rooms_individual %}
            <div class="room_element" data-filter="{{ rooms_individual.slug_name }}"
                 {% if rooms_individual.sectionName != sectionToUse.sectionName %}style="display: none"{% endif %}>
                <div class="container12">
                    <div class="room_content">
                        <div class="title_room">{{ rooms_individual.subtitle|safe }}</div>
                        {{ rooms_individual.content|safe }}
                    </div>
                    {% if not no_booking %}
                        <a class="button-promotion" href="#data">
                            <span>{{ T_reservar }}</span>
                        </a>
                    {% endif %}
                    {% if room_external_link and room_external_link_text %}
                        <a class="room_external_link" href="{{ room_external_link|safe }}" target="_blank">
                            <span>{{ room_external_link_text }}</span>
                        </a>
                    {% endif %}
                </div>

                {% if rooms_individual.gallery %}
                    <div class="room_gallery {% if not rooms_individual.services %}no_services{% endif %}">
                        <div class="room_carousel owl-carousel">
                            {% for i in rooms_individual.gallery %}
                                <a class="gallery_element" href="{{ i.servingUrl|safe }}=s1900" rel="lightbox[room_{{ forloop.parent.counter }}]">
                                    <img data-src="{{ i.servingUrl|safe }}" class="center_image"/>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                {% if rooms_individual.services %}
                    <div class="services_wrapper">
                        {% if rooms_individual.services_title %}
                            <div class="services_title">{{ rooms_individual.services_title|safe }}</div>
                        {% endif %}
                        <div class="container12">
                            <div class="services_container">
                                {% for i in rooms_individual.services %}
                                    <div class="service_element">
                                        {% if i.title %}
                                            <i class="fa {{ i.title|safe }}"></i>
                                        {% endif %}
                                        <span>{{ i.description|safe }}</span>
                                    </div>
                                {% endfor %}
                            </div>

                            {% if not no_booking %}
                                <a class="button-promotion" href="#data">
                                    <span>{{ T_reservar }}</span>
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}

            </div>
        {% endif %}
    </div>
</div>

<script>
    $(window).load(function () {
        var owl_params = {
            loop: true,
            nav: true,
            dots: false,
            items: 5,
            margin: 0,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            autoplay: true
        };
        $(".room_carousel").owlCarousel(owl_params);
        setSizeImage();
    });
    $(window).resize(setSizeImage);
    function setSizeImage() {
        var size_picture = $(".room_carousel:visible .owl-item").width();
        $(".room_carousel .owl-item").height(size_picture);
        $(".gallery_element").height(size_picture);
        $(".gallery_element img").each(function () {
            $(this).attr("src", $(this).attr("data-src") + "=s" + Math.round(size_picture) + "-c");
        })
    }
</script>