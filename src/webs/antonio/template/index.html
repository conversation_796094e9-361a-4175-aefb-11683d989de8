{% extends "__base.html" %}

{% block content %}
    <!--Header-->
    {% include "header.html" %}
{% spaceless %}
    {% block slider %}
        <section id="slider_container">
            {% if not home %}
                <div class="inner_slider">
                    <img {% if pictures %}src="{{ pictures.0.servingUrl }}=s1900"{% endif %} class="center_image"/>
                </div>
            {% else %}
                {{ revolution_slider|safe }}
            {% endif %}
            <div id="full_wrapper_booking" {% if not home %}class="inner_engine"{% endif %}>
                <div id="wrapper_booking" class="container12">
                    <div class="menu_full_screen" id="menu_controller">
                        <div id="lines" class="has_transition_600">
                            <hr class="has_transition_600 _1">
                            <hr class="has_transition_600 _2">
                            <hr class="has_transition_600 _3">
                        </div>
                    </div>
                    <div id="booking" class="boking_widget_inline">
                        {{ booking_engine|safe }}
                    </div>
                </div>
                {% if ticks_slider %}
                    <div class="ticks_wrapper">
                        {% for x in ticks_slider %}
                            <a {% if x.linkUrl %}href="{{ x.linkUrl|safe }}"{% endif %} class="tick_element">
                                {% if x.description %}
                                    <i class="fa {{ x.title|safe }}"></i>
                                {% endif %}
                                <span>{{ x.description|safe }}</span>
                            </a>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            {% if slider_icos %}
                <div class="slider_icos_wrapper">
                    <div class="container12">
                        <div class="slider_icos owl-carousel">
                            {% for x in slider_icos %}
                                <div class="icon">
                                    {% if x.description %}
                                        <i class="fa {{ x.title|safe }}"></i>
                                    {% endif %}
                                    <span>{{ x.description|safe }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <script>
                    $(window).load(function () {
                        var owl_params = {
                            loop: true,
                            nav: true,
                            dots: false,
                            items: 6,
                            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                            margin: 0,
                            autoplay: true
                        };

                        $(".slider_icos").owlCarousel(owl_params);
                    })
                </script>
            {% endif %}
        </section>
    {% endblock %}

    {% block main_content %}
        <section id="content">
            <div id="wrapper_content">
                {% include "_main_content.html" %}
            </div>
        </section>
    {% endblock %}

    {% include "footer.html" %}
{% endspaceless %}
{% endblock %}