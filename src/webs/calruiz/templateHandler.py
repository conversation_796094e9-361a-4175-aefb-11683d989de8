# -*- coding: utf-8 -*-

from booking_process.constants.advance_configs_names import CONTACT_PHONES, TOP_SECTIONS, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import getPicturesForKey, get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "calrz"
TEMPLATE_NAME = "calruiz"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True
			}
			params.update(params_mobile)

			sections = self.getSections(language)
			language_dict = get_web_dictionary(language)
			footer_params = {
				'policies_section': self.build_footer_policies(sections),
				'language': get_language_code(language)
			}
			footer_params.update(language_dict)
			footer = self.buildTemplate_2("mobile/_footer.html", footer_params, False, TEMPLATE_NAME)

			params['custom_element_home'] += footer
			params['custom_element_inner'] = footer

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		if section:
			section_type = section['sectionType']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
		    'phone_contact': get_config_property_value(CONTACT_PHONES),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True)
		}

		footer_columns = self.getPicturesProperties(language, "_footer_columns", ['rrss'])
		result_params_dict['footer_columns'] = list(filter(lambda x: x.get("title") != "legal_footer_extra_text", footer_columns))
		result_params_dict['legal_footer_extra_text'] = list(filter(lambda x: x.get("title") == "legal_footer_extra_text", footer_columns))

		all_sections = self.getSections(language)
		top_sections = self.getSectionsFor(language, all_sections, TOP_SECTIONS)
		for top_element in top_sections:
			section_pictures = getPicturesForKey(language, top_element.get('key'), [])
			top_element['icon'] = list(filter(lambda x: x.get('title') == 'ico', section_pictures))
		self.internationalizeUrls(top_sections)
		result_params_dict['top_sections'] = top_sections

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		if section_type == "Inicio":
			result['home'] = True

		elif section_type == "Habitaciones":
			result['rooms'] = self.getRooms(language)

		elif section_type == "Ofertas":
			result['banner_offers_list'] = True
			result['offers'] = self.buildPromotionsInfo(language)

		elif section_type == u"Localización":
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language)
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)
		props_list = ['btn_text','btn_booking']

		if advance_properties.get('banner_cycle'):
			result['banner_cycle'] = self.getPicturesProperties(language, advance_properties.get('banner_cycle'), props_list)

		if advance_properties.get('banner_full'):
			result['banner_full'] = self.getPicturesProperties(language, advance_properties.get('banner_full'), props_list)

		if advance_properties.get('banner_icons'):
			result['banner_icons'] = self.getPicturesProperties(language, advance_properties.get('banner_icons'), props_list)

		if advance_properties.get('banner_map'):
			result['banner_map'] = get_section_from_section_spanish_name(advance_properties.get('banner_map'), language)
			result['iframe_map'] = get_section_from_section_spanish_name("Iframe google maps", language)

		if advance_properties.get('banner_offers'):
			result['banner_offers'] = get_section_from_section_spanish_name(advance_properties.get('banner_offers'), language)
			banner_offers_pic = get_pictures_from_section_name(advance_properties.get('banner_offers'), language)
			for pic in banner_offers_pic:
				if pic.get('title') == 'pattern':
					result['banner_offers']['pattern'] = pic.get('servingUrl')
				if pic.get('linkUrl'):
					result['banner_offers']['link'] = pic.get('linkUrl')
					if pic.get('description'):
						result['banner_offers']['btn_text'] = pic.get('description')
			result['offers'] = self.buildPromotionsInfo(language)

		if advance_properties.get("minigallery"):
			num_items = 5
			if user_agent_is_mobile():
				num_items = 1
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			mini_dict = {'minigallery': minigallery_images, 'num_items': num_items, 'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		return result

	def getRooms(self, language):
		args = ['gallery', 'btn_booking']
		rooms = self.getPicturesProperties(language, "_habitaciones_blocks", args)
		for room in rooms:
			if room.get('gallery'):
				room['gallery'] = get_pictures_from_section_name(room['gallery'], language)
			elif 'http_' in room.get('linkUrl'):
				room['gallery'] = get_pictures_from_section_name(room.get('linkUrl'), language)
				room['linkUrl'] = False

		return rooms

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)

		return offers

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result = self.getPicturesForGallerySection(language)

		if section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			result['disable_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']

		if section_type == "Habitaciones":
			pass

		elif section_type == "Ofertas":
			pass

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		result += "</div>"

		return result