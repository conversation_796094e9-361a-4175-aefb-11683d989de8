body {
  font-family: "Roboto", sans-serif;
  font-size: 19px;
  font-weight: 300;
  line-height: 25px;
  color: #333333;
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  strong {
    font-weight: 700;
  }
  a {
    text-decoration: none;
  }
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  #slider_container {
    position: relative;
    .inner_slider {
      position: relative;
      height: 450px;
      width: 100%;
      overflow: hidden;
      img {
        @include center_image;
      }
    }
  }

  .content_subtitle_wrapper {
    padding: 100px calc((100% - 1140px) / 2);
    text-align: center;
    h1.content_subtitle_title {
      color: $corporate_2;
      font-family: "Martel", sans-serif;
      font-size: 40px;
      font-weight: 700;
      line-height: 37px;
    }
    .content_subtitle_description {
      padding: 30px 150px 0;
      font-size: 19px;
      font-weight: 300;
      big {
        color: $corporate_2;
      }
    }
  }

  .big-img {
    .fa.fa-angle-right,
    .fa.fa-angle-left {
      @extend .fal;
      font-size: 60px;
    }
    .image_filters_wrapper {
      .filter_element{
        font-weight: 700;
        &.active {
          background: $corporate_1;
          color: white;
        }
      }
    }
  }

  #my-bookings-form {
    padding: 0 0 50px;
    margin: auto;

    .modify_reservation_widget #motor_reserva #envio input {
      width: 200px;
      margin-left:13px;
    }
    #reservation {
      margin-top: 0 !important;

      .modify_reservation_widget {
        margin: auto;
        margin-top: 40px;
        margin-bottom: 0;
      }

      .my-bookings-booking-info {
        margin: 40px auto 0;

        .fResumenReserva {
          margin: auto;
        }
      }

      #contenedor_opciones {
        #hab1, #hab2, #hab3 {
          width: 350px;
        }
      }
    }

    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        font-weight: 100;
      }

      select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 300px;
        margin: 10px auto;
        padding: 0 15px;
        height: 40px;
        text-align: center;
        font-size: 14px;
        background-color: white;
        border-radius: 0;
        border: 1px solid $corporate_1;
      }

      input {
        display: block;
        width: 405px;
        margin: 10px auto;
        height: 40px;
        text-align: center;
        font-size: 14px;
        border: 1px solid $corporate_1;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            height: 40px;
            text-transform: uppercase;
            font-size: 16px;
            color: white;
            font-family: "Roboto", sans-serif;
            border: 0;
            cursor: pointer;
            width: 100%;
            @include transition(all, .4s);

            &.cancelButton {
              background: $corporate_2;
              height: 40px;
              text-transform: uppercase;
              font-size: 16px;
              color: white;
              border: 0;
              cursor: pointer;
              width: 200px;
              font-weight: 100;
              @include transition(all, .4s);

              &:hover {
                background-color: white;
                border: 1px solid $corporate_1;
                color: $corporate_1;
              }
            }

            &.modify-reservation {
              background: $corporate_1;

              &:hover {
                background-color: white;
                border: 1px solid $corporate_1;
                color: $corporate_1;
              }
            }

            &.searchForReservation {
              background: $corporate_2;

              &:hover {
                background-color: white;
                border: 1px solid $corporate_2;
                color: $corporate_2;
              }
            }
          }
        }
      }
    }
    #cancelButton {
      display: none;
    }
  }

  #cancel-button-container {
    button {
      background: $corporate_2;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 20px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_2, 10%);
      }
    }
  }

  #motor_reserva {
    #searchForm {
      #contenedor_opciones {
        .numero_personas {
          &.adultos, &.ninos {
            padding: 5px 35px;
            max-width: 165px;
            .selector_adultos, .selector_ninos {
              display: block;
              margin-bottom: 10px;
            }
            #info_ninos {
              @include center_x;
              top: 30px;
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .gallery-image {
    margin-top: 0;
  }

  &.user_isIpad {
    .lightbox .lb-container {
      .lb-prev, .lb-next {
        opacity: 1;
      }
    }
  }
}
