.banner_map_wrapper {
  background: $corporate_1;
  text-align: right;
  overflow: hidden;
  height: 480px;
  .banner_map {
    position: relative;
    z-index: 1;
    box-shadow: 5px 0 15px -5px #333;
    display: inline-block;
    vertical-align: middle;
    width: 420px;
    padding: 60px 60px 60px 10px;
    text-align: left;
    h2 {
      @include title_style;
      color: white;
    }
    .desc {
      color: white;
    }
    .btn_personalized {
      @include btn_style;
      overflow: visible;
      text-transform: uppercase;
      &:before {
        border-color: $corporate_2;
        background: white;
      }
      &:after {
        border-color: white;
        width: calc(100% - 2px);
        height: calc(100% - 3px);
      }
      span {
        color: white;
      }
      &.map_info {
        margin: 30px 0;
      }
      &:hover span {
        color: $corporate_1 !important;
      }
    }





    .map_widget {
      margin-top: 30px;
      h3 {
        position: relative;
        color: white;
        font-family: "Martel",serif;
        font-size: 25px;
        font-weight: 700;
        padding: 10px;
        i {
          @include center_y;
          right: 5px;
          cursor: pointer;
        }
      }
    }
    input {
      display: inline-block;
      vertical-align: middle;
      padding: 10px;
      font-size: 14px;
      border-width: 0;
      margin-bottom: 20px;
      width: calc(100% - 151px);
    }
    .btn_personalized {
      width: 150px;
      vertical-align: middle;
      font-size: 14px;
      padding: 7px 0 6px;
      text-align: center;
      margin-bottom: 20px;
    }
  }
  .iframe_map {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - ((100% - 1140px) / 2 + 420px));
    height: 490px;
    position: relative;
    overflow: hidden;
    iframe {
      width: 100%;
      height: 480px;
    }
  }
}