.luxury {
  .minigallery_wrapper {
    background-color: #333;
    border-top-color: #333;
    border-bottom-color: #333;
  }
}
.minigallery_wrapper {
  border-top: 5px solid white;
  border-bottom: 5px solid white;

  h2 {
    position: relative;
    text-align: center;
    font-size: 50px;
    line-height: 45px;
    font-weight: 300;
    padding: 30px 0;
    margin-bottom: 20px;
    small, big {
      font-family: "Satisfy", sans-serif;
      color: $corporate_1;
    }
    small {
      font-size: 40px;
    }
    big {
      font-size: 80px;
    }
  }
  .owl-item {
    background-color:$corporate_1;

    overflow: hidden;
    @include transition(all, .6s);

    &.active {
      width: 15vw !important;height: 350px;
    }
    &.active ~ .active ~ .active {
      width: 40vw !important;
    }
    &.active ~ .active ~ .active ~ .active {
      width: 15vw !important;
    }
    &.active ~ .active ~ .active ~ .active ~ .active {
      width: 15vw !important;
    }

    img {
      width: auto;
      opacity:1;
      @include transition(all,.6s);
    }
    span {
      display: block;
      width: 90%;
      color: white;
      font-size: 30px;
      font-weight: 300;
      line-height: 30px;
      text-align: center;
      opacity: 0;
      //text-shadow: 0 0 5px rgba(0,0,0,.6);
      @include center_xy;
      @include transition(all,.6s);
      i.fa {
        display: block;
        text-align: center;
        font-size: 25px;
      }
      small {
        font-family: "Martel", serif;
        font-size: 30px;
        line-height: 30px;
      }
      big {
        font-family: "Martel", serif;
        font-size: 70px;
        line-height: 70px;
      }
    }

    &:hover {
      img {
        opacity: .4;
      }
      span {
        opacity: 1;
      }
      .minigallery_desc {
        img {
          opacity: .8;
        }
      }
    }
    .minigallery_desc {
      img {
        opacity: .4;
      }
    }
  }

  .owl-nav {
    &.disabled {
      display: block;
    }
    & > div {
      position: absolute;
      bottom: 0;
      top: 0;
      color: white;
      cursor: pointer;
      font-size: 60px;
      width: 100px;
      height: 100%;
      line-height: 100px;
      padding: 0 10px;
      overflow: hidden;
      i.fa {
        position: absolute;
        bottom: 10px;
        z-index: 5;
      }
      &:hover {
        &:before {
          background: rgba($corporate_1, .8);
        }
      }
    }

    .owl-prev {
      left: 0;
      background: linear-gradient(to right, rgba(0,0,0,0.8), rgba(0,0,0,0));
      i.fa {
        @extend .fa-angle-left;
        left: 10px;
      }
    }

    .owl-next {
      right: 0;
      text-align: right;
      background: linear-gradient(to left, rgba(0,0,0,0.8), rgba(0,0,0,0));
      i.fa {
        right: 10px;
        @extend .fa-angle-right;
      }
    }
  }
}