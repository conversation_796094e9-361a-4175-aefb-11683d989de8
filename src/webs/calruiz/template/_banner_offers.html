<div class="banner_offers_wrapper {% if not banner_offers %}banner_offers_list{% endif %}">
    {% if banner_offers and banner_offers.pattern %}
        <div class="pattern" style="background-image: url('{{ banner_offers.pattern }}');"></div>
    {% endif %}
    {% if banner_offers and banner_offers.subtitle %}<h2>{{ banner_offers.subtitle|safe }}</h2>{% endif %}
    {% if banner_offers and banner_offers.content %}<div class="banner_desc">{{ banner_offers.content|safe }}</div>{% endif %}
    {% if banner_offers and banner_offers.link %}<a href="{{ banner_offers.link }}" class="offers_link">
        {% if banner_offers.btn_text %}
            <span>{{ banner_offers.btn_text|safe }}</span>
        {% else %}
            <span>{{ T_ver_mas_info }}</span>
        {% endif %}
    </a>{% endif %}
    <div class="banner_offers {% if banner_offers %}owl-carousel{% endif %}">
        {% for banner in offers %}<div class="banner">
            <div class="banner_image">
                <img src="{{ banner.picture }}=s800" alt="{{ banner.title|safe }}">
            </div><div class="banner_content">
                <h2>{{ banner.name|safe }}</h2>
                {% if banner.description %}<div class="desc">{{ banner.description|safe }}</div>{% endif %}
                <div class="links">
                    {% if banner.linkUrl %}
                        <a href="{{ banner.linkUrl }}" data-text="{% if banner.btn_text %}{{ banner.btn_text|safe }}"><span>{{ banner.btn_text|safe }}</span>{% else %}{{ T_ver_mas }}"><span>{{ T_ver_mas }}</span>{% endif %}</a>
                    {% endif %}
                    {% if offers_link %}
                        <a href="{{ offers_link }}" data-text="{{ T_ver_mas }}"><span>{{ T_ver_mas }}</span></a>
                    {% endif %}
                    <a href="#data" class="button_promotion" data-text="{{ T_reservar }}"><span>{{ T_reservar }}</span></a>
                </div>
            </div>
        </div>{% endfor %}
    </div>
</div>
{% if banner_offers %}<script>
$(window).load(function () {
    $(".banner_offers").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        navText: ['<i class="fal fa-angle-left" aria-hidden="true"></i>', '<i class="fal fa-angle-right" aria-hidden="true"></i>'],
        autoplay: true,
        autoplayHoverPause: true
    });
});
</script>{% endif %}