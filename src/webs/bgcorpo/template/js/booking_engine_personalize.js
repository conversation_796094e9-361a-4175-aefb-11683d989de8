$(window).load(function () {
    fade_out_widget();
    prepare_guests_selector();
    set_occupancy_number();
    $(".guest_selector").unbind();
    $(".guest_selector").click(function (e) {
       e.preventDefault();
       $(".room_list_wrapper").slideToggle(function () {
           $(this).css("overflow","inherit");
       });
    });

    $(".destination_field, .close_hotel_selector").unbind("click");
    $(".destination_field, .close_hotel_selector").click(function() {
        $(".hotel_selector").slideToggle();
    });

    $('.hotel_selector_option').click(function() {
       let kids_range_age = $(this).data('kids_ages_range');
       let babies_range_age = $(this).data('babies_ages_range');

       if (kids_range_age) {
          $('.children_selector .range-age').html(`(${kids_range_age} ${$.i18n._("T_anyos")})`);
       }

       if (babies_range_age) {
          $('.babies_selector .range-age').html(`(${babies_range_age} ${$.i18n._("T_anyos")})`);
       }
    });
});

function prepare_guests_selector() {
   $('body').on('click', '.guest_selector, .close_guesst_button, .save_guest_button', function() {
      toggle_guest_selector();
   });

   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

   //Close room button
   for (var x=2;x<=3;x++){
      var close_button_element = $("<div class='remove_room_element'></div>");
      $(".room_list .room" + x).append(close_button_element);
   }

   $(".remove_room_element").click(function(){
      var actual_room_numbers = $("select.rooms_number").val();
      if (actual_room_numbers > 1){
         var target_room_number = parseInt(actual_room_numbers) - 1;
         $("select.rooms_number option").removeAttr('selected');
         $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
         $(".room" + actual_room_numbers).hide();
         $("select.rooms_number").val(target_room_number);
         $("select.rooms_number").selectric("refresh");
      }
      set_occupancy_number()
   });
}

function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = $("select[name='numRooms']").val(),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
             actual_select_babies = $("select[name='babiesRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
            babies_number += parseInt(actual_select_babies);
         }
      }
   }

   var target_placeholder = $(".guest_selector .placeholder_text"),
       placeholder_string = "";

   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);
   babies_number = parseInt(babies_number);

   placeholder_string += "<span class='guest_adults'>" + adults_number + "</span>";

   if(!$(".adults_only_selector").length){
      placeholder_string += "/" + kids_number;
   }

   if($(".babies_selector").length) {
      placeholder_string += "/" + babies_number;
   }

   target_placeholder.html(placeholder_string);
}