function createCookie(key, valor){
    document.cookie= key + "=" + valor;
}

function searchCookie(key){
   var encontrado = -1;
   var x = document.cookie;
   if (x) {
        var y = x.split(";");
        for (var i=0;i< y.length; i++) {
            encontrado = y[i].search(key);
            if (encontrado > -1) {
                resultado = y[i].split("=");
                return resultado[1];
            }
        }
   }
}

$(function () {
    _check_user_login();

    $(".logout").click(function () {
        deleteCookie('user_login');
        setTimeout(function () {
            window.location.reload()
        }, 1000);
        return false;
    });


    $(".forget_password").click(function () {
        _open_remember_password();
        return false;
    });

    $(".recovery_button").click(function () {
        _send_password();
    });

    $("#identifier").focusout(function(){
        if($(this).val()){
           $.post('/users/', {
               'action': 'get_hotel_reservation',
               'alternative_hotels_booking': $("input[name='alternative_hotels_booking']").val(),
               'identifier': $("#identifier").val()
           }, function (response) {
               if(response){
                   $("input[namespace='" + response + "']").prop('checked', true);
               }
           })
        }
    })
});


/*======== Forms =======*/
$("form.suscribe_form").validate({
    rules: {
        identifier: "required",
        name: "required",
        surname: "required",
        address: "required",
        pais: "required",
        province: "required",
        city: "required",
        postal_code: "required",
//        birthday: "required",
//        gender: "required",
//        job: "required",
//        dni: "required",
        password: "required",
        password_confirmation: {
            equalTo: '#password'
        },
        telephone: {
            required: function (element) {
                return !$("#telephone").val() > 0 || !$("#mobile").val() > 0;
            }
        },
        comments: "required",
        email: {
            required: true,
            email: true
        },
        privacy: "required"
    }, highlight: function (element) {
        $(element).addClass('input-error');
    }, unhighlight: function (element) {
        $(element).removeClass('input-error');
    }
});

performin_request = false;
function register_user() {
    if (performin_request) {
            return false;
    }
    performin_request = true;

    if ($("form.suscribe_form").valid()) {

        var user_info_params = $("form.suscribe_form").serializeArray(),
            final_dict = {};


        for (var x=0;x<user_info_params.length;x++){
            final_dict[user_info_params[x]['name']] = user_info_params[x]['value']
        }

        selected_hotels_check = $("input[name='hotels']:checked");
        if(selected_hotels_check.length > 0){
            final_dict['destiny_code'] = selected_hotels_check.attr('destiny_code');
            final_dict['hotel_code'] = selected_hotels_check.attr('hotel_code');
        }

        if (!final_dict['destiny_code']){final_dict['destiny_code'] = '88'}
        if (!final_dict['hotel_code']){final_dict['hotel_code'] = '55'}
        final_dict['username'] = final_dict['email'];

        final_dict['action'] = 'put';

        final_dict['language'] = $(".suscribe_form input[name='language']").val();


        //user_info_params = $.param(final_dict);
        user_info_params = final_dict;

        _perform_post_register(user_info_params);
    }else{
        performin_request = false;
    }
}

function _perform_post_register(form_params) {
    $.post('/users/', form_params, function (response) {
        if (response === 'repeated') {
            alert($(".repetead_user").html());

        } else if (response === 'booking_inexistent') {
            alert($(".booking_inexistent").html());
        } else if (response) {
            alert($(".completed_message").html());
            //$("form.suscribe_form").find("input").val("");

            ga('send', 'event', {
                'eventCategory': 'Contacto',
                'eventAction': 'Formularios',
                'eventLabel': 'Registro club BG'
            });
        }

        performin_request = false;
    })
}


function _login_user(element) {
    var form_params_element = $("form.club_user_login").serialize();
    $.post('/users/?' + form_params_element, function (response) {
        if (response) {
            var response_json = $.parseJSON(response);
            $(".club_user_login, .forget_password, .register_club_link").hide();
            $(".welcome_login").show();
            $(".logout").show();
            $(".modify_profile").show();
            $(".welcome_login .user_name_login").html(response_json.name + " " + response_json.surname);
            createCookie('user_login', response_json.name + " " + response_json.surname);
            createCookie('user_credentials', response_json.username + ";_" + response_json)
        }else{
            alert($.i18n._("error newsletter"));
        }
    })
}

function _check_user_login() {
    var login_cookie = searchCookie('user_login');
    if (login_cookie) {
        $(".club_user_login, .forget_password, .register_club_link").hide();
        $(".welcome_login").show();
        $(".logout").show();
        $(".modify_profile").show();
        $(".welcome_login .user_name_login").html(login_cookie);
    }
}

function _open_remember_password() {
  if(typeof $.fancybox === 'function'){
    $.fancybox($("#forget_password_popup"), {
        'width': 700,
        'wrapCSS': "forger_password_fancybox"
    })
  } else{
    $.fancybox.open($("#forget_password_popup"), {
        'wrapCSS': "forger_password_fancybox"
    })
  }

}

function _send_password() {
    var email_to_send = $("#forget_email").val();
    if (!email_to_send) {
        $("#forget_email").addClass("error");
    } else {
        $("#forget_email").removeClass("error");
        var params_query = "action=recovery&email=" + email_to_send;
        $.post('/users/', params_query, function (response) {
            if (response){
                alert($(".completed_message").html());
            }else{
                alert(incorrect_user_message);
            }
        })
    }
}