<div class="banner_block_full_wrapper {% if home %}banner_block_home{% endif %}
    {% if banner_block_bg_img %}with_bg_img" style="background-image: url('{{ banner_block_bg_img|safe }}=s1600')"{% else %}" {% endif %}>
    {% if banner_block_text %}
        <div class="banner_block_content">
            {% if banner_block_text.subtitle %}
                <h2 class="content_title">{{ banner_block_text.subtitle|safe }}</h2>
            {% endif %}
            {% if banner_block_text.content %}
                <div class="content_text">{{ banner_block_text.content|safe }}</div>
            {% endif %}
        </div>
    {% endif %}
    <div class="banner_block_wrapper">
        {% if banner_block_destinies %}
            {% for destiny, hotels in banner_block_destinies.items() %}
                {% if hotels.hotel_list %}
                    <div class="banner_block">
                        {% if hotels.hotel_list.0.pictures %}
                            <img src="{% if banner_force_image %}{% for banner in banner_force_image %}{% if banner.clean_name == hotels.class_name %}{{ banner.servingUrl|safe }}{% endif %}{% endfor %}{% else %}{% if hotels.hotel_list.0.pictures.1 %}{{ hotels.hotel_list.0.pictures.1|safe }}{% else %}{{ hotels.hotel_list.0.pictures.0|safe }}{% endif %}{% endif %}" alt="{{ destiny|safe }}">
                        {% endif %}
                        <div class="banner_content">
                            <div class="banner_text">
                                <h4 class="title">{{ destiny|safe }}</h4>
                                <div class="text hotels_number">
                                    <span class="number">{{ hotels.hotel_list|length|safe }}</span>
                                    {{ T_hoteles }}
                                </div>
                                {% if hotels.hotel_list.0.destiny_link %}
                                    <a href="{% if banner_force_image %}{% for banner in banner_force_image %}{% if banner.extra_banner_link and banner.clean_name == hotels.class_name %}{{ banner.extra_banner_link|safe }}?filter={{ destiny|safe }}{% endif %}{% endfor %}{% else %}{{ hotels.hotel_list.0.destiny_link|safe }}{% endif %}" {% if "http" in hotels.hotel_list.0.destiny_link %} target="_blank" {% endif %}class="btn_personalized_2">
                                        {{ T_ver_mas }}
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        {% else %}
            {% for banner in banner_block %}
                <div class="banner_block">
                    {% if banner.servingUrl %}
                        <img src="{{ banner.servingUrl|safe }}" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                    {% endif %}
                    {% if banner.title or banner.description %}
                        <div class="banner_content">
                            <div class="banner_text">
                                {% if banner.title %}
                                    <h4 class="title">{{ banner.title|safe }}</h4>
                                {% endif %}
                                {% if banner.description %}
                                    <div class="text">{{ banner.description|safe }}</div>
                                {% endif %}
                                {% if banner.linkUrl %}
                                    <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %} target="_blank" {% endif %}class="btn_personalized_2">
                                        {% if banner.link_text %}
                                            {{ banner.link_text|safe }}
                                        {% else %}
                                            {{ T_ver_mas }}
                                        {% endif %}
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}
    </div>
</div>

{% if not is_mobile and not user_isIpad %}
    <script>
        $(window).load(function () {
            function banner_block_fx() {
                $(".banner_block_wrapper .banner_block:nth-of-type(odd)").addnimation({parent:$(".banner_block_wrapper"), class:"fadeInLeft", reiteration: false});
                $(".banner_block_wrapper .banner_block:nth-of-type(even)").addnimation({parent:$(".banner_block_wrapper"), class:"fadeInRight", reiteration: false});
            }
            banner_block_fx();
            $(window).scroll(banner_block_fx);
        });
    </script>
{% endif %}