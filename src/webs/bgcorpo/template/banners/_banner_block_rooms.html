<div class="banner_block_rooms_full_wrapper">
    {% if banner_block_rooms_sec and banner_block_rooms_sec.subtitle %}
        <div class="banner_block_content">
            <h2 class="content_title">{{ banner_block_rooms_sec.subtitle|safe }}</h2>
            {% if banner_block_rooms_sec.content %}
                <div class="content_text">{{ banner_block_rooms_sec.content|safe }}</div>
            {% endif %}
        </div>
    {% endif %}
    {% if banner_block_rooms_pics %}
        <div class="banner_block_wrapper {% if banner_block_rooms_pics|length > 2 %}owl-carousel{% endif %}">
            {% for pic in banner_block_rooms_pics %}
            <div class="banner_block">
                {% if pic.servingUrl %}
                    <img src="{{ pic.servingUrl }}">
                {% endif %}
                <div class="banner_content">
                    <div class="banner_text">
                        {% if pic.title %}
                        <h4 class="title">{{ pic.title|safe }}</h4>
                        {% endif %}
                        {% if pic.linkUrl %}
                        <a href="{{ pic.linkUrl }}" class="btn_personalized_2">{{ T_ver_habitaciones }}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
<script>
    $(window).load(function () {
        $(".banner_block_rooms_full_wrapper .banner_block_wrapper").owlCarousel({
            loop: {% if is_mobile %}false{% else %}true{% endif %},
            nav: true,
            dots: false,
            draggabale: false,
            items: {% if is_mobile %}1{% else %}2{% endif %},
            navText: ['<i class="fal fa-long-arrow-left"></i>', '<span>{{ T_siguiente }}</span> <i class="fal fa-long-arrow-right"></i>'],
            autoplay: false,
            margin: 20
        });
    });
</script>