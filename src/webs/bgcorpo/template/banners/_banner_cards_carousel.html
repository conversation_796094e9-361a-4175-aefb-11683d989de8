<div class="banner_cards_carousel_wrapper">
    {% if banner_bg_image %}
    <div class="bg_image">
        <img src="{{ banner_bg_image }}">
    </div>
    {% endif %}
    <div class="cards_container">
        {% if banner_carousel_cards_sec and banner_carousel_cards_sec.subtitle %}
        <div class="top_banner">
            <div class="content_title">
                <h3 class="title">
                    {{ banner_carousel_cards_sec.subtitle|safe }}
                </h3>
            </div>
        </div>
        {% endif %}
        <div class="cards_wrapper {% if banner_carousel_cards_pics|length > 3 %}owl-carousel{% endif %}">
            {% for pic in banner_carousel_cards_pics %}
                {% if pic.title != 'bg_image' %}
                <div class="card">
                    {% if pic.servingUrl %}
                    <div class="picture_wrapper">
                        <img src="{{ pic.servingUrl }}">
                    </div>
                    {% endif %}
                    <div class="card_content">
                        {% if pic.title %}
                        <div class="content_title">
                            <h3 class="title">
                                {{ pic.title|safe }}
                            </h3>
                        </div>
                        {% endif %}
                        {% if pic.description %}
                        <div class="desc">
                            {{ pic.description|safe }}
                        </div>
                        {% endif %}
                        {% if pic.linkUrl or pic.custom_link %}
                        <div class="links_wrapper">
                            <a href="{% if pic.custom_link %}{{ pic.custom_link }}{% elif pic.linkUrl %}{{ pic.linkUrl }}{% endif %}" {% if pic.custom_link or 'http' in pic.linkUrl %}target="_blank"{% endif %} class="btn_link_more">{{ T_ver_mas }}</a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
<script>
    $(window).load(function () {
        $(".banner_cards_carousel_wrapper .cards_wrapper").owlCarousel({
            loop: false,
            nav: true,
            dots: false,
            draggabale: false,
            items: {% if is_mobile %}1{% else %}3{% endif %},
            navText: ['<i class="fal fa-long-arrow-left"></i>', '<i class="fal fa-long-arrow-right"></i>'],
            autoplay: false
        });
    });
</script>