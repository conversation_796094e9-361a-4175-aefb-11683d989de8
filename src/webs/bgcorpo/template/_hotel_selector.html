<div class="destination_wrapper">
    <span class="hotel_selector_label">{{ T_hotel }}/{{ T_destino }}</span>
  <div class="destination_field">
      <span class="destination" readonly="readonly" type="text" name="destination">{{ T_seleccionar }}</span>
  <input  type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="{{T_seleccionar}} {{ T_hotel }}">
  </div>
</div>

<div class="hotel_selector" {% if popup_loading_background %}style="background-image: url('{{ popup_loading_background.servingUrl }}=s1900');"{% endif %}>
    <div class="close_hotel_selector"><i class="fa fa-times"></i></div>
    <div class="hotel_selector_inner">
        <div class="search_hotels">
            <input name="search_hotels" class="search_hotels_selector" placeholder="{{ T_buscar_ahora }}">

            <div class="all_hotels_list_search" style="display: none;">
                {% for destino, hoteles in hotels_grouped.items() %}
                    {% for hotel_element in hoteles.hotel_list %}<a class="hotel_element_search hotel_selector_option" id="{{ hotel_element.namespace }}">
                        <span class="title_selector">{{ hotel_element.subtitle|safe }}</span> <small>({{ destino|safe }})</small>
                    </a><input type="hidden" id="url_booking_{{ hotel_element.namespace }}" value={{ hotel_element.url_booking }}><input type="hidden" id="namespace_{{ hotel_element.namespace }}" value={{ hotel_element.namespace }}>{% endfor %}
                {% endfor %}
            </div>
        </div>
        {% for destiny, value in hotels_grouped.items() %}<div class="destiny">
        <h3>{{ destiny }}</h3>
            <ul class="hotel_selector_destiny">
                {% for hotel in value.hotel_list %}
                    <li id="{{ hotel.namespace }}"
                        {%  if hotel.kids_ages_range %}
                            data-kids_ages_range="{{ hotel.kids_ages_range }}"
                        {%  endif %}
                        {%  if hotel.babies_ages_range %}
                            data-babies_ages_range="{{ hotel.babies_ages_range }}"
                        {%  endif %}
                        class="{{ hotel.namespace }} {% if hotel.only_adults %}only_adults{% endif %} filter_all filter_{{ hotel.destiny_class }} hotel_selector_option">
                        <span class="title_selector">{{ hotel.subtitle|safe }}</span>
                    </li>
                    <input type="hidden" id="url_booking_{{ hotel.namespace }}" value="{{ hotel.url_booking }}">
                    <input type="hidden" id="namespace_{{ hotel.namespace }}" value="{{ hotel.namespace }}">
                {% endfor %}
                {% if value.hotel_list|length > 1 %}
                    <span class="booking_0_hotel_selection" namespaces="{% for hotel in value.hotel_list %}{{ hotel.namespace }}{% if not loop.last %};{% endif %}{% endfor %}" hotel_name="{{ destiny }}"><strong>{{ T_todos_hoteles_en }} </strong>{{ destiny }}</span>
                {% endif %}
            </ul>
        </div>
        {% endfor %}
    </div>
</div>
