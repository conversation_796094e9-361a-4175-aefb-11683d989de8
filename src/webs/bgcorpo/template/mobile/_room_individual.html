<div class="individual_room_picture">
    <div class="background_overlay"></div>
    <img class="room_image" src="{{ actual_section.pictures.0|safe }}=s800">
    {% if actual_section.subtitle %}<h2 class="individual_room_title">{{ actual_section.subtitle|safe }}</h2>{% endif %}
</div>
<div class="booking_room_button_element button-promotion" {% if actual_section.sectionName %}data-namespace="{{ actual_section.sectionName|safe }}"{% endif %} onclick="return">{{ T_reservar }}</div>

{% if actual_section.content %}<div class="individual_room_description">{{ actual_section.content|safe }}</div>{% endif %}

{% if banner_carousel %}{% include "banners/_banner_carousel.html" %}{% endif %}

{% if banner_offers %}{% include "banners/_banner_offers.html" %}{% endif %}

<div class="individual_room_gallery">
    {% for room_picture in actual_section.pictures %}
        <div {% if not loop.first %}class="room_picture_element"{% endif %}>
            <a  href="{{ room_picture|safe }}=s800" data-fancybox="{{ actual_section.title|safe }}" data-caption="{{ actual_section.title|safe }} - {{loop.index}}/{{ actual_section.pictures|length }}" title="{{ actual_section.title|safe }}">
                {% if not loop.first %}
                    <img class="room_image" src="{{ room_picture|safe }}">
                {% endif %}
            </a>
        </div>
    {% endfor %}
</div>

{% if iframe_map %}<div class="iframe_map_wrapper">{{ iframe_map|safe }}</div>{% endif %}

<script type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions_mobile.js?v=1.14"></script>

<script type="text/javascript">
$(window).load(function(){

    $(".myFancyPopup").fancybox({
        maxWidth: '100%',
        maxHeight: '100%',
        fitToView: false,
        width: '100%',
        height: '70%',
        padding: 5,
        type: 'image'
    });

});
</script>