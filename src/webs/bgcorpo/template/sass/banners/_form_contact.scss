.contact_form_wrapper,.contact_form_cv_wrapper {
  padding: 20px 0 100px;
  overflow: hidden;

  h3 {
    @include title_styles;
    text-align: center;
    padding-bottom: 50px;
  }

  #contact {
    display: block;
    margin: auto;

    .info {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      position: relative;
    }

    .contInput {
      display: inline-block;
      float: left;
      padding: 10px 0 10px 20px;
      position: relative;

     /* &:nth-of-type(-n+3) {
        width: calc((100% - 5px) / 3);
        padding-top: 20px;
      }

      &:nth-of-type(4) {
        width: 100%;
      }

      &:nth-of-type(3), &:nth-of-type(5) {
        margin-right: 0;
      }*/

      input:not([type="checkbox"]), select, textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 14px;
        border-width: 0;
        width: 100%;
        margin-bottom: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }
      &.hotels_select{
        display: none;
      }
      &.hotels_select,
      &.area {
        grid-column: 1 / span 4;
      }
    }
    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 20px 50px;
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $black;
    }

    #contact-button {
      @include btn_styles;
      position: absolute;
      right: 0;
      bottom: 20px;
    }
  }
}

.contact_form_cv_wrapper {
  #contact {
    .contInput {
      &.area, &.hotels_select, &.file, &.captcha {
        display: block;
        grid-column: 1 / span 2;
      }

      label.error {
        color: #e07373;
        font-size: 12px;
        position: absolute;
        bottom: 10px;
        display: block;
      }

      &.privacy {
        label.error {
          bottom: -10px;
        }
      }

      select, input {
        font-family: $text_family;

        &.error {
          outline: 1px solid #e07373;
        }
      }

      &.hotels_select {
        position: relative;

        &:after {
          content: "\f078";
          font-family: "Font Awesome 5 Pro";
          position: absolute;
          bottom: 44px;
          right: 20px;
          color: #343434;
          font-size: 14px;
          font-weight: 700;
        }
      }
    }

    .info {
      grid-template-columns: repeat(2, 1fr);
    }
    #contact-button{
      border:none;
    }
  }
}