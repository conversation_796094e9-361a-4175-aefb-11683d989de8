.banner_images_wrapper {
  margin-top: 0px;
  margin-bottom: 200px;

  .background {
    position: absolute;
    top: 500px;
    right: 0;
    height: 420px;
  }

  .content_wrapper {
    font-family: $title_family;
    color: $corporate_1;
    position: relative;
    margin-top: -165px;
    margin-bottom: -120px;
    .filter_tabs {
        right: 85px;
      .tab {
        width: 175px;
        text-align: center;
        font-size: 12px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        margin-bottom: 3px;
        padding: 8px 0;
        text-transform: uppercase;
        font-family: Raleway, sans-serif;
        cursor: pointer;
        clear: both;
        &.active {
              color: #c1a47c;
        }
        &:hover{
          opacity: .8;
        }
      }
    }

    .images_wrapper {
      padding: 0 50px;
      clear: both;
      background: white;
      padding: 90px 40px;
      position: relative;
      top: 0;
      clear: both;
      height: 732px;
      margin: 0px calc((100% - 1296px)/2);
      width: 1293px;
      .filter_tabs {
            right: 88px;
            top: 110px;
            position: absolute;
            z-index: 100;

      .tab {
        width: 175px;
        text-align: center;
        font-size: 12px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        margin-bottom: 3px;
        padding: 8px 0;
        text-transform: uppercase;
        font-family: Raleway, sans-serif;
        cursor: pointer;
        clear: both;
        &.active {
              color: #c1a47c;

        }
        &:hover{
          opacity: .8;
        }
      }
    }
      .images {
        position: absolute;
        opacity: 0;
        transition: all .3s;
        width: 90%;
        left: 50%;
        transform: translate(-50%, 0%);

        .image {
          position: relative;

          .picture_wrapper {
            height: 700px;
            position: relative;

            &:after {
              content: "";
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              z-index: 2;
              opacity: 0;
              transition: all .3s;
            }

            &:before {
              content: "";
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;

              z-index: 2;
              transition: all .3s;
            }

            img {
              @include cover_image;
              position: relative;
              z-index: 1;
              object-fit: cover;
            }

            .image_desc {
              font-size: 18px;
              letter-spacing: 0.36px;
              line-height: 25px;
              font-weight: 500;
              margin-left: 20px;
              position: absolute;
              bottom: 110px;
              color: white;
              font-family: $text_family;
              padding: 0 40px;
              transition: all .3s;
              opacity: 0;
              z-index: 3;

              @media (min-width: 900px) and (max-width: 1530px) {
                padding: 0 20px;
                bottom: 90px;
              }
            }
          }
        }

        &.active {
          opacity: 1;
          z-index: 3;
        }



        .owl-nav {
          transform: translate(0px, -50%);
          flex-flow: column;
          position: absolute;
          top: 50%;
          display: flex;
          position: absolute;
          color: white !important;
          font-size: 58px;
          flex-direction: row;
          cursor: pointer;
          .owl-next {
            background: transparent;
            padding: 0 !important;
            left: 1083px;
            position: relative;
            display: block;
        }  .owl-prev {
            background: transparent;
            padding: 0;
            left: 18px;
            position: relative;
            display: block;
        }
        }


      }
    }
    .sub-images_wrapper {
      padding: 0 50px;
      clear: both;
      background: white;
      padding: 90px 40px;
      position: relative;
      top: 0;
      clear: both;
      height: 52px;
      margin: 0px calc((100% - 1296px)/2);
      width: max-content;
      .owl-stage{
        display: flex;
        flex-wrap: nowrap;
        width: 1232px !important;
        justify-content: center;
      }
      .sub-images {
        position: absolute;
        opacity: 0;
        transition: all .3s;
        width: 90%;
        left: 50%;
        transform: translate(-50%, 0%);
        .owl-nav{
          display:none !important;
        }
        .owl-item.active{
          width: 80px !important;
        }
        .owl-item:not(.active){
          display: none !important;
        }
        .sub-image {
          position: relative;

          .picture_wrapper {
            height: 50px;
            position: relative;

            &:after {
              content: "";
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              z-index: 2;
              opacity: 0;
              transition: all .3s;
            }

            &:before {
              content: "";
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;

              z-index: 2;
              transition: all .3s;
            }

            img {
              @include cover_image;
              position: relative;
              z-index: 1;
              object-fit: cover;
            }



            .image_desc {
              font-size: 18px;
              letter-spacing: 0.36px;
              line-height: 25px;
              font-weight: 500;
              margin-left: 20px;
              position: absolute;
              bottom: 110px;
              color: white;
              font-family: $text_family;
              padding: 0 40px;
              transition: all .3s;
              opacity: 0;
              z-index: 3;

              @media (min-width: 900px) and (max-width: 1530px) {
                padding: 0 20px;
                bottom: 90px;
              }
            }
          }
        }

        &.active {
          opacity: 1;
          z-index: 3;
        }

        .owl-prev {
          color: #31314C;
          background: transparent;
          padding: 0;
          left: 0;
          position: relative;
          display: block;
        }

        .owl-nav {
          right: -3.5%;
          transform: translate(0px, -50%);
          flex-flow: column;
          position: absolute;
          top: 50%;
          display: flex;

          img {
            width: 20px;
          }
        }

        .owl-next {
          color: #31314C;
          background: transparent;
          padding: 0 !important;
          left: 0;
          position: relative;
          display: block;
          margin-top: 10px;
        }
      }
    }
  }


}

