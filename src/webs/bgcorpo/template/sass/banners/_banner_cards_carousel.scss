.banner_cards_carousel_wrapper {
  padding: 70px 0;
  position: relative;

  .bg_image {
    @include full_size;
    background-color: white;


    img {
      @include cover_image;
      filter: grayscale(100%);
      opacity: .1;
    }

    &::before {
      position: absolute;
      content: '';
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
      z-index: 1;
    }
  }

  .cards_container {
    margin: 0 auto;
    padding: 0;
    width: 1200px;
    position: relative;
    z-index: 1;

    .top_banner {
      margin-bottom: 20px;

      .content_title {
        text-align: center;

        .title {
          @include title_styles;
          font-size: 36px;
        }
      }
    }


    .cards_wrapper {
      display: flex;
      justify-content: space-between;

      &.owl-carousel {
        .owl-stage-outer {
          display: flex;
        }

        .owl-stage {
          padding: 30px 0 50px;
          display: flex;
          height: 100%;

          .owl-item {
            height: 100%;
            padding: 20px;
            display: flex;

            .card {
              width: 100%;
            }
          }
        }
      }


      .card {
        width: calc((100% / 3) - 20px);
        margin-right: 20px;
        background-color: white;
        box-shadow: 0px 4px 15px 0px rgba(0,0,0,0.5);
        display: flex;
        flex-direction: column;

        &:last-child {
          margin-right: 0;
        }

        .picture_wrapper {
          height: 280px;

          img {
            @include cover_image;
          }
        }

        .card_content {
          padding: 30px 30px 80px;
          flex: 1;

          .content_title {
            text-align: center;
            margin-bottom: 20px;

            .title {
              @include title_styles;
              font-weight: 400;

              span {
                text-transform: uppercase;
              }
            }
          }

          .desc {
            @include text_styles;
            text-align: center;
            margin-bottom: 20px;
          }

          .links_wrapper {
            display: inline-block;
            text-align: center;
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);

            .btn_link_more {
              @include btn_styles;
            }
          }
        }
      }


      .owl-nav {
        > div {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);

          i {
            font-size: 35px;
            color: $corporate_1;
            font-weight: 300;
          }

          &.owl-prev {
            left: -80px;
          }

          &.owl-next {
            right: -80px;
          }
        }
      }
    }
  }
}