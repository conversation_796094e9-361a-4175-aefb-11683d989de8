<div class="scapes-blocks">
    {% for block in offers %}
        <div class="block {% cycle 'row1' 'row2' %}">
            <div class="description">
                <h3 class="title-module">{{block.name|safe}}</h3>
                <ul>
                    <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva">{{ T_reservar }}</a> </li>
                    <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                </ul>
            </div>
            <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                <img src="{{block.picture}}=s1000">
            </a>

            <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }} offer_hidden_popup" style="display: none">
                <h3 class="offer_title_hidden">{{ block.name|safe }}</h3>
                {{block.description|safe}}
            </div>
        </div>

    {% endfor %}
</div>

<script>
    $(function(){
        max_scape_block = 0;
        $(".scapes-blocks .block .description").each(function(){
            var actual_scape_block = $(this).height();
            if (actual_scape_block > max_scape_block){
                max_scape_block = actual_scape_block;
            }
        });

        $(".scapes-blocks .block .description").height(max_scape_block);
    })
</script>