{# Ticks #}
{% if ticks_elements and not interior %}
    <div class="ticks_elements_wrapper">
        {% for tick_element in ticks_elements %}
            <div class="tick_element column4">
                <h4 class="tick_title" style="background: url({{ tick_element.servingUrl|safe }});">{{ tick_element.title|safe }}</h4>
                <div class="tick_description">{{ tick_element.description|safe }}</div>
            </div>
        {% endfor %}
    </div>
{% endif %}


{# Content access #}
{% if content_access %}
    </div>
    <div class="content_access_wrapper">
        <div class="container12 center_wrapper_content">
            {{ content|safe }}
        </div>
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}


{# Normal content #}
{% if content_subtitle %}
    </div>
    <div class="content_subtitle_wrapper">
        <div class="container12 reduced_width">
            <h3 class="content_subtitle_title">{{ content_subtitle.subtitle|safe }}</h3>
            <div class="content_subtitle_content">{{ content_subtitle.content|safe }}</div>
        </div>
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}

{# Ofertas #}
{% if offers %}
    {% include "offers.html" %}
{% endif %}

{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper container12">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}
            {{ location_html.content|safe }}
        </div>

        <div class="form-contact column6">
            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>
    </div>

    {% if iframe_google_map %}
        <div class="iframe-google-maps-wrapper">
            {{ iframe_google_map.content|safe }}
        </div>
    {% endif %}
{% endif %}


{# Cycle Banners #}
{% if cycle_banners %}
    <div class="cycle_banners_wrapper">
        <div class="container12">
            {% for x in cycle_banners %}
                <div class="cycle_element {% cycle 'align_left' 'align_right' %}">

                    <div class="exceded"><img src="{{ x.servingUrl|safe }}"></div>
                    <div class="cycle_text_wrapper">
                        <div class="center_div">
                            <h3 class="cycle_title">{{ x.title|safe }}</h3>

                            <div class="cycle_description">
                                {{ x.description|safe }}
                                {% if x.button %}
                                    <a href="{{ x.linkUrl|safe }}" class="cycle_link">{{ x.button }}</a>
                                {% else %}
                                    <a href="#hide_me_{{ forloop.counter }}" class="cycle_popup cycle_link">{{ T_ver_mas|upper }}</a>
                                    <div id="hide_me_{{ forloop.counter }}" style="display: none">
                                        <h3 class="cycle_title">{{ x.title|safe }}</h3>
                                        <div class="cycle_description">{{ x.description|safe }}</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}


{# Blocks x3 #}
{% if blocks_x3 %}
    <div class="blocks_x3_wrapper">
        <h4 class="blocks_x3_main">{{ blocks_x3.section.subtitle|safe }}</h4>
        {% for block_element in blocks_x3.pictures %}
            {% if forloop.last %}
                <a href="#data" class="button-promotion">
                    <div class="block_x3_element" {% if forloop.counter|divisibleby:"3" %}style="margin-right: 0"{% endif %}>
                        <div class="black_overlay"></div>
                        <img class="image_block_x3" src="{{ block_element.servingUrl|safe }}">
                        <div class="block_x3_title">{{ block_element.title|safe }}</div>
                    </div>
                </a>
            {% else %}
                {% if block_element.linkUrl %}<a href="{{ block_element.linkUrl|safe }}">{% endif %}
                    <div class="block_x3_element" {% if forloop.counter|divisibleby:"3" %}style="margin-right: 0"{% endif %}>
                        <div class="black_overlay"></div>
                        <img class="image_block_x3" src="{{ block_element.servingUrl|safe }}">
                        <div class="block_x3_title">{{ block_element.title|safe }}</div>
                    </div>
                {% if block_element.linkUrl %}</a>{% endif %}
            {% endif %}
        {% endfor %}
    </div>
{% endif %}


{# Ticks #}
{% if ticks_elements and interior %}


    <div class="ticks_elements_wrapper">
        <div class="line_bar"></div>

        {% for tick_element in ticks_elements %}
            <div class="tick_element column4">
                <h4 class="tick_title" style="background: url({{ tick_element.servingUrl|safe }});">{{ tick_element.title|safe }}</h4>
                <div class="tick_description">{{ tick_element.description|safe }}</div>
            </div>
        {% endfor %}
    </div>
{% endif %}