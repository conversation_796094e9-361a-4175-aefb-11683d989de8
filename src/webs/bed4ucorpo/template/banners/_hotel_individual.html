<div class="hotel_content_wrapper" id="hotel_content_wrapper">
    <div class="hotel_sections_links" id="hotel_sections_links">
        {% if content_subtitle and not common_section %}
            <span class="link active" data-target="hotel_content_wrapper">{{ T_hotel|safe }}</span>
        {% endif %}
        {% if apartment %}
            <span class="link" data-target="hotel_rooms_full_wrapper">{{ T_apartamentos }}</span>
        {% else %}
            {% if hotel_rooms %}
                <span class="link" data-target="hotel_rooms_full_wrapper">{{ T_habitaciones }}</span>
            {% endif %}
        {% endif %}
        {% if hotel_offers %}
            <span class="link" data-target="hotel_offers_full_wrapper">{{ T_ofertas }}</span>
        {% endif %}
        {% if banner_services_pics %}
            <span class="link" data-target="banner_services_wrapper">{{ T_servicios }}</span>
        {% endif %}
        {% if hotel_gallery %}
            <span class="link" data-target="banner_gallery_full_wrapper">{{ T_galeria|safe }}</span>
        {% endif %}
        {% if services_icons_hotel %}
            <span class="link" data-target="services_icons_hotel_full_wrapper">{{ T_servicios }}</span>
        {% endif %}
        {% if hotel_map %}
            <span class="link" data-target="hotel_map_full_wrapper">{{ T_ubicacion|safe }}</span>
        {% endif %}
        {% if extra_links_pics %}
            {% if extra_links_pics|length > 1 %}
                <div class="dropdown extra_link">
                    <div class="dropdown-button">{{ T_tour_virtual }}</div>
                    <div class="dropdown-content">
                        {% for pic in extra_links_pics %}
                            {% if pic.title and pic.linkUrl %}
                                <a href="{{ pic.linkUrl|safe }}"
                                   {% if 'http' in pic.linkUrl %}target="_blank"{% endif %}>{{ pic.title|safe }}</a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                {% for pic in extra_links_pics %}
                    {% if pic.title and pic.linkUrl %}
                        <a href="{{ pic.linkUrl|safe }}" {% if 'http' in pic.linkUrl %}target="_blank"{% endif %}
                           class="extra_link">{{ pic.title|safe }}</a>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endif %}

    </div>
</div>

<script>
    $(window).on('load', function () {
        $("#hotel_sections_links .link").click(function () {
            const target = $(this).data("target");
            $("#hotel_sections_links .link").removeClass("active");
            $(this).addClass("active");

            let scrollTop;

            {% if is_mobile %}
                scrollTop = $("#" + target).offset().top;
            {% else %}
                scrollTop = $("#" + target).offset().top - $("#full_wrapper_booking").outerHeight() - $("#hotel_sections_links").outerHeight();
            {% endif %}

            $('html, body').stop().animate({
                scrollTop: scrollTop
            }, 1500);
        })

        {% if not is_mobile %}
            fixHotelLinks();
            changeActiveHotelSectionLink();
        {% endif %}
    })

    {% if not is_mobile %}
        $(window).scroll(function () {
            fixHotelLinks();
            changeActiveHotelSectionLink();
        });
    {% endif %}

    function fixHotelLinks() {
        const actual_position = $(window).scrollTop();
        const hotel_links_position = $("#hotel_content_wrapper").offset().top + $("#hotel_content_wrapper .content_title").outerHeight() - $("#full_wrapper_booking").outerHeight();
        const hotel_sections_links = $("#hotel_sections_links");
        const hotel_links_fixed = hotel_sections_links.hasClass('fixed');

        if ((actual_position > hotel_links_position) && (!hotel_links_fixed)) {
            hotel_sections_links.addClass('fixed');
        }
        if ((actual_position < hotel_links_position) && (hotel_links_fixed)) {
            hotel_sections_links.removeClass("fixed");
        }
    }

    function changeActiveHotelSectionLink() {
        $("#hotel_sections_links .link").each(function () {
            const link_target = $(this).data("target");
            const target_top = $("#" + link_target).offset().top - $("#full_wrapper_booking").outerHeight() - $("#hotel_sections_links").outerHeight() - 10;
            const actual_position = $(window).scrollTop();

            if (actual_position > target_top) {
                $("#hotel_sections_links .link").removeClass("to_active");
                $(this).addClass("to_active");
            }
        })

        $("#hotel_sections_links .link.active").removeClass("active");
        $("#hotel_sections_links .link.to_active").addClass("active");
    }
</script>