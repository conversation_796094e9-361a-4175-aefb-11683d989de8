<div class="hotel_rooms_full_wrapper" id="hotel_rooms_full_wrapper">
    {% if apartment %}
        <h3 class="content_title">
            {{ T_apartamentos }}
        </h3>
    {% else %}
        <h3 class="content_title">
            {{ T_habitaciones }}
        </h3>
    {% endif %}
    <div class="hotel_rooms_wrapper">
        {% for room in hotel_rooms %}
            <div class="room">
                {% if room.picture %}
                    <div class="room_gallery_wrapper">
                        {% if room.pictures %}
                            <a href="{{ room.pictures.0.servingUrl|safe }}" class="gallery_icon" {% if is_mobile %}data-fancybox="room_gallery_{{ room.key }}"{% else %}rel="lightbox[room_gallery_{{ room.key }}]"{% endif %}>
                                <i class="fal fa-images"></i>
                            </a>
                            {% for pic in room.pictures[1:] %}
                                <a href="{{ pic.servingUrl|safe }}" {% if is_mobile %}data-fancybox="room_gallery_{{ room.key }}"{% else %}rel="lightbox[room_gallery_{{ room.key }}]"{% endif %} style="display: none"></a>
                            {% endfor %}
                            <div class="room_gallery owl-carousel">
                                {% for pic in room.pictures %}
                                    {% if pic.servingUrl %}
                                        <div class="img_wrapper">
                                            <img src="{{ pic.servingUrl|safe }}=s600">
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="room_gallery">
                                <div class="img_wrapper">
                                    <img src="{{ room.picture|safe }}=s600">
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
                <div class="room_content">
                    {% if room.name %}
                        <span class="title">{{ room.name|safe }}</span>
                    {% endif %}
                    {% if room.description %}
                        <div class="text">{{ room.description|safe }}</div>
                    {% endif %}
                    <div class="buttons_wrapper">
                        <span class="open_room_modal btn_personalized_1" data-target="{{ room.key|safe }}">{{ T_ver_detalles }}</span>
                        <a href="#data" class="button-promotion btn_personalized_1" data-namespace="{{ section_namespace|safe }}">{{ T_reservar }}</a>

                        <div class="room_modal" id="{{ room.key|safe }}">
                            <div class="modal_content">
                                <div class="room_modal_close"><span></span><span></span></div>
                                <div class="content">
                                    <div class="room_modal_wrapper">
                                        {% if room.highlight_text %}
                                            <div class="highlight_text">
                                                <i class="fas fa-star"></i>
                                                {{ room.highlight_text|safe }}
                                            </div>
                                        {% endif %}
                                        {% if room.picture %}
                                            <div class="room_gallery_popup owl-carousel">
                                                {% if room.pictures|length < 2 %}
                                                <div class="img_wrapper">
                                                    <img src="{{ room.picture|safe }}=s600">
                                                </div>
                                                {% else %}
                                                {% for pic in room.pictures %}
                                                    {% if pic.servingUrl %}
                                                        <div class="img_wrapper">
                                                            <img src="{{ pic.servingUrl|safe }}=s600">
                                                        </div>
                                                    {% endif %}
                                                {% endfor %}
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                        <div class="room_modal_content">
                                            {% if room.name %}
                                                <span class="title">{{ room.name|safe }}</span>
                                            {% endif %}
                                            {% if room.description %}
                                                <div class="text">{{ room.description|safe }}</div>
                                            {% endif %}
                                            {% if room.room_icons %}
                                                <div class="room_icons">
                                                    {% for icon in room.room_icons %}
                                                        <div class="icon_wrapper">
                                                            {% if icon.ico %}<i class="{{ icon.ico|safe }} icon"></i>{% endif %}
                                                            {% if icon.description %}<span class="icon_text">{{ icon.description|safe }}</span>{% endif %}
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <div class="room_extra_info">
                                                {% if room.measure %}<span class="info"><i class="fal fa-ruler"></i>{{ room.measure|safe }}</span>{% endif %}
                                                {% if room.occupancy %}<span class="info"><i class="fal fa-user"></i>{{ room.occupancy|safe }}</span>{% endif %}
                                            </div>
                                             <div class="modal_buttons_wrapper">
                                                <a href="#data" class="button-promotion booking_close_room_popup btn_personalized_1" data-namespace="{{ section_namespace|safe }}">{{ T_reservar }}</a>
                                             </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>



<script>
    $(window).on('load', function () {
        $(".room_gallery.owl-carousel, .room_gallery_popup.owl-carousel").owlCarousel({
            loop: true,
            nav: false,
            dots: true,
            items: 1,
            navSpeed: 500,
            autoplay: false
        });

        $(".open_room_modal").click(function (e) {
            e.preventDefault();
            const room_modal_target = $(this).data("target");
            $(".room_modal#" + room_modal_target).addClass("active");
            $("body").addClass("modal_opened");
        });
        $(".room_modal_close").click(function (e) {
            e.preventDefault();
            $(".room_modal").removeClass("active");
            $("body").removeClass("modal_opened");
        });
        $(".room_modal").click(function (e) {
            if($(e.target).closest(".modal_content").length < 1) {
                $(".room_modal").removeClass("active");
                $("body").removeClass("modal_opened");
             }
        });
    });
</script>