<div class="banner_icons_multiple container12">
    <div class="banner_hotel_names {% if is_mobile %}owl-carousel{% endif %}">
        {% for hotel_name, hotel_icons in banner_icons_multiple.items() %}
            <span class="hotel_name {% if loop.first %}active{% endif %}">{{ hotel_name }}</span>
        {% endfor %}
    </div>

    <div class="banner_hotel_icons owl-carousel">
        {% for hotel_name, hotel_icons in banner_icons_multiple.items() %}
            <div class="banner_icons">
                {% for column, column_icons in hotel_icons.items() %}
                    <div class="col {{ col }}">
                        {% for icon_element in column_icons %}
                            <div class="icon_element">
                                <div class="icon">
                                    {% if icon_element.icon %}
                                        <i class="{{ icon_element.icon|safe }}"></i>
                                    {% endif %}
                                </div>
                                <div class="title">
                                    {% if icon_element.title %}
                                        {{ icon_element.title|safe }}
                                    {% endif %}
                                </div>
                                <div class="toggle">
                                    <i class="toggle_open fas fa-plus"></i>
                                    <i class="toggle_close fas fa-minus"></i>
                                </div>
                                <div class="desc">
                                    {% if icon_element.description %}
                                        {{ icon_element.description|safe }}
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).on('load', function () {
        $(".banner_icons_multiple .icon_element .toggle").click(function () {
            $(this).closest(".icon_element").toggleClass("opened");
        });

        $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").owlCarousel({
            loop: false,
            nav: true,
            dots: false,
            items: 1,
            navSpeed: 500,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
        });

        {% if not is_mobile %}
            $(".banner_icons_multiple .banner_hotel_names .hotel_name").click(function () {
                var index = $(this).index();
                $(".banner_icons_multiple .banner_hotel_names .hotel_name").removeClass("active");
                $(this).addClass("active");
                $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").trigger('to.owl.carousel', [index, 500]);
            });

            $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").on('changed.owl.carousel', function (event) {
                var index = event.item.index;
                $(".banner_icons_multiple .banner_hotel_names .hotel_name").removeClass("active");
                $(".banner_icons_multiple .banner_hotel_names .hotel_name").eq(index).addClass("active");
            });
        {% endif %}

        {% if is_mobile %}
            $(".banner_icons_multiple .banner_hotel_names").owlCarousel({
                loop: false,
                nav: true,
                dots: false,
                dragging: false,
                items: 1,
                navSpeed: 500,
                navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            });

            $(".banner_icons_multiple .banner_hotel_names .owl-prev").click(function () {
                $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").trigger('prev.owl.carousel');
            });

            $(".banner_icons_multiple .banner_hotel_names .owl-next").click(function () {
                $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").trigger('next.owl.carousel');
            });

            $(".banner_icons_multiple .banner_hotel_icons.owl-carousel").on('dragged.owl.carousel', function (event) {
                var index = event.item.index;
                $(".banner_icons_multiple .banner_hotel_names").trigger('to.owl.carousel', [index, 500]);
            });
        {% endif %}
    });
</script>