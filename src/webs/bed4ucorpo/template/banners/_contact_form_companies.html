<div class="contact_form_wrapper contact_form_companies">
    <div class="container12">
        <h3>{% if contact_form_companies.subtitle %}{{ contact_form_companies.subtitle|safe }}{% else %}{{ T_formulario_contacto }}{% endif %}</h3>
        <form name="contact" id="contact" method="post" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>
            <div class="info">
                {% for input_item in contact_form_companies.input_list %}
                    {% if input_item.type != 'checkbox' %}
                        <div class="contInput {% if input_item.type %}{{ input_item.type }}{% endif %}">
                            {% if input_item.icon %}<i class="{{ input_item.icon }}" aria-hidden="true"></i>{% endif %}
                            {% if not input_item.type %}
                                <input type="text" {% if input_item.altText %}id="{{ input_item.altText }}" name="{{ input_item.altText }}"{% endif %}
                                       class="bordeInput" value="" {% if input_item.title %}placeholder="{{ input_item.title|safe }}"{% endif %} {% if input_item.required %}required{% endif %}/>
                            {% else %}
                                {% if input_item.type == 'textarea' %}
                                    <textarea type="text" {% if input_item.altText %}id="{{ input_item.altText }}" name="{{ input_item.altText }}"{% endif %} class="bordeInput"
                                      value="" {% if input_item.title %}placeholder="{{ input_item.title|safe }}"{% endif %} {% if input_item.required %}required{% endif %}></textarea>
                                {% endif %}
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="checkbox_inputs_container">
                            <div class="selector_container">
                                <div class="title">
                                    <i class="fa fa-building" aria-hidden="true"></i>
                                    <div class="checkbox_title">{{ input_item.title|safe }}</div>
                                </div>
                                <i class="fas more fa-plus-circle" aria-hidden="true"></i>
                                <i class="fal less fa-minus-circle" aria-hidden="true" style="display:none"></i>
                            </div>
                            <div class="inputs_container" style="display: none">
                                {% set options = input_item.options.split(';') %}
                                {% for option in options %}
                                    <div class="checkbox_input">
                                        <input type="checkbox" id="{{ option }}" name="{{ option }}" value="True"/>
                                        <label for="{{ option }}">{{ option }}</label>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src='https://www.google.com/recaptcha/api.js'></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}
                <div class="contInput policy-terms">
                    <input type="checkbox" id="accept-term" name="accept_term" required/>
                    {% if is_mobile %}
                        <a class="myFancyPopup fancybox.iframe" data-fancybox data-options='{"caption" : "{{ T_politica_de_privacidad }}", "src" : "/{{ language_code }}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" href="/{{ language_code }}/?sectionContent=politica-de-privacidad.html">{{ T_lopd }}</a>
                    {% else %}
                        <a class="myFancyPopup fancybox.iframe" href="{{ language_code }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                    {% endif %}
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
            </div>
        </form>
    </div>
</div>
<script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">
    $(window).load(function () {

        $('.selector_container').on('click', function () {
            $(this).find('.more').toggle();
            $(this).find('.less').toggle();
            $(this).parent().find('.inputs_container').slideToggle();
        });

        $("#contact-button").click(function () {
            let form = $(this).closest("#contact");

            if (form.valid()) {
                let form_data = form.serialize();
                if ($("#g-recaptcha-response").val() || !$('.g-recaptcha').length) {
                $.post(
                    "/utils/?action=contact", form_data,
                    function (data) {
                        alert($.i18n._("gracias_contacto"));
                        form.trigger("reset");
                    }
                );
                } else {
                      $(".g-recaptcha > div").css('border', '1px solid red');
                    }
                }
        });

        {% if not is_mobile and not user_isIpad %}
            function contact_form_fx() {
                $(".contact_form_wrapper .container12").addnimation({parent:$(".contact_form_wrapper"),class:"fadeInUp", classOut: "fadeOutDown"});
            }
            contact_form_fx();
            $(window).scroll(contact_form_fx);
        {% endif %}
    });
</script>