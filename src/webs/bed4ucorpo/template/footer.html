<footer>
    <div id="footer_to_top">
        <i class="fal fa-chevron-up"></i>
    </div>
    <div class="footer_logo">
        <a href="{{host|safe}}/">
            <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
        </a>
    </div>
    {% if extra_custom_logotype %}
        <div class="extra_footer_logo">
            {% for logotype in extra_custom_logotype %}
                {% if logotype.linkingUrl %}
                    <a href="{{ logotype.linkingUrl }}" target="_blank"> <img src="{{ logotype.servingUrl|safe }}">
                    </a>
                {% else %}
                    <img src="{{ logotype.servingUrl|safe }}">
                {% endif %}
            {% endfor %}
        </div>
    {% endif %}
    <div class="footer_columns_wrapper">
        {% for column in footer_columns %}
            <div class="footer_column">
                {% if column.title %}
                    <h4 class="title">{{ column.title|safe }}</h4>
                {% endif %}
                {% if column.description %}
                    <div class="desc">
                        {{ column.description|safe }}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
    <div class="footer_legal_text_wrapper">
        {% if texto_legal %}
            <div class="legal_text">{{ texto_legal|safe }}</div>
        {% endif %}
        <div class="footer_links_wrapper">
            {% for x in policies_section %}
                {% if x.custom_link %}
                <a href="{{ x.custom_link }}">{{ x.title|safe }}</a> {% if not is_mobile %}|{% endif %}
                {% else %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}"
                   {% if is_mobile %}data-fancybox data-options='{"caption" : "", "src" : "/{{language}}/?sectionContent={{ x.friendlyUrl }}", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" rel="nofollow" {% else %}class="myFancyPopup fancybox.iframe"{% endif %}>{{ x.title|safe }}</a> {% if not is_mobile %}|{% endif %}
                {% endif %}
            {% endfor %}
            <!--<a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> {% if not is_mobile %}|{% endif %}
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> {% if not is_mobile %}|{% endif %}
            <a target="_blank" href="/rss.xml">RSS</a>-->
            <div class="social_media_wrapper">
                {% if facebook_id %}
                    <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank"> <i class="fa fa-facebook"></i> </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="https://www.instagram.com/{{ instagram_id }}/" target="_blank"> <i class="fa fa-instagram"></i> </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank"> <i class="fa fa-twitter"></i> </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank" rel="publisher"> <i
                            class="fa fa-google-plus"></i> </a>
                {% endif %}
                {% if flickr_id %}
                    <a href="http://www.flickr.com/photos/{{ flickr_id }}" target="_blank"> <i class="fa fa-flickr"></i> </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/channel/{{ youtube_id }}" target="_blank"> <i class="fa fa-youtube"></i> </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="https://es.pinterest.com/{{ pinterest_id }}" target="_blank"> <i class="fa fa-pinterest"></i> </a>
                {% endif %}
                {% if linkedin_id %}
                    <a href="https://www.linkedin.com/{{ linkedin_id }}" target="_blank"> <i class="fa fa-linkedin"></i> </a>
                {% endif %}
                {% if whatsapp_id %}
                    <a href="http://wa.me/{{ whatsapp_id }}" target="_blank"> <i class="fa fa-whatsapp"></i> </a>
                {% endif %}
                {% if line_id %}
                    <a href="https://line.me/R/ti/p/{{ line_id }}" target="_blank"> <i class="fab fa-line"></i> </a>
                {% endif %}
                {% if tripadvisor_id %}
                    <a href="https://www.tripadvisor.es/{{ tripadvisor_id }}" target="_blank"> <i class="fa fa-tripadvisor"></i> </a>
                {% endif %}
            </div>
        </div>
    </div>
</footer>