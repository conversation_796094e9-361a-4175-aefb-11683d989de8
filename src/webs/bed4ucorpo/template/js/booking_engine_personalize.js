$(window).load(function () {
    fade_out_widget();
    prepare_guests_selector();
    set_occupancy_number();

    $(".guest_selector").click(function(){
        if(($(window).height()-380) > $(window).scrollTop()) {
            $(".room_list_wrapper").addClass("room_list_wrapper_up");
        } else {
            $(".room_list_wrapper").removeClass("room_list_wrapper_up");
        }
    });
    $(document).click(function (e) {
        if($(e.target).closest(".ui-datepicker-header").length < 1 &&
           $(e.target).closest(".datepicker_wrapper_element").length < 1 &&
           $(e.target).closest(".datepicker_wrapper_element_2").length < 1 &&
           $(e.target).closest(".datepicker_wrapper_element_3").length < 1 &&
           $(e.target).closest(".dates_selector_personalized").length < 1) {
            DP_extend_info.hide_datepicker_wrapper();
        }
    });

    OccupancyController.init();

    var title_selector = $('.hotel_selector').find('.hotel_selector_option');
    var title_destination = $('.destination_wrapper #destination_field');

    title_selector.each(function () {
        $(this).click(function () {
            title_destination.addClass('location_selected');
        });
    });
});
$("html").click(function(event){
    var not_parent_occupany_overlay = $(event.target).closest(".occupancy_overlay").length === 0,
        parent_occupancy_wrapper = $(event.target).closest("#occupancy_wrapper").length,
        not_parent_room_list_wrapper = $(event.target).closest(".room_list_wrapper").length === 0,
        not_parent_guest_selector = $(event.target).closest(".guest_selector").length === 0

    if($(event.target).closest(".destiny_selector_wrapper").length === 0 && $(event.target).closest(".destination_wrapper").length === 0) {
        $("body").removeClass('selecting_hotel');
        $(".destiny_selector_wrapper").removeClass('active');
    }
    if($(event.target).closest(".datepicker_wrapper_element").length === 0 && $(event.target).closest(".dates_selector_personalized").length === 0 && $(event.target).closest(".ui-widget-header").length === 0) {
        $(".datepicker_wrapper_element").slideUp();
        $("body").removeClass('datepicker_shown');
    }
    if(not_parent_occupany_overlay && not_parent_room_list_wrapper && not_parent_guest_selector && !parent_occupancy_wrapper) {
        $(".room_list_wrapper").slideUp();
        $("body").removeClass('occupancy_selection');
    }

    if($(event.target).closest(".datepicker_wrapper_element").length === 0 && $(event.target).closest(".dates_selector_personalized").length === 0 && $(event.target).closest(".ui-widget-header").length === 0 &&
        not_parent_occupany_overlay && not_parent_room_list_wrapper && not_parent_guest_selector) {
        if (!parent_occupancy_wrapper) {
            $("body").removeClass('occupancy_selection');
        }
        $("body").removeClass('datepicker_shown');
    }
});
 function _update_capacity_rooms_v2() {

      $(".paraty-booking-form").each(function () {
        var rooms_number = parseInt($(this).find("select.rooms_number").val()),
            adults_room_1 = $(this).find('select.adults_room_1').val(),
            children_room_1 = $(this).find('select.children_room_1').val(),
            babies_room_1 = $(this).find('select.babies_room_1').val(),
            adults_room_2 = $(this).find('select.adults_room_2').val(),
            children_room_2 = $(this).find('select.children_room_2').val(),
            babies_room_2 = $(this).find('select.babies_room_2').val(),
            adults_room_3 = $(this).find('select.adults_room_3').val(),
            children_room_3 = $(this).find('select.children_room_3').val(),
            babies_room_3 = $(this).find('select.babies_room_3').val();

        var final_string = '';

        if (rooms_number > 0) {
            final_string = [adults_room_1, children_room_1, babies_room_1].join("-");
        }

        if (rooms_number > 1) {
            final_string += ' + ' + [adults_room_2, children_room_2, babies_room_2].join("-");
        }

        console.log(final_string);
        $(this).find(".guest_selector .placeholder_text").html(final_string);
    });

}
function prepare_guests_selector() {
   $('body').on('click', '.guest_selector, .close_guesst_button, .save_guest_button', function() {
      toggle_guest_selector();
   });

   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

   $(".remove_room_element").click(function(){
      var actual_room_numbers = $("select.rooms_number").val();
      if (actual_room_numbers > 1){
         var target_room_number = parseInt(actual_room_numbers) - 1;
         $("select.rooms_number option").removeAttr('selected');
         $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
         $(".room" + actual_room_numbers).hide();
         $("select.rooms_number").val(target_room_number);
         $("select.rooms_number").selectric("refresh");
      }
      set_occupancy_number()
   });

   var add_room_html = "<div class='add_room'><i class='far fa-plus'></i></div>",
       remove_room_html = "<div class='remove_room'><i class='far fa-minus'></i></div>";

   $(".room_list_wrapper").append(add_room_html).append(remove_room_html);
   $(".add_room").click(function () {add_remove_room(true)});
   $(".remove_room").click(function() {add_remove_room(false)});
}

function add_remove_room(flag){
   let number_rooms = parseInt($("select.rooms_number").val()),
       sel_num = 0;
   if (flag && number_rooms < 3) {
       sel_num = number_rooms;
       $($(".rooms_number .selectricItems li").get(sel_num)).trigger("click");
   }
   if (!flag && number_rooms > 1) {
       sel_num = number_rooms - 2;
       $($(".rooms_number .selectricItems li").get(sel_num)).trigger("click");
   }
   set_occupancy_number();
}


function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
      console.log("showing");
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = $("select[name='numRooms']").val(),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

       if (number_of_rooms){
          for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
             var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
                 actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
                 actual_select_babies = $("select[name='babiesRoom" + room_loop + "']").val();

             if(actual_select_adults || actual_select_kids){
                adults_number += parseInt(actual_select_adults);
                kids_number += parseInt(actual_select_kids);
                babies_number += parseInt(actual_select_babies);
             }
          }
       }
       var target_placeholder = $(".guest_selector .placeholder_text"),
           placeholder_string = "";


       adults_number = parseInt(adults_number);
       kids_number = parseInt(kids_number);
       babies_number = parseInt(babies_number);

       adults_str = adults_number != 1 ? adults_tag : adult_tag;
       kids_str = kids_number != 1 ? kids_tag : kid_tag;

       placeholder_string += adults_number + " " + adults_str;

       if(!$(".adults_only_selector").length){
          placeholder_string += " + " + kids_number + " " + kids_str;
       }

       target_placeholder.html(placeholder_string);
}


var OccupancyController = function () {
    return {
        init: function () {
            this.prepare_listeners();
            _update_capacity_rooms_v2();
        },

        prepare_listeners: function () {
            var occupancy_wrapper = $("#occupancy_wrapper");
            occupancy_wrapper.find(".plus").click(function () {
                OccupancyController.increase_element($(this));
            });

            occupancy_wrapper.find(".minus").click(function () {
                OccupancyController.decrease_element($(this));
            });

            occupancy_wrapper.find(".input_simulator").change(function () {
                OccupancyController.ages_checker($(this));
            });

            $(".age_option").click(this.select_kid_age);

            this.prepare_occupancy_overlay();
            $(".occupancy_overlay").click(function () {
                var actual_position = $(window).scrollTop(),
                    booking_top = $("#full_wrapper_booking").offset().top - 200;
                if (actual_position > booking_top) {
                    $("#occupancy_wrapper").addClass("occupancy_down");
                } else {
                    $("#occupancy_wrapper").removeClass("occupancy_down");
                }
                $("body").toggleClass('occupancy_selection');
            });

            $("body > .black_overlay").click(function () {
                $("body").removeClass('occupancy_selection');
            });
        },

        prepare_occupancy_overlay: function () {
            var occupancy_overlay = $("<div class='occupancy_overlay'></div>");
            $(".rooms_number_wrapper").append(occupancy_overlay.clone());
            $(".guest_selector").append(occupancy_overlay.clone());
        },

        update_rooms_number: function (target_num_rooms) {
            var occupancy_wrapper = $("#occupancy_wrapper");
            for (var x = 1; x <= 3; x++) {
                if (x <= target_num_rooms) {
                    occupancy_wrapper.find(".room_num_" + x).removeClass('hide');
                } else {
                    occupancy_wrapper.find(".room_num_" + x).addClass('hide');
                }
            }
        },

        increase_element: function (clicked_element) {
            if (clicked_element.hasClass('disabled')) {
                return;
            }

            var wrapper = clicked_element.closest(".modification_buttons"),
                target_input = wrapper.find("input"),
                actual_value = parseInt(target_input.val());

            wrapper.find(".minus").removeClass('disabled');

            actual_value++;
            target_input.val(actual_value).attr('value', actual_value);
            target_input.trigger('change');

            var target_modifation = target_input.attr('target');
            this.update_input_value(target_modifation, actual_value);

            var max_val = parseInt(target_input.attr('max-value'));

            if (actual_value >= max_val) {
                clicked_element.addClass('disabled');
            }
        },

        decrease_element: function (clicked_element) {
            if (clicked_element.hasClass('disabled')) {
                return;
            }

            var wrapper = clicked_element.closest(".modification_buttons"),
                target_input = wrapper.find("input"),
                actual_value = parseInt(target_input.val());

            wrapper.find(".plus").removeClass('disabled');

            actual_value--;
            target_input.val(actual_value).attr('value', actual_value);
            target_input.trigger('change');

            var target_modifation = target_input.attr('target');
            this.update_input_value(target_modifation, actual_value);

            var min_val = parseInt(target_input.attr('min-value'));

            if (actual_value <= min_val) {
                clicked_element.addClass('disabled');
            }
        },

        update_input_value: function (target_input, target_value) {
            $("*[name='" + target_input + "']").each(function () {
                var tag_name = $(this).prop('tagName').toLowerCase();
                if (tag_name == 'input') {
                    $(this).val(target_value);
                } else if (tag_name == 'select') {
                    $(this).find("option").removeAttr('selected');
                    $(this).val(target_value);
                    _update_capacity_rooms_v2();
                    try {
                        $(this).selectric("refresh");
                    } catch (e) {
                    }
                }
            });

            if (target_input == 'numRooms') {
                OccupancyController.update_rooms_number(target_value);
            }
        },

        ages_checker: function (modified_element) {
            var actual_target = modified_element.attr('target');
            if (actual_target.indexOf('childrenRoom') > -1) {
                var actual_value = modified_element.attr('value'),
                    parsed_value = parseInt(actual_value);

                var parent_wrapper = modified_element.closest('.guest_controll');

                parent_wrapper.find(".block_age_selection").each(function (index, element) {
                    if (index + 1 <= parsed_value) {
                        $(element).removeClass('hide');
                    } else {
                        $(element).addClass('hide');
                    }
                });

                parent_wrapper.find(".block_age_selection").removeClass('last');
                parent_wrapper.find(".block_age_selection:not(.hide):last").addClass('last');

            }
        },

        select_kid_age: function () {
            var ages_wrapper = $(this).closest('.block_age_selection');
            ages_wrapper.find(".age_option").removeClass('active');
            $(this).addClass('active');
        }
    };
}();

function _update_capacity_rooms_v2() {
    var number_of_rooms = $("select[name='numRooms']").val(),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

       if (number_of_rooms){
          for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
             var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
                 actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
                 actual_select_babies = $("select[name='babiesRoom" + room_loop + "']").val();

             if(actual_select_adults || actual_select_kids){
                adults_number += parseInt(actual_select_adults);
                kids_number += parseInt(actual_select_kids);
                babies_number += parseInt(actual_select_babies);
             }
          }
       }
       var target_placeholder = $(".guest_selector .placeholder_text"),
           placeholder_string = "";


       adults_number = parseInt(adults_number);
       kids_number = parseInt(kids_number);
       babies_number = parseInt(babies_number);

       var adults_str = adults_number != 1 ? adults_tag : adult_tag;
       var kids_str = kids_number != 1 ? kids_tag : kid_tag;

       placeholder_string += adults_number + " " + adults_str;

       if(!$(".adults_only_selector").length){
          placeholder_string += " + " + kids_number + " " + kids_str;
       }

       target_placeholder.html(placeholder_string);
}