.destiny_selector_wrapper {
  position: relative;
  bottom: 0;
  left: 0;
  background: white;
  width: 410px;
  @include box-sizing(border-box);
  overflow: hidden;
  transition: max-height .5s;


  &.active {
    max-height: 750px;
    border-bottom: 1px solid #cacaca;
  }

  & > .title_wrapper {
    font-size: 14px !important;
  }

  .options_wrapper {
    max-height: 450px;
    overflow: scroll;

    &::-webkit-scrollbar {
      background-color: rgba(255, 255, 255, 0.4);
      width: 13px;
      opacity: 0.4;
    }

    /* background of the scrollbar except button or resizer */
    &::-webkit-scrollbar-track {
      background-color: #fff;
      opacity: 0.4;
    }

    /* scrollbar itself */
    &::-webkit-scrollbar-thumb {
      background-color: #babac0;
      border-radius: 16px;
      border: 4px solid #fff;
      opacity: 0.4;
    }

    /* set button(top and bottom of the scrollbar) */
    &::-webkit-scrollbar-button {
      display: none
    }
  }

  .title_wrapper, .light_title {
    text-transform: uppercase;
    font-size: 13px;
    color: #434343;
    padding: 10px 20px;
    border-bottom: 1px solid #cacaca;
    position: relative;
    @include box-sizing(border-box);

    &:after {
      content: "\f594";
      font-family: "Font Awesome 5 Pro";
      font-weight: 300;
      display: inline-block;
      font-style: normal;
      font-variant: normal;
      text-rendering: auto;
      line-height: 1;
      position: absolute;
      right: 20px;
      top: 50%;
      font-size: 15px;
      color: #2074ca;
      @include transform(translateY(-50%));
    }
  }

  .light_title {
    border-bottom: 0;
    color: $corporate_3;
    font-weight: bolder;

    &:after {
      display: none;
    }
  }

  .destiny_hotels {
    max-height: 0;
    overflow: hidden;
    transition: max-height .5s;
    background: #f3f3f3;
  }

  .hotel_element_selector {
    font-size: 15px;
    color: $corporate_3;
    padding: 10px 28px 18px;
    position: relative;
    @include box-sizing(border-box);
    border: 0;
    font-weight: bold;
    .title_selector{
      color: $black;
    }

    .category {
      display: inline-block;

      i {
        font-size: 13px;
        vertical-align: middle;
      }
    }
  }

  .destiny_b0 {
    border-bottom: 0;
    font-size: 14px;
    padding: 15px 20px 8px;
  }

  .destiny_element {
    .title_wrapper {
      border-bottom: 0;
      font-family: $text_family;
      color: $black;
      letter-spacing: 0.6px;
      font-size: 15px;
      font-weight: 700;
      padding: 0px 27px 15px;

      &:after {
        display: none;
      }

      .more, .less {
        position: absolute;
        right: 20px;
        top: 25%;
        @include transform(translateY(-50%));
        color: $corporate_2;
      }

      .more {
        background: $corporate_2;
        width: 15px;
        height: 15px;
        border-radius: 20px;

        &:before {
          content: '\f067';
          font-weight: 300;
          color: white;
          position: absolute;
          left: 50%;
          font-size: 10px;
          top: 8px;
          transform: translate(-50%, -50%);
        }
      }

      .less {
        opacity: 0;
      }
    }

    &.active {
      .destiny_hotels {
        max-height: 600px;
      }

      .title_wrapper {
        .less {
          opacity: 1;
        }

        .more {
          opacity: 0;
        }
      }
    }
  }

  .check_all_hotels, .destiny_element {
    cursor: pointer;
  }

  .check_all_hotels {
    font-size: 14px;
    padding: 22px 20px 12px;
  }
}

body.selecting_hotel {
  .dates_selector_personalized, .rooms_number_wrapper, .guest_selector, .wrapper_booking_button {
    opacity: 0.4;
  }
}

body.inner_section {
  .destiny_selector_wrapper {
    bottom: auto;
    top: 85%;
  }
}

#data {
  .destiny_selector_wrapper {
    position: relative;
    left: 0;
    top: 0;

  }
}