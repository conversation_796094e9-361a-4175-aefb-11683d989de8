//Base web (change too in config.rb)
$base_web: "bed4o";

$is_mobile: false !default;

@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap');

@font-face {
  font-family: 'Gotham Rounded';
  src: url('/static_1/fonts/gotham/v3/GothamRounded-Light.otf'),
  url('/static_1/fonts/gotham/v3/GothamRounded-Book.otf') format('otf');
  font-weight: 200; // Should be 300 but it's taken by 'Book' already
  font-style: normal;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('/static_1/fonts/gotham/v3/GothamRounded-Book.otf'),
  url('/static_1/fonts/gotham/v3/GothamRounded-Book.otf') format('otf');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('/static_1/fonts/gotham/v3/GothamRounded-Medium.otf'),
  url('/static_1/fonts/gotham/v3/GothamRounded-Medium.otf') format('otf');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('/static_1/fonts/gotham/v3/GothamRounded-Bold.otf'),
  url('/static_1/fonts/gotham/v3/GothamRounded-Bold.otf') format('otf');
  font-weight: 700;
  font-style: normal;
}


// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #FF3300;
$corporate_2: #CCCC00;
$corporate_3: #0099CC;
$corporate_4: $corporate_1;
$corporate_4_dark: $corporate_2;
$black: #161C21;
$lightgrey: #F5F5F5;
$grey: #666666;
$grey2: #777777;
$grey3: rgba(48, 57, 72, 0.22);
$color_text: $black;

$title_family: 'DM Serif Display', serif;
$text_family: "Gotham Rounded", sans-serif;
$btn_family: $text_family;

$border_radius: 10px;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

@mixin title_styles($color: $corporate_2, $fz: 148px, $fw: 400, $lh: 119px, $ls: 0, $tt: uppercase) {
  font-family: $title_family;
  font-size: $fz;
  font-weight: $fw;
  line-height: $lh;
  letter-spacing: $ls;
  color: $color;
  text-transform: $tt;
  display: block;
  margin: 0;
  
  @media (max-width: 575px) {
    font-size: 44px !important;
    line-height: 50px !important;
  }
}
@mixin destination_input_styles() {
  color: $corporate_1;
  font-size: 14px;
}
@mixin banner_title_styles($color: white, $fw: 400, $fz: 30px, $lh: 34px, $ls: 0) {
  font-family: $title_family;
  font-weight: 400;
  font-size: 30px;
  line-height: 34px;
  letter-spacing: 0;
  color: $color;
  display: block;
  margin: 0;
  
  @media (max-width: 575px) {
    font-size: 23px;
    line-height: 26px;
  }
}

@mixin banner_title_styles_2() {
  font-family: $text_family;
  font-size: 22px;
  font-weight: 500;
  line-height: 30px;
  letter-spacing: 0;
  color: $color_text;
  text-align: left;
  padding-bottom: 20px;
  margin: 0;
}

@mixin banner_title_svg() {
  font-family: $text_family;
  display: inline-block;
  font-size: 36px;
  font-weight: 500;
  line-height: 40px;
  letter-spacing: 0;
  color: $corporate_1;
  text-align: left;
  padding-bottom: 20px;
  margin: 0;
  position: relative;
  
  .subtitle {
    display: block;
    color: $black;
  }
  
  &::before {
    position: absolute;
    content: '';
    background-image: url("https://storage.googleapis.com/cdn.paraty.es/b4u-corporativa/files/logo_gris.svg");
    background-repeat: no-repeat;
    background-size: cover;
    width: 190px;
    height: 190px;
    z-index: -1;
    top: -30px;
    right: 0;
    transform: translateX(50%);
  }
}

@mixin text_styles($color: $color_text, $fw: 300, $fz: 17px, $ls: 1px, $lh: 20px) {
  font-family: $text_family;
  font-weight: $fw;
  font-size: $fz;
  color: $color;
  letter-spacing: $ls;
  line-height: $lh;
}

@mixin btn_styles() {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  background-color: $corporate_1;
  color: white;
  font-size: 17px;
  line-height: 31px;
  font-weight: 700;
  letter-spacing: 1.3px;
  text-align: center;
  padding: 10px 20px;
  font-family: $btn_family;
  text-transform: uppercase;
  border: none;
  margin: 0;
  width: auto;
  cursor: pointer;
  @include transition(all, .3s);
  
  &:after {
    content: "\f054";
    font-family: "Font Awesome 5 Pro";
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    font-weight: 300;
    font-size: 26px;
  }
  
  &.open_modal, &.open_room_modal {
    background-color: $corporate_2;
    
    &:before {
      content: "+";
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;
      font-weight: 700;
      font-size: 12px;
    }
    
    &:after {
      display: none;
    }
  }
  
  &:focus {
    outline: 0;
  }
  
  @if not $is_mobile {
    &:hover {
      background-color: $corporate_3;
    }
  }
}

@mixin btn_styles_2() {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  color: $color_text;
  font-size: 17px;
  line-height: 20px;
  font-weight: 300;
  letter-spacing: 1.3px;
  text-align: center;
  padding: 10px 15px 10px 0;
  font-family: $btn_family;
  text-transform: uppercase;
  border: none;
  margin: 0;
  width: auto;
  cursor: pointer;
  border-bottom: 4px solid $corporate_2;
  @include transition(all, .6s);
  
  @media (max-width: 575px) {
    font-size: 14px;
    line-height: 18px;
  }
  
  &:before {
    content: "+";
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    font-weight: 700;
    font-size: 12px;
    color: $corporate_2;
  }
  
  &.white {
    color: white;
    
    &:before {
      color: white;
    }
  }
  
  &:focus {
    outline: 0;
  }
  
  @if not $is_mobile {
    &:hover {
      color: $corporate_2;
      padding-bottom: 5px;
      margin-bottom: 5px; // To compensate padding
    }
  }
}

@mixin btn_styles_3($color: $corporate_1) {
  display: inline-block;
  font-size: 17px;
  color: $black;
  line-height: 20px;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 300;
  padding: 10px 0;
  border-bottom: 4px solid $corporate_1;
  transition: all .4s;
  
  &:hover {
    padding-bottom: 5px;
    margin-bottom: 5px; // To compensate padding
  }
}

@mixin icon_styles() {
  position: relative;
  display: inline-block;
  color: $corporate_1;
  font-size: 40px;
}

@mixin base_banner_styles() {
  position: relative;
  padding: 70px calc((100% - 1140px) / 2);
  overflow: hidden;
  width: 100%;
}

@mixin owl_nav_styles($color: $corporate_3) {
  position: relative;
  font-size: 42px;
  width: 78px;
  height: 78px;
  
  @media (max-width: 575px) {
    font-size: 32px;
    width: 60px;
    height: 60px;
  }
  
  
  border-radius: 50%;
  background-color: white;
  color: $color;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 3px 6px #00000029;
  cursor: pointer;
  @include transition(all, .6s);
  
  i {
    @include center_xy;
  }
  
  &.disabled {
    opacity: 0;
    visibility: hidden;
    cursor: default;
  }
  
  @if not $is_mobile {
    &:not(.disabled):hover {
      background-color: $color;
      color: white;
    }
  }
}

@mixin owl_dots_styles($color: white, $size: 12px) {
  display: inline-block;
  vertical-align: middle;
  float: none;
  margin-right: 7px;
  width: $size;
  height: $size;
  border-radius: 50%;
  background-color: rgba($color, .5);
  @include transition(all, .4s);
  &:hover, &.active, &.selected {
    background-color: $color;
  }
}

@mixin newsletter_placeholder() {
  font-size: 16px;
  color: rgba($color_text, .22);
  font-weight: 400;
  font-family: $text_family;
  text-transform: uppercase;
}

@mixin box_shadow() {
  -webkit-box-shadow: 6px 3px 14px #00000029;
  -moz-box-shadow: 6px 3px 14px #00000029;
  box-shadow: 6px 3px 14px #00000029;
}

@mixin checkbox_styles() {
  -moz-appearance: none;
  -webkit-appearance: none;
  display: inline-block;
  vertical-align: middle;
  width: 9px;
  height: 9px;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid $corporate_3;
  cursor: pointer;
  &:checked {
    background-color: $corporate_3;
  }
  &:focus {
    outline: none;
  }
}

@mixin fa_default_style() {
  font-family: "Font Awesome 5 Pro";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

@mixin center($axis: both) {
  position: absolute;
  
  @if $axis==X {
    left: 50%;
    transform: translateX(-50%);
  } @else if $axis==Y {
    top: 50%;
    transform: translateY(-50%);
  } @else {
    top: 50%;
    left: 50%;
  }
}

@mixin fill($axis: both) {
  position: absolute;
  
  @if $axis==X {
    left: 0;
    right: 0;
  } @else if $axis==Y {
    top: 0;
    bottom: 0;
  } @else {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}