.hotel_rooms_full_wrapper {
  @include base_banner_styles;
  border: 25px solid $corporate_1;

  .content_title {
    @include title_styles($corporate_3);
    text-align: right;
    padding-bottom: 40px;
    font-size: 100px;
    line-height: 100px;
    text-transform: capitalize;
  }

  .hotel_rooms_wrapper {
    .owl-stage-outer, .owl-stage, .owl-item {
      height: 100%;
    }

    .room {
      position: relative;
      @include display_flex;
      width: 100%;
      margin-bottom: 20px;

      &:last-of-type {
        margin-bottom: 30px;
      }

      .gallery_icon {
        @include owl_nav_styles;
        position: absolute;
        top: 10px;
        right: 10px;
        width: 60px;
        height: 60px;
        font-size: 30px;
        z-index: 3;
      }

      .room_gallery_wrapper {
        position: relative;
        display: inline-block;
        width: 370px;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        overflow: hidden;

        .room_gallery {
          height: 100%;
          .img_wrapper {
            position: relative;
            display: block;
            width: 100%;
            height: 270px;
            overflow: hidden;

            &:before {
              content: '';
              @include full_size;
              z-index: 2;
              background: linear-gradient(to top, rgba($black, .8) 5%, rgba($black, .6) 10%, rgba($black, .5) 20%, rgba($black, 0) 100%);
            }

            img {
              @include cover_image;
            }
          }

          .owl-dots {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;

            .owl-dot {
              @include owl_dots_styles(white);
            }
          }
        }
      }



      .room_content {
        display: inline-flex;
        flex-wrap: wrap;
        align-items: center;
        width: calc(100% - 370px);
        background-color: $lightgrey;
        padding: 30px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        overflow: hidden;

        .title {
          @include banner_title_styles_2;
          width: 100%;
          padding-bottom: 20px;
        }

        .text {
          @include text_styles;
          font-size: 15px;
          width: 100%;
          padding-bottom: 30px;
          .hidden_text {
            display: none;
          }
        }

        .buttons_wrapper {
          display: inline-block;
          width: 100%;
          text-align: right;

          .btn_personalized_1 {
            margin-left: 15px;
          }
        }
      }
    }
  }
}

.room_modal_wrapper {
  position: relative;

  .room_gallery_popup {
    position: relative;
    display: block;
    width: 100%;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    overflow: hidden;

    .img_wrapper {
      position: relative;
      display: block;
      width: 100%;
      height: 310px;
      overflow: hidden;

      &:before {
        content: '';
        @include full_size;
        z-index: 2;
        background: linear-gradient(to top, rgba($black, .8) 5%, rgba($black, .6) 10%, rgba($black, .5) 20%, rgba($black, 0) 100%);
      }

      img {
        @include cover_image;
      }
    }

    .owl-dots {
      position: absolute;
      bottom: 20px;
      left: 80px;
      right: 80px;
      text-align: center;

      .owl-dot {
        @include owl_dots_styles(white);
      }
    }
  }

  .room_modal_content {
    @include display_flex;
    align-items: center;
    width: 100%;
    background-color: white;
    padding: 30px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    overflow: hidden;
    text-align: left;

    .title {
      @include banner_title_styles_2;
      width: 100%;
      padding-bottom: 20px;
    }

    .text {
      @include text_styles(15px);
      width: 100%;
      padding-bottom: 20px;
      .hidden_text {
        display: block !important;
      }
    }

    .room_icons {
      @include display_flex;
      width: 100%;
      text-align: left;
      padding-bottom: 20px;

      .icon_wrapper {
        display: inline-block;
        width: 50%;
        padding: 10px 20px 0 0;

        .icon {
          display: inline-block;
          vertical-align: middle;
          font-size: 22px;
          color: $corporate_3;
          width: 40px;
        }

        .icon_text {
          display: inline-block;
          vertical-align: middle;
          font-size: 15px;
          color: $color_text;
        }
      }
    }

    .room_extra_info {
      display: inline-block;
      width: 60%;
      text-align: left;

      .info {
        display: block;
        font-size: 15px;
        padding: 3px 0;
        color: $color_text;

        i {
          display: inline-block;
          vertical-align: middle;
          color: $corporate_3;
          font-size: 18px;
          width: 40px;
        }
      }
    }

    .modal_buttons_wrapper {
      display: inline-block;
      width: 40%;
      text-align: right;
    }
  }
}