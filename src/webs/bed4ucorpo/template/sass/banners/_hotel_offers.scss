.hotel_offers_full_wrapper {
  @include base_banner_styles;
  background-image: linear-gradient(to bottom, white 30%, $lightgrey 30%, $lightgrey 100%);

  .content_title {
    @include title_styles($corporate_3);
    text-transform: capitalize;
    padding-bottom: 30px;
    font-size: 100px;
    line-height: 100px;
    text-align: right;
  }

  .hotel_offers_wrapper {
    @include display_flex;
    align-items: flex-start;
    width: 100%;

    .offer {
      display: inline-block;
      width: calc(50% - 15px);
      margin-bottom: 30px;

      &:nth-of-type(2n) {
        margin-left: 30px;
      }

      .img_wrapper {
        position: relative;
        width: 100%;
        height: 370px;
        overflow: hidden;

        img {
          @include cover_image;
        }
      }
      .offer_content {
        position: relative;
        z-index: 5;
        display: block;
        width: 100%;
        background-color: rgba($corporate_1, .75);
        padding: 30px;
        margin-top: -30px;
        margin-left: 15px;
        text-align: left;

        .title {
          @include banner_title_styles;
          @include ellipsis(2); // Just for ensure there are only 2 lines
          height: 68px; // Double of line-height
        }
        .text {
          @include text_styles(white);
          @include ellipsis(4);
          margin: 15px 0;
        }
        .offer_links {
          .btn_personalized_2 {
            margin-right: 10px;
          }

          .button-promotion {
            background-color: $corporate_2;

            &:hover {
              background-color: $corporate_3;
            }
          }
        }
      }
    }
  }
}