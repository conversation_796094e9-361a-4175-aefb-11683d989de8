.hotel_content_wrapper {
  @include base_banner_styles;
  @include display_flex;
  overflow: visible;
  z-index: 6;

  .hotel_sections_links {
    display: flex;
    width: 100%;
    background-color: white;
    padding-bottom: 30px;

    .link,
    .extra_link {
      position: relative;
      width: 100%;
      font-size: 22px;
      font-family: $text_family;
      line-height: 30px;
      font-weight: 300;
      text-transform: capitalize;
      text-align: center;
      padding: 15px;
      cursor: pointer;
      color: black;
      order: 3;
      @include transition(all, .6s);

      &:hover {
        color: $corporate_2;
      }

      &.active {
        color: $corporate_3;
        font-weight: 500;
      }
    }

    .link {
      &:first-of-type {
        order: 1;
      }
    }

    .extra_link {
      order: 2;
    }

    &.fixed {
      position: fixed;
      z-index: 99;
      left: 0;
      right: 0;
      top: 65px;
      box-shadow: 1px 5px 8px 0 rgba(0, 0, 0, 0.1);
      padding: 10px calc((100% - 1140px) / 2);
    }
  }
}

.dropdown {
  position: relative;
  display: inline-block;

  .dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    border-top: solid 2px #CCCC00;
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    left: 50%;
    transform: translateX(-50%);
    width: max-content;

    a {
      color: #333;
      padding: 12px 16px;
      text-decoration: none;
      display: block;

      &:hover {
        background-color: #f1f1f1;
      }
    }
  }

  &:hover {
    .dropdown-content {
      display: block;
    }
  }
}