.banner_images_wrapper {
  @include base_banner_styles;
  @include display_flex;
  align-items: center;
  justify-content: center;
  padding-top: 265px;
  padding-bottom: 230px;
  margin-bottom: 170px;
  text-align: center;


  .img_wrapper {
    @include center_xy;
    z-index: 3;
    width: 440px;
    height: 676px;
    overflow: hidden;
    opacity: 0;
    top: calc(50% + 100px);
    @include transition(all, 1.2s);

    img {
      @include cover_image;
    }
  }

  .banner {
    position: relative;
    width: 100%;
    z-index: 1;
    opacity: 0;
    @include transform(translateY(100px));
    @include transition(transform, 1.2s);

    &:nth-of-type(1) .banner_content .title:hover {
      opacity: 1;
      color: $corporate_3;
    }

    &:nth-of-type(2) .banner_content .title:hover {
      opacity: 1;
      color: $corporate_1;
    }

    &:nth-of-type(3) .banner_content .title:hover {
      opacity: 1;
      color: $corporate_2;
    }

    &:nth-of-type(3) {
      .banner_content {
        .title {
          padding-bottom: 0;
        }
      }
    }

    .banner_content {
      position: relative;
      text-align: center;

      .title {
        @include title_styles($black);
        display: inline-block;
        opacity: .05;
        z-index: 1;
        text-align: center;
        padding-bottom: 60px;
        cursor: pointer;
        @include transition(all, .6s);
      }

      .text {
        @include text_styles;
        text-align: left;
        display: inline-block;
        width: 320px;
        position: absolute;
        right: 0;
        top: 130px;
        opacity: 0;
        visibility: hidden;
        margin-top: -30px;
        @include transition(all, .6s)
      }
    }

    &.active {
      z-index: 5;

      &:nth-of-type(1) .banner_content .title {
        color: $corporate_3;
      }

      &:nth-of-type(2) .banner_content .title {
        color: $corporate_1;
      }

      &:nth-of-type(3) .banner_content .title {
        color: $corporate_2;
      }

      .banner_content {
        .title {
          opacity: 1;
        }

        .text {
          visibility: visible;
          opacity: 1;
          margin-top: 0;
        }
      }
    }
  }

  .banner_images_link {
    @include center_x;
    bottom: auto;
    top: calc(100% - 300px);
    z-index: 15;
    display: inline-block;
    font-size: 17px;
    color: $black;
    line-height: 20px;
    letter-spacing: 1.3px;
    text-transform: uppercase;
    margin-top: 200px;
    font-weight: 300;
    padding: 10px 0;
    border-bottom: 4px solid $corporate_1;
    opacity: 0;
    visibility: hidden;
    @include transform(translate(-50%, 100px));
    transition: transform 1.2s, padding 0.5s, margin 0.5s;

    &.active {
      opacity: 1;
      visibility: visible;
    }
    
    &:hover {
      color: $corporate_1;
      padding-bottom: 5px;
    }
  }
}

.banner_images_wrapper.onscroll_class {
  .banner {
    opacity: 1;
    @include transform(translateY(0));
  }
  .banner_images_link {
    opacity: 1;
    @include transform(translate(-50%, 0));
  }

  .img_wrapper {
    opacity: 1;
    top: 50%;
  }
}