.banner_gallery_full_wrapper {
  @include base_banner_styles;
  background-color: $lightgrey;
  
  .content_title {
    @include title_styles($corporate_1);
    font-size: 100px;
    line-height: 100px;
    text-transform: capitalize;
    position: relative;
    z-index: 2;
    margin-left: -80px;
    margin-bottom: -30px;
    
    > span {
      display: flex;
      justify-content: flex-start;
    }
    @media (max-width: 1160px) {
      margin-left: 0;
    }
  }

  .extra_link {
    position: absolute;
    z-index: 3;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    margin-top: 30px;
    margin-left: 20px;

    img {
      width: 50px;
      height: 40px;
      padding: 15px 5px 0 5px;
    }
  }
  
  .banner_gallery_wrapper {
    width: 100%;
    
    .banner {
      position: relative;
      display: inline-block;
      float: left;
      height: 270px;
      margin-left: 20px;
      width: calc(25% - 15px);
      overflow: hidden;
      cursor: pointer;
      
      img, video {
        @include cover_image;
      }
      
      .img_content {
        @include full_size;
        z-index: 2;
        background-color: rgba($corporate_1, .8);
        opacity: 0;
        visibility: hidden;
        @include transition(all, .6s);
        
        .hotel_logo {
          @include center_xy;
          min-width: auto;
          min-height: auto;
          max-width: 100%;
          width: 240px;
          opacity: .4;
        }
        
        .serarch_icon {
          @include center_xy;
          z-index: 4;
          color: white;
          font-size: 64px;
        }
      }
      
      &:first-of-type, &:nth-of-type(5n) {
        width: calc(50% - 10px);
        height: 560px;
        margin-left: 0;
      }
      
      &:nth-of-type(2), &:nth-of-type(4n + 1) {
        width: calc(50% - 10px);
        margin-bottom: 20px;
      }
      
      &:nth-of-type(4n) {
        margin-left: 20px;
      }
      
      &:hover {
        .img_content {
          opacity: 1;
          visibility: visible;
        }
      }
    }
    
    .owl-nav {
      @include center_y;
      left: 0;
      right: 0;
      height: 0;
      
      .owl-prev, .owl-next {
        @include owl_nav_styles($corporate_3);
        @include center_y;
        left: -100px;
      }
      
      .owl-next {
        left: auto;
        right: -100px;
      }
    }
    
    .owl-dots {
      display: block;
      padding-top: 20px;
      text-align: center;
      
      .owl-dot {
        @include owl_dots_styles($grey2, 20px);
      }
    }
  }
}