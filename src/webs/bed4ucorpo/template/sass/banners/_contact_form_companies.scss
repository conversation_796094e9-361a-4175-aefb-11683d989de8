.contact_form_wrapper.contact_form_companies{
  #contact{
    .contInput{
      width: calc((100% - 5px) / 3);
      input, textarea{
        margin-bottom: 0;
        font-family: $text_family;
        &::placeholder{
          font-family: $text_family;
        }
        &:focus-visible{
          outline: none;
        }
      }

      select {
        &#hotel {
          padding-left: 30px;
          border: none;
          border-bottom: 1px solid #FF3300;
          max-height: 48px;
          width: 100%;
        }
      }

      i{
        color: $corporate_1;
        position: absolute;
        font-family: "Font Awesome 5 Pro";
        top: 57%;
        left: 20px;
        transform: translateY(-50%);
        &.fa-envelope-o, &.fa-phone{
          top: 50%;
        }
      }
      &.textarea{
        width: 100%;
        i{
          top: 33px;
        }
      }
    }
  }
}