.banner_icons_multiple {
  padding: 50px 0;

  .banner_hotel_names {
    display: flex;
    justify-content: space-between;
    padding-bottom: 30px;
    padding-left: 30px;

    .hotel_name {
      color: #D1D1D1;
      cursor: pointer;
      text-decoration: none;
      width: 140px;

      &:hover {
        text-decoration: underline;
        text-decoration-color: #D1D1D1;
        transition: text-decoration 0.5s ease-in-out;
      }

      &.active {
        color: #0F99CC;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
          text-decoration-color: #0F99CC;
          transition: text-decoration 0.5s ease-in-out;
        }
      }

      &:first-child, &:last-child {
        width: 100px;
      }
    }
  }

  .banner_hotel_icons {
    position: relative;

    .banner_icons {
      @include display_flex;
      align-items: flex-start;

      .col {
        width: calc(100% / 3);

        &:not(:last-of-type) {
          border-right: 1px solid rgba($corporate_3, 0.5);
        }

        .icon_element {
          display: block;
          width: 100%;
          @include display_flex;
          align-items: center;
          padding: 0 20px;

          .icon {
            width: 50px;
            font-size: 30px;
            color: $corporate_3;
            margin: 0 5px;
          }

          .title {
            flex: 1;
            height: 60px; // Controls entire .icon_element height
            @include display_flex;
            align-items: center;
            @include text_styles($fw: 300, $ls: 0, $lh: 20px);
          }

          .toggle {
            width: 34px;
            height: 34px;
            border-radius: 50%;
            border: 2px solid $corporate_3;
            background-color: $corporate_3;
            color: white;
            cursor: pointer;
            position: relative;
            @include transition(all, 0.5s);

            i {
              @include center_xy;
              font-size: 18px;
            }

            .toggle_close {
              display: none;
            }
          }

          .desc {
            width: 100%;
            @include text_styles($fw: 200, $ls: 0);
            padding-left: 15px;
            border-left: 2px solid $corporate_2;
            max-height: 0;
            @include transition(all, 0.5s);
            overflow: hidden;
          }
        }

        .icon_element.opened {
          .desc {
            max-height: fit-content;
          }

          .toggle {
            background-color: white;
            color: $corporate_3;

            .toggle_open {
              display: none;
            }

            .toggle_close {
              display: inline;
            }
          }
        }
      }
    }

    .owl-prev {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(-50%, -50%);
    }

    .owl-next {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translate(50%, -50%);
    }

    .owl-prev, .owl-next {
      color: #0099CC;
      font-size: 32px;
      margin: 0 -30px;

      &.disabled {
        color: #0099CC5E;
      }
    }
  }
}