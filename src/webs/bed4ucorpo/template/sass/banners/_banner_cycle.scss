.banner_cycle_wrapper {
  @include base_banner_styles;
  
  .banner_title {
    text-align: center;
    margin-bottom: 100px;
    
    .title {
      @include banner_title_svg;
      color: $corporate_1;
    }
  }
  
  .banner {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 100px;
    
    .img_wrapper {
      width: 440px;
      height: 675px;
      opacity: 0;
      transform: translateY(100px);
      transition: all 1.2s;
      
      img {
        @include cover_image;
      }
    }
    
    .content_wrapper {
      flex: 1;
      z-index: 1;
      margin-top: 200px;
      
      .content_title {
        .title {
          @include title_styles;
          padding-top: 10px;
          font-weight: 400;
          
        }
      }
      
      .desc {
        @include text_styles;
        padding: 70px 90px 20px 100px;
      }
    }
    
    &:nth-child(odd) {
      flex-direction: row-reverse;
      
      .content_title {
        text-align: right;
        margin-right: -50px;
      }
    }
    
    &:nth-child(even) {
      flex-direction: row;
      
      .content_title {
        margin-left: -50px;
      }
    }
    
     &:nth-child(4) {
       .content_wrapper {
         .content_title {
           .title {
             span {
               margin-left: 140px;
             }
           }
         }
       }
    }
    
    &.onscroll_class {
      .img_wrapper {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
}