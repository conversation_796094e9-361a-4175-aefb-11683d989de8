.banner_icons_wrapper {
  padding: 50px 0;

  .banner_icons {
    @include display_flex;
    align-items: flex-start;

    .col {
      width: calc(100% / 3);

      &:not(:last-of-type) {
        border-right: 1px solid rgba($corporate_3, 0.5);
      }

      .icon_element {
        display: block;
        width: 100%;
        @include display_flex;
        align-items: center;
        padding: 0 20px;

        .icon {
          width: 50px;
          font-size: 30px;
          color: $corporate_3;
          margin: 0 5px;
        }

        .title {
          flex: 1;
          height: 60px; // Controls entire .icon_element height
          @include display_flex;
          align-items: center;
          @include text_styles($fw: 300, $ls: 0, $lh: 20px);
        }
        
        .toggle {
          width: 34px;
          height: 34px;
          border-radius: 50%;
          border: 2px solid $corporate_3;
          background-color: $corporate_3;
          color: white;
          cursor: pointer;
          position: relative;
          @include transition(all, 0.5s);

          i {
            @include center_xy;
            font-size: 18px;
          }

          .toggle_close {
            display: none;
          }
        }
        
        .desc {
          width: 100%;
          @include text_styles($fw: 200, $ls: 0);
          padding-left: 15px;
          border-left: 2px solid $corporate_2;
          max-height: 0;
          @include transition(all, 0.5s);
          overflow: hidden;
        }
      }

      .icon_element.opened {
        .desc {
          max-height: fit-content;
        }

        .toggle {
          background-color: white;
          color: $corporate_3;

          .toggle_open {
            display: none;
          }

          .toggle_close {
            display: inline;
          }
        }
      }
    }
  }
}