.modal, .room_modal, .popup_description {
  display: block;
  @include full_size;
  position: fixed;
  overflow-x: hidden;
  overflow-y: scroll;
  height: 100vh;
  max-height: 0;
  visibility: hidden;
  z-index: 1005;
  background: rgba($corporate_2, .9);
  @include transition(all, .6s);

  &.active {
    max-height: 100vh;
    visibility: visible;
  }

  .modal_content,
  .popup_content{
    @include center_xy;
    background: white;
    width: 820px;
    max-width: 80vw;
    max-height: 80vh;
    border-radius: 5px;

    .modal_close, .room_modal_close, .popup_close {
      position: absolute;
      top: -70px;
      right: 0;
      width: 50px;
      height: 50px;
      cursor: pointer;
      z-index: 50;

      &:hover {
        span {
          background-color: $corporate_1;
        }
      }

      span {
        display: block;
        width: 100%;
        height: 1px;
        background-color: white;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        @include transition(all, .6s);

        &:nth-child(1) {
          transform: translateY(-50%) rotate(45deg);
        }

        &:nth-child(2) {
          transform: translateY(-50%) rotate(-45deg);
        }
      }
    }

    iframe {
      width: 100%;
      vertical-align: middle;
      border-radius: 10px;
    }
  }
}

.room_modal {
  .modal_content {
    background: transparent;
  }
}

modal {
  display: none;
}