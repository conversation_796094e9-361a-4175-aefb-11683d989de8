<div class="destination_wrapper">
    <div class="destination_field">
        <select class="hotel_selector_mobile">
            <option>{{ T_seleccionar_hotel }}</option>
            {% for destiny, value in hotels_grouped.items() %}
                {% for hotel in value.hoteles %}
                    {% if not hotel.disabled_booking or not hotel.url_booking %}
                      <option class="hotel_selector_option" id="{{ hotel.namespace|safe }}" data-namespace="{{ hotel.namespace|safe }}" data-url_booking="{{ hotel.url_booking }}" {% if hotel.selected %}selected{% endif%}>
                      {{ hotel.name|safe }}
                      </option>
                    {% endif %}
                {% endfor %}
            {% endfor %}
        </select>
    </div>
</div>
