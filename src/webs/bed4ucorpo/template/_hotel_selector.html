<div class="destination_wrapper">
    <div class="destination_field_title">
        <span>{{ T_seleccionar }} {{ T_alojamiento }}</span>
        <i class="fal fa-hotel"></i>
    </div>
    <div class="destination_field" id="destination_field">
        <label for="destination">{{ T_todos_los_alojamiento }}</label>
        <input class="destination" readonly="readonly" id="destination" type="text" name="destination"/>
        <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder"
               class="default_destination_placeholder">
    </div>
</div>


<div class="hotel_selector" id="hotel_selector">
    <div class="hotel_selector_title">
        <span class="title">{{ T_seleccionar }}</span>
        <span class="subtitle">{{ T_destino }} / {{ T_alojamiento }}</span>
        <i class="fal fa-hotel"></i>
    </div>
    <div class="destiny_selector_wrapper">
        <div class="options_wrapper">
           {% for destiny_key, destiny_data in hotels_grouped.items() %}
             {% if destiny_data.hoteles|length > 1 %}
                <div class="destiny_element">
                    <div class="title_wrapper">
                        {{ destiny_key|safe }}
                        {% if destiny_data.extra_destiny %} - {{ destiny_data.extra_destiny|safe }}{% endif %}
                        ({{ destiny_data.hoteles|length }}
                        {% if destiny_data.hoteles|length == 1 %}{{ T_alojamiento }}{% else %}{{ T_alojamientos }}{% endif %})
                        <i class="fas more fa-plus-circle"></i>
                        <i class="fal less fa-minus-circle"></i>
                    </div>
                    <div class="destiny_hotels hotel_list">
                        {% if destiny_data.hoteles|length > 1 %}
                            <div class="destiny_b0 light_title"
                                multiple_namespace="{% for hotel_element in hotels_grouped.items() %}{% if 'eco' in hotel_element.hotel_namespace %}r__{% endif %}{{ hotel_element.hotel_namespace }}{% endfor %}"
                                target_url="/booking0"
                                {% if destiny_data.hoteles|length == 1 and destiny_data.hoteles.0.age_kids %}age_kids="{{ destiny_data.hoteles.0.age_kids }}"{% endif %}
                            >
                               <span>{{ T_ver_todos_hoteles }} {{ destiny_key|safe }}</span>
                                {% if destiny_data.extra_destiny %} - {{ destiny_data.extra_destiny|safe }}{% endif %}
                            </div>
                        {% endif %}

                        {% for hotel_element in destiny_data.hoteles %}
                            <div class="hotel_element_selector hotel_selector_option" namespace="{{ hotel_element.hotel_namespace|safe }}"
                                 {% if hotel_element.short_name %}data-shortname="{{ hotel_element.short_name|safe }}"{% endif %}
                                 {% if not hotel_element.url_booking %}
                                    target_url="https://{{ hotel_element.hotel_namespace }}-dot-{% if 'eco' in hotel_element.hotel_namespace %}eco-hotels{% else %}best-hoteles{% endif %}.appspot.com/booking1"
                                 {% else %}
                                    target_url="{{ hotel_element.url_booking|safe }}"
                                 {% endif %}
                                {% if hotel_element.age_kids %}age_kids="{{ hotel_element.age_kids }}"{% endif %}
                                 {% if hotel_element.id %}id="{{ hotel_element.id }}"{% endif %}
                            >
                               <span class="title_selector">{{ hotel_element.title|safe }}</span>
                                <input type="hidden" {% if hotel_element.id %}id="url_booking_{{ hotel_element.id }}"{% endif %}
                                       {% if hotel_element.url_booking %}value="{{ hotel_element.url_booking }}" {% endif %}
                                />
                                <input type="hidden"
                                       {% if hotel_element.id %}
                                        id="namespace_{{ hotel_element.id }}"
                                        value="{{ hotel_element.id }}"
                                       {% endif %}

                                />
                                {% if hotel_element.category %}
                                    <div class="category">
                                        {% if hotel_element.keys_category %}
                                            {% for star in hotel_element.category|create_range %}
                                                <i class="fa fa-key" aria-hidden="true"></i>
                                            {% endfor %}
                                        {% else %}
                                            {% for star in hotel_element.category|create_range %}
                                                <i class="fas fa-star"></i>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
             {% for hotel_element in destiny_data.hoteles %}
            <div class="destiny_element">
                <div class="hotel_element_selector hotel_selector_option" namespace="{{ hotel_element.hotel_namespace|safe }}"
                                 {% if hotel_element.short_name %}data-shortname="{{ hotel_element.short_name|safe }}"{% endif %}
                                 {% if not hotel_element.url_booking %}
                                    target_url="https://{{ hotel_element.hotel_namespace }}-dot-{% if 'eco' in hotel_element.hotel_namespace %}eco-hotels{% else %}best-hoteles{% endif %}.appspot.com/booking1"
                                 {% else %}
                                    target_url="{{ hotel_element.url_booking|safe }}"
                                 {% endif %}
                                {% if hotel_element.age_kids %}age_kids="{{ hotel_element.age_kids }}"{% endif %}
                                 {% if hotel_element.id %}id="{{ hotel_element.id }}"{% endif %}
                            >
                               <span class="title_selector">{{ hotel_element.title|safe }}</span>
                                <input type="hidden" {% if hotel_element.id %}id="url_booking_{{ hotel_element.id }}"{% endif %}
                                       {% if hotel_element.url_booking %}value="{{ hotel_element.url_booking }}" {% endif %}
                                />
                                <input type="hidden"
                                       {% if hotel_element.id %}
                                        id="namespace_{{ hotel_element.id }}"
                                        value="{{ hotel_element.id }}"
                                       {% endif %}

                                />
                                {% if hotel_element.category %}
                                    <div class="category">
                                        {% if hotel_element.keys_category %}
                                            {% for star in hotel_element.category|create_range %}
                                                <i class="fa fa-key" aria-hidden="true"></i>
                                            {% endfor %}
                                        {% else %}
                                            {% for star in hotel_element.category|create_range %}
                                                <i class="fas fa-star"></i>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
            </div>
             {% endfor %}

            {% endif %}
            {% endfor %}

        </div>
    </div>
</div>

