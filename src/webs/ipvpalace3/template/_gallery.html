<section class="gallery-image">
    <div class="gallery-image-wrapper container12">

        <div class="big-img">
            <div class="gallery_image_title"></div>
            <img src="/img/{{ base_web }}/right_arrow.png" class="gallery_previous_image">
            <img src="" class="main_image" alt=""/>
            <img src="/img/{{ base_web }}/right_arrow.png" class="gallery_next_image">

            <div class="image_filters_wrapper">
                {% for filter in filters %}
                    <div class="filter_element">{{ filter|safe }}</div>
                {% endfor %}
            </div>
        </div>

        <div class="video_iframe" style="display: none">
            <iframe width="1140" height="605" src="" frameborder="0" allowfullscreen></iframe>

            <img src="/img/{{ base_web }}/right_arrow.png" class="gallery_previous_image">
            <img src="/img/{{ base_web }}/right_arrow.png" class="gallery_next_image">

            <div class="image_filters_wrapper">
                {% for filter in filters %}
                    <div class="filter_element">{{ filter|safe }}</div>
                {% endfor %}
            </div>
        </div>

        <div class="hidden_gallery" style="display: none;">
            {% for x in images.images_blocks %}
                <img src="{{ x.servingUrl|safe }}=s1900" {% if x.video %}video_url="{{ x.video|safe }}"{% endif %} {% if x.description %}title="{{ x.description|safe }}"{% endif %} alt="" description="{{ x.title|safe }}"/>
            {% endfor %}
        </div>

        <div class="image-grid">
            <div class="gallery_list" id="gallery_list_1">
                <ul class="slides">

                </ul>
            </div>
        </div>
    </div>
</section>


<script async>
    $(function () {

        $("img.gallery_next_image, .main_image").click(function () {
            var actual_image = $(".big-img .main_image").attr('src');
            var target_image = $(".gallery_list li img[src='" + actual_image + "']");
            if (!actual_image){
                actual_image = $(".video_iframe iframe").attr('src');
                target_image = $(".gallery_list li img[video_url='" + actual_image + "']");
            }
            var final_image = target_image.parent().next().find("img");

            if(!final_image.attr('src')) {
                final_image = $(".gallery_list li:first-child img");
            }

            var final_src = final_image.attr('src');

            if(final_image.attr('video_url')) {
                $(".video_iframe").find("iframe").attr('src', final_image.attr('video_url'));
                $(".big-img").hide();
                $(".video_iframe").show();
            }

            var image_title = final_image.attr('title');
            if(image_title) {
                $(".gallery_image_title").css('display', 'block').html(image_title);
            }else{
                $(".gallery_image_title").css('display', 'none');
            }
            $(".big-img img.main_image").fadeOut('fast', function () {
                $(".big-img img.main_image").attr('src', final_src);
                $(".big-img img.main_image").fadeIn('fast');
            });
        });

        $("img.gallery_previous_image").click(function () {

            var actual_image = $(".big-img .main_image").attr('src');
            var target_image = $(".gallery_list li img[src='" + actual_image + "']");
            if (!actual_image){
                actual_image = $(".video_iframe iframe").attr('src');
                target_image = $(".gallery_list li img[video_url='" + actual_image + "']");
            }

            var final_image = target_image.parent().prev().find("img");

            if(!final_image.attr('src')) {
                final_image = $(".gallery_list li:last-child img");
            }

            if(final_image.attr('video_url')) {
                $(".video_iframe").find("iframe").attr('src', final_image.attr('video_url'));
                $(".big-img").hide();
                $(".video_iframe").show();
            }

            var final_src = final_image.attr('src');


            var image_title = final_image.attr('title');
            if(image_title){
                $(".gallery_image_title").css('display', 'block').html(image_title);
            }else{
                $(".gallery_image_title").css('display', 'none')
            }

            $(".big-img img.main_image").fadeOut('fast', function () {
                $(".big-img img.main_image").attr('src', final_src);
                $(".big-img img.main_image").fadeIn('fast');
            });
        });


        initialize_flexslider_gallery();

        $(".hidden_gallery img").each(function(){
            if($(this).attr('description')) {
                $(this).attr('description', $(this).attr('description').replace(/ /g, ''))
            }
        });

        $(".filter_element").click(function () {
            $(".filter_element").removeClass('active');
            $(this).addClass('active');

            var selected_filter = $(this).html(),
                    selected_images = $(".hidden_gallery img[description=" + selected_filter.replace(/ /g, '') + "]");

            $("#gallery_list_1 .slides li").remove();
            selected_images.clone().appendTo("#gallery_list_1 .slides");
            $("#gallery_list_1 .slides img").wrap('<li></li>');
            initialize_flexslider_gallery();
            $("#gallery_list_1").flexslider(0);
            var number_of_elements = $("#gallery_list_1 ul.slides li:visible").length;
            available_slides = Math.ceil(number_of_elements / 14);
            if (available_slides <= 1) {
                $("#gallery_list_1 .slides").css({
                    'width': 'auto'
                })
            } else {
                calculate_slides()
            }

            var src_first = $("#gallery_list_1 li:visible img").first();
            if (src_first.attr('video_url')){
                $(".video_iframe").find("iframe").attr('src', src_first.attr('video_url'));
                $(".big-img").hide();
                $(".video_iframe").show();
            }else{
                $(".big-img img.main_image").attr("src", src_first.attr("src"));
                $(".big-img .gallery_image_title").html(src_first.attr("title"));
                $(".big-img").show();
                $(".video_iframe").hide();
            }
        });

    });

    function calculate_slides() {
        $("#gallery_list_1 .flex-nav-next a").unbind('click');
        $("#gallery_list_1 .flex-nav-next a").click(function (e) {
            var slider = $("#gallery_list_1").data("flexslider");
            var actual_slide = slider.currentSlide;
            if (actual_slide == (available_slides - 1)) {
                $("#gallery_list_1").flexslider(0);
            } else {
                $("#gallery_list_1").flexslider(actual_slide + 1);
            }
            e.preventDefault;
            return false;
        });

        $("#gallery_list_1 .flex-nav-prev a").unbind('click');
        $("#gallery_list_1 .flex-nav-prev a").click(function (e) {
            var slider = $("#gallery_list_1").data("flexslider");
            var actual_slide = slider.currentSlide;
            if (actual_slide < 1) {
                $("#gallery_list_1").flexslider(available_slides - 1);
            } else {
                $("#gallery_list_1").flexslider(actual_slide - 1);
            }
            e.preventDefault;
            return false;
        });
    }

    function initialize_flexslider_gallery() {
        $("#gallery_list_1").removeData("flexslider");
        $("#gallery_list_1").flexslider({
            animation: "slide",
            controlNav: false,
            slideshow: false,
            minItems: 14,
            maxItems: 14,
            itemWidth: (1030 / 14),
            after: function (slider) {
                window.curSlide = slider.currentSlide;
            }
        });


        $(".filter-gallery li").click(function (e) {
            elem = $(this).attr("number");
            $(this).parent().find(".element_hide").removeClass("element_hide");
            $(this).addClass("element_hide");
            $(".gallery_list").removeClass("showed");

            $(".gallery_list").hide("fast").removeClass("showed");
            var src_first = $(".showed li img:first-child").attr("src");
            $(".big-img img").attr("src", src_first);
        });

        var src_first = $("#gallery_list_1 li img:first-child");
        $(".big-img img.main_image").attr("src", src_first.attr("src"));

        var image_title = src_first.attr("title");
        if(image_title){
            $(".big-img .gallery_image_title").css('display', 'block').html(image_title);
        }else{
            $(".big-img .gallery_image_title").css('display', 'none')
        }
        $(".big-img .gallery_image_title").html(image_title);

        $(".gallery_list li img").click(function (e) {
            if ($(this).attr('video_url')) {
                $(".video_iframe").find("iframe").attr('src', $(this).attr('video_url'));
                $(".big-img").hide();
                $(".video_iframe").show();
            } else {
                var src = $(this).attr("src");
                $(".big-img img.main_image").fadeOut('fast', function () {
                    $(".big-img img.main_image").attr('src', src);
                    $(".big-img img.main_image").fadeIn('fast');
                });
                var image_title = $(this).attr("title");
                if(image_title){
                    $(".big-img .gallery_image_title").css('display', 'block').html(image_title);
                }else{
                    $(".big-img .gallery_image_title").css('display', 'none');
                }
                $(".big-img").show();
                $(".video_iframe").hide();
            }

            e.preventDefault();
            return false;
        });
    }

    $(window).on('load', function () {
        $(".filter_element").first().trigger('click');
    });
</script>