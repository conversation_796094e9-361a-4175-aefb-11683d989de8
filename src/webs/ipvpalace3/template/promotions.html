<div class="buttons_filter_wrapper">
    <div class="offers_filter active">{{ T_ofertas }}</div>
    <div class="package_filter">{{ T_paquetes }}</div>
</div>

<div class="offers_wrapper">

    {% for offer_element in promotions %}
        <div class="cycle_block_element {% cycle 'left_block' 'right_block' %}" {% if offer_element.startDate %}data-startDate="{{ offer_element.startDate }}"{% endif %}{% if offer_element.endDate %} data-endDate="{{ offer_element.endDate }}"{% endif %}>
            <div class="text_cycle_wrapper">
                <h2 class="cycle_title_element">{{ offer_element.name|safe }}</h2>

                <div class="cycle_description">{{ offer_element.description|safe }}</div>
            </div>
            <div class="image_cycle_wrapper">
                <img src="{{ offer_element.picture }}" alt="" class="cycle_image_element">
            </div>

            <div class="buttons_wrapper">
                {% if offer_element.link %}<a href="{{ offer_element.link }}" class="offer_link">{{ T_ver_mas }} <img class="right_arrow_button" src="/img/{{ base_web }}/right_arrow_corp.png"> </a>{% endif %}
                <a class="offer_button" href="#data" {% if offer_element.smartDatasAttributes %}{{ offer_element.smartDatasAttributes }}{% endif %} {% if offer_element.promocode %}data-promocode="{{ offer_element.promocode }}"{% endif %}>{{ T_calendario_disponibilidad }}</a>
            </div>
        </div>
    {% endfor %}

</div>



<div class="package_wrapper" style="display: none">

    {% for x in package_info %}
        <div class="cycle_block_element {% cycle 'left_block' 'right_block' %}">
            <div class="text_cycle_wrapper">
                <h2 class="cycle_title_element">{{ x.title|safe }}</h2>

                <div class="cycle_description">{{ x.description|safe }}</div>
            </div>
            <div class="image_cycle_wrapper">
                <img src="{{ x.servingUrl }}" alt="x.altText" class="cycle_image_element">
            </div>

            <div class="buttons_wrapper">
                {% if x.linkUrl %}<a href="{{ x.linkUrl }}" class="offer_link">{{ T_ver_mas }} <img class="right_arrow_button" src="/img/{{ base_web }}/right_arrow_corp.png?v=1"> </a>{% endif %}
                <a class="button-promotion" href="#data" {% if x.smartdateini %}data-smartdateini="{{ x.smartdateini|safe }}"{% endif %} {% if x.smartdatefin %}data-smartdatefin="{{ x.smartdatefin|safe }}"{% endif %} {% if x.smartpackage %}data-smartpackage="{{ x.smartpackage|safe }}"{% endif %} {% if x.only_weekend %}only-weekend="true"{% endif %}>{{ T_reservar }}</a>
            </div>
        </div>
    {% endfor %}

</div>
<input type="hidden" name="btn_text" value="{{ T_calendario_disponibilidad }}">
<input type="hidden" name="book_text" value="{{ T_reservar }}">
<script type="text/javascript" src="/js/{{ base_web }}/offers.js?v=1.1"></script>
<script>
    $(function(){
        $(".offers_filter").click(function () {
            $(".offers_filter").toggleClass('active');
            $(".package_filter").toggleClass('active');

            $(".offers_wrapper").slideDown('fast', function(){
                $(".package_wrapper").slideUp('fast');
            });
        });

        $(".package_filter").click(function () {
            $(".offers_filter").toggleClass('active');
            $(".package_filter").toggleClass('active');

            $(".package_wrapper").slideDown('fast', function(){
                $(".offers_wrapper").slideUp('fast');
            });
        })
    });
</script>