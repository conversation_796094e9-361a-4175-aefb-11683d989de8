@keyframes dissapear {
  0% {opacity: 1;}
  50% {opacity: 0;}
  100% {opacity: 1;}
}

.lateral_options_main_content {
  padding: 70px 0 30px;
  background: #F5F5F5;
  text-align: center;
  color: #333333;
  font-family: 'Raleway', sans-serif;
  font-size: 15px;
  line-height: 30px;

  .description_wrapper {
    hide {
      max-height: 0;
      overflow: hidden;
      display: block;
      @include transition(max-height, .5s);

      &.active {
        max-height: 800px;
      }
    }

    .see_more, .see_less {
      color: $corporate_1;
      text-transform: uppercase;
      text-decoration: underline;
      font-family: '<PERSON>', sans-serif;
      cursor: pointer;
      display: table;
      margin: auto;
      @include transition(opacity, .5s);

      &:hover {
        opacity: 0.7;
      }
    }

    .disabled {
      display: none;
    }
  }
}

.lateral_options_block {
  margin: 30px auto;
  display: table;
  width: 100%;

  .lateral_options_left {
    width: 31%;
    float: left;
    border: 1px solid $corporate_1;

    .option_element {
      border-bottom: 1px solid $corporate_1;
      font-family: '<PERSON>', sans-serif;
      font-size: 15px;
      letter-spacing: 0.82px;
      padding: 15px 20px;
      cursor: pointer;

      i {
        color: $corporate_1;
        font-size: 46px;
        vertical-align: middle;
        margin-right: 14px;
      }


      &:last-of-type {
        border-bottom: 0;
      }

      &.active {
        background: $corporate_1;
        color: white;

        i {
          color: white;
        }
      }
    }
  }

  .lateral_options_right {
    width: 60%;
    float: right;

    &.modifying {
      animation-name: dissapear;
      animation-duration: 1s;
    }

    .title_selection {
      font-family: 'Oswald', sans-serif;
      font-size: 15px;
      margin-bottom: 30px;

      i {
        font-size: 46px;
        vertical-align: middle;
        margin-right: 20px;
        color: $corporate_1;
      }
    }

    .content_selection {
      ul {
        li {
          margin-bottom: 45px;
          font-size: 15px;
          color: #333333;
          padding-left: 45px;
          position: relative;

          &:before {
            content: '\f00c';
            display: inline-block;
            font: normal normal normal 14px/1 FontAwesome;
            font-size: inherit;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: absolute;
            left: 0;
            top: -4px;
            color: #A0BB31;
            font-size: 27px;
          }
        }
      }
    }
  }
}