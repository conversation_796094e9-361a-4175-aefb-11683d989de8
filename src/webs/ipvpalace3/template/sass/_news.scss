.automatic_content_wrapper {
  .news_wrapper {
    width: 373px;
    display: inline-block;
    margin-right: 6px;
    position: relative;
    height: 440px;
    float: left;
    overflow: hidden;

    .news_img {
      img {
        @include center-image();
      }
    }
    

    .text_news_wrapper {
      padding: 35px 5% 25px;
      width: 100%;
      margin: auto;
      position: absolute;
      top: 340px;
      left: 0;
      right: 0;
      color: white;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      background: -webkit-linear-gradient(transparent, black);
      background: -o-linear-gradient(transparent, black);
      background: -moz-linear-gradient(transparent, black);
      background: linear-gradient(rgba(0, 0, 0, 0), #000000);
      -webkit-transition: all 0.5s;
      -moz-transition: all 0.5s;
      -ms-transition: all 0.5s;
      -o-transition: all 0.5s;
      transition: all 0.5s;
      height: 440px;

    }

    &:hover .text_news_wrapper {
      top: 0;
    }

    h4.news_title {
      font-size: 32px;
      line-height: 30px;
      font-family: '<PERSON>', sans-serif;
      font-weight: lighter;
      margin-bottom: 45px;

      &:after {
        content: '';
        display: block;
        width: 155px;
        height: 2px;
        background: white;
        margin: 11px 0;
      }
    }

    .news_description {
      margin-top: 8px;
      font-size: 12px;
      line-height: 25px;
    }
  }
}

h2.news_date {
    position: absolute;
    top: 61px;
    right: 80px;
    font-family: 'Oswald', sans-serif;
    font-size: 22px;
    color: $corporate_1;
    font-weight: lighter;
  }