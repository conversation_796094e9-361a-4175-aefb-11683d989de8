@import "compass";
@import "plugins/mixins";
@import "plugins/templateBaseline";

$color_1: #bb9242;
$color_2: #ffd600;

img {
  vertical-align: middle;
}

* {
  box-sizing: border-box;
}

body {
  padding: 0;
  font-family: Ralew<PERSON>, sans-serif;
}

#logotype {
  text-align: center;
  padding: 20px 0;
  background: #383838;
}

main {
  #main_content {
    text-align: center;
    margin-top: 100px;

    h1#title_section {
      color: $color_1;
      margin-bottom: 45px;
      font-size: 30px;
      font-family: Oswald, sans-serif;
    }

    #description_section {
      width: 600px;
      font-size: 13px;
      color: white;
      line-height: 25px;
      display: block;
      margin: auto;
    }
  }

  #newsletter {
    margin-top: 100px;
    text-align: center;

    #newsletter_form {
      input {
        width: 400px;
        display: block;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: 1px solid white;
        background: transparent;
        padding-left: 10px;
        height: 40px;
        color: white;
        margin: 10px auto;

        &::-webkit-input-placeholder {
          //color: lighten($color_1, 20%);
          color: white;
        }

        &::-moz-placeholder {
          color: lighten($color_1, 10%);
        }

        &:-ms-input-placeholder {
          color: lighten($color_1, 10%);
        }

        &:-moz-placeholder {
          color: lighten($color_1, 10%);
        }
      }

      label.error {
        color: white;
        margin-bottom: 15px;
        display: block;
        font-size: 12px;
      }

      #submit_newsletter {
        text-transform: uppercase;
        padding: 10px 30px;
        background: $color_2;
        color: #565656;
        margin: auto;
        border-radius: 0;
        border: none;
        cursor: pointer;
        font-size: 15px;
        @include transition(background, .4s);

        &:hover {
          background: $color_1;
        }
      }
    }

    #return_home {
      color: white;
      display: inline-block;
      margin-top: 10px;
      font-size: 11px;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }
    }
  }
}

@media (max-width: 440px) {
  main #newsletter #newsletter_form input {
    width: 100%;
  }
}