@font-face {
  font-family: 'nexa';
  src: url('/static_1/fonts/nexa/nexa-bold.otf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'nexa';
  src: url('/static_1/fonts/nexa/nexa-light.otf');
  font-weight: 100;
  font-style: normal;
}

#ui-datepicker-div {
  font-family: 'nexa', sans-serif;
  font-weight: lighter;
  border-radius: 0;
  width: auto;
  padding: 10px 14px;
  border: 1px solid #646464;

  .ui-datepicker-header {
    border-radius: 0;
  }

  .ui-widget-header {
    background: none !important;
    border: 0 !important;

    .ui-icon {
      background: url(/static_1/images/booking_5/left_arrow_gray.png);
      background-position: center !important;
      height: 22px;
      margin: 0;

      &:before {
        display: none;
      }
    }
  }

  .ui-datepicker-title {
    color: #646464 !important;
    margin-bottom: 15px;
    font-size: 29px;
    margin-top: 10px;
    font-weight: 100;
  }

  td {
    a, span {
      text-align: center;
      padding: 16px 20px 15px;
      font-size: 20px;
      font-weight: lighter;
      background: none !important;
      color: #646464;

      &.ui-state-active {
        background: gray!important;
        color: white;
      }
    }
  }

  thead {
    th span {
      color: #646464;
      font-weight: lighter;
      font-size: 22px;
    }
  }

  .ui-state-focus, .ui-state-hover, .ui-widget-content .ui-state-focus, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-focus, .ui-widget-header .ui-state-hover {
    border: 0;
    background: none;
    background-position: center !important;
  }

  .ui-datepicker-next {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  .ui-corner-all {
    span {
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }

  .ui-datepicker-next, .ui-datepicker-prev {
    top: 17px;
    cursor: pointer;
  }

  table tr {
    border-bottom: 1px solid #646464;
    td {
      border-right: 1px solid #646464;
      padding: 0;

      &:hover {
        background: lightgrey;

        a {
          color: white;
        }
      }

      &:first-of-type {
        border-left: 1px solid #646464;
      }
    }
  }
}

