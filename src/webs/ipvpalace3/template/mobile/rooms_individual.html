<h1 class="individual_room_title">{{ actual_section.title|safe }}</h1>
<div class="individual_room_pictures">
    <ul class="slides">
        {% for room_picture in actual_section.pictures %}
            <li class="room_picture_element">
                <img class="room_image" src="{{ room_picture|safe }}">
            </li>
        {% endfor %}
    </ul>
</div>
<div class="individual_room_description">{{ actual_section.content|safe }}</div>
{% if offer_individual %}
<div id="shareSocialArea">
    <script type="text/javascript"> var addthis_config = {ui_language: "es"} </script>
    <div class="addthis_toolbox addthis_default_style" addthis:title="{{ actual_section.title|safe }}" addthis:description='{{ description_share|safe }}'>
        <a href="https://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
           class="addthis_button_compact" style="color:#5A5655"><span class="share_text">{{ T_compartir }}</span></a>
        <span class="addthis_separator">|</span>
        <a class="addthis_button_facebook"></a>
        <a class="addthis_button_google"></a>
        <a class="addthis_button_twitter"></a>
        <a class="addthis_button_favorites"></a>
    </div>
    <script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>
</div>
{% endif %}


<script>
    $(function () {
        $(".individual_room_pictures").flexslider({
            controlNav: true
        });
    });
</script>