<div class="individual_room_picture">
    <h1 class="individual_room_title">{{ actual_section.title|safe }}</h1>
    <div class="background_overlay"></div>
    <img class="room_image" src="{{ actual_section.pictures.0|safe }}=s800">
</div>

<div class="booking_room_button_element button-promotion">{{ T_reservar }}</div>

<div class="individual_room_description">{{ actual_section.content|safe }}</div>
{% if not offer_individual %}
<div class="individual_room_gallery">{% for room_picture in actual_section.pictures %}<div {% if not loop.first %}class="room_picture_element"{% endif %}><a  href="{{ room_picture|safe }}=s800" data-fancybox="{{ actual_section.title|safe }}" data-caption="{{ actual_section.title|safe }} - {{loop.index}}/{{ actual_section.pictures|length }}" title="{{ actual_section.title|safe }}">{% if not loop.first %}<img class="room_image" src="{{ room_picture|safe }}">{% endif %}</a></div>{% endfor %}</div>

<script type="text/javascript">
$(window).on('load', function(){

    $(".myFancyPopup").fancybox({
        maxWidth: '100%',
        maxHeight: '100%',
        fitToView: false,
        width: '100%',
        height: '70%',
        padding: 5,
        type: 'image'
    });

});
</script>
{% endif %}
