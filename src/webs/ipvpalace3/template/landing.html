{% spaceless %}
    <!DOCTYPE html>
    <html lang="{{ language_code }}">
    <head>
        <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if section.sectionName %} {{ hotel_name|safe }} | {{ section.sectionName|safe }} {% else %}  {{ hotel_name|safe }} {% endif %} {% endif %}</title>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        {% if hotel_namespace %}
            <link rel="icon" href="/static_1/images/favicon_{{ hotel_namespace }}.ico" type="image/x-icon">
        {% else %}
            <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
        {% endif %}

        <!-- styles -->
        <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/landing.css?v=2"/>
        <link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic|Oswald:200,400,300,700|Raleway:400,300,500,700' rel='stylesheet' type='text/css'>
    </head>
    <body class="{{ language }}"
          style="background: url({{ image_background.servingUrl|safe }}=s1900);background-attachment: fixed;background-size: cover;">
    <div id="logotype">
        <img src="{{ logotype|safe }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
    </div>

    <main>
        <section id="main_content">
            <h1 id="title_section">{{ section.subtitle|safe }}</h1>

            <div id="description_section">{{ section.content|safe }}</div>
        </section>
        <section id="newsletter">
            <form class="form_newsletter" id="newsletter_form" action="/utils?action=promopopup" method="post">
                <input type="email" placeholder="{{ T_email }}" name="email" id="email" required>
                <input type="hidden" name="language" value="{{ language_code }}"/>
                <input type="submit" id="submit_newsletter" value="{{ T_enviar }}">
            </form>
            <a href="/{% if language_code != "es" %}{{ language_code }}/{% endif %}"
               id="return_home">{{ T_volver_inicio }}</a>
        </section>


    </main>

    {{ jquery|safe }}
    <script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
    <script>
        $(window).on('load', function () {
            $(".form_newsletter").validate({
                messages: {
                    name: "{{ T_campo_obligatorio}}",
                    surname: "{{ T_campo_obligatorio}}",
                    email: {
                        required: "{{ T_campo_obligatorio|safe }}",
                        email: "{{ T_campo_valor_invalido|safe }}"
                    }
                },
                submitHandler: function (form) {
                    $.post($(form).attr("action"), $(form).serialize(), function () {
                        alert("{{ T_gracias_newsletter }}");
                        form.reset();
                        window.location = "/{% if language_code != "es" %}{{ language_code }}/{% endif %}"
                    })
                }
            });


        });
    </script>

    </body>
    </html>
{% endspaceless %}
