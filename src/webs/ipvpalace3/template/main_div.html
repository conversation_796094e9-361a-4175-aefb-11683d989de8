<div id="main-sections">
	<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
		{% for section in menu_personalized %}
		<li class="main-section-div-wrapper" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}

					<a>
                        <img class="menu_icon" src="{{ section.icon.0.servingUrl|safe }}">
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
            {% else %}

                {% if section.title %}

                <a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrlInternational}}">
                    <img class="menu_icon" src="{{ section.icon.0.servingUrl|safe }}">
                    <span itemprop="name">{{ section.title|safe }}</span>
                </a>

                {% endif %}

            {% endif %}

            {% if section.subsections %}
            <ul>


                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">

                         {% if subsection.title %}


                            <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrlInternational}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                            {{ subsection.title|safe}}
                            </a>

                          {% endif %}    

                    </li>
                {% endfor %}
            </ul>
            {% endif %}


		</li>
		{% endfor %}


{#        <li class="main-section-div-wrapper">#}
{#            <a class="button-promotion" href="#data">#}
{#                <img class="menu_icon" src="/static_1/images/booking_5/offer_header.png" style="height: 33px;">#}
{#                <span itemprop="name">{{ T_reservar }}</span>#}
{#            </a>#}
{#        </li>#}
	</ul>
</div>