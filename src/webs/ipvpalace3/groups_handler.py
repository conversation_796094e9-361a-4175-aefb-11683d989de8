import os
import re
from flask.views import MethodView

from booking_process.models.payment.credit_card_model import CreditCardDetails
from booking_process.utils.booking.bookingUtils import encryptCreditCard
from models.reservations import Reservation
from paraty_commons_3.email_utils import sendEmail
from booking_process.constants.advance_configs_names import EMAIL_SENDER, MANAGEMENT_LANGUAGE, EMAIL_CONTACT_FORMS, \
	CREDIT_CARD_ENCRYPTION
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.hotel_data import get_hotel_manager_email
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.templates.template_utils import buildTemplate


class GroupsHandler(MethodView):

	def build_fields_for_message(self, form_fields):
		"""
		creates the subject and sender for emails sent
		"""
		section = form_fields.get('section', '')
		emailSubject = form_fields.get('emailSubject', '')
		emailSubjectCustomized = form_fields.get('emailSubjectCustomized', '')
		hotel_name = get_config_property_value(EMAIL_SENDER)

		#Translate title email contact
		manager_language = get_config_property_value(MANAGEMENT_LANGUAGE)
		language_items = manager_language if manager_language else SPANISH
		form_contact_title = get_web_dictionary(language_items)["T_formulario_grupos"]
		form_section_title = get_web_dictionary(language_items)["T_formulario_seccion"]

		if emailSubject:
			message_subject = emailSubject + " en " + hotel_name
			email_sender = u" %s %s <%s>" % (emailSubject, get_config_property_value(EMAIL_SENDER), '<EMAIL>')

		elif emailSubjectCustomized:
			message_subject = emailSubjectCustomized
			#email_sender = u" %s %s <%s>" % (emailSubjectCustomized, getConfigPropertyValue(EMAIL_SENDER), '<EMAIL>')
			if "-" in get_config_property_value(EMAIL_SENDER):
				reply_email = get_config_property_value(EMAIL_SENDER).split("-")
				email_sender = u" %s <%s>" % (emailSubjectCustomized, reply_email[1])
			else:
				email_sender = u" %s <%s>" % (emailSubjectCustomized, '<EMAIL>')

		elif section:
			message_subject = form_section_title + u" %s, %s " % (section, hotel_name)
			email_sender = form_section_title + u" %s %s <%s>" % (section, get_config_property_value(EMAIL_SENDER), '<EMAIL>')
		else:
			message_subject = form_contact_title + " " + hotel_name
			email_sender = form_contact_title + u" %s <%s>" % (get_config_property_value(EMAIL_SENDER), '<EMAIL>')

		return (message_subject, email_sender)


	def get_destination_addresses(self, form_fields):

		#Test purposes
		if form_fields.get('comments') and '@@@TEST@@@' in form_fields.get('comments'):
			return [form_fields.get('email')]

		email_contact = get_config_property_value(EMAIL_CONTACT_FORMS)

		if email_contact:
			return email_contact
		else:
			return re.split(r'[,;]+', get_hotel_manager_email())

	def get(self, *args, **kwargs):
		manager_language = get_config_property_value(MANAGEMENT_LANGUAGE)
		language_items = manager_language if manager_language else SPANISH
		form_fields = dict(list(self.request.POST.items()) + list(get_web_dictionary(language_items).items()))
		email_addresses = self.get_destination_addresses(form_fields)
		template_path = os.path.join(os.path.dirname(__file__), 'template/emails/_fast_booking_mail.html')
		message_subject, emailSender = self.build_fields_for_message(form_fields)

		#Generate cc encrypted
		cc_details = CreditCardDetails()
		if form_fields['credit_card']:
			cc_details.number = form_fields['credit_card']
		# if form_fields['cvv']:
		# 	cc_details.cvv = form_fields['cvv']

		reservation = Reservation()
		reservation.name = form_fields['name']
		reservation.email = form_fields['email']
		reservation.telephone = form_fields['telephone']
		encrypt_password = get_config_property_value(CREDIT_CARD_ENCRYPTION)
		encrypted_link = encryptCreditCard(cc_details, reservation, encrypt_password)
		form_fields['cc_link'] = unescape(encrypted_link)

		fast_booking_template = buildTemplate(template_path, form_fields)

		sendEmail(email_addresses, message_subject, fast_booking_template, fast_booking_template)
