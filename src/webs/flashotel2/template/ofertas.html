
<div class="top_content_wrapper offers_wrapper container12">

    <div class="scapes-blocks">
        {% for block in blocks %}

            <div class="block {% cycle 'row1' 'row2' %} {{ block.priority }} all_offers">
                <div class="description">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    <ul>

                    <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                    
                    {% if not block.promocode == "NORESERVAR" %}

                        <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva" data-promocode="{% if block.promocode  %}{{ block.promocode }}{%  endif %}"   data-forced-booking="{% if block.forcedBooking  %}{{ block.forcedBooking }}{%  endif %}">{{ T_reservar }}</a> </li>
                    {% endif %}

                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                    <img src="{{block.picture}}=s1000">
                </a>

                <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    {{block.description|safe}}
                </div>

            </div>
        {% endfor %}
        <span class="stretch"></span>
    </div>
</div>


<script>

    $(".offer_selector").click(function(){
        $(".offer_selector.selected").removeClass("selected");
        $(this).addClass("selected");
        elements = $(this).attr('id');
        $(".block:not(." + elements + ")").hide('fast');
        $("." + elements).show('fast');
        $(".everywhere").show('fast');
    });

</script>