@import "compass";

//@import "defaults";

//Base web (change too in templateHandler and in config.rb)
$base_web: "flas2";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #AF0929;
$corporate_2: #787878;
$corporate_3: #b9ce1b;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

@import "mixins";
@import "booking/booking_engine_2";
@import "booking_engine";
@import "booking/selectric";
@import "booking_engine_inner";

@import "template_specific";

#logoDiv a img {
	width: 200px;
}

#full_wrapper_booking #fecha_entrada input, #fecha_salida input{
  background: white url(/img/#{$base_web}/ico-rosamar/ico-calendario-header.png) no-repeat 93% !important;
}

#full_wrapper_booking #motor_reserva select, #full_wrapper_booking #motor_reserva select.selector_adultos, #full_wrapper_booking #motor_reserva select.selector_ninos{
  background: white url(/img/#{$base_web}/ico-rosamar/down-arrow.png) no-repeat 93% !important;
}

header #section-active a {
    color: $corporate_1;
}

header #main_menu a:hover {
    color: $corporate_1;
}

header #main_menu .button-promotion:hover {
    color: white !important;
    opacity: 0.8;
}

footer .footer_column_title {
	color: $corporate_1 !important;
}

.gallery_filt_title {
    color: $corporate_1 !important;
    border-bottom-color: $corporate_1 !important;
}