/* line 1, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine_2.scss */
.booking_form_title {
  background: #AF0929;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
  position: relative;
}
/* line 16, ../../../../sass/booking/_booking_engine_2.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 21, ../../../../sass/booking/_booking_engine_2.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}
/* line 26, ../../../../sass/booking/_booking_engine_2.scss */
.booking_form_title:before {
  position: absolute;
  left: 0px;
  margin: 0px auto;
  right: 0px;
  bottom: -8px;
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 8px solid #787878;
}

/* line 42, ../../../../sass/booking/_booking_engine_2.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 49, ../../../../sass/booking/_booking_engine_2.scss */
.destination_wrapper {
  position: relative;
}
/* line 51, ../../../../sass/booking/_booking_engine_2.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 57, ../../../../sass/booking/_booking_engine_2.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 65, ../../../../sass/booking/_booking_engine_2.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #AF0929 url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 79, ../../../../sass/booking/_booking_engine_2.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 86, ../../../../sass/booking/_booking_engine_2.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 95, ../../../../sass/booking/_booking_engine_2.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 110px;
}
/* line 102, ../../../../sass/booking/_booking_engine_2.scss */
.date_box:before {
  position: absolute;
  left: 0px;
  margin: 0px auto;
  right: 0px;
  top: -8px;
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid white;
}
/* line 116, ../../../../sass/booking/_booking_engine_2.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 122, ../../../../sass/booking/_booking_engine_2.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #AF0929;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
  border-bottom: 0px !important;
}
/* line 131, ../../../../sass/booking/_booking_engine_2.scss */
.date_box .date_day .month {
  padding-top: 5px;
  font-weight: lighter;
  font-size: 18px;
  border-bottom: 1px solid darkgrey;
  padding-bottom: 2px;
  color: #AF0929;
}
/* line 140, ../../../../sass/booking/_booking_engine_2.scss */
.date_box .date_day .day {
  font-weight: lighter;
  font-size: 43px;
  border-bottom: 1px solid darkgray;
  padding-bottom: 2px;
  color: #AF0929;
}
/* line 149, ../../../../sass/booking/_booking_engine_2.scss */
.date_box .date_year {
  color: #AF0929;
  font-size: 16px;
  height: 14px;
  line-height: 14px;
  margin-top: 3px;
}

/* line 158, ../../../../sass/booking/_booking_engine_2.scss */
.room {
  padding-left: 33.33%;
  clear: both;
  position: relative;
  display: table;
}
/* line 163, ../../../../sass/booking/_booking_engine_2.scss */
.room .room_title {
  position: absolute;
  left: 0px;
  top: 35px;
  bottom: 0px;
  display: table;
  margin: auto !important;
}
/* line 172, ../../../../sass/booking/_booking_engine_2.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 2px;
}
/* line 177, ../../../../sass/booking/_booking_engine_2.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 183, ../../../../sass/booking/_booking_engine_2.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 188, ../../../../sass/booking/_booking_engine_2.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 197, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button {
  position: relative;
  text-align: left;
  display: table;
  width: 100%;
}
/* line 203, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #AF0929;
  height: 40px;
  font-size: 16px;
  width: 100%;
  display: none;
}
/* line 216, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button .promocode_text {
  color: #575757;
  text-decoration: underline;
  text-align: center;
  margin-top: 15px;
  cursor: pointer;
}
/* line 222, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button .promocode_text strong {
  color: #AF0929;
}
/* line 227, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #AF0929;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 240, ../../../../sass/booking/_booking_engine_2.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 247, ../../../../sass/booking/_booking_engine_2.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 252, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 257, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 262, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 270, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 274, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 278, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 282, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 286, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 290, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 294, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .promocode_input {
  width: 100%;
  display: none;
}
/* line 298, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .promocode_input ::-webkit-input-placeholder {
  text-align: center;
}
/* line 302, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .promocode_input :-moz-placeholder {
  /* Firefox 18- */
  text-align: center;
}
/* line 307, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .promocode_input ::-moz-placeholder {
  /* Firefox 19+ */
  text-align: center;
}
/* line 312, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .promocode_input :-ms-input-placeholder {
  text-align: center;
}
/* line 317, ../../../../sass/booking/_booking_engine_2.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 323, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 330, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 335, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 342, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 346, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 351, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 355, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 359, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 367, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 373, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 376, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 383, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 389, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 393, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 398, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .promocode_input {
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 404, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 408, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 413, ../../../../sass/booking/_booking_engine_2.scss */
.selectric {
  height: 110px !important;
  position: relative;
}
/* line 417, ../../../../sass/booking/_booking_engine_2.scss */
.selectric:before {
  position: absolute;
  left: 0px;
  margin: 0px auto;
  right: 0px;
  top: -8px;
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid white;
}
/* line 431, ../../../../sass/booking/_booking_engine_2.scss */
.selectric .label {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  display: table !important;
  margin: auto;
  text-align: center !important;
  font-size: 40px !important;
  margin-left: 0px !important;
}
/* line 443, ../../../../sass/booking/_booking_engine_2.scss */
.selectric button {
  display: none;
}
/* line 447, ../../../../sass/booking/_booking_engine_2.scss */
.selectric .more_room_button, .selectric .less_room_button {
  height: 20px;
  width: 100%;
  position: absolute;
  top: 12px;
  text-align: center;
  z-index: 3;
}
/* line 456, ../../../../sass/booking/_booking_engine_2.scss */
.selectric .less_room_button {
  bottom: 12px;
  top: auto;
  background: url(/static_1/images/booking/arrow_down_small.png) no-repeat center center;
}
/* line 462, ../../../../sass/booking/_booking_engine_2.scss */
.selectric .more_room_button {
  background: url(/static_1/images/booking/arrow_up.png) no-repeat center center;
}

/* line 468, ../../../../sass/booking/_booking_engine_2.scss */
.adults_selector .label, .children_selector .label {
  width: 70%;
}
/* line 471, ../../../../sass/booking/_booking_engine_2.scss */
.adults_selector .button, .children_selector .button {
  display: block !important;
  background: url(/static_1/images/booking/arrow_down_big.png) no-repeat center center !important;
}

/* line 478, ../../../../sass/booking/_booking_engine_2.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 483, ../../../../sass/booking/_booking_engine_2.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 487, ../../../../sass/booking/_booking_engine_2.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 491, ../../../../sass/booking/_booking_engine_2.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #AF0929;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 500, ../../../../sass/booking/_booking_engine_2.scss */
.fancybox-inner {
  overflow: visible !important;
}

/* line 508, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 512, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 516, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 521, ../../../../sass/booking/_booking_engine_2.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 532, ../../../../sass/booking/_booking_engine_2.scss */
.room_list .selectric {
  height: 55px !important;
}

/* line 537, ../../../../sass/booking/_booking_engine_2.scss */
.departure_date_wrapper {
  margin: 0px 2px;
}

/* line 541, ../../../../sass/booking/_booking_engine_2.scss */
.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label {
  color: #414141;
  text-align: center;
  margin: 6px 0px 13px;
}

/* line 547, ../../../../sass/booking/_booking_engine_2.scss */
.stay_selection label {
  width: 100%;
  text-align: center !important;
  display: table;
  margin: 0px auto 14px !important;
}

@-moz-document url-prefix() {
  /* line 557, ../../../../sass/booking/_booking_engine_2.scss */
  .selectric .label {
    top: 6px;
  }

  /* line 561, ../../../../sass/booking/_booking_engine_2.scss */
  .rooms_number .selectric .label {
    top: 33px;
  }
}
/*=============== Booking Widget ================*/
/* line 3, ../sass/_booking_engine.scss */
div#wrapper_booking {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  height: 545px;
}

/* line 12, ../sass/_booking_engine.scss */
.spinner_wrapper {
  text-align: center;
  position: relative !important;
  width: 25px;
  height: 25px;
  margin: auto;
  right: 0px !important;
  bottom: 0px !important;
  padding: 15px;
  display: none;
}

/* line 24, ../sass/_booking_engine.scss */
.interior #wrapper_booking {
  height: 600px;
  bottom: auto;
}

/* line 29, ../sass/_booking_engine.scss */
.fancybox-inner {
  overflow: initial;
}

/* line 33, ../sass/_booking_engine.scss */
.border_booking {
  height: auto !important;
  width: 235px !important;
  box-sizing: border-box;
  background: white;
}

/* line 40, ../sass/_booking_engine.scss */
#data, .landing_booking_popup {
  height: auto !important;
}

/* line 49, ../sass/_booking_engine.scss */
.interior .booking_widget {
  top: 0px;
  margin-top: 105px;
}

/* line 55, ../sass/_booking_engine.scss */
.booking_widget, #data, .landing_booking_popup {
  bottom: 18px;
  margin: auto;
  height: 287px;
  width: 260px;
}
/* line 62, ../sass/_booking_engine.scss */
.booking_widget .room, #data .room, .landing_booking_popup .room {
  padding-left: 70px;
  margin-left: 12px;
}
/* line 67, ../sass/_booking_engine.scss */
.booking_widget .month, #data .month, .landing_booking_popup .month {
  overflow: hidden;
}
/* line 71, ../sass/_booking_engine.scss */
.booking_widget .booking_form, #data .booking_form, .landing_booking_popup .booking_form {
  height: auto;
  box-sizing: border-box;
  margin: 0px !important;
  padding: 0px;
  width: 100%;
}
/* line 78, ../sass/_booking_engine.scss */
.booking_widget .booking_form .stay_selection, #data .booking_form .stay_selection, .landing_booking_popup .booking_form .stay_selection {
  display: table;
  text-align: center;
  margin: 0 auto;
  padding-top: 20px;
}
/* line 84, ../sass/_booking_engine.scss */
.booking_widget .booking_form .stay_selection label, #data .booking_form .stay_selection label, .landing_booking_popup .booking_form .stay_selection label {
  text-transform: uppercase;
  font-size: 10px;
}
/* line 90, ../sass/_booking_engine.scss */
.booking_widget .booking_form .wrapper_booking_button button, #data .booking_form .wrapper_booking_button button, .landing_booking_popup .booking_form .wrapper_booking_button button {
  border-radius: 0px;
  width: 211px;
  float: none;
  margin: 8px auto 0px;
  display: block;
  font-weight: lighter;
  font-size: 20px;
  font-family: 'Source Sans Pro', sans-serif;
  background: #b9ce1b url("/static_1/images/booking/flecha_motor_der.png") no-repeat scroll 95% 50%;
  height: 30px;
  margin-bottom: 12px;
}
/* line 105, ../sass/_booking_engine.scss */
.booking_widget .best_price, #data .best_price, .landing_booking_popup .best_price {
  display: none;
}
/* line 109, ../sass/_booking_engine.scss */
.booking_widget h4.booking_title_2, #data h4.booking_title_2, .landing_booking_popup h4.booking_title_2 {
  display: block;
  font-family: yanone, sans-serif;
  text-transform: uppercase;
  font-weight: 300;
  font-size: 16px;
  line-height: 25px;
}
/* line 118, ../sass/_booking_engine.scss */
.booking_widget .booking_form_title:before, #data .booking_form_title:before, .landing_booking_popup .booking_form_title:before {
  border-top: 8px solid #AF0929;
  bottom: -7px;
}
/* line 123, ../sass/_booking_engine.scss */
.booking_widget .booking_form_title, #data .booking_form_title, .landing_booking_popup .booking_form_title {
  background: #AF0929;
  width: 100%;
  height: 41px;
  box-sizing: border-box;
}
/* line 129, ../sass/_booking_engine.scss */
.booking_widget .booking_form_title:not(.wrapper-new-web-support), #data .booking_form_title:not(.wrapper-new-web-support), .landing_booking_popup .booking_form_title:not(.wrapper-new-web-support) {
  padding: 9px;
  height: 40px;
}
/* line 143, ../sass/_booking_engine.scss */
.booking_widget .date_box, #data .date_box, .landing_booking_popup .date_box {
  background: #eaeaea;
  border-radius: 0px;
}
/* line 147, ../sass/_booking_engine.scss */
.booking_widget .date_box .date_year, #data .date_box .date_year, .landing_booking_popup .date_box .date_year {
  color: #888282;
}
/* line 151, ../sass/_booking_engine.scss */
.booking_widget .date_box:before, #data .date_box:before, .landing_booking_popup .date_box:before {
  border-bottom: 8px solid #eaeaea;
}
/* line 155, ../sass/_booking_engine.scss */
.booking_widget .date_box .date_day .day, #data .date_box .date_day .day, .landing_booking_popup .date_box .date_day .day {
  border-bottom: 1px solid #888282;
  font-weight: lighter;
  line-height: 50px;
  font-size: 39px;
  color: #888282;
  font-family: yanone, sans-serif;
}
/* line 164, ../sass/_booking_engine.scss */
.booking_widget .date_box .date_day .month, #data .date_box .date_day .month, .landing_booking_popup .date_box .date_day .month {
  border-bottom: 1px solid #888282;
  color: #888282;
}
/* line 170, ../sass/_booking_engine.scss */
.booking_widget .selectric, #data .selectric, .landing_booking_popup .selectric {
  background: #eaeaea;
  border-radius: 0px;
}
/* line 174, ../sass/_booking_engine.scss */
.booking_widget .selectric .label, #data .selectric .label, .landing_booking_popup .selectric .label {
  color: #888282 !important;
  font-family: yanone, sans-serif;
  font-size: 22px !important;
}
/* line 180, ../sass/_booking_engine.scss */
.booking_widget .selectric:before, #data .selectric:before, .landing_booking_popup .selectric:before {
  border-bottom: 8px solid #EAEAEA;
  top: -6px;
}
/* line 185, ../sass/_booking_engine.scss */
.booking_widget .selectric .button, #data .selectric .button, .landing_booking_popup .selectric .button {
  display: none;
  background: url(/static_1/images/booking/arrow_down_small.png) no-repeat center center !important;
}
/* line 192, ../sass/_booking_engine.scss */
.booking_widget .selectricItems li.selected, #data .selectricItems li.selected, .landing_booking_popup .selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 198, ../sass/_booking_engine.scss */
.booking_widget .selectricItems li, #data .selectricItems li, .landing_booking_popup .selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  color: #a5a5a5;
  font-size: 25px;
  line-height: 40px;
  border-bottom: 1px solid #fff;
  padding: 10px 0px;
  width: 100%;
  text-align: center;
}
/* line 209, ../sass/_booking_engine.scss */
.booking_widget .selectricItems li:hover, #data .selectricItems li:hover, .landing_booking_popup .selectricItems li:hover {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}
/* line 216, ../sass/_booking_engine.scss */
.booking_widget .selectricItems ul, #data .selectricItems ul, .landing_booking_popup .selectricItems ul {
  z-index: 40;
}
/* line 220, ../sass/_booking_engine.scss */
.booking_widget .selectricItems .room, #data .selectricItems .room, .landing_booking_popup .selectricItems .room {
  padding-top: 17px;
}
/* line 225, ../sass/_booking_engine.scss */
.booking_widget .room_title, #data .room_title, .landing_booking_popup .room_title {
  padding-left: 11px;
}
/* line 229, ../sass/_booking_engine.scss */
.booking_widget .adults_selector, #data .adults_selector, .landing_booking_popup .adults_selector {
  padding-left: 12px;
}
/* line 233, ../sass/_booking_engine.scss */
.booking_widget input.promocode_input, #data input.promocode_input, .landing_booking_popup input.promocode_input {
  background: #eaeaea;
  width: 211px !important;
  margin-left: 24px;
  border-radius: 0px !important;
  height: 30px;
}
/* line 241, ../sass/_booking_engine.scss */
.booking_widget .stay_selection .entry_date_wrapper label, .booking_widget .stay_selection .departure_date_wrapper label, .booking_widget .stay_selection .rooms_number_wrapper label, .booking_widget .room .room_title, .booking_widget .room .adults_selector label, .booking_widget .room .children_selector label, .booking_widget .wrapper_booking_button .promocode_text, #data .stay_selection .entry_date_wrapper label, #data .stay_selection .departure_date_wrapper label, #data .stay_selection .rooms_number_wrapper label, #data .room .room_title, #data .room .adults_selector label, #data .room .children_selector label, #data .wrapper_booking_button .promocode_text, .landing_booking_popup .stay_selection .entry_date_wrapper label, .landing_booking_popup .stay_selection .departure_date_wrapper label, .landing_booking_popup .stay_selection .rooms_number_wrapper label, .landing_booking_popup .room .room_title, .landing_booking_popup .room .adults_selector label, .landing_booking_popup .room .children_selector label, .landing_booking_popup .wrapper_booking_button .promocode_text {
  color: #868686;
}
/* line 245, ../sass/_booking_engine.scss */
.booking_widget .promocode_text, #data .promocode_text, .landing_booking_popup .promocode_text {
  font-family: yanone, sans-serif;
}

/* line 254, ../sass/_booking_engine.scss */
.selectricOpen .selectricItems {
  z-index: 22222;
}

/* line 258, ../sass/_booking_engine.scss */
.room_list .selectricOpen .selectricItems {
  top: 35px !important;
}

/* line 262, ../sass/_booking_engine.scss */
.rooms_number_wrapper .selectricOpen .selectricItems {
  top: 75px !important;
}

/* line 266, ../sass/_booking_engine.scss */
.wrapper-new-web-support {
  opacity: 1 !important;
  border-radius: 0px !important;
}
/* line 270, ../sass/_booking_engine.scss */
.wrapper-new-web-support:before {
  content: none;
}

/* line 275, ../sass/_booking_engine.scss */
.date_box, .selectric {
  height: 75px !important;
  width: 66px;
}

/* line 280, ../sass/_booking_engine.scss */
.date_box .date_day .month {
  font-size: 12px;
}

/* line 284, ../sass/_booking_engine.scss */
.booking_widget .date_box .date_day .day, #data .date_box .date_day .day, .landing_booking_popup .date_box .date_day .day {
  font-size: 23px;
  line-height: 27px !important;
  padding-bottom: 0px;
  padding-top: 2px;
}

/* line 291, ../sass/_booking_engine.scss */
.date_box .date_year {
  font-size: 12px;
}

/* line 295, ../sass/_booking_engine.scss */
.selectric .more_room_button {
  top: 3px;
}

/* line 299, ../sass/_booking_engine.scss */
.selectric .less_room_button {
  bottom: 3px;
}

/* line 303, ../sass/_booking_engine.scss */
.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper, .room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
  width: 68px;
}

/* line 307, ../sass/_booking_engine.scss */
.selectricWrapper {
  width: 65px;
  height: 35px;
}

/* line 312, ../sass/_booking_engine.scss */
.room_list .selectric {
  height: 35px !important;
}

/* line 316, ../sass/_booking_engine.scss */
.wrapper_booking_button .promocode_text {
  text-transform: uppercase;
  cursor: pointer;
  font-size: 10px;
  text-decoration: none;
  margin-top: 10px;
}
/* line 323, ../sass/_booking_engine.scss */
.wrapper_booking_button .promocode_text strong {
  color: initial;
  font-size: 10px !important;
  font-weight: lighter;
  color: #868686;
}

/* line 331, ../sass/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  font-size: 13px;
}

/* line 335, ../sass/_booking_engine.scss */
.room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  font-size: 10px;
  text-transform: uppercase;
  margin: 6px 0px 9px;
}

/* line 341, ../sass/_booking_engine.scss */
.stay_selection label {
  margin: 0px auto 11px !important;
}

/* line 345, ../sass/_booking_engine.scss */
.booking_form_title {
  padding-top: 9px;
  padding-bottom: 11px;
}

/* line 350, ../sass/_booking_engine.scss */
.room .room_title {
  top: 28px;
}

/* line 354, ../sass/_booking_engine.scss */
.wrapper-new-web-support {
  height: auto !important;
  font-size: 13px;
  background-color: #AF0929 !important;
}

/* line 360, ../sass/_booking_engine.scss */
.wrapper-new-web-support .web_support_number {
  font-size: 15px !important;
}

/* line 364, ../sass/_booking_engine.scss */
.rooms_number_wrapper .selectric .button {
  display: none;
}

/* line 368, ../sass/_booking_engine.scss */
.landing_booking_popup .wrapper-new-web-support {
  display: none;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #AF0929;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #AF0929 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 1, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking {
  width: 1140px;
  position: absolute;
  bottom: 20px;
  z-index: 22;
  left: 0;
  padding-top: 11px;
  right: 0;
  margin: auto;
}
/* line 11, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .numero_personas {
  float: left;
  display: table;
}
/* line 16, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .wrapper-old-web-support#booking_engine_title {
  display: block;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  background: rgba(0, 0, 0, 0.8);
  opacity: 1;
  margin-top: -4px;
  text-align: center;
  font-family: "Source Sans Pro";
}
/* line 27, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .wrapper-old-web-support#booking_engine_title .web_support_label_2 {
  display: inline-block;
  margin: 0 5px;
}

/* line 34, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #wrapper_booking {
  position: relative;
  padding: 0;
  width: auto;
  height: auto;
  display: table;
}

/* line 42, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #motor_reserva {
  position: relative !important;
  color: #5a5a5a;
  font-size: 12px;
  padding: 0px !important;
  margin: 0;
  width: auto !important;
  display: inline-block;
}

/* line 52, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #fecha_entrada input, #fecha_salida input {
  background: white url(/img/flas2/ico-flashotel/ico-calendario-header.png) no-repeat 93% !important;
  width: 125px !important;
  height: 37px !important;
  border: 0 !important;
  border-radius: 0px !important;
  padding-left: 15px;
  font-size: 13px;
  font-weight: 300;
  border-right: 2px solid #DCDCDC !important;
  background-size: 16px !important;
}
/* line 64, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #fecha_entrada input::-webkit-input-placeholder, #fecha_salida input::-webkit-input-placeholder {
  color: #7D7D7D;
  font-size: 13px;
}
/* line 69, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #fecha_entrada input:-moz-placeholder, #fecha_salida input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
  font-size: 13px;
}
/* line 75, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #fecha_entrada input::-moz-placeholder, #fecha_salida input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
  font-size: 13px;
}
/* line 81, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #fecha_entrada input:-ms-input-placeholder, #fecha_salida input:-ms-input-placeholder {
  color: #7D7D7D;
  font-size: 13px;
}

/* line 87, ../sass/_booking_engine_inner.scss */
.colocar_fechas {
  margin: 0 !important;
}

/* line 91, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #contenedor_habitaciones,
#full_wrapper_booking #contenedor_opciones {
  width: auto !important;
  margin: 0 0 0 5px !important;
}

/* line 97, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #contenedor_fechas {
  width: auto;
}

/* line 101, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #contenedor_habitaciones {
  width: auto !important;
  height: auto;
  margin-top: 5px !important;
  margin-left: 0px !important;
}

/* line 108, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #contenedor_opciones {
  margin-top: 5px !important;
  margin-left: 0px !important;
}

/* line 113, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #titulo_fecha_entrada, #titulo_fecha_salida {
  float: left;
  margin-right: 10px;
  width: 120px !important;
  margin-top: 10px;
}

/* line 120, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #booking_engine_title {
  padding: 10px;
  width: 175px;
  font-weight: 500;
  border-left: 1px solid white;
  border-right: 1px solid white;
  margin-top: 11px;
  display: none;
}

/* line 130, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #booking_title1 {
  display: none;
  text-align: center;
  font-size: 18px;
  color: #AF0929;
  text-transform: uppercase;
}

/* line 138, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #booking_title2 {
  text-align: center;
  font-size: 18px;
  line-height: 30px;
  color: #AF0929;
  text-transform: uppercase;
}

/* line 146, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #best_price {
  text-align: center;
  font-size: 11px;
  color: #AF0929;
  text-transform: uppercase;
}

/* line 153, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #info_ninos {
  font-size: 9px !important;
  top: 3px;
  left: 170px;
  width: 100px;
  display: none;
}

/* line 161, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #search-button {
  border-radius: 0px !important;
  height: 39px !important;
  width: 170px !important;
  background: #bdcd37;
  color: white;
  margin: auto !important;
  width: 150px;
  text-transform: uppercase;
}
/* line 171, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #search-button:hover {
  opacity: 0.9;
}

/* line 176, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #search-button:hover {
  background-color: #787878;
}

/* line 180, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .spinner {
  text-align: center;
  height: 30px;
}

/* line 185, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio input {
  width: 130px !important;
  height: 37px !important;
  border: 0px !important;
  border-radius: 0px !important;
  margin: 0px auto 0px !important;
  margin-right: 0px !important;
  text-align: center;
  font-size: 13px;
  font-weight: 300;
  padding: 1px !important;
  color: gray;
  float: left;
}
/* line 199, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio input::-webkit-input-placeholder {
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 205, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio input:-moz-placeholder {
  /* Firefox 18- */
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 212, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio input::-moz-placeholder {
  /* Firefox 19+ */
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}
/* line 219, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio input:-ms-input-placeholder {
  color: #7D7D7D;
  font-size: 10px;
  padding-top: 2px;
}

/* line 226, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #envio {
  text-align: center;
  padding-bottom: 0px;
  margin-left: 9px;
  height: auto;
  width: auto;
}

/* line 234, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #ui-datepicker div {
  z-index: 9999 !important;
}

/* line 238, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #selector_habitaciones {
  width: 75px !important;
}

/* line 242, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .adultos {
  margin: 0 !important;
}

/* line 246, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .ninos {
  float: left;
  margin: 0px;
}

/* line 251, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #ui-datepicker-div {
  z-index: 99999 !important;
}

/* line 255, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #motor_reserva select {
  height: 39px !important;
  width: 134px !important;
  padding: 5px;
  padding-left: 15px;
  font-size: 13px;
  line-height: 100%;
  border-right: 2px solid #DCDCDC !important;
  border: 0px;
  border-radius: 0;
  -webkit-appearance: none;
  color: #7D7D7D;
  font-weight: 300;
  margin-bottom: 0px;
  background: white url(/img/flas2/select_down.png) no-repeat 93% !important;
}
/* line 271, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #motor_reserva select.selector_adultos, #full_wrapper_booking #motor_reserva select.selector_ninos {
  width: 137px !important;
  background: white url(/img/flas2/select_down.png) no-repeat 93% !important;
}

/* line 277, ../sass/_booking_engine_inner.scss */
#ui-datepicker-div {
  z-index: 999999 !important;
  background: white;
}

/* line 282, ../sass/_booking_engine_inner.scss */
#motor_reserva label, #motor_reserva p {
  color: white;
}

/* line 286, ../sass/_booking_engine_inner.scss */
#contenedor_fechas {
  padding-top: 0;
}

/* line 291, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking label#titulo_fecha_entrada, #full_wrapper_booking label#titulo_fecha_salida {
  display: none;
}
/* line 295, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #contenedor_habitaciones > label {
  display: none;
}
/* line 300, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking .adultos.numero_personas > label {
  display: none !important;
}
/* line 305, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #titulo_ninos {
  display: none !important;
}
/* line 309, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking label.selector_bebes {
  display: none;
}
/* line 313, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #booking fieldset {
  margin: 5px 0 0;
  float: left;
}
/* line 318, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking #search-button {
  font-size: 12px;
}
/* line 322, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking ul.ticks_elements {
  text-align: justify;
  width: 640px;
  justify-content: space-between;
  margin-top: 25px;
}
/* line 328, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking ul.ticks_elements:after {
  display: inline-block;
  width: 100%;
  height: 0;
}
/* line 334, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking ul.ticks_elements li {
  display: inline-block;
  text-align: center;
  font-size: 14px;
  color: white;
}
/* line 340, ../sass/_booking_engine_inner.scss */
#full_wrapper_booking ul.ticks_elements li img {
  vertical-align: middle;
}

/* Preload images */
/* line 2, ../../../../sass/plugins/_lightbox.scss */
body:after {
  content: url(/static_1/lib/lightbox/images/close.png) url(/static_1/lib/lightbox/images/loading.gif) url(/static_1/lib/lightbox/images/prev.png) url(/static_1/lib/lightbox/images/next.png);
  display: none;
}

/* line 7, ../../../../sass/plugins/_lightbox.scss */
.lightboxOverlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: black;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  opacity: 0.8;
  display: none;
}

/* line 18, ../../../../sass/plugins/_lightbox.scss */
.lightbox {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 10000;
  text-align: center;
  line-height: 0;
  font-weight: normal;
}

/* line 28, ../../../../sass/plugins/_lightbox.scss */
.lightbox .lb-image {
  display: block;
  height: auto;
  max-width: inherit;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
}

/* line 39, ../../../../sass/plugins/_lightbox.scss */
.lightbox a img {
  border: none;
}

/* line 43, ../../../../sass/plugins/_lightbox.scss */
.lb-outerContainer {
  position: relative;
  background-color: white;
  *zoom: 1;
  width: 250px;
  height: 250px;
  margin: 0 auto;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}

/* line 57, ../../../../sass/plugins/_lightbox.scss */
.lb-outerContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 63, ../../../../sass/plugins/_lightbox.scss */
.lb-container {
  padding: 4px;
}

/* line 67, ../../../../sass/plugins/_lightbox.scss */
.lb-loader {
  position: absolute;
  top: 43%;
  left: 0;
  height: 25%;
  width: 100%;
  text-align: center;
  line-height: 0;
}

/* line 77, ../../../../sass/plugins/_lightbox.scss */
.lb-cancel {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;
  background: url(/static_1/lib/lightbox/images/loading.gif) no-repeat;
}

/* line 85, ../../../../sass/plugins/_lightbox.scss */
.lb-nav {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 10;
}

/* line 94, ../../../../sass/plugins/_lightbox.scss */
.lb-container > .nav {
  left: 0;
}

/* line 98, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a {
  outline: none;
  background-image: url("data:image/gif;base64,R0lGODlhAQABAPAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==");
}

/* line 103, ../../../../sass/plugins/_lightbox.scss */
.lb-prev, .lb-next {
  height: 100%;
  cursor: pointer;
  display: block;
}

/* line 109, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-prev {
  width: 34%;
  left: 0;
  float: left;
  background: url(/static_1/lib/lightbox/images/prev.png) left 48% no-repeat;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}

/* line 122, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-prev:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 127, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-next {
  width: 64%;
  right: 0;
  float: right;
  background: url(/static_1/lib/lightbox/images/next.png) right 48% no-repeat;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  -o-transition: opacity 0.6s;
  transition: opacity 0.6s;
}

/* line 140, ../../../../sass/plugins/_lightbox.scss */
.lb-nav a.lb-next:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 145, ../../../../sass/plugins/_lightbox.scss */
.lb-dataContainer {
  margin: 0 auto;
  padding-top: 5px;
  *zoom: 1;
  width: 100%;
  -moz-border-radius-bottomleft: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-bottom-left-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* line 158, ../../../../sass/plugins/_lightbox.scss */
.lb-dataContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 164, ../../../../sass/plugins/_lightbox.scss */
.lb-data {
  padding: 0 4px;
  color: #ccc;
}

/* line 169, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-details {
  width: 85%;
  float: left;
  text-align: left;
  line-height: 1.1em;
}

/* line 176, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-caption {
  font-size: 13px;
  font-weight: bold;
  line-height: 1em;
}

/* line 182, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-number {
  display: block;
  clear: left;
  padding-bottom: 1em;
  font-size: 12px;
  color: #999999;
}

/* line 190, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-close {
  display: block;
  float: right;
  width: 30px;
  height: 30px;
  background: url(/static_1/lib/lightbox/images/close.png) top right no-repeat;
  text-align: right;
  outline: none;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
  opacity: 0.7;
  -webkit-transition: opacity 0.2s;
  -moz-transition: opacity 0.2s;
  -o-transition: opacity 0.2s;
  transition: opacity 0.2s;
}

/* line 206, ../../../../sass/plugins/_lightbox.scss */
.lb-data .lb-close:hover {
  cursor: pointer;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}

/* line 212, ../../../../sass/plugins/_lightbox.scss */
.lb-number {
  display: none !important;
}

/* line 216, ../../../../sass/plugins/_lightbox.scss */
.fancybox-opened .fancybox-outer {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

/*! fancyBox v2.1.5 fancyapps.com | fancyapps.com/fancybox/#license */
/* line 4, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-wrap,
.fancybox-skin,
.fancybox-outer,
.fancybox-inner,
.fancybox-image,
.fancybox-wrap iframe,
.fancybox-wrap object,
.fancybox-nav,
.fancybox-nav span,
.fancybox-tmp {
  padding: 0;
  margin: 0;
  border: 0;
  outline: none;
  vertical-align: top;
}

/* line 22, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 8020;
}

/* line 29, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-skin {
  position: relative;
  background: #f9f9f9;
  color: #444;
  text-shadow: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

/* line 39, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened {
  z-index: 8030;
}

/* line 43, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened .fancybox-skin {
  -webkit-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* line 49, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-outer, .fancybox-inner {
  position: relative;
}

/* line 53, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-inner {
  overflow: hidden;
}

/* line 57, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-type-iframe .fancybox-inner {
  -webkit-overflow-scrolling: touch;
}

/* line 61, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-error {
  color: #444;
  font: 14px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  margin: 0;
  padding: 15px;
  white-space: nowrap;
}

/* line 69, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-image, .fancybox-iframe {
  display: block;
  width: 100%;
  height: 100%;
}

/* line 75, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-image {
  max-width: 100%;
  max-height: 100%;
}

/* line 80, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading, .fancybox-close, .fancybox-prev span, .fancybox-next span {
  background-image: url("/static_1/lib/fancybox/fancybox_sprite.png");
}

/* line 84, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -22px;
  margin-left: -22px;
  background-position: 0 -108px;
  opacity: 0.8;
  cursor: pointer;
  z-index: 8060;
}

/* line 96, ../../../../sass/plugins/_fancybox_2_1_5.scss */
#fancybox-loading div {
  width: 44px;
  height: 44px;
  background: url("/static_1/lib/fancybox/fancybox_loading.gif") center center no-repeat;
}

/* line 102, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-close {
  position: absolute;
  top: -18px;
  right: -18px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  z-index: 8040;
}

/* line 112, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav {
  position: absolute;
  top: 0;
  width: 40%;
  height: 100%;
  cursor: pointer;
  text-decoration: none;
  background: transparent url("../../static_1/lib/fancybox/blank.gif");
  /* helps IE */
  -webkit-tap-highlight-color: transparent;
  z-index: 8040;
}

/* line 124, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-prev {
  left: 0;
}

/* line 128, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-next {
  right: 0;
}

/* line 132, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav span {
  position: absolute;
  top: 50%;
  width: 36px;
  height: 34px;
  margin-top: -18px;
  cursor: pointer;
  z-index: 8040;
  visibility: hidden;
}

/* line 143, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-prev span {
  left: 10px;
  background-position: 0 -36px;
}

/* line 148, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-next span {
  right: 10px;
  background-position: 0 -72px;
}

/* line 153, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-nav:hover span {
  visibility: visible;
}

/* line 157, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-tmp {
  position: absolute;
  top: -99999px;
  left: -99999px;
  visibility: hidden;
  max-width: 99999px;
  max-height: 99999px;
  overflow: visible !important;
}

/* Overlay helper */
/* line 169, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock {
  overflow: hidden !important;
  width: auto;
}

/* line 174, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock body {
  overflow: hidden !important;
}

/* line 178, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock-test {
  overflow-y: hidden !important;
}

/* line 182, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-overlay {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  display: none;
  z-index: 1001;
  background: url("/static_1/lib/fancybox/fancybox_overlay.png");
}

/* line 193, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-overlay-fixed {
  position: fixed;
  bottom: 0;
  right: 0;
}

/* line 199, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-lock .fancybox-overlay {
  overflow: auto;
  overflow-y: scroll;
}

/* Title helper */
/* line 206, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title {
  visibility: hidden;
  font: normal 13px/20px "Helvetica Neue",Helvetica,Arial,sans-serif;
  position: relative;
  text-shadow: none;
  z-index: 8050;
}

/* line 214, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-opened .fancybox-title {
  visibility: visible;
}

/* line 218, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-float-wrap {
  position: absolute;
  bottom: 0;
  right: 50%;
  margin-bottom: -35px;
  z-index: 8050;
  text-align: center;
}

/* line 227, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-float-wrap .child {
  display: inline-block;
  margin-right: -100%;
  padding: 2px 20px;
  background: transparent;
  /* Fallback for web browsers that doesn't support RGBa */
  background: rgba(0, 0, 0, 0.8);
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
  text-shadow: 0 1px 2px #222;
  color: #FFF;
  font-weight: bold;
  line-height: 24px;
  white-space: nowrap;
}

/* line 243, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-outside-wrap {
  position: relative;
  margin-top: 10px;
  color: #fff;
}

/* line 249, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-inside-wrap {
  padding-top: 10px;
}

/* line 253, ../../../../sass/plugins/_fancybox_2_1_5.scss */
.fancybox-title-over-wrap {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #fff;
  padding: 10px;
  background: #000;
  background: rgba(0, 0, 0, 0.8);
}

/*Retina graphics!*/
@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5) {
  /* line 268, ../../../../sass/plugins/_fancybox_2_1_5.scss */
  #fancybox-loading, .fancybox-close, .fancybox-prev span, .fancybox-next span {
    background-image: url("/static_1/lib/fancybox/<EMAIL>");
    background-size: 44px 152px;
    /*The size of the normal image, half the size of the hi-res image*/
  }

  /* line 273, ../../../../sass/plugins/_fancybox_2_1_5.scss */
  #fancybox-loading div {
    background-image: url("/static_1/lib/fancybox/<EMAIL>");
    background-size: 24px 24px;
    /*The size of the normal image, half the size of the hi-res image*/
  }
}
/* line 3, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden {
  display: none;
}

/* line 7, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix, .ui-icon {
  display: block;
}

/* line 11, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-hidden-accessible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
}

/* line 17, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}

/* line 28, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* line 36, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
* html .ui-helper-clearfix {
  height: 1%;
}

/* line 40, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix, .ui-widget-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* line 48, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-helper-zfix {
  opacity: 0;
  filter: Alpha(Opacity=0);
}

/* line 53, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled {
  cursor: default !important;
}

/* line 57, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
}

/* line 63, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1.1em;
}

/* line 68, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget .ui-widget {
  font-size: 1em;
}

/* line 72, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget button, .ui-widget input, .ui-widget select, .ui-widget textarea {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 1em;
}

/* line 76, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content {
  border: 1px solid #a6c9e2;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x #fcfdfd;
  color: #222;
}

/* line 82, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-content a {
  color: #222;
}

/* line 86, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header {
  border: 1px solid #4297d7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x #5c9ccc;
  color: #fff;
  font-weight: 700;
}

/* line 93, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header a {
  color: #fff;
}

/* line 97, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid #c5dbec;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x #dfeffc;
  font-weight: 700;
  color: #2e6e9e;
}

/* line 104, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default a, .ui-state-default a:link, .ui-state-default a:visited {
  color: #2e6e9e;
  text-decoration: none;
}

/* line 109, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus, .ui-state-hover, .ui-widget-content .ui-state-focus, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-focus, .ui-widget-header .ui-state-hover {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x #d0e5f5;
  font-weight: 700;
  color: #1d5987;
}

/* line 116, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-hover a, .ui-state-hover a:hover {
  color: #1d5987;
  text-decoration: none;
}

/* line 121, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active {
  border: 1px solid #79b7e7;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x #f5f8f9;
  font-weight: 700;
  color: #e17009;
}

/* line 128, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
  color: #e17009;
  text-decoration: none;
}

/* line 133, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget :active {
  outline: 0;
}

/* line 137, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
  border: 1px solid #fad42e;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_55_fbec88_40x100.png) 50% 50% repeat-x #fbec88;
  color: #363636;
}

/* line 143, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight a, .ui-widget-content .ui-state-highlight a, .ui-widget-header .ui-state-highlight a {
  color: #363636;
}

/* line 147, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
  border: 1px solid #cd0a0a;
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x #fef1ec;
  color: #cd0a0a;
}

/* line 153, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error a, .ui-state-error-text, .ui-widget-content .ui-state-error a, .ui-widget-content .ui-state-error-text, .ui-widget-header .ui-state-error a, .ui-widget-header .ui-state-error-text {
  color: #cd0a0a;
}

/* line 157, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-primary, .ui-widget-content .ui-priority-primary, .ui-widget-header .ui-priority-primary {
  font-weight: 700;
}

/* line 161, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-priority-secondary, .ui-widget-content .ui-priority-secondary, .ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  font-weight: 400;
}

/* line 167, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  background-image: none;
}
/* line 172, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-disabled.ui-datepicker-other-month, .ui-widget-content .ui-state-disabled.ui-datepicker-other-month, .ui-widget-header .ui-state-disabled.ui-datepicker-other-month {
  opacity: 0;
}

/* line 177, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon, .ui-widget-content .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_469bdd_256x240.png);
}

/* line 181, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon {
  width: 16px;
  height: 16px;
}

/* line 186, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-header .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_d8e7f3_256x240.png);
}

/* line 190, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-default .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_6da8d5_256x240.png);
}

/* line 194, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-focus .ui-icon, .ui-state-hover .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_217bc0_256x240.png);
}

/* line 198, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-active .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_f9bd01_256x240.png);
}

/* line 202, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-highlight .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_2e83ff_256x240.png);
}

/* line 206, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-state-error .ui-icon, .ui-state-error-text .ui-icon {
  background-image: url(/static_1/css/datepicker.redmond/images/ui-icons_cd0a0a_256x240.png);
}

/* line 210, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-n {
  background-position: 0 0;
}

/* line 214, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-ne {
  background-position: -16px 0;
}

/* line 218, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-e {
  background-position: -32px 0;
}

/* line 222, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-se {
  background-position: -48px 0;
}

/* line 226, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-s {
  background-position: -64px 0;
}

/* line 230, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-sw {
  background-position: -80px 0;
}

/* line 234, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-w {
  background-position: -96px 0;
}

/* line 238, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-1-nw {
  background-position: -112px 0;
}

/* line 242, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-n-s {
  background-position: -128px 0;
}

/* line 246, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-carat-2-e-w {
  background-position: -144px 0;
}

/* line 250, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}

/* line 254, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}

/* line 258, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}

/* line 262, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}

/* line 266, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-s {
  background-position: -64px -16px;
}

/* line 270, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}

/* line 274, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}

/* line 278, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}

/* line 282, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}

/* line 286, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}

/* line 290, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}

/* line 294, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}

/* line 298, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}

/* line 302, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}

/* line 306, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-s {
  background-position: -64px -32px;
}

/* line 310, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}

/* line 314, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}

/* line 318, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}

/* line 322, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}

/* line 326, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}

/* line 330, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}

/* line 334, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}

/* line 338, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}

/* line 342, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}

/* line 346, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}

/* line 350, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}

/* line 354, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-n {
  background-position: 0 -48px;
}

/* line 358, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}

/* line 362, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}

/* line 366, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}

/* line 370, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}

/* line 374, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}

/* line 378, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}

/* line 382, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}

/* line 386, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}

/* line 390, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}

/* line 394, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}

/* line 398, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}

/* line 402, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}

/* line 406, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}

/* line 410, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}

/* line 414, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}

/* line 418, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}

/* line 422, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}

/* line 426, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}

/* line 430, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}

/* line 434, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}

/* line 438, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}

/* line 442, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}

/* line 446, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}

/* line 450, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}

/* line 454, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}

/* line 458, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}

/* line 462, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}

/* line 466, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4 {
  background-position: 0 -80px;
}

/* line 470, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}

/* line 474, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-extlink {
  background-position: -32px -80px;
}

/* line 478, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-newwin {
  background-position: -48px -80px;
}

/* line 482, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-refresh {
  background-position: -64px -80px;
}

/* line 486, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-shuffle {
  background-position: -80px -80px;
}

/* line 490, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}

/* line 494, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}

/* line 498, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}

/* line 502, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-folder-open {
  background-position: -16px -96px;
}

/* line 506, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document {
  background-position: -32px -96px;
}

/* line 510, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-document-b {
  background-position: -48px -96px;
}

/* line 514, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-note {
  background-position: -64px -96px;
}

/* line 518, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-closed {
  background-position: -80px -96px;
}

/* line 522, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-mail-open {
  background-position: -96px -96px;
}

/* line 526, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-suitcase {
  background-position: -112px -96px;
}

/* line 530, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-comment {
  background-position: -128px -96px;
}

/* line 534, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-person {
  background-position: -144px -96px;
}

/* line 538, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-print {
  background-position: -160px -96px;
}

/* line 542, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-trash {
  background-position: -176px -96px;
}

/* line 546, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-locked {
  background-position: -192px -96px;
}

/* line 550, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-unlocked {
  background-position: -208px -96px;
}

/* line 554, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bookmark {
  background-position: -224px -96px;
}

/* line 558, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-tag {
  background-position: -240px -96px;
}

/* line 562, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-home {
  background-position: 0 -112px;
}

/* line 566, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-flag {
  background-position: -16px -112px;
}

/* line 570, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calendar {
  background-position: -32px -112px;
}

/* line 574, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cart {
  background-position: -48px -112px;
}

/* line 578, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pencil {
  background-position: -64px -112px;
}

/* line 582, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clock {
  background-position: -80px -112px;
}

/* line 586, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-disk {
  background-position: -96px -112px;
}

/* line 590, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-calculator {
  background-position: -112px -112px;
}

/* line 594, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomin {
  background-position: -128px -112px;
}

/* line 598, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-zoomout {
  background-position: -144px -112px;
}

/* line 602, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-search {
  background-position: -160px -112px;
}

/* line 606, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-wrench {
  background-position: -176px -112px;
}

/* line 610, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gear {
  background-position: -192px -112px;
}

/* line 614, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-heart {
  background-position: -208px -112px;
}

/* line 618, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-star {
  background-position: -224px -112px;
}

/* line 622, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-link {
  background-position: -240px -112px;
}

/* line 626, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-cancel {
  background-position: 0 -128px;
}

/* line 630, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plus {
  background-position: -16px -128px;
}

/* line 634, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-plusthick {
  background-position: -32px -128px;
}

/* line 638, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minus {
  background-position: -48px -128px;
}

/* line 642, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-minusthick {
  background-position: -64px -128px;
}

/* line 646, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-close {
  background-position: -80px -128px;
}

/* line 650, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-closethick {
  background-position: -96px -128px;
}

/* line 654, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-key {
  background-position: -112px -128px;
}

/* line 658, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-lightbulb {
  background-position: -128px -128px;
}

/* line 662, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-scissors {
  background-position: -144px -128px;
}

/* line 666, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-clipboard {
  background-position: -160px -128px;
}

/* line 670, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-copy {
  background-position: -176px -128px;
}

/* line 674, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-contact {
  background-position: -192px -128px;
}

/* line 678, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-image {
  background-position: -208px -128px;
}

/* line 682, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-video {
  background-position: -224px -128px;
}

/* line 686, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-script {
  background-position: -240px -128px;
}

/* line 690, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-alert {
  background-position: 0 -144px;
}

/* line 694, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-info {
  background-position: -16px -144px;
}

/* line 698, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-notice {
  background-position: -32px -144px;
}

/* line 702, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-help {
  background-position: -48px -144px;
}

/* line 706, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-check {
  background-position: -64px -144px;
}

/* line 710, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-bullet {
  background-position: -80px -144px;
}

/* line 714, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-off {
  background-position: -96px -144px;
}

/* line 718, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-radio-on {
  background-position: -112px -144px;
}

/* line 722, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-w {
  background-position: -128px -144px;
}

/* line 726, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pin-s {
  background-position: -144px -144px;
}

/* line 730, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-play {
  background-position: 0 -160px;
}

/* line 734, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-pause {
  background-position: -16px -160px;
}

/* line 738, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-next {
  background-position: -32px -160px;
}

/* line 742, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-prev {
  background-position: -48px -160px;
}

/* line 746, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-end {
  background-position: -64px -160px;
}

/* line 750, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-seek-first, .ui-icon-seek-start {
  background-position: -80px -160px;
}

/* line 754, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-stop {
  background-position: -96px -160px;
}

/* line 758, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-eject {
  background-position: -112px -160px;
}

/* line 762, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-off {
  background-position: -128px -160px;
}

/* line 766, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-volume-on {
  background-position: -144px -160px;
}

/* line 770, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-power {
  background-position: 0 -176px;
}

/* line 774, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal-diag {
  background-position: -16px -176px;
}

/* line 778, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-signal {
  background-position: -32px -176px;
}

/* line 782, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-0 {
  background-position: -48px -176px;
}

/* line 786, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-1 {
  background-position: -64px -176px;
}

/* line 790, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-2 {
  background-position: -80px -176px;
}

/* line 794, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-battery-3 {
  background-position: -96px -176px;
}

/* line 798, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-plus {
  background-position: 0 -192px;
}

/* line 802, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-minus {
  background-position: -16px -192px;
}

/* line 806, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-close {
  background-position: -32px -192px;
}

/* line 810, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-e {
  background-position: -48px -192px;
}

/* line 814, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}

/* line 818, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-w {
  background-position: -80px -192px;
}

/* line 822, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}

/* line 826, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}

/* line 830, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}

/* line 834, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}

/* line 838, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}

/* line 842, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}

/* line 846, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}

/* line 850, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circle-check {
  background-position: -208px -192px;
}

/* line 854, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}

/* line 858, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}

/* line 862, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}

/* line 866, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}

/* line 870, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}

/* line 874, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}

/* line 878, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}

/* line 882, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}

/* line 886, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}

/* line 890, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}

/* line 894, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}

/* line 898, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}

/* line 902, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-overlay, .ui-widget-shadow {
  background: url(/static_1/css/datepicker.redmond/images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x #aaa;
  opacity: .3;
  filter: Alpha(Opacity=30);
}

/* line 908, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-left, .ui-corner-tl, .ui-corner-top {
  -moz-border-radius-topleft: 5px;
  -webkit-border-top-left-radius: 5px;
  -khtml-border-top-left-radius: 5px;
  border-top-left-radius: 5px;
}

/* line 915, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-right, .ui-corner-top, .ui-corner-tr {
  -moz-border-radius-topright: 5px;
  -webkit-border-top-right-radius: 5px;
  -khtml-border-top-right-radius: 5px;
  border-top-right-radius: 5px;
}

/* line 922, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bl, .ui-corner-bottom, .ui-corner-left {
  -moz-border-radius-bottomleft: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -khtml-border-bottom-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

/* line 929, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-corner-all, .ui-corner-bottom, .ui-corner-br, .ui-corner-right {
  -moz-border-radius-bottomright: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -khtml-border-bottom-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* line 936, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-widget-shadow {
  margin: -8px 0 0 -8px;
  padding: 8px;
  -moz-border-radius: 8px;
  -khtml-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* line 945, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none;
}

/* line 951, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}

/* line 956, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next, .ui-datepicker .ui-datepicker-prev {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}

/* line 963, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover, .ui-datepicker .ui-datepicker-prev-hover {
  top: 1px;
}

/* line 967, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}

/* line 971, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}

/* line 975, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}

/* line 979, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}

/* line 983, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-next span, .ui-datepicker .ui-datepicker-prev span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}

/* line 992, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}

/* line 998, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}

/* line 1003, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month-year {
  width: 100%;
}

/* line 1007, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
  width: 49%;
}

/* line 1011, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}

/* line 1018, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: 700;
  border: 0;
}

/* line 1025, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td {
  border: 0;
  padding: 1px;
}

/* line 1030, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker td a, .ui-datepicker td span {
  display: block;
  padding: .2em;
  text-align: right;
  text-decoration: none;
}

/* line 1037, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

/* line 1046, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em;
  width: auto;
  overflow: visible;
}

/* line 1055, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-multi .ui-datepicker-group, .ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}

/* line 1059, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}

/* line 1063, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}

/* line 1068, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}

/* line 1072, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}

/* line 1076, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}

/* line 1080, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}

/* line 1084, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}

/* line 1088, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}

/* line 1094, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl {
  direction: rtl;
}

/* line 1098, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}

/* line 1103, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}

/* line 1108, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}

/* line 1113, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}

/* line 1118, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}

/* line 1122, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}

/* line 1126, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}

/* line 1131, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
.ui-datepicker-cover {
  display: none;
  display: block;
  position: absolute;
  z-index: -1;
  filter: mask();
  top: -4px;
  left: -4px;
  width: 200px;
  height: 200px;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
  /* line 1144, ../../../../sass/plugins/_jquery_ui_1_8_16_custom.scss */
  div.ui-datepicker {
    font-size: 20px;
  }
}
/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 3, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 7, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 11, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #AF0929 !important;
}

/* line 15, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #AF0929 !important;
  color: white;
}

/* line 20, ../sass/_template_specific.scss */
a {
  text-decoration: none;
}

/* line 24, ../sass/_template_specific.scss */
section#content {
  background: white;
}

/* line 28, ../sass/_template_specific.scss */
.contact_call_center_wrapper {
  display: inline-block;
}

/* line 32, ../sass/_template_specific.scss */
#popup-click-to-call {
  font-family: 'Oswald', sans-serif;
}

/* line 37, ../sass/_template_specific.scss */
#popup-click-to-call #numero, #popup-click-to-call #bllamar {
  margin-top: 20px !important;
}

/* line 43, ../sass/_template_specific.scss */
header {
  position: relative;
}

/* line 47, ../sass/_template_specific.scss */
header {
  background: rgba(237, 238, 240, 0.8);
  height: 130px;
  padding-top: 15px;
  position: fixed;
  width: 100%;
  top: 0px;
  z-index: 1000;
  min-width: 1140px;
}
/* line 57, ../sass/_template_specific.scss */
header .header_content {
  width: auto;
  width: 785px;
  float: right;
  font-weight: bolder;
  font-family: "Source Sans Pro";
  font-size: 18px;
}
/* line 65, ../sass/_template_specific.scss */
header .header_content .top_header, header .header_content .bottom_header {
  float: right;
}
/* line 69, ../sass/_template_specific.scss */
header .header_content a {
  color: #787878;
}
/* line 72, ../sass/_template_specific.scss */
header .header_content .bottom_header {
  border-top: 1px solid #59595b;
  margin-top: 10px;
  padding-top: 8px;
}
/* line 79, ../sass/_template_specific.scss */
header #top-sections, header #social, header .adults_only, header .telephone, header #lang, header .opinion-link {
  display: inline-block;
}
/* line 83, ../sass/_template_specific.scss */
header .opinion-link {
  display: inline-block;
}
/* line 87, ../sass/_template_specific.scss */
header #top-sections {
  vertical-align: top;
  margin-top: 4px;
}
/* line 91, ../sass/_template_specific.scss */
header #top-sections a {
  padding-left: 20px;
}
/* line 95, ../sass/_template_specific.scss */
header #top-sections a:hover {
  opacity: 0.7;
}
/* line 99, ../sass/_template_specific.scss */
header #top-sections img {
  display: inline-block;
  margin-top: 2px;
  padding: 0 5px;
  vertical-align: bottom;
}
/* line 107, ../sass/_template_specific.scss */
header .wifi {
  float: left;
  padding-left: 42px;
  background: url("/img/flas2/ico-flashotel/wifi-gratis.png") no-repeat;
  line-height: 33px;
  width: 199px;
  color: #595959;
}
/* line 116, ../sass/_template_specific.scss */
header .opinion-link img {
  vertical-align: bottom;
  display: inline-block;
  margin-top: 6px;
  margin-right: 4px;
}
/* line 123, ../sass/_template_specific.scss */
header #main_menu {
  background-color: #787878;
}
/* line 126, ../sass/_template_specific.scss */
header #main_menu a {
  margin: 12px 0;
  font-size: 16px;
  color: white;
  display: inline-block;
  text-transform: uppercase;
  font-family: Oswald;
}
/* line 135, ../sass/_template_specific.scss */
header #main_menu a:hover {
  color: #b9ce1b;
}
/* line 140, ../sass/_template_specific.scss */
header .button-promotion {
  background-color: #bdcd37;
  padding: 5px 15px;
  margin-top: 6px !important;
}
/* line 146, ../sass/_template_specific.scss */
header #mainMenuDiv {
  background-color: transparent;
  height: 45px;
}
/* line 151, ../sass/_template_specific.scss */
header #main-sections-inner {
  text-align: justify;
  display: flex;
  justify-content: space-between;
}
/* line 157, ../sass/_template_specific.scss */
header #main-sections-inner > div {
  display: inline-block;
}
/* line 161, ../sass/_template_specific.scss */
header #section-active a {
  color: #b9ce1b;
}
/* line 164, ../sass/_template_specific.scss */
header .main-section-div-wrapper {
  position: relative;
}
/* line 167, ../sass/_template_specific.scss */
header .main-section-div-wrapper ul {
  position: absolute;
  box-sizing: border-box;
  width: 160px;
  padding: 10px;
  background: white;
  color: #AF0929;
  display: none;
}
/* line 176, ../sass/_template_specific.scss */
header .main-section-div-wrapper ul a {
  color: #AF0929 !important;
}
/* line 179, ../sass/_template_specific.scss */
header .main-section-div-wrapper ul a:hover {
  color: #f42a51 !important;
}
/* line 185, ../sass/_template_specific.scss */
header #lang a {
  background-color: #787878;
  color: white;
  border-radius: 20px;
  display: inline-block;
  text-align: center;
  width: 30px;
  padding: 5px 0;
  font-family: "Source Sans Pro";
  font-size: 16px;
}
/* line 197, ../sass/_template_specific.scss */
header #lang a:hover {
  background-color: #AF0929;
}
/* line 201, ../sass/_template_specific.scss */
header #lang .selected {
  background-color: #AF0929;
}
/* line 205, ../sass/_template_specific.scss */
header #top_sections {
  bottom: 10px;
}
/* line 208, ../sass/_template_specific.scss */
header #top_sections a {
  padding-left: 20px;
}
/* line 213, ../sass/_template_specific.scss */
header #social {
  padding-left: 20px;
}
/* line 217, ../sass/_template_specific.scss */
header .adults_only {
  vertical-align: top;
}
/* line 221, ../sass/_template_specific.scss */
header .telephone {
  margin-top: 2px;
  padding-left: 15px;
  vertical-align: top;
  display: inline-block;
}
/* line 227, ../sass/_template_specific.scss */
header .telephone img {
  vertical-align: top;
  display: inline-block;
  margin-top: 7px;
}
/* line 233, ../sass/_template_specific.scss */
header .telephone span {
  vertical-align: top;
  margin-top: 6px;
  display: inline-block;
  padding-left: 10px;
  color: #787878;
}
/* line 242, ../sass/_template_specific.scss */
header #lang {
  margin-top: 3px;
  padding-left: 15px;
  vertical-align: top;
  display: inline-block;
}

/* line 251, ../sass/_template_specific.scss */
.gallery_1 li .crop {
  position: relative;
  height: 212px;
}
/* line 255, ../sass/_template_specific.scss */
.gallery_1 li .crop a img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  width: auto !important;
  height: auto !important;
}

/* line 270, ../sass/_template_specific.scss */
.ticks_wrapper {
  text-align: center;
  background-color: #ededef;
  font-family: "Source Sans Pro", sans-serif;
}
/* line 275, ../sass/_template_specific.scss */
.ticks_wrapper .ticks {
  display: inline-block;
  padding: 5px 20px;
}
/* line 279, ../sass/_template_specific.scss */
.ticks_wrapper .ticks img {
  display: inline-block;
  vertical-align: top;
}

/* ===== ==== === == = Banner Ticks = == === ==== ===== */
/* line 287, ../sass/_template_specific.scss */
.banner_ticks {
  background-color: black;
}
/* line 289, ../sass/_template_specific.scss */
.banner_ticks .banner_ticks_wrapper {
  text-align: center;
  font-size: 13px;
  color: white;
  font-family: 'Oswald', sans-serif;
}
/* line 294, ../sass/_template_specific.scss */
.banner_ticks .banner_ticks_wrapper .ticks {
  display: inline-block;
  text-align: center;
  text-transform: uppercase;
  line-height: 18px;
  margin: 0px 40px;
}
/* line 300, ../sass/_template_specific.scss */
.banner_ticks .banner_ticks_wrapper .ticks span {
  font-weight: 200;
}
/* line 303, ../sass/_template_specific.scss */
.banner_ticks .banner_ticks_wrapper .ticks img {
  display: block;
  margin: 10px auto;
}
/* line 308, ../sass/_template_specific.scss */
.banner_ticks .banner_ticks_wrapper a {
  display: block;
  width: 120px;
  margin: 20px auto;
  text-transform: uppercase;
  background-color: #b9ce1b;
  padding: 10px 45px;
  color: white;
}

/* line 321, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer {
  background-color: #2A2A2A !important;
  padding: 0px !important;
  overflow: auto;
  overflow-x: hidden;
  color: white;
}
/* line 327, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer .popup_ventajas {
  width: 100%;
  font-family: "Source Sans Pro", sans-serif;
}
/* line 331, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer h3 {
  border-bottom: 1px solid lightgrey;
  font-size: 18px;
  width: 100%;
  display: block;
  padding: 15px;
  font-family: 'Oswald', sans-serif;
  box-sizing: border-box;
}
/* line 340, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer img {
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
}
/* line 345, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer .banner_title {
  font-size: 18px;
}
/* line 348, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer .banner_desc {
  font-weight: 200;
  display: block;
  padding-left: 30px;
}
/* line 353, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer .banner_list, .fancybox-ventajas .fancybox-outer p {
  padding: 15px;
  font-size: 12px;
  font-weight: 200;
}
/* line 358, ../sass/_template_specific.scss */
.fancybox-ventajas .fancybox-outer p {
  padding-left: 45px;
}

/* ===== ==== === == = Paquetes = == === ==== ===== */
/* line 365, ../sass/_template_specific.scss */
.paquetes_wrapper {
  margin-bottom: 30px;
  text-align: center;
}
/* line 368, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete {
  width: calc(99%/3);
  box-sizing: border-box;
  display: inline-block;
}
/* line 372, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_image {
  position: relative;
  width: 100%;
  height: 300px;
  font-family: "Oswald", sans-serif;
  font-size: 20px;
  color: white;
  text-transform: uppercase;
  overflow: hidden;
}
/* line 382, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_image .paquete_background, .paquetes_wrapper .paquete .paquete_image .background_over {
  min-height: 100%;
  min-width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 387, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_image .background_over {
  background-color: rgba(0, 0, 0, 0.3);
}
/* line 390, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_image .middle_block {
  text-align: center;
  width: 100%;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}
/* line 395, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_image .paquete_logo {
  height: 50px;
  margin-top: 10px;
  display: inline-block;
}
/* line 401, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_links {
  display: flex;
}
/* line 403, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_links .plus, .paquetes_wrapper .paquete .paquete_links .button-promotion {
  display: block;
  color: white;
  font-family: "Oswald", sans-serif;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 413, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_links .plus {
  font-size: 50px;
  width: 44px;
  line-height: 20px;
  background-color: #b9ce1b;
}
/* line 419, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_links .button-promotion {
  width: 100%;
  padding: 10px;
  background-color: #AF0929;
}
/* line 424, ../sass/_template_specific.scss */
.paquetes_wrapper .paquete .paquete_links .plus:hover, .paquetes_wrapper .paquete .paquete_links .button-promotion:hover {
  background-color: #787878;
}

/* line 433, ../sass/_template_specific.scss */
#form-opinion label {
  display: block;
  text-align: center;
  font-family: "Source Sans Pro", sans-serif;
  text-transform: uppercase;
  margin-bottom: 10px;
}
/* line 441, ../sass/_template_specific.scss */
#form-opinion input, #form-opinion textarea {
  background: white;
  border: 1px solid #AF0929;
  width: 400px;
  margin-bottom: 25px;
  padding: 10px;
  background: #f7f7f7;
}
/* line 449, ../sass/_template_specific.scss */
#form-opinion textarea {
  height: 150px;
}
/* line 452, ../sass/_template_specific.scss */
#form-opinion .send_comments {
  text-align: center;
  background: #AF0929;
  padding: 8px;
  color: white;
  text-transform: uppercase;
  font-family: "Source Sans Pro", sans-serif;
  cursor: pointer;
}

/* line 464, ../sass/_template_specific.scss */
footer {
  background: #787878;
  padding: 38px 0;
}
/* line 468, ../sass/_template_specific.scss */
footer .footer_column_title {
  color: #b9ce1b;
  border-bottom: 1px solid white;
  margin-bottom: 10px;
  padding-bottom: 10px;
  font-size: 14px;
  font-family: Oswald;
}
/* line 477, ../sass/_template_specific.scss */
footer .footer_column_description {
  color: white;
  font-family: "Source Sans Pro";
}
/* line 481, ../sass/_template_specific.scss */
footer .footer_column_description a, footer .footer_column_description .localizacion {
  display: block;
  color: white;
  font-family: "Source Sans Pro";
  font-size: 15px;
  line-height: 18px;
}
/* line 490, ../sass/_template_specific.scss */
footer .full-copyright {
  text-align: center;
  padding-top: 30px;
  font-family: "Source Sans Pro";
}
/* line 496, ../sass/_template_specific.scss */
footer .full-copyright * {
  color: white;
}
/* line 501, ../sass/_template_specific.scss */
footer #newsletter #title_newsletter, footer #newsletter #suscEmailLabel {
  display: none !important;
}
/* line 506, ../sass/_template_specific.scss */
footer .footer_column_description #newsletter input#suscEmail {
  width: 200px;
  height: 30px;
  border: 0;
  text-align: center;
}
/* line 513, ../sass/_template_specific.scss */
footer .footer_column_description #newsletter button#newsletter-button {
  width: 202px;
  height: 30px;
  border: 0;
  margin-top: 4px;
  text-transform: uppercase;
  font-family: 'Source Sans Pro';
  color: white;
  font-size: 14px;
  font-weight: 100;
  background: #AF0929;
  cursor: pointer;
}
/* line 527, ../sass/_template_specific.scss */
footer .last {
  margin-top: 20px;
}

/* line 533, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  text-align: center;
  width: 700px;
  margin: 40px auto;
}
/* line 538, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_title {
  color: #AF0929;
  font-size: 28px;
  text-transform: uppercase;
  font-family: Oswald;
  font-weight: 400;
  margin-bottom: 25px;
}
/* line 547, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description {
  color: #787878;
  font-family: "Source Sans Pro";
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
}

/* line 557, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper {
  text-align: center;
  padding-top: 30px;
}
/* line 561, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
  transition: all 1s;
}
/* line 571, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .overlay:hover {
  opacity: 0;
}
/* line 575, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .subtitle_title {
  color: #787878;
  padding-bottom: 30px;
  font-size: 28px;
  position: relative;
  font-family: Oswald;
  font-weight: lighter;
}
/* line 584, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .subtitle_title:before {
  border-top: 1px solid #787878;
  content: '';
  width: 1140px;
  display: block;
  position: absolute;
  left: 0;
  top: 20px;
  margin: auto;
  z-index: -2;
}
/* line 596, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .subtitle_title:after {
  content: '';
  position: absolute;
  top: 0;
  right: -10px;
  left: -10px;
  bottom: 0;
  background: white;
  z-index: -1;
  width: 200px;
  margin: auto;
}
/* line 609, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left {
  display: inline-block;
  width: 560px;
  float: left;
  position: relative;
}
/* line 616, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left img {
  width: 100%;
  display: inline-block;
  float: left;
}
/* line 622, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left span {
  position: absolute;
  top: 0;
  color: white;
}
/* line 628, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left .buttons {
  cursor: pointer;
}
/* line 631, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left .buttons .arrow-left {
  width: auto;
  position: absolute;
  bottom: 107px;
  left: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
}
/* line 641, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left .buttons .arrow-right {
  width: auto;
  position: absolute;
  bottom: 107px;
  right: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
  transform: rotate(180deg);
}
/* line 653, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_left .flex-direction-nav {
  display: none;
}
/* line 658, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right {
  display: inline-block;
  width: 560px;
  float: right;
  position: relative;
}
/* line 665, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right .banner_background {
  width: 100%;
  display: inline-block;
  float: left;
}
/* line 671, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right span {
  position: absolute;
  top: 0;
  color: white;
  font-weight: lighter;
}
/* line 678, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right .buttons {
  cursor: pointer;
}
/* line 680, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right .buttons .arrow-left {
  width: auto;
  position: absolute;
  bottom: 107px;
  left: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
}
/* line 690, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right .buttons .arrow-right {
  width: auto;
  position: absolute;
  bottom: 107px;
  right: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
  transform: rotate(180deg);
}
/* line 702, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_top_right .flex-direction-nav {
  display: none;
}
/* line 707, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_bottom {
  display: inline-block;
  width: 100%;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
  height: 208px !important;
}
/* line 716, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_bottom .flex-direction-nav {
  display: none;
}
/* line 720, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_bottom .arrow-left {
  cursor: pointer;
  width: auto;
  position: absolute;
  bottom: 80.5px;
  left: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
}
/* line 731, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_bottom .arrow-right {
  cursor: pointer;
  width: auto;
  position: absolute;
  bottom: 80.5px;
  right: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
  transform: rotate(180deg);
}
/* line 744, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_element {
  height: 250px;
}
/* line 748, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_element li {
  height: 250px;
  overflow: hidden;
  position: relative;
}
/* line 754, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_element .cartel {
  position: absolute;
  top: 105px;
  left: 52.5px;
}
/* line 760, ../sass/_template_specific.scss */
.bannerx3_destacados_wrapper .bannerx3_element span {
  position: absolute;
  top: 105px;
  right: 0;
  left: 0;
  font-size: 28px;
  font-family: Oswald;
}

/* line 770, ../sass/_template_specific.scss */
.smart_gallery {
  display: inline-block;
  width: 100%;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
  height: 208px !important;
}
/* line 778, ../sass/_template_specific.scss */
.smart_gallery .slides li {
  position: relative;
  width: 380px;
  height: 208px;
  overflow: hidden;
}
/* line 783, ../sass/_template_specific.scss */
.smart_gallery .slides li img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 791, ../sass/_template_specific.scss */
.smart_gallery .flex-direction-nav {
  display: block;
}
/* line 795, ../sass/_template_specific.scss */
.smart_gallery .arrow-left, .smart_gallery .arrow-right {
  cursor: pointer;
  width: auto;
  position: absolute;
  bottom: 80.5px;
  left: 20px;
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
  z-index: 2;
}
/* line 807, ../sass/_template_specific.scss */
.smart_gallery .arrow-right {
  right: 20px;
  left: auto;
  transform: rotate(180deg);
}

/* line 815, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper {
  text-align: center;
  padding-top: 30px;
}
/* line 819, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .subtitle_title {
  color: #787878;
  padding-bottom: 30px;
  font-size: 28px;
  position: relative;
  font-family: Oswald;
  font-weight: lighter;
}
/* line 828, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .subtitle_title:before {
  border-top: 1px solid #787878;
  content: '';
  width: 1140px;
  display: inline-block;
  position: absolute;
  left: 0;
  top: 20px;
  margin: auto;
  z-index: -2;
}
/* line 840, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .subtitle_title:after {
  content: '';
  position: absolute;
  top: 20px;
  right: -10px;
  left: -10px;
  bottom: 50px;
  background: white;
  z-index: -1;
  width: 290px;
  margin: auto;
}
/* line 853, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .banner_element {
  display: inline-block;
  height: 350px;
}
/* line 858, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_image, .bannerx2_destino_wrapper .bannerx2_description {
  display: inline-block;
}
/* line 862, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_image {
  width: 790px;
  background-color: black;
  height: 100%;
  overflow: hidden;
}
/* line 868, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_image img {
  width: 100%;
}
/* line 873, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_description {
  width: 350px;
  background-color: #ededef;
  height: 100%;
  display: table;
}
/* line 880, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_description .vertical_center {
  display: table-cell;
  vertical-align: middle;
  padding: 0 26px;
}
/* line 886, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_description a {
  display: inline-block;
  margin-top: 20px;
}
/* line 892, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_title {
  color: #AF0929;
  text-transform: uppercase;
  font-family: "Source Sans Pro";
  font-size: 18px;
}
/* line 900, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2_text {
  color: #787878;
  margin-top: 15px;
  font-family: "Source Sans Pro";
  font-size: 14px;
  line-height: 22px;
}
/* line 909, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .top_banner .bannerx2_image {
  float: right;
}
/* line 913, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .top_banner .bannerx2_description {
  float: left;
}
/* line 919, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bottom_banner .bannerx2_image {
  float: left;
}
/* line 923, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bottom_banner .bannerx2_description {
  float: right;
}
/* line 928, ../sass/_template_specific.scss */
.bannerx2_destino_wrapper .bannerx2-arrow {
  background: #AF0929;
  padding: 13px;
  border-radius: 30px;
  transform: rotate(180deg);
}

/*===== Slider Container ====*/
/* line 937, ../sass/_template_specific.scss */
.slider_inner_container {
  position: relative;
  height: 430px;
  width: 100%;
}
/* line 942, ../sass/_template_specific.scss */
.slider_inner_container .slider_image {
  width: 100%;
  height: auto;
  position: fixed;
  top: 0;
  z-index: -2;
  min-width: 1920px;
}

/* line 952, ../sass/_template_specific.scss */
.tp-bullets {
  bottom: 0px !important;
  opacity: 1 !important;
  z-index: 23 !important;
  width: 450px;
  padding: 10px 0;
  text-align: center;
  background-color: #787878;
}

/* line 962, ../sass/_template_specific.scss */
.tp-bullets .bullet {
  background: url("/img/flas2/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

/* line 969, ../sass/_template_specific.scss */
.tp-bullets .bullet.selected {
  background: url("/img/flas2/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

/* line 974, ../sass/_template_specific.scss */
.tp-rightarrow.default {
  background: url("/img/flas2/flecha-slider.png?v=1") no-Repeat 0 0;
  transform: rotate(180deg);
  top: 13px !important;
}

/* line 980, ../sass/_template_specific.scss */
.tp-leftarrow.default {
  background: url("/img/flas2/flecha-slider.png?v=1") no-Repeat 0 0;
  top: 33px !important;
}

/* line 986, ../sass/_template_specific.scss */
.social_bottom {
  padding: 40px 0;
}

/* line 990, ../sass/_template_specific.scss */
.social_bottom a:hover {
  opacity: 0.8;
}

/* line 994, ../sass/_template_specific.scss */
.social_bottom a span {
  vertical-align: super;
  margin-top: 17px;
  display: inline-block;
  padding-left: 30px;
  font-family: "Source Sans Pro";
}

/*================  Banners x2 ===============*/
/* line 1007, ../sass/_template_specific.scss */
.banners_x2_wrapper .ico-photo {
  position: absolute;
  width: auto;
  top: 20px;
  left: 20px;
}
/* line 1014, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_image img {
  width: 100%;
}
/* line 1018, ../sass/_template_specific.scss */
.banners_x2_wrapper p.bannerx2_title {
  color: #626262;
  text-align: center;
  font-size: 20px;
  margin-bottom: 30px;
  font-weight: bold;
  text-transform: uppercase;
}
/* line 1026, ../sass/_template_specific.scss */
.banners_x2_wrapper p.bannerx2_title:after {
  content: "";
  width: 55px;
  border-bottom: 2px solid #AF0929;
  display: block;
  margin: 17px auto 0px;
}
/* line 1035, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row {
  position: relative;
  clear: both;
  display: table;
  width: 100%;
  margin-bottom: 5px;
}
/* line 1045, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.left .bannerx2_image {
  float: left;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}
/* line 1051, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.left .bannerx2_text {
  width: 35%;
  float: right;
  position: absolute;
  right: 0px;
}
/* line 1060, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.right .bannerx2_image {
  float: right;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}
/* line 1066, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.right .bannerx2_text {
  width: 35%;
  float: left;
  position: absolute;
  left: 0px;
}
/* line 1074, ../sass/_template_specific.scss */
.banners_x2_wrapper .ico-photo {
  width: auto !important;
}
/* line 1078, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_text {
  height: 100%;
}
/* line 1082, ../sass/_template_specific.scss */
.banners_x2_wrapper .banner_center_container {
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  text-align: center;
  padding: 0px 0px;
  display: table;
}
/* line 1091, ../sass/_template_specific.scss */
.banners_x2_wrapper .banner_center_container .button_wrapper_rooms {
  display: none;
}
@-moz-document url-prefix() {
  /* line 1097, ../sass/_template_specific.scss */
  .banners_x2_wrapper .banner_center_container {
    padding-top: 52px;
  }
}
/* line 1102, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_description {
  font-size: 15px;
  padding: 0 40px;
  color: #797979;
  margin-top: 12px;
  line-height: 28px;
  font-family: "Source Sans Pro";
}
/* line 1110, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_description strong {
  font-weight: bold;
}
/* line 1114, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_description .hide_more {
  display: none;
}
/* line 1119, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador {
  /*position: absolute;
  bottom: 5px;
  right: 0px;
  z-index: 22;
  height: 40px;
  width: 200px;
  display: block;
  background: $corporate_2;
  list-style-type: none;
  text-align: center;
  padding-top: 13px;
  box-sizing: border-box;
  padding-left: 9px;*/
  position: absolute;
  right: 343px;
  z-index: 22;
  bottom: 21px;
}
/* line 1138, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador li {
  /*display: inline;*/
  cursor: pointer;
  float: left;
  border: 1px solid white;
  border-radius: 26px;
  box-sizing: border-box;
  cursor: pointer;
  margin: 0 2px;
}
/* line 1149, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador .circle_green {
  background: url("/img/flas2/bullet_flexslider.png") no-repeat;
  width: 20px;
  height: 20px;
  display: inline-block;
}
/* line 1156, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador .circle_green.flex-inner-slideractive {
  background: url("/img/flas2/bullet_flexslider_active.png") no-repeat;
  margin-bottom: 4px;
}
/* line 1161, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador span {
  width: 12px;
  height: 12px;
  display: block;
  border-radius: 26px;
  margin: 2px;
  background: white;
  box-sizing: border-box;
}
/* line 1170, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-controlador span.flex-inner-slideractive {
  background: #fdae47;
}
/* line 1175, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-inner-slidercontrol-nav {
  position: absolute;
  bottom: 10px;
  color: white;
  left: 0;
  right: 0;
  text-align: center;
}
/* line 1183, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-inner-slidercontrol-nav li {
  display: inline-block;
  width: 20px;
  height: 20px;
}
/* line 1188, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-inner-slidercontrol-nav li a {
  background: url("/img/morer/bullet_flexslider.png") no-repeat center;
  display: inline-block;
  width: 20px;
  vertical-align: middle;
  color: transparent;
  cursor: pointer;
}
/* line 1196, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-inner-slidercontrol-nav li .flex-inner-slideractive {
  background: url("/img/morer/bullet_flexslider_active.png") no-repeat center;
}
/* line 1202, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex_prev {
  height: 30px;
  width: 20px;
  left: 10px;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  background: url("/static_1/images/booking/flecha_motor_izq.png") no-repeat center;
  background-size: 9px;
  cursor: pointer;
}
/* line 1213, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex_prev:hover {
  opacity: 0.8;
}
/* line 1218, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex_next {
  height: 30px;
  width: 20px;
  right: 10px;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  background: url("/static_1/images/booking/flecha_motor_der.png") no-repeat center;
  background-size: 9px;
  cursor: pointer;
}
/* line 1231, ../sass/_template_specific.scss */
.banners_x2_wrapper .flex-inner-sliderdirection-nav {
  display: none;
}
/* line 1235, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.left .video_wrapper {
  float: left;
  width: 65%;
  position: relative;
}
/* line 1241, ../sass/_template_specific.scss */
.banners_x2_wrapper .bannerx2_row.right .video_wrapper {
  float: right;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}
/* line 1248, ../sass/_template_specific.scss */
.banners_x2_wrapper .video_wrapper {
  overflow: hidden;
  padding-bottom: 5px;
  position: relative;
}
/* line 1253, ../sass/_template_specific.scss */
.banners_x2_wrapper .video_wrapper img.video_cover_iframe {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 2;
}
/* line 1260, ../sass/_template_specific.scss */
.banners_x2_wrapper .video_wrapper:before {
  content: "";
  padding-top: 47%;
  display: block;
}
/* line 1266, ../sass/_template_specific.scss */
.banners_x2_wrapper .video_wrapper iframe {
  position: absolute;
  top: 0px;
  bottom: 0px !important;
  height: 99%;
  left: 0px;
  right: 0px;
}
/* line 1276, ../sass/_template_specific.scss */
.banners_x2_wrapper img.play_pause_video {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  width: 200px;
  height: 200px;
  margin: auto;
}
/* line 1287, ../sass/_template_specific.scss */
.banners_x2_wrapper .button_wrapper_rooms {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background-color: #787878;
  color: white;
  margin: 15px auto 0 !important;
  text-transform: uppercase;
  padding: 6px;
  box-sizing: border-box;
  cursor: pointer;
}
/* line 1299, ../sass/_template_specific.scss */
.banners_x2_wrapper .button_wrapper_rooms:hover {
  opacity: 0.8;
}
/* line 1304, ../sass/_template_specific.scss */
.banners_x2_wrapper .rooms_buttons_wrapper {
  text-align: center;
}
/* line 1308, ../sass/_template_specific.scss */
.banners_x2_wrapper a.button-promotion.room_promotion {
  background: #AF0929;
  color: white;
  text-decoration: none;
  padding: 6px 20px;
  text-transform: uppercase;
  margin-top: 15px;
  display: inline-block;
  font-family: "Source Sans Pro";
}
/* line 1319, ../sass/_template_specific.scss */
.banners_x2_wrapper h3.bannerx2_title {
  color: #AF0929;
  text-align: center;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-size: 25px;
  font-family: Oswald;
}
/* line 1328, ../sass/_template_specific.scss */
.banners_x2_wrapper .hide_more {
  line-height: 22px;
  font-size: 12px;
}
/* line 1333, ../sass/_template_specific.scss */
.banners_x2_wrapper .hidden_cycle {
  padding: 20px;
}
/* line 1336, ../sass/_template_specific.scss */
.banners_x2_wrapper .hidden_cycle .bannerx2_title {
  text-align: left;
}
/* line 1338, ../sass/_template_specific.scss */
.banners_x2_wrapper .hidden_cycle .bannerx2_title:after {
  margin-left: 0;
}
/* line 1342, ../sass/_template_specific.scss */
.banners_x2_wrapper .hidden_cycle .bannerx2_description {
  padding: 0px 0px;
}
/* line 1345, ../sass/_template_specific.scss */
.banners_x2_wrapper .hidden_cycle .hide_more {
  font-size: 15px;
  font-weight: bolder;
}

/*======= Ofertas ======*/
/* line 1353, ../sass/_template_specific.scss */
a.plus {
  padding: 8px 8px 7px !important;
}

/* line 1357, ../sass/_template_specific.scss */
a.play {
  padding: 10px 9px 5px !important;
}

/* line 1361, ../sass/_template_specific.scss */
.enlace_offer {
  display: block;
  height: 250px;
  overflow: hidden;
  position: relative;
}
/* line 1367, ../sass/_template_specific.scss */
.enlace_offer img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

/* line 1380, ../sass/_template_specific.scss */
.scapes-blocks {
  overflow: hidden;
  margin-top: 0px;
  margin-bottom: -12px;
}

/* line 1386, ../sass/_template_specific.scss */
.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
}
/* line 1392, ../sass/_template_specific.scss */
.scapes-blocks .block a.button-promotion.oferta-reserva {
  width: 100px;
  margin-right: 10px;
}
/* line 1397, ../sass/_template_specific.scss */
.scapes-blocks .block .description {
  padding: 20px;
  position: relative;
  background-color: #ECECEC;
  padding-right: 210px;
}
/* line 1403, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module {
  font-size: 20px;
  color: #AF0929;
  font-weight: 500;
  margin-top: 6px;
  height: 40px;
  font-family: Oswald;
}
/* line 1411, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module .offer-subtitle {
  font-weight: 300;
  font-size: 18px;
}
/* line 1417, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul {
  position: absolute;
  width: 115px;
  right: 7px;
  top: 24px;
  text-align: right;
  padding-right: 10px;
  font-family: "Source Sans Pro";
}
/* line 1426, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li {
  display: inline;
}
/* line 1429, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a {
  background-color: #AF0929;
  top: -3px;
  color: white;
  padding: 8px 7px 6px 7px;
  right: 97px;
  position: absolute;
  text-align: center;
  text-decoration: none;
}
/* line 1439, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a:hover {
  opacity: 0.8;
}
/* line 1443, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.plus {
  padding: 10px 7px 5px;
  margin-right: -86px;
  height: 18px;
  background: #4C4C4C;
}
/* line 1450, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play {
  padding: 10px 9px 5px;
}
/* line 1453, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play img {
  margin-top: 2px;
}
/* line 1460, ../sass/_template_specific.scss */
.scapes-blocks .block .description p {
  margin-bottom: 0;
}

/* line 1466, ../sass/_template_specific.scss */
.en a.button-promotion.oferta-reserva {
  width: 82px;
  right: 114px !important;
}

/* line 1471, ../sass/_template_specific.scss */
.scapes-blocks .row1 {
  margin-right: 10px;
}

/* line 1475, ../sass/_template_specific.scss */
.scapes-blocks .row2 {
  margin-left: 10px;
}

/* line 1479, ../sass/_template_specific.scss */
.scapes-bottom-content {
  background: #AF0929;
  padding: 20px;
}

/* line 1484, ../sass/_template_specific.scss */
.scapes-popup {
  padding: 19px;
}

/* line 1490, ../sass/_template_specific.scss */
.escapadas-popup h3 {
  color: #AF0929;
  margin-bottom: 20px;
  font-family: Oswald;
}
/* line 1495, ../sass/_template_specific.scss */
.escapadas-popup h5 {
  color: #AF0929;
  font-family: "Source Sans Pro";
}
/* line 1499, ../sass/_template_specific.scss */
.escapadas-popup p {
  font-family: "Source Sans Pro";
}

/* line 1504, ../sass/_template_specific.scss */
.offer_popup {
  padding: 25px;
}

/*========= Location and Contact ======*/
/* line 1510, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1517, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1522, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 50px;
  margin-top: 50px;
}

/* line 1531, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #AF0929;
  width: 95%;
  line-height: 20px;
  font-family: Oswald;
}

/* line 1545, ../sass/_template_specific.scss */
.location-info-and-form-wrapper div {
  width: 95%;
}

/* line 1549, ../sass/_template_specific.scss */
.location-info strong {
  font-family: "Source Sans Pro";
}

/* line 1554, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1558, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  width: 100%;
}

/* line 1567, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1571, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1575, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1583, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1588, ../sass/_template_specific.scss */
.form-contact #contactContent .bordeInput {
  width: auto;
  margin-right: 5px;
}

/* line 1594, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;
  font-family: "Source Sans Pro";
}

/* line 1605, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
}

/* line 1615, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  margin-right: 35px;
}

/* line 1625, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
  width: auto;
}

/* line 1631, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #AF0929 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
  font-family: "Source Sans Pro";
}

/* line 1647, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #AF0929 !important;
}

/* line 1651, ../sass/_template_specific.scss */
.location-info {
  padding-left: 20px;
  box-sizing: border-box;
  line-height: 30px;
  font-weight: 300;
  font-size: 14px;
  color: #717171;
  font-family: "Source Sans Pro";
}

/* line 1661, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1667, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #AF0929;
}

/*======= Vive blocks =======*/
/* line 1675, ../sass/_template_specific.scss */
.vive_wrapper h3.subtitle_title {
  text-align: center;
  font-family: Oswald;
  font-size: 40px;
  color: #AF0929;
  padding: 30px 0;
}

/* line 1683, ../sass/_template_specific.scss */
.vive_element {
  width: 273.6px;
  margin-right: 15px;
  float: left;
  margin-bottom: 23px;
  position: relative;
  font-family: "Source Sans Pro";
}
/* line 1691, ../sass/_template_specific.scss */
.vive_element .vive_description .hide_me {
  display: none;
}
/* line 1695, ../sass/_template_specific.scss */
.vive_element .exceded {
  height: 165px;
  position: relative;
  overflow: hidden;
}
/* line 1701, ../sass/_template_specific.scss */
.vive_element .vive_image {
  width: 100%;
  display: block;
}
/* line 1706, ../sass/_template_specific.scss */
.vive_element h3.vive_title {
  color: #787878;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 13px;
}
/* line 1713, ../sass/_template_specific.scss */
.vive_element h3.vive_title strong {
  display: block;
  font-weight: bolder;
}
/* line 1719, ../sass/_template_specific.scss */
.vive_element span.date_blog {
  color: #AF0929;
  font-style: italic;
  font-weight: bolder;
  margin-bottom: 5px;
  display: block;
  font-size: 14px;
}
/* line 1728, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper {
  background: #F4F4F4;
  text-align: center;
  padding: 18px 23px;
}
/* line 1737, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description {
  font-weight: lighter;
  font-size: 12px;
  line-height: 18px;
  height: 110px;
  color: gray;
  overflow: hidden;
  margin-top: 4px;
}
/* line 1746, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description strong {
  color: #787878;
  font-weight: bolder;
}
/* line 1751, ../sass/_template_specific.scss */
.vive_element .vive_description_wrapper .vive_description .destacado {
  position: absolute;
  top: 177px;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #787878;
  padding: 11px;
  font-size: 20px;
}
/* line 1763, ../sass/_template_specific.scss */
.vive_element a.vive_button, .vive_element .vive_button_fotos {
  color: white;
  background: #787878;
  width: 100%;
  display: block;
  text-align: center;
  height: 40px;
  text-decoration: none;
  text-transform: uppercase;
  box-sizing: border-box;
  padding: 10px 0;
}
/* line 1775, ../sass/_template_specific.scss */
.vive_element a.vive_button:hover, .vive_element .vive_button_fotos:hover {
  opacity: 0.8;
}
/* line 1780, ../sass/_template_specific.scss */
.vive_element .vive_buttons_wrapper {
  position: absolute;
  top: 15px;
  left: 15px;
}
/* line 1785, ../sass/_template_specific.scss */
.vive_element .vive_buttons_wrapper img.plus_image {
  width: auto;
  vertical-align: top;
  cursor: pointer;
  float: left;
  width: 34px;
  height: 34px;
}
/* line 1793, ../sass/_template_specific.scss */
.vive_element .vive_buttons_wrapper img.plus_image:hover {
  opacity: 0.8;
}
/* line 1798, ../sass/_template_specific.scss */
.vive_element .vive_buttons_wrapper .see_pictures_text {
  background: #BCBCBD;
  color: white;
  padding: 7px 6px;
  display: inline-block;
}

/* line 1808, ../sass/_template_specific.scss */
.hide_vive_description .vive_title {
  color: #AF0929;
  font-weight: lighter;
  margin-bottom: 0px;
  font-family: Oswald;
}
/* line 1814, ../sass/_template_specific.scss */
.hide_vive_description .vive_title strong {
  display: block;
  font-weight: bolder;
}
/* line 1820, ../sass/_template_specific.scss */
.hide_vive_description span.date_blog {
  color: #787878;
  font-weight: 700;
  font-style: italic;
  margin-bottom: 20px;
  display: block;
  font-family: "SOurce Sans Pro";
}
/* line 1829, ../sass/_template_specific.scss */
.hide_vive_description .vive_description {
  font-weight: lighter;
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  font-family: "Source Sans Pro";
}
/* line 1837, ../sass/_template_specific.scss */
.hide_vive_description .hide_me {
  display: inline;
}
/* line 1841, ../sass/_template_specific.scss */
.hide_vive_description .destacado {
  display: none;
}

/*======= Cycle Banners ======*/
/* line 1850, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element {
  height: 270px;
  overflow: hidden;
  margin-bottom: 5px;
}
/* line 1855, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_image_element {
  width: 35%;
  float: left;
  height: 270px;
}
/* line 1860, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_image_element img {
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}
/* line 1866, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper {
  width: 65%;
  float: right;
  background: #ededef;
  padding: 30px;
  text-align: left;
  font-weight: 100;
  box-sizing: border-box;
  color: black;
  height: 270px;
  line-height: 19px;
  font-family: 'open sans';
  font-size: 13px;
  color: #757575;
}
/* line 1882, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper h3.section_title {
  color: #AF0929;
  font-size: 21px;
  padding-bottom: 14px;
  font-weight: 300;
  font-family: Oswald, serif;
}
/* line 1890, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper span {
  font-family: "Source Sans Pro";
}
/* line 1894, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element .cycle_text_wrapper .exceded {
  overflow: hidden;
}
/* line 1900, ../sass/_template_specific.scss */
.banners_cycle_wrapper .cycle_element.right .cycle_image_element {
  float: right;
}
/* line 1906, ../sass/_template_specific.scss */
.banners_cycle_wrapper a.see_more_section, .banners_cycle_wrapper a.see_more_video {
  background: #AF0929;
  padding: 8px 15px;
  margin-top: 9px;
  display: inline-block;
  text-decoration: none;
  color: white;
  font-weight: 500;
  font-family: "Source Sans Pro";
}

/* line 1918, ../sass/_template_specific.scss */
.hidden_cycle {
  display: none;
  background: white;
  padding: 8px 30px;
  text-align: left;
  font-weight: 100;
  box-sizing: border-box;
  color: black;
  font-size: 14px;
  line-height: 19px;
}
/* line 1930, ../sass/_template_specific.scss */
.hidden_cycle h3.section_title {
  color: #AF0929;
  font-size: 21px;
  padding-bottom: 14px;
  font-weight: 300;
  font-family: Oswald;
}
/* line 1938, ../sass/_template_specific.scss */
.hidden_cycle span {
  font-family: "Source Sans Pro";
}

/* line 1944, ../sass/_template_specific.scss */
ul.gallery_1 {
  margin: 0 !important;
  margin-top: 12px !important;
}

/*======= Content Access =====*/
/* line 1951, ../sass/_template_specific.scss */
.content_access {
  width: 1140px;
}
/* line 1953, ../sass/_template_specific.scss */
.content_access h3.section-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #5CACDB;
  margin-bottom: 28px;
}
/* line 1960, ../sass/_template_specific.scss */
.content_access h3.section-title + div {
  font-size: 17px;
  font-weight: lighter;
  line-height: 29px;
  color: #636363;
  text-align: justify;
}
/* line 1969, ../sass/_template_specific.scss */
.content_access p {
  display: none;
}
/* line 1973, ../sass/_template_specific.scss */
.content_access .my-bookings-booking-info {
  margin: 0 auto;
}
/* line 1977, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields {
  text-align: center;
}
/* line 1981, ../sass/_template_specific.scss */
.content_access form#my-bookings-form {
  text-align: center;
  padding-bottom: 1px;
}
/* line 1986, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields {
  margin-top: 20px;
}
/* line 1989, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields label {
  display: block;
  line-height: 18px;
  font-size: 17px;
  font-weight: lighter;
  color: #636363;
  text-align: center;
}
/* line 1998, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input {
  width: 160px;
  text-align: center;
}
/* line 2003, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input#emailInput {
  margin-bottom: 6px;
}
/* line 2007, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button {
  display: block;
  margin: 20px auto 0;
  width: 165px;
  border: 0;
  background: #AF0929;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
}
/* line 2018, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button:hover {
  opacity: 0.8;
}
/* line 2024, ../sass/_template_specific.scss */
.content_access button#cancelButton {
  display: block;
  margin: 20px auto 0;
  width: 165px;
  border: 0;
  background: #AF0929;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
  display: none;
}

/* line 2039, ../sass/_template_specific.scss */
.gallery_filt_title {
  font-family: Oswald;
  color: #b9ce1b !important;
  border-bottom-color: #b9ce1b !important;
}

/* line 2046, ../sass/_template_specific.scss */
.fancybox-outer {
  background-color: #fff !important;
}

/*===== Work with us =====*/
/* line 2051, ../sass/_template_specific.scss */
form.form_unete {
  background: rgba(226, 226, 226, 0.46);
  padding: 30px;
  width: 400px;
  display: block;
  margin: auto;
}
/* line 2058, ../sass/_template_specific.scss */
form.form_unete label {
  color: #787878;
  font-family: "Source Sans Pro";
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  display: block;
  text-align: center;
}
/* line 2067, ../sass/_template_specific.scss */
form.form_unete label.error {
  color: red;
  margin-top: 0;
  line-height: 0;
  margin-bottom: 16px;
}
/* line 2075, ../sass/_template_specific.scss */
form.form_unete input {
  width: 100%;
  text-align: center;
  margin-top: 2px;
  margin-bottom: 15px;
  padding: 8px 0;
  border: 0;
}
/* line 2084, ../sass/_template_specific.scss */
form.form_unete input[type="file"] {
  margin-left: 65px !important;
}
/* line 2087, ../sass/_template_specific.scss */
form.form_unete input[type="checkbox"] {
  float: left;
  width: 25px;
  margin-top: 5px;
  margin-left: 50px;
}
/* line 2094, ../sass/_template_specific.scss */
form.form_unete #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #AF0929 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
  font-family: "Source Sans Pro";
}

/* line 2112, ../sass/_template_specific.scss */
.landing_wrapper {
  position: relative;
  height: 436px;
}
/* line 2116, ../sass/_template_specific.scss */
.landing_wrapper .image_landing {
  display: inline-block;
}
/* line 2119, ../sass/_template_specific.scss */
.landing_wrapper .image_landing img {
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
}
/* line 2127, ../sass/_template_specific.scss */
.landing_wrapper .landing_description {
  display: inline-block;
  position: absolute;
  top: 100px;
  left: 200px;
  font-family: Source Sans Pro;
}
/* line 2134, ../sass/_template_specific.scss */
.landing_wrapper .landing_description .landing_title {
  text-transform: uppercase;
}
/* line 2138, ../sass/_template_specific.scss */
.landing_wrapper .landing_description .landing_content {
  font-weight: lighter;
  margin-top: 30px;
}
/* line 2144, ../sass/_template_specific.scss */
.landing_wrapper #landingBookingWidget {
  display: inline-block;
  position: absolute;
  top: 29px;
  right: 210px;
}

/* line 2154, ../sass/_template_specific.scss */
.fancy-popup-auto .fancybox-outer {
  background: white !important;
}
/* line 2157, ../sass/_template_specific.scss */
.fancy-popup-auto .fancybox-outer .title-module {
  font-family: "Oswald";
  color: #AF0929;
  font-size: 20px;
  margin-bottom: 10px;
}

/* line 38, ../sass/styles_hotel-rosamar.scss */
#logoDiv a img {
  width: 200px;
}

/* line 42, ../sass/styles_hotel-rosamar.scss */
#full_wrapper_booking #fecha_entrada input, #fecha_salida input {
  background: white url(/img/flas2/ico-rosamar/ico-calendario-header.png) no-repeat 93% !important;
}

/* line 46, ../sass/styles_hotel-rosamar.scss */
#full_wrapper_booking #motor_reserva select, #full_wrapper_booking #motor_reserva select.selector_adultos, #full_wrapper_booking #motor_reserva select.selector_ninos {
  background: white url(/img/flas2/ico-rosamar/down-arrow.png) no-repeat 93% !important;
}

/* line 50, ../sass/styles_hotel-rosamar.scss */
header #section-active a {
  color: #AF0929;
}

/* line 54, ../sass/styles_hotel-rosamar.scss */
header #main_menu a:hover {
  color: #AF0929;
}

/* line 58, ../sass/styles_hotel-rosamar.scss */
header #main_menu .button-promotion:hover {
  color: white !important;
  opacity: 0.8;
}

/* line 63, ../sass/styles_hotel-rosamar.scss */
footer .footer_column_title {
  color: #AF0929 !important;
}

/* line 67, ../sass/styles_hotel-rosamar.scss */
.gallery_filt_title {
  color: #AF0929 !important;
  border-bottom-color: #AF0929 !important;
}

/* line 74, ../sass/styles_hotel-rosamar.scss */
.ru header #main_menu a {
  font-size: 14px;
}
/* line 78, ../sass/styles_hotel-rosamar.scss */
.ru .scapes-blocks .block .description ul li a {
  right: 60px;
}
/* line 82, ../sass/styles_hotel-rosamar.scss */
.ru .scapes-blocks .block .description ul li a.plus {
  margin-right: -55px;
}
