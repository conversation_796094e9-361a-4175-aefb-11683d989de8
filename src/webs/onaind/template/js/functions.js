$(window).load(function () {
  $(".myFancyPopup").fancybox({
    maxWidth: 800,
    maxHeight: 600,
    fitToView: false,
    width: '70%',
    height: '70%',
    autoSize: false,
    aspectRatio: false,
    closeClick: false,
    openEffect: 'none',
    closeEffect: 'none'
  });


  $(".myFancyPopupAuto").fancybox({
    width: 650,
    height: 'auto',
    fitToView: false,
    autoSize: false
  });
  $(".myFancyPopupVideo").fancybox({
    width: 640,
    height: 'auto',
    fitToView: false,
    autoSize: false
  });

  //Adding class to Main Item in Main Menu Item Menu when child are selected
  $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

  if (window.PIE) {
    $(".css3").each(function () {
      PIE.attach(this);
    });
  }

  if (typeof (TAPixel) !== "undefined") {
    TAPixel.impressionWithReferer("001F000000vA4u0");
  }

  $("img[lazy=true]").unveil();

  effects_sass();

  $(".button-promotion, .button_promotion").unbind('click');
  only_execute_once = false;
  $(".button-promotion, .button_promotion").click(function () {
    prepare_booking_popup();
    open_booking_full();

    var room_filter = $(this).data("specialfilter");
    if (room_filter) {
      $("#data #roomFilter").val(room_filter);
    }
    if (!only_execute_once) {
      only_execute_once = true;
      $("#data select.selector_ninos").selectric("refresh");

    }
  });

  $(".close_fancybox").click(function () {
    $.fancybox.close();
  });
  $(".close_menu").click(function () {
    $(".menu_toggle").toggleClass("active");
    $("#main_menu").toggleClass("active");
    $("header").toggleClass("opened");
  });
  $(".menu_toggle").click(function () {
    $(this).toggleClass("active");
    $("#main_menu").toggleClass("active");
    $("header").toggleClass("opened");
  });

  showMenu();

  var bubble_content = $(".btn_bubble").html();
  $("#data .submit_button").html(bubble_content + "<span>" + $("#data .submit_button").html() + "</span>");
  $(".btn").each(function () {
    $(this).html(bubble_content + "<span>" + $(this).html() + "</span>");
  });

  $('header .button_promotion').on('click', function () {
    $('#wrapper_booking_fancybox .room_list_wrapper').show();
  })
});

$(window).scroll(showMenu);

function showMenu() {
  var actual_position = $(window).scrollTop(),
      slider_height = $("#slider_container").height() / 2,
      menu_showed = $("header").hasClass('fixed');

  if ((actual_position > slider_height) && (!menu_showed)) {
    $("header").addClass('fixed')
  }

  if ((actual_position < slider_height) && (menu_showed)) {
    $("header").removeClass("fixed");
  }
}