.offers_wrapper {
  padding: 0 calc((100% - 1100px) / 2) 70px;
  .offers {
    display: table;
    width: 100%;
    .offer {
      position: relative;
      float: left;
      width: calc(50% - 20px);
      height: 350px;
      margin-right: 20px;
      margin-bottom: 20px;
      overflow: hidden;
      background: $charcoal;
      &:nth-child(2n) {
        margin-right: 0;
      }
      &:hover {
        background: $terracota;
        &:before {
          height: 150%;
          border-radius: 0;
        }
        .offer_pic img {
          opacity: 0;
        }
        .offer_info .desc, .offer_info .btn {
          opacity: 1;
        }
      }

      &.active {
        background: $terracota;
        &:before {
          height: 150%;
          border-radius: 0;
        }
        .offer_pic img {
          opacity: 0;
        }
        .offer_info .desc, .offer_info .btn {
          opacity: 1;
        }
      }

      &:before {
        content: '';
        @include center_xy;
        background: $charcoal;
        width: 150%;
        height: 0;
        border-radius: 50%;
        @include transition(all,1s);
      }
      .offer_pic {
        @include full_size;
        img {
          @include center_image;
          opacity: .8;
          @include transition(opacity,.6s);
        }
      }
      .offer_info {
        @include full_size;
        padding: 50px;
        .title {
          position: relative;
          @include h4;
          font-size: 34px;
          line-height: 36px;
          color: white;
        }
        .desc {
          opacity: 0;
          @include body1;
          color: white;
          position: relative;
          margin-top: 25px;
          @include transition(opacity,1s);
          transition-delay: .3s;
        }
        .price {
          position: absolute;
          bottom: 25px;
          left: 25px;
          @include body1;
          color: white;
          big {
            @include h4;
            font-size: 34px;
          }
        }
        .btn {
          opacity: 0;
          position: absolute;
          bottom: 20px;
          right: 20px;
          width: 100px;
          height: 90px;
          @include transition(opacity,1s);
          span {
            margin-top: -10px;
          }
          &:hover {
            svg {
              * {
                fill: white;
                stroke: white;
                stroke-width: 0;
                stroke-linecap: round;
              }
            }
          }
        }
      }
    }
  }
}