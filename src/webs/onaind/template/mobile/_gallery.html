<div class="filters_gallery">
    {% for gallery, data in filters_gallery.items() %}
        <div class="gallery {{ gallery }}">
            <div class="title_gallery active" data-gallery="{{ gallery }}">{{ data.title|safe }}</div>
            {% for pic in data.pics %}
                <div class="pic {% if data.total and loop.index > data.total %}notshow{% endif %}">
                    <a href="{{ pic.servingUrl }}=s1900" data-fancybox="{{ pic.class_filter }}"
                     data-caption="{{ pic.title|safe }} - {{loop.index}}/{{ data.pics|length }}">
                        <img src="{{ pic.servingUrl }}=s300-c" alt="{{ pic.altText|safe }}">
                    </a>
                </div>
            {% endfor %}
        </div>
    {% endfor %}
</div>

<script>$(window).load(function () {
    $(".filters_gallery .title_gallery").click(function () {
        var gallery = $(this).data("gallery");
        $(this).toggleClass("active");
        if($(this).hasClass("active")){
            $("."+gallery+" .pic:not(.notshow)").fadeIn();
        } else {
            $("."+gallery+" .pic:not(.notshow)").fadeOut();
        }
    });
    {% if theme %}$("body").addClass("{{ theme|safe }}");{% endif %}
    {% if custom_widget %}$(".mobile_engine").hide();{% endif %}
});</script>