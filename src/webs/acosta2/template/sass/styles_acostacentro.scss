@import "compass";

$corporate_1: rgb(225, 5, 41);
$corporate_2: #787775;
$corporate_3: #CD9522;

@import "defaults";

#full_wrapper_booking .nights_number_wrapper_personalized {
  background: $corporate_3;
}

div#data {
  background: rgba($corporate_3, .7);
}

#full_wrapper_booking .wrapper_booking_button .submit_button {
  background: $corporate_3;

  &:hover {
    background: lighten($corporate_3, 10%);
  }
}

.newsletter_checkbox {
  color: white;
  font-size: 12px;
  text-align: left;

  a {
    text-decoration: underline !important;
  }
}

.check_newsletter {
  width: 285px;
}

input#promotions {
  float: left;
  margin-bottom: 16px;
}


.info{

    .title{
        a{
            text-decoration: underline !important;
        }
    }
    #promotions{
            height: auto;
            margin-left: -3px !important;

    }
    label[for=promotions]{
        float: left;
        font-size: 11px;
        margin-top: -29px;
        margin-left: 23px;
        color: #585858;




    }
}
