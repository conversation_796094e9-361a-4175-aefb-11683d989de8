.detailed_room_wrapper {
  margin-top: 100px;
  margin-bottom: 70px;
  float: left;
  display: inline-block;
  width: 100%;

  .room_detail_image_wrapper {
    height: 325px;
    position: relative;
    overflow: hidden;

    .room_detail_image {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }

  a.see_more_pictures_detailed {
    position: absolute;
    z-index: 1;
    bottom: 25px;
    right: 25px;
    text-transform: uppercase;
    text-decoration: none;
    color: white;

    span {
      display: inline-block;
      vertical-align: middle;
    }

    .fa {
      border: 2px solid white;
      position: relative;
      vertical-align: middle;
      width: 42px;
      height: 42px;
      margin-left: 10px;

      &:before {
        @include center_xy;
        font-size: 21px;
      }
    }
  }

  .next_prev_wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    a {
      display: inline-block;
      width: 200px;
      height: 65px;
      border: 2px solid white;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      float: left;
      text-align: center;
      color: $corporate_1;
      margin-right: 10px;
      background: white;

      &:last-child {
        margin-right: 0;
        float: right;
        .fa {
          left: auto;
          right: 5px;
        }
      }

      &:hover {
        .fa, span {
          color: $corporate_2;
        }
      }

      span {
        @include center_y;
        display: block;
        width: 100%;
        font-size: 12px;
        @include transition(color, .3s);
      }
      .fa {
        @include center_y;
        left: 5px;
        color: $corporate_1;
        font-size: 32px;
        @include transition(color, .3s);
      }
    }
  }

  .room_details_text {
    display: block;
    width: 547px;
    margin-top: -125px;
    z-index: 2;
    position: relative;
    background: white;
    padding: 40px;
    float: left;
    box-sizing: border-box;

    &:after {
      content: "";
      width: 22px;
      height: 22px;
      border-left: 2px solid $corporate_1;
      border-bottom: 2px solid $corporate_1;
      display: inline-block;
      position: absolute;
      left: 0;
      bottom: 0;
    }

    a.button-promotion {
      background: $corporate_2;
      color: white;
      text-decoration: none;
      float: right;
      text-transform: uppercase;
      width: 205px;
      height: 65px;
      box-sizing: border-box;
      text-align: center;
      padding: 22px 0;
      font-family: 'Oswald', sans-serif;
      @include transition(all, .6s);

      &:hover {
        background-color: $corporate_1;
      }
    }

    h1.room_title {
      font-size: $title_size;
      color: $corporate_1;
      font-family: 'Oswald', sans-serif;
      float: left;
      width: 230px;
      text-transform: uppercase;

      &:after {
        content: '';
        display: block;
        width: 65px;
        height: 4px;
        background: #ccc;
        margin: 21px 0 27px;
      }
    }

    .room_description {
      margin-top: 8px;
      font-size: 12px;
      line-height: 18px;
      color: #9a9a9a;
      clear: both;
    }

    #shareSocialArea {
      float: right;
    }
  }

  .minigallery_room_wrapper {
    display: inline-block;
    width: 593px;
    float: right;
    margin-top: 30px;

    .minigallery_element {
      display: inline-block;
      float: left;
      width: calc(25% - 10px);
      height: 138px;
      margin-right: 13px;
      position: relative;
      overflow: hidden;
      margin-top: 13px;

      &:nth-child(4n), &:last-child {
        margin-right: 0;
      }

      &:nth-child(-n+4) {
        margin-top: 0;
      }

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }
  }
  .services_slider_wrapper {
    margin-top: 30px;
    margin-bottom: 0;
  }
}