.contact_form_wrapper {
  background-color: #FAFAFA;
  display: table;
  clear: both;
  width: 100%;
  float: left;
  padding: 20px 80px 0;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  h3 {
    text-align: center;
    color: $corporate_1;
    font-size: 25px;
    font-weight: 100;
    margin-bottom: 30px;
    strong {
      font-weight: 700;
    }
  }

  #contact {
    display: table;
    width: 980px;
    margin: auto;

    .top_form {
      text-align: right;
      display: table;
      width: 100%;
      font-size: 14px;
      color: #4B4B4B;
      span {
        display: inline-block;
        vertical-align: middle;
        padding: 5px 20px 5px 0;
      }
      input[type=checkbox] {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
      }
    }
    label {
          padding: 15px 0 0;
          display: block;
          font-size: 14px;
          color: lighten($corporate_2, 30%);
          &.error {
            position: absolute;
            bottom: -20px;
            padding: 5px 10px;
            color: #943E46;
            background-color: #f8d7da;
            border-color: #f5c6cb;
            border-radius: 5px;
            z-index: 2;
          }
        }
    .contInput {
      display: inline-block;
      float: left;
      width: 100%;
      padding: 10px 0 10px 20px;
      position: relative;

      input[type=text], textarea {
        background-color: white;
      }

      &:nth-of-type(-n+2) {
        width: calc((100% - 20px)/2);
        padding-top: 20px;
      }

      &:nth-of-type(3), &:nth-of-type(4) {
        width: calc((100% - 20px)/2);
        .fa {
          top:15px;
        }
      }

      &:nth-of-type(5) {
        .fa {
          top:15px;
        }
      }

      &:nth-of-type(3), &:nth-of-type(5) {
        margin-right: 0;
      }

      &:nth-of-type(6) {
        padding: 0 20px 20px;
        input[type=file] {
          position: absolute;
          left: 20px;
          right: 20px;
          top: 5px;
          padding-top: 10px;
          padding-left: 400px;
        }
        .fa {
          top: 5px;
        }
      }

      .fa {
        width: 40px;
        height: 40px;
        color: $corporate_1;
        position: absolute;
        top: 25px;
        left: 20px;

        &:before {
          @include center_xy;
        }
      }

      input {
        width: 100%;
        height: 50px;
        padding-left: 40px;
        border: 0;

        &#accept-term {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }
      }

      textarea {
        width: calc(100% - 20px);
        padding-left: 40px;
        padding-top: 20px;
        border-color: transparent;
      }
      &.captcha {
        margin-left: 330px;
      }
    }
    .policy-terms {
      text-align: left;
    }
    a {
      display: inline-block;
      vertical-align: middle;
      color: #999;
    }

    #contact-button {
      display: block;
      margin: auto;
      width: calc(100% - 40px);
      background: $corporate_1;
      color: white;
      padding: 20px 0;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 100;
      margin-bottom: 10px;
      cursor: pointer;
      &:hover {
        background-color: darken($corporate_1,10%);
      }
    }
  }
}

.info{
    .contInput.policy-terms{
        #accept-term{
            clear: left;
            float: left;
            margin-top: 10px;
        }
        a{
            text-align: left;
            font-size: 13px;
            margin-top: 7px;
            text-decoration: underline;
        }
        #promotions{
            clear: left;
            float: left;
            width: auto;
            margin-top: -4px;
        }
        label[for=promotions]{
            color: #999 !important;
            font-size: 13px !important;
            margin-bottom: -5px !important;
        }

    }
}