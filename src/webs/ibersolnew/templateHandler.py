# -*- coding: utf-8 -*-
from copy import deepcopy
import datetime

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY, NEWSLETTER_POPUP_THANKS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape, build_friendly_url
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties, \
	get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_title, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os
from collections import OrderedDict

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "iberw"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web,
			'recaptcha_publickey': get_config_property_value(PUBLIC_CAPTCHA_KEY)
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))

		else:
			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language)
			}
			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		if section:
			section_type = section['sectionType']
		else:
			section = {}

		result_params_dict = {
			'base_web': base_web,
			'language_selected': get_language_title(language),
			'oficial_site': get_section_from_section_spanish_name('_oficial_site', language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True),
			'newsletter_custom': self.buildCustomNewsletter(language, check_newsletter=True, social=True),
			'hotels_dropdown': self.get_hotels_grouped(language),
			'booking_engine_2': self.buildSearchEngine2(language),
			'phone_selector': get_section_from_section_spanish_name(u'telefono de hoteles', language),
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'click_to_call_popup': get_section_from_section_spanish_name_with_properties("_click_to_call_popup", language),
			'floating_extra_buttons': get_pictures_from_section_name('_floating_buttons', language),
			'extra_top_header': get_pictures_from_section_name('_extra_top_header', language),
			'tick_slider': get_pictures_from_section_name('_tick_slider', language),
			'extra_top_header_covid': self.getPicturesProperties(language, 'extra_top_header_covid')
		}

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		# Automatic Content
		automatic_content = {
			'Galeria de Imagenes': True,
			'Mis Reservas Corp': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		newsletter_banner = get_section_from_section_spanish_name('_newsletter_banner', language)
		newsletter_advance_property = self.getSectionAdvanceProperties(newsletter_banner, language)
		if newsletter_advance_property.get('extra_image'):
			result_params_dict['newsletter_extra_image'] = newsletter_advance_property['extra_image']

		# floating buttons data
		contact_section = get_section_from_section_spanish_name(u'contacto', language)
		contact_url = contact_section.get('friendlyUrlInternational')
		if contact_url:
			result_params_dict['contact_url'] = contact_url
		checkin_section = get_section_from_section_spanish_name('_checkin_online', language)
		checkin_url = checkin_section.get('content')
		if checkin_section:
			result_params_dict['checkin_url'] = checkin_url

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")

		if section_type == "Inicio":
			result['home'] = True
			result['content_subtitle'] = None

		elif section_type == u"Habitación Individual" or section_type == "Extra 1" or section_type == "Extra 2":
			result['filter_namespace'] = section.get('title').replace("_","")
			minigallery_hotel = get_pictures_from_section_name(section.get('title'), language)
			for pic in minigallery_hotel:
				if pic.get('title') == 'slider':
					result['slider_pic'] = pic
				if pic.get('title') == 'parallax':
					result['parallax'] = pic
			result["minigallery_hotel"] = list(filter(lambda x: not x.get('title') or x.get('title') not in ['slider','destiny','parallax'], minigallery_hotel))
			result["content_hotel"] = True

		elif section_type == "Ofertas":
			result['offers'] = self.getOffers(language)

		elif section_type == "Oferta Individual":
			result['oferta_individual'] = True
			namespace_list = []
			remove_namespace = []
			all_offers = self.getOffers(language)
			for offer in all_offers:
				if offer.get('linkUrl') == section.get('friendlyUrlInternational') and offer.get("not_in"):
					remove_namespace = offer.get("not_in").split(";")
			all_hotels = self.get_hotels_apartments(language)
			for hotel in all_hotels:
				if hotel.get('namespace') not in remove_namespace:
					namespace_list.append(hotel.get('namespace'))

			result['hotels_by_destiny'] = self.get_hotels_by_namespace(namespace_list, language)

		elif section_type == u"Localización":
			result['contact_form'] = True
			result['select_hotels'] = self.get_hotels_apartments(language)
			result['iframe_maps'] = get_section_from_section_spanish_name("iframe google maps", language)

		elif section_type == 'Noticias':
			result['news_entries'] = self.buildNewsInfo(language)

		elif not section:
			news = self.buildNewsInfo(language)
			result['individual_news_entry'] = self.getCurrentNewsItem(news, language)
			if result.get('individual_news_entry'):
				result['pictures'] = result['individual_news_entry']['pictures']
			#related_news = list(filter(lambda x: x.get('tag_reference') and (x.get('tag_reference') == result['individual_news_entry'].get('tag_reference'), ''), news))
			#related_news = list(filter(lambda x: x.get('id') != result['individual_news_entry'].get('id', ''), related_news))
			#result['related_news'] = related_news
			result['news_section'] = get_sections_from_type('Noticias', language)
			convert_to_datetime = lambda x: datetime.datetime.strptime(x, '%d/%m/%Y')
			news.sort(reverse=True, key=lambda x: convert_to_datetime(x.get('creationDate')) if x.get('creationDate') else convert_to_datetime('01/01/1000'))
			result['sorted_news'] = news

		return result


	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True
	def get_revolution_transition(self):
		# Advance config to only change animation on ibersol corpo
		return "fade"

	# Cache
	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)

		for offer in offers:
			pictures_offer = getPicturesForKey(language, str(offer.get("offerKey")), [])
			if offer.get("priority") and "P" in offer['priority']:
				offer['filter_class'] = "package"
			else:
				offer['filter_class'] = "offer"
			if pictures_offer:
				advance_properties = self.getSectionAdvanceProperties(pictures_offer[0], language)
				if advance_properties.get("not_in"):
					offer['not_in'] = advance_properties["not_in"]

		return offers

	# Cache
	def getExtraBanners(self, section, language):
		result_params = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get("domain"):
			if advance_properties.get("domain") and not advance_properties.get('replace_domain_in_web'):
				result_params['hotel_link'] = advance_properties.get("domain")
			if advance_properties.get('replace_domain_in_web'):
				result_params['hotel_link'] = advance_properties.get('replace_domain_in_web')

		if advance_properties.get("iframe_map"):
			result_params['iframe_map'] = unescape(advance_properties.get('iframe_map', ''))
			result_params['price'] = unescape(advance_properties.get('price', ''))
			result_params['price_label'] = unescape(advance_properties.get('price_label', ''))

		if advance_properties.get("price"):
			result_params['price'] = advance_properties.get("price")
			if advance_properties.get("price_label"):
				result_params['price_label'] = unescape(advance_properties.get("price_label", ''))

		if advance_properties.get("hotels_by_destiny"):
			normalize_destiny = normalizeForClassName(advance_properties['hotels_by_destiny'])
			result_params['hotels_by_destiny'] = self.get_hotels_by_destiny(normalize_destiny, language)
			hotels_by_destiny = self.get_hotels_by_destiny(normalize_destiny, language)
			for hotel in hotels_by_destiny:
				if hotel.get("is_apartment"):
					result_params['have_apartments'] = True

		if advance_properties.get("hotels_by_namespace"):
			namespace_list = advance_properties['hotels_by_namespace'].split(";")
			result_params['hotels_by_destiny'] = self.get_hotels_by_namespace(namespace_list, language)

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			mini_dict = {'minigallery': minigallery_images,'num_items': 4,'margin': 10}
			minigallery_html = self.buildTemplate_2("_minigallery.html", mini_dict, False, 'ibersolnew')
			result_params["minigallery_property"] = advance_properties.get("minigallery")
			result_params["minigallery"] = minigallery_html

		if advance_properties.get("minigallery_footer"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery_footer"), language)
			mini_dict = {'minigallery': minigallery_images,'num_items': 4,'margin': 10}
			minigallery_html = self.buildTemplate_2("_minigallery.html", mini_dict, False, 'ibersolnew')
			result_params["minigallery_footer_property"] = advance_properties.get("minigallery_footer")
			result_params["minigallery_footer"] = minigallery_html

		if advance_properties.get('banner_icons'):
			result_params["banner_icons"] = get_pictures_from_section_name(advance_properties.get('banner_icons'), language)
			result_params["banner_icons_sec"] = get_section_from_section_spanish_name(advance_properties.get('banner_icons'), language)

		if advance_properties.get('banner_destinies'):
			result_params["banner_destinies"] = get_section_from_section_spanish_name(advance_properties.get('banner_destinies'), language)
			result_params['hotels_grouped'] = self.get_hotels_grouped(language)

		if advance_properties.get('banner_offers'):
			result_params["banner_offers_sec"] = get_section_from_section_spanish_name(advance_properties.get('banner_offers'), language)
			result_params["banner_offers"] = self.getOffers(language)

		if advance_properties.get("events_banner"):
			destiny_events_list = self.getPicturesProperties(language, "_destiny_events_list", ['filter', 'hotels'])
			for destiny in destiny_events_list:
				hotels_in_event = []
				if destiny.get("hotels") and destiny.get("filter"):
					hotels = self.get_hotels_apartments(language)

					for hotel in hotels:
						hotel['events_filtered'] = []
						add_hotel = False
						if hotel.get("events"):
							for event in hotel['events']:
								if event.get("filter") and event['filter'] == destiny['filter']:
									add_hotel = True
						if add_hotel:
							hotels_in_event.append(hotel)

					destiny['hotels'] = hotels_in_event

			result_params['events_banner'] = destiny_events_list

		result_params['footer_columns'] = get_pictures_from_section_name("footer columns", language)
		for col in result_params['footer_columns']:
			if col.get("servingUrl"):
				result_params['footer_columns_back'] = col.get('servingUrl')

		if get_pictures_from_section_name("extra_footer", language):
			result_params['extra_footer'] = get_pictures_from_section_name("extra_footer", language)

		if advance_properties.get("accordion_banner"):
			result_params['accordion_banner_sec'] = get_section_from_section_spanish_name(advance_properties['accordion_banner'], language)
			result_params['accordion_banner'] = get_pictures_from_section_name(advance_properties['accordion_banner'], language)

		if advance_properties.get('hide_booking_engine'):
			result_params['hide_booking_engine'] = True

		if advance_properties.get("form_contact_cv"):
			result_params['form_contact_cv'] = unescape(advance_properties.get("form_contact_cv"))
			result_params['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			#result_params["form_contact_cv"] = get_section_from_section_spanish_name(advance_properties.get('form_contact_cv'), language)
			result_params["work_form_subject"] = "Empleo"
			if advance_properties.get('thanks_cv'):
				result_params['thanks_cv'] = unescape(advance_properties['thanks_cv'])
			if advance_properties.get("hotel_options"):
				result_params["hotel_form_work"] = unescape(advance_properties.get("hotel_options", ""))
				result_params["hotel_form_work_list"] = result_params["hotel_form_work"].split(';')
			if advance_properties.get("destiny_options"):
				result_params["destiny_form_work"] = unescape(advance_properties.get("destiny_options", ""))
				result_params["destiny_form_work_list"] = result_params["destiny_form_work"].split(';')

		return result_params

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.get_hotels(language)
		mini_dict['apartments'] = self.get_apartments(language)
		mini_dict['aparthotels'] = self.get_aparthotels(language)
		mini_dict['hotels_grouped'] = self.get_hotels_grouped(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, 'ibersolnew')
		if user_agent_is_mobile():
			options['hotels_list_mobile'] = self.get_hotels_apartments(language)
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.get_hotels(language)
		mini_dict['apartments'] = self.get_apartments(language)
		mini_dict['aparthotels'] = self.get_aparthotels(language)
		mini_dict['hotels_grouped'] = self.get_hotels_grouped(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, 'ibersolnew')
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
		'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result = self.getPicturesForGallerySection(language)

		elif section['sectionType'] == "Mis Reservas Corp":
			hotels = self.get_hotels_apartments(language)
			for hotel in hotels:
				if hotel.get("domain"):
					hotel['value'] = hotel.get("subtitle")
			custom_buttom = get_section_from_section_spanish_name('_checkin_online', language)
			custom_url = custom_buttom.get('content')
			if custom_buttom:
				result['custom_url'] = custom_url
				result.update(get_web_dictionary(language))
				result['custom_button_text'] = result['T_checkin_online']

			result['selectOptions'] = hotels
			result['modify_reservation'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation_content'] = True
			result['checkin_reservation_online'] = True
			result['custom_button'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	# Cache
	def get_hotels(self, language):
		return self.get_info_accommodation(u"Habitación Individual", language)

	# Cache
	def get_apartments(self, language):
		return self.get_info_accommodation(u"Extra 1", language)

	# Cache
	def get_aparthotels(self, language):
		return self.get_info_accommodation(u"Extra 2", language)

	# Cache
	def get_hotels_apartments(self, language):
		return self.get_hotels(language) + self.get_apartments(language) + self.get_aparthotels(language)

	def get_info_accommodation(self, section_type, language):
		hotel_list = get_sections_from_type(section_type, language)
		returnhotels = []
		for hotel_element in hotel_list:
			hotel_properties = self.getSectionAdvanceProperties(hotel_element, language)
			hotel_element.update(hotel_properties)
			hotel_element['value'] = hotel_element.get('name')
			hotel_element['id'] = hotel_element.get('title').replace("_", "")
			hotel_element['namespace'] = hotel_element.get('id')
			hotel_element['iframe_map'] = unescape(hotel_element.get('iframe_map', ''))
			hotel_element['price_label'] = unescape(hotel_element.get('price_label', ''))
			hotel_element['destiny_class'] = normalizeForClassName(hotel_element.get('destiny'))
			hotel_element['stars'] = int(hotel_element.get('stars',0))
			hotel_element['order'] = int(hotel_element.get('order')) if hotel_element.get('order') else 0
			hotel_element['linkUrl'] = build_friendly_url(hotel_element.get('namespace'))
			hotel_element['gallery'] = get_pictures_from_section_name(hotel_element.get('title'), language)
			hotel_element['kids_ages'] = hotel_properties.get('kids_ages')
			hotel_element['no_kids'] = hotel_properties.get('no_kids')
			for pic in hotel_element['gallery']:
				if pic.get('title') == 'main':
					hotel_element['main_pic'] = pic
				if pic.get('title') == 'parallax':
					hotel_element['parallax'] = pic
			if section_type == u"Extra 1":
				hotel_element['is_apartment'] = True

			if hotel_element.get("events"):
				hotel_element['events_desc'] = get_section_from_section_spanish_name(hotel_element['events'], language)
				hotel_element['events'] = self.getPicturesProperties(language, hotel_element['events'], ['filter'])

			returnhotels.append(hotel_element)

		returnhotels.sort(key=lambda k: int(k['order']))

		return returnhotels

	# Cache
	def get_hotels_grouped(self, language):
		hotels = self.get_hotels_apartments(language)
		group = OrderedDict()
		for hotel in hotels:
			group.setdefault(hotel.get('destiny'), {"destinos": OrderedDict()})
			group[hotel.get('destiny')]["destinos"].setdefault(hotel.get('region'), [])
			group[hotel.get('destiny')]["class_name"] = normalizeForClassName(hotel.get('destiny'))
			group[hotel.get('destiny')]["linkUrl"] = build_friendly_url(hotel.get('destiny'))
			group[hotel.get('destiny')]["price"] = 1000
			group[hotel.get('destiny')]["price_label"] = hotel.get('price_label')
			if hotel.get('is_apartment'):
				group[hotel.get('destiny')]["is_apartment"] = True
			if group[hotel.get('destiny')]["price"] > float(str(hotel.get('price',0)).replace(",",".")):
				group[hotel.get('destiny')]["price"] = float(str(hotel.get('price',0)).replace(",","."))
			for image in hotel.get('gallery',[]):
				if image.get('title') == 'destiny':
					group[hotel.get('destiny')]['region_image'] = image.get('servingUrl')
			group[hotel.get('destiny')]["destinos"][hotel.get('region')].append(hotel)
			if group[hotel.get('destiny')].get("n_hoteles", False):
				group[hotel.get('destiny')]["n_hoteles"] = group[hotel.get('destiny')]["n_hoteles"] + 1
			else:
				group[hotel.get('destiny')]["n_hoteles"] = len(group[hotel.get('destiny')]["destinos"][hotel.get('region')])
		return group

	# Cache
	def get_hotels_by_destiny(self, destiny, language):
		hotels = self.get_hotels_apartments(language)
		result = []
		for hotel in hotels:
			if hotel.get("destiny_class") and hotel["destiny_class"] == destiny:
				result.append(hotel)

		return result

	# Cache
	def get_hotels_by_namespace(self, namespace_list, language):
		hotels = self.get_hotels_apartments(language)
		result = []
		for hotel in hotels:
			if hotel.get("namespace") in namespace_list:
				result.append(hotel)

		return result

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		language_dict = get_web_dictionary(language)

		if section_type in ["Habitaciones", u"Habitación Individual", "Extra 1"]:
			args = {
				'title': section.get('subtitle'),
				'content': section.get('content'),
				'custom_elements': self.getHtmlExtraBannersMobile(section, language)
			}
			if section_type in [u"Habitación Individual", "Extra 1"]:
				params = {
					'minigallery_mobile': get_pictures_from_section_name(section.get("sectionName"),language)
				}
				params.update(language_dict)
				args['custom_elements'] += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", params, False)

			return self.buildTemplate_2("mobile/secciones/normal/normal_section.html", args, False)

		elif section_type == "Ofertas":
			offers = self.getOffers(language)
			offers_filter = {
				# "package": get_web_dictionary(language).get("T_paquetes"),
				"offer": get_web_dictionary(language).get("T_ofertas")
			}

			args = {
				'elements': offers,
				'offers_filter': offers_filter
			}

			args.update(language_dict)

			return self.buildTemplate_2("mobile/_promotions_filter.html", args, False, "ibersolnew")

		elif section_type == u"Localización":
			args = {
				"iframe_google_maps": get_section_from_section_spanish_name("iframe google maps", language),
				"subtitle": section.get("subtitle"),
				"content": section.get("content", ""),
				"language_code": get_language_code(language)
			}

			advance_properties = self.getSectionAdvanceProperties(section, language)
			if advance_properties.get("accordion_banner"):
				accordion_banner = get_pictures_from_section_name(advance_properties['accordion_banner'], language)
				accordion_banner_sec = get_section_from_section_spanish_name(advance_properties['accordion_banner'], language)
				accordion_dict = {
					"accordion_banner": accordion_banner,
					'accordion_banner_sec': accordion_banner_sec
				}

				accordion_html = self.buildTemplate_2("_banner_accordion.html", accordion_dict, False, "ibersolnew")
				args['content'] += accordion_html

			args.update(language_dict)

			return self.buildTemplate_2("mobile/_location.html", args, False, "ibersolnew")


	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		advance_properties = self.getSectionAdvanceProperties(section, language)
		result = "<div class='section_content'>"

		if extra_banners.get("banner_icons"):
			params = {
				"carousel_icon": deepcopy(extra_banners['banner_icons'])
			}
			for icon in params['carousel_icon']:
				aux_title = icon.get("title")
				icon['title'] = icon.get("description")
				icon['description'] = aux_title
			result += self.buildTemplate_2("mobile_templates/2/_carousel_icon.html", params, False)

		if extra_banners.get("banner_destinies"):
			params = {
				"banner_destinies": extra_banners['banner_destinies'],
				"hotels_grouped": extra_banners.get("hotels_grouped")
			}
			params.update(language_dict)
			result += self.buildTemplate_2("_banner_destinies.html", params, False, "ibersolnew")

		if extra_banners.get("banner_offers"):
			params = {
				"banner_offers": extra_banners['banner_offers'],
				"banner_offers_sec": extra_banners.get("banner_offers_sec"),
				"is_mobile": True
			}
			params.update(language_dict)
			result += self.buildTemplate_2("_banner_offers.html", params, False, "ibersolnew")

		result += "</div>"

		if extra_banners.get("minigallery") and section['sectionType'] != "Inicio":
			params = {
				'minigallery_mobile': get_pictures_from_section_name(extra_banners.get("minigallery_property"), language)
			}
			params.update(language_dict)
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", params, False)

		if extra_banners.get("events_banner"):
			params = {
				'events_banner': extra_banners.get("events_banner")
			}
			params.update(language_dict)
			result += self.buildTemplate_2("_banner_events.html", params, False, 'ibersolnew')

		if extra_banners.get("iframe_map"):
			params = {
				'iframe_map': extra_banners.get("iframe_map"),
				'price': extra_banners.get("price"),
				'price_label': extra_banners.get("price_label"),
				'hotel_link': extra_banners.get("hotel_link")
			}
			params.update(language_dict)
			result += self.buildTemplate_2("_iframe_map.html", params, False, 'ibersolnew')

		if extra_banners.get("hotels_by_destiny"):
			params = {
				'hotels_by_destiny':extra_banners.get("hotels_by_destiny"),
				'special_mobile': user_agent_is_mobile()
			}
			params.update(language_dict)
			result += self.buildTemplate_2("_hotels_by_destiny.html", params, False, "ibersolnew")

		if extra_banners.get("minigallery_footer"):
			params = {
				'minigallery_mobile': get_pictures_from_section_name(extra_banners.get("minigallery_footer_property"), language)
			}
			params.update(language_dict)
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", params, False)


		if extra_banners.get('hide_booking_engine'):
			result += '<style>.mobile_engine_action {display:none}</style>'

		if extra_banners.get('form_contact_cv'):
			if advance_properties.get('thanks_cv'):
				thanks_cv = unescape(advance_properties['thanks_cv'])
			if advance_properties.get("hotel_options"):
				hotel_form_work = unescape(advance_properties.get("hotel_options", ""))
				hotel_form_work_list = hotel_form_work.split(';')
			if advance_properties.get("destiny_options"):
				destiny_form_work = unescape(advance_properties.get("destiny_options", ""))
				destiny_form_work_list = destiny_form_work.split(';')

			params = {
				'form_contact_cv': extra_banners.get("form_contact_cv"),
				'work_form_subject': "Empleo",
				'hotel_form_work_list': hotel_form_work_list,
				'destiny_form_work_list': destiny_form_work_list,
				'thanks_cv': thanks_cv,
			}
			params.update(language_dict)

			result += self.buildTemplate_2("_form_contact_cv.html", params, False, "ibersolnew")
		result += self.buildCustomNewsletter(language, check_newsletter=True, social=True)
		return result

	def buildCustomNewsletter(self, language, name=False, social=False, check_newsletter=False, background=True,fontawesome5=False):
		template_values = dict(get_web_dictionary(language))
		template_values['language_code'] = language.upper()
		template_values['language'] = get_language_code(language)
		template_values['name_in_form'] = name
		template_values['social'] = self.getSocialDictionary() if social else False
		template_values['background'] = background
		template_values['check_newsletter'] = check_newsletter
		template_values['newsletter_banner'] = get_section_from_section_spanish_name("_newsletter_banner", language)
		template_values['newsletter_custom_check'] = get_section_from_section_spanish_name("_newsletter_custom_check", language)
		template_values['fontawesome5'] = fontawesome5

		advance_newsletter = self.getSectionAdvanceProperties(template_values['newsletter_banner'], language)

		newsletter_thanks = get_config_property_value(NEWSLETTER_POPUP_THANKS)
		if newsletter_thanks:
			popup_thanks_section = get_section_from_section_spanish_name_with_properties(newsletter_thanks, language)
			template_values['newsletter_thanks'] = popup_thanks_section

		if advance_newsletter.get("email_placeholder"):
			template_values['T_introduce_email_placeholder'] = advance_newsletter.get("email_placeholder")

		if advance_newsletter.get("button_text"):
			template_values['T_enviar'] = advance_newsletter.get("button_text")

		content = self.buildTemplate_2('_newsletter_custom.html', template_values, False, "ibersolnew")

		return content
