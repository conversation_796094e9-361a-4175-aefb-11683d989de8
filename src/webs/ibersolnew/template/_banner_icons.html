<div class="banner_icons_wrapper">
    {% if banner_icons_sec and banner_icons_sec.subtitle %}<h1>{{ banner_icons_sec.subtitle|safe }}</h1>{% endif %}
    <div class="banner_icons">
        {% for icon in banner_icons %}<div class="ico">
            <div class="icono"><i class="fa {{ icon.title|safe }}"></i><b></b></div><span>{{ icon.description|safe }}</span>
        </div>{% endfor %}
    </div>
</div>

<!--script>
$(window).load(function () {
    function banner_icons_fx() {
        $(".banner_icons_wrapper h1").addnimation({parent:$(".banner_icons_wrapper"),class:"flipInX"});
        $(".banner_icons_wrapper .banner_icons").addnimation({parent:$(".banner_icons_wrapper"),class:"bounceInUp"});
    }
    $(window).scroll(banner_icons_fx);
});
</script-->