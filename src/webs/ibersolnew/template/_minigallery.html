<div class="minigallery_wrapper{% if effect_sass %} effects_sass" sass_effect="{{ effect_sass }}"{% else %}"{% endif %}>
    <div class="minigallery_content owl-carousel">
        {% for x in minigallery %}
            <a class="slider_element {% if x.title %}with_title{% endif %}" {% if x.linkUrl %}href="{{ x.linkUrl|safe }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% else %}href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[minigallery]"{% endif %}>
                <img class="center_image" data-src="{{ x.servingUrl|safe }}" alt="{{ x.name|safe }}" lazy="true"/>
                {% if x.title %}<span>{{ x.title|safe }}</span>{% endif %}
            </a>
        {% endfor %}

    </div>
</div>
<script>
    $(window).load(function () {
       $(".minigallery_content").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 4,
            navText: ['<i class="fa fa-angle-left" aria-hidden="true"></i>', '<i class="fa fa-angle-right" aria-hidden="true"></i>'],
            margin: 10,
            autoplay: true
        });
    })
</script>