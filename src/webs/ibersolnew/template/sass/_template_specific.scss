.btn_personalize_1 {
  position: relative;
  margin-right: 15px;
  background-color: $corporate_2;
  color: white;
  letter-spacing: 1px;
  padding: 10px 15px;
  font-weight: 600;
  border-radius: 30px;
  @include transition(all, .6s);
  &:hover {
    background-color: white;
    color: $corporate_2;
  }
}

.btn_personalize_2 {
  position: relative;
  margin-right: 15px;
  background-color: white;
  color: $corporate_1;
  letter-spacing: 1px;
  padding: 10px 15px;
  font-weight: 600;
  border-radius: 30px;
  @include transition(all, .6s);
  &:hover {
    background-color: $corporate_1;
    color: white;
  }
}

.btn_personalize_3 {
  position: relative;
  margin-right: 15px;
  background-color: $corporate_1;
  color: white;
  letter-spacing: 1px;
  padding: 10px 15px;
  font-weight: 600;
  border-radius: 30px;
  @include transition(all, .6s);
  &:hover {
    background-color: white;
    color: $corporate_1;
  }
}


body {
  font-family: "Montserrat", sans-serif;
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  a {
    text-decoration: none;
  }
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  .content_subtitle_wrapper {
    position: relative;
    padding: 50px 0 0;
    .content_subtitle_title {
      position: relative;
      color: #222;
      font-size: 30px;
      font-weight: 600;
      line-height: 35px;
      text-align: center;
      text-transform: uppercase;
      padding: 0 calc((100% - 1140px) / 2);
      big {
        font-size: 36px;
        font-weight: 400;
        color: $corporate_1;
      }

      &:after {
        content: '';
        display: block;
        width: 60px;
        height: 5px;
        background: $corporate_1;
        margin: 20px auto 30px;
      }
    }

    .content_subtitle_description {
      position: relative;
      color: #222;
      padding: 50px calc((100% - 900px) / 2);
      background-image: url("/img/#{$base_web}/pattern-lineas-celestes.png");
      text-align: center;
      font-weight: 400;
      strong {
        font-weight: 600;
      }
      &.content_hotel {
        background-attachment: fixed;
        background-size: cover;
        color: white;
        padding: 50px calc((100% - 900px) / 2);
        .content {
          position: relative;
        }
        .hotel_links {
          position: relative;
          padding-top: 50px;
          text-align: center;
          a {
            position: relative;
            display: inline-block;
            overflow: hidden;
            padding: 15px 25px;
            border-radius: 30px;
            background-color: $corporate_1;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 5px;
            &:hover {
              i.fa {
                &:after {
                  z-index: 2;
                  width: 100px;
                  height: 100px;
                  opacity: 0;
                }
              }
              &:after {
                opacity: .5;
              }
            }
            &:after {
              content: '';
              @include full_size;
              background-color: rgba(white,.3);
              opacity: 0;
              @include transition(opacity,1.6s);
            }
            i.fa {
              position: relative;
              vertical-align: middle;
              font-size: 20px;
              margin-right: 15px;
              &:after {
                content: '';
                @include center_xy;
                z-index: -1;
                border: 1px solid white;
                width: 0;
                height: 0;
                border-radius: 50%;
                @include transition(all, 1.6s);
              }
            }
            &:nth-child(2) {
              background-color: white;
              color: $corporate_2;
              &:after {
                border: 1px solid $corporate_2;
              }
              i.fa {
                font-size: 16px;
                &:after {
                  border: 1px solid $corporate_2;
                }
              }
            }
          }
        }
        &:before {
          content: '';
          @include full_size;
          background: rgba($corporate_2,0.6);
        }
      }
    }

    .accordion_banner_wrapper {

    }
  }

  .content_access {
    margin-top: 30px;

    .section-title {
      position: relative;
      color: #222;
      font-size: 30px;
      font-weight: 600;
      line-height: 35px;
      text-align: center;
      text-transform: uppercase;

      big {
        font-size: 36px;
        font-weight: 400;
        color: $corporate_1;
      }

      &:after {
        content: '';
        display: block;
        width: 60px;
        height: 5px;
        background: $corporate_1;
        margin: 20px auto 50px;
      }
    }

    .container12 > div {
      color: #222;
      padding: 0 100px;
      text-align: center;
        font-weight: 400;
    }

    #my-bookings-form {
      margin: 80px auto;

      #reservation {
        margin-top: 0 !important;

        .modify_reservation_widget {
          margin: auto;
          margin-top: 40px;
          margin-bottom: 0;
        }

        .my-bookings-booking-info {
          margin: 40px auto 0;

          .fResumenReserva {
            margin: auto;
          }
        }
      }

      #modify-button-container {
        display: none;
      }

      #my-bookings-form-fields {
        label {
          display: block;
          text-align: center;
          text-transform: uppercase;
          font-weight: 100;
          &#my-bookings-hotel-label {
            position: relative;
            width: 300px;
            margin: auto;
            @extend .fa-angle-down;
            &:before {
              @extend .fa;
              position: absolute;
              top: 40px;
              right: 10px;
              color: $corporate_1;
            }
          }
        }

        select {
          position: relative;
          z-index: 3;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          display: block;
          width: 300px;
          margin: 10px auto;
          padding: 0 15px;
          height: 40px;
          text-align: center;
          font-size: 14px;
          background-color: transparent;
          border-radius: 0;
          border: 1px solid $corporate_1;
          &:lang(ru) {
            font-size: 12px;
          }
        }

        input {
          display: block;
          width: 300px;
          margin: 10px auto;
          height: 40px;
          text-align: center;
          font-size: 14px;
          border: 1px solid $corporate_1;
        }

        ul {
          text-align: center;
          margin-top: 30px;

          li {
            display: inline-block;
            width: 200px;
            vertical-align: middle;

            button, a {
              height: 40px;
              text-transform: uppercase;
              font-size: 16px;
              color: white;
              font-family: "Roboto Condensed", sans-serif;
              border: 0;
              cursor: pointer;
              width: 100%;
              font-weight: 100;
              @include transition(background, .4s);

              &.custom-button {
                display: block;
                padding: 0.7em 0.6em;
                background: $corporate_5;
                height: 40px;
                text-transform: uppercase;
                font-size: 16px;
                color: white;
                border: 0;
                cursor: pointer;
                width: 200px;
                font-weight: 100;
                @include transition(background, .4s);

                &:hover {
                  background: $corporate_1;
                }
              }

              &.cancelButton {
                background: $corporate_2;
                height: 40px;
                text-transform: uppercase;
                font-size: 16px;
                color: white;
                border: 0;
                cursor: pointer;
                width: 200px;
                font-weight: 100;
                @include transition(background, .4s);

                &:hover {
                  background: darken($corporate_2, 10%);
                }
              }

              &.modify-reservation {
                background: $corporate_1;

                &:hover {
                  background: darken($corporate_1, 20%);
                }
              }

              &.searchForReservation {
                background: $corporate_2;

                &:hover {
                  background: darken($corporate_2, 20%);
                }
              }
            }
          }
        }
      }

      #cancelButton {
        display: none;
        background: $corporate_2;
        height: 40px;
        text-transform: uppercase;
        font-size: 16px;
        color: white;
        border: 0;
        cursor: pointer;
        width: 200px;
        font-weight: 100;
        margin: 40px auto 0;
        @include transition(background, .4s);

        &:hover {
          background: darken($corporate_2, 20%);
        }
      }
    }
  }
}


.click_to_call_popup_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 1001;
  display: none;

  .close_click_to_call {
    position: absolute;
    top: 10px;
    right: 10px;
    color: $corporate_1;
    cursor: pointer;
    @include transition(color, .4s);

    &:hover {
      color: $corporate_2;
    }
  }

  .click_to_call_content {
    text-align: center;

    .click_to_call_title {
      font-size: 30px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #222;
    }

    .click_to_call_description {
      width: 600px;
      margin: 0 auto 20px;
      font-size: 13px;
      line-height: 20px;
      color: #222;
    }

    .click_to_call_form {
      .click_to_call_input {
        width: 400px;
        border: 2px solid #222;
        padding: 10px;
        font-size: 16px;
        text-align: center;

        &.error_class {
          border-color: red;
        }

        &.valid {
          border-color: #222;
        }
      }

      .click_to_call_button {
        width: 400px;
        margin: auto;
        background-color: #222;
        border: 2px solid #222;
        color: white;
        text-transform: uppercase;
        font-size: 16px;
        font-weight: 600;
        padding: 10px;
        margin-bottom: 20px;
        cursor: pointer;
        display: block;
        @include transition(opacity, .4s);

        &:hover {
          opacity: .8;
        }
      }

      .check_click_wrapper {
        .check_element {
          a {
            color: #444;
            font-size: 12px;
          }
        }
      }
    }
  }
}

#dialoga-animate-widget {
  display: none!important;
}