.contact_form_wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  padding: 80px 0;
  background-color: $corporate_4;
  background-image: url("/img/#{$base_web}/pattern-lineas-blancas.png");
  overflow: hidden;
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    max-height: 500px;
    background: $corporate_4;
  }

  h3 {
    position: relative;
    color: #222;
    font-size: 30px;
    font-weight: 600;
    line-height: 35px;
    text-align: center;
    text-transform: uppercase;

    &:after {
      content: '';
      display: block;
      width: 60px;
      height: 5px;
      background: $corporate_1;
      margin: 20px auto 50px;
    }
  }

  #contact {
    display: table;
    width: 980px;
    margin: auto;

    .top_form {
      text-align: right;
      display: table;
      width: 100%;
      font-size: 14px;
      padding: 0 10px;
      color: #4B4B4B;
      .selector_hotel {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        float: left;
        padding: 0;
        margin: 10px;
        width: 250px;
        background-color: white;

        @extend .fa-angle-down;
        &:before {
          font-family: "Fontawesome", sans-serif;
          color: $corporate_2;
          @include center_y;
          right: 10px;
        }
        select {
          position: relative;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background: transparent;
          border: 1px solid transparent;
          border-radius: 0;
          box-shadow: 0 0 0 rgba(0, 0, 0, 0);
          padding: 7px;
          margin: 0;
          width: 100%;
          font-size: 14px;
        }
      }
      span {
        display: inline-block;
        vertical-align: middle;
        padding: 17px 10px 17px 0;
      }
      input[type=checkbox] {
        display: inline-block;
        vertical-align: middle;
        width: auto;
        height: auto;
      }
    }
    label {
      padding: 15px 0 0;
      display: block;
      font-size: 14px;
      color: lighten($corporate_2, 30%);
      &.error {
        position: absolute;
        bottom: -20px;
        padding: 5px 10px;
        color: #943E46;
        background-color: #f8d7da;
        border-color: #f5c6cb;
        border-radius: 5px;
        z-index: 2;
      }
      &[for=promotions] {
        display: inline-block;
        vertical-align: middle;
        padding: 0;
        color: #999;
      }
    }
    .contInput {
      display: inline-block;
      float: left;
      width: 100%;
      padding: 10px 0 10px 20px;
      position: relative;

      &:nth-of-type(-n+2) {
        width: calc((100% - 20px) / 2);
        padding-top: 20px;
      }

      &:nth-of-type(3), &:nth-of-type(4), &:nth-of-type(5) {
        width: calc((100% - 20px) / 3);
        .fa {
          top: 15px;
        }
      }

      &:nth-of-type(6) {
        .fa {
          top: 15px;
        }
      }

      &:nth-of-type(3), &:nth-of-type(5), &:nth-of-type(6) {
        margin-right: 0;
      }

      &:nth-of-type(6), {
        padding: 0 20px 20px;
        input[type=file] {
          position: absolute;
          left: 20px;
          right: 20px;
          top: 5px;
          padding-top: 10px;
          padding-left: 400px;
        }
        .fa {
          top: 5px;
        }
      }

      .fa {
        width: 40px;
        height: 40px;
        color: $corporate_1;
        position: absolute;
        top: 25px;
        left: 20px;

        &:before {
          @include center_xy;
        }
      }

      input {
        width: 100%;
        height: 50px;
        padding-left: 40px;
        border: 0;

        &#accept-term, &#promotions {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }
      }

      textarea {
        width: 100%;
        padding-left: 40px;
        padding-top: 20px;
        border-color: transparent;
        margin-top: 10px;
      }
      &.captcha {
        margin-left: 330px;
      }
      &.sup_checkboxes {
        vertical-align: middle;
        padding-top: 0;
      }
    }
    .policy-terms {
      text-align: center;
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: #999;
    }

    #contact-button {
      display: block;
      margin: auto;
      width: calc(100% - 40px);
      background: $corporate_1;
      color: white;
      padding: 20px 0;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 10px;
      cursor: pointer;
      &:hover {
        background-color: darken($corporate_1, 10%);
      }
    }
  }
}

.iframe_maps_wrapper {
  position: relative;
  .overlay {
    content: '';
    @include full_size();
    background-color: rgba(0,0,0,.3);
  }
  iframe {
    width: 100%;
    margin: auto;
    display: block;
  }
}