//Base web (change too in templateHandler and in config.rb)
$is_mobile: false !default;
$base_web: "iberw";
@import url('https://fonts.googleapis.com/css?family=Montserrat:300,400,600,700|Playfair+Display:400,700');
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);
$lightgrey: #F5F5F5;

// corporative colors definitions
$corporate_1: #4CCAFF;
$corporate_2: #003777;
$corporate_3: #B5ECFF;
$corporate_4: #F5F5F5;
$corporate_5: #00a1e5;

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

@mixin base_banner_styles() {
  position: relative;
  padding: 70px calc((100% - 1140px) / 2);
  overflow: hidden;
  width: 100%;
}

@mixin banner_title_styles($color: white, $fw: 400, $fz: 30px, $lh: 34px, $ls: 0) {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 30px;
  line-height: 34px;
  letter-spacing: 0;
  color: $color;
  display: block;
  margin: 0;

  @media (max-width: 575px) {
    font-size: 23px;
    line-height: 26px;
  }
}

@mixin text_styles($color: $black, $fw: 300, $fz: 17px, $ls: 1px, $lh: 20px) {
  font-family: "Montserrat", sans-serif;
  font-weight: $fw;
  font-size: $fz;
  color: $color;
  letter-spacing: $ls;
  line-height: $lh;
}

@mixin btn_styles_2() {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
  justify-content: center;
  align-items: center;
  background-color: $corporate-1;
  color: white;
  font-size: 17px;
  line-height: 20px;
  font-weight: 300;
  letter-spacing: 1.3px;
  text-align: center;
  padding: 15px 25px;
  font-family: "Montserrat", sans-serif;
  text-transform: uppercase;
  border: none;
  margin: 0;
  width: auto;
  cursor: pointer;
  @include transition(all, .6s);

  @media (max-width: 575px) {
    font-size: 14px;
    line-height: 18px;
  }

  &.white {
    color: white;

    &:before {
      color: white;
    }
  }

  &:hover {
    background: $corporate-2;
  }
}

@mixin title_styles($color: $corporate_2, $fz: 148px, $fw: 400, $lh: 119px, $ls: 0, $tt: uppercase) {
  font-family: "Montserrat", sans-serif;
  font-size: $fz;
  font-weight: $fw;
  line-height: $lh;
  letter-spacing: $ls;
  color: $color;
  text-transform: $tt;
  display: block;
  margin: 0;

  @media (max-width: 575px) {
    font-size: 44px !important;
    line-height: 50px !important;
  }
}

