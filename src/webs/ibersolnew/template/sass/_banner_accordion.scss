.accordion_banner_wrapper {
  display: table;
  width: 100%;
  padding: 70px calc((100% - 1140px) / 2);
  .accordion {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 380px);
    .accordion_element {
      margin-bottom: 10px;
      &:last-of-type {
        .accordion_title {
          background-color: $corporate_2;
        }
      }
    }
    .accordion_title {
      position: relative;
      display: block;
      padding: 10px;
      background-color: $corporate_1;
      text-transform: uppercase;
      font-weight: 600;
      color: white;
      i.fa {
        margin-right: 10px;
      }
      @extend .fa-angle-down;
      &:before {
        @extend .fa;
        @include center_y;
        right: 10px;
      }
    }
    .accordion_description {
      background-color: $corporate_4;
      padding: 10px;
      margin: 5px 0;
      font-size: 14px;
      display: none;
      strong {
        font-weight: 600;
      }
      a {
        color: $corporate_1;
      }
    }
  }
  .side_text {
    background-color: $corporate_4;
    padding: 17px 20px;
    width: 370px;
    float: right;
    line-height: 20px;
    font-size: 14px;
    font-weight: 400;
    strong {
      font-weight: 600;
    }
  }
}