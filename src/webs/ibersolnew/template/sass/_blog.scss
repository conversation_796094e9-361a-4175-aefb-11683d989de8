.banner_blog_wrapper {
  @include base_banner_styles;
  padding-top: 0;
  position: relative;
  
  .container12 {
    position: relative;
    z-index: 1;
    
    .top_banner {
      text-align: right;
      margin-bottom: 20px;
      
      #social {
        display: inline-flex;
        flex-wrap: nowrap;
        
        a {
          margin-right: 20px;
          padding: 5px 0;
          
          i {
            font-size: 40px;
            color: $corporate_2;
            transform: rotate(0) translate(0);
            transition: all .4s;
          }
          
          &:hover {
            i {
              color: $corporate_1;
              transform: rotate(-5deg) translate(-5px, 0);
            }
          }
        }
      }
    }
    
    .cards_wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: auto;
      grid-gap: 40px;
      
      .card {
        position: relative;
        overflow: hidden;
        background-color: white;
        border: solid 2px $lightgrey;
        
        .picture_wrapper {
          display: block;
          height: 200px;
          
          img {
            @include cover_image;
          }
        }
        
        .content_wrapper {
          display: block;
          overflow: hidden;
          text-align: center;
          padding: 30px 30px 100px;
          
          .content_title {
            min-height: 55px;
            margin-bottom: 10px;
            
            .title {
              @include banner_title_styles;
              color: $corporate_1;
              @include ellipsis(2);
            }
          }
          
          .desc {
            @include text_styles;
            @include ellipsis(3);

            hr {
              display: none;
            }
          }
        }
        
        .links_wrapper {
          position: absolute;
          bottom: 30px;
          left: 0;
          right: 0;
          text-align: center;
          
          a {
            @include btn_styles_2;
          }
        }
      }
    }
  }
}


.blog_entry_wrapper {
  @include base_banner_styles;
  
  .content_title {
    width: 100%;
    margin-bottom: 30px;
    padding-bottom: 28px;
    
    .title {
      @include title_styles($corporate_1);
      font-size: 60px;
      line-height: 66px;
      text-align: left;
      padding-bottom: 1px;
    }
    
    span {
      font-size: $description_size;
      text-transform: uppercase;
      color: $gray-1;
    }
  }
  
  .entry_body {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    
    .content_wrapper {
      width: 66%;
      @include text_styles;
      margin-right: 40px;
      
      .entry_tags {
        margin-top: 20px;
        font-size: .8em;
        font-weight: 100;
        font-style: italic;
      }
      
      .entry_share {
        margin-top: 40px;
        padding-bottom: 40px;
        border-bottom: 1px solid $corporate_1;
        
        .title {
          text-transform: uppercase;
          margin-bottom: 10px;
        }
        
        .at-style-responsive .at-share-btn {
          padding: 5px 25px;
          border-radius: 0 !important;
        }
      }
      
      .entry_comments {
        margin-top: 20px;
        
        h3 {
          text-transform: uppercase;
          text-align: left;
          font-size: 13px;
          letter-spacing: 1px;
          padding: 0;
          margin: 0;
          color: #BEBEBE;
          font-weight: 300;
        }
        
        .comment_list {
          
          .comment {
            background-color: #fafafa;
            border-radius: 3px;
            min-height: 70px;
            margin: 10px;
            padding: 20px;
            
            .comment_pic {
              position: relative;
              display: inline-block;
              vertical-align: middle;
              width: 70px;
              height: 70px;
              float: left;
              margin-right: 20px;
              margin-bottom: 20px;
              border-radius: 50%;
              overflow: hidden;
              
              img {
                @include center_xy;
              }
            }
            
            .name {
              margin-top: 20px;
              margin-bottom: 10px;
              
              span {
                text-transform: uppercase;
                color: #CCC;
                margin-right: 5px;
              }
            }
            
            .text {
              font-size: 80%;
            }
          }
        }
        
        .comment_form {
          .text_element {
            width: 100%;
            
            textarea {
              padding: 10px;
              display: block;
              width: 100%;
              border: 1px solid #707070;
              
              &::placeholder {
                color: #BEBEBE;
              }
            }
          }
          
          .input_element {
            display: block;
            width: 100%;
            margin-top: 15px;
            margin-right: 5px;
            
            input {
              padding: 10px;
              box-sizing: border-box;
              border: 1px solid #707070;
              width: calc(100% - 5px);
              
              &::placeholder {
                color: #BEBEBE;
              }
            }
          }
          
          .check_element {
            margin-top: 15px;
            
            a {
              color: $corporate_1;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
          
          #contact-button-wrapper {
            margin-top: 30px;
            
            #popup_form_button {
              @include btn_styles_2;
            }
          }
          
          label {
            display: block;
            text-transform: uppercase;
          }
        }
      }
    }
    
    .entry_sidebar {
      flex: 1;
      
      .block_news {
        margin-bottom: 40px;
        text-align: center;
        
        .content_title {
          text-align: left;
          margin-bottom: 20px;
          padding-bottom: 0;
          
          .title {
            font-family: "Montserrat", sans-serif;
            color: $corporate_1;
            font-size: 20px;
            color: $black;
          }
          
          &::before {
            left: 0;
            bottom: 0;
            transform: none;
          }
        }
        
        .new_item {
          text-align: left;
          position: relative;
          height: 220px;
          margin-bottom: 20px;
          overflow: hidden;
          
          &::before {
            position: absolute;
            content: '';
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgb(0, 0, 0);
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
            opacity: .5;
          }
          
          .content_title {
            position: absolute;
            top: auto;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1;
            padding: 0 20px 20px;
            margin-bottom: 0;
            
            .title {
              padding: 0;
              @include banner_title_styles;
              color: white;
              margin-bottom: 0;
              @include ellipsis(2);
            }
            
            span {
              font-family: "Montserrat", sans-serif;
              color: white;
              font-size: 12px;
              text-transform: uppercase;
            }
          }
          
          img {
            @include cover_image;
          }
        }
      }
    }
  }
}
