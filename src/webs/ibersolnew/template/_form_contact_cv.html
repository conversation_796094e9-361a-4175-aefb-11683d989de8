<div class="contact_form_wrapper work_form effects_sass" sass_effect="slide_up_effect">
    <div class="container12">
        <div class="content_subtitle_wrapper">
            {% if form_contact_cv %}
            <h3 class="content_subtitle_title">{{ form_contact_cv|safe }}</h3>
            {% else %}
            <h3 class="content_subtitle_title">{{ T_formulario_contacto }}</h3>
            {% endif %}
        </div>

        <form name="contact" id="contact" method="post" action="/utils/?action=work_with_us">

            <input type="hidden" name="action" id="action" value="contact"/>
            <input type="hidden" name="emailSubjectCustomized" id="emailSubjectCustomized" value="{{ work_form_subject }}"/>

            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="surname" name="surname" class="bordeInput" value=""
                           placeholder="{{ T_apellidos }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-phone" aria-hidden="true"></i>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                           placeholder="{{ T_telefono }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-envelope-o" aria-hidden="true"></i>
                    <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
                </div>
                <div class="contInput selector_type_option">
                    <i class="fa fa-map" aria-hidden="true"></i>
                    <select class="selector_type" name="type_option" id="type_option">
                        <option value="Type-option" disabled selected>{{ T_seleccionar }}</option>
                        <option value="Hotel">{{ T_hotel }}</option>
                        <option value="Destino">{{ T_destino }}</option>
                    </select>
                </div>
                <div class="contInput selector_type_active">
                    <i class="fa fa-map-pin" aria-hidden="true"></i>
                     <select class="hotel_destiny" name="hotel_destiny" id="hotel_destiny" disabled="true">
                        <option value="Hotel-Destiny" disabled selected>{{ T_seleccionar }}</option>
                        {% for hotel in hotel_form_work_list %}
                        <option class="hotel" value="{{ hotel|safe }}">{{ hotel|safe }}</option>
                        {% endfor %}
                        {% for destiny in destiny_form_work_list %}
                        <option class="destiny" value="{{ destiny|safe }}">{{ destiny|safe }}</option>
                        {% endfor %}
                    </select>
                </div>
                 <div class="contInput selector_job">
                    <i class="fa fa-briefcase" aria-hidden="true"></i>
                     <input type="text" id="job" name="job" class="bordeInput job" value=""
                           placeholder="{{  T_que_departamento }}"/>
                </div>
                <div class="contInput area">
                    <i class="fa fa-comment" aria-hidden="true"></i>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""
                              placeholder="{{ T_comentarios }}"></textarea>
                </div>
                <div class="contInput area">
                    <i class="fa fa-file-text-o " aria-hidden="true"></i>
                    <input id="uploadFile" placeholder="{{ T_adjunta_cv }} (Max. 3Mb)" disabled="disabled"/>
                    <input type="file" id="file_cv" name="file" value=""/>
                    <input type="hidden" id="url_4_upload" name="url_4_upload" value=""/>
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
                {% if captcha_box %}
                    <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    <script src='https://www.google.com/recaptcha/api.js'></script>
                {% endif %}
                <div class="contInput policy-terms">
                    <input type="checkbox" id="accept-term" name="accept_term"/>
                    <a class="myFancyPopup fancybox.iframe" data-fancybox data-type="iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
            </div>
        </form>
    </div>
</div>

{% if thanks_cv %}
    <div id="thanks_cv_poup" style="display: none;">
        {{ thanks_cv|safe }}
    </div>
{% endif %}

<script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>

<script type="text/javascript">

   $('#type_option').change(function(){
     let option_selected = $(this).val();

     if(option_selected == 'Hotel'){
        $('option.destiny').removeClass('active');
        $('option.hotel').addClass('active');
        $('#hotel_destiny').val('Hotel-Destiny');
        $('#hotel_destiny').prop("disabled", false);
     } else if(option_selected == 'Destino'){
       $('option.hotel').removeClass('active');
       $('option.destiny').addClass('active');
       $('#hotel_destiny').val('Hotel-Destiny');
       $('#hotel_destiny').prop("disabled", false);
     } else{
       $('option.hotel').removeClass('active');
       $('option.destiny').removeClass('active');
       $('#hotel_destiny').val('Hotel-Destiny');
       $('#hotel_destiny').prop("disabled", true);
     }
   });



</script>
<script type="text/javascript">
    $(window).load(function () {
        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");

        $("#contact").validate({
            rules: {
                hotel: "required",
                name: "required",
                surname: "required",
                type_option: "required",
                hotel_destiny: "required",
                job: "required",
                email: {
                    required: true,
                    email: true
                },
                telephone: {
                    required: true,
                    phone: true
                },
                comments: "required",
                accept_term: "required",
            },
            messages: {
                hotel: "{{ T_campo_obligatorio}}",
                name: "{{ T_campo_obligatorio}}",
                surname: "{{ T_campo_obligatorio}}",
                type_option: "{{ T_campo_obligatorio}}",
                hotel_destiny: "{{ T_campo_obligatorio}}",
                job: "{{ T_campo_obligatorio}}",
                email: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                emailConfirmation: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}",
                    equalTo: "{{T_not_confirmed_email_warning|safe}}"
                },
                telephone: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    phone: "{{ T_campo_valor_invalido|safe }}"
                },
                comments: "{{ T_campo_obligatorio|safe }}",
                accept_term: "{{ T_campo_obligatorio|safe }}",
            }
        });

        $("#contact-button").click(function () {
            if ($(this).attr('disabled')) {
                return;
            }

            $(this).attr('disabled', 'disabled');


            var data = {
                'name': $("#name").val(),
                'surname': $("#surname").val(),
                'telephone': $("#telephone").val(),
                'vacant_posts': $("#job").val(),
                'email': $("#email").val(),
                'comments': "{{ T_destino }}/{{ T_hotel }}: " + $("#hotel_destiny").val() + " - " + $("#comments").val(),
                'emailSubjectCustomized': $("#hotel_destiny").val() + ": " + $("#job").val() + " - " + $("#name").val() + " " + $("#surname").val(),
                'g-recaptcha-response': $("#g-recaptcha-response").val()
            };

            var url_for_upload = "",
                url_cv_download = "",
                confirma_no_cv = 1,
                send_email = 1;

            $.ajax({
                url: "/get_upload_url",
                type: 'GET',
                async: false,
                cache: false,
                success: function (returndata) {
                    url_for_upload = returndata;
                },
                error: function () {
                    $("#contact-button").removeAttr('disabled');
                }
            });

            if ($(this).closest("form").find("#file_cv").length && url_for_upload && $(this).closest("form").find("#file_cv").val()) {
                confirma_no_cv = 0;
                var formData = new FormData($(this).closest("form")[0]);
                $.ajax({
                    url: url_for_upload,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        url_cv_download = returndata;
                    },
                    error: function () {
                        $("#contact-button").removeAttr('disabled');
                    }
                });
            }
            if (!confirma_no_cv && (!url_cv_download || url_cv_download == "NO_FILE_UPLOAD")) {
                alert("ERROR UPLOADING CURRICULUM VITAE");
                $("#contact-button").removeAttr('disabled');
                window.location.reload();
                return false;
            }

            if (confirma_no_cv) {
                if (!confirm($.i18n._("confirm_no_cv"))) {
                    send_email = 0;
                    $(this).removeAttr('disabled');
                }
            }

            data['url_file_download'] = url_cv_download;

            if ($("#contact").valid() && send_email) {
                if ($("#g-recaptcha-response").val() || !$("#g-recaptcha-response").length) {
                    $.post(
                        "/utils/?action=work_with_us", data,
                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                            $("#name").val("");
                            $("#surname").val("");
                            $("#telephone").val("");
                            $("#email").val("");
                            $("#type_option").val("Type-option");
                            $("#hotel_destiny").val("Hotel-Destiny");
                            $('#hotel_destiny').prop("disabled", true);
                            $('.selector_job .job').val("");
                            $("#emailConfirmation").val("");
                            $("#comments").val("");
                            $("#file_cv").val("");
                            $("#contact-button").removeAttr('disabled');
                            $('#accept-term').prop("checked", false);
                        }
                    );

                } else {
                    $("#contact-button").removeAttr('disabled');
                    $(".g-recaptcha > div > div").css('border', '1px solid red');
                }

            }
        });
    });
</script>