.banner_offers_full_wrapper {
  padding: $mobile_padding;
  .banner_offer_wrapper {
    .banner_offer {
      align-items: center;
      padding: $mobile_padding;
      .banner_offer_content {
        display: block;
        background-color: $grey;
        padding: $mobile_padding;
        .title {
          @include title_styles;
          &:lang(ru) {
            font-size: 21px;
          }
        }
        .text {
          @include text_styles;
          margin-bottom: 20px;
        }
      }
      .img_wrapper {
        position: relative;
        height: 300px;
        overflow: hidden;
        img {
          @include center_image;
          width: auto;
        }
      }
    }
    .owl-item.active {
      z-index: 5;
      .banner_offer_content, .img_wrapper {
        @include border_radius;
      }
      .img_wrapper {
        @include box_shadow;
      }
    }
    .owl-nav {
      @include center_x;
      bottom: 20px;
      .owl-prev, .owl-next {
        @include owl_nav_styles;
      }
      .owl-next {
        margin-left: 10px;
      }
    }
  }
}