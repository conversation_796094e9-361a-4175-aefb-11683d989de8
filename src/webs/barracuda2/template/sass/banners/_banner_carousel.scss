.banner_carousel_full_wrapper {
  @include banner_wrapper_styles;
  overflow: visible;
  padding: 90px 0;
  @include display_flex;
  flex-direction: row-reverse;
  .svg_logo {
    position: absolute;
    top: -50px;
    right: 0;
    z-index: -1;
    height: 90%;
    * {
      fill: $svg_color_black;
    }
  }
  .banner_carousel_wrapper {
    display: inline-block;
    width: 60%;
    .owl-stage-outer {
      padding: 0 30px 40px 0;
      .owl-stage {
        @include display_flex;
        -webkit-flex-wrap: nowrap;
        -moz-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
        -o-flex-wrap: nowrap;
        flex-wrap: nowrap;
      }
    }
    .banner {
      width: 100%;
      height: 100%;
      @include border_radius;
      @include box_shadow;
      box-shadow: 3px 15px 35px 0px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      .img_wrapper {
        position: relative;
        width: 100%;
        height: 310px;
        overflow: hidden;
        img {
          @include center_image;
          width: auto;
        }
      }

      .banner_content {
        padding: 20px 20px 110px;
        text-align: center;
        background-color: white;
        .title {
          @include banner_block_title;
        }
        .text {
          @include text_styles;
          padding: 20px;
        }
        .icon_link {
          @include center_x;
          bottom:30px;
        }
      }
      &.first_banner {
        display: none;
      }
    }

    .owl-item {
      opacity: 0;
      visibility: hidden;
      @include transition(all, .6s);
      &.active {
        margin-left: -120px;
        margin-right: 170px;
        opacity: 1;
        visibility: visible;
      }
    }

    .owl-nav {
      position: absolute;
      bottom: -20px;
      right: 115px;
      &.disabled {
        display: block;
      }
      .owl-prev, .owl-next {
        @include owl_nav_styles;
        margin: 0 10px;
        &.disabled i {
          color: grey;
        }
      }
    }
  }
  .banner_carousel_content {
    display: inline-block;
    margin-top:130px;
    width: 40%;
    .content_title {
      @include title_styles;
    }
    .content_text {
      @include text_styles;
      padding-right: 150px;
    }
  }
}