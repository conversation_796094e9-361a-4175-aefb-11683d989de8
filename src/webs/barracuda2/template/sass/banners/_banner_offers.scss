.banner_offers_full_wrapper {
  @include banner_wrapper_styles;
  padding: 60px 0;
  .banner_offer_wrapper {
    .banner_offer {
      @include display_flex;
      align-items: center;
      padding-bottom: 30px;
      .banner_offer_content {
        display: inline-block;
        min-height: 480px;
        width: 50%;
        background-color: $grey;
        padding: 50px 220px 90px calc((100% - 1140px) / 2);
        .banner_content {
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 10px);
        }
        &:before {
          content: '';
          display: inline-block;
          vertical-align: middle;
          height: 100%;
          min-height: 340px;
          width: 1px;
          background: transparent;
        }
        .title {
          @include title_styles;
        }
        .text {
          @include text_styles;
          margin-bottom: 40px;
        }
      }
      .img_wrapper {
        position: relative;
        display: inline-block;
        width: calc(50% + 130px);
        height: 500px;
        margin-left: -130px;
        overflow: hidden;
        img {
          @include center_image;
          width: auto;
        }
      }
    }
    .owl-item.active {
      .banner_offer_content, .img_wrapper {
        @include border_radius;
      }
      .img_wrapper {
        @include box_shadow;
        box-shadow: 5px 15px 35px 0px rgba(0, 0, 0, 0.3);
      }
    }
    .owl-nav {
      position: absolute;
      bottom: 20px;
      right: calc((100% - 1140px) / 2);
      .owl-prev, .owl-next {
        @include owl_nav_styles;
      }
      .owl-next {
        margin-left: 30px;
      }
    }
    &:nth-of-type(odd) {
      .owl-item.active {
        .banner_offer_content {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
        }
        .img_wrapper {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
        }
      }
    }
    &:nth-of-type(even) {
      .owl-nav {
        right: auto;
        left: calc((100% - 1140px) / 2);
      }
      .owl-item.active {
        .img_wrapper {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
        }
        .banner_offer_content {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
        }
      }
      .banner_offer {
        flex-direction: row-reverse;
        .banner_offer_content {
          padding: 50px calc((100% - 1140px) / 2) 90px 220px;
        }
        .img_wrapper {
          margin-left: 0;
          margin-right: -130px;
        }
      }
    }
    &:not(:last-of-type) {
      padding-bottom: 60px;
      .owl-nav {
        bottom: 90px;
      }
    }
  }
}