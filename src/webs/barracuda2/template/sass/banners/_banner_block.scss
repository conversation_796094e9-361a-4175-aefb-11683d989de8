.banner_block_full_wrapper {
  @include banner_wrapper_styles;
  padding: 60px calc((100% - 1140px) / 2) 40px;
  .banner_block_content {
    text-align: center;
    .title {
      @include title_styles;
    }
    .text {
      @include text_styles;
    }
  }
  .banner_block_wrapper {
    @include display_flex;
    justify-content: center;
    .banner {
      display: inline-block;
      width: calc((100% / 3) - 20px);
      @include box_shadow;
      box-shadow: 3px 15px 35px 0px rgba(0, 0, 0, 0.3);
      @include border_radius;
      overflow: hidden;
      margin-bottom: 30px;
      .img_wrapper {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .banner_content {
        padding: 20px;
        text-align: center;
        background-color: white;
        .title {
          @include banner_block_title;
        }
        .text {
          @include text_styles;
          padding: 20px;
          padding-bottom: 90px;
        }
        a.icon_link {
          position: absolute;
          bottom: 10px;
          @include center_x;
        }
      }
      &:not(:nth-of-type(3n)):not(:last-of-type) {
        margin-right: 30px;
      }
    }
  }
}