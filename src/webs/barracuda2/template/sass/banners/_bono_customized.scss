@import "plugins/mixins";

.wrapper_gift_bono {
  max-width: 1140px;
  margin: auto;
  text-align: left;
  padding: 70px 0;

  .bono_title {
    margin-bottom: 30px;

    .title {
      display: block;
      font-weight: 700;
      font-size: 40px;
      color: $corporate_2;
    }

    .subtitle {
      display: block;
      font-weight: 400;
      font-size: 30px;
      color: $corporate_1;
    }
  }
}

.gift_bono_content {
  @include display_flex();
  justify-content: space-between;
  align-items: flex-start;


  .default_text {
    font-size: 16px;
    padding-bottom: 40px;
    line-height: 25px;
  }

  .half_content_wrapper {
    width: calc((100% - 100px) / 2);

    //&:nth-of-type(2) {
    //  padding-top: 70px;
    //}

    .prices_top_text {
      font-weight: 700;
      font-size: 26px;
      margin-bottom: 20px;
      color: $corporate_1;
    }

    .currency_selector_wrapper {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 10px 0 20px;

      .select2-container--default {
        width: auto !important;

        .select2-selection--single {
          border-radius: 0;
          outline: none;

          .select2-selection__rendered {
            padding-left: 20px;
            padding-right: 35px;
            font-weight: 600;
          }

          .select2-selection__arrow {
            top: 2px;
            right: 10px;

            &:before {
              content: "\f078";
              font-family: "Font Awesome 5 Pro";
              @include center_xy;
              font-weight: 300;
              color: #333;
              font-size: 17px;
            }

            b {
              display: none;
            }
          }
        }

        .select2-search--dropdown .select2-search__field {
          border: none;
          border-bottom: 2px solid #333;
        }

        &.select2-container--open .select2-selection--single .select2-selection__arrow {
          transform: rotateX(180deg);
        }
      }
    }

    .table_prices_wrapper {
      margin-bottom: 30px;

      .table_prices {
        display: grid;
        grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);

        .price_cell {
          cursor: pointer;
          padding: 20px;
          text-align: center;
          font-size: 22px;
          font-weight: bold;

          @media (min-width: 320px) and (max-width: 410px) {
            padding: 20px 15px;
          }

          &.other {
            background-color: $corporate_2;
            font-size: 20px !important;

            @media (min-width: 320px) and (max-width: 410px) {
              padding: 20px 0;
            }
          }
        }
      }
    }

    .discount_text {
      margin-bottom: 30px;
      text-align: center;
      font-style: italic;
      font-size: 18px;
    }

    .button_bono {
      display: block;
      text-align: center;

      .buy_bono {
        cursor: pointer;
        background: $corporate_2;
        border-radius: 50px;
        box-shadow: 0px 3px 6px #00000029;
        color: white;
        font-size: 16px;
        appearance: none;
        border: none;
        padding: 10px 45px;
        text-transform: uppercase;
        font-weight: 600;

        &:hover {
          color: white;
        }

        &.disabled {
          background: $corporate_2;
        }

      }
    }

    .custom_price_wrapper {
      margin-bottom: 30px;

      .message {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 0.5px;
      }

      .custom_input_wrapper {
        display: inline-block;
        vertical-align: middle;
        position: relative;

        .input_price_custom {
          box-sizing: border-box;
          appearance: none;
          border: none;
          background-color: $grey;
          width: 85px;
          padding: 10px 20px 10px 15px;
          font-family: $title_family;
          font-weight: 700;
          font-size: 16px;
          letter-spacing: 0.5px;
          color: $corporate_2;
          text-align: right;
        }

        .input_currency {
          @include center_y;
          right: 5px;
          font-weight: 700;
          font-size: 16px;
          letter-spacing: 0.5px;
        }
      }
    }

    .gift_wrapper {
      background: #0A5689;
      background-position: center !important;
      background-size: contain !important;
      background-repeat: no-repeat !important;
      height: 370px;
      border-radius: 25px;
      padding: 40px 60px;
      position: relative;
      margin: 20px 0;
      color: white;

      span {
        display: block;
        margin-top: 5px;
        margin-bottom: 50px;
        font-weight: 700;
        font-size: 20px;
        text-align: left;
        text-transform: uppercase;

        @media (min-width: 320px) and (max-width: 410px) {
          margin-top: 15px;
        }

        @media (min-width: 411px) and (max-width: 768px) {
          margin-left: 20px;
        }
      }

      .logo_bono {
        max-height: 40px;
        margin-bottom: 40px;
      }

      .input_wrapper {
        display: inline-block;
        position: relative;

        @media (min-width: 411px) and (max-width: 768px) {
          margin-left: 20px;
        }

        .hidden {
          visibility: hidden;
          position: absolute;
          pointer-events: none;
        }

        .message {
          display: none;
          @include center_x;
          top: -15px;
          width: 60%; // Should be equal to underline width
          text-align: left;
          font-size: 12px;
          white-space: nowrap;
        }

        .input_price, .input_price_custom {
          -webkit-appearance: none;
          -moz-appearance: none;
          -ms-appearance: none;
          -o-appearance: none;
          appearance: none;
          border: 0;
          background: transparent;
          font-size: 42px;
          color: white;
          font-weight: bold;
          width: 130px;
          height: 60px;
          outline: none;
          text-align: right;

          &::placeholder {
            color: white;
            white-space: pre-line;
            font-weight: lighter;
            position: relative;
            top: -12px;
            font-size: 18px;
            text-align: left;
          }
        }

        .custom_price {
          display: inline-block;
          vertical-align: middle;
          max-width: 350px;
          overflow: hidden;
          font-weight: 700;
          font-size: 42px;
          color: white;
          text-overflow: ellipsis;
        }

        .input_price_custom {
          visibility: hidden !important;
          position: absolute !important;
          pointer-events: none !important;
        }

        .currency {
          display: inline-block;
          vertical-align: middle;
          font-weight: 700;
          font-size: 42px;
        }

        label.error {
          display: block;
          white-space: nowrap;
        }
      }
    }

    .conditions_wrapper {
      margin-top: 40px;

      .conditions_title {
        margin-bottom: 20px;
      }

      .condition {
        display: grid;
        grid-template-columns: 30px auto;
        align-items: center;
        font-size: 15px;
        margin: 10px 0;

        i {
          vertical-align: middle;
          color: $corporate_1;
        }
      }
    }
  }
}

.wrapper_gift_bono.mobile_v {
  padding: 70px 20px 20px;

  .title {
    margin: auto;
  }

  .gift_bono_content {
    flex-flow: column;

    .half_content_wrapper {
      padding-top: 0 !important;
      width: auto;

      .discount_text {
        font-size: 15px;
      }

      .gift_wrapper {
        height: 200px;
        padding: 20px 35px;

        .title_wrapper {
          .title_info {
            font-size: 16px;
            margin-bottom: 20px;
          }
        }

        .input_wrapper {
          vertical-align: middle;

          .custom_price {
            max-width: 250px;
          }

          .input_price {
            width: 90px;
            font-size: 32px;
            padding: 0;
          }

          label.error {
            left: 20%;
            font-size: 12px;
          }
        }

        .button_bono {
          float: none;

          .buy_bono {
            font-size: 15px;
            padding: 10px 25px;
          }
        }
      }

      .conditions_wrapper {
        text-align: left;
      }
    }
  }
}

.select2-container.select2-container--default {
  .select2-search--dropdown .select2-search__field {
    border: none;
    border-bottom: 2px solid #333;

    &:focus {
      outline: none;
    }
  }

  .select2-results__option--disabled {
    display: none;
  }
}

.select2-results {
  strong {
    font-weight: 600 !important;
  }
}
