.rooms_wrapper {
  @include banner_wrapper_styles;
  padding: 30px 0;

  .room {
    padding: 50px 0 50px calc((100% - 1700px) / 2);
    @include display_flex;
    align-items: center;
    width: 100%;
    position: relative;

    .tour_virtual_link_icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      position: absolute;
      top: 115px;
      left: 120px;
      background: white;
      z-index: 1;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      img {
        width: 30px;
        height: auto;
      }
    }


    .room_gallery, .room_image {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      width: 40%;
      height: 540px;
      overflow: hidden;
      @include box_shadow;
      box-shadow: 5px 15px 35px 0px rgba(0, 0, 0, 0.3);
      @include border_radius;

      .room_img {
        width: 100%;
        height: 100%;
      }

      img {
        @include center_image;
        width: auto;
      }

      .search_icon {
        display: inline-block;
        position: absolute;
        z-index: 5;
        top: 20px;
        left: 20px;
        color: white;
        font-size: 28px;
        @include transition(color, .6s);

        &:hover {
          color: $corporate_2;
        }
      }

      .owl-stage-outer, .owl-stage, .owl-item {
        height: 100% !important;
      }

      .owl-item.active {
        z-index: 10;
      }

      .owl-nav {
        position: absolute;
        bottom: 45px;
        left: 20px;
        right: 20px;
        height: 0;

        .owl-prev, .owl-next {
          position: absolute;
          top: 0;
          left: 0;
          @include owl_nav_styles;

          i {
            color: white;
          }

          &:hover {
            i {
              color: $corporate_2;
            }
          }
        }

        .owl-next {
          left: auto;
          right: 0;
        }
      }
    }

    .rooms_content {
      display: inline-block;
      vertical-align: middle;
      width: 60%;
      padding: 30px calc((100% - 1140px) / 2) 30px 60px;
      background-color: $grey;

      .title {
        @include title_styles;
      }

      .text {
        @include text_styles;
        font-size: 17px;
        line-height: 23px;
      }

      .room_price {
        color: $text_color;
        text-transform: uppercase;
        display: block;
        margin: 20px 0;
        font-size: 18px;
      }

      .room_icons {
        @include display_flex;
        margin-bottom: 30px;
        margin-top: 40px;

        .tooltip {
          @include icon_styles_2($corporate_1);
          max-width: 60px;
          max-height: 60px;

          &:before {
            max-width: 80px;
            max-height: 80px;
          }

          i {
            font-size: 22px;
          }

          .tooltiptext {
            @include center_x;
            top: calc(100% + 10px);
            margin-top: -25px;
            text-align: center;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            @include transition(all, .6s);
          }

          &:hover {
            max-width: 50px;
            max-height: 50px;

            &:before {
              max-width: 60px;
              max-height: 60px;
            }

            .tooltiptext {
              opacity: 1;
              visibility: visible;
              margin-top: 0;
            }
          }
        }
      }

      .icon_link, .btn_personalized_1, .tour_virtual_link {
        margin-top: 30px;
      }
    }

    &:nth-of-type(even) {
      flex-direction: row-reverse;
      padding: 50px calc((100% - 1700px) / 2) 50px 0;

      .tour_virtual_link_icon {
        left: initial;
        right: 120px;
      }

      .room_gallery, .room_image {
        .search_icon {
          left: auto;
          right: 20px;
        }
      }


      .rooms_content {
        padding: 30px 30px 30px calc((100% - 1140px) / 2);
      }
    }

    @media (max-width: 1440px) {
      .tour_virtual_link_icon {
        left: 20px;
      }

      &:nth-of-type(even) {
        .tour_virtual_link_icon {
          right: 20px;
        }
      }
    }
  }
}

.fancybox-overlay .fancybox-type-inline {
  width: 80% !important;
  height: 80% !important;

  .fancybox-inner {
    width: 100% !important;
    height: 80vh !important;
  }
}

.tour_virtual_popup {
  width: 100%;
  height: 100%;

  iframe {
    width: 100%;
    height: 100%;
  }
}