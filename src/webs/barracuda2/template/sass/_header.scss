header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1101;
  padding: 50px 0;
  min-width: 1140px;
  @include linear_gradient_header;
  #wrapper-header {
    position: relative;
    text-align: center;
    .left_header, .right_header {
      @include center_y;
      left: 60px;
      color: white;
      text-transform: uppercase;
      font-size: 16px;
      a {
        @include base_link_styles(white);
        @include transition(all,0);
        font-weight: 500;
        padding: 1px 5px;
        text-rendering: unset;
        -webkit-font-smoothing: auto;
        &:hover {
          color: $corporate_2;
        }
      }
      .phone_contact {
        @include base_link_styles(white);
        border-right: 1px solid white;
        padding-right: 7px;
        margin-right: 2px;
        > i, > span {
          display: inline-block;
          vertical-align: middle;
        }
      }
      #lang {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        border-left: 1px solid white;
        padding-left: 7px;
        margin-left: 2px;
        cursor: pointer;
        .lang_selected {
          > span, > i {
            display: inline-block;
            vertical-align: middle;
            margin-right: 7px;
          }
          > span {
            font-weight: 700;
          }
          i {
            &:last-of-type {
              font-size: 22px;
              margin-bottom: 6px;
              margin-left: 0;
            }
          }
        }
        .lang_options_wrapper {
          @include center_x;
          top: calc(100% + 10px);
          margin-top: -25px;
          opacity: 0;
          visibility: hidden;
          @include border_radius;
          background-color: rgba($corporate_2, .8);
          @include transition(all, .6s);
          @include arrow_top_toggle(rgba($corporate_2, .8));
          &.active {
            opacity: 1;
            visibility: visible;
            margin-top: 0;
          }
          .lang_option {
            @include base_link_styles(white);
            padding: 8px 15px;
          }
        }
      }
      .top_sections {
        display: inline-block;
        vertical-align: middle;
        a {
          &:first-of-type {
            display: none;
          }
        }
      }
    }
    .right_header {
      left: auto;
      right: 60px;
      .top_sections {
        a {
          &:first-of-type {
            display: inline-block;
          }
          &:nth-of-type(2) {
            display: none;
          }
        }
      }
    }
    .left_header {
      .top_sections {
        a:last-of-type {
          display: none;
        }
      }
    }
    #logoDiv {
      display: inline-block;
      img {
        max-width: 300px;
      }
    }
  }
  #main_menu {
    #mainMenuDiv {
      margin-top: 15px;
      padding-top: 15px;
      text-align: center;
      border-top: 2px solid white;

      #main-sections-inner {
        display: flex;
        justify-content: space-evenly;
      }

      .main-section-div-wrapper {
        display: inline-block;
        vertical-align: middle;

        a {
          @include base_link_styles(white);
          text-transform: uppercase;
          font-size: 17px;
          font-weight: 500;

          &:hover {
            opacity: 1;
            color: $corporate_2;
            font-weight: bold;
          }
        }
      }
    }
  }
}

html[lang=ru] {
  #main_menu #mainMenuDiv .main-section-div-wrapper {
    margin: 0;
  }
}