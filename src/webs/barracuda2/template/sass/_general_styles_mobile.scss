$fontawesome5: true;
$mobile_padding: 20px;

@import "defaults";
@mixin title_styles() {
  position: relative;
  font-family: $title_family;
  font-weight: 400;
  font-size: 26px;
  line-height: 27px;
  box-sizing: border-box;
  margin: 10px 0 0;
  color: $corporate_1;
  .subtitle {
    display: block;
    font-weight: 700;
  }
}

@mixin box_shadow() {
  -webkit-box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.3);
  box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
}

body {
  nav a {
    color: $corporate_1;

    &.mailto {
      color: $corporate_2;
    }

    &.active {
      color: $corporate_2;
    }
  }

  .mobile_engine .mobile_engine_action {
    background-color: $corporate_2;
  }

  .main-owlslider .description_text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }

  .banner_carousel_full_wrapper {
    padding: 20px;
    .banner_carousel_wrapper .banner {
      .img_wrapper {
        border-radius: 10px;
      }

      .banner_content {
        padding: 20px 0 !important;
      }
    }
  }
}

@import "styles_mobile/2/2";
@import "mobile/banner_ventajas";
.banner_ventajas_full_wrapper {
  .banner_ventajas_wrapper {
    .banner_ventajas_icons {
      padding-top: 20px;
      .icon_wrapper {
        width: 50%;
        .icon {
          &:before {
            border-width: 2px;
          }
        }
      }
    }
  }
}

@import "mobile/banner_offers";
.banner_offers_full_wrapper {
  padding: 20px 5px;
  .banner_offer_wrapper {
    margin-bottom: 35px;
    .owl-item.active {
      .banner_offer_content {
        border-radius: 0 0 10px 10px;
      }
      .img_wrapper {
        border-radius: 10px 10px 0 0;
        box-shadow: 0 0 0 transparent;
        img {
          max-height: 100%;
        }
      }
    }
    .owl-nav {
      top: 100%;
      bottom: auto;
      .owl-prev, .owl-next {
        &:hover i {
          color: $corporate_2;
        }
      }
    }
  }
}

@import "mobile/banner_icons";
.banner_icons_full_wrapper {
  .banner_icons_wrapper {
    padding: 0 25px;
    box-sizing: border-box;
    .icon_wrapper {
      .icon {
        &:before {
          border-width: 2px;
        }
      }
    }
  }
}

@import "mobile/banner_carousel";
@import "mobile/banner_gallery";
.banner_gellery_full_wrapper {
  .banner_gellery_wrapper {
    .banner_gellery {
      .img_wrapper {
        border-radius: 10px 10px 0 0;
        box-shadow: 0 0 0 transparent;
      }
      .banner_content {
        border-radius: 0 0 10px 10px;
      }
    }
  }
}

@import "mobile/banner_instagram";
@import "mobile/banner_block";


@import "mobile/banner_cards";
@import "mobile/banner_gallery_black";

body {
  font-family: $text_family;

  a {
    text-decoration: none;
  }
  strong {
    font-weight: 700;
  }

  .icon_styles {
    @include icon_styles($corporate_1, $corporate_2);
  }

  .icon_styles_2 {
    @include icon_styles_2($corporate_1);
  }

  .icon_styles_3 {
    @include icon_styles_3($corporate_2);
  }

  .btn_personalized_1 {
    @include btn_styles;
    &:lang(ru) {
      font-size: 18px;
      padding: 25px 25px;
    }
  }

  .icon_link {
    @include icon_link;
    &.with_btn_booking {
      margin-right: 0px;
      margin-bottom: 20px;
    }
  }

  .with_bg_img {
    @include with_bg_img_styles($white_rgba);
  }

  #full_wrapper_booking {
    .wrapper_booking_button {
      .submit_button {
        background-color: $corporate_1;
      }
    }
  }

  .section_content {
    .location_content .section-title {
      display: none;
    }
    > h1, .content_subtitle_title, .section_title, .location_content .section-subtitle {
      @include title_styles;
      padding-top: 40px;
    }
    div.content, div.content_subtitle_description, .section-content, .contact_content_element {
      @include text_styles;
      width: auto;
      padding: 0 20px;
    }
    .my_reservation_section .section-title {
      @include title_styles;
      font-size: 38px;
      font-weight: 100;
      padding-top: 40px;
    }
  }

  footer {
    display: table;
    width: 100%;
    background: $corporate_1;
    color: white;
    padding-bottom: 30px;
    a {
      color: white;
    }
    .desktop-version-link {
      display: block;
      margin-bottom: 15px;
    }
    .social_footer a {
      @include icon_styles_3($corporate_2);
    }
  }
  .contact_form_wrapper {
    #contact {
      padding-bottom: 40px;
      #contact-button {
        margin-top: 20px;
        display: inline-block;
        vertical-align: middle;
        box-sizing: border-box;
        width: 50%;
        font-family: "Quicksand", sans-serif;
        color: white;
        background-color: $corporate_2;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 22px;
        font-weight: 500;
        padding: 15px 0;
        overflow: hidden;
        position: relative;
      }
    }
  }
  .promotions_wrapper {
    .owl-stage-outer .owl-item.active .offer_content .desc {
      margin-bottom: 60px;
    }
    &:lang(ru), &:lang(de) {
      .offer_content .button-promotion {
        width: 105%;
      }
    }
  }
  .rooms_wrapper .room_block .buttons a:nth-child(2) {
    &:lang(ru) {
      padding: 8px 10px 7px 8px;
    }
  }
}