<div class="gallery_filter_wrapper">{% set gallery_counter = 0 %}
    {% for gallery, pics in gallery_filter.items() %}
        <div class="gallery_block">
            {% set gallery_counter = gallery_counter + 1 %}
            <div class="gallery_title">
                {{ gallery|safe }}
            </div>
            {% for pic in pics %}
                {% if loop.first %}<div class="gallery_group_wrapper">{% endif %}
                    <a {% if pic.video %}href="{{ pic.video|safe }}" class="myFancyPopup fancybox.iframe image_wrapper with_svg_logo"{% else %}
                        href="{{ pic.servingUrl }}=s1900" rel="lightbox[gallery_{{ gallery_counter }}]" class="image_wrapper with_svg_logo" {% endif %}>
                        <img src="{{ pic.servingUrl }}=s550-c" alt="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}" title="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}">
                        {% if pic.description %}<span class="img_info">{{ pic.description|safe }}</span>{% endif %}
                    </a>
                {% if loop.index %5 == 0 and not loop.first %}</div><div class="gallery_group_wrapper">{% endif %}
                {% if loop.last %}</div>{% endif %}
            {% endfor %}
        </div>
    {% endfor %}
</div>