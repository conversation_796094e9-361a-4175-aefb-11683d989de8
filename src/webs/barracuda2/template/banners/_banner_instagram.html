<div class="banner_instagram_full_wrapper">
    {% if banner_instagram_content %}
        <div class="banner_instagram_content">
            {% if banner_instagram_content.subtitle %}
                <h3 class="content_title">{{ banner_instagram_content.subtitle|safe }}</h3>
            {% endif %}
            {% if banner_instagram_content.content %}
                <div class="content_text">{{ banner_instagram_content.content|safe }}</div>
            {% endif %}
        </div>
    {% endif %}
    {% if instagram_id %}
        <div class=" banner_instagram_wrapper instagram_wrapper" data-instagram-id="{{ instagram_id|safe }}"></div>
        <script src="/static_1/scripts/instagram_api.js?v=1.1"></script>
        <script>
            $(function () {
                instagram_api.init();
            });
        </script>
    {% endif %}
</div>

{% if not is_mobile and not user_isIpad %}
    <script>
        $(window).load(function () {
            function banner_instagram_fx() {
                for (x = 1; x <= 4; x++) {
                    $(".banner_instagram_wrapper .banner:nth-child(" + x + ")").addnimation({parent:$(".banner_instagram_full_wrapper"), class:"fadeInUp", delay: x * 80, reiteration: false});
                }
            }
            banner_instagram_fx();
            $(window).scroll(banner_instagram_fx);
        });
    </script>
{% endif %}
{% if is_mobile %}
<script>
    $(window).load(function () {
        $(".banner_instagram_wrapper").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-long-arrow-alt-left"></i>', '<i class="fal fa-long-arrow-alt-right"></i>'],
            margin: 0,
            navSpeed: 500,
            autoplay: false
        });
    });
</script>
{% endif %}