<div class="banner_gellery_full_wrapper with_svg_logo">
    {% if banner_gallery_content %}
        <div class="banner_gallery_content">
            {% if banner_gallery_content.subtitle %}
                <h3 class="content_title">{{ banner_gallery_content.subtitle|safe }}</h3>
            {% endif %}
            {% if banner_gallery_content.content %}
                <h3 class="content_text">{{ banner_gallery_content.content|safe }}</h3>
            {% endif %}
        </div>
    {% endif %}
    <div class="banner_gellery_wrapper owl-carousel">
        {% for banner in banner_gellery %}
            <div class="banner_gellery">
                {% if banner.servingUrl %}
                    <div class="img_wrapper">
                        <img src="{{ banner.servingUrl|safe }}=s800" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                    </div>
                {% endif %}
                {% if banner.title or banner.description %}
                    <div class="banner_content">
                        {% if banner.title %}
                            <h4 class="title">{{ banner.title|safe }}</h4>
                        {% endif %}
                        {% if banner.description %}
                            <div class="text">{{ banner.description|safe }}</div>
                        {% endif %}
                        {% if banner.since %}
                            <span class="room_price">{{ T_desde }} <strong>{{ banner.since|safe }}/{{ T_noche }}</strong></span>
                        {% endif %}
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %} class="icon_link {% if banner.btn_booking %}with_btn_booking{% endif %}">
                                <i class="fal fa-plus"></i>
                            </a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="button_promotion btn_personalized_1">
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
        $(".banner_gellery_wrapper").owlCarousel({
            loop: true,
            nav: true,
            dots: true,
            items: 1,
            navText: ['<i class="fal fa-long-arrow-alt-left"></i>', '<i class="fal fa-long-arrow-alt-right"></i>'],
            margin: 0,
            navSpeed: 500,
            autoHeight: false,
            autoplay: false
        });
    });
</script>