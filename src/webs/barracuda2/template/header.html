<header>
    <div id="wrapper-header" class="">
        <div class="left_header">
            {% if phone_contact %}
                <a {% if phone_contact_clean %}href="tel:{{ phone_contact_clean|safe }}"{% endif %} class="phone_contact">
                    <i class="fal fa-phone"></i>
                    <span>{{ phone_contact|safe }}</span>
                </a>
            {% endif %}
            <div class="top_sections">
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
            </div>
        </div>
        <div id="logoDiv" class="">
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <div class="right_header">
            <div class="top_sections">
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
            </div>
            <div id="lang">
                {% if language_selected_code %}
                    <div class="lang_selected">
                        <i class="far fa-globe"></i><span>{{ language_selected_code|safe }}</span><i class="fal fa-sort-down"></i>
                    </div>
                {% endif %}
                <div class="lang_options_wrapper">
                    {% for key, value in language_codes.items() %}
                        <a class="lang_option" href="{{ hostWithoutLanguage }}/{{ key }}/">{{ value|upper }}</a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    <nav id="main_menu">
        <div id="mainMenuDiv" class="container12">
            {% include "main_div.html" %}
        </div>
    </nav>
</header>