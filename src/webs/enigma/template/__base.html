<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,700" rel="stylesheet">
    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ sectionName|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1.2" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}

    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>

    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>

    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css"/>
<link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css?v=1.111">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=@@automatic_version@@"/>


    <!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

    <!--[if IE 9]>

    <![endif]-->

    <script type="text/javascript">
        if (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPad/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) {
            document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
        }
    </script>

    <!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
    <![endif]-->

    <!--[if lte IE 8]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->


    <!-- jquery -->
    {{ jquery|safe }}

    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>

<body itemscope itemtype="//schema.org/Hotel" {% if inner_section %}class="inner_section"{% endif %}>
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">

{% if lang_management %}
    <input type="hidden" id="lang_management" value="{{ lang_management }}">
{% endif %}
{% if lang_default %}
    <input type="hidden" id="lang_default" value="{{ lang_default }}">
{% endif %}

{% block content %}

    <!--EDIT HERE YOUR PAGE-->

{% endblock %}

{% block additional_js %}

    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js?v=1.1"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1"></script>

    <!-- new booking engine -->
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js"></script>
    <script>$(function () {
        DP_extend_info.config.booking_version = '7';
        DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            return "<div class='day'>" + dateComponents[0] + "</div>/<div class='month'>" + dateComponents[1] + "</div>/<div class='year'>" + dateComponents[2] + "</div> ";
        };
        DP_extend_info.init();
    })</script>

    <script async type="text/javascript" src="/static_1/scripts/booking_7.js?v=1.2"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js?v=1"></script>

    {% if minigallery or cycle_banners or images or rooms %}
        <!-- Flex Slider-->
        <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>
    {% endif %}

    <!-- My specific js  -->
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js?v=2.2"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js?v=1"></script>

    <script type="text/javascript" src="https://www.tripadvisor.com/js3/conversion/pixel.js" async></script>


{% endblock %}




{% if sectionToUse.sectionType == 'Inicio' %}
    <script>
        {% if popup_inicio_automatico %}
            $(document).ready(function () {
                {% if cookies_on_popup_inicio_automatico %}

                    window.setTimeout(function () {
                        if (searchCookie("anuncio_fancy_{{ language }}")) {
                        }
                        else {
                            $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});
                            document.cookie = "anuncio_fancy_{{ language }}=1"
                        }
                    }, 800);

                {%  else %}


                    window.setTimeout(function () {
                        $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});
                    }, 800);


                {% endif %}
            });
        {% endif %}
    </script>
    {% if popup_inicio_automatico %}
        {% if popup_inicio_automatico.0.servingUrl %}
            <div style="display:none">
                <div class="popup_inicio" style="position:relative;display:table;">
                    {% if popup_inicio_automatico.0.linkUrl %}
                        <a href="{{ popup_inicio_automatico.0.linkUrl }}">{% endif %}<img
                        src="{{ popup_inicio_automatico.0.servingUrl }}=s850">
                    {% if popup_inicio_automatico.0.linkUrl %}</a>{% endif %}
                </div>
            </div>
        {% endif %}
    {% endif %}
{% endif %}




<div style="display: none;">
    <div id="data">
        <div id="wrapper_booking_fancybox">
            <div id="booking_widget_popup" class="booking_widget_fancybox">
                {{ booking_engine_2 }}
            </div>
        </div>
    </div>
</div>

<script>
    $(function(){
        $("#booking label.dates_selector_label").html("{{ T_entrada }} / {{ T_salida }}");
        $(".promocode_input").attr("placeholder", "{{ T_promocode }}");
        $(".submit_button").html("<span>" + $(".submit_button").text() + "</span>")
    })
</script>


{{ extra_content_website|safe }}
{{ all_tracking_codes_footer|safe }}
</body>
</html>