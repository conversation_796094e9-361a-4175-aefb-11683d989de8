<footer>
    {% if social_ids %}
        <div class="social_footer">
            {% if social_ids.facebook_id %}
                <a href="https://www.facebook.com/{{social_ids.facebook_id}}" target="_blank"><i class="fa fa-facebook" aria-hidden="true"></i></a>
            {% endif %}
            {% if social_ids.twitter_id %}
                <a href="https://twitter.com/#!/{{social_ids.twitter_id}}" target="_blank"><i class="fa fa-twitter" aria-hidden="true"></i></a>
            {% endif %}
            {% if social_ids.google_plus_id %}
                <a href="https://plus.google.com/u/0/{{social_ids.google_plus_id}}" target="_blank" rel="publisher"><i class="fa fa-google-plus" aria-hidden="true"></i></a>
            {% endif %}
            {% if social_ids.youtube_id %}
                <a href="https://www.youtube.com/{{social_ids.youtube_id}}" target="_blank"><i class="fa fa-youtube" aria-hidden="true"></i></a>
            {% endif %}
            {% if social_ids.pinterest_id %}
                <a href="https://es.pinterest.com/{{ social_ids.pinterest_id }}" target="_blank"><i class="fa fa-pinterest-p" aria-hidden="true"></i></a>
            {% endif %}
            {% if social_ids.instagram_id %}
                <a href="https://www.instagram.com/{{ social_ids.instagram_id }}" target="_blank"><i class="fa fa-instagram" aria-hidden="true"></i></a>
            {% endif %}
        </div>
    {% endif %}
    <div class="desktop-version-link">
         <a href="/utils?action=change_served_web">{{ T_ver_version_escritorio }}</a>
     </div>
    <div class="footer_legal_text_wrapper">
        <div class="footer_links_wrapper">
            {% for x in policies_section %}
                <a data-fancybox data-options='{"caption" : "{{ x.title|safe }}",
                "src" : "/{{language}}/?sectionContent={{ x.friendlyUrl }}", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                   data-width="1200" href="/{{language}}/?sectionContent={{ x.friendlyUrl }}" rel="nofollow">{{ x.title|safe }}</a> |
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>
        </div>

        {% if texto_legal %}
            <div class="legal_text">{{ texto_legal|safe }}</div>
        {% endif %}
    </div>
</footer>