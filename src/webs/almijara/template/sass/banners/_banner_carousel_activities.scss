.banner_carousel_activities_wrapper {
    @include sec_pad;
    padding-bottom: 20px;
    
    .gallery {
        .picture_wrapper {
            height: 400px;
            
            .picture_info {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                color: white;
                padding: 20px;
                text-align: center;
                
                .title {
                    font-size: $font_lg;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translateX(-50%);
                    transition: all .5s;
                    
                    &.sub_border {
                        &::before {
                            background-color: white;
                        }
                    }
                }
                
                .desc {
                    margin: 0;
                    position: absolute;
                    opacity: 0;
                    top: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    transition: all .5s;
                    
                    
                }
                
                .see_more {
                    position: absolute;
                    bottom: -20px;
                    opacity: 0;
                    transition: all .8s;
                    
                    &::before {
                        font-family: "Font Awesome 5 Pro";
                        position: absolute;
                        content: '\f067';
                        color: white;
                        font-size: 25px;
                        @include center_xy;
                    }
                }
            }
            
            &:hover {
                .picture_info {
                    .title {
                        top: 30%;
                    }
                    
                    .desc {
                        opacity: 1;
                        top: 50%;
                        
                    }
                    
                    .see_more {
                        bottom: 40px;
                        opacity: 1;
                    }
                    
                    &::before {
                        opacity: .85;
                    }
                }
            }
        }
        
        .owl-nav {
            top: 50%;
            bottom: auto;

            > div {
               i {
                    color: white;
                }
            }
            
            .owl-prev {
                left: 50px;
                &::before {
                    content: '';
                    position: absolute;
                    top: -200px;
                    left: -50px;
                    right: -50px;
                    bottom: 0;
                    width: 100px;
                    height: 395px;
                    background: white;
                    background: linear-gradient(-90deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
                }
            }
            
            .owl-next {
                right: 50px;

                &::before {
                    content: '';
                    position: absolute;
                    top: -200px;
                    left: -50px;
                    right: -50px;
                    bottom: 0;
                    width: 100px;
                    height: 395px;
                    background: white;
                    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
                }
            }
        }
    }
}

