
<div class="banner_rooms_wrapper">
    <div class="container12">
        {% for banner in rooms %}
        <div class="room_wrapper">
            {% if banner.images %}
            <div class="pictures_gallery owl-carousel">
                <div class="picture_wrapper">
                    <img src="{{banner.servingUrl|safe}}=s1900" alt="{{banner.altText|safe}}">
                    <a href="{{banner.servingUrl|safe}}=s1900" rel="lightbox[{{ banner.key|safe if banner.key else 'room_pictures' }}]" class="picture_link"></a>
                </div>
                {% for pic in banner.images %}
                    <div class="picture_wrapper">
                        <img src="{{pic.servingUrl|safe}}=s1900" alt="{{pic.altText|safe}}">
                        <a href="{{pic.servingUrl|safe}}=s1900" rel="lightbox[{{ banner.key|safe if banner.key else 'room_pictures' }}]" class="picture_link"></a>
                    </div>
                {% endfor %}
            </div>
            {% endif %}
            <div class="content_wrapper">
                {% if banner.title %}
                    <div class="content_title">
                        <h3 class="title">{{ banner.title|safe }}</h3>
                    </div>
                {% endif %}
                {% if banner.description %}
                <div class="desc">
                    {{ banner.description|safe }}
                </div>
                {% endif %}
                {% if banner.room_icons %}
                    <div class="room_icons">
                        {% for icon in banner.room_icons %}
                            <div class="icon">
                                {% if icon.ico %}<i class="{{ icon.ico|safe }}"></i>{% endif %}
                                {% if icon.description %}<div class="icon_text">{{ icon.description|safe }}</div>{% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                <a href="#data" class="button_promotion btn_primary">{{ T_reservar }}</a>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script type="text/javascript">

    $(window).load(function () {
        $(".banner_rooms_wrapper .owl-carousel").owlCarousel({
            loop: false,
            nav: false,
            navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
            dots: false,
            items: 1,
            smartSpeed: 600,
            fluidSpeed: 600,
            navSpeed: 600,
            autoplay: false
        });
    });

</script>