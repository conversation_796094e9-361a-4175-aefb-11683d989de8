<div class="banner_gallery_wrapper">
    <div class="gallery_filters container12">
        <a href="#" data-filter="all" class="apply_filter active">{{ T_ver_todas }}</a>
        {% for y in filters_gallery_single %}
            {% for class, filter in y.items() %}
                <a href="#" data-filter="{{ class|safe }}" class="apply_filter">{{ filter|safe }}</a>
            {% endfor %}
        {% endfor %}
        {% if filters_gallery_room %}
            <ul>
                {% for key in filters_gallery_room %}
                    {% for class, filter in key.items() %}
                        <li>
                            <a href="#" data-filter="{{ class|safe }}" class="apply_filter">{{ filter|safe }}</a>
                        </li>
                    {% endfor %}
                {% endfor %}
            </ul>
        {% endif %}
    </div>
    <div class="gallery_filter_wrapper">
        {% for filter,gallery in pics_gallery.items() %}
            <div class="gallery_filter">
                <div class="gallery_photos">
                    {% for image in gallery %}
                        {% if "youtube" in image.servingUrl %}
                            <a class="iframe_video">
                                <iframe src="{{ image.servingUrl|safe }}" frameborder="0"></iframe>
                            </a>
                        {% else %}
                            <a href="{{ image.servingUrl|safe }}=s1024" rel="lightbox[gallery_carousel_{{ image.class_filter }}]" class="{{ image.class_filter }} all">
                                <img src="{{ image.servingUrl|safe }}=s500-c">
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>
<script>
    $(window).load(function () {
        resize_video();
        $(".apply_filter:not(.room)").click(function (e) {
            e.preventDefault();
            var data_filter = $(this).attr("data-filter");
            $(".apply_filter").removeClass("active");
            $(this).addClass("active");
            $(".gallery_filter .gallery_photos a").slideUp().promise().done(function () {
                $(".gallery_filter .gallery_photos a."+data_filter).slideDown();
            });
        });
    });
    $(window).resize(resize_video);
    function resize_video() {
        $("a.iframe_video").height($("a.iframe_video").width() - 1);
    }
    $('.apply_filter.room').click(function(){
        $('.filter_rooms_container').toggleClass('active');
    })
    $('.filter_rooms_container li a.apply_filter').click(function(){
        $('.filter_rooms_container').toggleClass('active');
    });
</script>