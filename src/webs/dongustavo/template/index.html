{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}

{% if not interior %}
    <section id="slider_container">
        {{ revolution_slider|safe }}
        <div id="full_wrapper_booking">
            <div id="wrapper_booking" class="container12">
                <div id="booking" class="boking_widget_inline">
                    {{ booking_engine }}
                </div>
            </div>
        </div>

        <div class="overlay_top"></div>
        <div class="overlay_bottom"></div>
        {% if ticks %}
            <div class="ticks_wrapper">
                <div class="container12">
                    {% for x in ticks %}
                        <div class="ticks_element">
                            <div class="title_tick">{{ x.title|safe }}</div>
                            <div class="description_tick">{{ x.description|safe }}</div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </section>
{% else %}
    <section id="inner_slider_container">
        <div class="exceded_image_slider">
            <img class="inner_slider_picture center_image" src="{{ pictures.0.servingUrl }}=s1900">
        </div>

        <div id="full_wrapper_booking">
            <div id="wrapper_booking" class="container12">
                <div id="booking" class="boking_widget_inline">
                    {{ booking_engine }}
                </div>
            </div>
        </div>
        <div class="overlay_top"></div>
    </section>
{% endif %}


{% endblock %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% endblock %}


{% include "footer.html" %}


{% endblock %}