/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #00B2C6;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #00B2C6 url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #00B2C6;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #00B2C6;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #00B2C6;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #00B2C6;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

/*============ Booking Widget ============*/
/* line 3, ../sass/_booking_engine.scss */
#full_wrapper_booking {
  position: absolute;
  top: initial;
  bottom: 0px;
  margin: 0 auto;
  left: 0px;
  right: 0px;
  z-index: 20;
}

/* line 12, ../sass/_booking_engine.scss */
.boking_widget_inline {
  position: realtive;
}

/* line 16, ../sass/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: auto;
}

/* line 20, ../sass/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  margin-left: 22px;
  margin-top: 6px;
}

/* line 25, ../sass/_booking_engine.scss */
div#wrapper_booking {
  position: absolute;
  margin: 0 auto;
  left: 0px;
  right: 0px;
  z-index: 23;
  bottom: 30px;
}

/* line 34, ../sass/_booking_engine.scss */
.boking_widget_inline .booking_form {
  background-color: inherit !important;
}

/* line 38, ../sass/_booking_engine.scss */
.boking_widget_inline {
  background-color: rgba(0, 0, 0, 0.75);
  padding: 1px 12px 17px;
  width: 1140px !important;
  box-sizing: border-box;
  height: auto;
}

/* line 46, ../sass/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: white;
  border-radius: 0px;
}

/* line 51, ../sass/_booking_engine.scss */
.date_box .date_year, .selectric .label {
  color: gray;
}

/* line 55, ../sass/_booking_engine.scss */
.room_selector .label {
  color: #00B2C6;
}

/* line 59, ../sass/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: table;
}
/* line 62, ../sass/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  font-family: 'Roboto Slab';
  border-radius: 0px;
  font-size: 21px;
  width: 220px !important;
  background: #00B2C6 url(/static_1/images/booking/flecha_motor_der.png) no-repeat;
  background-position-x: 250px;
  -ms-background-position-y: center;
  background-position-y: center;
  font-weight: 300;
  vertical-align: middle;
  margin-top: 15px !important;
  margin-bottom: 4px;
  width: 290px !important;
  line-height: 0;
  border: 1px solid white;
}

/* line 81, ../sass/_booking_engine.scss */
.submit_button {
  cursor: pointer;
}

/* line 85, ../sass/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: white;
  font-weight: 500;
}

/* line 90, ../sass/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  border-radius: 0px;
  position: absolute;
  margin-top: 0px;
  top: 49px;
  left: 34px;
  box-shadow: 2px 2px 3px #666666;
  background: white;
  display: none;
  color: #00B2C6;
}
/* line 101, ../sass/_booking_engine.scss */
.boking_widget_inline .promocode_input::-webkit-input-placeholder {
  color: #00B2C6;
}
/* line 104, ../sass/_booking_engine.scss */
.boking_widget_inline .promocode_input:-moz-placeholder {
  color: #00B2C6;
  opacity: 1;
}
/* line 108, ../sass/_booking_engine.scss */
.boking_widget_inline .promocode_input::-moz-placeholder {
  color: #00B2C6;
  opacity: 1;
}
/* line 112, ../sass/_booking_engine.scss */
.boking_widget_inline .promocode_input:-ms-input-placeholder {
  color: #00B2C6;
}

/* line 117, ../sass/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label, .boking_widget_inline .room .room_title, .boking_widget_inline .room .room_title, .boking_widget_inline .room .adults_selector label, .boking_widget_inline .room .children_selector label, .boking_widget_inline .room .babies_selector label {
  font-weight: lighter;
  color: white;
  font-family: 'Roboto';
  font-size: 11px;
}

/* line 124, ../sass/_booking_engine.scss */
.promocode_text {
  text-align: center;
  float: left;
  cursor: pointer;
  margin-top: 26px;
  font-family: roboto slab;
  font-size: 11px;
  text-decoration: underline;
  color: white;
  font-weight: 400;
  margin-right: 25px;
}
/* line 135, ../sass/_booking_engine.scss */
.promocode_text strong {
  font-weight: inherit;
}

/* line 140, ../sass/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  display: none;
}

/* line 144, ../sass/_booking_engine.scss */
#data {
  position: relative;
}
/* line 146, ../sass/_booking_engine.scss */
#data .promocode_text {
  margin-right: 0px;
}
/* line 149, ../sass/_booking_engine.scss */
#data .promocode_input {
  position: absolute;
  left: 107px;
  box-shadow: 2px 2px 5px !important;
  top: 15px;
}
/* line 156, ../sass/_booking_engine.scss */
#data .wrapper_booking_button button {
  font-size: 13px;
  margin-top: 15px;
  background: #AAAAAA;
}
/* line 162, ../sass/_booking_engine.scss */
#data .date_box, #data .selectric {
  background: #f5f5f5;
}
/* line 166, ../sass/_booking_engine.scss */
#data .booking_form {
  background: #C7C7C7;
  width: 270px;
}
/* line 171, ../sass/_booking_engine.scss */
#data .room .adults_selector {
  margin-right: 8px;
}
/* line 175, ../sass/_booking_engine.scss */
#data .promocode_text {
  margin-left: 0;
}

/* line 180, ../sass/_booking_engine.scss */
.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper {
  margin-right: 10px;
}

/* line 184, ../sass/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  margin-left: 15px;
}

/* line 188, ../sass/_booking_engine.scss */
.room .room_title, .room .adults_selector {
  margin-right: 5px;
}

/* line 192, ../sass/_booking_engine.scss */
.room_title {
  padding-right: 7px;
}

/* line 196, ../sass/_booking_engine.scss */
.room .adults_selector {
  margin-right: 10px;
}

/* line 200, ../sass/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  margin-right: 10px;
}

/* line 204, ../sass/_booking_engine.scss */
.promocode_text {
  margin-right: 20px;
  margin-left: 30px;
  margin-top: 27px;
  font-size: 12px;
  font-weight: lighter;
}

/* line 213, ../sass/_booking_engine.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 217, ../sass/_booking_engine.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 221, ../sass/_booking_engine.scss */
.ui-widget-header {
  background: #00B2C6 !important;
}

/* line 225, ../sass/_booking_engine.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #00B2C6 !important;
  color: white;
}

/* line 1, ../sass/_booking_widget_modal.scss */
#motor_reserva.modal-booking-widget {
  width: 256px !important;
}

/* line 5, ../sass/_booking_widget_modal.scss */
.modal-booking-widget {
  width: 50px;
}

/* line 9, ../sass/_booking_widget_modal.scss */
.modal-booking-widget {
  width: 50px;
}

/* line 13, ../sass/_booking_widget_modal.scss */
.modal-booking-widget fieldset {
  float: none;
}

/* line 17, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .adultos {
  margin: 0 30px 0 0 !important;
}

/* line 21, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .contenedor_opciones {
  width: 230px;
}

/* line 25, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .adultos select.selector_adultos,
.modal-booking-widget .contenedor_opciones .ninos select,
.modal-booking-widget .contenedor_opciones .numero_personas select {
  width: 100px;
}

/* line 31, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .contenedor_opciones .numero_personas select {
  float: none;
}

/* line 39, ../sass/_booking_widget_modal.scss */
.modal-booking-widget .ninos .info_ninos {
  left: 169px !important;
  width: 80px;
}

/* line 44, ../sass/_booking_widget_modal.scss */
#contenedor_opciones {
  width: 10px !important;
}

/* line 48, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  width: auto !important;
}

/* line 52, ../sass/_booking_widget_modal.scss */
.fancybox-outer {
  padding: 0 !important;
}

/* line 56, ../sass/_booking_widget_modal.scss */
.fancybox-inner {
  overflow: hidden !important;
}

/* line 60, ../sass/_booking_widget_modal.scss */
.booking-widget {
  background-color: white;
}

/* line 64, ../sass/_booking_widget_modal.scss */
.booking-widget fieldset {
  margin: 8px 0 0;
}

/* line 68, ../sass/_booking_widget_modal.scss */
.booking-widget label {
  color: #5a5a5a;
  display: block;
  font-size: 12px;
}

/* line 74, ../sass/_booking_widget_modal.scss */
.booking-widget .numero_habitacion {
  display: none;
}

/* line 78, ../sass/_booking_widget_modal.scss */
.modal-form {
  padding: 12px;
}

/* line 82, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title1 {
  display: none;
}

/* line 86, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title2 {
  text-align: center;
  background-color: #00B2C6;
  padding-top: 10px;
  font-size: 30px;
  color: white;
}

/* line 94, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  text-align: center;
  background-color: #00B2C6;
  padding-bottom: 10px;
  color: white;
}

/* line 101, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  margin-bottom: 20px;
  font-size: 18px;
}

/* line 106, ../sass/_booking_widget_modal.scss */
.modal-form #selector_hotel {
  width: 100%;
}

/* line 110, ../sass/_booking_widget_modal.scss */
.modal-form #hotel_destino {
  width: 100%;
}

/* line 114, ../sass/_booking_widget_modal.scss */
.modal-form .colocar_fechas {
  float: left;
}

/* line 118, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_salida {
  padding-top: 10px;
  clear: both;
}

/* line 123, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_entrada input, .modal-form .fecha_salida input {
  height: 18px;
  width: 91px;
  border: 1px solid #5a5a5a;
  border-radius: 4px;
  cursor: pointer;
  background: #f0f0f0 url(/img/expr1/date_icon.png) no-repeat 72px;
  background-size: 18px;
  float: right;
}

/* line 134, ../sass/_booking_widget_modal.scss */
.modal-form .contador_noches {
  display: none;
}

/* line 138, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones {
  margin-top: 10px;
  margin-bottom: 10px;
}

/* line 143, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones select {
  width: 95px !important;
  float: right;
}

/* line 148, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones label {
  float: left;
  width: 108px;
}

/* line 153, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones {
  margin-bottom: 10px;
}

/* line 157, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones select {
  float: right;
  width: 92px;
}

/* line 162, ../sass/_booking_widget_modal.scss */
.modal-form .hab1 {
  position: relative;
}

/* line 166, ../sass/_booking_widget_modal.scss */
.modal-form .hab2, .modal-form .hab3 {
  display: none;
}

/* line 170, ../sass/_booking_widget_modal.scss */
.modal-form .numero_habitacion {
  display: none;
}

/* line 174, ../sass/_booking_widget_modal.scss */
.modal-form .adultos {
  float: left;
  margin: 0 15px 0 0;
}

/* line 179, ../sass/_booking_widget_modal.scss */
.modal-form .ninos {
  float: right;
}

/* line 183, ../sass/_booking_widget_modal.scss */
.modal-form .info_ninos {
  position: absolute;
  line-height: 10px;
  text-align: center;
  top: 4px !important;
  left: 155px !important;
  font-size: 9px !important;
}

/* line 192, ../sass/_booking_widget_modal.scss */
.modal-form .envio {
  min-height: 100px;
}

/* line 196, ../sass/_booking_widget_modal.scss */
.modal-form .envio input {
  margin: 0 9px 0 0;
  color: black;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #464646;
  width: 100% !important;
}

/* line 205, ../sass/_booking_widget_modal.scss */
.modal-form .envio button {
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  cursor: pointer;
  overflow: visible;
  font-size: 24px;
  display: block;
  margin: 30px auto 0;
  background-color: #00B2C6;
  color: white;
}

/* line 219, ../sass/_booking_widget_modal.scss */
.modal-form .envio button:hover {
  background: #AAAAAA;
}

/* line 223, ../sass/_booking_widget_modal.scss */
.modal-form .spinner {
  top: 70px !important;
  left: 120px !important;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #00B2C6;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #00B2C6 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/*FONFS*/
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-Book.otf");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-bold.otf");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'gotham';
  src: url("/static_1/fonts/gotham/GothamRnd-Light.otf");
  font-weight: 300;
  font-style: normal;
}
/*=== General ===*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'gotham';
}

/* line 7, ../sass/_template_specific.scss */
body.interior section#slider_container {
  height: 630px;
  overflow: hidden;
  width: 100%;
  display: inline-block;
}

/* line 15, ../sass/_template_specific.scss */
section#content {
  padding-top: 80px;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 21, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 25, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 29, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #00B2C6 !important;
}

/* line 33, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #00B2C6 !important;
  color: white;
}

/*=== Header ===*/
/* line 39, ../sass/_template_specific.scss */
header {
  width: 100%;
  z-index: 30;
  background: #00B2C6;
  min-width: 1140px;
  top: 0;
  height: 150px;
  overflow: hidden;
  position: absolute;
}
/* line 49, ../sass/_template_specific.scss */
header div#logoDiv {
  margin-top: 7px;
  margin-left: 0;
}

/* line 55, ../sass/_template_specific.scss */
.top_header {
  text-align: right;
  margin-top: 14px;
}
/* line 59, ../sass/_template_specific.scss */
.top_header div#social {
  display: inline-block;
  float: right;
  margin-left: 15px;
  margin-top: 22px;
}
/* line 65, ../sass/_template_specific.scss */
.top_header div#social a {
  text-decoration: none;
  display: inline-table;
  float: left;
  margin-right: 5px;
}
/* line 73, ../sass/_template_specific.scss */
.top_header #lang {
  margin-top: 20px;
}
/* line 77, ../sass/_template_specific.scss */
.top_header div#lang {
  display: inline-block;
}
/* line 81, ../sass/_template_specific.scss */
.top_header .contact_phone {
  display: inline-block;
  font-size: 14px;
  color: white;
  font-weight: lighter;
  margin-right: 15px;
  margin-left: 15px;
  margin-top: 23px;
}
/* line 90, ../sass/_template_specific.scss */
.top_header .contact_phone img {
  vertical-align: bottom;
  margin-bottom: 2px;
  margin-right: 5px;
}
/* line 97, ../sass/_template_specific.scss */
.top_header div#top-sections {
  display: inline-table;
  margin-top: 23px;
}
/* line 101, ../sass/_template_specific.scss */
.top_header div#top-sections a {
  font-size: 12px;
  color: white;
  font-weight: lighter;
  text-decoration: none;
  margin-right: 22px;
}
/* line 108, ../sass/_template_specific.scss */
.top_header div#top-sections a:hover {
  opacity: 0.8;
}
/* line 112, ../sass/_template_specific.scss */
.top_header div#top-sections a:last-of-type {
  margin-right: 0;
}
/* line 116, ../sass/_template_specific.scss */
.top_header div#top-sections a img {
  vertical-align: middle;
  margin-bottom: 2px;
  margin-right: 5px;
}

/* line 125, ../sass/_template_specific.scss */
.web_oficial {
  color: white;
  float: left;
  font-size: 12px;
  margin-top: 27px;
}

/* line 133, ../sass/_template_specific.scss */
#lang {
  position: relative;
  top: 7px;
  float: right;
  font-size: 12px;
  color: white;
  font-weight: lighter;
  text-decoration: none;
  margin-left: 15px;
  cursor: pointer;
}
/* line 144, ../sass/_template_specific.scss */
#lang span#selected-language {
  padding-left: 4px;
}
/* line 148, ../sass/_template_specific.scss */
#lang #language-selector-options {
  position: absolute;
  margin-top: 4px;
}
/* line 153, ../sass/_template_specific.scss */
#lang .arrow {
  display: inline-block;
  background: url(/img/expr1/flecha_white_down.png) no-repeat center center !important;
  float: right;
  width: 30px;
  height: 26px;
  margin-top: -6px;
  background-size: 17px !important;
}
/* line 168, ../sass/_template_specific.scss */
#lang ul li {
  background: #ffffff;
  text-align: left;
  width: 80px;
  font-size: 14px;
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 180, ../sass/_template_specific.scss */
#lang ul li:hover {
  border-bottom: 1px solid rgba(128, 128, 128, 0.33);
  background: #f0f0f0;
  width: 80px;
}
/* line 186, ../sass/_template_specific.scss */
#lang ul li a {
  color: #666 !important;
  text-decoration: none !important;
}

/*===== Menu =====*/
/* line 196, ../sass/_template_specific.scss */
#mainMenuDiv {
  margin-top: 35px;
}
/* line 199, ../sass/_template_specific.scss */
#mainMenuDiv ul {
  text-align: justify;
  justify-content: space-between;
}
/* line 203, ../sass/_template_specific.scss */
#mainMenuDiv ul:after {
  content: "";
  width: 100%;
  display: inline-block;
  height: 0;
}
/* line 210, ../sass/_template_specific.scss */
#mainMenuDiv ul li {
  display: inline-block;
  text-align: center;
}
/* line 214, ../sass/_template_specific.scss */
#mainMenuDiv ul li:first-of-type {
  padding-left: 0;
}
/* line 218, ../sass/_template_specific.scss */
#mainMenuDiv ul li:nth-last-of-type(2), #mainMenuDiv ul li:last-of-type {
  border-right: 0;
}
/* line 222, ../sass/_template_specific.scss */
#mainMenuDiv ul li:last-of-type {
  padding-right: 0;
}
/* line 226, ../sass/_template_specific.scss */
#mainMenuDiv ul li a {
  text-decoration: none;
  font-size: 16px;
  font-weight: lighter;
  color: white;
  text-transform: uppercase;
  padding: 6px 0 5px;
}
/* line 234, ../sass/_template_specific.scss */
#mainMenuDiv ul li a.button-promotion {
  color: white !important;
  background: #00B2C6;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  padding: 9px 7px;
}
/* line 244, ../sass/_template_specific.scss */
#mainMenuDiv ul li:hover a {
  border-top: 2px solid white;
  border-bottom: 2px solid white;
}
/* line 249, ../sass/_template_specific.scss */
#mainMenuDiv ul li#section-active a {
  font-weight: 700;
  border-top: 2px solid white;
  border-bottom: 2px solid white;
  padding: 6px 0 5px;
}

/*=== Slider ===*/
/* line 260, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}

/* line 264, ../sass/_template_specific.scss */
.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

/* line 272, ../sass/_template_specific.scss */
.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

/* line 286, ../sass/_template_specific.scss */
.tparrows {
  display: none !important;
}

/* line 290, ../sass/_template_specific.scss */
.tp-bullets {
  bottom: 150px !important;
  opacity: 1 !important;
}

/* line 295, ../sass/_template_specific.scss */
.slide_inner {
  height: 630px;
  width: 100%;
  overflow: hidden;
  display: inline-block;
}
/* line 301, ../sass/_template_specific.scss */
.slide_inner img {
  width: 100%;
  display: block;
}

/* line 307, ../sass/_template_specific.scss */
.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;
}
/* line 314, ../sass/_template_specific.scss */
.down_slider_arrow:hover {
  opacity: 0.8;
}

/*==== Booking Widget ====*/
/* line 320, ../sass/_template_specific.scss */
label#titulo_fecha_entrada, label#titulo_fecha_salida {
  display: none;
}

/* line 324, ../sass/_template_specific.scss */
#contenedor_habitaciones > label {
  display: none;
}

/* line 329, ../sass/_template_specific.scss */
.adultos.numero_personas > label {
  display: none !important;
}

/* line 334, ../sass/_template_specific.scss */
#titulo_ninos {
  display: none !important;
}

/* line 338, ../sass/_template_specific.scss */
#booking fieldset {
  margin: 5px 0 0;
}

/* line 342, ../sass/_template_specific.scss */
#search-button {
  font-size: 14px;
}

/*======= Booking Widget Data =====*/
/* line 348, ../sass/_template_specific.scss */
#data .date_box {
  background: #ECECEC;
}
/* line 351, ../sass/_template_specific.scss */
#data .date_box .date_year {
  color: #5CACDB;
}
/* line 356, ../sass/_template_specific.scss */
#data .selectric, #data .promocode_input {
  background: #ECECEC;
}
/* line 360, ../sass/_template_specific.scss */
#data .promocode_input {
  font-size: 13px;
}

/* line 365, ../sass/_template_specific.scss */
.fancybox-inner {
  overflow: visible !important;
}

/*====== Content Subtitle =====*/
/* line 370, ../sass/_template_specific.scss */
h3.subtitle_title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #00B2C6;
  margin-bottom: 28px;
}

/* line 378, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  display: table;
  width: 100%;
}
/* line 382, ../sass/_template_specific.scss */
.content_subtitle_wrapper .divided1, .content_subtitle_wrapper .divided2, .content_subtitle_wrapper .divided3 {
  width: 350px;
  float: left;
  text-align: left;
}
/* line 388, ../sass/_template_specific.scss */
.content_subtitle_wrapper .divided3 {
  float: right;
}
/* line 392, ../sass/_template_specific.scss */
.content_subtitle_wrapper .divided2 {
  margin-left: 45px;
}
/* line 396, ../sass/_template_specific.scss */
.content_subtitle_wrapper .subtitle_description {
  font-size: 17px;
  font-weight: lighter;
  line-height: 29px;
  color: #636363;
  text-align: center;
  margin-bottom: 40px;
}

/*============== Gallery Section features ==============*/
/* line 409, ../sass/_template_specific.scss */
.gallery_1 li .crop img {
  height: 235px !important;
  width: 100%;
}

/*============== Gallery Mosaic ==============*/
/* line 417, ../sass/_template_specific.scss */
.gallery_title {
  padding-top: 60px;
}

/* line 421, ../sass/_template_specific.scss */
.gallery_title, .services_title {
  color: #626262;
  text-align: center;
  font-size: 27px;
  margin-bottom: 30px;
  font-weight: lighter;
  text-transform: uppercase;
}
/* line 429, ../sass/_template_specific.scss */
.gallery_title:after, .services_title:after {
  content: "";
  width: 55px;
  border-bottom: 2px solid #00B2C6;
  display: block;
  margin: 17px auto 0px;
}

/* line 438, ../sass/_template_specific.scss */
.gallery-mosaic-item {
  float: left;
  width: 375px;
  margin: 2px;
  height: 250px;
  overflow: hidden;
  position: relative;
}
/* line 446, ../sass/_template_specific.scss */
.gallery-mosaic-item img {
  min-width: 100%;
  min-height: 100%;
  max-height: auto;
  max-width: auto;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 463, ../sass/_template_specific.scss */
.gallery-mosaic-item img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 473, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  position: relative;
  height: 421px;
}
/* line 479, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item:hover img {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}
/* line 487, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img {
  width: auto;
  height: 100%;
  max-width: none;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 507, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img.video_arrow {
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
  width: 100px;
  height: 100px;
  z-index: 2;
  min-height: inherit;
  min-width: initial;
}

/* line 521, ../sass/_template_specific.scss */
.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

/* line 526, ../sass/_template_specific.scss */
.gallery-big {
  width: 396px;
  height: 360px;
}

/* line 532, ../sass/_template_specific.scss */
.gallery-mosaic {
  margin: 78px auto 0px;
}

/* line 536, ../sass/_template_specific.scss */
.flexslider_gallery {
  position: relative;
}
/* line 539, ../sass/_template_specific.scss */
.flexslider_gallery li {
  width: 1285px !important;
}

/*======= Banners x3 =======*/
/* line 545, ../sass/_template_specific.scss */
.bannersx3_wrapper {
  display: table;
  width: 100%;
  margin-top: 73px;
}
/* line 550, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element {
  float: left;
  width: 33.3%;
  position: relative;
  overflow: hidden;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
/* line 561, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element:hover {
  opacity: 0.8;
}
/* line 565, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element:before {
  content: "";
  display: block;
  padding-top: 95%;
}
/* line 571, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element img {
  width: auto;
  position: absolute;
  left: -50%;
  top: 0;
  min-height: 100%;
  max-width: none;
  margin: 0 auto;
  right: -50%;
  bottom: 0px;
}
/* line 583, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element.big {
  width: 33.4%;
}
/* line 586, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element.big:before {
  content: "";
  display: block;
  padding-top: 94.6%;
}
/* line 593, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element .circle {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 22px;
  width: 235px;
  margin: auto;
  text-align: center;
  color: white;
  background: rgba(101, 180, 230, 0.5);
  font-size: 21px;
  border-radius: 181px;
  padding: 105px 0px;
}
/* line 610, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element .underline_bottom {
  width: 75px;
  border-bottom: 2px solid white;
  margin: 5px auto;
}
/* line 616, ../sass/_template_specific.scss */
.bannersx3_wrapper .banner_element .underline_top {
  width: 75px;
  border-bottom: 2px solid white;
  margin: 5px auto;
}

/*===== Bottom Phrase ====*/
/* line 625, ../sass/_template_specific.scss */
.bottom_phrase {
  text-align: center;
  margin-top: 78px;
  margin-bottom: 70px;
}
/* line 630, ../sass/_template_specific.scss */
.bottom_phrase h3.title {
  font-size: 34px;
  color: #5CACDB;
  margin-bottom: 30px;
  font-weight: bolder;
}
/* line 637, ../sass/_template_specific.scss */
.bottom_phrase .description {
  font-size: 24px;
  color: #636363;
  font-weight: lighter;
}

/*===== Footer ====*/
/* line 645, ../sass/_template_specific.scss */
footer {
  background: #ECECEC;
  padding-top: 43px;
}
/* line 649, ../sass/_template_specific.scss */
footer .footer_column {
  text-align: center;
}
/* line 652, ../sass/_template_specific.scss */
footer .footer_column.left {
  box-sizing: border-box;
  padding-left: 295px;
}
/* line 659, ../sass/_template_specific.scss */
footer .footer_column.right {
  padding-right: 295px;
  box-sizing: border-box;
}
/* line 664, ../sass/_template_specific.scss */
footer .footer_column.right #newsletter #title_newsletter, footer .footer_column.right #newsletter label#suscEmailLabel {
  display: none !important;
}
/* line 670, ../sass/_template_specific.scss */
footer .footer_column .footer_column_description {
  border-bottom: 1px solid #00B2C6;
  padding-bottom: 10px;
}
/* line 676, ../sass/_template_specific.scss */
footer .footer_column h3.footer_column_image {
  height: 80px;
}
/* line 680, ../sass/_template_specific.scss */
footer .footer_column .footer_column_description {
  color: #A5A5A5;
  font-weight: lighter;
  font-size: 12px;
  line-height: 24px;
}
/* line 688, ../sass/_template_specific.scss */
footer input#suscEmail {
  margin-top: 13px;
  width: 237px;
  height: 35px;
  border: 0;
}
/* line 695, ../sass/_template_specific.scss */
footer button#newsletter-button {
  width: 239px;
  border: 0;
  background: #00B2C6;
  color: white;
  margin-top: 7px;
  text-transform: uppercase;
  font-size: 17px;
  padding: 8px;
}
/* line 706, ../sass/_template_specific.scss */
footer .wrapper_footer_columns {
  margin-bottom: 51px;
}
/* line 709, ../sass/_template_specific.scss */
footer .wrapper_footer_columns a {
  text-decoration: none;
}
/* line 714, ../sass/_template_specific.scss */
footer .full-copyright {
  background: #00B2C6;
  padding: 29px 0;
}
/* line 718, ../sass/_template_specific.scss */
footer .full-copyright .footer-copyright {
  text-align: center;
  font-size: 14px;
  color: white;
}
/* line 723, ../sass/_template_specific.scss */
footer .full-copyright .footer-copyright a {
  text-decoration: none;
  color: white;
  font-weight: lighter;
}
/* line 731, ../sass/_template_specific.scss */
footer div#facebook_like {
  width: 49%;
  float: left;
  margin-top: 2px;
  text-align: right;
}
/* line 738, ../sass/_template_specific.scss */
footer #google_plus_one {
  width: 49%;
  float: right;
}
/* line 743, ../sass/_template_specific.scss */
footer .social_likes {
  margin-top: 3px;
}
/* line 747, ../sass/_template_specific.scss */
footer div#div-txt-copyright {
  margin-top: 3px;
}

/*====== Rooms =======*/
/* line 753, ../sass/_template_specific.scss */
.room_element {
  background: #EBECEE;
  display: table;
  width: 100%;
  margin-bottom: 7px;
  position: relative;
}
/* line 760, ../sass/_template_specific.scss */
.room_element .exceded {
  width: 270px;
  float: left;
  height: 180px;
  overflow: hidden;
  position: relative;
}
/* line 767, ../sass/_template_specific.scss */
.room_element .exceded .lupa {
  position: absolute;
  top: 0;
  right: 0;
}
/* line 774, ../sass/_template_specific.scss */
.room_element > .description {
  float: right;
  width: 855px;
  box-sizing: border-box;
  padding: 23px 16px;
  padding-right: 150px;
  line-height: 20px;
}
/* line 782, ../sass/_template_specific.scss */
.room_element > .description h3.room_title {
  text-align: left;
  font-size: 20px;
  font-weight: 300;
  color: #5CACDB;
  margin-bottom: 10px;
}
/* line 790, ../sass/_template_specific.scss */
.room_element > .description .room_description {
  font-size: 11px;
  color: gray;
  height: 76px;
  overflow: hidden;
}
/* line 798, ../sass/_template_specific.scss */
.room_element .see_more_room {
  position: absolute;
  right: 20px;
  top: 23px;
  background: white;
  cursor: pointer;
}
/* line 805, ../sass/_template_specific.scss */
.room_element .see_more_room:hover {
  opacity: 0.8;
}
/* line 809, ../sass/_template_specific.scss */
.room_element .see_more_room span {
  padding: 0 10px 0 7px;
  font-size: 14px;
  font-style: italic;
  color: gray;
}
/* line 816, ../sass/_template_specific.scss */
.room_element .see_more_room img:not(.lupa) {
  width: 25px;
  display: inline-block;
  vertical-align: middle;
}
/* line 824, ../sass/_template_specific.scss */
.room_element .book_room {
  position: absolute;
  right: 20px;
  top: 60px;
  background: #00B2C6;
  color: white;
  cursor: pointer;
  height: 24px;
  box-sizing: border-box;
  width: 99px;
}
/* line 835, ../sass/_template_specific.scss */
.room_element .book_room:hover {
  opacity: 0.8;
}
/* line 839, ../sass/_template_specific.scss */
.room_element .book_room a {
  font-size: 14px;
  font-style: italic;
  color: white;
  text-align: center;
  width: 100%;
  display: block;
  padding-top: 5px;
  text-decoration: none;
}

/* line 858, ../sass/_template_specific.scss */
.hide_room_description {
  padding: 30px;
}
/* line 861, ../sass/_template_specific.scss */
.hide_room_description .room_title {
  font-size: 20px;
  font-weight: 300;
  color: #5CACDB;
  margin-bottom: 10px;
}
/* line 868, ../sass/_template_specific.scss */
.hide_room_description .room_description {
  font-size: 13px;
  color: gray;
}

/*======= Ofertas ======*/
/* line 875, ../sass/_template_specific.scss */
a.plus {
  padding: 8px 8px 7px !important;
}

/* line 879, ../sass/_template_specific.scss */
a.play {
  padding: 10px 9px 5px !important;
}

/* line 884, ../sass/_template_specific.scss */
.enlace_offer {
  display: block;
  height: 250px;
  overflow: hidden;
  position: relative;
}
/* line 890, ../sass/_template_specific.scss */
.enlace_offer img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

/* line 905, ../sass/_template_specific.scss */
.scapes-blocks {
  overflow: hidden;
  margin-top: 0px;
  margin-bottom: -12px;
}

/* line 911, ../sass/_template_specific.scss */
.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
}
/* line 917, ../sass/_template_specific.scss */
.scapes-blocks .block a.button-promotion.oferta-reserva {
  width: 100px;
  margin-right: 10px;
}
/* line 923, ../sass/_template_specific.scss */
.scapes-blocks .block .description {
  padding: 20px;
  position: relative;
  background-color: #ECECEC;
  padding-right: 210px;
}
/* line 929, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module {
  font-size: 23px;
  color: #00B2C6;
  font-weight: 500;
  margin-top: 6px;
  height: 40px;
}
/* line 936, ../sass/_template_specific.scss */
.scapes-blocks .block .description .title-module .offer-subtitle {
  font-weight: 300;
  font-size: 18px;
}
/* line 942, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul {
  position: absolute;
  width: 115px;
  right: 7px;
  top: 24px;
  text-align: right;
  padding-right: 10px;
}
/* line 950, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li {
  display: inline;
}
/* line 953, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a {
  background-color: #4C4C4C;
  top: -3px;
  color: white;
  padding: 8px 7px 6px 7px;
  right: 97px;
  position: absolute;
  text-align: center;
  text-decoration: none;
}
/* line 963, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a:hover {
  opacity: 0.8;
}
/* line 967, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.plus {
  padding: 10px 7px 5px;
  margin-right: -86px;
  height: 18px;
  background: #00B2C6;
}
/* line 974, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play {
  padding: 10px 9px 5px;
}
/* line 977, ../sass/_template_specific.scss */
.scapes-blocks .block .description ul li a.play img {
  margin-top: 2px;
}
/* line 984, ../sass/_template_specific.scss */
.scapes-blocks .block .description p {
  margin-bottom: 0;
}

/* line 990, ../sass/_template_specific.scss */
.en a.button-promotion.oferta-reserva {
  width: 82px;
  right: 114px !important;
}

/* line 995, ../sass/_template_specific.scss */
.scapes-blocks .row1 {
  margin-right: 10px;
}

/* line 999, ../sass/_template_specific.scss */
.scapes-blocks .row2 {
  margin-left: 10px;
}

/* line 1003, ../sass/_template_specific.scss */
.scapes-bottom-content {
  background: #00B2C6;
  padding: 20px;
}

/* line 1008, ../sass/_template_specific.scss */
.scapes-popup {
  padding: 19px;
}

/* line 1014, ../sass/_template_specific.scss */
.escapadas-popup h3 {
  color: #00B2C6;
  margin-bottom: 20px;
}
/* line 1018, ../sass/_template_specific.scss */
.escapadas-popup h5 {
  color: #00B2C6;
}

/* line 1023, ../sass/_template_specific.scss */
.offer_popup {
  padding: 25px;
}

/*======= Content Access =====*/
/* line 1029, ../sass/_template_specific.scss */
.content_access h3.section-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #5CACDB;
  margin-bottom: 28px;
}
/* line 1036, ../sass/_template_specific.scss */
.content_access h3.section-title + div {
  font-size: 17px;
  font-weight: lighter;
  line-height: 29px;
  color: #636363;
  text-align: center;
}
/* line 1045, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields {
  text-align: center;
}
/* line 1049, ../sass/_template_specific.scss */
.content_access form#my-bookings-form {
  text-align: center;
  padding-bottom: 1px;
}
/* line 1054, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields {
  margin-top: 20px;
}
/* line 1057, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields label {
  display: block;
  line-height: 18px;
  font-size: 17px;
  font-weight: lighter;
  color: #636363;
  text-align: center;
}
/* line 1066, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input {
  width: 160px;
  text-align: center;
}
/* line 1071, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields input#emailInput {
  margin-bottom: 6px;
}
/* line 1075, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button {
  display: block;
  margin: 20px auto 0;
  width: 165px;
  border: 0;
  background: #007DAD;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
}
/* line 1086, ../sass/_template_specific.scss */
.content_access #my-bookings-form-fields button#my-bookings-form-search-button:hover {
  opacity: 0.8;
}
/* line 1092, ../sass/_template_specific.scss */
.content_access button#cancelButton {
  display: block;
  margin: 20px auto 0;
  width: 165px;
  border: 0;
  background: #007DAD;
  color: white;
  text-transform: uppercase;
  padding: 7px;
  cursor: pointer;
  display: none;
}

/*========= Location and Contact ======*/
/* line 1108, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1115, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1122, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 75px;
}

/* line 1130, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #00B2C6;
  width: 95%;
  line-height: 20px;
}

/* line 1143, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1147, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1151, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;
}

/* line 1160, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1164, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1168, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1176, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1181, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;
}

/* line 1191, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

/* line 1201, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

/* line 1211, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 1216, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #00B2C6 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

/* line 1232, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #00B2C6 !important;
}

/* line 1236, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #717171;
  line-height: 30px;
}

/* line 1245, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1251, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #00B2C6;
}

/* line 34, ../sass/styles_demo6.scss */
#logoDiv img {
  height: auto !important;
  top: 17px !important;
  position: relative !important;
}

/* line 41, ../sass/styles_demo6.scss */
.bottom_phrase h3.title {
  color: #00B2C6 !important;
}

/* line 45, ../sass/styles_demo6.scss */
.bannersx3_wrapper .banner_element .circle {
  background: rgba(181, 164, 145, 0.5) !important;
}
