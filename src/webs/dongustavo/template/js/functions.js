$(function () {

    //$("#pikame").PikaChoose({showTooltips: true, carousel: false});

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".myFancyPopupMaps").fancybox({
        width: 600,
        height: 'auto',
        fitToView: false,
        autoSize: false,
        wrapCSS: "fancy_maps"
    });

    $(".room_popup").fancybox({
        width: 600,
        height: 'auto',
        fitToView: false,
        autoSize: false,
        wrapCSS: "fancy_rooms"
    });


    $(".myFancyPopupAutoOffer").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false,
        padding: 10
    });

    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");
        }
    });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    responsive_slider();
    $(window).on('resize', function () {
        responsive_slider();
    });

    $("#mainMenuDiv .sections_menu").click(function(){
        if ($(this).has("ul")){
            $(this).find("ul").slideToggle();
        }
    })

    $(".filter_gallery_element").first().trigger('click');

    $(".button-promotion").click(function(){
        updateDates($.datepicker.formatDate("dd/mm/yy", new Date()));
    });

    var max_height_columns = 0;
    $(".footer_column").each(function(){
        if (max_height_columns < $(this).height())  max_height_columns = $(this).height();
    })

    $(".footer_column").height(max_height_columns);
});

$(window).load(function(){
    showMenu();
    $(window).bind('scroll', function () {
        showMenu();
    });
});

(function () {
    var po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
})();

function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}

function responsive_slider() {
    $("section#slider_container").height($(window).height() * 0.8);
    position_slider = $("#full_wrapper_booking").position().top;
    $("#slider_container > div.forcefullwidth_wrapper_tp_banner > div.tp-banner-container > div.tp-bullets.simplebullets.round.hidebullets").css({top: position_slider - 40});
}

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height();
    if (!slider_height) {
        slider_height = $("#inner_slider_container").height();
    }
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("#full_wrapper_booking").slideDown().addClass('floating_booking').addClass('showed').find(".room_list_wrapper").addClass("floating_room_list");
        $("#ui-datepicker-div").addClass("fixed_datepicker");
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed').find(".room_list_wrapper").removeClass("floating_room_list");
        $("#ui-datepicker-div").removeClass("fixed_datepicker");
    }
}