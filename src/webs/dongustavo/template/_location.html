{% if location_html.content %}
    <div class="location-info-and-form-wrapper">

        <div class="column6 aditional-info">

            <div class="pre-indication info_main">
                <div class="titular"><h3>{{left_content.subtitle|safe}}</h3></div>
                <div class="exceded">
                    <img src="{{ main_content.pictures.0 }}=s1900" alt=""/>
                </div>
            </div>

            <div class="indication" style="display:none">
                <div class="titular">
                    <h3>{{banners1.1.title}}</h3>
                </div>
                <table class="instructions-container">
                    {% for banner in banners2 %}
                    <tr class="{% cycle 'uno' 'dos' %}">
                        <td>{{banner.hotelName}}</td>
                    </tr>
                    {% endfor %}
                </table>

            </div>

        </div>

        <div class="location-info column6">
                <div class="location_description_wrapper">
                    {{ location_html.content|safe }}
                </div>
            </div>
    </div>
{% endif %}


<div class="form-contact location_section">
    {{ contact_html }}
</div>

<div class="iframe_google_maps">
    <div class="content_iframe">
        {{ iframe_google_map.content|safe }}
    </div>
</div>

<script>
    $(function(){
        var location_text_height = $(".location-info").outerHeight();
        $(".info_main").height(location_text_height);
    });
</script>