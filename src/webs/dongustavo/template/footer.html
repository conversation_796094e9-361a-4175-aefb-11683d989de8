<footer>
    <div class="social_wrapper">
        <div class="content_social container12">
            {% if footer_logos %}<div class="footer_logos">
                {% for logo  in footer_logos %}
                    <a {% if logo.linkUrl %}href=""{% endif %}><img src="{{ logo.servingUrl|safe }}=s700"></a>
                {% endfor %}
            </div>{% endif %}
            <div class="social_box">
                <div class="social_title">{{ T_siguenos_en }}</div>

                {%if facebook_id %}
                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                    <i class="fa fa-facebook"></i>
                </a>
                {% endif %}
                {% if twitter_id %}
                <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                    <i class="fa fa-twitter"></i>
                </a>
                {% endif %}
                {% if flickr_id %}
                <a href="http://www.flickr.com/photos/{{flickr_id}}/" target="_blank">
                    <i class="fa fa-flickr"></i>
                </a>
                {% endif %}
                {% if youtube_id %}
                <a href="https://www.youtube.com/user/{{youtube_id}}" target="_blank">
                    <i class="fa fa-youtube"></i>
                </a>
                {% endif %}
                {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                    <i class="fa fa-google-plus"></i>
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    {% if awards_banners %}
        <div class="awards_banners_wrapper">
            <div class="container12">
                {% for x in awards_banners %}
                    <div class="award_element">
                        <img src="{{ x.servingUrl|safe }}" alt=""/>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <div class="wrapper_footer_columns container12">
        <div class="footer_column left column4">
            <div class="footer_column_description">{{ footer_columns.0.description|safe }}</div>
        </div>

        <div class="footer_column column4">
            {% for x in links_footer %}
                <a class="footer_element" href="{{host|safe}}/{{seoLinkString}}{{x.linkUrl|safe}}">{{ x.title|safe }}</a>
            {% endfor %}
        </div>

        <div class="footer_column right column4">
            <div class="footer_column_description">
                {{ footer_columns.1.description|safe }}
                {% include "newsletter.html" %}
            </div>
        </div>
    </div>

     <div class="full-copyright">
         {% if texto_legal %}
            <div id="div-txt-copyright" class="footer-copyright container12">
              {{texto_legal|safe}} © {% now "Y" %} |
            </div>
          {% endif %}

        <div class="footer-copyright container12">
            <div id="social-widgets">
                <div id="google">{{ footer_columns.4.description|safe }}</div>
                <div id="facebook">{{ footer_columns.5.description|safe }}</div>
            </div>
            {% for x in policies_section %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe" rel="nofollow">{{ x.title|safe }}</a> |
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html" title="Motores reserva hoteles">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>
        </div>

    </div>
</footer>