
<header>
    <div id="wrapper-header" class="container12">

        <div id="logoDiv" class="column3">
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}=s300" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>

        <div class="top_header column9">
            <div class="left_header">
                {% for x in ticks_header %}
                    <div class="tick_header_element">
                        {% if x.description %}
                            <i class="fa {{ x.description|safe }}"></i>
                        {% else %}
                            <img class="icon" src="{{ x.servingUrl|safe }}" alt=""/>
                        {% endif %}
                        {% if x.linkUrl %}
                            <a href="{{ x.linkUrl|safe }}">{{ x.title|safe }}</a>
                        {% else %}
                            <span{% if '9' in x.title %} class="phone"{% endif %}>{{ x.title|safe }}</span>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

            <div class="right_header">
                <div id="lang" class="lang2">
                    <ul id="language-selector-options" class="language-selector-options2">
                        {% for key, language in language_codes.items %}
                            <li class="language-option-flag {% if language == language_selected %}language_selected{% endif %}">
                                <a hreflang="{{ key }}"
                                   href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{{ language|slice:":3" }}</a>
                            </li>
                            {% if not forloop.last %}
                                <span class="separator">·</span>
                            {% endif %}
                        {% endfor %}
                    </ul>
                </div>

                <div class="separator">|</div>

                <div class="top_sections_wrapper">
                    {% for x in top_sections %}
                        <div class="top_element">
                            <a href="{{ host|safe }}/{{ x.friendlyUrl }}">{{ x.title|safe }}</a>
                        </div>
                    {% endfor %}

                </div>
            </div>
        </div>

        <div id="mainMenuDiv" class="column9">
            {% include "main_div.html" %}
        </div>
    </div>
</header>