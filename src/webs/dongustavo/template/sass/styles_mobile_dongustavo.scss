@import "defaults";
@import "styles_mobile/1/1";
@import "styles_mobile/1/carousel_icon";
$title_family: 'Roboto', sans-serif;
@import "styles_mobile/1/bannersx2";
@import "styles_mobile/1/cycle_banners_x3";

header {
  background: $corporate_1;
  .mailto, .telefono {
    i {
      color: white;
    }
  }
}

.fancybox-slide--iframe .fancybox-content{
  width: 100%!important;
}

.booking_room_button_element, .mobile_engine .mobile_engine_action {
  background: $corporate_3;
}

.nights_number_wrapper_personalized {
  span.days_number_datepicker, span.night_label {
    color: $corporate_1;
  }
}

#full_wrapper_booking {
  .wrapper_booking_button {
    .submit_button {
      background: $corporate_1;
    }
  }
}

.breadcrumbs, .nights_number_wrapper_personalized {
  background-color: $corporate_2;
  a, i {
    color: $corporate_1;
  }
}

.main_menu {
  background-color: $corporate_1;
}

.container_popup_booking {
  img {
    background-color: $corporate_1;
  }
}

body {
  .section_content {
    h2.section_title {
      margin-top: 0.5em;
    }
    .content {
      padding: 1.250em 1.250em 3.125em;
    }
  }
}

.location_content .contact_content_element {
    font-size: 16px;
    padding: 0 1em;
    box-sizing:  border-box;
}
.carousel_icon {
  bottom: 0em;
  top: 1em;
  .owl-item {
    .icon {
      @include center_xy;
    }
    span {
      color: white;
    }
  }
  .owl-item:nth-child(even) {
    background-color: $corporate_1;
  }
  .owl-item:nth-child(odd) {
    background-color: $corporate_2;
  }
}

#little-block {
  div {
    margin-top: 1em;
    span {
      background: $corporate_3;
      display: block;
      padding: 1em;
      margin-bottom: 0.5em;
    }
    a {
      color: white;
    }
  }
}

#full_wrapper_booking {
  .destination_wrapper {
    background: white !important;
    margin-bottom: 10px;
    width: calc(100% - 20px);
    position: relative;
    &:before {
      content: '\f278';
      display: block;
      font-family: "FontAwesome", sans-serif;
      font-size: 14px;
      color: #666;
      @include center_y;
      left: 7px;
      z-index: 2;
    }
    &:after {
      content: '\f078';
      display: block;
      font-family: "FontAwesome", sans-serif;
      font-size: 18px;
      color: #666;
      @include center_y;
      right: 7px;
      z-index: 2;
    }
    select {
      width: 100%;
      height: 45px;
      padding-left: 35px;
      box-sizing: border-box;
    }
  }
}

.rooms_wrapper {
    .room_block {
        .buttons {
            a {
                background-color: $corporate_1;
            }
            a:nth-child(2) {
                background-color: $corporate_3;
            }
        }
    }
}
.smaller {
    font-size: 15px;
}