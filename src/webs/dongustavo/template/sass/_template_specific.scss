/*=== General ===*/
body {
  font-family: 'Raleway', sans-serif;
}

body.interior {
  section#inner_slider_container {
    height: 468px;
    //overflow: hidden;
    width: 100%;
    display: inline-block;
    position: relative;

    .exceded_image_slider {
      height: 468px;
      overflow: hidden;
      position: relative;

      img {
        @include center_xy;
        max-width: none;
        min-width: 100%;
      }

      .overlay_top {
        position: absolute;
        top: 0;
        //background: rgba(black, 0.6);
        width: 100%;
        height: 150px;
        z-index: 100;
        background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
        background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, rgba(0, 0, 0, 0) 100%);
      }
    }

    #full_wrapper_booking {
      bottom: 0;
      top: auto;
      background: rgba(black, 0.6);
      width: 100%;
    }
  }
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

.wrapper-new-web-support.booking_form_title {
  position: relative !important;
  bottom: auto;
  top: auto;
  background: none !important;
  left: 0 !important;
  padding: 10px !important;
  padding-bottom: 0 !important;
}

.fancy-data #full-booking-engine-html-5 .wrapper-new-web-support.booking_form_title {
  margin-top: 0 !important;
  padding-bottom: 10px !important;
}

/*=== Header ===*/
header {
  width: 100%;
  z-index: 101;
  background: transparent;
  min-width: 1140px;
  top: 0;
  position: absolute;

  div#logoDiv {
    margin-top: 14px;
    margin-left: 0;
  }

  .left_header {
    float: left;
    padding: 3px 0;

    .tick_header_element {
      display: inline-block;
      vertical-align: middle;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }

      .icon, i.fa {
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        color: $corporate_1;
        font-size: 20px;
      }

      a, span {
        display: inline-block;
        vertical-align: middle;
        color: white;
        text-decoration: none;
        font-size: 14px;

        &.phone {
          font-size: 18px;
        }
      }

      a:hover {
        color: $corporate_1;
      }
    }
  }

  .right_header {
    float: right;

    .separator {
      display: inline-block;
      vertical-align: middle;
    }

    .link_maps {
      display: inline-block;
      vertical-align: middle;

      a {
        text-decoration: none;
        color: white;
        font-size: 14px;

        &:hover {
          color: $corporate_1;
        }
      }
    }

    .top_sections_wrapper {
      display: inline-block;
      vertical-align: middle;

      .top_element {
        display: inline-block;
        vertical-align: middle;

        a {
          display: inline-block;
          color: white;
          text-decoration: none;
          font-size: 14px;

          &:hover {
            color: $corporate_1;
          }
        }
      }
    }
  }
}

.top_header {
  text-align: right;
  margin-top: 14px;
  float: right;
  margin-right: 0;
  color: white;
  font-weight: lighter;
  font-size: 14px;
}

#lang {
  display: inline-block;
  vertical-align: middle;

  ul li {
    text-align: left;
    //width: 80px;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;
    display: inline-block;
    color: white;

    a {
      color: white;
      text-decoration: none;
      font-size: 14px;
      text-transform: uppercase;
    }

    &.language_selected a {
      color: $corporate_1;
      font-weight: bold;
    }
  }
}

/*===== Menu =====*/
#mainMenuDiv {
  margin-top: 15px;
  float: right;
  margin-right: 0;

  ul {
    text-align: justify;

    &:after {
      content: "";
      width: 100%;
      display: inline-block;
      height: 0;
    }

    .separator {
      display: inline-block;
      height: 25px;
      border-left: 1px solid white;
      vertical-align: middle;
    }

    li {
      display: inline-block;
      text-align: center;
      position: relative;

      &:first-of-type {
        padding-left: 0;
      }

      &:nth-last-of-type(2), &:last-of-type {
        border-right: 0;
      }

      &:last-of-type {
        padding-right: 0;
      }

      a {
        text-decoration: none;
        font-size: 16px;
        font-weight: lighter;
        color: white;
        padding: 6px 0 5px;

        &.button-promotion {
          color: white !important;
          background: $corporate_1;
          font-weight: bold;
          font-size: 12px;
          text-transform: uppercase;
          padding: 9px 25px;

          &:hover {
            background: lighten($corporate_1, 10%);
          }
        }

        .icon_home {
          vertical-align: middle;
        }
      }

      &:hover a {
        color: $corporate_1;
      }

      &#section-active a, {
        color: $corporate_1;
        i.fa {
          display: inline-block;
          vertical-align: middle;
          font-size: 30px;
        }
      }

      ul {
        display: none;
        position: absolute;
        top: 30px;
        left: -10px;
        padding: 10px;
        background: rgba(black, 0.65);
        width: 125px;

        &:after {
          display: none;
        }

        &:before {
          content: "";
          display: block;
          border-bottom: 10px solid rgba(black, 0.65);
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          position: absolute;
          bottom: 100%;
          left: 25px;
        }

        .separator_submenu {
          display: block;
          width: 100%;
          border-bottom: 1px solid white;
          margin: 5px 0;
        }

        li {
          a {
            color: white !important;

            &#subsection-active {
              color: $corporate_1 !important;
            }

            &:hover {
              color: $corporate_1 !important;
            }
          }
        }
      }
    }
  }
}

/*=== Slider ===*/
#slider_container {
  position: relative;
  overflow: hidden;
  .forcefullwidth_wrapper_tp_banner .tp-banner-container {

    .tp-banner {
      z-index: 0;
    }
  }
}

.tp-bullets {
  bottom: 50% !important;
  top: auto !important;
  opacity: 1 !important;
}

.tp-bullets .bullet {
  background: url("/img/#{$base_web}/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/#{$base_web}/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

.slide_inner {
  height: 630px;
  width: 100%;
  overflow: hidden;
  display: inline-block;

  img {
    width: 100%;
    display: block;
  }
}

.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

/*======= Content Access =====*/
.my-bookings-booking-info {
  margin: 0 auto !important;
}

.content_access {
  padding-top: 60px;
  padding-bottom: 60px;
  background: $corporate_2;
  display: inline-block;
  width: 100%;
  margin-top: -5px;

  h3.section-title {
    font-size: 22px;
    text-align: center;
    text-transform: uppercase;
    color: $corporate_1;
    margin-bottom: 30px;

    & + div {
      font-size: 14px;
      color: #000000;
      text-align: center;
      width: 80%;
      margin: 0 auto;
      line-height: 26px;
    }
  }

  #my-bookings-form {
  margin-top:80px;
  #reservation {
    margin-top: 0 !important;

    .modify_reservation_widget {
      margin-top: 40px;
      margin-bottom: 0;
    }

    .my-bookings-booking-info {
      margin: 40px auto 0;

      .fResumenReserva {
        margin: auto;
      }
    }
  }

  #my-bookings-form-fields {
    label {
      display: block;
      text-align: center;
      text-transform: uppercase;
      font-weight: 100;
    }

    input {
      display: block;
      width: 300px;
      margin: 10px auto;
      height: 40px;
      text-align: center;
      font-size: 14px;
      border: 1px solid $corporate_1;
    }

    ul {
      text-align: center;
      margin-top: 30px;

      li {
        display: inline-block;
        width: 200px;
        vertical-align: middle;

        button {
          height: 40px;
          text-transform: uppercase;
          font-size: 16px;
          color: white;
          border: 0;
          cursor: pointer;
          width: 100%;
          font-weight: 100;
          @include transition(background, .4s);

          &.modify-reservation {
            background: darken($corporate_2, 10%);

            &:hover {
              background: darken($corporate_2, 20%);
            }
          }

          &.searchForReservation {
            background: $corporate_3;

            &:hover {
              background: darken($corporate_3, 10%);
            }
          }
        }
      }
    }
  }

  #cancelButton {
    display: none;
    background: $corporate_3;
    height: 40px;
    text-transform: uppercase;
    font-size: 16px;
    color: white;
    border: 0;
    cursor: pointer;
    width: 200px;
    font-weight: 100;
    margin: 40px auto 0;
    @include transition(background, .4s);

    &:hover {
      background: darken($corporate_3, 10%);
    }
  }
}

.my-bookings-booking-info {
  margin: auto;
}
}

/*========= Location and Contact ======*/
.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }
}

.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  background: $corporate_1;
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 20px;
  margin-top: 30px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: $corporate_1;
  width: 95%;
  line-height: 20px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 60px;
}

.map_default_visible {
  margin-top: 50px;
}

div#map-canvas {
  height: 350px !important;
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  //color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  //color: white;
  margin-right: 35px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0 !important;
  height: 30px !important;
  width: 130px !important;
  background-color: $corporate_2 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0 !important;
  line-height: 32px;

  &:hover {
    opacity: 0.8;
  }
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #717171;
  line-height: 30px;
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

/*======= Location and Contact =========*/
.location_block_separator {
  display: block;
  width: 100%;
  margin: 60px 0;
}

.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }
}

.iframe-google-maps-wrapper {
  iframe {
    width: 100%;
  }
}

.location-info-and-form-wrapper {
  margin-top: 35px;
  display: table;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 30px;
}

.aditional-info {
  float: left;
  margin-left: 0;
  margin-right: 0;
  width: 570px !important;
  position: relative;
  overflow: hidden;

  .info_main img {
    min-width: 100%;
    max-width: none;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    max-width: initial;
  }
}

.location-info-and-form-wrapper h1 {
  font-family: 'Lato', sans-serif;
  font-size: 33px;
  color: #2c73b5;
  font-weight: 100;
  margin-bottom: 35px;
  position: relative;
  padding: 0 20px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;
}

li.how-to-go {
  cursor: pointer;
  color: $corporate_1;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
  .car {
    background: url("/img/amera/icons_maps/car.png") left center no-repeat;
  }
}

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 6px;
  font-size: 17px;
  font-weight: 100;
  //color: white;

  &:first-of-type {
    margin-top: 0;
  }
}

#contactContent .info {
  margin-top: 5px !important;
  padding-left: 32px !important;
  padding-top: 25px;
  background: #efefef;
  background: $corporate_2;
  box-sizing: border-box;
  width: 100%;
  display: block;
  padding-bottom: 35px;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: black;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  border: 0;
  background-color: white;
  color: black;
  width: 1070px;
  margin-right: 0;
}

.form-contact #contact-button-wrapper {
  padding-right: 0 !important;
  margin-right: 0;
}

.form-contact #contact-button {
  border-radius: 0 !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate_1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0 !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0 !important;
  line-height: 32px;

  &:hover {
    opacity: 0.8;
  }
}

.location-info {
  font-weight: 300;
  box-sizing: border-box;
  font-size: 14px;
  color: #4b4b4b;
  line-height: 25px;
  padding: 30px 0;

  h1 {
    //margin-bottom: 15px;
  }
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

ul.location_destiny {
  margin-top: 40px;

  li {
    padding: 10px 0 10px 35px;
    background: url('/img/#{$base_web}/destiny.png') left center no-repeat;
    cursor: pointer;

    a {
      text-decoration: none;
      color: $corporate-1;
    }
    a.active {
      color: white;
    }
  }
  .car {
    background: url('/img/#{$base_web}/car.png') left center no-repeat;
  }
  .walk {
    background: url('/img/#{$base_web}/walk.png') left center no-repeat;
  }
}

.form-contact.location_section {
  h1 {
    width: 100%;
    padding: 10px 30px;
    background-color: #e2ebf4;
    color: #2c73b5;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: 100;
    border-bottom: 2px solid white;
    box-sizing: border-box;
  }
}

.location_description_wrapper {
  color: white;
  padding: 0 55px;
  font-weight: 100;
  line-height: 24px;
  font-size: 16px;
}

#contactContent .info {
  padding-right: 32px;
  margin-bottom: 30px;

  .contInput {
    margin-bottom: 10px;
    display: inline-block;
    width: auto;
    clear: both;

    &:nth-of-type(2), &:nth-of-type(4) {
      float: right;
    }
  }
}

.iframe_google_maps {
  margin-bottom: 30px;
}

.destacados_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;
  visibility: hidden;
  overflow: hidden;

  .destacados_title {
    text-align: center;
    background: $corporate_1;
    text-transform: uppercase;
    color: white;
    padding: 10px 0;
    margin-bottom: 10px;
  }

  .destacados_content {
    display: inline-block;
    width: 100%;
    height: 230px;
    &.owl-carousel .owl-nav {display: block}
    .owl-prev, .owl-next  {
      width: 80px;
      height: 80px;
      border-radius:50%;
      @include center_y;
      left: -40px;
      background: rgba(0,0,0,0.6);
      &:after {
        content: '';
        background-color: white;
        width: 40px;
        height: 80px;
        position: absolute;
        top:0;
        left: 0;
      }
      i.fa {
        @include center_xy;
        font-size: 25px;
        color: $corporate_1;
        margin-left: 15px;
      }
    }

    .owl-next {
      left: auto;
      right: -40px;
      &:after {
        left: auto;right: 0;
      }
      i.fa {
        margin-left: -15px;
        content: '\f105';
      }
    }

    a {
      display: inline-block;
      text-decoration: none;
    }

    .destacado_element {
      display: inline-block;
      width: 100%;
      height: 230px;
      float: left;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      margin-right: 7px;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        a {
          opacity: .8;
        }
      }

      .destacado_image {
        display: inline-block;
        height: 100%;

        img {
          @include center_xy;
        }
      }

      .destacado_icon {
        @include center_xy;
      }
    }
  }
}

.home_content_wrapper {
  background: $corporate_2;
  position: relative;
  padding: 20px 0;
  display: inline-block;
  width: 100%;
  height: 370px;
  margin: 60px 0;

  .home_box {
    position: relative;
  }

  .prev_arrow {
    width: 27px;
    height: 45px;
    @include center_y;
    -webkit-transform: translate(0%, -50%) rotate(180deg);
    -moz-transform: translate(0%, -50%) rotate(180deg);
    -ms-transform: translate(0%, -50%) rotate(180deg);
    -o-transform: translate(0%, -50%) rotate(180deg);
    transform: translate(0%, -50%) rotate(180deg);
    left: 0;
    background: url(/img/#{$base_web}/home_right.png);;
  }

  .next_arrow {
    width: 27px;
    height: 45px;
    @include center_y;
    right: 0;
    background: url(/img/#{$base_web}/home_right.png);;
  }

  .home_box {
    .home_element {
      position: relative;
      height: 370px;
    }

    .content_element {
      @include center_y;
      width: 100%;
      text-align: center;

      .title_element {
        color: $corporate_1;
        font-size: 22px;
        margin-bottom: 28px;

        .subtitle {
          display: block;
          width: 100%;
          font-weight: bold;
          font-size: 14px;
          color: black;
          margin-top: 28px;
        }
      }

      .description_element {
        width: 80%;
        font-size: 14px;
        margin: 0 auto;
      }
    }
  }
}

.flex_banner_wrapper {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  background-color: white;
  .owl-carousel .owl-nav {display: block}
    .owl-prev, .owl-next  {
      width: 120px;
      height: 120px;
      border-radius:50%;
      @include center_y;
      left: -60px;
      background: rgba(0,0,0,0.6);
      i.fa {
        @include center_xy;
        font-size: 40px;
        color: $corporate_1;
        margin-left: 20px;
      }
    }

    .owl-next {
      left: auto;
      right: -60px;
      i.fa {
        margin-left: -20px;
        content: '\f105';
      }
    }

  .flex_element {
    position: relative;
    transition: all 0.3s ease;
    visibility: hidden;
    height: 300px;

    &:hover {
      .flex_overlay {
        box-shadow: inset 0 0 0 400px rgba($corporate_1, 0.6);
      }

      .flex_content {
        border: 1px solid white;
        padding: 10px;
        text-align: center;
        background-position-y: 50px;
        &:after {
          content: '\f067';
          font-size: 20px;
          color: white;
          display: inline-block;
          font-family: "fontawesome", sans-serif;
        }
      }
    }

    .flex_overlay {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      transition: all 1.5s ease;
    }

    .flex_content {
      @include center_xy;
      z-index: 1;
      transition: border 1.5s ease, padding .5s ease;
      box-sizing: border-box;
      border-color: white;
      display: table;

      .flex_title {
        color: white;
        font-size: 24px;
      }
    }
  }
}

.ticks_wrapper {
  position: absolute;
  bottom: 0;
  z-index: 101;
  width: 100%;
  color: #ffffff;
  padding-bottom: 30px;

  .ticks_element {
    display: inline-block;
    width: calc(100% / 3 - 10px);
    text-align: center;
    float: left;
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }

    .title_tick {
      text-transform: uppercase;
      font-size: 25px;
      color: white;
      font-weight: bold;
    }

    .description_tick {
      font-weight: normal;
      width: 80%;
      color: white;
      margin: 0 auto;
    }
  }
}

.overlay_top {
  position: absolute;
  top: 0;
  //background: rgba(black, 0.6);
  width: 100%;
  height: 150px;
  z-index: 100;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, transparent 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.85) 35%, rgba(0, 0, 0, 0.85) 25%, rgba(0, 0, 0, 0) 100%);
}

.overlay_bottom {
  position: absolute;
  bottom: 0;
  //background: rgba(black, 0.6);
  width: 100%;
  height: 150px;
  z-index: 100;
}
.slider_x2_wrapper {
  margin-bottom:20px;
  .slider_x2 {
    .hotel_element {
      position: relative;
      height: 345px;
      width: 100%;
      overflow: hidden;

      &:hover {
        .center_block {
          width: 100%;
        }
        .hotel_image {
          img {
            opacity: .6;
          }
        }
      }

      .center_block {
        @include center_xy;
        @include transition(all, .6s);
        background: rgba(black, 0.6);
        z-index: 1;
        padding: 10px;
        color: white;
        width: 65%;
        box-sizing: border-box;
        text-align: center;
        text-transform: uppercase;
        font-size: 22px;

        .icon_hotel {
          display: block;
          margin: 10px auto 0;
        }

        i.fa {
          display: block;
          text-align: center;
          font-size: 28px;
          margin-top: 10px;
          color: $corporate_1;
        }

        a {
          text-decoration: none;
          color: white;
          display: inline-block;
        }
      }

      .hotel_image {
        display: table;
        width: 100%;
        height: 100%;
        background-color:$corporate_1;
        img {
          @include center_y;
          @include transition(all, .6s);
          width: 100%;
        }
      }

      .hotel_title {
        position: absolute;
        bottom: 0;
        width: 100%;
        box-sizing: border-box;
        padding: 10px 0;
        background: rgba(black, 0.8);
        color: white;
        text-align: center;
        text-transform: uppercase;

        a {
          text-decoration: none;
          color: $corporate_1;

          &:hover {
            opacity: .8;
          }
        }
      }
    }
  }
}
.rooms_home_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;

  .hotel_block_wrapper {
    display: inline-block;
    width: 49%;
    height: 345px;
    float: left;
    box-sizing: border-box;
    visibility: hidden;

    .flex_hotel {
      position: relative;
      height: 345px;
      width: 100%;

      .center_block {
        @include center_xy;
        background: rgba(black, 0.6);
        z-index: 1;
        padding: 10px;
        color: white;
        width: 65%;
        box-sizing: border-box;
        text-align: center;
        text-transform: uppercase;
        font-size: 22px;

        .icon_hotel {
          display: block;
          margin: 10px auto 0;
        }

        a {
          text-decoration: none;
          color: white;
          display: inline-block;
        }
      }

      .hotel_element {
        position: relative;
        height: 345px;
        width: 100%;
        overflow: hidden;

        .hotel_image {
          img {
            @include center_y;
            width: 100%;
          }
        }

        .hotel_title {
          position: absolute;
          bottom: 0;
          width: 100%;
          box-sizing: border-box;
          padding: 10px 0;
          background: rgba(black, 0.8);
          color: white;
          text-align: center;
          text-transform: uppercase;

          a {
            text-decoration: none;
            color: $corporate_1;

            &:hover {
              opacity: .8;
            }
          }
        }
      }

      .prev_arrow {
        width: 40px;
        height: 80px;
        @include center_y;
        left: 0;
        background: url(/img/#{$base_web}/block_left.png);
      }

      .next_arrow {
        width: 40px;
        height: 80px;
        @include center_y;
        right: 0;
        background: url(/img/#{$base_web}/block_right.png);
      }
    }
  }

  .apartamento_block_wrapper {
    display: inline-block;
    width: 49%;
    height: 345px;
    float: right;
    box-sizing: border-box;
    visibility: hidden;

    .flex_apartamento {
      position: relative;
      height: 345px;
      width: 100%;

      .center_block {
        @include center_xy;
        background: rgba(black, 0.6);
        z-index: 1;
        padding: 10px;
        color: white;
        width: 65%;
        box-sizing: border-box;
        text-align: center;
        text-transform: uppercase;
        font-size: 22px;

        .icon_apartamento {
          display: block;
          margin: 10px auto 0;
        }

        a {
          text-decoration: none;
          color: white;
          display: inline-block;
        }
      }

      .apartamento_element {
        position: relative;
        height: 345px;
        width: 100%;
        overflow: hidden;

        .apartamento_image {
          img {
            @include center_y;
            width: 100%;
          }
        }

        .apartamento_title {
          position: absolute;
          bottom: 0;
          width: 100%;
          box-sizing: border-box;
          padding: 10px 0;
          background: rgba(black, 0.8);
          color: white;
          text-align: center;
          text-transform: uppercase;

          a {
            text-decoration: none;
            color: $corporate_1;

            &:hover {
              opacity: .8;
            }
          }
        }
      }

      .prev_arrow {
        width: 40px;
        height: 80px;
        @include center_y;
        left: 0;
        background: url(/img/#{$base_web}/block_left.png);;
      }

      .next_arrow {
        width: 40px;
        height: 80px;
        @include center_y;
        right: 0;
        background: url(/img/#{$base_web}/block_right.png);;
      }
    }
  }
}

footer {
  background: $corporate_1;

  .wrapper_footer_columns {
    padding: 40px 0;

    .footer_column {
      color: #ffffff;
      text-align: center;
      border-right: 1px solid white;
      font-weight: lighter;
      box-sizing: border-box;
      //min-height: 170px;
      margin: 0;
      width: calc(100% / 3);

      &:first-child {
        border-left: 1px solid white;
      }

      .footer_element {
        display: block;
        text-align: center;
        text-decoration: none;
        color: white;
        margin-bottom: 5px;

        &:hover {
          opacity: .8;
        }
      }

      .footer_column_description {
        width: 80%;
        margin: 0 auto;

        #suscEmail, #suscName {
          width: 100%;
          border: 0;
          padding: 15px 10px;
          box-sizing: border-box;
          background: rgba(white, 0.3);
          color: white;
          margin-bottom: 5px;
          text-align: center;

          &::-webkit-input-placeholder {
            color: white;
          }

          &:-moz-placeholder {
            /* Firefox 18- */
            color: white;
          }

          &::-moz-placeholder {
            /* Firefox 19+ */
            color: white;
          }

          &:-ms-input-placeholder {
            color: white;
          }
        }

        #newsletter-button {
          width: 100%;
          border: 0;
          background: white;
          color: $corporate_1;
          padding: 15px 0;
          text-transform: uppercase;
          cursor: pointer;

          &:hover {
            opacity: .8;
          }
        }
      }
    }
  }

  #newsletter {
    margin-top: 20px;

    #title_newsletter, #suscEmailLabel {
      display: none !important;
    }

    .newsletter_checkbox {
      margin-top: 5px;
      font-size: 12px;

      a {
        color: white;
        text-decoration: underline;
      }
    }
  }

  .full-copyright {
    text-align: center;

    .footer-copyright {
      display: inline-block;
      color: white;
      font-size: 12px;
      font-weight: lighter;
      margin-bottom: 15px;

      a {
        color: white;
        text-decoration: none;
      }
    }
  }

  #facebook_like {
    display: inline-block;
  }

  #google_plus_one {
    display: inline-block;
    vertical-align: middle;
  }

  .social_wrapper {
    background: $corporate_2;
    display: table;
    width: 100%;

    .content_social {
      display: table;
      padding:60px 0;

      .footer_logos {
        display:table-cell;
        vertical-align: middle;
        text-align: center;
        width: 750px;
      }
      .social_box {
        display:table-cell;
        vertical-align: middle;
        text-align: center;

        .social_title {
          font-size: 26px;
          color: $corporate_1;
          text-transform: uppercase;
          margin-bottom: 30px;
        }

        a {
          display: inline-block;
          vertical-align: middle;
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background-color: $corporate_1;
          color: white;
          position: relative;
          overflow: hidden;
          i.fa {
            @include center_xy;
          }
        }
      }
    }
  }
}

/*====== Habitaciones ====*/
.rooms_wrapper {
  margin-top: 30px;
  margin-bottom: 60px;

  .room_element {
    height: auto;
    margin-bottom: 20px;
    width: 100%;
    display: table;

    .sub-description-rooms {
      font-weight: bold;
      margin-bottom: 15px;
    }

    h3.room_title {
      font-size: 24px;
      color: $corporate_1;
      margin-bottom: 12px;
      padding-left: 0 !important;

      span.capacity {
        font-weight: lighter;
        font-size: 16px;
        text-transform: capitalize;
        vertical-align: top;
        margin-top: 8px;
        display: inline-block;

        &.kids_wrapper {
          margin-top: 10px;
        }

        & > span {
          margin-right: 2px;
        }

        i.fa {
          color: $corporate_1;
        }
        .capacity_image {
          //vertical-align: middle;
          //padding-bottom: 4px;

          &.kids {
            padding-bottom: 0;
            padding-top: 1px;
            vertical-align: top;
          }
        }
      }

      span.plus_image {
        display: inline-block;
        vertical-align: bottom;
        line-height: 22px;
      }
    }

    .room_popup {
      display: inline-block;
      width: 33px;
      height: 33px;
      position: relative;
      background: $corporate_1;
      color: white;
      margin-right: 5px;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }

      .fa {
        @include center_xy;
      }
    }

    .room_description {
      padding-bottom: 11px;
      border-bottom: 1px solid #CECECE;
      margin-bottom: 33px;
      color: #656565;
      font-weight: 100;
      line-height: 24px;
      font-size: 16px;

      hide {
        display: none;
      }
    }

    .service_elements li {
      display: inline-block;
      padding-right: 20px;
      font-size: 13px;
      color: $corporate_1;
      font-weight: lighter;

      img {
        vertical-align: middle;
        margin-right: 5px;
      }
    }

    .exceded {
      width: 32%;
      height: 278px;
      float: left;
      position: relative;
      overflow: hidden;

      img.room_image {
        min-height: 100%;
        max-width: none;
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        max-height: 100%;
      }

      i.plus_image {
        position: absolute;
        left: 20px;
        top: 20px;
        z-index: 1;
        color: white;
        background-color: $corporate_3;
        padding: 10px;
      }
    }

    .room_description_wrapper {
      background: #F8F8F8;
      float: right;
      width: 68%;
      padding: 25px 40px;
      min-height: 278px;
      box-sizing: border-box;
      position: relative;
    }

    .service_elements {
      .divide_0, .divide_1, .divide_2 {
        width: 32%;
        display: inline-table;

        li {
          font-size: 13px;
          color: $corporate_2;
          font-weight: lighter;

          img {
            vertical-align: middle;
            margin-right: 5px;
          }
        }
      }
    }

    .room_buttons_wrapper {
      position: absolute;
      top: 17px;
      right: 40px;

      img {
        vertical-align: middle;
        width: auto;
        display: none;
        height: 37px;
      }

      .room_book {
        color: white;
        padding: 8px;
        background: $corporate_1;
        width: 117px;
        box-sizing: border-box;
        display: inline-block;
        text-align: center;
        text-decoration: none;
        text-transform: uppercase;
        float: right;
        height: 33px;

        &:hover {
          background: lighten($corporate_1, 10%);
        }
      }
    }

    .room_types {
      position: absolute;
      top: 16px;
      right: 27%;

      a {
        text-decoration: none;
      }
    }
  }
}

.room_type_wrapper, .room_complete_description {
  display: none;

  h3 {
    font-size: 24px;
    color: $corporate_1;
    margin-bottom: 12px;
  }

  & > div {
    font-weight: lighter;
    font-size: 14px;
    line-height: 24px;
    color: gray;
  }
}

/* Rooms icons tooltips */
.tooltip {
  display: inline;
  position: relative;
  cursor: pointer;
}

.tooltip:hover:after {
  background: $corporate-1;
  border-radius: 5px;
  bottom: 26px;
  color: white;
  content: attr(title);
  left: 20%;
  top: -54px;
  height: 22px;
  padding: 5px 15px;
  position: absolute;
  z-index: 98;
  text-align: center;
  text-transform: uppercase;
}

.content_subtitle_wrapper {
  padding-top: 60px;
  padding-bottom: 60px;
  display: inline-block;
  width: 100%;
  margin-top: -5px;

  &.no_background {
    background: transparent;
    padding-bottom: 30px;

    .subtitle_title {
      margin-bottom: 0;
    }
  }

  .subtitle_title {
    font-size: 22px;
    text-align: center;
    text-transform: uppercase;
    color: $corporate_1;
    margin-bottom: 30px;
  }

  .subtitle_description {
    font-size: 14px;
    color: #000000;
    width: 80%;
    margin: 0 auto;
    text-align: center;
    line-height: 26px;
  }
}

/*================  Banners x2 Cycle ===============*/
.bannerx2_image img {
  width: 100%;
}

.banners_x2_wrapper {
  margin-top: 30px;
  margin-bottom: 60px;
}

p.bannerx2_title_cycle {
  color: white;
  text-align: center;
  font-size: 20px;
  margin-bottom: 30px;
  font-weight: bold;
  text-transform: uppercase;

  &:after {
    content: "";
    width: 55px;
    border-bottom: 2px solid white;
    display: block;
    margin: 17px auto 0;
  }
}

.bannerx2_row {
  position: relative;
  clear: both;
  display: block;
  width: 100%;
  margin-bottom: 5px;
  height: 400px;
  overflow: hidden;
}

.bannerx2_row.left {

  .bannerx2_image {
    float: left;
    width: 65%;
    margin-bottom: -5px;
    position: relative;
  }
  .bannerx2_text {
    width: 35%;
    float: right;
    position: absolute;
    right: 0px;
  }
}

.bannerx2_row.left.inverted_banners {
  .bannerx2_image {
    width: 35%;
  }
  .bannerx2_text {
    width: 65%;

    .banner_center_container {
      position: relative;
      top: inherit;
      -webkit-transform: none;
      -moz-transform: none;
      -ms-transform: none;
      -o-transform: none;
      transform: none;
      margin-top: 30px;
      min-height: 370px;
    }
  }
}

.bannerx2_row.right {
  .bannerx2_image {
    float: right;
    width: 65%;
    margin-bottom: -5px;
    position: relative;
  }
  .bannerx2_text {
    width: 35%;
    float: left;
    position: absolute;
    left: 0;
  }
}

.bannerx2_row.right.inverted_banners {
  .bannerx2_image {
    width: 35%;
  }
  .bannerx2_text {
    width: 65%;

    .banner_center_container {
      position: relative;
      top: inherit;
      -webkit-transform: none;
      -moz-transform: none;
      -ms-transform: none;
      -o-transform: none;
      transform: none;
      margin-top: 30px;
      min-height: 370px;
    }
  }
}

.bannerx2_text {
  height: 100%;
  background: $corporate_1;
}

.banner_center_container {
  position: absolute;
  //top: 0;
  //bottom: 0;
  margin: auto;
  text-align: center;
  padding: 0 60px;
  //display: table;
  top: 50%;
  //left: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  width: 100%;
  box-sizing: border-box;

  img.banner_cycle_icon {
    margin-bottom: 8px;
  }

  a.see_more_cycle {
    background: white;
    color: $corporate_1;
    padding: 6px 13px;
    text-align: center;
    font-size: 17px;
    margin-top: 12px;
    display: inline-block;

    &:hover {
      opacity: 0.8;
    }
  }
}

.see_more_cycle_popup {
  h2.popup_cycle_title {
    color: $corporate_1;
    font-size: 20px;
    margin-bottom: 30px;
    font-weight: bold;
    text-transform: uppercase;
  }

  .popup_cycle_description {
    color: #656565;
    font-weight: 100;
    line-height: 24px;
    font-size: 17px;
  }

  .download_button {
    display: none;
  }
}

@-moz-document url-prefix() {
  .banner_center_container {
    //padding-top: 52px;
  }
}

.bannerx2_description {
  line-height: 22px;
  font-size: 16px;
  font-weight: 100;
  color: white;
  overflow: hidden;

  hide {
    //display: none;
  }
}

.inverted_banners {
  min-height: 400px;
  height: auto;

  .see_more_cycle {
    bottom: 20px;
    background: white;
    color: $corporate_1;
    @include center_x;
    padding: 10px 25px;
    cursor: pointer;
  }

  .bannerx2_text {
    position: relative !important;
  }

  .bannerx2_image {
    height: 400px !important;
    overflow: hidden;
  }

  .bannerx2_image img {
    min-height: 100%;
    max-width: none;
    @include center_xy;
    max-height: 100%;
    width: initial;
  }
}

/*======== Image Gallery ========*/
.gallery-image {
  background: white;
  padding: 60px 0 30px;
}

.gallery_filters_wrapper {
  text-align: center;
  margin-bottom: 50px;
}

body.lang_en {
  .filter_gallery_element {
    font-size: 16px;
  }
}

.filter_gallery_element {
  display: inline-block;
  background: $corporate_1;
  color: white;
  padding: 12px 5px;
  font-size: 15px;
  font-weight: 100;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  float: left;
  border-right: 6px solid white;

  &:last-of-type {
    border-right: 0;
  }

  &:hover, &.active {
    opacity: 0.7;
  }
}

.filter-gallery {
  background: $corporate-1;
  height: 75px;
  line-height: 75px;
  color: white;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 200;
  margin-bottom: 50px;
  cursor: pointer;
  position: relative;

  .element_hide {
    display: none;
  }

  h3 {
    padding-left: 30px;
  }

  span {
    display: inline-block;
    position: absolute;
    height: 75px;
    width: 75px;
    background: $corporate-2 url(/img/holi2/arrow-newsletter.png) no-repeat center center;
    right: 0px;
    top: 0px;
    border-left: 2px solid white;
    //transform: rotate(90deg);
  }
  ul {
    background: lighten($corporate-1, 33%);
    font-size: 18px;
    line-height: 1;
    display: none;
    //width: calc(100% - 76px);

  }
  li {
    padding: 10px 30px;
    cursor: pointer;
    color: $corporate-1;
  }
  li:hover {
    background: lighten($corporate-1, 25%);
  }
}

.gallery-image-wrapper {
  .big-img {
    width: 100%;
  }
}

.big-img {
  text-align: center;
  /*max-height: 760px;
  overflow: hidden;*/
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
  position: relative;

  .gallery_image_title {
    position: absolute;
    top: 40px;
    left: 40px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 40px;
    text-transform: uppercase;
  }

  img.main_image {
    width: 100%;
  }

  img.gallery_previous_image, img.gallery_next_image {
    position: absolute;
    height: 70px;
    top: 0;
    bottom: 0;
    right: 30px;
    margin: auto;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  img.gallery_previous_image {
    -ms-transform: rotate(180deg); /* IE 9 */
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);
    right: auto;
    left: 30px;
  }
  iframe {
    height: 300px;
  }
}

.image-grid {

  margin-top: 20px;

  ul {
    overflow: hidden;
    text-align: center;
  }

  li {
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 50px;
    height: 50px;
    overflow: hidden;
    border: 1px solid white;
    position: relative;
  }

  li img {
    position: absolute;
    top: 0;
    left: -50%;
    bottom: 0;
    right: -50%;
    margin: 0 auto;
    min-width: 120%;
    min-height: 50px;
    height: auto;
    vertical-align: bottom;
    cursor: pointer;

  }
  li:hover, li.active {
    border: 2px solid $corporate-2;
  }
}

.gallery-wrapper .big-img {
  width: 100%;
  height: 650px;
  overflow: hidden;
}

// Form
.info {
  .check_privacy {
    width: 15px !important;
  }

  span {
    a {
      text-align: left;
      display: inline-block;
      color: #717171;
      text-decoration: underline;
    }
  }

  label.error[for="privacy"] {
    display: inline-block;
  }
}

/*======== Ofertas =======*/
.cycle_banners_wrapper.offers_wrapper {
  background: white;
  margin-top: 30px;

  .cycle_element {
    .cycle_text_wrapper {
      background: #F5F6F8;
      float: right;
      position: relative;
      top: auto;
      right: auto;
      left: auto;
      bottom: auto;

      .center_div {
        position: relative;
        top: auto;
        right: auto;
        left: auto;
        bottom: auto;
        padding: 20px 75px;
        box-sizing: border-box;

        .cycle_description {
          overflow: hidden;
        }

        a {
          margin-top: 21px;
        }
      }
    }

    .cycle_description hide {
      display: none;
    }

    .cycle_text_wrapper {

    }
  }
}

.cycle_banners_wrapper {
  padding: 0 0 20px;

  .slides > li {
    display: table;
    width: 100%;
    position: relative;
  }

  .cycle_element {
    display: table;
    position: relative;
    width: 100%;
    margin-bottom: 20px;

    .exceded {
      width: 50%;
      float: left;
      height: 275px;
      overflow: hidden;
      position: relative;

      img {
        min-width: 100%;
        max-width: none;
      }
    }

    .cycle_text_wrapper {
      width: 50%;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      background: #F8F8F8;

      .cycle_title {
        font-family: 'Lato', sans-serif;
        font-size: 15px;
        color: $corporate_1;
        margin-bottom: 35px;
        font-weight: bolder;
        position: relative;

        .plus_image {
          position: absolute;
          right: 0;
          top: -10px;
        }

        strong {
          font-weight: bolder;
        }
      }

      .cycle_description {
        font-size: 15px;
        color: #757881;
        line-height: 28px;

        strong {
          font-weight: bolder;
        }
      }

      a {
        display: inline-table;
        text-decoration: none;
        color: white;
        font-size: 15px;
        background: $corporate_1;
        padding: 4px 21px;
        margin-top: 26px;
        border-top: 1px solid;
        font-weight: 300;
        border-bottom: 1px solid;
        text-transform: uppercase;
        cursor: pointer;

        &:hover {
          background: lighten($corporate_1, 10%);
        }

        &.button-promotion {
          background: $corporate-1;
          font-weight: 500;

          &:hover {
            background: lighten($corporate_1, 10%);
          }
        }
      }

      .center_div {
        position: absolute;
        top: 0;
        bottom: 0;
        margin: auto;
        display: table;
        text-align: left;
        padding: 0 75px;
      }
    }

    ol.flex-controlador-cycle, ol.flex-controlador-cycle-offers {
      position: absolute;
      bottom: 80px;
      right: 25%;
      width: 25%;
      text-align: left;
      z-index: 2;
      padding: 0 75px;
      padding-right: 0;
      box-sizing: border-box;

      li {
        display: inline-table;
        margin: 0 3px;
      }

      span.bottom_lane {
        height: 4px;
        width: 60px;
        background: $corporate_1;
        display: block;
        opacity: 0.6;
        cursor: pointer;

        &.flex-active {
          opacity: 1;
        }
      }
    }

    &.align_right {
      .exceded {
        float: right;
      }

      .cycle_text_wrapper {
        right: auto;
        left: 0;
      }

      ol.flex-controlador-cycle, .flex-controlador-cycle-offers {
        right: auto;
        left: 0;
      }
    }
  }

  a.link_cycle {
    position: absolute;
    bottom: 66px;
    right: 53px;
    color: #ADADAD;
    font-style: italic;
    text-decoration: none;
    font-size: 18px;
    letter-spacing: 1px;
    padding: 9px 0;
    border-top: 1px solid #DAD3D2;
    border-bottom: 1px solid #DAD3D2;
  }
}

.link_blocks {
  display: block;
  background: white;
  color: $corporate_1;
  padding: 10px 20px;
  margin: 20px auto 0;
  width: 120px;
}

.fancy_maps {
  .fancybox-outer {
    padding: 15px !important;
  }
}

// Floatin Booking
.floating_booking#full_wrapper_booking {
  top: 0 !important;
  position: fixed !important;
  bottom: inherit !important;
  //background: rgba(0, 0, 0, 0.65);
  -webkit-box-shadow: 1px 1px 1px gray;
  -moz-box-shadow: 1px 1px 1px gray;
  box-shadow: 1px 1px 1px gray;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -ms-transform: none !important;
  -o-transform: none !important;
  transform: none !important;
  #wrapper_booking #booking {
    //background: rgba(black, 0.35);
  }
  #full_wrapper_booking {
    background: transparent;
  }

  .destination_wrapper input {
    background: white url("/img/hoteo/hotel_booking.png") no-repeat center left;
  }

  #full_wrapper_booking .date_box {
    background-color: white;
  }

  #full_wrapper_booking .selectric {
    background: white url("/img/hoteo/down_arrow.png") no-repeat 90%
  }

  #full_wrapper_booking .boking_widget_inline .room_list_wrapper {
    background: white;
  }

  .guest_selector {
    background-color: white;
  }

  div#wrapper_booking {
    background: transparent !important;
    padding-top: 0;
  }

  .booking_form_title {
    position: relative;
    text-align: center;
    background: transparent;
  }

  .floating_room_list {
    top: 93%;
  }
}

.fixed_datepicker {
  position: fixed !important;
  top: 75px !important;
}

#slider_container #full_wrapper_booking, #inner_slider_container {

  #wrapper_booking {
    #booking {
      .rooms_number_wrapper .rooms_number .selectricItems {
        top: 120% !important;
        width: 100% !important;
      }
      .room_list_wrapper .room_list .room .children_selector {
        padding: 7px 4px 5px;
      }
    }
  }
}

.awards_banners_wrapper {
  background: white;
  text-align: center;
  padding: 20px 0;

  .award_element {
    display: inline-block;
    vertical-align: middle;
    margin-right: 20px;

    &:last-of-type {
      margin-right: 0;
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .header_datepicker {
        background: $corporate_1;
    }
}