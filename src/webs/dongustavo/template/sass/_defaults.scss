$base_web: "dongo";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #B78D74;
$corporate_2: #E7E3D7;
$corporate-3: rgba(44, 115, 181, 0.85);
$corporate-4: #e40613;
$corporate-5: #8fc9f1;

$title_size: 22px;
$description_size: 14px;
$line_height: 14px;
$title_family: 'Raleway', sans-serif;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

// Mixins
@mixin center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  -o-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
}

@mixin center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%,0%);
  -moz-transform: translate(-50%,0%);
  -ms-transform: translate(-50%,0%);
  -o-transform: translate(-50%,0%);
  transform: translate(-50%,0%);
}

@mixin center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%,-50%);
  -moz-transform: translate(0%,-50%);
  -ms-transform: translate(0%,-50%);
  -o-transform: translate(0%,-50%);
  transform: translate(0%,-50%);
}

@mixin translate_none {
  opacity: 1;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
}

@mixin translate_from_top {
  opacity: 0;
  -webkit-transform: translate3d(-100%,-100%,0);
  -moz-transform: translate3d(-100%,-100%,0);
  -ms-transform: translate3d(-100%,-100%,0);
  -o-transform: translate3d(-100%,-100%,0);
  transform: translate3d(-100%,-100%,0);
}

@mixin translate_from_left {
  opacity: 0;
  -webkit-transform: translate3d(-100%,0%,0);
  -moz-transform: translate3d(-100%,0%,0);
  -ms-transform: translate3d(-100%,0%,0);
  -o-transform: translate3d(-100%,0%,0);
  transform: translate3d(-100%,0%,0);
}

@mixin translate_from_right {
  opacity: 0;
  -webkit-transform: translate3d(100%,0%,0);
  -moz-transform: translate3d(100%,0%,0);
  -ms-transform: translate3d(100%,0%,0);
  -o-transform: translate3d(100%,0%,0);
  transform: translate3d(100%,0%,0);
}

@mixin translate_from_bottom {
  opacity: 0;
  -webkit-transform: translate3d(0%,100%,0);
  -moz-transform: translate3d(0%,100%,0);
  -ms-transform: translate3d(0%,100%,0);
  -o-transform: translate3d(0%,100%,0);
  transform: translate3d(0%,10%,0);
}

// Effects CSS
@-webkit-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }

  100% {
    opacity: 1;
    display: block;
  }
}
@-moz-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }

  100% {
    opacity: 1;
    display: block;
  }
}
@-o-keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }

  100% {
    opacity: 1;
    display: block;
  }
}
@keyframes fade_in {
  0% {
    opacity: 0;
    display: none;
  }

  100% {
    opacity: 1;
    display: block;
  }
}

.fade_in_effect {
  -webkit-animation: fade_in; /* Safari 4+ */
  -moz-animation:    fade_in; /* Fx 5+ */
  -o-animation:      fade_in; /* Opera 12+ */
  animation:         fade_in;
}

@-webkit-keyframes slide_down {
  0% { @include translate_from_top }
  100% { @include translate_none }
}

@-moz-keyframes slide_down {
  0% { @include translate_from_top }
  100% { @include translate_none }
}

@-o-keyframes slide_down {
  0% { @include translate_from_top }
  100% { @include translate_none }
}

@keyframes slide_down {
  0% { @include translate_from_top }
  100% { @include translate_none }
}

.slide_down_effect {
  -webkit-animation: slide_down 0.7s; /* Safari 4+ */
  -moz-animation:    slide_down 0.7s; /* Fx 5+ */
  -o-animation:      slide_down 0.7s; /* Opera 12+ */
  animation:         slide_down 0.7s;
}

@-webkit-keyframes slide_left {
  0% { @include translate_from_left }
  100% { @include translate_none }
}

@-moz-keyframes slide_left {
  0% { @include translate_from_left }
  100% { @include translate_none }
}

@-o-keyframes slide_left {
  0% { @include translate_from_left }
  100% { @include translate_none }
}

@keyframes slide_left {
  0% { @include translate_from_left }
  100% { @include translate_none }
}

.slide_left_effect {
  -webkit-animation: slide_left 0.7s; /* Safari 4+ */
  -moz-animation:    slide_left 0.7s; /* Fx 5+ */
  -o-animation:      slide_left 0.7s; /* Opera 12+ */
  animation:         slide_left 0.7s;
}

@-webkit-keyframes slide_right {
  0% { @include translate_from_right }
  100% { @include translate_none }
}

@-moz-keyframes slide_right {
  0% { @include translate_from_right }
  100% { @include translate_none }
}

@-o-keyframes slide_right {
  0% { @include translate_from_right }
  100% { @include translate_none }
}

@keyframes slide_right {
  0% { @include translate_from_right }
  100% { @include translate_none }
}

.slide_right_effect {
  -webkit-animation: slide_right 0.7s; /* Safari 4+ */
  -moz-animation:    slide_right 0.7s; /* Fx 5+ */
  -o-animation:      slide_right 0.7s; /* Opera 12+ */
  animation:         slide_right 0.7s;
}

@-webkit-keyframes slide_bottom {
  0% { @include translate_from_bottom }
  100% { @include translate_none }
}

@-moz-keyframes slide_bottom {
  0% { @include translate_from_bottom }
  100% { @include translate_none }
}

@-o-keyframes slide_bottom {
  0% { @include translate_from_bottom }
  100% { @include translate_none }
}

@keyframes slide_bottom {
  0% { @include translate_from_bottom }
  100% { @include translate_none }
}

.slide_bottom_effect {
  -webkit-animation: slide_bottom 0.7s; /* Safari 4+ */
  -moz-animation:    slide_bottom 0.7s; /* Fx 5+ */
  -o-animation:      slide_bottom 0.7s; /* Opera 12+ */
  animation:         slide_bottom 0.7s;
}