{% if content_subtitle and not common_section %}
{% if extra_content_subtitle %}<div class="content_subtitle_carousel owl-carousel">{% endif %}
<div class="content_subtitle_wrapper">
    <h1 class="content_subtitle_title"><span>{{ content_subtitle.subtitle|safe }}</span></h1>
    {% if content_subtitle.content %}
        <div class="content_subtitle_description {% if extra_icons %}content_extra_icons{% endif %} {% if content_pics %}content_pics_in{% endif %}">
            {% if content_map %}
                <div class="content_pics">{{ content_map|safe }}</div>
            {% else %}
                {% if content_pics %}<div class="content_pics owl-carousel">
                    {% for pic in content_pics %}<a href="{{ pic.servingUrl }}=s1900" class="pic" rel="lightbox[content_subtitle]">
                        <img src="{{ pic.servingUrl }}=s530" alt="{{ pic.altText|safe }}">
                    </a>{% endfor %}
                </div>
                <script>
                $(window).load(function () {
                    $(".content_subtitle_description .content_pics").owlCarousel({
                        loop: false,
                        nav: false,
                        dots: false,
                        items: 1,
                        margin: 0,
                        autoplay: true
                    });
                });
                </script>
                {% endif %}
            {% endif %}
            <div class="content_desc">{{ content_subtitle.content|safe }}</div>
            {% if extra_icons %}<div class="extra_icons">
                {% for ico in extra_icons %}<a {% if ico.linkUrl %}href="{{ ico.linkUrl|safe }}"{% else %}onclick="return"{% endif %} class="ico">
                    {% if ico.altText %}
                        <i class="fa {{ ico.altText|safe }}"></i>
                    {% else %}
                        <i class="fa"><img src="{{ ico.servingUrl|safe }}" alt="{{ ico.title|safe }}"></i>
                    {% endif %}
                    <span>{{ ico.title|safe }}</span>
                </a>{% endfor %}
            </div>{% endif %}
        </div>
    {% endif %}
</div>
{% if extra_content_subtitle %}
<div class="content_subtitle_wrapper">
    <h1 class="content_subtitle_title"><span>{{ extra_content_subtitle.subtitle|safe }}</span></h1>
    {% if extra_content_subtitle.content %}
        <div class="content_subtitle_description {% if extra_extra_icons %}content_extra_icons{% endif %} {% if extra_content_pics or extra_content_map %}content_pics_in{% endif %}">
            {% if extra_content_map %}
                <div class="content_pics">{{ extra_content_map|safe }}</div>
            {% else %}
                {% if extra_content_pics %}<div class="content_pics owl-carousel">
                    {% for pic in extra_content_pics %}<a href="{{ pic.servingUrl }}=s1900" class="pic" rel="lightbox[extra_content_subtitle]">
                        <img src="{{ pic.servingUrl }}=s530" alt="{{ pic.altText|safe }}">
                    </a>{% endfor %}
                </div>
                {% endif %}
            {% endif %}
            <div class="content_desc">{{ extra_content_subtitle.content|safe }}</div>
            {% if extra_extra_icons %}<div class="extra_icons">
                {% for ico in extra_extra_icons %}<a {% if ico.linkUrl %}href="{{ ico.linkUrl|safe }}"{% endif %} class="ico">
                    {% if ico.altText %}
                        <i class="fa {{ ico.altText|safe }}"></i>
                    {% else %}
                        <i class="fa"><img src="{{ ico.servingUrl|safe }}" alt="{{ ico.title|safe }}"></i>
                    {% endif %}
                    <span>{{ ico.title|safe }}</span>
                </a>{% endfor %}
            </div>{% endif %}
        </div>
    {% endif %}
</div>
</div>
<script>
$(window).load(function () {
    $(".content_subtitle_carousel").owlCarousel({
        loop: false,
        nav: false,
        dots: true,
        items: 1,
        margin: 0,
        autoplay: true,
        autoplaySpeed: 1000,
        dotsSpeed: 1000,
        dragEndSpeed: 1000,
    });
});
</script>
{% endif %}
{% endif %}
{% if content_access or common_section %}<div class="content_access" {% if gallery_section %}style="margin: 0;" {% endif %}>{{ content|safe }}</div>{% endif %}
{% if rooms %}{% include "_rooms.html" %}{% endif %}
{% if offers %}{% include "_offers.html" %}{% endif %}
{% if gallery_section %}{% include "_gallery.html" %}{% endif %}
{% if bannerx3 %}{% include "_bannerx3.html" %}{% endif %}
{% if banner_picscontent %}{% include "_banner_picscontent.html" %}{% endif %}
{% if banner_icons %}{% include "_banner_icons.html" %}{% endif %}
{% if minigallery %}{{ minigallery|safe }}{% endif %}
{% if banner_map %}{% include "_banner_map.html" %}{% endif %}
{% if newsletter %}
    <div class="newsletter_global_wrapper">
        {% if newsletter_background %}<div class="banner_newsletter_background" style="background-image: url('{{ newsletter_background|safe }}=s1900')"></div> {% endif %}
        <div class="newsletter_pictures">
            {% if newsletter_pictures_link %}
            <div class="newsletter_link">
                <a href="{{ newsletter_pictures_link.linkUrl|safe }}">
                    {% if newsletter_pictures_link.description %}
                        <span>{{ newsletter_pictures_link.description|safe }}</span>
                    {% else %}
                        <span>{{ T_ver_mas }}</span>
                    {% endif %}
                    <i class="fa fa-angle-right"></i>
                </a>
            </div>
            {% endif %}
            {% for pic in newsletter_pictures %}<a href="{{ pic.servingUrl|safe }}=s1900" class="pic" rel="lightbox[newsletter_pictures]"><img src="{{ pic.servingUrl|safe }}=s260-c" alt="{{ pic.altText|safe }}"></a>{% endfor %}
        </div>{{ newsletter|safe }}
    </div>
{% endif %}