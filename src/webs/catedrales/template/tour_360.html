<div class="tour_360_wrapper">

    <h3 class="gallery_filt_title">Tour 360</h3>

    <div class="tourNav">
        {% for x in tour_360 %}
            <div class="tourLink">
                <a href="#t{{ forloop.counter }}">{{ x.title|safe }}</a>
            </div>
        {% endfor %}
    </div>

    {% for x in tour_360 %}
        <div class="tour" id="t{{ forloop.counter }}" {% if not forloop.first %} style="display: none;" {% endif %}>
            {{ x.description|safe }}
        </div>
    {% endfor %}

    <script async>
        $(".tourLink a").click(function (e) {
            e.preventDefault();
            thisTour = $(this).attr('href');
            $(".tour").hide();
            $(thisTour).show();
        });
    </script>

</div>