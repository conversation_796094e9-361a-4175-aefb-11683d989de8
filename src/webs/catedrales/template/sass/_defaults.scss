//Base web (change too in template<PERSON>and<PERSON> and in config.rb)
$base_web: "cates";
// colors definitions
$white: rgb(255, 255, 255);
$black: rgb(0, 0, 0);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #4F8ABD;
$corporate_2: #2CC8CC;
$corporate_3: #19C8CC;

// colors for booking widget
$booking_widget_color_1: $white;
//body back ground & year input text color
$booking_widget_color_2: $corporate_1;
//header background & input texts
$booking_widget_color_3: gray;
//label texts
$booking_widget_color_4: gray;
//not used, but must be defined

@mixin center-image {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}
