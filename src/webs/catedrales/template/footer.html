<footer>
    {% if not inner_section %}
        <div class="maps_footer">
            <div class="iframe_wrapper">
                <iframe class="mapa"
                        data-url="https://www.google.com/maps/embed?pb=!1m10!1m8!1m3!1d2827.1282859212033!2d-7.164371060175902!3d43.55366205169126!3m2!1i1024!2i768!4f13.1!5e0!3m2!1ses!2ses!4v1490803384809"
                        width="600" height="450" frameborder="0" style="border:0" allowfullscreen></iframe>
            </div>

            <div class="picture_iframe">
            </div>
            <div class="text_iframe">
                {{ iframe_maps.content|safe }}
                <div class="mapForm">
                    <input type="text" class="place" placeholder="{{ T_como_llegar }} {{ T_desde }}...">
                    <button class="go"></button>
                </div>
            </div>

            <script language="JavaScript">
                function goMap() {
                    mapaUrl = "https://www.google.com/maps?saddr=@place@&daddr={{ google_maps }}&output=embed&maptype=satellite";
                    newurl = mapaUrl.replace("@place@", $(".place").val());

                    $(".mapa").attr("src", newurl);
                }
                $(".go").click(function () {
                    goMap();
                });
                $('.place').bind("enterKey", function (e) {
                    goMap();
                });
                $('.place').keyup(function (e) {
                    if (e.keyCode == 13) {
                        $(this).trigger("enterKey");
                    }
                });
            </script>

            <script async>
                $(window).load(function(){
                    $("iframe.mapa").attr("src", $("iframe.mapa").attr("data-url"));
                })
            </script>
        </div>
    {% endif %}

    <div class="newsletter_social_wrapper container12">
        <div class="newsletter_wrapper">
            {{ newsletter }}
        </div>
        <div class="social_wrapper">
            {% if facebook_id %}
                <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank">
                    <img src="/img/{{ base_web }}/social/facebook.png" alt="{{ T_siguenos_en }} facebook"
                         title="{{ T_siguenos_en }} facebook">
                </a>
            {% endif %}
            {% if twitter_id %}
                <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank">
                    <img src="/img/{{ base_web }}/social/twitter.png" alt="{{ T_siguenos_en }} twitter"
                         title="{{ T_siguenos_en }} twitter">
                </a>
            {% endif %}
            {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank" rel="publisher">
                    <img src="/img/{{ base_web }}/social/googleplus.png" alt="{{ T_siguenos_en }} google+"
                         title="{{ T_siguenos_en }} google+">
                </a>
            {% endif %}
            {% if youtube_id %}
                <a href="https://www.youtube.com/{{ youtube_id }}" target="_blank">
                    <img src="/img/{{ base_web }}/social/youtube.png" alt="{{ T_siguenos_en }} youtube"
                         title="{{ T_siguenos_en }} youtube">
                </a>
            {% endif %}
        </div>
    </div>


    <div class="full-copyright">
        <div class="footer-copyright container12">


            <div id="social-widgets">
                <div id="google">{{ footer_columns.4.description|safe }}</div>
                <div id="facebook">{{ footer_columns.5.description|safe }}</div>
            </div>

            {% for x in policies_section %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>

        </div>

        {% if texto_legal %}
            <div id="div-txt-copyright" class="footer-copyright container12">
                {{ texto_legal|safe }}
            </div>
        {% endif %}




        <div class="social_wrapper">
            <div id="facebook_like">
                <div id="fb-root"></div>
                <script src="//connect.facebook.net/es_ES/all.js#appId=128897243865016&amp;xfbml=1"></script>
                <div>
                    <fb:like font="" href="" layout="button_count" send="false" show_faces="false"
                             width="110"></fb:like>
                </div>
            </div>
            <div id="google_plus_one">
                <div class="g-plusone"></div>
            </div>
        </div>

    </div>


</footer>