<div class="normal_section_mobile">
  {% if section_params.title %}
    <h2 class="section_title">{{section_params.subtitle|safe}}</h2>
  {% endif %}

  <div class="section-content">
     {{section_params.content|safe}}
  </div>

  {# Blocks #}
{% if blocks_mobile %}
    <div class="gray_background">
        <div class="destinations_elements_wrapper container12">
            {% for x in blocks_mobile %}

                <div class="dest_wrapper" id="destinationblock-{{ forloop.counter }}">

                    <ul class="slides">
                        <li>
                            <a href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[gallery_{{ x.title|safe }}]" class="swipebox">
                                <img class="room_picture" src="{{ x.servingUrl|safe }}=s1900">
                            </a>
                        </li>
                    </ul>

                    <div class="title-dest">{{ x.title|safe }}</div>
                    <span class="separator"></span>
                    <div class="desc-dest" id="desc-dest-{{ forloop.counter }}">
                          <span id="real_desc_dest_{{ forloop.counter }}" class="real_desc_dest">
                            {{ x.description|safe }}
                          </span>
                    </div>
                    <script>
                        $(function () {
                            $("#destinationblock-{{ forloop.counter }}").flexslider({
                                controlNav: true
                            });
                        });
                    </script>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}

</div>


