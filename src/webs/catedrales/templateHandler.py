# -*- coding: utf-8 -*-
from collections import OrderedDict
import copy
import os

from booking_process.constants.advance_configs_names import CONTACT_PHONES, SECTIONS_WITH_PICTURE_IN_SLIDER, MAIN_SECTIONS, \
	TOP_SECTIONS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url, get_text_version, sanitize_html
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection


thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "cates"


class TemplateHandler(BaseTemplateHandler2WithRedirection):
	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'iframe_maps': get_section_from_section_spanish_name('Iframe google maps', language),
							  'official_website': get_section_from_section_spanish_name('web oficial', language),
							  'revolution_slider': self.buildRevolutionSlider(language,
																			  additionalParams=get_web_dictionary(
																				  language),
																			  specificTemplate='../webs/catedrales/template/slider_revolution.html'),
							  'phone_hotel': get_config_property_value(CONTACT_PHONES)}

		#GoogleMaps
		sectionIframe = self.getSectionAdvanceProperties(result_params_dict['iframe_maps'], language)
		if sectionIframe.get('google_maps'):
			result_params_dict['google_maps'] = sectionIframe.get('google_maps')
		else:
			result_params_dict['google_maps'] = "paraty+tech+malaga"

		#Bottom Popup
		result_params_dict['bottom_popup'] = get_section_from_section_spanish_name("popup inicio footer", language)
		result_params_dict['bottom_popup_text'] = get_section_from_section_spanish_name("promocion pop up", language)
		result_params_dict['bottom_popup_background'] = get_pictures_from_section_name("promocion pop up", language)

		if section_type != 'Inicio':
			result_params_dict['inner_section'] = True

		if sectionToUse['sectionName'].lower() in get_config_property_value(SECTIONS_WITH_PICTURE_IN_SLIDER).lower():
			actual_pictures = get_pictures_from_section_name(sectionToUse['sectionName'], language)
			actual_pictures = list(filter(lambda x: x.get('title') != 'ico', actual_pictures))
			result_params_dict['pictures'] = actual_pictures
			result_params_dict['revolution_slider'] = self.buildRevolutionSlider(language,
																				 specificPictures=actual_pictures,
																				 additionalParams=get_web_dictionary(
																					 language),
																				 specificTemplate='../webs/catedrales/template/slider_revolution.html')

		#Website menu
		all_sections = copy.deepcopy(
			self.getSections(language, True)) #Inefficient but needed to avoid translated sections
		menu_sections = self.getSectionsFor(language, all_sections, MAIN_SECTIONS)
		for menu_element in menu_sections:
			section_pictures = getPicturesForKey(language, menu_element.get('key'), [])
			menu_element['icon'] = list(list(filter(lambda x: x.get('title') == 'ico', section_pictures)))

		result_params_dict['menu_personalized'] = menu_sections

		#Header menu
		all_sections = copy.deepcopy(
			self.getSections(language, True)) #Inefficient but needed to avoid translated sections
		top_sections = self.getSectionsFor(language, all_sections, TOP_SECTIONS)
		for menu_element in top_sections:
			section_pictures = getPicturesForKey(language, menu_element.get('key'), [])
			menu_element['icon'] = filter(lambda x: x.get('title') == 'ico', section_pictures)

		result_params_dict['top_sections_personalized'] = top_sections


		#Content Subtitle
		if sectionToUse.get('subtitle'):
			result_params_dict['content_subtitle'] = sectionToUse


		#Automatic Content
		automatic_content = {
		'Galeria de Imagenes': True,
		'Mis Reservas': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True


		#Rooms
		if section_type == 'Habitaciones':
			result_params_dict['rooms_content_subtitle'] = sectionToUse
			rooms_section = get_pictures_from_section_name('habitaciones_blocks', language)

			result_params_dict['rooms_blocks'] = rooms_section


		#Individual Room
		if section_type == 'Extra 1' or section_type == 'Extra 2':
			room_blocks = get_pictures_from_section_name('habitaciones_blocks', language)

			for number, room_element in enumerate(room_blocks):
				actual_room_section = build_friendly_url(str(sanitize_html(sectionToUse.get('title', ''), []))).strip().lower()
				room_element_section = room_element.get('linkUrl', '')

				if room_element_section == actual_room_section:
					previous_room_number = number - 1
					next_room_number = 0 if len(room_blocks) - 1 == number else number + 1
					result_params_dict['previous_room'] = room_blocks[previous_room_number].get('linkUrl').replace('<br>', ' ')
					result_params_dict['next_room'] = room_blocks[next_room_number].get('linkUrl').replace('<br>', ' ')

			result_params_dict['room_details'] = sectionToUse
			room_section_pictures = get_pictures_from_section_name(sectionToUse['sectionName'], language)
			slider_pictures_room = list(filter(lambda x: x.get('title') == 'slider', room_section_pictures))
			if slider_pictures_room:
				result_params_dict['pictures'] = slider_pictures_room
			result_params_dict['room_details']['pictures'] = list(filter(lambda x: x.get('title') != 'slider',room_section_pictures))
			result_params_dict['room_details']['description_share'] = get_text_version(sectionToUse['content'])
			result_params_dict['content_subtitle'] = None
			result_params_dict['offer_individual'] = section_type == 'Extra 2'
			result_params_dict['disabled_booking_slider'] = True


		#Ofertas
		if section_type == 'Ofertas':
			result_params_dict['offers'] = self.buildPromotionsInfo(language)
			for offer_element in result_params_dict['offers']:
				offer_pictures = getPicturesForKey(language, str(offer_element.get('offerKey')), [])
				# if offer_pictures:
				offer_element['link'] = offer_pictures[0].get('linkUrl')

		if section_type == u'Localización':
			result_params_dict['content_subtitle'] = None
			section_pictures = get_pictures_from_section_name(u'localización', language)
			result_params_dict['slider_picture'] = list(filter(lambda x: x.get('title') == 'slider', section_pictures))
			result_params_dict['pictures'] = list(filter(lambda x: not x.get('title') in ['slider', 'ico'], section_pictures))
			result_params_dict['content_location'] = get_section_from_section_spanish_name(sectionToUse['sectionName'],
																						   language)
			if not result_params_dict['content_location'] or not result_params_dict['content_location']['subtitle']:
				result_params_dict['content_location'] = get_section_from_section_spanish_name(u'localización',
																							   language)

			additionalParams4Contact = {
			'language': language,
			'extra': None,
			'picturesInSlider': True,
			'privacy_checkbox': True,
			'reservation_question': True
			}

			sectionTemplate = 'secciones/contact.html'
			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = '' #we dont need here the description. We've got it above
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			iframe_google_map = get_section_from_section_spanish_name("Iframe google maps", language)

			result_params_dict['contact_html'] = contact_html
			result_params_dict['iframe_google_map'] = iframe_google_map


		#Galeria de imagenes
		if section_type == 'Galeria de Imagenes':
			result_params_dict['pictures'] = get_pictures_from_section_name('slider_gallery', language)
			result_params_dict['revolution_slider'] = self.buildRevolutionSlider(language,
																				 specificPictures=get_pictures_from_section_name(
																					 'slider_gallery', language),
																				 additionalParams=get_web_dictionary(
																					 language),
																				 specificTemplate='../webs/catedrales/template/slider_revolution.html')

		# Tour 360
		if advance_properties.get('tour_360'):
			result_params_dict['tour_360'] = get_pictures_from_section_name(advance_properties['tour_360'], language)

		#Mis Reservas
		if section_type == 'Mis Reservas':
			result_params_dict['my_booking'] = True

		if advance_properties.get('bannersx4'):
			result_params_dict['top_blocks'] = get_pictures_from_section_name(advance_properties['bannersx4'], language)

		if advance_properties.get('cycle_banners', False):
			cycle_pictures = get_pictures_from_section_name(advance_properties['cycle_banners'], language)
			for cycle_element in cycle_pictures:
				cycle_properties = self.getSectionAdvanceProperties(cycle_element, language)
				if cycle_properties.get('icon'):
					cycle_element['icon'] = cycle_properties['icon']

			result_params_dict['cycle_banners'] = cycle_pictures

		if advance_properties.get("bannersx3"):
			result_params_dict['bannersx3'] = get_pictures_from_section_name(advance_properties.get("bannersx3"), language)

		if advance_properties.get('events', False):
			events_blocks = advance_properties['events'].split("@@")
			events_sections = []
			for x in events_blocks:
				events_sections.append(get_section_from_section_spanish_name(x, language))

			result_params_dict['events_blocks'] = events_sections

		if advance_properties.get('banner_bottom'):
			result_params_dict['bottom_banner'] = get_section_from_section_spanish_name(
				advance_properties['banner_bottom'], language)

		if advance_properties.get('carousel_banner'):
			result_params_dict['carousel_banner'] = get_pictures_from_section_name(
				advance_properties['carousel_banner'], language)

		return result_params_dict

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		return self.buildTemplate('booking/booking_engine_5/_booking_widget.html', params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		context = {
		'booking_header': get_section_from_section_spanish_name('booking_header', language)

		}
		context['booking_header_promocode'] = self.getSectionAdvanceProperties(context['booking_header'], language).get('promocode')
		options['custom_title_html'] = buildTemplate('%s/template/header_booking.html' % '/'.join(os.path.abspath(__file__).split("/")[:-1]), context)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		return options

	def getTemplateUrl(self, section=None):
		return thisUrl

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:

				if x.get('title') == 'ico':
					continue

				nameFilter = x.get('title', "")

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [x['servingUrl']]
				else:
					filters[nameFilter].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
		'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType,
											super(TemplateHandler, self).getTemplateForSectionType(sectionType,
																								   sectionTemplate))
		return template

	def get_revolution_initial_height(self):
		return "550"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True


	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		base_path = os.path.dirname(__file__)

		advance_properties = self.getSectionAdvanceProperties(currentSection, language)

		section_params = {}

		if user_agent_is_mobile():

			section_type = currentSection['sectionType']
			additionalParams['custom_elements'] = ''

			if section_type == 'Habitaciones':

				rooms_information = get_pictures_from_section_name('habitaciones_blocks', language)
				for room in rooms_information:
					room_pictures = get_pictures_from_section_name(room.get('linkUrl'), language)
					room['gallery'] = room_pictures
					room['individual_link'] = build_friendly_url(room['title'].replace('<br>', ' '))

				section_params['rooms_information'] = rooms_information
				section_params['language_code'] = get_language_code(language)

				result = dict(list(section_params.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(base_path, 'template/mobile/rooms.html')

				return buildTemplate(fullPath, result)

			if section_type == 'Ofertas':
				section_params['elements'] = self.buildPromotionsInfo(language)
				section_params['language'] = get_language_code(language)
				for offer_element in section_params['elements']:
					offer_pictures = getPicturesForKey(language, str(offer_element.get('offerKey')), [])
					if offer_pictures:
						offer_element['link'] = offer_pictures[0].get('linkUrl')

				result = dict(list(section_params.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(base_path, 'template/mobile/offers.html')

				return buildTemplate(fullPath, result)

			if section_type == 'Extra 1' or section_type == 'Extra 2':
				if section_type == "Extra 2":
					section_params['description_share'] = get_text_version(currentSection['content'])
					section_params['offer_individual'] = True
				section_params['actual_section'] = get_section_from_section_spanish_name(currentSection['sectionName'], language)
				if section_type == 'Extra 1' and section_params['actual_section']:
					room_section_pictures = get_pictures_from_section_name(currentSection['sectionName'], language)
					section_params['actual_section']['pictures'] = list(filter(lambda x: x.get('title') != 'slider', room_section_pictures))
					filtered_pics = []
					for pic in section_params['actual_section']['pictures']:
						filtered_pics.append(pic.get('servingUrl'))
					section_params['actual_section']['pictures'] = filtered_pics
				result = dict(list(section_params.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(base_path, 'template/mobile/rooms_individual.html')
				return buildTemplate(fullPath, result)

			if advance_properties.get('carousel_banner'):
				result = dict(list(section_params.items()) + list(get_web_dictionary(language).items()))
				result['carousel_banner'] = get_pictures_from_section_name(advance_properties['carousel_banner'],
																			language)
				fullPath = os.path.join(base_path, 'template/mobile/carousel_bottom.html')
				rendered_template = buildTemplate(fullPath, result)
				additionalParams['custom_elements'] += rendered_template

			if currentSection['sectionType'] == u'Galeria de Imagenes':
				section_pictures = getPicturesForKey(language, currentSection.get('key', ''), [])
				if section_pictures:
					filters = OrderedDict()
					for x in section_pictures:
						if not (x.get('title') if x.get('title') else '').lower() in ['slider', 'ico']:
							candidate = x['servingUrl']

							if x.get('linkUrl', False):
								candidate = x['linkUrl']

							if not x.get('title', False):
								x['title'] = get_web_dictionary(language).get('T_mas_fotos', '')

							if not filters.get(x.get('title', ''), False):
								filters[x.get('title', '')] = [candidate]
							else:
								filters[x.get('title', '')].append(candidate)
					additionalParams['filtered_gallery_mobile'] = filters

				sectionTemplate = 'secciones/gallerys_new/gallery_1.html'
				mobileAllow = False
				return self.buildTemplate(sectionTemplate, additionalParams, mobileAllow)

			if section_type == u"Localización":
				additionalParams['title'] = currentSection['title']
				additionalParams['content'] = currentSection['content']
				additionalParams['iframe_google_maps'] = get_section_from_section_spanish_name('iframe maps mobile',
																							   language)
				sectionTemplate = 'mobile/secciones/location/location_contact.html'
				result = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()))
				mobileAllow = False
				return self.buildTemplate(sectionTemplate, result, mobileAllow)

			if advance_properties.get("bannersx3"):
				additionalParams['section_params'] = currentSection
				additionalParams['blocks_mobile'] = get_pictures_from_section_name(advance_properties.get("bannersx3"), language)

				template_values = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(os.path.dirname(__file__), "template/mobile/normal_section_blocks.html")
				return buildTemplate(fullPath, template_values)

			if advance_properties.get('cycle_banners', False):
				additionalParams['section_params'] = currentSection
				cycle_pictures = get_pictures_from_section_name(advance_properties['cycle_banners'], language)
				for cycle_element in cycle_pictures:
					cycle_properties = self.getSectionAdvanceProperties(cycle_element, language)
					if cycle_properties.get('icon'):
						cycle_element['icon'] = cycle_properties['icon']

				additionalParams['blocks_mobile'] = cycle_pictures

				template_values = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(os.path.dirname(__file__), "template/mobile/normal_section_blocks.html")
				return buildTemplate(fullPath, template_values)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)