<footer>
    <div class="wrapper_footer_columns container12">

        <div class="itc_logo"><img src="/img/{{ base_web }}/logo-footer.png?v=1"></div>

        <div class="social_wrapper">
            {%if facebook_id %}
                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-facebook-footer.png?v=1" alt="{{ T_siguenos_en }} facebook" title="{{ T_siguenos_en }} facebook"> </a>
            {% endif %}
            {% if twitter_id %}
                <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-twitter-footer.png?v=1" alt="{{ T_siguenos_en }} twitter" title="{{ T_siguenos_en }} twitter"> </a>
            {% endif %}
            {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher"> <img src="/img/{{ base_web }}/social/ico-google-footer.png" alt="{{ T_siguenos_en }} google+" title="{{ T_siguenos_en }} google+"> </a>
            {% endif %}
            {% if youtube_id %}
                <a href="https://www.youtube.com/{{youtube_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-youtube-footer.png" alt="{{ T_siguenos_en }} youtube" title="{{ T_siguenos_en }} youtube"> </a>
            {% endif %}
        </div>
        <div class="newsletter_wrapper">{{ newsletter }}</div>
        <div class="bottom_footer container12">
            <div class="copyright_wrapper">
                {{ T_copyright }} {% now "Y" %} 
            </div>

            <div class="default_links">
                {% for x in policies_section %}
                    {% if x.custom_link %}
                        <a href="{{ x.custom_link }}">{{ x.title|safe }}</a> |
                    {% else %}
                        <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                    {% endif %}
                {% endfor %}
                <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html" title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
                <a target="_blank" href="/rss.xml">RSS</a>
            </div>
        </div>
    </div>

    <div class="container12">
        <div id="facebook_like">
            <div id="fb-root"></div>
            <script src="//connect.facebook.net/es_ES/all.js#appId=128897243865016&amp;xfbml=1"></script>
            <div>
                <fb:like font="" href="" layout="button_count" send="false" show_faces="false" width="110"></fb:like>
            </div>
        </div>
        <div id="google_plus_one">
            <div class="g-plusone"></div>
        </div>
    </div>

    <div class="legal_text">

        {% if texto_legal %}
            <div id="div-txt-copyright" class="footer-copyright container12">
              {{texto_legal|safe}}
            </div>
        {% endif %}

    </div>


</footer>