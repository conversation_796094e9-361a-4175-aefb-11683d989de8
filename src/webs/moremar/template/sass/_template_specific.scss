/*=== General ===*/
body {
  font-family: 'Raleway',Helvetica, Arial, Helvetica, sans-serif;
}

body.interior {
  section#slider_container {
    height: 630px;
    overflow: hidden;
    width: 100%;
    display: inline-block;
  }
}

section#content {
  padding-top: 40px;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

/*=== Header ===*/
header {
  width: 100%;
  z-index: 30;
  background: white;
  min-width: 1140px;
  top: 0;
  height: 150px;
  position: absolute;

  div#logoDiv {
    margin-top: 7px;
    margin-left: 0;
  }

  .right_header {
    width: 845px !important;
    padding-top: 10px;
  }

  .middle_header {
    width: 845px !important;
  }

  .hotel_phone_wrapper {
    float: right;
    font-size: 12px;
    margin-top: 14px;
    color: $corporate_2;

    img {
      vertical-align: bottom;
    }
  }
}

.top_header {
  text-align: right;
  margin-top: 14px;

  div#social {
    display: inline-block;
    float: right;
    margin-left: 15px;
    margin-top: 22px;

    a {
      text-decoration: none;
      display: inline-table;
      float: left;
      margin-right: 5px;
    }
  }

  #lang {
    margin-top: 20px;
  }

  div#lang {
    display: inline-block;
  }

  .contact_phone {
    display: inline-block;
    font-size: 14px;
    color: white;
    font-weight: lighter;
    margin-right: 15px;
    margin-left: 15px;
    margin-top: 23px;

    img {
      vertical-align: bottom;
      margin-bottom: 2px;
      margin-right: 5px;
    }
  }

  div#top-sections {
    display: inline-table;
    margin-top: 23px;

    a {
      font-size: 12px;
      color: white;
      font-weight: lighter;
      text-decoration: none;
      margin-right: 22px;

      &:hover {
        opacity: 0.8;
      }

      &:last-of-type {
        margin-right: 0;
      }

      img {
        vertical-align: middle;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }
  }
}

#top-sections {
  float: right;
  font-size: 12px;
  margin-top: 14px;
  margin-left: 20px;

  a {
    text-decoration: none;
    color: $corporate_2;
  }

  a:hover {
    opacity: 0.5;
  }

  img {
    vertical-align: bottom;
  }
}

.web_oficial {
  color: white;
  float: left;
  font-size: 12px;
  margin-top: 27px;
}

#lang {
  position: relative;
  top: 7px;
  float: right;
  font-size: 12px;
  color: white;
  font-weight: lighter;
  text-decoration: none;
  margin-left: 15px;
  cursor: pointer;
  background-color: $corporate_1;
  padding: 5px;
  width: 115px;
  padding-top: 7px;

  .lang_image {
    display: inline-block;
    vertical-align: middle;
  }

  span#selected-language {
    padding-left: 4px;
  }

  #language-selector-options {
    position: absolute;
    margin-top: 4px;
  }

  .arrow {
    display: inline-block;
    background: url(/img/#{$base_web}/flecha_white_down.png?v=1) no-repeat center center !important;
    float: right;
    width: 30px;
    height: 26px;
    margin-top: -6px;
    background-size: 17px !important;
    vertical-align: middle;
  }

  #selected-language {

  }

  ul li {
    background: #ffffff;
    text-align: left;
    width: 80px;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}

/*===== Menu =====*/
#mainMenuDiv {
  margin-top: 35px;

  ul {
    text-align: justify;
    justify-content: space-between;

    &:after {
      content: "";
      width: 100%;
      display: inline-block;
      height: 0;
    }

    li {
      display: inline-block;
      text-align: center;

      &:first-of-type {
        padding-left: 0;
      }

      &:nth-last-of-type(2), &:last-of-type {
        border-right: 0;
      }

      &:last-of-type {
        padding-right: 0;
      }

      a {
        text-decoration: none;
        font-size: 12px;
        font-weight: lighter;
        color: black;
        text-transform: uppercase;
        padding: 6px 0 5px;

        &.button-promotion {
          color: white !important;
          background: $corporate_1;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 12px;
          padding: 9px 7px;
        }
      }

      &:hover a {
        //border-top: 2px solid white;
        border-bottom: 3px solid #0099bb;
      }

      &#section-active a, {
        font-weight: 700;
        //border-top: 2px solid white;
        border-bottom: 3px solid $corporate_1;
        padding: 6px 0 5px;
      }
    }
  }
}

/*=== Slider ===*/
#slider_container {
  position: relative;
}

.tp-banner-container {
  height: 600px !important;
}

.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

.tparrows {
  display: none !important;
}

.tp-bullets {
  bottom: -40px !important;
  opacity: 1 !important;
}

.slide_inner {
  height: 630px;
  width: 100%;
  overflow: hidden;
  display: inline-block;

  img {
    width: 100%;
    display: block;
  }
}

.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

/*==== Booking Widget ====*/
label#titulo_fecha_entrada, label#titulo_fecha_salida {
  display: none;
}

#contenedor_habitaciones > label {
  display: none;
}

.adultos.numero_personas {
  & > label {
    display: none !important;
  }
}

#titulo_ninos {
  display: none !important;
}

#booking fieldset {
  margin: 5px 0 0;
}

#search-button {
  font-size: 14px;
}

/*======= Booking Widget Data =====*/
#data {
  .date_box {
    background: #ECECEC;

    .date_year {
      color: #5CACDB;
    }
  }

  .selectric, .promocode_input {
    background: #ECECEC;
  }

  .promocode_input {
    font-size: 13px;
  }
}

.fancybox-inner {
  overflow: visible !important;
}

/*====== Content Subtitle =====*/
h3.subtitle_title {
  text-align: center;
  font-size: 12px;
  font-weight: 700;
  color: $corporate_1;
  width: 600px;
  margin: 30px auto;

  span:before, span:after {
    border-top: 1px solid $gray-3;
    display: block;
    height: 1px;
    content: " ";
    width: 30%;
    position: relative;
    left: 0;
    top: 0.7em;
  }

  span:after {
    left: 70%;
    top: -0.5em;
    content: " ";
  }

}

.content_subtitle_wrapper {
  display: table;
  width: 750px;
  margin: 0 auto;

  .divided1, .divided2, .divided3 {
    width: 350px;
    float: left;
    text-align: left;
  }

  .divided3 {
    float: right;
  }

  .divided2 {
    margin-left: 45px;
  }

  .subtitle_description {
    font-size: 15px;
    font-weight: lighter;
    line-height: 25px;
    color: #636363;
    text-align: justify;
    //margin-bottom: 40px;

  }
}

/*============== Gallery Section features ==============*/

.gallery_1 li .crop img {
  height: 235px !important;

}

/*============== Gallery Mosaic ==============*/
.gallery_title {
  padding-top: 60px;
}

.gallery_title, .services_title {
  color: #626262;
  text-align: center;
  font-size: 27px;
  margin-bottom: 30px;
  font-weight: lighter;
  text-transform: uppercase;

  &:after {
    content: "";
    width: 55px;
    border-bottom: 2px solid $corporate_1;
    display: block;
    margin: 17px auto 0px;
  }
}

.gallery-mosaic-item {
  float: left;
  width: 375px;
  margin: 2px;
  height: 250px;
  overflow: hidden;
  position: relative;

  img {
    min-width: 100%;
    min-height: 100%;
    max-height: auto;
    max-width: auto;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  position: relative;
  height: 421px;

  &:hover img {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
  }

  img {
    width: auto;
    height: 100%;
    max-width: none;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

  }

  img.video_arrow {
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    bottom: 0px;
    width: 100px;
    height: 100px;
    z-index: 2;
    min-height: inherit;
    min-width: initial;
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 78px auto 0px;
}

.flexslider_gallery {
  position: relative;

  li {
    width: 1285px !important;
  }
}

/*======= Banners x3 =======*/
.bannersx3_wrapper {
  display: table;
  width: 1140px;
  margin: 0 auto;
  margin-top: 73px;

  .banner_element {
    float: left;
    width: 550px;
    padding: 0 10px;
    position: relative;
    overflow: hidden;
    -webkit-transition: all 1s;
    -moz-transition: all 1s;
    -ms-transition: all 1s;
    -o-transition: all 1s;
    transition: all 1s;

    &:hover {
      opacity: 0.8;
    }

    &:before {
      content: "";
      display: block;
      padding-top: 95%;
    }

    img {
      width: auto;
      position: absolute;
      //left: -50%;
      top: 0;
      //min-height: 100%;
      //max-width: none;
      margin: 0 auto;
      //right: -50%;
      //bottom: 0px;
    }

    &.big {
      width: 550px;

      &:before {
        content: "";
        display: block;
        padding-top: 94.6%;
      }
    }

    .circle {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      height: 22px;
      width: 235px;
      margin: auto;
      text-align: center;
      color: white;
      background: rgba(101, 180, 230, 0.5);
      font-size: 21px;
      border-radius: 181px;
      padding: 105px 0px;
    }

    .underline_bottom {
      width: 75px;
      border-bottom: 2px solid white;
      margin: 5px auto;
    }

    .underline_top {
      width: 75px;
      border-bottom: 2px solid white;
      margin: 5px auto;
    }
  }
}

/*===== Bottom Phrase ====*/
.bottom_phrase {
  text-align: center;
  margin-top: 78px;
  margin-bottom: 70px;

  h3.title {
    font-size: 34px;
    color: #5CACDB;
    margin-bottom: 30px;
    font-weight: bolder;
  }

  .description {
    font-size: 24px;
    color: #636363;
    font-weight: lighter;
  }
}

/*===== Footer ====*/
footer {
  background: $corporate_2;
  padding-top: 47px;

  .footer_column {
    text-align: center;

    &.left {
      box-sizing: border-box;
      padding-left: 295px;

    }

    &.right {
      padding-right: 295px;
      box-sizing: border-box;

      #newsletter {
        #title_newsletter, label#suscEmailLabel {
          display: none !important;
        }
      }
    }

    .footer_column_description {
      border-bottom: 1px solid $corporate_1;
      padding-bottom: 10px;
    }

    h3.footer_column_image {
      height: 80px;
    }

    .footer_column_description {
      color: #A5A5A5;
      font-weight: lighter;
      font-size: 12px;
      line-height: 24px;
    }
  }

  input#suscEmail {
    margin-top: 13px;
    width: 237px;
    height: 35px;
    border: 0;
  }

  button#newsletter-button {
    width: 239px;
    border: 0;
    background: $corporate_1;
    color: white;
    margin-top: 7px;
    text-transform: uppercase;
    font-size: 17px;
    padding: 8px;
  }

  .wrapper_footer_columns {
    padding-bottom: 37px;

    a {
      text-decoration: none;
    }
  }

  .full-copyright {
    background: $corporate_1;
    padding: 29px 0;

    .footer-copyright {
      text-align: center;
      font-size: 14px;
      color: white;

      a {
        text-decoration: none;
        color: white;
        font-weight: lighter;
      }
    }
  }

  div#facebook_like {
    width: 49%;
    float: left;
    margin-top: 2px;
    text-align: right;
  }

  #google_plus_one {
    width: 49%;
    float: right;
  }

  .social_likes {
    margin-top: 3px;
  }

  div#div-txt-copyright {
    margin-top: 3px;
    text-align: center;
    text-transform: uppercase;
    color: white;
    font-size: 11px;
  }

  .legal_text {
    background: $corporate_1;
    padding: 20px 0;
  }

  .itc_logo {
    float: left;

    img {
      width: 100%;
    }
  }

  .newsletter_wrapper, footer .social_wrapper {
    float: right;
    margin-left: 20px;
  }


  #newsletter form#form-newsletter {
    display: inline-block;
    vertical-align: middle;
    position: relative;
  }

  #newsletter #form-newsletter label#suscEmailLabel {
    display: none !important;
  }

  #newsletter input#suscEmail {
    background: rgba(255, 255, 255, 0.2);
    border: 0;
    padding: 15px;
    width: 330px;
    box-sizing: border-box;
    height: 40px;
  }

  div#newsletterButtonExternalDiv button#newsletter-button {
    border: 0;
    background: transparent url(/static_1/images/booking/flecha_motor_der.png) no-repeat center;
    text-indent: 999px;
    overflow: hidden;
    width: 40px;
    height: 40px;
    background-size: 7px;
  }


  #newsletter {
      h2#title_newsletter {
        display: inline-block;
        text-transform: uppercase;
        color: white;
        font-size: 11px;
        margin-right: 15px;
        position: relative;
        top: -14px;
      }
      #newsletterButtonExternalDiv {
        display: inline-block;
        position: absolute;
        top: 5px;
        right: 260px;
      }
      .newsletter_checkbox {
          font-size: 11px;
          a, label {
              color: white;
          }
          a {
              text-decoration: underline;
          }
          label[for="promotions"] {
              display: inline;
          }
      }
  }



  .social_wrapper {
    margin-top: 17px;
  }

  .newsletter_wrapper, .social_wrapper {
    float: right;
    margin-left: 20px;
  }

  .bottom_footer.container12 {
    width: 900px;
    text-align: right;
    border-top: 1px solid white;
    margin-top: 55px;
    padding-top: 50px;
  }

  .bottom_footer {
    float: right;
  }

  .copyright_wrapper {
    float: left;
    width: 355px;
    display: inline-block;
    text-transform: uppercase;
    color: white;
    font-size: 12px;
    padding-right: 4px;
    border-right: 1px solid white;
  }

  .default_links {
    float: right;
    color: white;
    font-size: 12px;
  }

  .bottom_footer .default_links a {
    text-decoration: none;
    text-transform: uppercase;
    color: white;
    font-size: 11px;
  }
}

/*====== Rooms =======*/
.room_element {
  background: #EBECEE;
  display: table;
  width: 100%;
  margin-bottom: 7px;
  position: relative;

  .exceded {
    width: 270px;
    float: left;
    height: 180px;
    overflow: hidden;
    position: relative;

    .lupa {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  & > .description {
    float: right;
    width: 855px;
    box-sizing: border-box;
    padding: 23px 16px;
    padding-right: 150px;
    line-height: 20px;

    h3.room_title {
      text-align: left;
      font-size: 20px;
      font-weight: 300;
      color: #5CACDB;
      margin-bottom: 10px;
    }

    .room_description {
      font-size: 11px;
      color: gray;
      height: 76px;
      overflow: hidden;
    }
  }

  .see_more_room {
    position: absolute;
    right: 20px;
    top: 23px;
    background: white;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }

    span {
      padding: 0 10px 0 7px;
      font-size: 14px;
      font-style: italic;
      color: gray;
    }

    img:not(.lupa) {
      width: 25px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .book_room {
    position: absolute;
    right: 20px;
    top: 60px;
    background: $corporate_1;
    color: white;
    cursor: pointer;
    height: 24px;
    box-sizing: border-box;
    width: 99px;

    &:hover {
      opacity: 0.8;
    }

    a {

      font-size: 14px;
      font-style: italic;
      color: white;
      text-align: center;
      width: 100%;
      display: block;
      padding-top: 5px;
      text-decoration: none;
    }

  }

}

.hide_room_description {
  padding: 30px;

  .room_title {
    font-size: 20px;
    font-weight: 300;
    color: #5CACDB;
    margin-bottom: 10px;
  }

  .room_description {
    font-size: 13px;
    color: gray;
  }
}

/*======= Ofertas ======*/
a.plus {
  padding: 8px 8px 7px !important;
}

a.play {
  padding: 10px 9px 5px !important;
}

.enlace_offer {
  display: block;
  height: 250px;
  overflow: hidden;
  position: relative;

  img {
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
  }

}

.scapes-blocks {
  overflow: hidden;
  margin-top: 0px;
  margin-bottom: -12px;
}

.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;

  a.button-promotion.oferta-reserva {
    width: 100px;
    margin-right: 10px;
  }

  .description {
    padding: 20px;
    position: relative;
    background-color: #ECECEC;
    padding-right: 210px;

    .title-module {
      font-size: 23px;
      color: $corporate_1;
      font-weight: 500;
      margin-top: 6px;
      height: 40px;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 115px;
      right: 7px;
      top: 24px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline;

        a {
          background-color: $corporate_1;
          top: -3px;
          color: white;
          padding: 8px 7px 6px 7px;
          right: 97px;
          position: absolute;
          text-align: center;
          text-decoration: none;

          &:hover {
            opacity: 0.8;
          }
        }
        a.plus {
          padding: 10px 7px 5px;
          margin-right: -86px;
          height: 18px;
          background: #4C4C4C;
        }

        a.play {
          padding: 10px 9px 5px;

          img {
            margin-top: 2px;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.en a.button-promotion.oferta-reserva {
  width: 82px;
  right: 114px !important;
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_1;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}

.offer_popup {
  padding: 25px;
}

/*======= Content Access =====*/
.content_access {
  width: 1140px;
  h3.section-title {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: #5CACDB;
    margin-bottom: 28px;

    & + div {
      font-size: 17px;
      font-weight: lighter;
      line-height: 29px;
      color: #636363;
      text-align: justify;
    }
  }

  #my-bookings-form-fields {
    text-align: center;
  }

  form#my-bookings-form {
    text-align: center;
    padding-bottom: 1px;
  }

  #my-bookings-form-fields {
    margin-top: 20px;

    label {
      display: block;
      line-height: 18px;
      font-size: 17px;
      font-weight: lighter;
      color: #636363;
      text-align: center;
    }

    input {
      width: 160px;
      text-align: center;
    }

    input#emailInput {
      margin-bottom: 6px;
    }

    button#my-bookings-form-search-button {
      display: block;
      margin: 20px auto 0;
      width: 165px;
      border: 0;
      background: #007DAD;
      color: white;
      text-transform: uppercase;
      padding: 7px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  button#cancelButton {
    display: block;
    margin: 20px auto 0;
    width: 165px;
    border: 0;
    background: #007DAD;
    color: white;
    text-transform: uppercase;
    padding: 7px;
    cursor: pointer;
    display: none;
  }
}

/*========= Location and Contact ======*/
.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }
}

.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 40px;
  margin-top: 50px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: $corporate_1;
  width: 95%;
  line-height: 20px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  //margin-top: 30px;
  width: 100%;
  //margin-bottom: 30px;

}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-right: 5px;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;
  font-family: 'Raleway',Helvetica, Arial, Helvetica, sans-serif;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate_1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate_1 !important;
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #717171;
  line-height: 30px;
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

// Ticks

.ticks_wrapper_top {
  text-align: center;
  display: table;
  margin: auto;
  margin-bottom: 20px;
  margin-top: 10px;
  width: 100%;
}

.ticks_wrapper_top .tick_element.column4 {
  width: 380px;
  /* padding: 0 20px; */
  box-sizing: border-box;
  margin: 0;
}

.ticks_wrapper_top .title_image_tick {
  display: inline-block;
  margin-bottom: 18px;
  width: 100%;
  padding: 10px 0;
}

.ticks_wrapper_top .title_image_tick img.tick_image {
  margin-right: 10px;
  vertical-align: top;
}

.ticks_wrapper_top .title_image_tick .tick_title {
  font-size: 22px;
  color: white;
  display: inline;
}

.ticks_wrapper_top .description_tick {
  font-size: 15px;
  line-height: 20px;
  color: #646464;
  width: 320px;
  margin: 0 auto;
  display: inline-block;
}

// Cycle Banner

.subtitle_offers {
  text-align: center;
  font-size: 25px;
  padding-bottom: 20px;
  color: $corporate_1;
  font-weight: bolder;
}

.cycle_banners_wrapper {
  margin-bottom: 77px;
}

.cycle_banners_wrapper .cycle_element {
  background: $corporate_1;
  display: table;
  margin-bottom: 20px;
  height: 395px;
  width: 100%;
}

.cycle_banners_wrapper .cycle_element .exceded {
  width: 69%;
  overflow: hidden;
  float: left;
}

.exceded img {
  display: block;
  max-width: none;
}

.cycle_description {
  width: 31%;
  height: 395px;
  float: right;
  position: relative;
}

.vertical_center {
  display: table;
  position: absolute;
  top: 0;
  right: 40px;
  left: 30px;
  bottom: 0;
  margin: auto;
  text-align: justify;
  width: 300px;
}

h3.cycle_title {
  font-size: 35px;
  color: white;
}

.cycle_text {
  color: white;
  font-weight: 400;
  margin-top: 12px;
  line-height: 28px;
}

.vertical_center a {
  margin-top: 16px;
  display: block;
  float: right;
}

.cycle_banners_wrapper .cycle_element.right_banner .exceded {
  float: right;
}

//Slider top and bottom

.slider-top {
  margin-top: 20px;
  margin-bottom: 30px;

  .flex-inner-slidercontrol-nav {
    position: absolute;
    bottom: 10px;
    color: white;
    /* left: 0; */
    right: 324px;
    text-align: right;
  }

  .flexslider-top {
    position: relative;

    ol {
      position: absolute;
      bottom: 50px;
    }
  }
  .slider-image {
    float: left;
  }

  .slider-description {
    position: relative;
  }

  .content-description {
    a {
      position: absolute;
      bottom: 40px;
      left: 310px;
      //right: 340px;
    }
  }
}

.slider-bottom {
  .slider-image {
    float: right;
  }

  .slider-description {
    position: relative;
  }

  .content-description {
    a {
      position: absolute;
      bottom: 40px;
      left: 310px;
    }
  }

  .flexslider-bottom {
    position: relative;

    ol {
      position: absolute;
      bottom: 50px;
      width: 100px;
    }
  }

  .flex-inner-slidercontrol-nav {
    position: absolute;
    bottom: 10px;
    color: white;
    left: 42px;
    text-align: left;
  }
}

.slider-top, .slider-bottom {
  background: $corporate_1;
  color: white;

  .content-description {
    overflow: hidden;
    min-height: 370px;
    position: relative;
  }

  .slider-description {
    padding: 20px 40px;
    text-align: justify;

    h3 {
      font-size: 35px;
    }

    .description-text {
      font-weight: 300px;
      margin-top: 12px;
      font-size: 15px;
      line-height: 25px;
    }
  }

  .slider-image {
    img {
      display: block;
      width: 100%;
      height: 370px;
    }
  }

  /*img {
    display: block;
    max-width: none;
    height: 100px;
  }*/
}

//Slider left and right

.slider-bottom-module {
  clear: both;
  overflow: hidden;
}

.slider-left.column6 {
  width: 550px;
}

.flexslider-left {
  position: relative;
}

.weather-block {
  background: none;
  color: $corporate_1;
  position: absolute;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  padding: 16px 0px;
  font-size: 20px;
  padding-left: 40px;

  img {
    height: 25px;
    z-index: 4 !important;
    float: left;
    position: initial !important;
    margin-top: 5px;
    margin-right: 10px;
    width: 33px;
    margin-left: 15px;
  }

  .number {
    display: inline-block;
    float: left;
    margin-top: 5px;
    color: white;
  }

  .weather-text {
    float: left;
    margin-top: 7px;
    margin-right: 10px;
  }

  span.weather-location {
    border-right: 1px solid white;
    padding-right: 15px;

  }
}

.block-slide-left {
  position: relative;
  height: 378px;
  overflow: hidden;
}

.block-slide-left h3 {
  color: white;
  font-size: 20px;
  background: rgba(0, 0, 0, 0.6);
  padding: 20px 30px;
  z-index: 3;
}

.block-slide-left img {
  position: absolute;
  top: 0px;
  z-index: -1;
}

.flexslider-left .flex-direction-nav {
  position: absolute;
  top: 15px;
  right: 15px;
}

.flexslider-left .flex-direction-nav li {
  display: inline-block;
}

.slider-right.column6 {
  width: 550px;
}

.flexslider-right {
  position: relative;
}

.block-slide-right {
  position: relative;
  height: 378px;
  overflow: hidden;
  color: white;
}

.block-slide-right .description {
  background: rgba(0, 0, 0, 0.6);
  z-index: 3;
  font-size: 16px;
}

.block-slide-right img {
  position: absolute;
  top: 0px;
  z-index: -1;
}

.block-slide-right .description h3 {
  color: $corporate_1;
  font-size: 20px;
  padding: 20px 30px;
  cursor: pointer;
}

.block-slide-right .description p {
  padding: 20px 0px;
  margin: 0px 20px;
  border-top: 1px solid white;
  font-size: 15px;
  line-height: 25px;
}

.block-slide-right .description .arrow-down {
  position: relative;
  z-index: 100;
  float: right;
}

// bannersx3 gray

.bannersx3_gray {
  background: #f5f5f5;
}

.bannersx3_gray .bannersx3_wrapper {
  margin-bottom: 80px;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element {
  width: 33.33%;
  float: left;
  height: 600px;
  position: relative;
  overflow: hidden;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element .background {
  min-width: 100%;
  height: auto;
  max-width: none;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element .overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -ms-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element:hover .overlay {
  opacity: 0;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element .center_text {
  position: absolute;
  top: 46%;
  bottom: 0;
  left: 0;
  right: 0;
  display: table;
  margin: auto;
  width: 100%;
  text-align: center;
}

.bannersx3_gray .bannersx3_wrapper .bannerx3_element .center_text .bannerx3_title {
  font-size: 32px;
  font-weight: 700;
  color: white;
}

/*================  Banners x2 ===============*/
.banners_x2_wrapper {
  //margin-top: 80px;
  //margin-bottom: 87px;

  .ico-photo {
    position: absolute;
    width: auto;
    top: 20px;
    left: 20px;
  }
}

.bannerx2_image img {
  width: 100%;
}

p.bannerx2_title {
  color: #626262;
  text-align: center;
  font-size: 20px;
  margin-bottom: 30px;
  font-weight: bold;
  text-transform: uppercase;

  &:after {
    content: "";
    width: 55px;
    border-bottom: 2px solid $corporate_1;
    display: block;
    margin: 17px auto 0px;
  }
}

.bannerx2_row {
  position: relative;
  clear: both;
  display: table;
  width: 100%;
  margin-bottom: 5px;
}

.bannerx2_row.left {

  .bannerx2_image {
    float: left;
    width: 65%;
    margin-bottom: -5px;
    position: relative;
  }
  .bannerx2_text {
    width: 35%;
    float: right;
    position: absolute;
    right: 0px;
  }
}

.bannerx2_row.right {
  .bannerx2_image {
    float: right;
    width: 65%;
    margin-bottom: -5px;
    position: relative;
  }
  .bannerx2_text {
    width: 35%;
    float: left;
    position: absolute;
    left: 0px;
  }
}

.bannerx2_text {
  height: 100%;
}

.banner_center_container {
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  text-align: center;
  padding: 0px 0px;
  display: table;

  .button_wrapper_rooms {
    display: none;
  }
}

@-moz-document url-prefix() {
  .banner_center_container {
    padding-top: 52px;
  }
}

.bannerx2_description {
  font-size: 15px;
  padding: 0 40px;
  color: #797979;
  font-weight: bolder;
  margin-top: 12px;
  line-height: 28px;

  strong {
    font-weight: bold;
  }

  .hide_more {
    display: none;
  }
}

.flex-controlador {
  /*position: absolute;
  bottom: 5px;
  right: 0px;
  z-index: 22;
  height: 40px;
  width: 200px;
  display: block;
  background: $corporate_2;
  list-style-type: none;
  text-align: center;
  padding-top: 13px;
  box-sizing: border-box;
  padding-left: 9px;*/
  position: absolute;
  right: 343px;
  z-index: 22;
  bottom: 21px;

  li {
    /*display: inline;*/
    cursor: pointer;
    float: left;
    border: 1px solid white;
    border-radius: 26px;
    box-sizing: border-box;
    cursor: pointer;
    margin: 0 2px;
  }

  .circle_green {
    background: url("/img/#{$base_web}/bullet_flexslider.png") no-repeat;
    width: 20px;
    height: 20px;
    display: inline-block;
  }

  .circle_green.flex-inner-slideractive {
    background: url("/img/#{$base_web}/bullet_flexslider_active.png") no-repeat;
    margin-bottom: 4px;
  }

  span {
    width: 12px;
    height: 12px;
    display: block;
    border-radius: 26px;
    margin: 2px;
    background: white;
    box-sizing: border-box;
  }
  span.flex-inner-slideractive {
    background: #fdae47;
  }
}

.flex-inner-slidercontrol-nav {
  position: absolute;
  bottom: 10px;
  color: white;
  left: 0;
  right: 0;
  text-align: center;

  li {
    display: inline-block;
    width: 20px;
    height: 20px;

    a {
      background: url("/img/morer/bullet_flexslider.png") no-repeat center;
      display: inline-block;
      width: 20px;
      vertical-align: middle;
      color: transparent;
      cursor: pointer;
    }
    .flex-inner-slideractive {
      background: url("/img/morer/bullet_flexslider_active.png") no-repeat center;
    }
  }
}

.flex_prev {
  height: 30px;
  width: 20px;
  left: 10px;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  background: url("/static_1/images/booking/flecha_motor_izq.png") no-repeat center;
  background-size: 9px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}

.flex_next {
  height: 30px;
  width: 20px;
  right: 10px;
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  background: url("/static_1/images/booking/flecha_motor_der.png") no-repeat center;
  background-size: 9px;
  cursor: pointer;
}

.flex-inner-sliderdirection-nav {
  display: none;
}

.bannerx2_row.left .video_wrapper {
  float: left;
  width: 65%;
  position: relative;
}

.bannerx2_row.right .video_wrapper {
  float: right;
  width: 65%;
  margin-bottom: -5px;
  position: relative;
}

.video_wrapper {
  overflow: hidden;
  padding-bottom: 5px;
  position: relative;

  img.video_cover_iframe {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 2;
  }

  &:before {
    content: "";
    padding-top: 47%;
    display: block;
  }

  iframe {
    position: absolute;
    top: 0px;
    bottom: 0px !important;
    height: 99%;
    left: 0px;
    right: 0px;
  }
}

img.play_pause_video {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  width: 200px;
  height: 200px;
  margin: auto;
}

.button_wrapper_rooms {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background-color: $corporate_2;
  color: white;
  margin: 15px auto 0 !important;
  text-transform: uppercase;
  padding: 6px;
  box-sizing: border-box;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.rooms_buttons_wrapper {
  text-align: center;
}

a.button-promotion.room_promotion {
  background: $corporate_1;
  color: white;
  text-decoration: none;
  padding: 6px 20px;
  text-transform: uppercase;
  margin-top: 15px;
  display: inline-block;
}

h3.bannerx2_title {
  color: $corporate_1;
  text-align: center;
  margin-bottom: 4px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 25px;
}

.hide_more {
  line-height: 22px;
  font-size: 12px;
}

.hidden_cycle {
  padding: 20px;

  .bannerx2_title {
    text-align: left;
    &:after {
      margin-left: 0;
    }
  }
  .bannerx2_description {
    padding: 0px 0px;
  }
  .hide_more {
    font-size: 15px;
    font-weight: bolder;
  }
}

/*=== Gallery ===*/
.border-gallery .gallery_filt_title {
  font-weight: 500;
  line-height: 45px;
  border-bottom: 0px solid #8e8b8b;
  margin-top: 20px;
  width: 100%;
  text-align: center;
  font-size: 34px !important;
  color: $corporate_1;
}

.border-gallery .wrapper_filt .gallery_1 li {
  width: 283px !important;
  height: 187px !important;

  .crop img {
    height: auto;
  }
}

.wrapper_filt .gallery_1 li:first-of-type .crop img {
  height: auto !important;
}

.tp-bullets {
  bottom: -35px !important;
  opacity: 1 !important;
  background: transparent;
  z-index: 23 !important;
  width: 1140px;
  margin: auto;
  text-align: center;

  &:before {
    border-top: 1px solid #bebebe;
    content: '';
    width: 1140px;
    display: block;
    position: absolute;
    left: 0;
    top: 12px;
    z-index: -1;
  }

  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: -10px;
    left: -10px;
    bottom: 0;
    background: white;
    z-index: -1;
    width: 200px;
    margin: auto;
  }

  .bullet {
    background: url("/img/morer/sprit-slider.png?v=1") no-Repeat top left !important;
    float: none !important;
    display: inline-block;

    &.selected {
      background-position: bottom left !important;
    }
  }
}

#social img {
  margin-right: 15px;
  margin-top: 10px;
}

.social_wrapper img {
  margin-right: 15px;
  margin-top: 5px;
}

// Booking widget popup
.fancybox-inner {
  //width: 300px !important;
}

// Popup Ver mas
.fancybox-inner {
  padding: 20px;

  .title-module-popup {
    font-size: 23px;
    color: #0099bb;
    font-weight: 500;
    margin-top: 6px;
    height: 40px;
  }

  .booking_title_custom{
    display: none;
  }
}



// Resumen reserva
.my-bookings-booking-info {
  margin: 0 auto !important;
}

// Slider
.forcefullwidth_wrapper_tp_banner {
  margin-top: 150px !important;
}

// Title Booking widget
#custom_new_title {
  margin-left: 160px;
}

.my_booking_section {
  & > div {
    display: none;
  }

  .cancel_booking_questions {
    display: table;
  }
}

// Languages
.fr a.button-promotion.oferta-reserva {
    width: 110px !important;
}

.fr .copyright_wrapper {
    width: 318px !important;
}

.de .copyright_wrapper {
  width: 338px !important;
}

.de a.button-promotion.oferta-reserva {
  margin-right: -5px !important;
}

.it a.button-promotion.oferta-reserva {
    margin-right: -20px !important;
}

.it .copyright_wrapper {
    width: 465px !important;
}

.ru{

  .scapes-blocks .block .description ul{
    right:-9px;
  }
  .scapes-blocks .block a.button-promotion.oferta-reserva{
    width:120px;
  }
  .scapes-blocks .block .description ul li a.plus{
    margin-right:-70px;
  }
}

.ca .copyright_wrapper {
  width: 349px !important;
}

.es .copyright_wrapper {
  width: 344px !important;
}

.ru .copyright_wrapper {
  width: 365px !important;
}

.en .copyright_wrapper {
  width: 365px !important;
}


/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate_1;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  width: 120px;
  background: $gray-3;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }
    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}
textarea#cancellation-reasons {
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}