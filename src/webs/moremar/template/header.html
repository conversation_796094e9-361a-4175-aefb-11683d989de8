<header>
    <div id="wrapper-header" class="container12">


        <div id="logoDiv" class="column3">
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <div class="column3 right_header">
            
            <div id="lang">
                <img class="lang_image" src="/img/{{ base_web }}/ico-idiomas-header.png?v=1">
                <span id="selected-language" class="selected-language2">{{ language_selected }}</span>
                <span class="arrow"></span>
                <ul id="language-selector-options" class="language-selector-options2" style="display: none;">
               {% for key, value in language_codes.items %}
                    <!--<a href="{% if not key == 'es' %}{{ hostWithoutLanguage }}/{{ key }}/{% else %}/{% endif %}" {% if  key == language %} class="selected" {% endif %}>-->
                       <!--{{ value|upper }}-->
                    </a>
                    <li class="language-option-flag">
                        <a hreflang="{{ key }}"
                           href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{{ value }}</a>
                    </li>
                {% endfor %}
                </ul>
            </div>

            <div id="top-sections">
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                        <img src="/img/{{ base_web }}/{% cycle 'ico-misReservas-header.png?v=1' 'location.png' %}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
            </div>

            <div class="hotel_phone_wrapper">
                <img src="/img/{{ base_web }}/ico-tlf-header.png?v=1" alt="hotel_phone" title="hotel_phone">
                <span class="hotel_phone">{{ phones|safe }}</span>
            </div>

           <div id="social">
                {%if facebook_id %}
                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-facebook-header.png?v=1" alt="{{ T_siguenos_en }} facebook" title="{{ T_siguenos_en }} facebook"></a>
                {% endif %}
                {% if twitter_id %}
                <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-twitter-header.png?v=1" alt="{{ T_siguenos_en }} twitter" title="{{ T_siguenos_en }} twitter"></a>
                {% endif %}
                {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher"> <img src="/img/{{ base_web }}/social/ico-google-header.png" alt="{{ T_siguenos_en }} google+" title="{{ T_siguenos_en }} google+"></a>
                {% endif %}
                {% if youtube_id %}
                <a href="https://www.youtube.com/{{youtube_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/ico-youtube-header.png" alt="{{ T_siguenos_en }} youtube" title="{{ T_siguenos_en }} youtube"></a>
                {% endif %}
            </div>
        </div>
        <div class="column6 middle_header">

            <nav id="main_menu">
                <div id="mainMenuDiv">
                    {% include "main_div.html" %}
                </div>
            </nav>

        </div>
    </div>

</header>