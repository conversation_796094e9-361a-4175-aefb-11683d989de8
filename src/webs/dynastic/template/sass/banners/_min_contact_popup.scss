.contact_overlay {
  @include full_size;
  position: fixed;
  background-color: #000000;
  opacity: .6;
  z-index: 9999;
}
.contact_popup_wrapper {
  @include center_xy;
  position: fixed;
  z-index: 10000;
  background: white;
  border-radius: 18px;
  padding: 30px 55px;
  @include display_flex;
  align-items: center;
  justify-content: center;

  .close_popup {
    position: absolute;
    top: -50px;
    color: white;
    right: 0;
    font-size: 34px;
    cursor: pointer;
  }

  .contact_popup_title {
    display: block;
    font-size: 26px;
    font-family: $title_family;
    text-align: center;
    color: $corporate_1;
    margin-bottom: 10px;
    small {
      font-weight: lighter;
      font-size: 18px;
      display: block;
    }
  }
  .form_wrapper {
    width: 100%;
    padding: 0 10px;
    form {
      @include display_flex;
      flex-flow: column;
      align-items: center;
      .bordeInput {
        margin: 10px 0;
        width: 100%;
        padding: 10px 0;
        border: none;
        border-bottom: 2px solid $corporate_1;
        color: $corporate_1;
        font-size: 12px;
        outline: none;
        &::placeholder {
          text-transform: uppercase;
          color: $corporate_1;
        }
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      input[type=number] {
        -moz-appearance: textfield;
      }
      #contact-button {
        font-family: $title_family;
        background: $corporate_2;
        text-transform: uppercase;
        color: white;
        border: none;
        border-radius: 0;
        cursor: pointer;
        width: 110px;
        padding: 10px 20px;
        font-size: 16px;
        margin-top: 20px;
        &:hover {
          background: $corporate_1;
        }
      }
    }
  }
  @media screen and (max-width: 700px) {
    padding: 30px 30px;
    width: 70%;
  }
}