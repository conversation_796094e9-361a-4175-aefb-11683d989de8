<link rel="stylesheet" href="/static_1/css/templates/mobile/promotions/promotions_3.css" type="text/css" media="screen"/>
<div class="offers_wrapper">

    {% for currentItem in elements %}

        <div class="promotions-item">
            <h3 class="promotion_name">{{ currentItem.name|safe }}</h3>
            {% if currentItem.picture %}
                <div>
                    <img data-src="{{ currentItem.picture }}=s800" alt="photo-offer" lazy_mobile="true"/>
                </div>
            {% endif %}

            <div class="promotion_description">{{ currentItem.description|safe }}</div>

            <br/>

            {% if currentItem.link %}
                <div class="offer_link"><a href="{{ currentItem.link|safe }}">{{ T_ver_mas }}</a></div>
            {% endif %}


            <div class="booking_general_button">
                <a data-role="button" class="button button-promotion"
                   {% if "BONOREGALO" in currentItem.promocode %}data-promocodebono="{{ currentItem.promocode }}"{% endif %}
                   {% if currentItem.bonoRegaloMinDay %}data-bonoRegaloMinDay="{{ currentItem.bonoRegaloMinDay }}"{% endif %}
                   {% if currentItem.bonoRegaloMaxDay %}data-bonoRegaloMaxDay="{{ currentItem.bonoRegaloMaxDay }}"{% endif %}
                   {% if currentItem.bonoRegaloIniDay %}data-bonoRegaloIniDay="{{ currentItem.bonoRegaloIniDay }}"{% endif %}
                   {% if "BONOREGALO" in currentItem.promocode %}data-bonoregalo-title="{{ currentItem.name|safe }}"{% endif %}
                   {% if currentItem.promocode %}data-promocode="{{ currentItem.promocode|safe }}"{% endif %}
                        >
                    {{ T_reservar }}
                </a>
            </div>

        </div>
    {% endfor %}

</div>


<script>

    function createCookie(key, valor) {
        document.cookie = key + "=" + valor;
    }

    $(".button-promotion").click(function () {
        promocode = $(this).attr('data-promocodebono');
        min_day = $(this).attr('data-bonoregalominday');
        max_day = $(this).attr('data-bonoregalomaxday');
        ini_day = $(this).attr('data-bonoregaloiniday');
        bonoregalo_title = $(this).attr('data-bonoregalo-title');

        createCookie("offer", "promocodebono|" + promocode +
                "-bonoregalominday|" + min_day +
                "-bonoregalomaxday|" + max_day +
                "-bonoregaloiniday|" + ini_day +
                "-bonoregalotitle|" + bonoregalo_title
        );
    });

    $(function () {
        $(".room_element .room_description").each(function (n) {
            var number_words = $(this).html().split(" ");
            if (number_words.length > 50) {
                var normal_text = number_words.slice(0, 50);
                var hidden_text = number_words.slice(51);
                var final_text = normal_text.join(" ") + '<span class="hide_room_description">' + hidden_text.join(" ") + '</span><div class="see_more_room">{{ T_ver_mas }}</div>';
                $(this).html(final_text);
            }
        });

        $(".see_more_room").click(function () {
            $(this).parent().find(".hide_room_description").slideToggle();
        });

    });

     $(".promotions-item .booking_general_button").click(function () {
        if ($(this).hasClass('opened')) {
            $(this).removeClass('opened');
            return;
        }
        $(".promotions-item .booking_general_button").each(function () {
            if ($(this).hasClass('opened')) {
                $(this).removeClass('opened');
                $(".hidden_booking_widget").slideUp('fast');
            }
        });
        $(this).addClass('opened');
        var x = $(".hidden_booking_widget").detach();
        x.insertAfter($(this));
    });
</script>

	


