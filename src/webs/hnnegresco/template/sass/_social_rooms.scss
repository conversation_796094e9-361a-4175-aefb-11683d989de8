img.social_rooms {
  position: fixed;
  right: 0;
  bottom: 20px;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  z-index: 1003;
  cursor: pointer;

  &.active {
    right: 100%;
    margin-right: 0;
  }
}

.social_elements_wrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 100%;
  width: 100%;
  z-index: 1002;
  background: $corporate_1;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;

  &.active {
    left: 0;
  }
}

.all_elements_wrapper_social {
  position: absolute;
  width: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  .content_wrapper_social {
    width: 750px;
    padding: 20px;
    margin: auto;
    color: white;
    letter-spacing: 1px;
    display: block;
    height: 100%;

    & > h3 {
      font-family: "<PERSON>";
    }

    h3 {
      text-align: center;
      font-size: 25px;
      margin-bottom: 45px;
      text-transform: uppercase;
    }
    h4 {
      font-weight: bold;
      padding-bottom: 10px;
    }
    p{
      text-align: justify;
      letter-spacing: 2px;
      padding-bottom: 30px;
    }
    ul{
      list-style-type: disc;
      padding-left: 15px;
      padding-bottom: 30px;
    }
    .content_social {
      height: 400px;
      width: 100%;
      overflow: auto;
      padding: 0 20px;
    }
    .button-promotion {
      display: block;
      border-radius: 0px !important;
      border: 0;
      background: #F3D132;
      color: $white;
      margin: auto !important;
      margin-top: 50px !important;
      text-transform: uppercase;
      padding: 20px 50px;
      font-size: 24px;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.close_social_rooms_button {
  float: right;
  margin-top: 10px;
  margin-right: 20px;
  width: 50px;
  cursor: pointer;
  position: relative;
  z-index: 999;

  &:before {
    content: "x";
    color: white;
    font-size: 85px;
    line-height: 36px;
    font-family: 'Montserrat', sans-serif;
  }
}