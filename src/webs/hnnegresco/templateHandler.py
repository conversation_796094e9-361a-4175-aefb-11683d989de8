# -*- coding: utf-8 -*-
import json

from booking_process.utils.calendar.utilities.closed_hotel_utils import get_range_hotel_closed
from booking_process.utils.language.language_constants import SPANISH
from booking_process.constants.advance_configs_names import CONTACT_PHONES, MAIN_SECTIONS, GOOGLE_TAG_MANAGER_ID, CLOSED_HOTEL
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
	get_section_from_section_spanish_name_with_properties
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os
from collections import OrderedDict

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "hnneo"

class TemplateHandler(BaseTemplateHandler2WithRedirection):
	__number_babies = 3
	__number_kids = 5

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)


		result_params_dict = {'base_web': base_web,
							  'aviso_legal': get_section_from_section_spanish_name("aviso legal", language),
							  'contact_phone': get_config_property_value(CONTACT_PHONES),
							  'newsletter': self.buildNewsletter2(language, name=True, social=True, check_newsletter=True),
							  'number_babies_widget': self.__number_babies,
							  'number_kids_widget': self.__number_kids,
							  'floating_button': get_section_from_section_spanish_name('floating_button', language),
							  'booking_engine_2': self.buildSearchEngine2(language),
							  'gtm_tag': get_config_property_value(GOOGLE_TAG_MANAGER_ID),
							  'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False})
							  }

		if sectionToUse.get('subtitle'):
			result_params_dict['content_subtitle'] = sectionToUse

		if self.getPicturesProperties(language, 'extra_top_header'):
			result_params_dict['extra_top_header'] = self.getPicturesProperties(language, 'extra_top_header')

		if user_agent_is_mobile():
			extra_top_header = self.getPicturesProperties(language, 'extra_top_header')
			if extra_top_header:
				result_params_dict['extra_code_mobile'] = self.getMobileHeaderBanner(extra_top_header)

		if get_config_property_value(CLOSED_HOTEL):
			result_params_dict['closed_hotel'] = json.dumps(get_range_hotel_closed(SPANISH))


		#Automatic Content
		automatic_content = {
		'Galeria de Imagenes': True,
		'Mis Reservas': True
		}

		# Website menu
		all_sections = self.getSections(language)
		menu_sections = self.getSectionsFor(language, all_sections, MAIN_SECTIONS)
		for menu_element in menu_sections:
			section_pictures = getPicturesForKey(language, menu_element.get('key'), [])
			menu_element['icon'] = list(filter(lambda x: x.get('title') == 'ico', section_pictures))

		result_params_dict['menu_personalized'] = menu_sections

		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		if section_type == "Inicio":
			result_params_dict['home'] = True

		elif section_type == "Habitaciones":
			result_params_dict['rooms'] = get_pictures_from_section_name("habitaciones_blocks", language)
			result_params_dict['rooms'] = list(filter(lambda l: l.get("title") != "background", result_params_dict['rooms']))

		elif section_type == u'Habitación Individual' or section_type == 'Oferta Individual':
			result_params_dict['room_details'] = sectionToUse
			room_section_pictures = get_pictures_from_section_name(sectionToUse['sectionName'], language)
			slider_pictures_room = list(filter(lambda x: x.get('title') == 'slider', room_section_pictures))
			if slider_pictures_room:
				result_params_dict['pictures'] = slider_pictures_room
			result_params_dict['room_details']['pictures'] = list(filter(lambda x: x.get('title') not in ['slider'], room_section_pictures))

			if section_type == u'Habitación Individual':
				room_blocks = get_pictures_from_section_name('habitaciones_blocks"', language)
				room_blocks = list(filter(lambda l: l.get("title") != "background", room_blocks))
				for number, room_element in enumerate(room_blocks):
					actual_room_section = sectionToUse.get('friendlyUrlInternational', '')
					room_element_section = room_element.get('linkUrl')
					if room_element_section == actual_room_section:
						previous_room_number = number - 1
						next_room_number = 0 if len(room_blocks) - 1  == number else number + 1
						result_params_dict['previous_room'] = room_blocks[previous_room_number].get('linkUrl')
						result_params_dict['next_room'] = room_blocks[next_room_number].get('linkUrl')
						break

			result_params_dict['content_subtitle'] = None
			result_params_dict['offer_individual'] = section_type == 'Oferta Individual'

		elif section_type == "Ofertas":
			result_params_dict['offers'] = self.getOffers(language)

		elif section_type == u"Localización":
			result_params_dict['content_location'] = get_section_from_section_spanish_name(section_name, language)
			section_pictures = get_pictures_from_section_name(section_name, language)
			images_location = list(map(lambda x: x.get('servingUrl'), filter(lambda x: x.get('title') not in ['slider', 'ico'], section_pictures)))
			if images_location:
				result_params_dict['content_location']['pictures'] = images_location[0]
			result_params_dict['iframe_google_maps'] = get_section_from_section_spanish_name("Iframe google maps", language)

			additionalParams4Contact = {
			'language': language,
			'extra': None,
			'picturesInSlider': True,
			'privacy_checkbox': True,
			'reservation_question': True
			}

			sectionTemplate = 'secciones/contact.html'
			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = '' #we dont need here the description. We've got it above
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			result_params_dict['contact_html'] = contact_html

		elif section_type == 'Galeria de Imagenes':
			result_params_dict['gallery_section'] = True

		elif section_type == 'Normal':
			section_to_render = get_pictures_from_section_name(sectionToUse['sectionName'], language)
			if section_to_render:
				slider_pictures_room = list(filter(lambda x: x.get('title') == 'slider', section_to_render))
				if slider_pictures_room:
					result_params_dict['pictures'] = slider_pictures_room

		result_params_dict = self.getExtraBanners(advance_properties, result_params_dict, language)


		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "560"

	def get_revolution_initializer(self):
		return True

	def getExtraBanners(self, advance_properties, result_params, language):
		result_params['custom_element_home_upper'] = ''

		if advance_properties.get('ticks', False):
			result_params['ticks'] = get_pictures_from_section_name(advance_properties.get('ticks'), language)
		if advance_properties.get('minigallery', False):
			result_params['minigallery'] = get_pictures_from_section_name(advance_properties.get('minigallery'), language)
		if advance_properties.get('bannersx3', False):
			bannersx3_image = get_pictures_from_section_name(advance_properties.get("bannersx3"), language)
			bannersx3_html = self.buildTemplate_2("banners/_bannersx.html", {'banners': bannersx3_image, 'size_image': 800},allowMobile=False)
			result_params["bannersx3"] = bannersx3_html
		if advance_properties.get('banner_mosaic', False):
			result_params['banner_mosaic'] = get_section_from_section_spanish_name(advance_properties.get('banner_mosaic'), language)
			result_params['banner_mosaic_images'] = self.getPicturesProperties(language, advance_properties.get('banner_mosaic'), ['youtube'])
			for image_in_block_mosaic in result_params['banner_mosaic_images']:
				if image_in_block_mosaic.get('linkUrl'):
					result_params['banner_mosaic_link'] = image_in_block_mosaic.get('linkUrl')
		if advance_properties.get('room_blocks', False):
			result_params['room_blocks_title'] = get_section_from_section_spanish_name(advance_properties.get('room_blocks'), language)
			room_block_images = self.getPicturesProperties(language, advance_properties.get('room_blocks'), ['background'])
			result_params['room_blocks'] = []
			for image_in_block in room_block_images:
				if image_in_block.get('title') != 'background':
					result_params['room_blocks'].append(image_in_block)
				else:
					result_params['room_blocks_background'] = image_in_block

				if image_in_block.get("background"):
						result_params['room_blocks_background_color'] = image_in_block.get("background")

		if advance_properties.get("banner_ventajas"):
			myParams = {
				'ticks': self.getPicturesProperties(language, advance_properties.get("banner_ventajas")),
				'content_section': get_section_from_section_spanish_name_with_properties(advance_properties.get("banner_ventajas"), language)
			}
			myParams.update(dict(get_web_dictionary(language)))
			result_params['banner_ventajas'] = self.buildTemplate_2("/banner/_banner_ventajas.html", myParams, False,thisWeb)

			if user_agent_is_mobile():
				mini_html = self.buildTemplate_2("/banner/_banner_ventajas.html", myParams, False, thisWeb)
				result_params['custom_element_home_upper'] += mini_html

		return result_params

	def buildSearchEngine(self, language, selectOptions=None):
		if user_agent_is_mobile():
			return super(TemplateHandler, self).buildSearchEngine(language, selectOptions)
		
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, allowMobile=False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		return options 

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['horizontal_nolabel'] = True
		options['custom_new_title'] = get_section_from_section_spanish_name('saber mas', language).get('content', '')
		options['caption_submit_book'] = True

		self.__number_babies = len(options['validBabies'])
		self.__number_kids = len(options['validKids'])
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
		'Galeria de Imagenes': 'secciones/gallerys_new/gallery_full_width.html'
		}

		template = templateSectionsDict.get(sectionType,
											super(TemplateHandler, self).getTemplateForSectionType(sectionType,
																								   sectionTemplate))

		return template

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)
		for offer in offers:
			pictures_offer = getPicturesForKey(language, str(offer.get("offerKey")), [])
			if pictures_offer:
				if pictures_offer[0].get("linkUrl"):
					offer['link'] = pictures_offer[0].get("linkUrl")
		return offers

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			filters = OrderedDict()
			picture_gallery = self.get_hotel_gallery(language)
			for img in picture_gallery:
				nameFilter = img.get("title")
				if not filters.get(nameFilter, False):
					filters[nameFilter] = [img['servingUrl']]
				else:
					filters[nameFilter].append(img['servingUrl'])

			result['filters_gallery'] = filters


		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getRoomsBlocks(self, language, all_sections):
		rooms_blocks = self.getPicturesProperties(language, "habitaciones_blocks", [])
		rooms = []
		for room in rooms_blocks:
			if room.get("linkUrl"):
				room_section = list(filter(lambda l: l.get("friendlyUrlInternational") == room.get("linkUrl"), all_sections))
				if room_section:
					gallery_room = get_pictures_from_section_name(room_section[0].get("sectionName"), language)
					room['gallery'] = list(filter(lambda l: l.get("title") != "slider", gallery_room))
				rooms.append(room)
		return rooms

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''
			section_name = ''
			base_path = os.path.dirname(__file__)
			if currentSection:
				section_name = currentSection['sectionName'].lower().strip()

			if user_agent_is_mobile():
				if advance_properties.get("cycle_banners"):
					cycle_dict = dict(get_web_dictionary(language))
					cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("cycle_banners"), language)
					cycle_html = self.buildTemplate_2("mobile_templates/1/_cycle_banners_v1.html", cycle_dict, False)
					additionalParams['custom_elements'] += cycle_html

				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if section_name == u'ubicación':
					additionalParams['title'] = currentSection.get('title')
					additionalParams['subtitle'] = currentSection.get('subtitle')
					additionalParams['content'] = currentSection.get('content')
					additionalParams['iframe_google_maps'] = get_section_from_section_spanish_name('iframe google maps', language)
					sectionTemplate = os.path.join(base_path, 'template/mobile/ubication.html')
					return buildTemplate(sectionTemplate, additionalParams)


			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)


	def getMobileHeaderBanner(self, top_header):
		base_path = os.path.dirname(__file__)
		mobile_template_params = {
			'extra_top_header': top_header
		}
		fullPath_extra_header = os.path.join(base_path, 'template/mobile/_extra_top_header.html')
		mobile_template = buildTemplate(fullPath_extra_header, mobile_template_params)
		return mobile_template

	def buildPage(self, property_text, language):
		result_html = ""
		section_name = property_text
		section = get_section_from_section_spanish_name(section_name, language)
		section_properties = self.getSectionAdvanceProperties(section, language)
		banner1_html = ""

		if banner1_html:
			result_html += banner1_html

		return result_html