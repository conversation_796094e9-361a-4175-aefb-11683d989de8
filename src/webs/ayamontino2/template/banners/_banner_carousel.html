<div class="banner_carousel_full_wrapper">
    {% if banner_carousel_content.subtitle %}<h3>
        {{ banner_carousel_content.subtitle|safe }}
        {% if banner_carousel_ico %}<i class="{{ banner_carousel_ico.0.description|safe }}"></i>{% endif %}
    </h3>{% endif %}

    <div class="banner_carousel_wrapper owl-carousel">
        {% for banner in banner_carousel %}
            <div class="banner">
                <div class="banner_pic">
                    <img src="{{ banner.servingUrl }}=s380-c" alt="{{ banner.title|safe }}">
                    {% if banner.linkUrl %}<a href="{{ banner.linkUrl|safe }}" class="btn">{{T_ver_mas}}</a>{% endif %}
                </div>
                <div class="banner_content">
                    <h2>{{banner.title|safe}}</h2>
                    {%if banner.altText %}<span class="extra">{{banner.altText|safe}}</span>{% endif %}
                    <div class="desc">{{banner.description|safe}}</div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>$(window).load(function () {
    $(".banner_carousel_wrapper").owlCarousel({
        loop: false,
        nav: true,
        dots: false,
        items: {% if is_mobile %}1{% else %}3{% endif %},
        navText: ['<i class="fa icon-longarrow"></i>', '<i class="fa icon-longarrow"></i>'],
        margin: 40,
        autoplay: false
    });
});</script>

{% if not is_mobile and not user_isIpad %}
    <script>
    $(window).load(function () {
        function banner_carousel_fx() {
            $(".banner_carousel_wrapper .banner").addnimation({parent:$(".banner_carousel_wrapper"),class:"zoomInUp",classOut:"zoomOutUp"});
        }
        banner_carousel_fx();
        $(window).scroll(banner_carousel_fx);
    });
    </script>
{% endif %}