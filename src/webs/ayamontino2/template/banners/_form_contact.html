<div class="contact_form_wrapper">
    <div class="container12">
        <h3>{{ T_formulario_contacto }}</h3>

        <form name="contact" id="contact" method="post" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>
            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

                <div class="contInput">
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                <div class="contInput">
                    <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
                </div>
                <div class="contInput area">
                    <textarea style="height:120px;" type="text" id="comments" name="comments" class="bordeInput" value="" placeholder="{{ T_comentarios }}"></textarea>
                </div>
                <div class="contInput">
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                           placeholder="{{ T_telefono }}"/>
                </div>
                <div class="contInput">
                    <input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value="" placeholder="{{ T_confirm_email }}"/>
                </div>
                <div class="contInput captcha">
                    <script src='https://www.google.com/recaptcha/api.js'></script>
                    <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
                <div class="contInput policy-terms">
                    <div>{{ T_campos_obligatorios }}</div>
                    <input type="checkbox" id="accept-term" name="accept_term"/>
                    <a class="myFancyPopup fancybox.iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    $(window).load(function () {

        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
                                   rules: {
                                       name: "required",
                                       email: {
                                           required: true,
                                           email: true
                                       },
                                       emailConfirmation: {
                                           required: true,
                                           equalTo: "#email",
                                           email: true
                                       },
                                       telephone: {
                                           required: true,
                                           phone: true
                                       },
                                       comments: "required",
                                       accept_term: "required"
                                   },
                                   messages: {
                                       name: "{{ T_campo_obligatorio}}",
                                       email: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           email: "{{ T_campo_valor_invalido|safe }}"
                                       },
                                       emailConfirmation: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           email: "{{ T_campo_valor_invalido|safe }}",
                                           equalTo: "{{T_not_confirmed_email_warning|safe}}"
                                       },
                                       telephone: {
                                           required: "{{ T_campo_obligatorio|safe }}",
                                           phone: "{{ T_campo_valor_invalido|safe }}"
                                       },
                                       comments: "{{ T_campo_obligatorio|safe }}",
                                       accept_term: "{{ T_campo_obligatorio|safe }}"
                                   }

                               });

        $("#contact-button").click(function () {
            var data = {
                'name': $("#name").val(),
                'surname': $("#surname").val(),
                'telephone': $("#telephone").val(),
                'email': $("#email").val(),
                'comments': $("#comments").val(),
                'g-recaptcha-response': $("#g-recaptcha-response").val()
            };
            if ($("#contact").valid()) {
                if ($("#g-recaptcha-response").val()) {
                       $.post(
                               "/utils/?action=contact", data,
                               function (data) {
                                   alert("{{ T_gracias_contacto }}");
                                   $("#name").val("");
                                   $("#surname").val("");
                                   $("#telephone").val("");
                                   $("#email").val("");
                                   $("#emailConfirmation").val("");
                                   $("#comments").val("");
                               }
                       );
                       } else {
                            $(".g-recaptcha > div > div").css('border', '1px solid red');
                       }

            }
        });

        function contact_form_fx() {
            $(".contact_form_wrapper .container12").addnimation({parent:$(".contact_form_wrapper"),class:"fadeInUp"});
        }
        $(window).scroll(contact_form_fx);
    });
</script>