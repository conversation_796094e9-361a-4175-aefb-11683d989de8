<div class="section_offers_wrapper">
    <div class="banner_offers">
        {% for offer in offers %}<div class="offer {% if offer.priority and 'P' in offer.priority %}paquete{% else %}oferta{% endif %}">
            <div class="offer_image">
                <img src="{{ offer.picture }}=s500-c" alt="{{ offer.name }}">
                <div class="center_xy"><span>{{ offer.picDesc|safe }}</span></div>
            </div>
            <div class="offer_content">
                <div class="text">
                    <div class="title">{{ offer.name|safe }}</div>
                    <div class="desc">{{ offer.description|safe }}</div>
                </div>
                <div class="btn_offer {% if offer.linkUrl %}btn_half{% endif %}">
                    <a href="#data" class="button_promotion" data-not_in="{{ offer.not_in }}">{{ T_reservar }}</a><!--
                    -->{% if offer.linkUrl %}<a href="{{ offer.linkUrl }}">{{ T_ver_mas }}</a>{% endif %}
                </div>
            </div>
        </div>{% endfor %}
    </div>
</div>

{% if not is_mobile and not user_isIpad %}
    <script>
    max_offer_height = 0;
    $(".banner_offers .offer").each(function () {
        var actual_height = $(this).find(".offer_content .text").outerHeight();
        if (actual_height > max_offer_height) {
            max_offer_height = actual_height;
        }
    });

    $(".banner_offers .offer .offer_content .text").height(max_offer_height);

    $(window).load(function () {
        function offers_fx() {
            $(".section_offers_wrapper .offer").addnimation({parent:$(".section_offers_wrapper"),class:"bounceInUp"});
        }
        offers_fx();
        $(window).scroll(offers_fx);
    });
    </script>
{% endif %}