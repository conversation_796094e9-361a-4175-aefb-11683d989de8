.banner_offers_wrapper, .section_offers_wrapper {
  position: relative;
  padding: 50px calc((100% - 1140px) / 2);
  font-family: $text_family;
  &:before {
    content: '';
    @include full_size;
    left: calc((100% - 1040px) / 2);
    z-index: -1;
    background-color: rgba($corporate_2, .4);
    border-radius: 30px 0 0 30px;
  }
  h1 {
    position: relative;
    color: #222;
    font-size: 30px;
    font-weight: 600;
    line-height: 35px;
    text-align: center;
    font-family: $title_family;
    big {
      font-size: 36px;
      font-weight: 400;
      color: $corporate_1;
    }
    &:after {
      content: '';
      display: block;
      width: 60px;
      height: 5px;
      background: $corporate_1;
      margin: 20px auto 50px;
    }
  }
  .banner_offers {
    text-align: center;
    min-height: 600px;
    .offer {
      box-shadow: 0 0 10px 0 grey;
      &:hover {
        .offer_image {
          &:before {
            opacity: 1;
          }
          .center_xy {
            -webkit-transform: scale(1);
            -moz-transform: scale(1);
            -ms-transform: scale(1);
            -o-transform: scale(1);
            transform: scale(1);
            span {
              opacity: 1;
            }
            &:before {
              width: 140px;
              height: 140px;
              opacity: 0;
            }
            &:after {
              width: 140px;
              height: 140px;
            }
          }
        }
      }
      .offer_image {
        position: relative;
        width: 100%;
        height: 330px;
        overflow: hidden;
        &:before {
          content: '';
          @include full_size;
          z-index: 2;
          background: rgba($corporate_2,.8);
          opacity: 0;
          @include transition(all,.6s);
        }
        img {
          @include center_image;
          max-height: 330px;
        }
        .center_xy {
          z-index: 3;
          -webkit-transform: scale(0);
          -moz-transform: scale(0);
          -ms-transform: scale(0);
          -o-transform: scale(0);
          transform: scale(0);
          @include transition(all,.6s);
          span {
            @include center_xy;
            text-align: center;
            color: white;
            font-size: 12px;
            line-height: 20px;
            font-weight: 600;
            opacity: 0;
            @include transition(all,1s);
            strong {
              font-size: 36px;
              line-height: 20px;
              font-family: $text_family;
            }
          }
          &:before, &:after {
            content: '';
            @include center_xy;
            width: 0;
            height: 0;
            border-radius: 50%;
            border: 2px solid white;
            @include transition(all,1.6s);
          }
          &:before {
            width: 0;
            height: 0;
            opacity: 1;
            @include transition(all,1s);
          }
        }
      }
      .offer_content {
        background: white;
        .ico {
          text-align: center;
          margin-top: -25px;
          i.fa {
            position: relative;
            z-index: 5;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: $corporate_1;
            font-size: 30px;
            color: white;
            &:before {
              @include center_xy;
            }
          }
        }
        .title {
          font-weight: 600;
          text-align: center;
          padding: 20px;
          font-family: $title_family;
          font-size: 28px;
          color: $corporate_3;
        }
        .desc {
          padding: 0 40px 20px;
          text-align: center;
          color: $corporate_1;
          font-size: 17px;
          line-height: 26px;
          strong {
            font-weight: 600;
          }
        }
        .btn_offer {
          &.btn_half {
            a {
              display: inline-block;
              vertical-align: middle;
              width: 50%;
            }
          }
          a {
            display: block;
            padding: 10px;
            text-align: center;
            text-transform: uppercase;
            color: white;
            letter-spacing: 1px;
            background: $corporate_1;
            font-weight: 600;
            @include transition(all,.6s);
            &:hover {
              background: $corporate_3;
            }
            &:nth-of-type(2) {
              background-color: $corporate_2;
              &:hover {
                background-color: darken($corporate_2,10%);
              }
            }
          }
        }
      }
    }
  }
}
.section_offers_wrapper {
  .banner_offers {
    text-align: left;
    .offer {
      display: inline-block;
      vertical-align: top;
      width: calc((100% - 60px) / 3);
      margin: 0 10px 20px;
    }

  }
}