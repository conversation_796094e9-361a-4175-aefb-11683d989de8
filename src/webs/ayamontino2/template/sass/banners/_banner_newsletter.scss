.newsletter_wrapper {
  position: relative;
  padding: 50px 0;
  margin: 100px 0;
  &:before {
    content: '';
    @include full_size;
    right: 50%;
    background: rgba($corporate_2, .4);
    border-radius: 0 20px 20px 0;
  }
  .newsletter_container {
    .newsletter_title {
      @include title_style;
    }
    .newsletter_title {
      display: inline-block;
      clear: both;
      margin-right: 50%;
      text-align: left;
      padding-left: 0;
      position: relative;
      margin-bottom: 50px;
      @extend .icon-line-comment;
      &:before {
        font-family: "icomoon", sans-serif;
        font-size: 70px;
        @include center_y;
        right: 0;
        z-index: -1;
        opacity: .8;
        color: white;
      }
      small {
        font-style: normal !important;
        text-transform: uppercase;
        letter-spacing: 2px;
        margin-left: 30px;
      }
    }
    .newsletter_description {
      width: calc(50% + 50px);
      position: relative;
      z-index: 2;
      float: right;
      font-size: 18px;
      line-height: 26px;
      color: $corporate_1;
      padding-right: 50px;
      @extend .icon-line-mail;
      &:before {
        font-family: "icomoon", sans-serif;
        display: inline-block;
        float: left;
        color: $corporate_2;
        line-height: 50px;
        font-size: 65px;
        margin-right: 20px;
        opacity: .4;
      }
    }
    .newsletter_form {
      background: white;
      position: relative;
      padding: 110px 50px 50px;
      box-shadow: 0 0 20px rgba(0,0,0,0.15);
      border-radius: 10px;
      width: calc(50% + 100px);
      float: right;
      margin-top: -85px;
      .input_email {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: inline-block;
        vertical-align: middle;
        background: #EFEFEF;
        border-radius: 15px;
        padding: 15px 25px;
        font-size: 14px;
        border-width: 0;
        width: 350px;
        margin-bottom: 20px;
      }
      .button_newsletter {
        position: relative;
        z-index: 5;
        display: inline-block;
        vertical-align: middle;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 15px 105px 15px 25px;
        margin-left: 10px;
        margin-bottom: 20px;
        color: $corporate_3;
        background: transparent;
        border-width: 0;
        letter-spacing: 1px;
        cursor: pointer;
        font-size: 16px;
        @include transition(opacity, .6s);
        @extend .icon-longarrow;
        &:before {
          display: block;
          @include center_y;
          right: 25px;
          font-family: "icomoon", sans-serif;
          color: $corporate_1;
          font-size: 20px;
          text-shadow: 0 0 20px rgba(0,0,0,0.8);
        }
        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          z-index: -1;
          width: 50px;
          height: 50px;
          background-color: rgba($corporate_2, .8);
          border-radius: 50px;
          @include transition(width, .6s);
        }
        &:hover {
          &:after {
            width: 100%;
          }
        }
      }
      .check_newsletter {
        font-size: 12px;
        color: $corporate_1;
        a {
          color: $corporate_1;
          &:hover {
            color: $corporate_2;
          }
        }
      }
    }
    .social_newsletter {
      float: left;
      width: 460px;
      text-align: center;
      padding: 0 50px;
      margin-top: -65px;
      a {
        display: inline-block;
        width: 150px;
        height: 110px;
        position: relative;
        color: white;
        font-size: 100px;
        i.fa {
          @include center_xy;
        }
        &:hover {
          color: $corporate_1;
        }
      }
    }
  }
}
