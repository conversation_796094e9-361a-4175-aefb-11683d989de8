header {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  z-index: 30;
  min-width: 1140px;
  .header_top {
    background: rgba($corporate_1, .95);
    padding: 10px calc((100% - 1050px) / 2);
    color: white;
    .phone {
      display: inline-block;
      vertical-align: middle;
      letter-spacing: 2px;
      font-size: 14px;
      i.fa {
        font-size: 30px;
        vertical-align: middle;
      }
    }
    #social {
      display: inline-block;
      vertical-align: middle;
      padding: 0 20px;
      a {
        color: white;
        display: inline-block;
        padding: 0 5px;
        &:hover {
          color: $corporate_2;
        }
      }
      i.fa {
        font-size: 25px;
      }
    }
    #top-sections {
      display: inline-block;
      float: right;
      padding: 0 20px;
      a {
        color: white;
        display: inline-block;
        padding: 0 5px;
        text-transform: uppercase;
        font-size: 14px;
        letter-spacing: 2px;
        &:hover {
          color: $corporate_2;
        }
        i.fa {
          font-size: 30px;
          vertical-align: middle;
        }
      }
    }
    #lang {
      display: inline-block;
      float: right;
      position: relative;
      &:hover {
        .lang_options_wrapper {
          opacity: 1;
          margin-top: 0;
        }
      }
      .lang_selected {
        color: white;
        display: inline-block;
        padding: 0 5px;
        text-transform: uppercase;
        font-size: 14px;
        letter-spacing: 2px;
        &:hover {
          color: $corporate_2;
        }
        i.fa {
          font-size: 30px;
          vertical-align: middle;
          padding: 0 10px;
        }
      }
      .lang_options_wrapper {
        position: absolute;
        top: calc(100% + 10px );
        right: 0;
        left: 0;
        opacity: 0;
        margin-top: -25px;
        background: $corporate_2;
        filter: drop-shadow(0 0 15px rgba(0,0,0,0.15));
        border-radius: 5px;
        @include transition(all, .6s);
        &:before {
          content: '';
          display: block;
          @include center_x;
          top: -20px;
          border: 10px solid transparent;
          border-color: transparent transparent $corporate_2 transparent;
        }
        a {
          display: block;
          padding: 5px 15px;
          text-align: center;
          color: white;
          &:hover {
            background-color: rgba($corporate_3, .05);
            color: $corporate_1;
          }
          &:first-of-type a {
            border-radius: 5px 5px 0 0;
          }
          &:last-of-type a {
            border-radius: 0 0 5px 5px;
          }
        }
      }
    }
  }
  #main_menu {
    background: rgba(white, .95);
    padding: 10px calc((100% - 1050px) / 2);
    text-align: center;
    #main-sections-inner {
      display: inline-block;
      vertical-align: middle;
      .main-section-div-wrapper {
        display: inline-block;
        vertical-align: middle;
        a {
          display: block;
          color: $corporate_1;
          padding: 5px;
          text-transform: uppercase;
          letter-spacing: 1px;
          font-size: 13px;
          @include transition(color, .6s);
          &:hover {
            color: $corporate_3;
          }
        }
        &:first-of-type a {
          padding-left: 0;
        }
        &:last-of-type a {
          padding-right: 0;
        }
      }
    }
    #logoDiv {
      display: inline-block;
      vertical-align: middle;
      padding: 0 20px;
    }
  }
}

#slider_container {
  position: relative;
  .forcefullwidth_wrapper_tp_banner {
    .tp-bullets {
      opacity: 0 !important;
    }
    .tp-leftarrow, .tp-rightarrow {
      background: transparent;
    }
    .tp-leftarrow {
      &:before {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -o-transform: rotate(180deg);
        transform: rotate(180deg);
      }
    }
    .tparrows {
      @extend .icon-longarrow;
      &:before {
        display: block;
        position: relative;
        font-family: "icomoon", sans-serif;
        color: white;
        font-size: 50px;
        text-shadow: 0 0 20px rgba(0,0,0,0.8);
      }
    }
  }
}