footer {
  position: relative;
  background-color: $corporate_1;
  padding: 50px 0;
  .toTop {
    position: absolute;
    top: -25px;
    right: 70px;
    width: 50px;
    height: 50px;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    padding: 7px 0;
    border-radius: 50%;
    background-color: $corporate_2;
    i {
      font-size: 24px;
      padding-right: 10px;
      @include transform(rotate(-90deg));
    }
    &:hover {
      background-color: darken($corporate_2, 10%);
    }
  }

  .footer_content {
    .footer_columns_wrapper {
      .footer_column {
        width: 45%;
        display: inline-block;
        vertical-align: middle;
        .text {
          padding-top: 20px;
          color: white;
          letter-spacing: 1px;
          line-height: 21px;
          a {
            color: white;
          }
        }
        .menu_footer {
          padding-top: 20px;
          text-align: center;
          -webkit-column-count: 2;
          -moz-column-count: 2;
          column-count: 2;
          a {
            color: white;
            text-transform: uppercase;
            font-size: 22px;
            line-height: 34px;
            display: block;
            @include transition(all, .6s);
            &:hover {
              color: $corporate_2;
            }
          }
        }
      }
    }
    .footer_legal_text_wrapper {
      padding-top: 50px;
      .footer_links_wrapper {
        text-align: center;
        font-size: 18px;
        color: $corporate_2;
        a {
          color: #F7F2F2;
          @include transition(all, .6s);
          &:hover {
            color: white;
          }
        }
      }
      .legal_text {
        text-align: center;
        color: white;
        padding-top: 20px;
      }
    }
  }
}

.awards{
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}