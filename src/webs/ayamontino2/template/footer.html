{% if footer_awards %}
    <div class="awards {% if footer_awards|length > 3 %} owl-carousel{% endif%}">
        {% for award in footer_awards %}
        <div class="award_content">
            {% if award.servingUrl %}<div class="img_wrapper"><img src="{{award.servingUrl|safe}}" alt="" class="award_image"></div>{% endif %}
            {% if award.title %}<span class="award_title">{{award.title|safe}}</span>{% endif %}
        </div>
        {% endfor %}
    </div>

    {% if footer_awards|length > 3 %}
        <script>
            $(window).load(function () {
                var owl_params = {
                    loop: true,
                    nav: true,
                    dots: false,
                    items: 7,
                    margin: 0,
                    navText: ['<i class="fa fa-angle-left" aria-hidden="true"></i>', '<i class="fa fa-angle-right" aria-hidden="true"></i>'],
                    autoplay: true,
                    autoplayHoverPause: true
                };
                $(".awards").owlCarousel(owl_params);
            });
        </script>
    {% endif%}
{% endif %}
<footer>
    <div class="toTop">
        <i class="fa icon-longarrow"></i>
    </div>
    <div class="footer_content container12">

        {% if footer_columns %}
            <div class="footer_columns_wrapper">
                {% for x in footer_columns %}
                    <div class="footer_column">
                        {% if x.servingUrl %}
                            <div class="image">
                                <img src="{{ x.servingUrl|safe }}">
                            </div>
                        {% endif %}
                        {% if x.description %}
                            <div class="text">
                                {{ x.description|safe }}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <div class="footer_legal_text_wrapper">
            <div class="footer_links_wrapper">
                {% for x in policies_section %}
                    <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}"
                       class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                {% endfor %}
                <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
                   title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a>|
                <a target="_blank" href="/rss.xml">RSS</a>
            </div>

            {% if texto_legal %}
                <div class="legal_text">{{ texto_legal|safe }}</div>
            {% endif %}
        </div>

    </div>
</footer>