<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">
<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }} - {{ hotel_name|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}
    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>
    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="format-detection" content="telephone=no">
    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>
    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=1.195"/>
    <!--[if IE 8]><link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" /><![endif]-->
    <!--[if lte IE 7]><script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script><![endif]-->
    <!--[if lte IE 8]><script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->

    {{ jquery|safe }}
    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>
<body {% if not home %}class="inner_section" {% endif %}>
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{{ rich_snippet|safe }}
{% if lang_management %}<input type="hidden" id="lang_management" value="{{ lang_management }}">{% endif %}
{% if lang_default %}<input type="hidden" id="lang_default" value="{{ lang_default }}">{% endif %}
{% block content %}<!--EDIT HERE YOUR PAGE-->{% endblock %}
{% block additional_js %}
    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1.2"></script>
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js"></script>
    <script>$(function () {
        DP_extend_info.config.booking_version = '7';
         DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            var month_short = $.datepicker._defaults['monthNames'][parseInt(dateComponents[1], 10) - 1];
            return "<span class='day'>" + dateComponents[0] + "</span><span class='month'>" + month_short + "</span><span class='year'>" + dateComponents[2] + "</span>";
        };
    });$(window).load(function () {
        DP_extend_info.init();
        $("label.dates_selector_label").html("<span>{{ T_entrada }}</span><span>{{ T_salida }}</span>");
        $(".wrapper_booking_button .submit_button").html("<span>{{ T_reservar }}</span>");
    });</script>
    <script type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_7.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js?v=1.15"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1.12"></script>
    <script async type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js"></script>

    <div style="display: none;">
        <div id="data">
            <div id="wrapper_booking_fancybox">
                <div id="booking_widget_popup" class="booking_widget_fancybox">{{ booking_engine_2|safe }}</div>
            </div>
        </div>
    </div>
{% endblock %}
{{ extra_content_website|safe }}
{{ all_tracking_codes_footer|safe }}
</body>
</html>