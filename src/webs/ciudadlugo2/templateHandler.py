# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.constants.advance_configs_names import PROMOCODE_IN_EMAIL, WHATSAPP_ID, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "ciud2"

class TemplateHandler(BaseTemplateHandler2):

	def get_opinions(self, language):
		opiniones = {}
		opinions_section = get_section_from_section_spanish_name('_opiniones', language)
		opinions = get_pictures_from_section_name('_opiniones', language)
		opinion_link = get_section_from_section_spanish_name('opiniones', language)
		value = 0

		for opinion in opinions:
			if opinion['title']:
				for field in opinion['title'].split("@"):
					if 'name' in field:
						opinion['name'] = field.split("=")[1]
					if 'grade' in field:
						opinion['grade'] = field.split("=")[1]
						value += float(opinion['grade'].replace(",", "."))
					if 'channel' in field:
						opinion['channel'] = field.split("=")[1]

		if opinions_section:
			opiniones['content'] = opinions_section['content']
			opiniones['title'] = opinions_section['title']
			opiniones['subtitle'] = opinions_section['subtitle']

		if len(opinions) > 0:
			opiniones['opiniones'] = opinions
			opiniones['cantidad'] = len(opinions)
			opiniones['media'] = value / len(opinions)
			opiniones['media'] = round(opiniones['media'], 1)

		if opinion_link:
			opiniones['link'] = opinion_link['friendlyUrlInternational']

		return opiniones


	def buildRoomsFromSections(self, language):
		all_rooms =  get_pictures_from_section_name("habitaciones_blocks", language)
		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures']=get_pictures_from_section_name(sect_gallery, language)
			room['name']=room['title']
		return all_rooms

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer_column", language),
							  'popup_slider': get_section_from_section_spanish_name('popup_slider', language),
							  'maps': get_section_from_section_spanish_name('maps', language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'header_number': get_section_from_section_spanish_name("_header_number", language),
							  'header_number_hotel': get_config_property_value(WHATSAPP_ID),
							  'booking_engine_2': self.buildSearchEngine2(language),
							  'recaptcha_publickey': get_config_property_value(PUBLIC_CAPTCHA_KEY)
							  }

		#Content Access
		content_sections = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True
		}

		extra_footer_image = get_pictures_from_section_name("_extra_footer_image", language)
		if extra_footer_image and extra_footer_image[0]:
			result_params_dict['extra_footer_image'] = extra_footer_image[0]

		if advance_properties.get("banners_bottom"):
			result_params_dict['responsive_banners'] = get_pictures_from_section_name('banners_bottom', language)

		result_params_dict["bottom_popup"] = get_section_from_section_spanish_name("popup inicio footer", language)
		bottom_popup_advance = self.getSectionAdvanceProperties(result_params_dict["bottom_popup"], language)
		if bottom_popup_advance.get('promocode'):
			result_params_dict["bottom_popup_promocode"] = bottom_popup_advance.get('promocode')
		result_params_dict["bottom_popup_text"] = get_section_from_section_spanish_name("promocion pop up", language)
		result_params_dict['bottom_popup_background'] = get_pictures_from_section_name("promocion pop up", language)
		result_params_dict['bottom_popup_promocode'] = get_config_property_value(PROMOCODE_IN_EMAIL)

		result_params_dict['opinions'] = self.get_opinions(language)

		if section_name == 'opiniones':
			result_params_dict['opinions'] = None
			result_params_dict['opinions_inner'] = self.get_opinions(language)

		if section_type == 'Inicio':
			result_params_dict['inicio'] = True
			result_params_dict["ticks"] = get_pictures_from_section_name("_tick_home", language)

		if content_sections.get(section_type, False):
			result_params_dict['content_access'] = True

		#Cycle Banners
		if advance_properties.get("banners_cycle"):
			result_params_dict['banners_cycle'] = get_pictures_from_section_name(
				advance_properties.get("banners_cycle"), language)

		if advance_properties.get("minigallery"):
			result_params_dict['minigallery'] = get_pictures_from_section_name(
				advance_properties.get("minigallery"), language)

		actual_section = get_section_from_section_spanish_name(sectionToUse['sectionName'], language) if sectionToUse else {}
		if actual_section.get('subtitle', False):
			result_params_dict['normal_content'] = actual_section

		# Habitaciones
		if section_type == 'Habitaciones':
			all_rooms = self.buildRoomsFromSections(language)
			result_params_dict['rooms'] = all_rooms

		# Ofertas
		if section_type == 'Ofertas':
			result_params_dict["blocks"] = super(TemplateHandler, self).buildPromotionsInfo(language)

		if section_type == u"Atención al cliente":
			result_params_dict['form_contact'] = True

		if section_type == u'Localización':
			result_params_dict['normal_content'] = ''
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			additionalParams4Contact['captcha_box'] = result_params_dict.get('recaptcha_publickey')

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html = get_section_from_section_spanish_name(u"localización", language)
			iframe_google_map = get_section_from_section_spanish_name("Iframe google maps", language)



			result_params_dict['contact_html'] = contact_html
			result_params_dict['maps'] = None
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = location_html.get('subtitle', '')
			result_params_dict['localizacion_access'] = True
			result_params_dict['location_links'] = get_pictures_from_section_name('location links', language)

		if advance_properties.get("services_cycle"):
			result_params_dict['services_cycle'] = get_pictures_from_section_name(advance_properties.get("services_cycle"), language)


		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		return self.buildTemplate('booking/booking_engine_5/_booking_widget.html', params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['horizontal_nolabel'] = False
		options['custom_new_title'] = get_section_from_section_spanish_name('saber mas', language).get('content', '')
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:
				if not filters.get(x.get('title', ''), False):
					filters[x.get('title', '')] = [x['servingUrl']]
				else:
					filters[x.get('title', '')].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''

			if user_agent_is_mobile():
				if advance_properties.get("banners_cycle"):
					cycle_dict = dict(get_web_dictionary(language))
					cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("banners_cycle"), language)
					cycle_html = self.buildTemplate_2("mobile_templates/1/_cycle_banners_v1.html", cycle_dict, False)
					additionalParams['custom_elements'] += cycle_html

				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("services_cycle"):
					mini_dict = {'services_cycle_mobile' : get_pictures_from_section_name(advance_properties.get("services_cycle"), language)}
					services_banners = self.buildTemplate_2("mobile_templates/1/_services_cycle.html", mini_dict, False)
					additionalParams['custom_elements'] += services_banners

			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)