/*==== General ===*/
body {
  font-family: 'Merriweather', serif;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

.wrapper-new-web-support.booking_form_title {
  display: block;
  background: none;
  opacity: 1;
  font-size: 12px;
  font-weight: lighter;
  color: white;
  font-family: 'Roboto';
  padding-bottom: 0;

  .web_support_label_1_special, .web_support_label_2 {
    display: inline-block;
  }
}

.wrapper-new-web-support .web_support_number {
  font-size: 16px !important;
}

.wrapper-old-web-support .web_support_label_2:before, .wrapper-new-web-support .web_support_label_2:before {
  margin: 0 10px;
}

/*============ Header ============*/

header {
  width: 100%;
  height: auto;
  color: $corporate_1;
  overflow: visible;
  background: white;

  #wrapper-header {
    position: relative;
    z-index: 22;
    height: auto;
  }

  a {
    color: $corporate_1;
  }

}

#logoDiv {
  margin-top: 0px;
  width: auto;
  float: left;
  width: 250px;
  height: auto;
  overflow: hidden;
  background: white;

  img {
    background: white;
    width: auto;
    padding-top: 6px;
  }
}

.middle-header {
  margin-right: 0px !important;
  color: rgb(98, 98, 98);
  float: right;
  width: 850px;
}

.top-row, .bottom-row {
  overflow: auto;
}

.bottom-row {
  display: contents;
  margin-top: 7px;
  text-align: right;
  width: 660px;
  margin-right: 0;
  margin-left: auto;

  .header_number {
    display: inline-block;
    vertical-align: middle;

    &.phone_hotel {
      float: left;

      .fa {
        color: #3C332C;
      }
    }

    .fa {
      color: #25D366;
      vertical-align: middle;
      font-size: 26px;
    }

    span {
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.text_official {
  .official {
    font-weight: 500;
    display: block;
    float: right;
    padding-top: 2px;
    font-size: 16px;
  }
}

span.official {
  color: $corporate_1;
  text-transform: uppercase;
  font-weight: 300;
}

.header_button {
  position: relative;
  float: right;
  margin-top: 5px;
  font-family: 'open sans', sans-serif;
  margin-left: 15px;
  background-color: $corporate_1;
  padding: 5px 10px 5px 30px;
  color: white;
  .fa {
    @include center_y;
    left: 5px;
  }
}

#lang {
  float: right;
  text-transform: capitalize;
  margin-right: 0;
  margin-top: 5px;
  font-size: 14px;
  font-family: 'open sans', sans-serif;
  font-weight: 400;
  background: #efefef;
  color: #4b4b4b;

  &:before {
    content: '\f0d7';
    font-family: 'fontawesome', sans-serif;
    display: inline-block;
    width: 30px;
    height: 30px;
    background-color: $corporate_1;
    vertical-align: middle;
    color: white;
    padding: 8px 10px;
    box-sizing: border-box;
  }
  .current_language {
    padding: 5px 15px;
    display: inline-block;
    vertical-align: middle;
  }
  .option {
    opacity: 0.8;
    cursor: pointer;
  }

  .selected .option {
    opacity: 1;
  }

  a {
    color: #4b4b4b;
    font-weight: 100;
    text-decoration: none;
  }
  :not(.selected) {
    opacity: 0.8;
  }

  a:hover {
    opacity: 1;
  }

  span:first-child {
    padding-right: 5px;
    font-weight: 300;
    opacity: 1;
  }

  a span {
    padding-right: 0px;
  }

  .lang_selector {
    background-color: $corporate_1;
    height: 0;
    overflow: hidden;
    position: absolute;
    top: 35px;
    right: 138px;
    a {
      color: white;
      display: block;
    }
  }
  &:hover {
    .lang_selector {
      padding: 15px;
      height: auto;
      overflow: inherit;
    }
  }
}

.web-oficial.interior {
  width: 1140px;
  margin: 0 auto;
  padding: 0px;
  font-size: 13px;
  background: $corporate_1;

  img {
    width: 20px;
    height: 20px;
    padding-top: 3px;
  }

  .tick_wrapper {
    padding-top: 4px;
    span {
      color: white !important;
    }
  }

}

.tick_wrapper {
  float: left;
  font-family: 'Source Sans Pro', sans-serif;
}

.tick_wrapper img {
  width: auto;
  height: 23px;
  padding-top: 4px;
}

.tick_center {
  display: table-caption;
  margin: 0 auto;
}

.en .web-oficial {
  width: 245px;
}

#social {
  width: auto;
  margin-top: 9px;

  img {
    width: 32px;
    height: 32px;
  }

  a {
    color: #804b4b;
    text-decoration: none;
    i.fa {
      display: inline-block;
      position: relative;
      background-color: $corporate_2;
      border-radius: 50%;
      color: #4b4b4b;
      width: 30px;
      height: 30px;
      overflow: hidden;
      &:before {
        @include center_xy;
      }
    }
    &:hover {
      i.fa {
        background-color: $corporate_1;
      }
    }
  }

}

#top-sections {
  margin-top: 10px;
  text-align: right;
  margin-right: 30px;
  font-family: 'open sans', sans-serif;
  float: right;
  width: auto;
  padding-left: 0;
  font-size: 14px;

  a span {
    margin-left: 20px;
  }

  a:hover {
    opacity: 0.8;
  }

  a {
    color: rgb(98, 98, 98);
    text-decoration: none;
  }
}

#main_menu {
  height: 40px;
}

#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  height: 40px;
  box-sizing: border-box;
}

#main-sections ul {
  display: table;
  width: 865px;
  .main-section-div-wrapper {
    display: table-cell;

    &#section-active {
      a {
        color: $corporate_1;
      }
    }
  }
}

#mainMenuDiv a {
  padding: 9px 0px 0px;
  text-decoration: none;
  color: #4b4b4b;
  display: inline-block;
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 400;

  &.button_promotion span {
    border-bottom: 2px solid white;
  }
  &:hover:not(.button_promotion) {
    color: $corporate_1;
  }
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  height: 40px;
  text-align: justify;
  -ms-text-justify: distribute-all-lines;
  text-justify: distribute-all-lines;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper {
  text-align: center;

  a {
    line-height: 23px;
    font-size: 15px;

    &:hover {
      color: $corporate-2;
    }
  }
}

//Top_menu//
span.separator {
  margin-left: 6px;
  margin-right: 2px;
}

a.separator {
  margin-left: 3px;
}

.book_menu {
  padding: 10px 22px;
  color: white;
  font-weight: bolder;
  font-size: 17px;
}

/*=============== Revolution Slider ============*/
.tp-bullets .bullet {
  background: white !important;
  border-radius: 19px;
  width: 15px !important;
  height: 15px !important;
  margin: 4px !important;
  &.selected {
    background: rgba(255, 255, 255, 0.6) !important;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.6) !important;
  }
}

.ticks {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 30;
  background-color: rgba(black, 0.6);
  padding: 20px;
  text-align: right;
  color: white;
  .tick {
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    font-weight: 100;
    .fa, span {
      display: inline-block;
      vertical-align: middle;
      margin: 0 20px 0 0;
    }
    .fa {
      margin: 0 5px;
      font-size: 200%;

      &.selected {
        color: $corporate_2;
      }
    }
    &:first-of-type {
      float: left;
    }
  }
}

#slider_container {
  position: relative;

  .tp-bullets {
    bottom: 90px!important;
  }

  .inner_slider {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      @include center_image;
    }
  }
}

.see_more_slider {
  position: absolute;
  top: 50px;
  right: 20%;
  z-index: 23;
  width: 475px;
  cursor: pointer;
  width: 495px;

  .plus_slider {
    float: left;
  }

  p.default_title {
    background: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 14px 11px 8px 70px;
    height: 22px;
    font-weight: lighter;
  }

  p.hidden_text {
    background: black;
    color: white;
    padding: 25px;
    width: 475px;
    font-weight: lighter;
    display: none;
    box-sizing: border-box;
  }
}

/*======== Normal Content =======*/
.normal_content_wrapper {
  margin-top: 73px;

  .normal_title {
    color: $corporate_1;
    text-align: center;
    margin-bottom: 60px;
    font-weight: 100;
    font-size: 28px;

    strong {
      font-weight: bolder;
    }
  }

  .separator {
    display: table;
    margin: 16px auto 11px;
  }

  .normal_description {
    font-family: 'open sans';
    font-size: 13px;
    display: table;
    text-align: center;
    color: #757575;
    padding: 0 200px;
    margin: auto auto 75px;

    strong {
      font-weight: bolder;
    }
  }

  .left_block {
    width: 47%;
    float: left;
  }

  .right_block {
    width: 47%;
    float: right;
  }
}

/*====== Banners Bottom =====*/
section.footer_banners {
  display: table;
  width: 100%;
}

.banner_element {
  width: 50%;
  float: left;
  position: relative;
  background: #F9F8F6;

  .image_banner {
    width: 50%;
    float: left;
    position: relative;
    overflow: hidden;

    &:before {
      content: "";
      display: block;
      padding-top: 86%; /* initial ratio of 1:1*/
    }

    img {
      width: auto;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      min-width: 100%;
      bottom: 0;
      min-height: 100%;
      max-width: none;
    }
  }

  .description_banner {
    width: 50%;
    float: right;
    text-align: center;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    font-family: 'open sans';

    h3.title {
      font-size: 14px;
      font-weight: bolder;
      color: rgb(0, 0, 0);

      .gold {
        font-family: 'Merriweather', serif;
        color: $corporate_1;
        display: block;
        font-size: 17px;
        line-height: 27px;
        font-weight: lighter;
        margin-top: 6px;
      }
    }

    p.description {
      padding: 0 14%;
      font-size: 14px;
      font-family: 'Merriweather', serif;
      color: #949494;
      font-weight: 300;
      line-height: 19px;
    }
  }

  .separator {
    width: 50px;
    height: 3px;
    background: $corporate_1;
    margin: 15px auto;
  }

  &.bottom {
    .description_banner {
      right: auto;
      left: 0;
    }

    .image_banner {
      float: right;
    }
  }

  &.complete_image {

    .image_banner {
      width: 100%;

      &:before {
        content: "";
        display: block;
        padding-top: 43%; /* initial ratio of 1:1*/
      }
    }

    .description_banner {
      left: 0;
    }

    .plus_separator {
      display: none;
    }

    .description_banner {
      h3.title {
        color: white;
        font-family: 'Roboto Slab';
        font-size: 19px;
        font-weight: 500;
      }
      p.description {
        color: white;
        padding: 0;
        font-weight: lighter;
      }
    }

    .separator {
      background: white;
    }
  }

  img.plus_separator {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}

.maps_footer {
  display: table;
  width: 100%;
  padding-top: 38px;

  h3.map_title {
    padding-bottom: 33px;
    text-align: center;
    font-size: 30px;
    color: $corporate_1;
    font-weight: lighter;
    box-shadow: 0 4px 5px #A2A2A2;
    z-index: 2;
    position: relative;

    strong {
      font-weight: 700;
    }
  }
}

.map_content {
  height: 380px;
  iframe {
    width: 100%;
    height: 380px;
  }
}

/*===== Footer ====*/
footer {
  background: #404040;
  color: white;
  padding-top: 50px;

  .footer_column {
    color: white;
    font-size: 12px;

    .footer_column_title {
      font-size: 18px;
      font-family: 'Roboto Slab';
      margin-bottom: 5px;
    }

    .image_logo {
      float: left;
    }

    .footer_column_description {
      line-height: 23px;
      font-family: 'open sans';
      font-size: 13px;
    }
  }
  a {
    color: white;
    &:hover {
      color: $corporate_1;
    }
  }
  .footer_column.last {
    width: 170px !important;
  }

  .newsletter_wrapper {
    margin-top: 20px;

    .newsletter_container {
        width: auto;
    }

    #title_newsletter, #form-newsletter #suscEmailLabel {
      display: none !important;
    }

    input#suscEmail {
      background: #a2a2a2;
      border: 0;
      border-radius: 2px;
      width: 180px;
      height: 22px;
      float: left;
      padding-left: 15px;
      box-sizing: border-box;
    }

    .button_newsletter {
      background: $corporate_1;
      color: white;
      border: 0;
      display: inline;
      margin-left: 9px;
      font-size: 12px;
      text-transform: uppercase;
      border-radius: 2px;
      padding: 4px 20px 3px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .extra_footer_image {
    display: block;
    background: white;
    text-align: center;
    padding: 20px;
    margin-bottom: 20px;

    a {
      display: block;
      width: 650px;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.wrapper_footer_columns {
  margin-bottom: 55px;
}

.social_likes {
  text-align: center;
  font-size: 11px;

  div#facebook_like {
    width: 49.5%;
    float: left;
    text-align: right;
    margin-top: 2px;
  }

  div#google_plus_one {
    width: 49%;
    float: right;
    text-align: left;
  }

  span.copyright_text {
    display: block;
    margin-bottom: 6px;
    font-family: 'open sans';
    color: #6D6D6D;
  }
}

.footer_links_business {
  color: #6D6D6D;
  text-align: center;
  font-size: 12px;
  margin-top: 4px;

  a {
    color: #6D6D6D;
    text-decoration: none;
    font-family: 'open sans';
  }
}

div#div-txt-copyright {
  text-align: center;
  font-size: 12px;
  color: #6D6D6D;
  font-family: 'open sans';
}

.full-copyright {
  padding-bottom: 15px;
}

/*============ Habitaciones =============*/

.room_wrapper {
  margin-top: 75px;
  margin-bottom: 75px;
  display: table;
}

div#description-main-section.content_rooms {
  box-sizing: border-box;
  margin-top: 0px;
}

section#top_content {
  padding-top: 200px;
}

.rooms-description {
  background: rgba(245, 245, 245, 0.8);
  padding: 20px 20px 30px;
  color: grey;
  position: relative;

  .destino {
    font-weight: 700;
    font-family: yanone, sans-serif;
  }

  .title-module {
    font-size: 23px;
    color: $corporate_1;
    margin-bottom: 20px;

  }
}

.description-rooms {
  font-weight: lighter;
  font-size: 14px;
  font-family: 'open sans';
}

h4.title-module {
  font-size: 23px;
  color: #C5AD81;
  margin-top: 10px;
  margin-bottom: 5px;
}

.rooms {
  margin-bottom: 25px;
  position: relative;
}

.blockleft {
  margin-left: 0px;
}

.blockright {
  margin-right: 0px;
  margin-left: 30px;
}

.sub-description-rooms {
  margin: 10px 0 20px;
  font-weight: bold;
  font-size: 14px;
}

span.btn-corporate {
  position: absolute;
  top: 0;
  right: 0;
}

span.btn-corporate {
  position: absolute;
  top: 16px;
  right: 50px;
  text-transform: uppercase;
  color: white !important;
  background-color: $corporate_2;
}

span.btn-corporate {
  padding: 5px 12px;
  font-family: 'open sans';
}

.btn-flecha {
  background-color: $corporate_1;
  height: 22px;
  padding: 5px 10px;
  position: absolute;
  width: 10px;
  top: 16px;
  right: 15px;
  text-align: center;
}

.rooms {
  .room_img {
    width: 100%;

  }
  .ico_cam_room {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 20;
  }
  span.btn-corporate {
    right: 20px;
  }
  .btn-flecha {
    width: 70px;
    cursor: pointer;
    font-family: 'open sans';

    &:hover {
      opacity: 0.8;
    }
  }
}

a.rooms-img {
  height: 220px;
  display: block;
  overflow: hidden;
  position: relative;
}

img.room_img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

a.btn_vermas_room {
  color: white !important;
  font-weight: 700;
  margin-left: 1px;
  text-align: center;
  text-decoration: none;
}

.room-links {
  a.button-promotion {
    color: white !important;
    text-decoration: none;
  }

  .btn-corporate:hover {
    opacity: 0.8;
  }
}

.blockleft-room {
  margin-left: 0px;
  margin-right: 30px;
}

.blockright-room {
  margin-right: 0px;
}

.myFancyPopupRooms {
  margin-left: 20px;
  margin-right: 20px;
  width: 550px;

  .sub-description-rooms {
    font-weight: 700;
    font-family: yanone, sans-serif;
    color: #C5AD81;
    font-size: 18px;
    margin-top: 0;
  }
}

/*======= Cycle Banners ======*/
.banners_cycle_wrapper {
  margin-bottom: 70px;

  .cycle_element {
    height: 270px;
    overflow: hidden;
    margin-bottom: 5px;

    .cycle_image_element {
      width: 35%;
      float: left;
      height: 270px;

      img {
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }
    .cycle_text_wrapper {
      width: 65%;
      float: right;
      background: #F8F7F5;
      padding: 30px;
      text-align: left;
      font-weight: 100;
      box-sizing: border-box;
      color: black;
      height: 270px;
      line-height: 19px;

      font-family: 'open sans';
      font-size: 13px;
      color: #757575;

      h3.section_title {
        color: #C5AD81;
        font-size: 21px;
        padding-bottom: 14px;
        font-weight: 300;
        font-family: 'Roboto Slab', serif;

      }

      .exceded {
        height: 170px;
        overflow: hidden;
      }
    }

    &.right {
      .cycle_image_element {
        float: right;
      }
    }
  }

  a.see_more_section, a.see_more_video {
    background: #72162F;
    padding: 8px 15px;
    margin-top: 9px;
    display: inline-block;
    text-decoration: none;
    color: white;
    font-weight: 500;
  }
}

.hidden_cycle {
  display: none;
  float: right;
  background: #F8F7F5;
  padding: 8px 30px;
  text-align: left;
  font-weight: 100;
  box-sizing: border-box;
  color: black;
  font-size: 14px;
  line-height: 19px;

  h3.section_title {
    color: #C5AD81;
    font-size: 21px;
    padding-bottom: 14px;
    font-weight: 300;
  }
}

/*===== Content Access ====*/
.content_access {
  margin-top: 73px;
  margin-bottom: 75px;
  font-family: 'open sans';
  font-size: 13px;
  text-align: center;
  display: table;
  color: #757575;

  .section-title {
    color: $corporate_1;
    text-align: center;
    margin-bottom: 60px;
    font-weight: 100;
    font-size: 28px;

    strong {
      font-weight: bolder;
    }
  }

  #my-bookings-form-fields {
    width: 211px;
    margin: 20px auto;

    input#emailInput {
      margin-bottom: 10px;
    }

    label#my-bookings-localizador-label {
      margin-right: 7px;
    }

    label#my-bookings-email-label {
      margin-right: 36px;
    }

    button#my-bookings-form-search-button, button#cancelButton {
      margin-top: 30px;
      background: $corporate_1;
      color: white;
      border: 0;
      margin-left: 9px;
      font-size: 16px;
      text-transform: uppercase;
      border-radius: 2px;
      padding: 10px 25px;
      cursor: pointer;
      margin-top: 13px;
      margin-left: auto;
      margin-right: auto;
    }

    button#my-bookings-form-search-button {
      display: block;
    }
  }

  .my-bookings-booking-info {
    margin: 0 auto;
  }

  .bordeInput {
    width: 117px;
    height: 13px;
  }

  button#cancelButton {
    margin-top: 30px;
    background: $corporate_1;
    color: white;
    border: 0;
    margin-left: 9px;
    font-size: 12px;
    text-transform: uppercase;
    border-radius: 2px;
    padding: 4px 19px 5px;
    cursor: pointer;
    display: none;
    margin-left: auto;
    margin-right: auto;
  }
}

.content_acceded {
  .normal_description {
    display: none;
  }

  & + .content_access {
    margin-top: 0;
    text-align: center;
    margin: auto;
  }
}

/************************* SCAPES/OFERTAS ************************/
a.plus {
  padding: 7px 8px 9px !important;
}

a.play {
  padding: 10px 9px 5px !important;
}

.scapes-blocks {
  overflow: hidden;
  margin-top: 75px;
  margin-bottom: 75px;
}

.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  height: 492px;
  overflow: hidden;
  position: relative;

  a img {
    margin-bottom: -5px;
    width: 100%;
  }

  .description {
    padding: 20px;
    position: relative;
    background-color: rgba(245, 245, 245, 0.8);
    padding-right: 160px;

    .title-module {
      font-size: 23px;
      color: $corporate_1;
      font-weight: 700;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 200px;
      right: 0;
      top: 34px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline-block;
        a {
          display: block;
          background-color: $corporate_2;
          top: -1px;
          color: white;
          padding: 7px 7px 8px;
          text-align: center;
          height: 21px;
          text-decoration: none;
          &:hover {
            opacity: 0.8;
          }
        }
        a.plus {
          padding: 10px 7px 5px;
          height: 20px;
          background: $corporate_1;
          text-decoration: none;
          min-width: 74px;
        }

        a.play {
          padding: 10px 9px 5px;
          text-decoration: none;

          img {
            margin-top: 2px;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_1;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {
  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }

  h5 {
    color: $corporate_1;
  }
}

.oferta-reserva {
  margin-right: 10px;
}

/*======= Localizacion y Contacto ======*/

.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }

}

.location-info-and-form-wrapper {
  margin-top: 75px;
  margin-bottom: 75px;
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 20px;
  padding-bottom: 20px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #C5AD81;
  font-family: 'open sans';
  width: 95%;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;

}

li.how-to-go {
  cursor: pointer;
  color: $corporate_1;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
  .car {
    background: url("/img/amera/icons_maps/car.png") left center no-repeat;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: #757575;
  font-weight: 300;
  font-family: 'open sans';

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #C5AD81 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 28px;
  font-family: 'open sans';
}

.form-contact #contact-button:hover {
  background-color: $corporate_1 !important;
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  color: #757575;
  font-family: 'open sans';
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

/*======= Different languages =======*/
body.olanguage {
  .oferta-reserva {
    width: 140px;
    right: 105px !important;
  }

  #top-sections {
    width: 516px;
    padding-left: 0;
    padding-right: 0px;
    box-sizing: border-box;
  }

  .rooms {
    .btn-flecha {
      width: auto;
    }

    span.btn-corporate {
      right: 96px;
    }
  }

  .content_access #my-bookings-form-fields {
    width: 320px;
  }

  .content_access #my-bookings-form-fields label#my-bookings-email-label {
    margin-right: 110px;
  }

  #my-bookings-localizador-label {
    min-width: 142px;
    display: inline-block;
    text-align: justify;
  }
}

/* ===== ===== === == OPINIONS == === ===== ===== */
.opinions .header, .opinions-total .header {
  background-color: darken($corporate_1, 10%);
  position: relative;
  display: table;
  width: 100%;

  img {
    position: relative;
    display: inline-block;
    float: left;
    border-right: 2px solid white;
    margin-right: 15px;
    padding-right: 21px;
  }

  p {
    color: rgb(90, 90, 90);
    font-size: 16px;
    line-height: 14px;
    margin-top: 2px;
  }
}

.opinions-total {
  margin: -240px 0 50px 0;

  h3.title-module {
    display: block;
    color: white;
    margin-left: 400px;
    text-align: left;
    font-family: mnstrial;
    font-size: 42px;
    margin-top: 36px;
  }
}

.opinions {
  width: 379px !important;
  margin-left: 0px;
  margin-right: 0px;
}

.opinions .value, .opinions-total .value {
  background: #f6f7f8;
  text-align: center;
  padding: 14px 0;
  border-top: 2px solid white;
  font-size: 16px;
  text-transform: uppercase;
  color: $corporate-1;

  .media {
    font-size: 30px;
    font-family: nexabold;
    color: $corporate-1;
    margin-right: 10px;
  }
}

.opinions .coment, .opinions-total .coment {
  background: #f6f7f8;

  padding: 15px 0;
  border-top: 1px solid white;
  position: relative;

  .plus-link {
    position: absolute;
    right: 15px;
    top: 12px;
    background-color: $corporate-1;
    padding: 8px 8px 0 !important;

    &:hover {
      background-color: pink;
    }
  }
  span {
    font-size: 12px;

    p {
      display: inline;
    }
  }
  .calification {
    color: white;
    background-color: $gray-2;
    padding: 10px 10px 9px;
    margin-right: 20px;
    margin-left: 15px;
    font-size: 14px;
  }
}

.opinions-total table {
  width: 100%;

  tr {
    background-color: #f6f7f8;
    border-top: 2px solid white;

    .name {
      text-transform: uppercase;
      width: 95px;
      border-right: 2px solid white;
      color: white;
      background: $corporate_1;
      font-weight: bolder;
      box-sizing: border-box;
      text-align: center;
      font-size: 30px;
      margin: auto;
      height: 20px;
      padding: 0 !important;
      vertical-align: middle;
    }
    .opinion-description {
      width: 800px;
      padding: 20px;
      color: #86858a;

      strong {
        font-weight: bold;
        font-size: 16px;
      }
    }
    .calification {
      border-left: 2px solid white;
      vertical-align: middle;
      padding: 20px;
      text-align: center;

      span {
        color: white;
        background-color: #787878;
        padding: 10px 10px 9px;
        font-size: 16px;
      }
    }
    p {
      margin-bottom: 0;
    }
  }
}

.opinions-button {
  position: absolute;
  right: 46px;
  top: 22px;
  display: block;
  color: white;
  text-transform: uppercase;
  padding: 2px;
  border-bottom: 1px solid white;
  margin-top: 8px;
}

.yellow_left {
  display: table;
  float: left;
  background: $corporate_1;
  padding: 25px 25px 23px;

  &:after {
    content: '';
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-left: 15px solid $corporate_1;
    border-bottom: 15px solid transparent;
    position: absolute;
    top: 34px;
    bottom: 0px;
    margin-left: 25px;
    z-index: 4;
  }
}

span.media_opinion {
  color: white;
  border-right: 2px solid white;
  padding-right: 15px;
  font-size: 45px;
  padding-top: 5px;
  line-height: 55px;
  margin-top: 20px;
}

span.number_opinions {
  margin-left: 22px;
  color: white;
  display: inline-block;
  position: relative;
  vertical-align: top;
  text-transform: uppercase;
  margin-top: 18px;
}

/*== Opinions home ===*/
.opinions_title {
  text-align: center;
  display: block;
  color: $corporate_1;
  line-height: 37px;
  font-size: 39px;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.opinions_wrapper {
  display: table;
  padding-top: 50px;
  margin: 0 auto 50px;

  .left_block {
    width: 188px;
    float: left;
    height: 186px;
    background: #FFB548;

    img {
      width: 147px;
      display: block;
      margin: auto;
      margin-top: 13px;
    }

    span.title_opinion {
      text-align: center;
      display: block;
      color: white;
      line-height: 37px;
      font-size: 39px;
      text-transform: capitalize;
      font-family: mnstrial;
    }
  }

  .middle_block {
    width: 1140px;
    float: left;

    .desc_title_opin {
      display: none;
    }
  }

  table {
    margin: auto;

    td.name {
      padding: 10px 18px;
      font-size: 16px;

      p {
        float: left;
        font-size: 14px;
        color: #B1B1B1;
        text-transform: uppercase;
        margin-top: 16px;
      }
    }
    td.logo {
      vertical-align: middle;
      img {
        display: block;
        margin: auto;
        vertical-align: middle;
        width: 70px;
      }
    }

    span.rate_gived {
      float: right;
      text-align: center;
      width: 70px;
      box-sizing: border-box;
      background: #78787A;
      color: white;
      padding: 8px 0 8px;
      font-size: 23px;
    }
  }

  .right_block {
    float: right;
    width: 187px;
    background: #FFB548;
    height: 186px;
    position: relative;
  }

  .opinions_media {
    text-align: center;
    margin: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 30px;
    font-size: 42px;
    color: white;
    height: 65px;

    span {
      font-size: 20px;
      display: block;
      text-transform: uppercase;
      font-weight: bold;
      margin-top: 4px;
    }
  }

}

.form-general {
  margin-top: 0px;
  overflow: hidden;
  width: 350px;
  height: auto;

  h3 {
    font-size: 26px;
    font-family: mnstrial;
    color: $corporate_1;
    margin-bottom: 20px;
    margin-top: 10px;
    line-height: 20px;
  }

  label {
    margin-bottom: 5px;
    display: block;
    color: rgb(90, 90, 90);
    font-family: nexaregular, sans-serif;
    text-transform: capitalize !important;

    &.error {
      color: darkred;
      margin-top: -10px;
      margin-bottom: 13px;

      &[for='opinion'] {
        margin-top: 5px;
      }
    }
  }

  input {
    margin-bottom: 15px;
    width: 300px;

    &.input-error {
      border: 1px solid darkred;
    }
  }

  a#opinions-button {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    color: white;
    border: 0;
    background: #950c13;
    font-size: 18px;
    text-transform: uppercase;
    height: auto;
    width: 110px;
    margin-top: 4px;
    padding: 8px;
    display: block;
    text-align: center;
  }
  textarea {
    width: 300px;
  }
}

#opinions-form {
  background: $gray-4;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  margin-bottom: 40px;

  h3 {
    color: $corporate_1;
    text-align: center;
  }

  li {
    width: 50%;
    display: inline-block;
    float: left;

    label {
      margin-bottom: 10px;

      &.error {
        color: red;
      }
    }

    input {
      width: 98%;
      box-sizing: border-box;
      border: 0;
      height: 30px;
    }

    &.comments {
      width: 100%;

      textarea {
        width: 99%;
        box-sizing: border-box;
        border: 0;
      }
    }
  }
}

/*===== Opinions form =====*/
#opinions-form {
  h3 {
    font-size: 30px;
  }

  #opinions-button {
    height: 30px;
    text-align: center;
    box-sizing: border-box;
    background: $corporate_2;
    padding: 5px 20px;
    clear: both;
    color: white;
    text-transform: uppercase;
    cursor: pointer;
  }

  label {
    display: block;
    text-transform: capitalize;
    margin-top: 11px;
    margin-bottom: 3px;
  }

  input, textarea {
    padding: 5px 15px;
    width: 280px;
  }

  textarea {
    margin-bottom: 20px;
    border: 1px solid lightgray;
  }
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate_2;
  left: 0;
  bottom: 0;
  padding: 20px 0;
  z-index: 1000;

}

.bottom_popup #wrapper2 .icon {
  position: relative;
  float: left;
  font-size: 50px;
  color: white;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  display: inline-block;
  vertical-align: middle;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.bottom_popup .close_button {
  position: absolute;
  top: 5px;
  right: 5px;
  .fa {
    background-color: rgba(white, .8);
    padding: 3px 6px 5px 5px;
    color: $corporate_2;
    border-radius: 50%;
    &:hover {
      background-color: white;
    }
  }
}

button.bottom_popup_button {
  width: 120px;
  background: $corporate_1;
  border: 0;
  height: 36px;
  color: white;
  display: inline-block;
  vertical-align: middle;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

#popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }
    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}

.demo7, .ciudadlugo {
  .room_wrapper {
    .rooms {
      &:nth-child(3), &:nth-child(4), &:nth-child(5) {
        width: 360px;
      }

      &:nth-child(4) {
        margin: 0;
      }

      &:nth-child(5) {
        margin-right: 0;
        margin-left: 30px;
      }

      &:nth-child(6) {
        margin-right: 30px;
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
        margin-left: 10px;
      }
    }
  }
}

.form_contact_wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;

  .contInput {
    margin-bottom: 5px;

    &.policy-terms {
      a {
        text-decoration: none;
        color: $corporate_2;
      }
    }

    label {
      display: block;
      color: $corporate_1;
      margin-bottom: 5px;
    }

    input, textarea {
      width: 300px;
      box-sizing: border-box;
      padding: 7px;

      &#accept-term {
        width: auto;
      }
    }
  }

  #contact-button {
    background: $corporate_1;
    color: white;
    border-radius: 0;
    padding: 10px 25px;
    border: 0;
    font-size: 16px;
    display: inline-block;
    margin-top: 10px;
  }
}

@import "cycle_services";

html[lang=fr] {
  .ticks .tick {
    font-size: 13px;
  }

  .scapes-blocks .block .description ul {
    width: 240px;
  }

  .scapes-blocks .block .description {
    padding-right: 255px;
  }
}

#indications {
    div {
        margin-left:36pt;
    }
}
input#privacy {
    display: inline-block;
    width: auto !important;
  }

//*==== Galleries =====*//
.gallery_1 {
  .crop {
    position: relative;
    height: 212px;

    a {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }

    img {
      position: absolute;
      top: -50%;
      bottom: -50%;
      left: -50%;
      right: -50%;
      margin: auto;
      height: auto !important;
      min-height: 100%;
      max-height: 140%;
      min-width: 100%;
      max-width: 140%;
    }
  }
}

.minigallery_wrapper {
  width: 100vw;
  margin: 5px 0;
  
  .img_wrapper {
    height: 250px;
    
    img {
      @include cover_image;
    }
  }
}

body.hostel-cross{
  .form-contact #contact-button-wrapper{
    margin-top: 15px;
  }
  .form_contact_wrapper .contInput.captcha{
    display: flex;
    justify-content: center;
  }
}