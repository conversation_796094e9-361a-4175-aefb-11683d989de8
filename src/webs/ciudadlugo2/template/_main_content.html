{# Normal Content #}
{% if normal_content %}
    <div class="normal_content_wrapper {% if content_access %}content_acceded{% endif %}">
        <h3 class="normal_title">{{ normal_content.subtitle|safe }}</h3>

        <div class="normal_description">{{ normal_content.content|safe }}</div>
    </div>
{% endif %}

{% if opinions_inner %}
    {% include "opinions_section.html" %}
{% endif %}

{# Banners Cycle #}
{% if banners_cycle %}
    <div class="banners_cycle_wrapper">
        {% for x in banners_cycle %}
            <div class="cycle_element {% cycle 'left' 'right' %}">
                <div class="cycle_image_element"><img data-src="{{ x.servingUrl|safe }}" lazy="true"></div>
                <div class="cycle_text_wrapper">
                    <div class="exceded">
                        <h3 class="section_title">{{ x.title|safe }}</h3>
                        {{ x.description|safe }}
                    </div>
                    <div class="buttons_wrapper">
                        {% if x.video %}
                            <a href=".hidden_cycle_{{ forloop.counter }}_video"
                               class="see_more_video">{{ T_ver_video }}</a>
                            <div class="hidden_cycle_{{ forloop.counter }}_video"
                                 style="display: none;height: 315px;">{{ x.video|safe }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="hidden_cycle_{{ forloop.counter }} hidden_cycle">
                <h3 class="section_title">{{ x.title|safe }}</h3>
                {{ x.description|safe }}
            </div>
        {% endfor %}
    </div>
{% endif %}


{# Content Access #}
{% if content_access %}
    <div class="content_access">
        {{ content }}
    </div>
{% endif %}


{#Ofertas#}
{% if blocks %}
    <div class="scapes-blocks">
        {% for block in blocks %}
            <div class="block {% cycle 'row1' 'row2' %}">
                <div class="description">
                    <h3 class="title-module">{{ block.name|safe }}</h3>
                    <ul>
                        <li><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                               class="button-promotion oferta-reserva">{{ T_reservar }}</a></li>
                        <li><a href="#event-modal-{{ forloop.counter }}"
                               class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}"
                   class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{% endif %} enlace_offer">
                    <img data-src="{{ block.picture }}=s1000" lazy="true">
                </a>

                <div id="event-modal-{{ forloop.counter }}"
                     class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                    {{ block.description|safe }}
                </div>
            </div>

        {% endfor %}
    </div>

    <script>
        if (document.documentElement.lang == 'en') {
            $(".oferta-reserva").html("Book")
        }
    </script>
{% endif %}

{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}
            {{ location_html.content|safe }}
        </div>

        <div class="form-contact column6">
            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>
    </div>

    <div class="iframe-google-maps-wrapper">
        {{ iframe_google_map.content|safe }}
    </div>

{% endif %}


{#Habitaciones#}
{% if rooms %}
    <div class="room_wrapper">
        {% for room in rooms %}
            <div class="rooms column6 {% cycle 'blockleft-room' 'blockright-room' %}"
                 id="room_block_{{ forloop.counter }}">
                <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);"
                   class="rooms-img">
                    <img src="/img/{{ base_web }}/ico_fotos_blocks.png" class="ico_cam_room">
                    <img data-src="{{ room.pictures.0.servingUrl }}=s560" class="room_img" lazy="true">
                </a>

                <div class="rooms-description">
                    <h3 class="title-module"><span class="destino">{{ room.name|safe }}</span></h3>
                    {{ room.description|safe }}
                    <div class="room-links">
                        <span class="btn-corporate"><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                                                       class="button-promotion">{{ T_reservar }}</a></span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <script>
        $(function () {
            max_room = 0;
            $(".rooms-description").each(function () {
                actual_room = $(this).height();
                if (actual_room > max_room) {
                    max_room = actual_room;
                }
            })
            $(".rooms-description").height(max_room);
        });
    </script>
{% endif %}

{% if form_contact %}
    {% include "banners/_contact_form.html" %}
{% endif %}

{% if services_cycle %}
    {% include "_services_cycle.html" %}
{% endif %}

