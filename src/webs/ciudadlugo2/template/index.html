{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}

<section id="slider_container">
    {% if not inicio %}
        <div class="inner_slider">
            <img src="{{ pictures.0.servingUrl }}=s1900" alt=""/>
        </div>
    {% else %}
        {{ revolution_slider|safe }}
        {% if ticks %}
            <div class="ticks">
                <div class="container12">
                {% for tick in ticks %}
                    <div class="tick">
                        {% if tick.description %}
                            <i class="fa {{ tick.description|safe }}"></i>
                        {% else %}
                            <img src="{{ tick.servingUrl|safe }}" alt="">
                        {% endif %}
                        <span>{{ tick.title|safe }}</span>
                    </div>
                {% endfor %}
                </div>
            </div>
        {% endif %}
    {% endif %}
    <div id="full_wrapper_booking">
        <div id="wrapper_booking" class="container12">
            <div id="booking" class="boking_widget_inline">
                {{ booking_engine }}
            </div>
        </div>
    </div>
</section>


{% endblock %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% if minigallery %}
    <div class="minigallery_wrapper owl-carousel">
        {% for pic in minigallery %}
            <div class="img_wrapper">
                <img src="{{ pic.servingUrl|safe }}" alt="">
            </div>
        {% endfor %}
    </div>
    <script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script>
    $(window).on("load", function () {
        $(".minigallery_wrapper.owl-carousel").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 8,
            margin: 5,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            autoplay: true
        });
    });
    </script>
{% endif %}

<section class="footer_banners">
    {% include "banners_footer.html" %}
</section>

{% endblock %}

{% include "footer.html" %}


{% endblock %}