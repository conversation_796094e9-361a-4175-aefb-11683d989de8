<div class="cycle_banners_wrapper">
    <div class="container12 container_web">
        {% for banner in cycle_banners %}
            <div class="banner effects_sass" sass_effect="slide_up_effect">
                <div class="banner_image">
                    <img src="{{ banner.servingUrl|safe }}=s800" alt="{{ banner.altText|safe }}">
                </div>
                <div class="banner_info">
                    <h2>{{ banner.title|safe }}</h2>
                    <div class="description">{{ banner.description|safe }}</div>
                    <div class="banner_links">
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl|safe }}"
                               class="button-promotion" {% if banner.external %}target="_blank"{% endif %}>
                                {% if banner.btn_text %}
                                    {{ banner.btn_text|safe }}
                                {% else %}
                                    {{ T_ver_mas }}
                                {% endif %}
                            </a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="button-promotion">
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                        {% if banner.description and "hide" in banner.description %}
                            <span class="hide_description deploy">
                                {{ T_ver_mas }}
                            </span>
                        {% endif %}


                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function(){
        $("span.hide_description").click(function(){
            var button = $(this),
                description_hide = button.closest(".banner_info").find(".description .hide");
            description_hide.slideToggle("slow");
            $(this).toggleClass("deploy");

            if ($(this).hasClass("deploy")) {
                $(this).text("{{ T_ver_mas }}");
            } else {
                $(this).text("{{ T_ver_menos }}");
            }
        });
    })
</script>