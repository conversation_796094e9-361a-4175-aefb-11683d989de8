<div class="highlight_banner_wrapper">
    <div class="container12 container_web">
        {% if highlight_section %}
            {% if highlight_background %}
                <img class="highlight_background animated infinite pulse rubberBand slower" src="{{ highlight_background|safe }}=s320">
            {% endif %}

            <div class="highlight_section_content ">
                <div class="highlight_information animated effects_sass" sass_effect="bounceInLeft">
                    <h2 class="highlight_section_title">{{ highlight_section.subtitle|safe }}</h2>
                    <div class="highlight_section_description">{{ highlight_section.content|safe }}</div>

                    {% if highlight_icons %}
                        <div class="highlight_icons">
                            {% for icon in highlight_icons %}
                                <div class="icon_element">
                                    <div class="icon_picture">
                                        {% if icon.icon %}
                                            <i class="fa {{ icon.icon }}"></i>
                                        {% else %}
                                            <img src="{{ icon.servingUrl|safe }}">
                                        {% endif %}
                                    </div>
                                    <p class="icon_title">{{ icon.title|safe }}</p>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="highlight_section_gallery owl-carousel">
                    {% for picture in highlight_banners %}
                        <div class="gallery_element animated effects_sass delay-{{ loop.index0 }}s" sass_effect="slideInUp">
                            <a {% if picture.linkUrl %}href="{{ picture.linkUrl|safe }}"{% endif %}>
                                <img src="{{ picture.servingUrl|safe }}" class="center_image">
                                <h3 class="center_xy">{{ picture.title|safe }}</h3>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
    $(window).load(function () {
        var owl_params = {
            loop: true,
            dots: true,
            items: 3,
            margin: 40,
            autoplay: true,
            responsive : {
                0 : {
                    items : 1
                },
                480 : {
                    items : 2
                },
                768 : {
                    items : 3
                }
            }
        };

        $(".highlight_section_gallery").owlCarousel(owl_params);
    })
</script>