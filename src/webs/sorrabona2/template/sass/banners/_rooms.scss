.room_wrapper {
  margin: 140px 0;

  .container12 {
    position: relative;
  }

  .room_element {
    position: relative;
    margin-top: 100px;

    &:first-child {
      margin-top: 0;
    }

    .room_slider_gallery {
      width: 400px;
      display: inline-block;
      z-index: 1;
      box-shadow: 1px 1px 12px 2px rgba(black, .2);
      position: absolute;
      top: -60px;

      .owl-item {
        height: 400px;
        overflow: hidden;

        img {
          width: auto;
        }
      }
      .owl-nav {
        position: absolute;
        left: 0;
        text-align: right;
        .owl-prev, .owl-next {
          display: inline-block;
          vertical-align: middle;
          i {
            color: $corporate_1;
            font-size: 42px;
          }
          &.disabled {
            cursor: default;
            i {
              color: rgba($corporate_1, .4);
            }
          }
        }
        .owl-next {
          margin-left: 20px;
        }
      }
      .room_picture a {
            @extend .fa-search;
            &:before {
              @extend .fa;
              position: absolute;
              color: $corporate_1;
              right: 10px;
              top: 10px;
              font-size: 22px;
              z-index: 2;
            }
          }
    }

    .room_content {
      width: 680px;
      display: inline-block;
      background-color: rgba(white, 0.5);
      color: $corporate_1;
      padding: 80px 40px 90px 280px;
      z-index: 0;
      box-shadow: 1px 1px 12px 2px rgba(black, .2);
      float: right;

      .room_title {
        font-size: 28px;
        width: 370px;
        font-family: $font_family;
        text-transform: uppercase;
      }

      .room_description {
        margin-top: 40px;
        line-height: 24px;
        font-size: 14px;
        font-family: $font_family_2;
      }

      .button-promotion {
        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 12px;
        background-color: $corporate_4;
        color: $corporate_1;
        padding: 5px 15px;
        display: inline-block;
        cursor: pointer;
        margin-top: 40px;
        @include transition(opacity, .4s);
        box-shadow: 3px 3px 0 0 lighten(gray, 15%);

        &:hover {
          opacity: .8;
        }
      }
    }
    &:nth-child(odd) {
      .room_slider_gallery {
        right: 0;
        .room_picture a {
            &:before {
              left: 10px;
            }
          }
      }
      .room_content {
        float: left;
        padding: 80px 280px 90px 40px;
      }
    }
  }
}