<div id="main-sections">
	<ul id="main-sections-inner" class="container">
		{% for section in main_sections %}
		<div class="main-section-div-wrapper section_{{ forloop.counter }}" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}

					<a>{{ section.title|safe }}</a>
            {% else %}
                <a href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}">
                    {{ section.title|safe }}
                </a>
            {% endif %}

            {% if section.subsections %}
            <ul>


                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">
                        <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                        {{ subsection.title|safe}}
                        </a>
                    </li>
                {% endfor %}
            </ul>
            {% endif %}


		</div>
		{% endfor %}

        <a href="#data" class="button_promotion"><span class="book_menu">{{ T_reservar }}</span></a>
	</ul>
</div>