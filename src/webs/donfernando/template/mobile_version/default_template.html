<link rel="stylesheet" href="/static_1/css/templates/mobile/rooms/rooms_3.css" type="text/css" media="screen"/>
<div class="normal_section_mobile">

    <h2 class="section_title">{{section_params.title|safe}}</h2>

    <div class="section-content">
       {{section_params.content|safe}}
    </div>

</div>


{# Events Blocks #}
{% if events_blocks %}
    <div class="gray_background">
        <div class="destinations_elements_wrapper container12">
            {% for x in events_blocks %}

                <div class="dest_wrapper" id="destinationblock-{{ forloop.counter }}">

                    <div class="title-dest">{{ x.subtitle|safe }}</div>

                    <ul class="slides">
                        {% for picture in x.pictures %}
                            <li>
                                <img class="room_picture" src="{{ picture.servingUrl|safe }}=s1900">
                            </li>
                        {% endfor %}
                    </ul>


                    <span class="separator"></span>
                    <div class="desc-dest" id="desc-dest-{{ forloop.counter }}">
                          <span id="real_desc_dest_{{ forloop.counter }}" class="real_desc_dest">
                            {{ x.content|safe }}
                          </span>
                    </div>
                    <script>
                        $(function () {
                            $("#destinationblock-{{ forloop.counter }}").flexslider({
                                controlNav: true
                            });
                        });
                    </script>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}



{# Images Carousel #}
{% if mini_gallery %}
    <div class="mini_gallery_wrapper">
        <div class="flexslider_mini_gallery">
            <ul class="slides">
                {% for x in mini_gallery.pictures %}
                    <li>
                        <div class="text-bannerx2">
                            <a href="{{ x|safe }}=s1900" rel="lightbox[gallery]" class="swipebox">
                                <img src="{{ x|safe }}">
                            </a>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>

        <script>
            $(function () {
                $(".flexslider_mini_gallery").flexslider({
                    controlNav: false
                });
            });
        </script>
    </div>
{% endif %}


<style>
    .content > .booking_general_button {
        display: block;
    }
</style>