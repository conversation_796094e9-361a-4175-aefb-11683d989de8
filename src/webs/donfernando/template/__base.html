<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:100,200,300,400,200italic,300italic' rel='stylesheet' type='text/css'>
<title>{% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} </title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

{% if namespace %}
	  <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
{% else  %}
      <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
{% endif %}

<meta name="keywords" content="{{keywords|safe}}" />
<meta name="description" content="{{description|safe}}" />
<meta name="revisit-after" content="2 days" />
<meta http-equiv="Content-Language" content="{{language}}" />

<meta name="dc.title" content="{% if sectionName %} {{sectionName|safe}} - {% endif %} {{hotel_name|safe}}" />
<meta name="dc.description" content="{{description|safe}}" />
<meta name="dc.keywords" content="{{keywords|safe}}" />
<meta name="dc.language" content="{{ language }}" />
<meta name="dc.creator" content="{{ hotel_name }}"/>
<meta name="dc.format" content="text/html" />
<meta name="dc.identifier" content="{{ hostWithoutLanguage}}{{ path }}" />
<script type="text/javascript">
if(navigator.userAgent.match(/Android/i)
  || navigator.userAgent.match(/webOS/i)
  || navigator.userAgent.match(/iPhone/i)
  || navigator.userAgent.match(/iPad/i)
  || navigator.userAgent.match(/iPod/i)
  || navigator.userAgent.match(/BlackBerry/i)
  || navigator.userAgent.match(/Windows Phone/i)) {
    document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes, maximum-scale=1.2">');
}
</script>

<!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

<!--[if lte IE 8]>
<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->


<!-- jquery -->
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>



<!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen" />




    {% if datepicker_theme %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"  />
    {% endif %}

<!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>


<!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css" />
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=2.1" />


<!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

<!--[if IE 9]>

<![endif]-->

    {{ extra_head|safe }}

</head>

<body style="background: {% if interior %}rgb(236, 236, 236){% endif %} url({{ interior_background.0.servingUrl }}=s1600);background-size: cover;" class="{{ language }}{% if interior %} interior{% endif %}" onbeforeunload="deleteCookie('maps');deleteCookie('street')">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}


{% block content %}

<!--EDIT HERE YOUR PAGE-->

{% endblock %}


<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>
<script type="text/javascript" src="//maps.googleapis.com/maps/api/js?sensor=false&language={{ language }}"></script>
<script type="text/javascript" src="/static_1/scripts/location.js"></script>
<script type="text/javascript" src="/static_1/scripts/common.js"></script>

{%  if google_analytics_id %}
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', '{{google_analytics_id}}']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();


</script>
{%  endif %}

{% block additional_js %}



    <!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{language_code}}.js" type="text/javascript"></script>

      <!-- new booking engine -->
    <script src="/static_1/scripts/booking_2.js"></script>
    <script type="text/javascript" src="/static_1/lib/spin.min.js"></script>
    <script src="/static_1/scripts/hotel_selector_2.js"></script>

    <!-- lightbox -->
    <script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <script src="/static_1/lib/selectric/jquery.selectric.min.js" type="text/javascript"></script>


     <!-- My specific js  -->
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js"></script>

    <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>


    <!-- KenBurn Slider ALWAYS AT THE END!!!!!!!! -->
     <!-- jQuery KenBurn Slider  -->
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.revolution.js"></script>

    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>

    <script src="/static_1/lib/jquery.simpleweather/jquery.simpleWeather.min.js"></script>

    <!-- Flex Slider-->
    <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>


{% endblock %}


<div style="display: none;">
    <div id="data">
        <div id="wrapper_booking_fancybox">
            <div id="booking_widget_popup" class="booking_widget_fancybox">
                {{ booking_engine }}
            </div>
        </div>
    </div>
</div>


<script>
     $(".maps_switcher").click(function(){
            cookie_estado = searchCookie("maps");

            if(!cookie_estado){
                $(".forcefullwidth_wrapper_tp_banner").css('display', 'none');
                $("#street_view_container").css('display', 'none');
                $("#slider_map_container").css('display', 'block');
                createCookie('maps', '1');
                $("#wrapper_booking").css('display', 'none');
                $(this).text($.i18n._("volver_atras"));
                $(".street_switcher").text($.i18n._("ver_en") + " Street View");
                deleteCookie("street");
            }else{
                $(".forcefullwidth_wrapper_tp_banner").css('display', 'block');
                $("#slider_map_container").css('display', 'none');
                deleteCookie("maps");
                $("#wrapper_booking").css('display', 'block');
                $(this).text("{{ T_ver_en_mapa }}");
            }
        });


     $(".street_switcher").click(function(){
            cookie_estado_2 = searchCookie("street");

            if(!cookie_estado_2){
                $(".forcefullwidth_wrapper_tp_banner").css('display', 'none');
                $("#slider_map_container").css('display', 'none');
                $("#street_view_container").css('display', 'block');
                createCookie('street', '1');
                $("#wrapper_booking").css('display', 'none');
                $(this).text($.i18n._("volver_atras"));
                $(".maps_switcher").text("{{ T_ver_en_mapa }}");
                deleteCookie("maps");
            }else{
                $(".forcefullwidth_wrapper_tp_banner").css('display', 'block');
                $("#street_view_container").css('display', 'none');
                deleteCookie("street");
                $("#wrapper_booking").css('display', 'block');
                $(this).text($.i18n._("ver_en") + " Street View");
            }
        });
</script>

<!--Start of LiveBeep Script-->
<script type="text/javascript">
(function(d,s){
u=(('https:' == d.location.protocol) ? 'https://' : 'http://') + 'www.livebeep.com/'+d.domain+'/eye.js';if((h=d.location.href.split(/#ev!/)[1])) u += '?_e=' +h;else if((r=/.*\_evV=(\w+)\b.*/).test(c=d.cookie) ) u += '?_v=' + c.replace(r,'$1');var lb = document.createElement('script');lb.type = 'text/javascript';lb.async = true;lb.src = u;var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(lb, s);})(document,'script');
</script>
<!--End of LiveBeep Script-->


{% if bottom_popup and bottom_popup_text %}
    <div class="bottom_popup" style="display: none">
        <div class="close_button">
            <img src="/static_1/images/close.png" alt="close" title="close"/>
        </div>
        <a class="myFancyPopup" href=".popup_inicial">
            <div id="wrapper2">
                <div class="bottom_popup_text">{{ bottom_popup.content|safe }}</div>
                <button class="bottom_popup_button">{{ T_apuntate|safe }}</button>
            </div>
        </a>
    </div>
    <div style="display:none;">
        <div class="popup_inicial" style="background:url({{ bottom_popup_text.pictures.0 }});">
            <p class="popup_description">
                {{ bottom_popup_text.content|safe }}
            </p>

            <form action="" method="post" class="form_popup">
                <ul>
                    <li>
                        <input id="id_email" type="text" name="email" maxlength="150"
                               placeholder="{{ T_introduce_email_placeholder }}">
                    </li>
                </ul>
                <button class="popup_button">{{ bottom_popup_text.subtitle|safe }}</button>
                <div class="spinner_wrapper_faldon" style="display:none;"><img src='/static_1/images/spinner.gif'
                                                                               width="30" height="30" alt="spinner"
                                                                               title="spinner"></div>
            </form>

            {% if bottom_popup_background.0.description %}
                <div id="new_gracias_newsletter"
                     style="display: none">{{ bottom_popup_background.0.description|safe }}</div>
            {% endif %}
        </div>
    </div>


{% endif %}

{{ extra_content_website|safe }}
</body>
</html>
