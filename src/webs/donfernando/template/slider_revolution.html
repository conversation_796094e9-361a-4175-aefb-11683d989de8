<style type="text/css">

    .tp-banner-container {
        width: 100%;
        position: relative;
        padding: 0;
        min-width: {{ startWidth }}px;
        height: {{ initialHeight }}px;
    }

    .tp-banner {
        width: 100%;
        position: relative;
        display: none;
    }

    .tp-arr-titleholder {
        display: none !important;
    }


</style>


<div class="tp-banner-container">
    <div class="tp-banner">
        <ul>

            {% for picture in pictures %}
                <li data-title="{{ picture.title|safe }}" data-transition="slideleft" data-slotamount="7"
                    data-masterspeed="800">
                    <img src="/static_1/images/dummy.png" data-lazyload="{{ picture.servingUrl }}=s1600" alt="slider-revolution-{{forloop.counter}}">
                    {{ picture.description|safe }}
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<script type="text/javascript">

    var revapi;

    jQuery(document).ready(function () {

        revapi = jQuery('.tp-banner').revolution(
                {
                    //Add a comment to this line
                    delay: 6000,
                    startwidth: 1140,
                    startheight: 550,
                    hideThumbs: 10,
                    fullWidth: "on",
                    forceFullWidth: "on",
                    fullScreen: "off",
                    onHoverStop: "on",
                    lazyLoad: "on",
                    navigationArrows: "solo",
                    navigationType: "bullet",
                    hideTimerBar: "on",
                });

        $(".tp-banner").show();

    });	//ready

</script>