{#Normal Content#}
{% if normal_content %}
        <div class="normal_content{% if quita_huecos %} gallery{% endif %}">
            {% if flex_slider %}

                <div class="flexslider">
                    <ul class="slides">
                        {% for slideFlex in flex_slider %}
                            <li>
                                <img src="{{ slideFlex.servingUrl }}=s1600" class="flex_image">
                            </li>
                        {% endfor %}
                    </ul>
                </div>

            {% endif %}
            {% if section_name %}<h3 class="section_title">{{ section_name|safe }}</h3>{% endif %}
            {{ content }}
        </div>
    {% endif %}

{#Habitaciones#}
{% if rooms %}
    <div class="room_wrapper">
    {% for room in rooms %}
            <div class="rooms column6 {% cycle 'blockleft-room' 'blockright-room' %}"
                 id="room_block_{{ forloop.counter }}">
                <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);"
                   class="rooms-img">
                    <img src="/img/amera/ico_fotos_blocks.png" class="ico_cam_room">
                    <img src="{{ room.pictures.0.servingUrl }}=s560" class="room_img">
                </a>

                <div class="rooms-description">
                    <h3 class="title-module"><span class="destino">{{ room.name|safe }}</span></h3>
                    {{ room.description|safe }}
                    <div class="room-links">
                        <span class="btn-corporate"><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                                                       class="button-promotion">{{ T_reservar }}</a></span>
                        <span class="btn-flecha"><a href="#" class="btn_vermas_room">{{ T_ver_mas }} </a></span>
                    </div>
                </div>
            </div>
    {% endfor %}
    </div>
{% endif %}

{#Ofertas#}
{% if blocks %}
    <div class="scapes-blocks">
        {% for block in blocks %}
            <div class="block {% cycle 'row1' 'row2' %}">
                <div class="description">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    <ul>
                        <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva">{{ T_reservar }}</a> </li>
                        <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                    <img src="{{block.picture}}=s1000">
                </a>

                <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                    {{block.description|safe}}
                </div>
            </div>

        {% endfor %}
    </div>

    <script>
    if (document.documentElement.lang == 'en'){
        $(".oferta-reserva").html("Book")
    }
    </script>
{% endif %}

{#Localizacion y contacto#}
{% if contact_html %}
<div class="location-info-and-form-wrapper">
    <div class="location-info column6">
        {%  if img_4_title %}
            <h1>{{ img_4_title|safe }}</h1>
            <span></span>
       {% else %}
            <h1>{{ location_html.title|safe }}</h1>
       {% endif %}


        {{ location_html.content|safe }}



    </div>

    <div class="form-contact column6">

         <h1>{{ subtitle_form|safe }}</h1>
        {{ contact_html }}
    </div>
</div>


{% comment %}<div class="iframe-google-maps-wrapper">
    <div id="slider_map_container">
    <div class="map" id="map-canvas" style="height:450px">

    </div>
</div>
</div>{% endcomment %}

{% endif %}

{#Mini Gallery#}
{% if mini_gallery %}

    <div class="gallery-mosaic container12">
    <h3 class="gallery_title">{{ mini_gallery_title.subtitle|safe }}</h3>
    <div class="gallery_container">
    <div class="gallery-smalls column4">
        {% for picture in mini_gallery|slice:":7" %}
            {% if not forloop.first %}

                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe }}">
                        <img src="{{ picture.servingUrl }}=s500" alt="">
                    </a>
                </div>


            {% else %}


                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a {% if pictures.linkUrl %}class="youtube_video" href="{{ picture.linkUrl }}"{% else %}href="{{ picture.servingUrl }}=s1600" rel="lightbox[gallery]" title="{{ picture.name|safe }}"{% endif %}>
                        <img src="{{ picture.servingUrl }}=s1600" alt="">
                    </a>
                </div>
                </div>
                <div class="gallery-big column8">

            {% endif %}

        {% endfor %}
        </div>
        </div>


</div>

{% endif %}

{#Servicios#}
{% if some_services %}
<div class="some_services">

    <h3 class="services_title">{{ some_services_title.subtitle|safe }}</h3>
    {% for service in some_services %}
        <div class="service_wrapper">

        <img class="services_image" src="{{ service.servingUrl }}">
        <p class="service_title">{{ service.title|safe }}</p>

        </div>
    {% endfor %}

</div>
{% endif %}

{# Services Blocks #}
{% if blocks_in_section %}
<div class="blocks_section_container">
            {% for block in blocks_in_section %}
            <div class="block-activity {% cycle 'block-1' 'block-2' 'block-3'%}">

                <h4 class="block_activity_title">{{block.title|safe}}</h4>

                <div class="activity_home_image" >
                {% if block.pictures %}
                    <a class="cboxelement" id="image_rooms" href="javascript:showGallery([ {% for picture in block.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{picture.name|safe}}'}, {% endfor %} ]);">
                        <img src="/img/{{ base_web }}/icono_fotos_habitaciones.png" class="room-img-lupa">
                        <img src="{{block.servingUrl|safe}}=s370" >
                    </a>
                {%  else %}
                    <img src="{{block.servingUrl|safe}}=s370" >
                {%  endif  %}
                </div>

                <input type="hidden" name="descripcion_deployed" value="0" class="descripcion_deployed" id="descripcion_deployed_{{ forloop.counter }}">

                <div class="room_home_description" >
                     {{block.description|safe}}
                </div>

                 {% if im_mobile %}<div class="buttons-rooms">
                     <a href="{% if im_mobile %}/{% else %}#data{% endif %}" class="button-room-more"><button>{{ T_ver_mas }}</button></a>
                 </div>
                {% endif %}

            </div>

            {% endfor %}
 </div>
{% endif %}





{#Banners x2 Bottom#}
{% if banner_x2 %}

    <div class="bannerx2_wrapper">
        {% for banner in banner_x2 %}
            <div class="column6 banner_bottom">
                {% if banner.linkUrl %}<a href="{{ banner.linkUrl }}">{% endif %}<p class="banner_title">{{ banner.title|safe }}</p>{% if banner.linkUrl %}</a>{% endif %}
                {% if banner.linkUrl %}<a href="{{ banner.linkUrl }}">{% endif %}<div class="image_banner_wrapper">
                    <img class="banner_image" src="{{ banner.servingUrl }}=s1600">
                </div>{% if banner.linkUrl %}</a>{% endif %}
                <p class="banner_description">{{ banner.description|safe }}</p>
            </div>
        {% endfor %}
    </div>

{% endif %}