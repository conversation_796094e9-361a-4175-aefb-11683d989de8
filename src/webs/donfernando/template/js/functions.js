$(function () {

    $("#pikame").PikaChoose({showTooltips: true, carousel: false});


    //gallery rooms
    function showGallery(elements) {
        $.fancybox(elements, {
            'prevEffect': 'none',
            'nextEffect': 'none',
            'type': 'image',
            'arrows': true,
            'nextClick': true,
            'mouseWheel': true,
            'helpers': {
                title: {
                    type: 'outside'
                },
                overlay: {
                    opacity: 0.8,
                    css: {
                        'background-color': '#000'
                    }
                },
                thumbs: {
                    width: 50,
                    height: 50
                }
            }
        });
    }


    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });
    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".button_promotion").fancybox({
        width: 800,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });


    $(".button-room-more").fancybox({
        width: 800,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });


    $(".button-promotion").fancybox({
        width: 300,
        maxwidth: 300,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });

    $(".youtube_video").click(function () {
        $.fancybox({
            'padding': 0,
            'autoScale': false,
            'transitionIn': 'none',
            'transitionOut': 'none',
            'title': this.title,
            'width': 680,
            'height': 495,
            'href': this.href.replace(new RegExp("watch\\?v=", "i"), 'v/'),
            'type': 'swf',
            'swf': {
                'wmode': 'transparent',
                'allowfullscreen': 'true'
            }
        });

        return false;
    });


    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');


    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }





    if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}


    menu_length = $(".main-section-div-wrapper").length;

    for (i = 1; i <= menu_length; i++) {
        menu_width = $(".section_" + i).css("width");
        menu_width = parseInt(menu_width, 10) + 20;
        $(".section_" + i).css("width", menu_width);
    }

    $(".booking_form").wrap("<div class='border_booking'></div>");


    //Rooms Fancybox "Ver Mas"

    $(".room-links > .btn-flecha").click(function (e) {
        var id_popup = $(this).parent().parent().find(".id_popup").val();
        e.preventDefault();
        $.fancybox($("#" + id_popup));
    });


    locationApp.init(39.4750263, -6.371635);

     $('.flexslider').flexslider({
          controlNav: false,
          directionNav: false,
          animation: "slide"
     });

    var map_position_initial = $(".booking_widget").offset().left;
    $(".maps_switcher").css("left", map_position_initial + "px");
    $(".street_switcher").css("left", map_position_initial + "px");

    $(window).resize(function(){
        map_position_initial = $(".booking_widget").offset().left;
        if(!searchCookie("maps")){
            $(".maps_switcher").css("left", map_position_initial + "px");
            $(".street_switcher").css("left", map_position_initial + "px");
        }else{
            $(".maps_switcher").css("left", "auto");
            $(".street_switcher").css("left", "auto");
            $(".maps_switcher").css("right", "300px");
            $(".street_switcher").css("right", "300px");
        }
    });

try {
    gallery_first_src = $(".gallery_1 li:first-of-type .crop img").attr('src');
    gallery_first_src = gallery_first_src.split("=")[0]
    $(".gallery_1 li:first-of-type .crop img").attr('src', gallery_first_src);
}
catch(err) {

}




});


function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}

