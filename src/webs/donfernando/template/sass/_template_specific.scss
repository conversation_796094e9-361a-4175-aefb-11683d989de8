//************* General *************//
a {
  text-decoration: none !important;
}

body {
  color: rgb(75, 75, 75);
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 400;
}

/* Language */
.en a.button-promotion.oferta-reserva {
  margin-right: 10px;
}

//**************** Header *************//

header {
  width: 100%;
  height: 101px;
  color: white;
  overflow: visible;
  background: $corporate_1;

  #wrapper-header {
    position: relative;
    z-index: 22;
    height: 50px;
  }

  a {
    color: white;
  }

}

#logoDiv {
  margin-top: 0px;
  width: 245px;
  float: left;
  height: 115px;
  img {
    box-shadow: 2px 2px 5px black;
  }
}

.middle-header {
  margin-right: 0px !important;
  margin-left: 60px;
}

.top-row, .bottom-row {
  overflow: auto;
}

.bottom-row {
  margin-top: 7px;
}

.text_official {
  padding-top: 17px;
  .official {
    font-weight: lighter;
    padding-left: 75px;
    display: block;
    float: left;
    padding-top: 2px;
  }
}

#lang {
  float: right;
  text-transform: uppercase;
  margin-right: 0px;
  padding-bottom: 11px;
  font-size: 18px;
  font-weight: 400;

  .option {
    opacity: 0.8;
    cursor: pointer;
  }

  .selected .option {
    opacity: 1;
  }

  span.separator {
    margin-left: 0px;
    margin-right: 0px;
    font-weight: lighter;
  }

  a {
    color: white;
    font-weight: 100;
  }
  :not(.selected) {
    opacity: 0.8;
  }

  span:first-child {
    padding-right: 5px;
    font-weight: lighter;
    opacity: 1;
  }

  a span {
    padding-right: 0px;
  }
}

.web-oficial.interior {
  width: 1140px;
  margin: 0 auto;
  padding: 0px;
  font-size: 13px;
  background: $corporate_1;

  img {
    width: 20px;
    height: 20px;
    padding-top: 3px;
  }

  .tick_wrapper {
    padding-top: 4px;
    span {
      color: white !important;
    }
  }

}

.tick_wrapper {
  float: left;
  font-family: 'Source Sans Pro', sans-serif;
}

.tick_wrapper img {
  width: auto;
  height: 23px;
  padding-top: 4px;
}

.tick_center {
  display: table-caption;
  margin: 0 auto;
}

.en .web-oficial {
  width: 245px;
}

#social {
  width: 140px;
  float: right;
  margin-right: 0px !important;

  img {
    width: 32px;
    height: 32px;
  }

  a:hover {
    opacity: 0.8;
  }
}

#top-sections {
  margin-top: 6px;
  text-align: right;
  margin-right: 0px !important;
  font-size: 18px;
  width: 525px;
  font-weight: lighter;

  a span ::first-letter {
    text-transform: uppercase;
  }

  a:hover {
    opacity: 0.8;
  }
}

#main_menu {
  background: white;
  height: 40px;
}

#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  height: 40px;
  padding-left: 275px;
  box-sizing: border-box;
}

#mainMenuDiv a {
  padding: 9px 8px 5px;
  text-decoration: none;
  color: $corporate_3;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  &:hover:not(.button_promotion) {
    color: $corporate_3;
    border-top: 1px solid;
    font-size: 14px;
    padding-top: 0px;
    padding-bottom: 0px;
    border-bottom: 1px solid;
  }
}

#mainMenuDiv a.button_promotion:hover {
  font-weight: 400;
  opacity: 0.8;
}

#section-active a {
  color: $corporate_3;
  border-top: 1px solid;
  font-size: 14px;
  padding-top: 0px;
  padding-bottom: 0px;
  border-bottom: 1px solid;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;
  height: 40px;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper {
  text-align: center;

  a {
    line-height: 23px;
    font-size: 15px;

    &:hover {
      color: $corporate-2;
    }
  }
}

//Top_menu//
span.separator {
  margin-left: 6px;
  margin-right: 2px;
}

a.separator {
  margin-left: 3px;
}

.book_menu {
  background: $corporate_2;
  padding: 4px 22px;
  color: white;
}

.middle-header {
  float: right;
  width: 700px;
}

/*=============== Booking Widget ================*/

div#wrapper_booking {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 450px;
}

.spinner_wrapper {
  text-align: center;
  position: relative !important;
  width: 25px;
  height: 25px;
  margin: auto;
  right: 0px !important;
  bottom: 0px !important;
  padding: 15px;
  display: none;
}

.interior #wrapper_booking {
  height: 500px;
  bottom: auto;
}

.fancybox-inner {
  overflow: initial;
}

.border_booking {
  padding: 0px 10px 10px;
  height: auto !important;
  width: 370px !important;
  box-sizing: border-box;
  background: white;
}

#data {
  height: auto !important;
}

.booking_widget, #data {
  right: 0px;
  top: 0px;
  bottom: 0px;
  margin: auto;
  height: 405px;

  .booking_form {
    border-right: 1px solid $corporate_1;
    border-bottom: 1px solid $corporate_1;
    border-left: 1px solid $corporate_1;
    height: 100%;
    box-sizing: border-box;
    margin: 0px !important;
    padding: 0px;
    width: 350px;

    .stay_selection {
      display: table;
      text-align: center;
      margin: 0 auto;
      padding-top: 20px;
    }

    .wrapper_booking_button button {
      border-radius: 0px;
      width: 245px;
      float: none;
      margin: 8px auto 0px;
      display: block;
      font-weight: lighter;
      font-size: 20px;
      font-family: "Source Sans Pro", sans-serif;
      background: $corporate_2 url("/static_1/images/booking/flecha_motor_der.png") no-repeat scroll 95% 50%;
      height: 40px;
      margin-bottom: 12px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .best_price {
    display: none;
  }

  h4.booking_title_2 {
    display: block;
    font-family: yanone, sans-serif;
    text-transform: uppercase;
    font-weight: 300;
    font-size: 26px;
  }

  .booking_form_title {
    background: $corporate_3;
    margin-top: -13px !important;
  }

  .booking_form_title:before {
    border-top: 8px solid $corporate_3;
    bottom: -7px;
  }

  .date_box {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    &:before {
      border-bottom: 8px solid rgb(234, 234, 234);
    }

    .date_day .day {
      border-bottom: 2px solid $corporate_1;
      font-weight: bolder;
      line-height: 50px;
      font-size: 54px;
    }

    .date_day .month {
      border-bottom: 2px solid $corporate_1;
    }
  }

  .selectric {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    &:before {
      border-bottom: 8px solid #EAEAEA;
      top: -6px;
    }

    .button {
      display: none;
      background: url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
    }
  }

  .selectricItems {
    li.selected {
      background: #EFEFEF;
      color: #444;
      border-top-color: #E0E0E0;
    }

    li {
      font-weight: 500;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
      color: #a5a5a5;
      font-size: 25px;
      line-height: 40px;
      border-bottom: 1px solid #fff;
      padding: 10px 0px;
      width: 100%;
      text-align: center;

      &:hover {
        background: #EFEFEF;
        color: #444;
        border-top-color: #E0E0E0;
      }
    }

    ul {
      z-index: 40;
    }

    .room {
      padding-top: 17px;
    }
  }

  .room_title {
    padding-left: 30px;
  }

  .adults_selector {
    padding-left: 19px;
  }

  .promocode_input {
    background: rgb(234, 234, 234);
    width: 245px !important;
    margin-left: 51px;
    border-radius: 0px !important;
    padding-top: 11px;
    box-sizing: border-box;
  }

  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .wrapper_booking_button .promocode_text {
    color: rgb(134, 134, 134);
  }

  .promocode_text {
    font-family: yanone, sans-serif;
  }
}

.wrapper-new-web-support {
  opacity: 1 !important;
  border-radius: 0px !important;

  &:before {
    content: none;
  }
}

.interior .booking_widget {
  margin: 50px auto;
}

.booking_widget.location {
  right: 20.2vw;
}

//******************* Content *******************//

section#content {
  font-family: 'Source Sans Pro', sans-serif;
  background: white;
}

.normal_content {
  padding-top: 60px;
  text-align: center;
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;

  strong {
    color: $corporate_3;
  }

  .section_title, .section-title {
    font-family: yanone, sans-serif;
    text-transform: uppercase;
    color: $corporate_3;
    padding: 0px 59px 17px;
    display: table;
    font-size: 34px;
    margin: 0 auto 20px;
    background: url(/img/amera/dot.png) repeat-x;
    background-position: bottom;
    background-size: 11px;
  }

}

.interior .normal_content {
  margin-top: 30px;
  background: rgba(255, 255, 255, 0.8);
  padding: 25px;
  box-sizing: border-box;
  margin-bottom: 30px;
}

img.flex_image {
  width: 100%;
  margin-bottom: 30px;
}

////*Gallery Mosaic*///////

.gallery_title {
  padding-top: 60px;
}

.gallery_title, .services_title {
  font-family: yanone, sans-serif;
  font-weight: 400;
  background-position: bottom;
  color: $corporate_3;
  padding: 33px 59px 17px;
  display: table;
  font-size: 34px;
  background: url(/img/amera/dot.png) repeat-x;
  background-position: bottom;
  margin: 0px auto 45px;
  background-size: 11px;
}

.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 137%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;
  height: 360px;

  img {
    width: 100%;
    height: 97%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 0px auto;
}

.gallery_container {
  margin-left: 22px;
}

//************** Servicios_hotel  ***************//

.services_image {
  display: block;
  margin: 0 auto;
  width: 113px;
  height: 113px;
  padding: 0px 10px 10px;
}

.service_wrapper {
  display: inline-block;
  width: auto;
  vertical-align: top;
  text-align: center !important;
}

.some_services {
  text-align: center;
}

//**************** Footer *****************//
footer {
  background: $corporate_3;
  padding: 40px 0;
  color: white;
  font-weight: 300;
  font-size: 14px;
  margin-top: 60px;

  a {
    color: white;
    line-height: 20px;

    &:hover {
      color: $gray-4;
    }
  }
}

.interior footer {
  margin-top: 0px;
}

.footer_column .footer_column_title,
.footer_column.last h2 {
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.49);
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 500;
  width: 80%;
}

div#footer_column_description {
  width: 70%;
  font-family: 'Source Sans Pro', sans-serif;

  strong {
    font-family: yanone, sans-serif;
    display: block;
    font-weight: 500;
    font-size: 17px;
  }
}

label#suscEmailLabel {
  width: 252px;
}

.newsletter_container {
  width: auto;
}

#title_newsletter2 {
  font-family: yanone, sans-serif;
  display: block;
  border-bottom: 0px;
  margin-bottom: 0px;
  text-transform: none;
  font-size: 17px;
  padding-bottom: 0px;
  font-weight: 500;
}

.full-copyright {
  margin-top: 20px;
  font-size: 14px;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;

  p {
    margin-bottom: 0px;
  }
}

#suscEmail {
  height: 20px;
  border-radius: 3px;
  outline: medium none;
  border: medium none;
  margin-top: 10px;
  background: rgba(255, 255, 255, 0.2) none repeat scroll 0% 0%;
  width: 85%;
  color: white;
  padding-left: 13px;

  &::-webkit-input-placeholder {
    color: white;
  }
}

.button_newsletter {
  background: white;
  padding: 5px 28px 5px;
  border: none;
  color: $corporate_3;
  cursor: pointer;
  height: 15px;
  margin-top: 10px;
  font-weight: bolder;
  width: 44px;
  font-size: 13px;
  border-radius: 3px;
  text-transform: uppercase;

  &:hover {
    opacity: 0.8;
  }
}

.newsletter_checkbox {
  margin-top: 3px;

  a {
    text-decoration: underline !important;
  }
}

.last img {
  margin-top: 20px;
  margin-left: -3px;
}

.banner-block .flex-direction-nav,
.room-block .flex-direction-nav,
.lounge-block .flex-direction-nav {
  display: none;
}

#google_plus_one {
  text-align: center;
}

#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

#facebook_like {
  text-align: center;
  margin-top: 10px;
}

#facebook_like iframe {
  height: 21px;
  width: 103px;
}

//*************** Banners x2 *****************//
.bannerx2_wrapper {
  margin-top: 60px;

  p.banner_title {
    text-align: center;
    font-size: 18px;
    font-weight: lighter;
    margin-bottom: 19px;

    strong {
      display: block;
      font-size: 34px;
      color: $corporate-3;
      font-weight: 400;
      font-family: yanone, sans-serif;
    }
  }
  .image_banner_wrapper {
    height: 160px;
    overflow: hidden;
    img.banner_image {
      width: 100%;
      height: 100%;
    }
  }

  p.banner_description {
    background: $corporate_1;
    color: white;
    text-align: center;
    padding: 25px;
    line-height: 29px;
    font-weight: lighter;
  }
  .dot_separation {
    height: 20px;
    background: url(/img/amera/dot.png) repeat-x;
    width: 457px;
    margin: 0 auto 25px;
    background-size: 11px;
  }
}

.banner_bottom a {
  color: rgb(75, 75, 75);
}

//********************* Habitaciones ************************//

.room_wrapper {
  margin-top: 35px;
}

div#description-main-section.content_rooms {
  box-sizing: border-box;
  margin-top: 0px;
}

section#top_content {
  padding-top: 200px;
}

.rooms-description {
  background: rgba(245, 245, 245, 0.8);
  padding: 20px;
  min-height: 200px;
  color: grey;
  position: relative;

  .destino {
    font-weight: 700;
    font-family: yanone, sans-serif;
  }

  .title-module {
    font-size: 23px;
    color: $corporate_1;
    margin-bottom: 20px;

  }
}

.description-rooms {
  font-weight: lighter;
}

h4.title-module {
  font-size: 23px;
  color: $corporate_3;
  margin-top: 10px;
  margin-bottom: 5px;
}

.rooms {
  margin-bottom: 25px;
  position: relative;
}

.blockleft {
  margin-left: 0px;
}

.blockright {
  margin-right: 0px;
  margin-left: 30px;
}

.sub-description-rooms {
  margin: 10px 0 20px;
  font-weight: bold;
}

span.btn-corporate {
  position: absolute;
  top: 0;
  right: 0;
}

span.btn-corporate {
  position: absolute;
  top: 16px;
  right: 50px;
  text-transform: uppercase;
  color: white !important;
  background-color: $corporate_2;
  margin-right: 20px;
}

span.btn-corporate {
  padding: 6px 8px;
}

.btn-flecha {
  background-color: $corporate_1;
  height: 22px;
  padding: 5px 10px;
  position: absolute;
  width: 10px;
  top: 16px;
  right: 15px;
  text-align: center;
}

.rooms {
  .room_img {
    width: 100%;

  }
  .ico_cam_room {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 20;
  }
  span.btn-corporate {
    right: 100px;
  }
  .btn-flecha {
    width: 70px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

a.rooms-img {
  height: 220px;
  display: block;
  overflow: hidden;
  position: relative;
}

img.room_img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

a.btn_vermas_room {
  color: white !important;
  font-weight: 700;
  margin-left: 1px;
  text-align: center;
}

.room-links {
  a.button-promotion {
    color: white !important;
  }

  .btn-corporate:hover {
    opacity: 0.8;
  }
}

.blockleft-room {
  margin-left: 0px;
  margin-right: 30px;
}

.blockright-room {
  margin-right: 0px;
}

.myFancyPopupRooms {
  margin-left: 20px;
  margin-right: 20px;
  width: 550px;
}

/************************* SCAPES/OFERTAS ************************/
a.plus {
  padding: 7px 8px 9px !important;
}

a.play {
  padding: 10px 9px 5px !important;
}

.scapes-blocks {
  overflow: hidden;
  margin-top: 30px;
  margin-bottom: 10px;
}

.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  height: 492px;
  overflow: hidden;
  position: relative;

  a img {
    margin-bottom: -5px;
    width: 100%;
  }

  .description {
    padding: 20px;
    position: relative;
    background-color: rgba(245, 245, 245, 0.8);
    padding-right: 160px;

    .title-module {
      font-size: 23px;
      color: $corporate_1;
      font-weight: 700;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 115px;
      right: 0;
      top: 34px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline;
        a {
          background-color: $corporate_2;
          top: -1px;
          color: white;
          padding: 7px 7px 8px;
          right: 97px;
          position: absolute;
          text-align: center;
          height: 21px;
          &:hover {
            opacity: 0.8;
          }
        }
        a.plus {
          padding: 10px 7px 5px;
          margin-right: -75px;
          height: 20px;
          background: $corporate_1;
        }

        a.play {
          padding: 10px 9px 5px;

          img {
            margin-top: 2px;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_1;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}

/************  Location and Contact  *************/

.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }

}

.location-info-and-form-wrapper {
  margin-top: 30px;
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 20px;
  padding-bottom: 20px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: $corporate_3;
  width: 95%;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;

}

li.how-to-go {
  cursor: pointer;
  color: $corporate_1;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
  .car {
    background: url("/img/amera/icons_maps/car.png") left center no-repeat;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 300px !important;
}

#contactContent .bordeInput {
    float: left;
    margin-right: 10px;
    margin-top: 12px;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate_3 !important;
  color: white !important;
  margin-top: 10px;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate_1 !important;
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

.check_privacy {
  width: auto !important;
  margin-left: 0 !important;

  & + span {
    padding-right: 30px;
    padding-bottom: 14px;
    display: inline-block;
    a {
      display: block;
      margin-bottom: 10px;
      color: black;
      margin-top: 2px;
      font-weight: 300;
      text-decoration: underline!important;
    }
  }
}

//************* Galeria *************//

//VIDEO ON GALLERY
#video_corporativo {
  position: relative;
  z-index: 9;
  width: 567px;
  height: 380px;
  left: 2px;

  iframe {
    width: 566px !important;
    height: 376px !important;
    padding-top: 2px;
  }
}

.visita_or_video_mobile {
  float: left;
}

// SPECIAL SEPARATION IN GALLERY SEPARATION

.main-gallery {
  padding: 0px !important;
  background-color: transparent !important;
}

.gallery_1 li .crop {
  width: 283px !important;
  height: auto;
  overflow: hidden !important;
  border: 0px solid white !important;
  box-sizing: border-box !important;
}

.gallery_1 li .crop img {

  min-height: 190px !important;
  min-width: 285px !important;
}

.normal_content.gallery {
  padding: 0px;
}

//********** Mis Reservas **********//

#reservation {
  margin-left: 292px;
  margin-bottom: 20px;
}

#cancellation-confirmation-button {
  background: $corporate_1;
  border: 1px solid white;
  color: white;
  padding: 4px;
  font-size: 16px;
  border-radius: 4px;
  width: 200px;
  margin-top: 5px;
}

#cancelButton {
  display: none;
  text-align: center;
  margin: 20px auto 0px;
  background: #553a2b;
  color: white;
  border: 0px;
  cursor: pointer;
  padding: 7px 26px;
}

#my-bookings-form #hotelSelect {
  display: initial !important;
}

#description-main-section {
  background: rgba(252, 241, 235, 0.85);;
  margin-top: 200px;
  margin-bottom: 20px;
  padding: 40px;
  font-weight: 300;

  .section-title, h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }

  p {
    margin-bottom: 9px;
    color: black;
    font-weight: 300;
  }

  #my-bookings-form-fields {
    #my-bookings-localizador-label, #my-bookings-form-search-button {
      margin-left: 20px;
    }

    label {
      color: #000000;
      font-weight: 300;
    }

  }

  input {
    background: white;
    margin-bottom: 9px;
  }

  button {
    background: $corporate_1;
    border: 1px solid white;
    color: white;
    padding: 4px;
    font-size: 16px;
    width: 90px;
    border-radius: 4px;
  }

}

div#my-bookings-form-fields {
  width: 296px;
  margin: 20px auto;
  text-align: left;
}

input#localizadorInput, input#emailInput {
  width: 200px;
}

label#my-bookings-email-label {
  margin-right: 34px;
}

#cancelButton {
  width: 160px;
}

button#my-bookings-form-search-button {
  text-align: center;
  display: table;
  margin: 20px auto 0px;
  background: $corporate_3;
  color: white;
  border: 0px;
  cursor: pointer;
  padding: 7px 26px;

  &:hover {
    opacity: 0.8;
    background: $corporate_1;
  }
}

/*======================= Services Blocks ==========================*/
.blocks_section_container {

  margin-top: 60px;
  margin-bottom: 60px;

  .block-activity {
    width: 364px;
    display: inline-block;
    float: none;
    margin-right: 19px;
    margin-bottom: 20px;
    background: whitesmoke;
    vertical-align: top;

    &.block-3 {
      display: inline-block;
      margin-right: 0;
    }
  }

  .block_activity_title {
    padding: 30px 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
    font-style: italic;
    text-align: center;
    background: $corporate_1;
  }

  .block_description {
    float: left;
    width: 255px;
    height: 154px;
    background-color: rgb(245, 245, 245);
    padding: 20px 10px 10px 10px;
    text-align: center;
    position: relative;
  }

  .room_home_description {
    padding: 30px 30px 30px 30px;
    text-align: left;
    font-weight: 200;
    font-size: 12px;
    line-height: 19px;
    height: 75px;
    overflow: hidden;

    p {
      text-align: center;
    }
  }

  //room buttons
  .buttons-rooms {
    margin-top: 15px;
    text-align: center;
    padding-bottom: 15px;

  }

  .button-room-more button {
    cursor: pointer;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    color: white;
    border: 0;
    background-color: $corporate_1;
    font-size: 12px;
    text-transform: uppercase;

    width: 100px;

    padding: 5px 20px 5px 20px;
    text-decoration: none;

  }

  .button-room-more button:hover {
    color: white;
    background-color: $corporate_2;
  }

  .activity_home_image {
    width: 364px;
    height: 237px;
    overflow: hidden;
    position: relative;
  }

  .room-img-lupa {
    position: absolute;
    top: 100px;
    right: 0px;
    margin-top: -90px !important;
    margin-right: 320px;
  }
}

/*============ Mapas ===========*/

.maps_switcher, .street_switcher {
  position: absolute;
  bottom: 0px;
  z-index: 22;
  background: rgba(85, 58, 43, 0.83);
  width: 370px;
  text-align: center;
  color: white;
  box-sizing: border-box;
  text-transform: uppercase;
  font-size: 22px;
  padding: 10px;
  cursor: pointer;

  &:hover {
    background: $corporate_3;
  }
}

.street_switcher {
  bottom: 48px;
}

#street_view_container {
  height: 550px;
}

div#slider_map_container {
  z-index: 300;
}

#map-canvas {
  height: 550px !important;

  * {
    max-width: none;
  }
}

#map-canvas.default_hide {
  display: none;
}

.interior #map-canvas {
  height: 510px !important;
}

/*============ slider Container ===============*/

section#slider_container {
  position: relative;
  min-height: 510px;
}

section#slider_container iframe {
  width: 100% !important;
}

.slider_inner_container .slider_image {
  width: 100%;
  height: auto;
  position: fixed;
  top: 0px;
  z-index: -2;
  min-width: 1920px;
}

span.slider_text {
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  height: 80px;
  display: table-caption;
  color: white;
  text-transform: uppercase;
  font-size: 77px;
  font-weight: 200;
  line-height: 65px;
}

//*************** Galeria ****************//

.section_title {
  font-size: 77px;
  font-weight: 200;
  color: #15abc3;
  text-align: center;
  width: 700px;
  margin: 25px auto 55px;
  line-height: 65px;
}

.gallery_1 li .crop {
  width: 283px;
  overflow: hidden;
  img {
    display: block;
    height: 212px;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery_1 li {
  margin: 1px;
  float: left;
  height: 212px;
}

ul.gallery_1 {
  margin: 12px 0px 35px;
  display: table;
}

.gallery_1 li:first-of-type {
  height: auto;
  width: 568px;

  .crop {
    width: 100% !important;
    height: auto !important;
    img {
      width: 590px !important;
      height: auto !important;
    }
  }
}

.interior .tp-banner-container {
  height: 669px !important;
}

.interior .tp-revslider-mainul {
  position: fixed !important;
  z-index: -30;
}

/*========== Booking Widget =========*/

.ui-widget-header {
  background: $corporate_1 url(images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x;
  border: 1px solid $corporate_1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid $corporate_1;
  background: $corporate_1 url(images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x;
  font-weight: bold;
  color: white;
}

.ui-state-hover {
  background: $corporate_1;
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate-1;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  width: 120px;
  background: $gray-3;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }
    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}



