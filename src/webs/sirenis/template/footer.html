<footer class="has_transition">
    <div class="footer_content">
        <div class="container12">
            <div class="footer_columns_wrapper">
                {% for foo in footer_column %}
                    <div class="hotels_column">
                        {% if foo.title %}
                            <div class="content_title">
                                <h4 class="title">{{ foo.title|safe }}</h4>
                            </div>
                        {% endif %}
                        {% if foo.servingUrl %}
                            <div class="logo_wrapper">
                                <img src="{{ foo.servingUrl|safe }}" alt="">
                            </div>
                        {% endif %}
                        {% if foo.description %}
                            <div class="desc">
                                {{ foo.description|safe }}
                            </div>
                        {% endif %}
                        {% if foo.link_text %}
                            <a id="open_popup" class="btn btn_light">{{ foo.link_text|safe }}</a>
                        {% endif %}
                        {% if foo.rrss %}
                            <div class="footer_rrss">
                                {% if facebook_id %}
                                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                                        <i class="fa fa-facebook" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                                {% if twitter_id %}
                                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                                        <i class="fa fa-twitter" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                                {% if google_plus_id %}
                                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                                {% if youtube_id %}
                                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                                        <i class="fa fa-youtube" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                                {% if pinterest_id %}
                                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                                {% if instagram_id %}
                                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                                        <i class="fa fa-instagram" aria-hidden="true"></i>
                                    </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="footer_legal_text_wrapper">
            <div class="container12">
                <div class="footer_links_wrapper">
                    {% for x in policies_section %}
                        <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}"
                           class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                    {% endfor %}
                    <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
                       title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                    <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
                    <a target="_blank" href="/rss.xml">RSS</a>
                </div>

                {% if texto_legal %}
                    <div class="legal_text">{{ texto_legal|safe }}</div>
                {% endif %}
            </div>
        </div>
    </div>
</footer>
{% include "banners/_newsletter_popup.html" %}
