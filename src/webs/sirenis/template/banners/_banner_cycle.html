<div class="banner_cycle_wrapper has_transition  {% if is_mobile %}owl-carousel{% endif %}">
    {% for banner in cycle_banner_pics %}
        <div class="banner">
            {% if banner.servingUrl %}
            <div class="picture_wrapper">
                <img src="{{ banner.servingUrl|safe }}=s800" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
            </div>
            {% endif %}
            <div class="content_wrapper">
                {% if banner.title %}
                    <div class="content_title">
                        <h3 class="title">{{ banner.title|safe }}</h3>
                    </div>
                {% endif %}
                {% if banner.description %}
                    <div class="desc">{{ banner.description|safe }}</div>
                {% endif %}
                <a href="#data" class="button-promotion btn btn_light">
                    {{ T_reservar }}
                </a>
                {% if banner.linkUrl %}
                    <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %} class="btn btn_light_outline">
                        {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                    </a>
                {% endif %}
            </div>
        </div>
    {% endfor %}
</div>

<script>

    $(window).load(function () {

        $(".banner_cycle_wrapper.owl-carousel").owlCarousel({
            loop: true,
            nav: false,
            dots: true,
            items: 1,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            autoplay: false,
            autoHeight: true
        });

    });

</script>