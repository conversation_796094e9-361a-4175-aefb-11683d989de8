<div class="wrapper_content banner_text_wrapper has_transition {% if home %}sec_pad{% endif %} {% if banner_text_section_properties %}{{ banner_text_section_properties.extra_class|safe }}{% endif %}">
    <div class="container12">
        {% if banner_text_section.subtitle %}
        <div class="content_title text_center">
            <h3 class="title">{{ banner_text_section.subtitle|safe }}</h3>
        </div>
        <div class="desc text_center">
            {{ banner_text_section.content|safe }}
        </div>
        {% endif %}
        {% if banner_text_pictures %}
            <div class="gallery_wrapper">
                {% for pic in banner_text_pictures %}
                <div>
                    <a class="picture_link" href="{{ pic.servingUrl|safe }}" rel="lightbox[rooms_gallery]">
                        <img src="{{ pic.servingUrl|safe }}=s1900" alt="{{ pic.altText|safe }}">
                    </a>
                </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>