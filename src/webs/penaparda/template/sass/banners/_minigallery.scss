.minigallery_wrapper {
  width: 100%;
  overflow: hidden;

  .center_image {
    position: unset;
    height: 100%;
    transform: inherit;
  }
  .owl-item {
    background-color: $corporate_1;
    height: 300px;
    overflow: hidden;

    img {
      width: auto;
      opacity: 1;
      @include transition(all, .6s);
    }
    span {
      display: block;
      width: 90%;
      color: white;
      font-size: 20px;
      text-align: center;
      text-shadow: 0 0 5px rgba(0, 0, 0, .6);
      @include center_xy;
      i.fa {
        display: block;
        text-align: center;
        font-size: 25px;
      }
    }

    &:hover {
      img {
        opacity: .4;
      }
      .minigallery_desc {
        img {
          opacity: .8;
        }
      }
    }
    .minigallery_desc {
      img {
        opacity: .4;
      }
    }
  }

  .owl-nav {
    display: block;
    .owl-prev, .owl-next {
      @include center_y;
      color: white;
      cursor: pointer;
      font-size: 28px;
      left: 20px;
      @include transition(color, .6s);
      i.fa {
        @include center_xy;
        &:before {
          content: "\f053";
          font-family: "Font Awesome 5 Pro";
          font-weight: 300;
        }
      }
      &:hover {
        color: $corporate_1;
      }
    }
    .owl-next {
      left: auto;
      right: 20px;
      i.fa {
        &:before {
          content: "\f054";
        }
      }
    }
  }
}
