.boking_widget_inline {
  background: rgba(228, 228, 228, 0.31) !important;

  .booking_form {
    background: transparent !important;
  }

  .wrapper_booking_button button {
    font-family: 'Cabin', sans-serif;
    border-radius: 0px;
    font-size: 24px;
    width: 300px !important;
    background: #76a8d9 url(/static_1/images/booking/flecha_motor_der.png) no-repeat;
    background-position-x: 261px;
    -ms-background-position-y: center;
    background-position-y: center;
    font-weight: 300;
    vertical-align: middle;
    margin-top: 6px !important;
    margin-bottom: 4px;
  }
}

/*====== Booking Widget =====*/
#wrapper_booking {
  position: relative;
}

.date_box .date_year {
  color: rgb(135, 131, 120);
}

.room {
  padding-top: 7px;
}

.room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  color: white;
  font-weight: 300;
}

.date_box, .wrapper_booking_button .promocode_input, .selectric {
  border-radius: 0px;
}

.selectric .button {
  // background: none !important;
}

.promocode_text {
  text-align: center;
  cursor: pointer;
  margin-top: 26px;
  font-family: 'Cabin', sans-serif;
  font-size: 12px;
  color: white;
  padding-left: 10px;
  font-weight: 300;
  padding-top: 15px;
  strong {
    font-weight: inherit;
  }
}

.wrapper_booking_button button {
  width: 100%;
  border-radius: 0px;
}

.wrapper_booking_button .promocode_input {
  display: none;
}

.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label {
  color: white;
  font-weight: 300;
  font-size: 11px;
  padding-left: 5px;
}

.booking_widget {
  bottom: 160px;
  top: auto;
  left: 20px;
}

.booking_form {
  background: rgba(0, 0, 0, 0.5);
  padding-top: 4px;
}

.booking_form_title {
  background: rgba(0, 0, 0, 0.5);
  font-family: 'Cabin', sans-serif;
  text-transform: uppercase;
  padding-top: 16px;
  font-weight: 300;
  font-size: 18px;

  &:before {
    content: "";
    width: 24%;
    height: 2px;
    border-top: 1px solid white;
    position: absolute;
    left: 20px;
    top: 26px;
  }

  &:after {
    content: "";
    width: 24%;
    height: 2px;
    border-top: 1px solid white;
    position: absolute;
    right: 20px;
    top: 26px;
  }

  .best_price {
    display: none !important;
  }
  .booking_title_1 {
    display: block !important;
  }
}

/*=== Booking Widget Inline ==*/
.boking_widget_inline {
  background-color: rgba(255, 255, 255, 0.75);
  padding: 1px 0px 10px;
  width: 1140px !important;

  ul.room_list {
    padding-left: 20px;
  }

  .selectric .button {
    background: $corporate_1 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  }

  .booking_form {
    width: auto;
  }

  .stay_selection {
    margin-left: 42px;
  }

  div#wrapper_booking {
    position: absolute;
    margin: 0 auto;
    left: 0px;
    right: 0px;
    bottom: 88px;
    z-index: 23;
  }

  .booking_form {
    background-color: rgba(228, 228, 228, 0.31) !important;
    padding-bottom: 18px;
    padding-top: 14px;
  }

  .room {
    padding-top: 1px;
  }

  .selectric, .date_box {
    background: white;
    border-radius: 0px;
  }

  .date_box .date_year, .selectric .label {
    color: gray;
  }

  .room_selector .label {
    color: $corporate_1;
  }

  .wrapper_booking_button {
    display: table;

    button {
      font-family: 'Cabin', sans-serif;
      border-radius: 0px;
      font-size: 24px;
      width: 300px !important;
      background: $corporate_1 url(/static_1/images/booking/flecha_motor_der.png) no-repeat;
      background-position-x: 261px;
      -ms-background-position-y: center;
      background-position-y: center;
      font-weight: 300;
      vertical-align: middle;
      margin-top: 6px !important;
      margin-bottom: 4px;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .submit_button {
    cursor: pointer;
  }

  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label {
    color: $corporate_2;
    font-weight: 500;
  }

  .promocode_input {
    border-radius: 0px;
    position: absolute;
    margin-top: 0px;
    top: 49px;
    left: 34px;
    box-shadow: 2px 2px 3px rgb(102, 102, 102);
    background: white;
    display: none;
    color: $corporate_1;

    &::-webkit-input-placeholder {
      color: $corporate_1;
    }
    &:-moz-placeholder {
      color: $corporate_1;
      opacity: 1;
    }
    &::-moz-placeholder {
      color: $corporate_1;
      opacity: 1;
    }
    &:-ms-input-placeholder {
      color: $corporate_1;
    }
  }

  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
    font-weight: 400;
    color: rgb(73, 73, 73);
    font-family: 'Cabin', sans-serif;
    font-size: 11px;
  }

  .promocode_text {
    text-align: center;
    float: left;
    cursor: pointer;
    margin-top: 5px;
    font-family: 'Cabin', sans-serif;
    font-size: 11px;
    text-decoration: underline;
    color: rgb(73, 73, 73);
    padding-left: 15px;
    font-weight: 400;
    margin-right: 25px;
    strong {
      font-weight: inherit;
    }
  }

  .wrapper_booking_button .promocode_input {
    display: none;
  }

  #data {
    position: relative;
    .promocode_text {
      margin-right: 0px;
    }
    .promocode_input {
      position: absolute;
      left: 107px;
      box-shadow: 2px 2px 5px !important;
      top: 15px;
    }

    .wrapper_booking_button button {
      font-size: 13px;
    }

    .date_box, .selectric {
      background: #f5f5f5;
    }
  }

  .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper {
    margin-right: 5px;
  }

  .room_list_wrapper {
    margin-left: 0px;
  }

  .room .room_title, .room .adults_selector {
    margin-right: 5px;
  }

  .room_title {
    padding-right: 10px;
  }

  .room_list_wrapper {
    margin-right: 10px;
  }

  .promocode_text {
    margin-right: 31px;
  }

  .selectric, .date_box {
    background: white;
  }
}

