/*======== Booking Widget =======*/
div#wrapper_booking {
  position: absolute;
  height: auto;
  top: 25px;
  left: 0;
  right: 0;
  z-index: 35;
}

.booking_widget {
  position: absolute;
  left: 20px;
}

#full-booking-engine-html-5 {
  width: 299px;
}

#full-booking-engine-html-5 form.booking_form {
  background: white;
}

.booking_form_title .best_price {
  display: none;
  color: white;
  font-size: 16px;
  padding: 20px;
  font-weight: 600;
  text-align: center;
}

.promocode_header p.first_offer_name {
  color: white;
  margin-bottom: 0;
  text-transform: uppercase;
  font-size: 15px;
}

.promocode_header p.second_offer_name {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: lighter;
  color: rgba(255, 255, 255, 0.5) !important;
}

.booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
  border: 0;
}

.booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
  border-bottom: 0 !important;
}

.selectric {
  height: 38px;
  background: transparent;
}

.booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
  font-size: 11px !important;
  padding: 0;
}

.wrapper-new-web-support .web_support_number, .web_support_label_1 {
  line-height: 15px !important;
}

.wrapper-new-web-support.booking_form_title {
  background: gray !important;
}

#data #full-booking-engine-html-5 {
  margin-top: 0 !important;
}

.date_box.entry_date, .date_box.departure_date {
  margin-top: 6px;
  background: url("/img/corae/calendario-widget.png") no-repeat center right;
}

.selectricWrapper .selectric {
  margin-top: 0px;
}

#slider_inner_container #full-booking-engine-html-5 {
  margin-top: -17px !important;
}

.promocode_text {
  display: none;
}

.promocode_wrapper {
  text-align: center;

  .promocode_input {
    text-align: center;
  }
}

.date_year {
  display: none;
}

/*==== Header widget booking ===*/
.booking_header_discount {
  margin-right: 10px !important;
  display: inline-block !important;
  float: left !important;
  margin-top: 5px;
}

.promocode_header p.second_offer_name, .first_offer_name {
  display: inline-block;
  color: white;
}

.center_block {
  display: table;
  margin: auto;
  width: 250px;
}

#full-booking-engine-html-5 .booking_form_title {
  background: #76a8d9;
  background: #76a8d9;
}

/*==== Booking widget ===*/
.booking_widget {
  position: absolute;
}

button.submit_button {
  color: white;
  background: #76a8d9;
}

.bottom_html_promocode {
  margin: 10px;
  border: 1px solid #76a8d9;
  color: #76a8d9;
  text-align: center;
  padding: 10px 0;
  font-size: 13px;
  background: #f7f7f7;
  -webkit-box-shadow: 0 0 5px black;
  -moz-box-shadow: 0 0 5px black;
  box-shadow: 0 0 5px black;
  position: relative;

  b {
    font-weight: bolder;
  }

  &:before {
    content: "";
    position: absolute;
    z-index: 300;
    top: -10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 10px;
    border-color: #76a8d9 transparent transparent transparent;
    -ms-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  &:after {
    content: "";
    position: absolute;
    z-index: 300;
    top: -8px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 8px 0 8px;
    border-color: white transparent transparent transparent;
    -ms-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}