{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}


<section id="slider_container">
{% if inicio %}
    {{ revolution_slider|safe }}
    <div id="wrapper_booking" class="container12">

            <div class="booking_widget">
                {{ booking_engine }}
            </div>

    </div>
{% else %}
    <div id="wrapper_booking" class="container12">
        <div class="boking_widget_inline">
            {{ booking_engine_2 }}
        </div>
    </div>
{% endif %}

</section>

{% endblock %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% endblock %}

<section class="slider">
    <div class="slider-bottom container12">
        <ul class="bxslider">
        {% for banner in banner_slider %}
            <li class="banner-slider column4">
                <a href="{{banner.linkUrl}}"><h4>{{banner.title|safe}}</h4></a>
                <a href="{{banner.linkUrl}}"><img src="{{banner.servingUrl|safe}}" alt="{{banner.title|safe}}" title="{{banner.title|safe}}" /></a>
            </li>
        {% endfor %}
        </ul>
    </div>
</section>


{% include "footer.html" %}


{% endblock %}