<div id="main-sections">
	<ul id="main-sections-inner" class="container">
		{% for section in main_sections %}
		<div class="main-section-div-wrapper" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}
					<a>{{ section.title|safe }}</a>
            {% else %}
                <a href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}" {% if section.sectionName|safe == 'villas coral premium' or section.sectionName|safe == 'villas coral beach' %}class="special_menu"{% endif %}>
                    {{ section.title|safe }}
                {% if section.sectionName|safe == 'villas coral premium' or section.sectionName|safe == 'villas coral beach' %}
                    <br><span class="subtitle_text">{{ web_oficial.subtitle|safe }}</span>
                {% endif %}
                </a>
            {% endif %}

            {% if section.subsections %}
            <ul>


                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">
                        <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                        {{ subsection.title|safe}}
                        </a>
                    </li>
                {% endfor %}
            </ul>
            {% endif %}


		</div>
		{% endfor %}
	</ul>
</div>