

<div class="wrapper-slider-and-phrase">


    <div class="slider-suite percent-double-column">

         <div class="suites_slider">
                <ul class="slides">

                    {% for banner in slider_suites %}
                        <li>
                            <div class="main_slidersuite cover-sliders" >


                                <div class="wrap-img-slidersuite" style="background: url('{{banner.servingUrl|safe}}=s1000') top center no-repeat;background-size: cover;"></div>


                                <div>

                                    <h3 class="slidersuite-title">{{ banner.title|safe }}</h3>
                                    <span class="span-underline"></span>

                                    <div class="slidersuite-descriptions">{{ banner.description|safe }}</div>

                                </div>


                            </div>
                        </li>


                    {% endfor %}


                </ul>
         </div>


    </div>





    <div class="frase-picasso percent-seconds-column  last_column">

        <div class="father-transparency cover-sliders" style="background: url('{{frase_picasso.pictures.0|safe}}') top center no-repeat;background-size: cover;">


            <div class="frase-picasso-tranparency transparency-gray">

                <h3 class="frase-picasso-title">{{ frase_picasso.subtitle|safe }}</h3>
                <span class="span-underline"></span>

                <div class="slidersuite-descriptions">{{ frase_picasso.content|safe }}</div>

            </div>


        </div>



    </div>


</div>





