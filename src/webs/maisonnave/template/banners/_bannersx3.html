{% if bannerx3 %}
    <div class="bannerx3_wrapper">
        {% for banner in bannerx3 %}
            <div class="banner effects_sass" sass_effect="slide_up_effect">
                <div class="overlay"></div>
                <div class="banner_image">
                    <img src="{{ banner.servingUrl|safe }}=s500" alt="{{ banner.altText|safe }}">
                </div>
                <div class="banner_info">
                    <h2>{{ banner.title|safe }}</h2>
                    <div class="description">{{ banner.description|safe }}</div>
                    <div class="banner_links">
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl }}" {% if 'http' in banner.linkUrl %}target=_blank {% endif %}>
                                {% if banner.btn_text %}
                                    {{ banner.btn_text|safe }}
                                {% else %}
                                    {{ T_ver_mas }}
                                {% endif %}</a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="button_promotion"
                               {% if banner.package_booking %}smart-package-button="true"{% endif %} {{ banner.smartDatasAttributes }}>{{ T_reservar }}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}