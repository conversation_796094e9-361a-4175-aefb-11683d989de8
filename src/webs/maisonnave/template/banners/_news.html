{% for item in news[1:] %}<div class="entry_widget has_transition">
    <div class="image">
        <a href="/{{ path }}/{{ item.friendlyUrl }}">
            <img src="{{ item.picture|safe }}{% if 'cdn2.paraty' in item.picture %}=s400-c{% endif %}">
        </a>
    </div><div class="content">
        <div class="date"><span>{{ item.creationDate|safe }}</span></div>
        <div class="title">{{ item.name|safe }}</div>
        <div class="tags">{% for tag in item.tag_list %}<span>{{ tag|safe }}</span>{% endfor %}</div>
    </div>
</div>
{% endfor %}
