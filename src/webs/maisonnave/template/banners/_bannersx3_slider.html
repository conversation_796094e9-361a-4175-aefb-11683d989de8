{% if bannerx3_slider %}
    <div class="bannerx3_slider_wrapper {% if bannerx3_slider|length > 3 %}owl-carousel{% endif %} effects_sass"
         sass_effect="slide_up_effect">
        {% for banner in bannerx3_slider %}
            <div class="banner">
                <div class="overlay"></div>
                <div class="banner_image">
                    <img src="{{ banner.servingUrl|safe }}=s500" alt="{{ banner.altText|safe }}">
                </div>
                <div class="banner_info">
                    <h2 class="title">{{ banner.title|safe }}</h2>
                    <div class="description">{{ banner.description|safe }}</div>
                    <div class="banner_links">
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl }}">
                                {% if banner.btn_text %}
                                    {{ banner.btn_text|safe }}
                                {% else %}
                                    {{ T_ver_mas }}
                                {% endif %}
                            </a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="button_promotion">{{ T_reservar }}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <script>
        $(window).load(function () {
            {% if bannerx3_slider|length > 3 %}
                var owl_params = {
                    loop: true,
                    nav: true,
                    dots: false,
                    items: 3,
                    margin: 30,
                    navText: ['<i class="fal fa-angle-left"></i>', '<i class="fal fa-angle-right"></i>'],
                    autoplay: true,
                    autoplayHoverPause: true
                };
                $(".bannerx3_slider_wrapper").owlCarousel(owl_params);
            {% endif %}
        });
    </script>
{% endif %}