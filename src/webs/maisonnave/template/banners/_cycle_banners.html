<div class="cycle_banners_wrapper">
    <div class="container12">
        {% for banner in cycle_banners %}
            <div class="banner effects_sass" sass_effect="slide_up_effect">
                <div class="banner_image">
                    {% if banner.gallery_pictures %}
                        <div class="gallery_room_wrapper">
                            <div class="room_slider owl-carousel">
                                {% for picture in banner.gallery_pictures %}
                                    <div class="picture_room">
                                        <a class="picture_popup" href="{{ picture.servingUrl|safe }}=s1800" {% if is_mobile %}data-fancybox="{{ banner.key|safe }}"{% else %}rel="lightbox[{{ banner.key|safe }}]"{% endif %}>
                                            <img src="{{ picture.servingUrl|safe }}=s800">
                                        </a>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <img src="{{ banner.servingUrl|safe }}=s800" alt="{{ banner.altText|safe }}">
                    {% endif %}
                </div>
                <div class="banner_info">
                    <h2>{{ banner.title|safe }}</h2>
                    <div class="description">{{ banner.description|safe }}</div>
                    <div class="read_more">{{ T_leer_mas }}</div>
                    <div class="banner_links">
                        {% if not rooms_content %}
                            {% if banner.linkUrl and not banner.btn_link %}
                                <a href="{{ banner.linkUrl }}" {% if banner.external %}target="_blank" {% endif %}>
                                    <span>{% if banner.btn_text %}{{ banner.btn_text|safe }}{% else %}
                                        {{ T_ver_mas }}{% endif %}</span>
                                </a>
                            {% endif %}
                        {% endif %}
                        {% if banner.btn_text %}
                            <a href="{{ banner.linkUrl }}" target="_blank">
                                <span>{% if banner.btn_text %}{{ banner.btn_text|safe }}{% else %}
                                    {{ T_ver_mas }}{% endif %}</span>
                            </a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="button_promotion">
                                <span>{{ T_reservar }}</span>
                            </a>
                        {% endif %}
                        {% if banner.extra_link %}
                            <a class="extra_link" target="_blank" href="{{ banner.extra_link.link|safe }}">
                                <span>{{ banner.extra_link.text|safe }}</span>
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function() {
        var owl_params = {
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            margin: 0,
            navText: ['<i class="fal fa-angle-left"></i>', '<i class="fal fa-angle-right"></i>'],
            autoplay: false
        };
        $(".room_slider").owlCarousel(owl_params);

        $(".banner .banner_info .description").each(function () {
            if ($(this).outerHeight() > 200) {
                $(this).closest(".banner_info").addClass("expanded");
            }
        })
        $(".banner .banner_info .read_more").on("click", function () {
            let banner = $(this).closest(".banner_info");
            if (!banner.hasClass("open")) {
                banner.addClass("open");
                $(this).html($.i18n._("T_leer_menos"))
            } else {
                banner.removeClass("open");
                $(this).html($.i18n._("T_leer_mas"))
            }
        });
    })
</script>