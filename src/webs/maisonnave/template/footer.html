<footer>
    <div class="footer_content container12">

        <div class="footer_legal_text_wrapper">
            {% if texto_legal %}
                <div class="legal_text">{{ texto_legal|safe }}</div>
            {% endif %}

            <div class="logo">
                <a href="{{host|safe}}/">
                    <img itemprop="logo" src="{{ logo_footer.0.servingUrl if logo_footer else logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
                </a>
            </div>

            <div class="footer_links_wrapper">
                {% for section in main_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrlInternational }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrlInternational }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
                {% for section in top_extra_links %}
                    <a href="{{ section.linkUrl }}">
                        <span>{{ section.title|safe }}</span>
                    </a>
                {% endfor %}
            </div>
        </div>

        <div class="footer_conditions_links_wrapper">
            {% for x in policies_section %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}"
                   class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>
        </div>
    </div>

    {% if extra_bottom_footer and extra_bottom_footer.content %}
        <div class="extra_bottom_footer">
            {{ extra_bottom_footer.content|safe }}
        </div>
    {% endif %}

    {% if namespace == 'maisonnave' %}
        <div class="tripadvisor_wrapper">
            <div id="TA_certificateOfExcellence631" class="TA_certificateOfExcellence"><ul id="fh9DtwPH" class="TA_links fnfyYgQRzH"><li id="R5iOoIvStZ" class="ee5f0NHAN"><a target="_blank" href="https://www.tripadvisor.es/Hotel_Review-g187520-d232829-Reviews-Maisonnave_Hotel-Pamplona_Navarra.html"><img src="https://www.tripadvisor.es/img/cdsi/img2/awards/v2/tchotel_2020_LL-14348-2.png" alt="TripAdvisor" class="widCOEImg" id="CDSWIDCOELOGO"/></a></li></ul></div><script async src="https://www.jscache.com/wejs?wtype=certificateOfExcellence&amp;uniq=631&amp;locationId=232829&amp;lang=es&amp;year=2020&amp;display_version=2" data-loadtrk onload="this.loadtrk=true"></script>
        </div>
    {% endif %}
</footer>