.banner_paralax_wrapper {
  position: relative;
  width: 100%;
  height: 75vh;
  overflow: hidden;
  background-size: cover;
  background-attachment: fixed;
  .paralax_slider {
    @include full_size;
    z-index:-1;
    .owl-item {
      height: 75vh;
      width: 100%;
      overflow: hidden;
    }
    .paralax_pic {
      width: 100%;
      height: 100%;
      background-position: center;
      background-size: cover;
      //background-attachment: fixed;
    }
  }
  .banner_content {
    @include full_size;
    z-index: 2;
    background-color: rgba(0, 0, 0, .1);

    .iframe_maps {
      width: 1200px;

      iframe {
        width: 800px;
        height: 450px;
      }
    }

    h2 {
      @include center_xy;
      width: 100%;
      color: white;
      text-align: center;
      font-size: 75px;
      font-family: $title_family;
      font-style: italic;
      font-weight: 100;

      span {
        font-weight: 400;
        font-style: normal;
        display: block;
        font-size: 50px;
        line-height: 100px;
        font-family: $title_family_2;
        text-transform: uppercase;
      }
    }
    .description {
      position: absolute;
      bottom: 0;
      color: white;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.3); /* For browsers that do not support gradients */
      background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
    }
  }
}