.banner_services_wrapper {
  position: relative;
  padding: 0 calc((100% - 1140px) / 2);
  margin-top: 50px;
  h2.title_background {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    color: rgba($corporate_2, 0.6);
    font-size: 120px;
    letter-spacing: 5px;
    text-align: center;
    font-family: $title_family;
    font-weight: 700;

    &.empty_services {
      position: relative;
    }
  }
  .desc {
    padding: 0 200px 75px;
    text-align: center;
    color: $black;
    font-size: 20px;
    font-weight: 300;
    font-family: $body_family;
  }
  .services {
    position: relative;
    text-align: center;
    padding: 140px 0 30px;
    .service {
      display: inline-block;
      vertical-align: top;
      width: 200px;
      color: $black;
      a#open_description {
        color: $black;
      }
      span {
        display: block;
        width: 100%;
        padding: 40px 0 0;
        text-align: center;
        font-size: 14px;
        font-family: $body_family;
      }
      big {
        font-weight: 700;
        display: block;
      }
      i.fa {
        position: relative;
        display: block;
        margin: auto;
        text-align: center;
        font-size: 50px;
        color: white;
        background-color: $corporate_2;
        width: 100px;
        height: 100px;
        margin-bottom: 2px;
        border-radius: 50%;
        -webkit-animation-name: rotate-border;
        -webkit-animation-duration: 5s;
        -webkit-animation-delay: 2s;
        -webkit-animation-iteration-count: infinite;
        -webkit-animation-direction: normal;
        -webkit-animation-timing-function: ease-in-out;
        animation-name: rotate-border;
        animation-duration: 5s;
        animation-delay: 2s;
        animation-iteration-count: infinite;
        animation-direction: normal;
        animation-timing-function: ease-in-out;
        @include transition(all, .3s);
        &:before {
          @include center_xy;
        }
        &:after {
          content: '';
          @include center_xy;
          width: 130px;
          height: 130px;
          border-radius: 50%;
          border: 2px solid $corporate_2;
          @include transition(all, .3s);
        }
      }
      &:hover {
        i.fa {
          margin-bottom: 0;
          &:after {
            border: 3px solid $corporate_1 !important;
          }
        }
      }
      &:nth-child(1) i.fa {
        -webkit-animation-delay: 1.8s;
        animation-delay: 1.8s;
      }
      &:nth-child(2) i.fa {
        -webkit-animation-delay: 2s;
        animation-delay: 2s;
      }
      &:nth-child(3) i.fa {
        -webkit-animation-delay: 2.2s;
        animation-delay: 2.2s;
      }
      &:nth-child(4) i.fa {
        -webkit-animation-delay: 2.4s;
        animation-delay: 2.4s;
      }
    }
  }
}


@-webkit-keyframes rotate-border {
  0%, 10% {
    -webkit-transform: translate(0,-5px);
    -moz-transform: translate(0,-5px);
    -ms-transform: translate(0,-5px);
    -o-transform: translate(0,-5px);
    transform: translate(0,-5px);
  }
  10%, 50% {
    -webkit-transform: translate(0,0);
    -moz-transform: translate(0,0);
    -ms-transform: translate(0,0);
    -o-transform: translate(0,0);
    transform: translate(0,0);
  }
  50%, 100% {
    -webkit-transform: translate(0,0);
    -moz-transform: translate(0,0);
    -ms-transform: translate(0,0);
    -o-transform: translate(0,0);
    transform: translate(0,0);
  }
}
@keyframes rotate-border {
  0%, 10% {
    -webkit-transform: translate(0,-5px);
    -moz-transform: translate(0,-5px);
    -ms-transform: translate(0,-5px);
    -o-transform: translate(0,-5px);
    transform: translate(0,-5px);
  }
  10%, 50% {
    -webkit-transform: translate(0,0);
    -moz-transform: translate(0,0);
    -ms-transform: translate(0,0);
    -o-transform: translate(0,0);
    transform: translate(0,0);
  }
  50%, 100% {
    -webkit-transform: translate(0,0);
    -moz-transform: translate(0,0);
    -ms-transform: translate(0,0);
    -o-transform: translate(0,0);
    transform: translate(0,0);
  }
}
