.bannerx3_slider_wrapper,
.bannerx3_wrapper {
  width: 1100px;
  margin: auto;
  margin-bottom: 40px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20px;
  
  
  
  .banner {
    background: red;
    position: relative;
    height: 510px;
    width: 100%!important;
    overflow: hidden;
    
    .overlay {
      @include full_size;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 1;
      @include transition(background-color, .6s);
    }
    .banner_image {
      @include full_size;
      overflow: hidden;
      img {
        @include center_xy;
        min-height: 100%;
        min-width: 100%;
        width: auto;
        max-width: none;
      }
    }

    .banner_info {
      position: absolute;
      bottom: auto;
      left: 0;
      right: 0;
      top: 350px;
      height: 510px;
      box-sizing: border-box;
      padding: 50px 20px;
      z-index: 2;
      @include transition(top, .6s);
      h2 {
        text-align: center;
        font-size: 30px;
        color: white;
        margin-bottom: 30px;
        font-family: $title_family;
      }
      .description {
        @include center_xy;
        text-align: center;
        font-weight: 400;
        font-size: 13px;
        line-height: 25px;
        letter-spacing: 1px;
        //font-family: "E<PERSON> Garamond", serif;
        color: white;
        width: 245px;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 180px;
        -webkit-line-clamp: 7;
        -webkit-box-orient: vertical;
      }
      .banner_links {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 0 50px 50px;
        a {
          border: 1px solid white;
          text-transform: uppercase;
          color: white;
          display: block;
          margin-bottom: 5px;
          font-weight: 300;
          font-size: 14px;
          padding: 10px;
          text-align: center;
          @include transition(all, .4s);

          &.button_promotion {
            color: white;
            background-color: $corporate_1;
            margin-bottom: 0;
            border: 0;

            &:hover {
              background-color: rgba($corporate_1, .6);
              border-color: $corporate_1;
            }
          }

          &:hover {
            background-color: $corporate_2;
            border-color: $corporate_2;
            color: white;
          }
        }
      }
    }
    &:hover {
      .overlay {
        background-color: rgba(0, 0, 0, .8);
      }
      .banner_image {
        img {
          -webkit-filter: blur(5px);
          filter: blur(5px);
        }
      }
      .banner_info {
        top: 0;
      }
    }

    @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
      .overlay {
        background-color: rgba(0, 0, 0, .4);
      }
      .banner_image {
        img {
          -webkit-filter: blur(3px);
          filter: blur(3px);
        }
      }
      .banner_info {
        top: 0;
      }
    }
  }
  &.owl-carousel .owl-nav .owl-next,
  &.owl-carousel .owl-nav .owl-prev {
    @include center_y;
    font-size: 50px;
    color: lighten($corporate_2, 30%);
  }
  &.owl-carousel .owl-nav .owl-next {
    right: -50px;
  }
  &.owl-carousel .owl-nav .owl-prev {
    left: -50px;
  }
}

.bannerx3_wrapper {
  text-align: center;
  margin-bottom: 30px;
  .banner {
    display: inline-block;
    width: calc(100% / 3 - 23px);
    overflow: hidden;
    margin: 10px;
  }
}