.datepicker_wrapper_element {
  .header_datepicker {
    background-color: $corporate_1;
  }
}

#full_wrapper_booking {
  position: absolute;
  padding: 0;
  width: 100%;
  min-width: 1140px;
  z-index: 1000;
  bottom: 150px;

  /*======== Booking Widget =======*/

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    width: auto;
    display: table;
    margin: auto !important;
    position: relative;
    background: rgba(white, .4);
    padding: 8px !important;

    .promocode_header {
      display: none;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    position: relative;
    background: white;
  }

  .dates_selector_personalized {
    display: none;
  }

  .stay_selection {
    display: inline-block !important;
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: black;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    width: 50% !important;
    height: auto;
    float: left;
    box-sizing: border-box;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .web_support_label_1 {
    display: inline-block;
  }

  .wrapper-new-web-support.booking_form_title {
    text-align: center;
    background: white;
    background: rgba(255, 255, 255, 0.7);
    opacity: 1;
    margin-top: 17px;
    font-size: 13px !important;
    font-weight: bold;
    position: relative;
    top: -240px;
    padding: 10px;
    color: $corporate_1;
    border-radius: 10px 10px 0 0;
    z-index: -1;
    width: 100%;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;
    background-position-x: left;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {
    background-position-x: left;
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;
    position: relative;

    &:after {
      content: '';
      width: 1px;
      height: 66px;
      background: #DDD;
      position: absolute;
      top: 10px;
      right: 0;
      @-moz-document url-prefix() {
        height: 66px;
        position: absolute;
        top: 13px;
      }
    }


    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: auto;
      height: auto;
      padding: 16px 20px 0;

      label {
        display: none;
      }

      .date_box {
        background-image: none !important;
        margin-top: 0;

        .date_day {
          text-align: center;
          margin-top: 0;
          font-family: $title_family_2;

          .month, .year {
            display: inline-block;
            text-transform: uppercase;
            font-size: 14px;
          }

          .year {
            margin-left: 4px;
          }

          .day {
            display: block;
            font-size: 24px;
            font-weight: 400;
            color: #796A57;
          }
        }
      }
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    display: inline-block;
    vertical-align: top;
    background: transparent;
    width: auto;
    height: auto;
    padding: 20px 10px 0;
    position: relative;

    &:after {
      content: '';
      width: 1px;
      height: 66px;
      background: #DDD;
      position: absolute;
      top: 10px;
      right: 0;
      @-moz-document url-prefix() {
        height: 66px;
        position: absolute;
        top: 13px;
      }
    }

    @-moz-document url-prefix() {
      padding: 15px 20px 23px;
    }
    label.rooms_label {
      font-family: $title_family_2;
      font-size: 14px;
      margin-bottom: 12px;
      display: block;
      margin-right: 10px;
      text-transform: uppercase;
      font-weight: lighter;
    }
    .rooms_number {
      padding-left: 0;
      box-sizing: border-box;
      background: transparent;


      .selectric {
        height: auto;
        font-family: $title_family_2;

        .label {
          line-height: 16px;
          font-weight: 400;
          color: #796A57;
          text-align: center;
          font-size: 24px;
        }

        .button {
          display: none;
        }
      }
    }
  }

  .room_list_wrapper {
    display: none;
    vertical-align: top;
    float: left;
    background: white;
    width: 250px;
    position: absolute;
    left: auto;
    right: 300px;
    top: 90px;
    padding: 5px;
    background: rgba($corporate_1, 0.8);

    &.room_list_wrapper_up {
      top: auto;
      bottom: 187px;
    }

    .room {
      background: white;
      height: 55px;

      &.room1, &.room2, &.room3 {
        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 55px;
          border-left: 1px solid #d3d3d3;
          width: 50% !important;
        }
        .adults_selector {
          border-right-width: 0;
        }

        .selectric {
          height: 20px;
          position: relative;
          font-size: 12px;
          color: lightgrey;
          @extend .fa-sort;
          &:before {
            @extend .far;
            @include center_y;
            right: 10px;
          }
          .label {
            line-height: 20px;
          }
          .button {
            display: none;
          }
        }
      }

      &.room3, &.room2 {
        border-bottom: 1px solid lightgray;
        height: 35px;

        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 35px;
        }
      }

      &.room3 {
        border-top: 0;
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: right;
    height: 47px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      float: left;
      display: inline-block;
      vertical-align: top;
      width: 150px;
      height: 47px;
      background: white;
      position: relative;
      padding-top: 30px;
      border-radius: 0;
      border-top: 0;
      input.promocode_input {
        margin-top: 0;
        background: transparent;
        text-align: center;
        letter-spacing: 1px;
        font-weight: 100;
        text-transform: uppercase;
      }
    }

    .submit_button {
      position: relative;
      bottom: 0;
      left: 0;
      width: 210px;
      height: 84px;
      @-moz-document url-prefix() {
        height: 86px;
      }
      display: block;
      vertical-align: top;
      font-size: 22px;
      letter-spacing: 2px;
      background: $corporate_1;
      color: white;
      font-weight: 500;

      span {
        position: relative;
        z-index: 2;
        letter-spacing: 3px;
      }
      &:hover {
        &:before {
          width: 100%;
        }
      }
      &:before {
        content: '';
        @include full_size;
        width: 0;
        background-color: darken($corporate_2, 10%);
        @include transition(width, .6s);
      }
    }
  }

}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
div.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 140px;
  height: auto;
  padding: 19.5px 20px 5px;
  @-moz-document url-prefix() {
    padding: 15px 20px 10px;
  }
  box-sizing: border-box;
  cursor: pointer;
  background: transparent;
  position: relative;
  margin-left: 10px;

  &:lang(es) {
    margin-left: 0;
  }

  & > label {
    font-size: 14px;
    display: block;
    font-weight: lighter;
    font-family: $title_family_2;
    text-align: center;
    text-transform: uppercase;
  }
  span.placeholder_text {
    display: block;
    font-size: 20px;
    font-weight: lighter;
    line-height: 39px;
    box-sizing: border-box;
    background: transparent;
    background-position-y: 0;
    text-align: center;
    font-family: $title_family_2;

    .guest_adults {
      font-size: 24px;
      margin-right: 4px;
      font-weight: bold;
      color: #796A57;
    }
    .guest_kids {
      margin-left: 10px;
      font-weight: bold;
    }
    &.selected_value {
      color: #585d63;
      font-size: 21px;
      padding-top: 3px;
      background-position-y: 8px;
      font-weight: 600;
    }
  }

  b.button {
    display: none;
  }
  &:after {
    content: '';
    width: 1px;
    height: 66px;
    background: #DDD;
    position: absolute;
    top: 10px;
    right: 0;
    @-moz-document url-prefix() {
      height: 66px;
      position: absolute;
      top: 13px;
    }
  }
}

#booking label {
  cursor: pointer;
}

.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

#booking .room_list label {
  display: block !important;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 154px !important;
      margin-left: -23px !important;
      top: 35px;
      &.rooms_number_up {
        top: -175px !important;
      }
    }
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto !important;
  width: 100%;
  background-color: rgba($corporate_2, 0.9);
}

body .modify_reservation_widget #motor_reserva #envio input {
  margin-left: 10px;
  width: 247px;

}