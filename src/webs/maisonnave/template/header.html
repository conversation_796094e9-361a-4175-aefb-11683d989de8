
<header>
    <div id="wrapper-header" class="container12">
        <div class="phone_social_wrapper">
            {% if phones %}
                <div class="phone_wrapper"><i class="fa icon-headset fa-fw" aria-hidden="true"></i><span>{{ phones.0 }}</span>
                </div>
            {% endif %}
            {% if email_booking %}
            <div class="email_booking">
                <span>{{ email_booking|safe }}</span>
            </div>
            {% endif %}
        </div>

        <div id="logoDiv" class="">
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <div class="top_extra_links">
            <div id="social">
                {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/{{twitter_id}}" target="_blank">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>
            {% for x in top_extra_links %}
                <a href="{{ x.linkUrl }}">{{ x.title|safe }}</a>
            {% endfor %}
        </div>
        <div id="lang">
                <span id="selected-language" class="selected-language2">
                    <i class="far fa-globe fa-fw"></i>
                    {% for key, value in language_codes.items() %}{% if  key == language %}{{ value|upper }}{% endif %}{% endfor %}
                    <i class="fal fa-angle-down arrow fa-fw"></i>
                </span>
                <ul id="language-selector-options" class="language-selector-options2">
                    {% for key, value in language_codes.items() %}
                        <li class="language-option-flag">
                            <a hreflang="{{ key }}" href="{{ hostWithoutLanguage|safe }}/{{ key }}/"><span>{{ value|upper }}</span></a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
    </div>
    <nav id="main_menu">
        <div id="mainMenuDiv" class="container12">
            {% include "main_div.html" %}
        </div>
    </nav>
</header>

<div id="top-sections">
    {% for section in top_sections %}
        <a href="{{ host|safe }}/{{ section.friendlyUrlInternational }}">
            <i class="fa fas {{ loop.cycle("icon-booking", "fa-map-marker-alt") }} fa-fw"></i>
            <span>{{ section.title|safe }}</span>
        </a>
    {% endfor %}
</div>