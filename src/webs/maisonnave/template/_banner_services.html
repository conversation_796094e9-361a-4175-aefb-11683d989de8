<div class="banner_services_wrapper">
    {% if banner_services_sec.subtitle %}<h2 class="title_background {% if not banner_services %}empty_services{% endif %}">{{ banner_services_sec.subtitle|safe }}</h2>{% endif %}
    {% if banner_services %}<div class="services {% if is_mobile %}owl-carousel{% endif %}">
        {% for service in banner_services %}
            <div class="service">
                {% if service.popup_description %}
                    <a id="open_description" href="#icondescription_{{ loop.index }}">
                        <div style="display: none">
                            <div id="icondescription_{{ loop.index }}" style="max-width: 1140px; padding:15px;">
                                {{ service.popup_description|striptags|safe }}
                            </div>
                        </div>
                {% endif %}
                {% if service.title %}
                    <i class="fa {{ service.title|safe }}"><span class="overlay"></span></i>
                {% else %}
                    <img src="{{ service.servingUrl|safe }}" alt="">
                {% endif %}
                {% if service.description %}<span>{{ service.description|safe }}</span>{% endif %}
                {% if service.popup_description %}
                    </a>
                {% endif %}
            </div>
        {% endfor %}
    </div>{% endif %}
    {% if banner_services_sec.content %}<div class="desc">{{ banner_services_sec.content|safe }}</div>{% endif %}
</div>

{% if not is_mobile and not user_isIpad %}
    <script>
    $(window).load(function () {
        function banner_services_fx() {
            $(".banner_services_wrapper .service:nth-child(1)").addnimation({parent:$(".banner_services_wrapper"),class:"bounceInUp"});
            setTimeout(function() {$(".banner_services_wrapper .service:nth-child(2)").addnimation({parent:$(".banner_services_wrapper"),class:"bounceInUp"})},200);
            setTimeout(function() {$(".banner_services_wrapper .service:nth-child(3)").addnimation({parent:$(".banner_services_wrapper"),class:"bounceInUp"})},400);
            setTimeout(function() {$(".banner_services_wrapper .service:nth-child(4)").addnimation({parent:$(".banner_services_wrapper"),class:"bounceInUp"})},600);
            setTimeout(function() {$(".banner_services_wrapper .service:nth-child(5)").addnimation({parent:$(".banner_services_wrapper"),class:"bounceInUp"})},800);
        }
        banner_services_fx();
        $(window).scroll(banner_services_fx);
    });

    $(document).ready(function () {
        $("#open_description").fancybox();
    });

    </script>
{% endif %}

{% if is_mobile %}
    <script>
        $(window).load(function () {
            owl_params = {
                loop: true,
                nav: true,
                dots: false,
                items: 1,
                navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                margin: 0,
                autoplay: true
            };

            $(".services").owlCarousel(owl_params);
        });
    </script>
{% endif %}