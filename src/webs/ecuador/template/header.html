<header>
    <div id="wrapper-header">

        <div class="container12">
            <div id="logoDiv">
                <a href="{{ host|safe }}/">
                    <img src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
                </a>
            </div>
        </div>


        <div id="right-header">


            <div id="top-menu-wrapper-1">
                <div class="container12 top-menu-wrapper-1-inside">


                    <div id="mini-top-menu">
                        {% for section in top_sections %}
                            <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                                <span>{{ section.title|safe }}</span>
                            </a>

                        {% endfor %}
                    </div>


                    <div id="social">
                        {% if facebook_id %}
                            <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank"> <img
                                    src="/img/{{ base_web }}/social/facebook.png?v=1" width="25" height="25"> </a>
                        {% endif %}
                        {% if twitter_id %}
                            <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank"> <img
                                    src="/img/{{ base_web }}/social/twitter.png?v=1" width="25" height="25"> </a>
                        {% endif %}
                        {% if google_plus_id %}
                            <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank"> <img
                                    src="/img/{{ base_web }}/social/googleplus.png" width="25" height="25"> </a>
                        {% endif %}
                        {% if youtube_id %}
                            <a href="https://www.youtube.com/channel/{{ youtube_id }}" target="_blank"> <img
                                    src="/img/{{ base_web }}/social/youtube.png?v=1" width="25" height="25"> </a>
                        {% endif %}
                        {% if flickr_id %}
                            <a href="http://www.flickr.com/photos/{{ flickr_id }}/" target="_blank"> <img
                                    src="/img/{{ base_web }}/social/flickr.png?v=1" width="25" height="25"> </a>
                        {% endif %}
                    </div>

                    <div id="ticksContainer">

                        <div class="ticks {{ language }}" id="tick1">
                            {{ web_oficial_txt.subtitle|safe }}
                        </div>
                        <div class="ticks {{ language }}" id="tick2">
                            {{ T_sin_gastos }}
                        </div>
                        <div class="ticks {{ language }}" id="tick3">
                            {{ T_reserva_segura }}
                        </div>

                    </div>


                </div>
            </div>

            <div class="top-menu-wrapper-2-container">
                <div id="top-menu-wrapper-2" class="container12">


                    <div id="lang">
                        <span id="selected-language">{{ language_selected }}</span>
                        <span class="arrow"></span>

                        <ul id="language-selector-options">
                            {% for key, language in language_codes.items %}
                                <li class="language-option-flag">
                                    <a href="{% if key == 'es' %}/{% else %}{{ hostWithoutLanguage|safe }}/{{ key }}/{% endif %}">{{ language }}</a>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>


                    <div id="web_oficial">
                        {{ web_oficial_txt.content|safe }}
                    </div>


                    <div id="profesionales_wrapper">
                         <a href="#" id="profesionales-link" class="txt_negrita">{{ T_zona_profesional }}</a>
                    </div>

                    <div class="free_wifi">{{ T_wifi_gratuito }}</div>

                     <div id="login_profesionales"  style="display:none">

                        <span class="label_password">{{ introduzca_pass|safe  }}</span>

                        <form id="form_agencias" onsubmit="event.preventDefault();loginagencia();return false;">

                            <label>{{ T_password }}</label>
                            <input type="password" name="agency_password" id="agency_password">


                            <input type="submit" style="position: absolute; left: -9999px; width: 1px; height: 1px;"/>

                            <div class="links_profesionales" style="display: none"></div>
                        </form>

                    </div>





                </div>
            </div>


            <nav id="main_menu">
                <div id="mainMenuDiv" class="container12">
                    {% include "main_div.html" %}
                </div>

            </nav>

        </div>
    </div>


</header>