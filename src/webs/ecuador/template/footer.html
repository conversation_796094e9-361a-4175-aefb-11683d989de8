<footer>


    <div class="wrapper_footer_columns container12">


        <div class="footer_column last">
            <div class="description_wrapper_footer">
                <h3 class="footer_column_title">{{ newsletter_info.subtitle|safe }}</h3>

                <h2 id="title_newsletter">{{ newsletter_info.title|safe }}</h2>

                <div class="footer-newsletter-slogan"> {{ newsletter_info.content|safe }} </div>

                <div id="footer_column_description">{{ newsletter|safe }}</div>
            </div>
        </div>



         <div class="footer_column block-0">
            <div class="description_wrapper_footer">
                 {% if footer_columns.0.servingUrl %}
                    <img src="{{ footer_columns.0.servingUrl }}">
                {% endif %}

                <h3 class="footer_column_title">{{ footer_columns.0.title|safe }}</h3>

                <div id="footer_column_description">{{ footer_columns.0.description|safe }}</div>
            </div>
        </div>


        <div class="footer_column block-1">
         {% if footer_columns.1.linkUrl  %}<a href="{{ footer_columns.1.linkUrl }}"> {% endif %}
                {% if footer_columns.1.servingUrl %}

                    <img src="{{ footer_columns.1.servingUrl }}">

                {% endif %}


                <h3 class="footer_column_title news">{{ footer_columns.1.title|safe }}</h3>

                <div id="footer_column_description">{{ footer_columns.1.description|safe }}</div>
          {% if footer_columns.1.linkUrl  %}</a>{% endif %}
        </div>


    </div>

    <div class="footer-bottom">
        <div class="full-copyright container12">

            <div class="column12">
                <div id="google_plus_one">
                    <div class="g-plusone"></div>
                </div>
                <div id="facebook_like">
                    <div id="fb-root">
	 </div>
<script src="//connect.facebook.net/es_ES/all.js#appId=128897243865016&amp;xfbml=1"></script><div>
	<fb:like font="" href="" layout="button_count" send="false" show_faces="false" width="110"></fb:like></div>
                </div>
            </div>

            {% if texto_legal %}
                <div id="div-txt-copyright" class="footer-copyright column8">
                    {{ texto_legal|safe }}
                </div>
            {% endif %}

            <div class="footer-copyright column4">

                {% for x in policies_section %}
                    {% if x.custom_link %}
                        <a href="{{ x.custom_link }}">{{ x.title|safe }}</a> |
                    {% else %}
                        <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                    {% endif %}
                {% endfor %}
                <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html" title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
                <a target="_blank" href="/rss.xml">RSS</a>

            </div>
        </div>
    </div>

</footer>