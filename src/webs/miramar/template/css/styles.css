/* line 1, ../sass/_booking_engine.scss */
#motor_reserva {
  color: #5a5a5a;
  font-size: 12px;
  padding: 0px !important;
  border: 1px solid #bebebe;
}

/* line 7, ../sass/_booking_engine.scss */
#fecha_entrada input, #fecha_salida input {
  background: white url(/img/mirar/date_icon.png) no-repeat 75px !important;
  padding-left: 4px;
}

/* line 11, ../sass/_booking_engine.scss */
.colocar_fechas {
  width: 200px !important;
  margin-top: 10px !important;
}

/* line 15, ../sass/_booking_engine.scss */
#contenedor_fechas,
#contenedor_habitaciones,
#contenedor_opciones {
  padding-left: 10px;
  padding-right: 10px;
}

/* line 22, ../sass/_booking_engine.scss */
#titulo_fecha_entrada {
  float: left;
  min-width: 105px;
}

/* line 26, ../sass/_booking_engine.scss */
#titulo_fecha_salida {
  float: left;
  min-width: 105px;
}

/* line 30, ../sass/_booking_engine.scss */
#booking_engine_title {
  padding: 10px;
}

/* line 33, ../sass/_booking_engine.scss */
#booking_title1 {
  display: none;
  text-align: center;
  font-size: 20px;
}

/* line 38, ../sass/_booking_engine.scss */
#booking_title2 {
  text-align: center;
  font-size: 26px;
}

/* line 42, ../sass/_booking_engine.scss */
#best_price {
  text-align: center;
  font-size: 10px;
}

/* line 46, ../sass/_booking_engine.scss */
#info_ninos {
  font-size: 8px !important;
  top: 8px;
  left: 150px;
}

/* line 51, ../sass/_booking_engine.scss */
#search-button {
  background-color: #5e89be;
  color: white;
  margin: auto !important;
  width: 150px;
  text-transform: uppercase;
}

/* line 58, ../sass/_booking_engine.scss */
#search-button:hover {
  background-color: #5e75be;
}

/* line 61, ../sass/_booking_engine.scss */
.spinner {
  text-align: center;
  height: 30px;
}

/* line 65, ../sass/_booking_engine.scss */
#envio input {
  width: 150px !important;
  margin: 5px auto 15px !important;
}

/* line 69, ../sass/_booking_engine.scss */
#envio {
  text-align: center;
  padding-bottom: 10px;
}

/* line 1, ../sass/_booking_widget_modal.scss */
.fancybox-wrap {
  width: auto !important;
}

/* line 5, ../sass/_booking_widget_modal.scss */
.fancybox-outer {
  padding: 0 !important;
}

/* line 9, ../sass/_booking_widget_modal.scss */
.fancybox-inner {
  overflow: hidden !important;
}

/* line 13, ../sass/_booking_widget_modal.scss */
.booking-widget {
  background-color: white;
}

/* line 17, ../sass/_booking_widget_modal.scss */
.booking-widget fieldset {
  margin: 8px 0 0;
}

/* line 21, ../sass/_booking_widget_modal.scss */
.booking-widget label {
  color: #5a5a5a;
  display: block;
  font-size: 12px;
}

/* line 27, ../sass/_booking_widget_modal.scss */
.booking-widget .numero_habitacion {
  display: none;
}

/* line 31, ../sass/_booking_widget_modal.scss */
.modal-form {
  padding: 12px;
}

/* line 35, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title1 {
  display: none;
}

/* line 39, ../sass/_booking_widget_modal.scss */
.modal-form .booking_title2 {
  text-align: center;
  background-color: #5e89be;
  padding-top: 10px;
  font-size: 30px;
  color: white;
}

/* line 47, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  text-align: center;
  background-color: #5e89be;
  padding-bottom: 10px;
  color: white;
}

/* line 54, ../sass/_booking_widget_modal.scss */
.modal-form .best_price {
  margin-bottom: 20px;
}

/* line 58, ../sass/_booking_widget_modal.scss */
.modal-form #selector_hotel {
  width: 100%;
}

/* line 62, ../sass/_booking_widget_modal.scss */
.modal-form #hotel_destino {
  width: 100%;
}

/* line 66, ../sass/_booking_widget_modal.scss */
.modal-form .colocar_fechas {
  float: left;
}

/* line 70, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_salida {
  padding-top: 10px;
  clear: both;
}

/* line 75, ../sass/_booking_widget_modal.scss */
.modal-form .fecha_entrada input, .modal-form .fecha_salida input {
  height: 18px;
  width: 91px;
  border: 1px solid #5a5a5a;
  border-radius: 4px;
  cursor: pointer;
  background: #f0f0f0 url(/img/mirar/date_icon.png) no-repeat 75px;
  float: right;
}

/* line 85, ../sass/_booking_widget_modal.scss */
.modal-form .contador_noches {
  display: none;
}

/* line 89, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones {
  margin-top: 10px;
  margin-bottom: 10px;
}

/* line 93, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones select {
  width: 95px !important;
  float: right;
}

/* line 97, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_habitaciones label {
  float: left;
  width: 108px;
}

/* line 102, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones {
  margin-bottom: 10px;
}

/* line 106, ../sass/_booking_widget_modal.scss */
.modal-form .contenedor_opciones select {
  float: right;
  width: 92px;
}

/* line 111, ../sass/_booking_widget_modal.scss */
.modal-form .hab1 {
  position: relative;
}

/* line 115, ../sass/_booking_widget_modal.scss */
.modal-form .hab2, .modal-form .hab3 {
  display: none;
}

/* line 119, ../sass/_booking_widget_modal.scss */
.modal-form .numero_habitacion {
  display: none;
}

/* line 123, ../sass/_booking_widget_modal.scss */
.modal-form .adultos {
  float: left;
  margin: 0 15px 0 0;
}

/* line 128, ../sass/_booking_widget_modal.scss */
.modal-form .ninos {
  float: right;
}

/* line 132, ../sass/_booking_widget_modal.scss */
.modal-form .info_ninos {
  position: absolute;
  line-height: 10px;
  text-align: center;
  top: 4px !important;
  left: 155px !important;
  font-size: 9px !important;
}

/* line 141, ../sass/_booking_widget_modal.scss */
.modal-form .envio {
  min-height: 100px;
}

/* line 145, ../sass/_booking_widget_modal.scss */
.modal-form .envio input {
  margin: 0 9px 0 0;
  color: black;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #464646;
  width: 100% !important;
}

/* line 154, ../sass/_booking_widget_modal.scss */
.modal-form .envio button {
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  cursor: pointer;
  overflow: visible;
  font-size: 24px;
  display: block;
  margin: 30px auto 0;
  background-color: #5e89be;
  color: white;
}

/* line 168, ../sass/_booking_widget_modal.scss */
.modal-form .envio button:hover {
  background: #5e75be;
}

/* line 172, ../sass/_booking_widget_modal.scss */
.modal-form .spinner {
  top: 70px !important;
  left: 120px !important;
}

/* ----- FOR DEVELOPMENT ONLY ----- */
/* line 3, ../sass/_template_specific.scss */
body {
  line-height: 24px;
  font-family: 'Roboto', arial;
  color: #5a5a5a;
  background: url("/img/mirar/fondo_contenido.png");
}

/* line 9, ../sass/_template_specific.scss */
strong {
  font-weight: bold;
}

/* line 12, ../sass/_template_specific.scss */
#ui-datepicker-div {
  font-size: 12px;
  z-index: 1200;
}

/* line 16, ../sass/_template_specific.scss */
a {
  text-decoration: none;
}

/* line 20, ../sass/_template_specific.scss */
#booking {
  top: 28px !important;
}

/* line 24, ../sass/_template_specific.scss */
.wrapper-old-web-support {
  color: black;
}

/************* header *************/
/* line 31, ../sass/_template_specific.scss */
#logoDiv {
  margin-top: 7px;
  margin-left: -5px;
}

/* line 35, ../sass/_template_specific.scss */
header {
  border-bottom: 4px solid #5e89be;
  height: 120px;
  background-color: white;
}

/* line 40, ../sass/_template_specific.scss */
#wrapper_header {
  position: relative;
  height: 70px;
}

/* line 44, ../sass/_template_specific.scss */
#languageDiv {
  float: right;
  font-size: 12px;
  margin: 5px 10px;
  text-transform: uppercase;
}

/* line 50, ../sass/_template_specific.scss */
#social {
  float: right;
  margin: 5px 10px;
}

/* line 54, ../sass/_template_specific.scss */
#social a {
  text-decoration: none;
  color: #5a5a5a;
}

/* line 58, ../sass/_template_specific.scss */
#topMenuDiv {
  float: right;
  font-size: 12px;
  margin: 5px 25px;
}

/* line 63, ../sass/_template_specific.scss */
#topMenuDiv a, #languageDiv a {
  text-decoration: none;
  color: #5a5a5a;
}

/* line 67, ../sass/_template_specific.scss */
#languageDiv a {
  margin: 0 5px;
}

/* line 70, ../sass/_template_specific.scss */
#web_ofical {
  margin-left: 20px;
  margin-right: 0px !important;
  float: right;
}

/* line 75, ../sass/_template_specific.scss */
#ticks_container {
  float: right;
  font-size: 10px;
  text-transform: uppercase;
  margin: 1px 0 5px;
}
/* line 81, ../sass/_template_specific.scss */
#ticks_container .ticks_top {
  float: left;
  width: 102px;
  padding-left: 33px;
  text-align: left;
  line-height: 16px;
}
/* line 88, ../sass/_template_specific.scss */
#ticks_container #tick1 {
  background: url("/img/mirar/pago_icon.png") no-repeat 0;
}
/* line 91, ../sass/_template_specific.scss */
#ticks_container #tick2 {
  background: url("/img/mirar/gastos_icon.png") no-repeat 0;
}
/* line 94, ../sass/_template_specific.scss */
#ticks_container #tick3 {
  background: url("/img/mirar/segura_icon.png") no-repeat 0;
}

/* line 98, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 14px;
  z-index: 99;
  position: relative;
  top: 10px;
  clear: both;
}

/* line 105, ../sass/_template_specific.scss */
#mainMenuDiv a {
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  color: #5a5a5a;
  display: inline-block;
}

/* line 112, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  background: #5e89be;
  color: white;
}

/* line 116, ../sass/_template_specific.scss */
#section-active a {
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  background: #5e89be;
  color: white;
  display: inline-block;
}

/* line 124, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 127, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 130, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 133, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 136, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 140, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
}

/* line 143, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 149, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/************* slider y banner *************/
/* line 156, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}

/* line 159, ../sass/_template_specific.scss */
#booking {
  width: 222px;
  position: absolute;
  top: 45px;
  left: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 500;
  padding: 5px;
}

/************* Main Banners ************/
/* line 172, ../sass/_template_specific.scss */
.main_banners {
  position: relative;
  overflow: hidden;
}
/* line 176, ../sass/_template_specific.scss */
.main_banners img {
  width: 360px;
}
/* line 179, ../sass/_template_specific.scss */
.main_banners .main_banner_title {
  color: #5e89be;
  font-size: 14px;
  text-transform: uppercase;
  text-align: center;
}
/* line 185, ../sass/_template_specific.scss */
.main_banners .main_banner_description {
  color: white;
  background-color: #5e89be;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  padding: 5px 10px;
  position: absolute;
  width: 340px;
  height: 40px;
  bottom: 0;
  opacity: 0;
  -webkit-transform: translateY(40px);
  transform: translateY(40px);
  -webkit-transition: opacity 1s, -webkit-transform 1s;
  transition: opacity 1s, transform 1s;
}

/* line 203, ../sass/_template_specific.scss */
.main_banners:hover .main_banner_description {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.9;
}

/******************** Banners laterales ********************/
/* line 212, ../sass/_template_specific.scss */
#wrapper_banners_lateral {
  margin-top: 50px;
}
/* line 215, ../sass/_template_specific.scss */
#wrapper_banners_lateral .banners_lateral {
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}
/* line 220, ../sass/_template_specific.scss */
#wrapper_banners_lateral .banners_lateral #banner_lateral_title {
  color: #5e89be;
  font-size: 14px;
  text-transform: uppercase;
  text-align: center;
}
/* line 226, ../sass/_template_specific.scss */
#wrapper_banners_lateral .banners_lateral #banner_lateral_description {
  color: white;
  background-color: #5e89be;
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  padding: 5px 10px;
  position: absolute;
  height: 40px;
  bottom: 0;
  opacity: 0;
  -webkit-transform: translateY(40px);
  transform: translateY(40px);
  -webkit-transition: opacity 1s, -webkit-transform 1s;
  transition: opacity 1s, transform 1s;
}
/* line 245, ../sass/_template_specific.scss */
#wrapper_banners_lateral .banners_lateral:hover #banner_lateral_description {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.9;
}

/************* contenido *************/
/* line 255, ../sass/_template_specific.scss */
#content {
  background: url("/img/mirar/fondo_contenido.png");
}
/* line 258, ../sass/_template_specific.scss */
#content h3 {
  font-size: 18px;
  padding-bottom: 30px;
  font-weight: bold;
  color: #5e89be;
}
/* line 264, ../sass/_template_specific.scss */
#content h4 {
  font-size: 16px;
  padding: 25px 0 20px;
  font-weight: bold;
}

/* line 270, ../sass/_template_specific.scss */
#wrapper_content {
  padding: 20px 0;
  background-color: white;
  z-index: 100;
}

/* line 275, ../sass/_template_specific.scss */
.content-page-wrapper {
  padding: 0 20px 30px;
  font-size: 15px;
}

/* line 279, ../sass/_template_specific.scss */
#main_text {
  clear: left;
}
/* line 282, ../sass/_template_specific.scss */
#main_text li {
  list-style: disc;
  margin-left: 40px;
}

/************* footer *************/
/* line 291, ../sass/_template_specific.scss */
footer {
  padding: 10px 0;
  background-color: #bebebe;
}
/* line 295, ../sass/_template_specific.scss */
footer #wrapper_footer_columns {
  margin-bottom: 20px;
}
/* line 300, ../sass/_template_specific.scss */
footer #wrapper_footer_columns .footer_column .footer_column_title {
  margin: 0 0 5px;
  color: #5e89be;
  font-size: 16px;
  text-transform: uppercase;
}
/* line 306, ../sass/_template_specific.scss */
footer #wrapper_footer_columns .footer_column .footer_column_description {
  font-size: 14px;
}
/* line 309, ../sass/_template_specific.scss */
footer #wrapper_footer_columns .footer_column .footer_column_description a {
  color: #5a5a5a;
}
/* line 316, ../sass/_template_specific.scss */
footer #wrapper_footer_columns #footer_column2 img {
  float: left;
  margin-right: 15px;
}

/* line 323, ../sass/_template_specific.scss */
#suscEmail {
  width: 222px !important;
  height: 20px;
}

/* line 327, ../sass/_template_specific.scss */
#newsletter h2 {
  margin: 0 0 5px;
  color: #5e89be;
  font-size: 16px;
  text-transform: uppercase;
}

/* line 333, ../sass/_template_specific.scss */
#newsletter label {
  font-size: 14px;
}

/* line 336, ../sass/_template_specific.scss */
#newsletter input {
  margin: 15px 0;
}

/* line 339, ../sass/_template_specific.scss */
#newsletter #form-newsletter {
  margin: 0;
}

/* line 342, ../sass/_template_specific.scss */
#newsletter .bordeInput {
  border: 1px solid darkgray !important;
  width: 150px;
}

/* line 346, ../sass/_template_specific.scss */
#newsletter button {
  width: 150px;
  margin: 0 auto;
  background: #5e89be;
  color: white;
  border: none;
  text-align: center;
  font-size: 16px;
  border-radius: 5px;
  padding: 7px 3px;
  cursor: pointer;
}

/* line 358, ../sass/_template_specific.scss */
#newsletter button:hover {
  background: #5e75be;
}

/* line 361, ../sass/_template_specific.scss */
.newsletter_checkbox {
  font-size: 12px;
}
/* line 364, ../sass/_template_specific.scss */
.newsletter_checkbox a {
  text-decoration: underline;
  color: #5e89be;
}
/* line 369, ../sass/_template_specific.scss */
.newsletter_checkbox label {
  font-size: 12px !important;
}

/* line 373, ../sass/_template_specific.scss */
input#promotions {
  margin: 0;
}

/* line 376, ../sass/_template_specific.scss */
input#privacy {
  margin: 0;
}

/* line 379, ../sass/_template_specific.scss */
#footer {
  color: #5e89be;
  margin-top: 20px;
  text-align: center;
  line-height: 12px;
}
/* line 385, ../sass/_template_specific.scss */
#footer a {
  text-decoration: none;
  color: #5e89be;
}
/* line 389, ../sass/_template_specific.scss */
#footer #footer_bottom_text {
  font-size: 10px;
}

/***************** otros retoques *****************/
/* line 397, ../sass/_template_specific.scss */
button {
  width: 120px;
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  background-color: #5e89be;
  color: white;
  font-size: 18px;
  cursor: pointer;
  overflow: visible;
}

/* line 409, ../sass/_template_specific.scss */
button:hover {
  background-color: #5e75be;
}

/* line 412, ../sass/_template_specific.scss */
.contVerDetalles a {
  color: #5e89be;
}

/* line 415, ../sass/_template_specific.scss */
#workWithUs {
  display: none;
}

/* line 418, ../sass/_template_specific.scss */
#my-bookings-form .bordeInput, #contactContent .bordeInput {
  width: 250px;
  margin: 5px 0 15px;
  padding: 3px 0;
  display: block;
}

/* line 424, ../sass/_template_specific.scss */
#cancelButton {
  display: none;
}

/* line 427, ../sass/_template_specific.scss */
#my-bookings-form-fields p {
  margin-bottom: 15px;
  font-size: 15px;
}

/* line 431, ../sass/_template_specific.scss */
#my-bookings-form {
  margin-top: 25px;
}

/* line 434, ../sass/_template_specific.scss */
.promotions-wrap {
  padding: 0 30px;
}

/* line 437, ../sass/_template_specific.scss */
ul.gallery li {
  list-style: none !important;
  margin: 10px 6px !important;
  width: 125px !important;
  height: 95px !important;
  border: solid 1px #cccccc;
  background-color: #ffffff;
  padding: 3px;
}

/* line 446, ../sass/_template_specific.scss */
ul.gallery li img {
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
  height: 100%;
  background: none !important;
  border: none !important;
}

/* line 454, ../sass/_template_specific.scss */
#contact input, #contact textarea {
  border: 1px solid #787878 !important;
}

/* line 457, ../sass/_template_specific.scss */
#contact #contact-button-wrapper {
  float: left !important;
  width: auto;
}

/* line 461, ../sass/_template_specific.scss */
#contactContent h1 {
  color: #5a5a5a !important;
}

/* line 464, ../sass/_template_specific.scss */
#itemDescription {
  width: 420px;
}

/* line 467, ../sass/_template_specific.scss */
#google_plus_one {
  text-align: center;
}

/* line 470, ../sass/_template_specific.scss */
#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

/* line 474, ../sass/_template_specific.scss */
#facebook_like {
  text-align: center;
  margin-top: 10px;
}

/* line 478, ../sass/_template_specific.scss */
#facebook_like iframe {
  height: 21px;
  width: 103px;
}
