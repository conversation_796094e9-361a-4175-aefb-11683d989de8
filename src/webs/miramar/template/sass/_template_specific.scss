/* ----- FOR DEVELOPMENT ONLY ----- */

body{
  line-height: 24px;
  font-family: 'Roboto', arial;
  color: $gray-1;
  background: url("/img/mirar/fondo_contenido.png");
}
strong{
  font-weight: bold;
}
#ui-datepicker-div{
  font-size: 12px;
  z-index: 1200;
}
a{
  text-decoration: none;
}

#booking{
  top:28px!important;
}

.wrapper-old-web-support {
  color: black;
}


/************* header *************/

#logoDiv{
  margin-top: 7px;
  margin-left:-5px;
}
header{
  border-bottom: 4px solid $corporate-1;
  height: 120px;
  background-color:white;
}
#wrapper_header{
  position: relative;
  height: 70px;
}
#languageDiv{
  float: right;
  font-size: 12px;
  margin: 5px 10px;
  text-transform: uppercase;
}
#social{
  float: right;
  margin: 5px 10px;
}
#social a{
  text-decoration: none;
  color: $gray-1;
}
#topMenuDiv{
  float: right;
  font-size: 12px;
  margin: 5px 25px;
}
#topMenuDiv a, #languageDiv a{
  text-decoration: none;
  color: $gray-1;
}
#languageDiv a{
  margin: 0 5px;
}
#web_ofical{
  margin-left:20px;
  margin-right:0px !important;
  float:right;
}
#ticks_container{
  float: right;
  font-size: 10px;
  text-transform: uppercase;
  margin: 1px 0 5px;

  .ticks_top{
    float: left;
    width: 102px;
    padding-left: 33px;
    text-align: left;
    line-height: 16px;
  }
  #tick1{
    background: url('/img/mirar/pago_icon.png') no-repeat 0;
  }
  #tick2{
    background: url('/img/mirar/gastos_icon.png') no-repeat 0;
  }
  #tick3{
    background: url('/img/mirar/segura_icon.png') no-repeat 0;
  }
}
#mainMenuDiv{
  font-size: 14px;
  z-index: 99;
  position: relative;
  top: 10px;
  clear: both;
}
#mainMenuDiv a{
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  color: $gray-1;
  display: inline-block;
}
#mainMenuDiv a:hover{
  background: $corporate-1;
  color: $white;
}
#section-active a{
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  background: $corporate-1;
  color: $white;
  display: inline-block;
}
#main-sections-inner ul{
  display: none;
}
#main-sections-inner div:hover > ul{
  display: block;
}
#main-sections-inner div ul{
  position: absolute;
}
#main-sections-inner li ul{
  position: absolute;
}
#main-sections-inner div li{
  float: none;
  display: block;
}
#main-sections-inner{
  text-align:justify;
}
#main-sections-inner:after{
  content:' ';
  display:inline-block;
  width: 100%;
  height: 0;
}
#main-sections-inner > div{
  display: inline-block
}


/************* slider y banner *************/

#slider_container{
  position: relative;
}
#booking{
  width: 222px;
  position: absolute;
  top: 45px;
  left:50px;
  background-color: rgba(255,255,255,0.8);
  z-index: 500;
  padding: 5px;
}


/************* Main Banners ************/

.main_banners{
  position: relative;
  overflow: hidden;

  img{
    width: 360px;
  }
  .main_banner_title{
    color: $corporate-1;
    font-size: 14px;
    text-transform: uppercase;
    text-align: center;
  }
  .main_banner_description{
    color: $white;
    background-color: $corporate-1;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    padding: 5px 10px;
    position: absolute;
    width: 340px;
    height: 40px;
    bottom: 0;
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
    -webkit-transition: opacity 1s, -webkit-transform 1s;
    transition: opacity 1s, transform 1s;
  }
}
.main_banners:hover .main_banner_description{
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.9;
}


/******************** Banners laterales ********************/

#wrapper_banners_lateral{
  margin-top: 50px;

  .banners_lateral{
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;

    #banner_lateral_title{
      color: $corporate-1;
      font-size: 14px;
      text-transform: uppercase;
      text-align: center;
    }
    #banner_lateral_description{
      color: $white;
      background-color: $corporate-1;
      font-size: 14px;
      line-height: 18px;
      text-align: center;
      padding: 5px 10px;
      position: absolute;
      height: 40px;
      bottom: 0;
      opacity: 0;
      -webkit-transform: translateY(40px);
      transform: translateY(40px);
      -webkit-transition: opacity 1s, -webkit-transform 1s;
      transition: opacity 1s, transform 1s;

    }
  }

  .banners_lateral:hover #banner_lateral_description{
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 0.9;
  }
}


/************* contenido *************/

#content{
  background: url("/img/mirar/fondo_contenido.png");

  h3{
    font-size: 18px;
    padding-bottom: 30px;
    font-weight: bold;
    color: $corporate-1;
  }
  h4{
    font-size: 16px;
    padding: 25px 0 20px;
    font-weight: bold;
  }
}
#wrapper_content{
  padding: 20px 0;
  background-color: $white;
  z-index: 100;
}
.content-page-wrapper{
  padding: 0 20px 30px;
  font-size: 15px;
}
#main_text {
  clear: left;

  li{
    list-style: disc;
    margin-left: 40px;
  }
}


/************* footer *************/

footer{
  padding: 10px 0;
  background-color: $gray-3;

  #wrapper_footer_columns{
    margin-bottom: 20px;

    .footer_column{

      .footer_column_title{
        margin: 0 0 5px;
        color: $corporate-1;
        font-size: 16px;
        text-transform: uppercase;
      }
      .footer_column_description{
        font-size: 14px;

        a{
          color: $gray-1;
        }
      }
    }

    #footer_column2{
      img{
        float: left;
        margin-right: 15px;
      }
    }
  }
}
#suscEmail{
  width: 222px !important;
  height: 20px;
}
#newsletter h2{
  margin: 0 0 5px;
  color: $corporate-1;
  font-size: 16px;
  text-transform: uppercase;
}
#newsletter label{
  font-size: 14px;
}
#newsletter input{
  margin: 15px 0;
}
#newsletter #form-newsletter{
  margin: 0;
}
#newsletter .bordeInput{
  border: 1px solid darkgray !important;
  width: 150px;
}
#newsletter button{
  width: 150px;
  margin: 0 auto;
  background: $corporate-1;
  color: $white;
  border: none;
  text-align: center;
  font-size: 16px;
  border-radius: 5px;
  padding: 7px 3px;
  cursor: pointer;
}
#newsletter button:hover{
  background: $corporate-2;
}
.newsletter_checkbox {
  font-size: 12px;

  a {
    text-decoration: underline;
    color: $corporate_1;
  }

  label {
    font-size: 12px !important;
  }
}
input#promotions {
  margin: 0;
}
input#privacy {
  margin: 0;
}
#footer{
  color: $corporate-1;
  margin-top: 20px;
  text-align: center;
  line-height: 12px;

  a{
    text-decoration: none;
    color: $corporate-1;
  }
  #footer_bottom_text{
    font-size: 10px;
  }
}


/***************** otros retoques *****************/

button{
  width: 120px;
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  background-color: $corporate-1;
  color: $white;
  font-size: 18px;
  cursor: pointer;
  overflow: visible;
}
button:hover{
  background-color: $corporate-2;
}
.contVerDetalles a{
  color: $corporate-1;
}
#workWithUs{
  display: none;
}
#my-bookings-form .bordeInput, #contactContent .bordeInput{
  width: 250px;
  margin: 5px 0 15px;
  padding: 3px 0;
  display: block;
}
#cancelButton{
  display:none;
}
#my-bookings-form-fields p{
  margin-bottom: 15px;
  font-size: 15px;
}
#my-bookings-form{
  margin-top: 25px;
}
.promotions-wrap{
  padding: 0 30px;
}
ul.gallery li{
  list-style: none !important;
  margin: 10px 6px !important;
  width: 125px !important;
  height: 95px !important;
  border: solid 1px #cccccc;
  background-color: #ffffff;
  padding: 3px;
}
ul.gallery li img{
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
  height: 100%;
  background: none !important;
  border: none !important;
}
#contact input, #contact textarea{
  border: 1px solid #787878 !important;
}
#contact #contact-button-wrapper{
  float: left !important;
  width: auto;
}
#contactContent h1{
  color: $gray-1 !important;
}
#itemDescription{
  width: 420px;
}
#google_plus_one{
  text-align: center;
}
#___plusone_0{
  margin-top: 10px !important;
  width: 64px !important;
}
#facebook_like{
  text-align: center;
  margin-top: 10px;
}
#facebook_like iframe{
  height: 21px;
  width: 103px;
}