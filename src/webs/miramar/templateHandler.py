import os

from booking_process.utils.data_management.pictures_utils import getPicturesFor<PERSON>ey, get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from webs.BaseTemplateHandler2 import BaseTemplateHandler2

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

class TemplateHandler(BaseTemplateHandler2):

	def get_pictures_from_section_name(self, sectionName, language):
		sliderMessagesSection = get_section_from_section_spanish_name(sectionName, language)
		if sliderMessagesSection:
			return getPicturesForKey(language, sliderMessagesSection.get('key'))
		else:
			return []

	def get_revolution_full_width(self):
		'''By default, on'''
		return "off"

	def getAdditionalParams(self, currentSectionName, language, allSections):

		sectionToUse = self.getCurrenSection(allSections)
		if not sectionToUse:
			sectionToUse = {'sectionName': ""}
		currentSectionKey = sectionToUse.get('key')

		additional_params_dict = {
			'langs': self.getLanguages(),
			'main_banners': getPicturesForKey(language, currentSectionKey), #Imagenes de la sección
			'bottom_footer': self.buildFooter(language),
			'footer_columns': get_pictures_from_section_name("footer columns", language),
			'images_ticks': get_pictures_from_section_name("image ticks", language),
			'wifi_gratis': get_section_from_section_spanish_name("wifi gratis", language),
			'main_title': get_section_from_section_spanish_name("titulo inicio", language)
		}

		return additional_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl



