 <div class="minigallery_wrapper container12">
        {% for pic in minigallery_full %}

            <div class="minigal-img-wrapper" id="minigal-img-wrapper-{{ forloop.counter }}">

                {% if forloop.counter == 1 and iframeMiniGal %}
                    <img src="{{pic.servingUrl|safe}}=s1000" class="minigal-img"/>
                {% else %}
                     <a href="{{pic.servingUrl|safe}}=s1500" rel="lightbox[gallery]">
                        <img src="{{pic.servingUrl|safe}}=s1000" class="minigal-img-hover"/>
                    </a>
                {% endif %}


                {% if forloop.counter == 1 and iframeMiniGal %}
                    {{ iframeMiniGal|safe }}
                {% endif %}


            </div>
        {% endfor %}
  </div>