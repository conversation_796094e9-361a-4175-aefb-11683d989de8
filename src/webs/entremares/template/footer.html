<footer {% if footer_back %}style="background-image: url('{{ footer_back.0.servingUrl|safe }}=s1900')" {% endif %}>
    <div class="footer_content container12">
    <div class="overlay"></div>

        <div class="main_footer">
            {% if footer_columns %}
                <div class="footer_columns_wrapper">
                    {% for x in footer_columns %}<div class="footer_column">
                            {% if x.servingUrl %}<a href="#"><img src="{{ x.servingUrl|safe }}=s500" ></a>{% endif %}
                            {% if x.title %}<span class="title">{{ x.title|safe }}</span>{% endif %}
                            {% if x.description %}<span class="text">{{ x.description|safe }}</span>{% endif %}
                        </div>{% endfor %}
                </div>
            {% endif %}
            {% if newsletter %}
                {{ newsletter|safe }}
            {% endif %}
        </div>

        <hr>

        <div class="footer_legal_text_wrapper">
            <div class="footer_links_wrapper">
                {% for y in extra_policies_footer %}
                    <a href="{{ y.linkUrl|safe }}" target="_blank" rel="nofollow noopener noreferrer">{{ y.title|safe }}</a> |
                {% endfor %}
                    {% for x in policies_section %}
                        {% if x.custom_link %}
                            <a href="{{ x.custom_link }}">{{ x.title|safe }}</a> |
                        {% else %}
                            <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                        {% endif %}
                    {% endfor %}
                <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
                   title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a>
                <!--<a target="_blank" href="/rss.xml">RSS</a>-->
            </div>

            {% if texto_legal %}
                <div class="legal_text">{{ texto_legal|safe }}</div>
            {% endif %}
        </div>
    </div>


</footer>

{% if logos_footer %}
    <div class="logos_footer_wrapper">
        {% for logo in logos_footer %}
            <a {% if logo.linkUrl %} href="{{ logo.linkUrl|safe }}" {% endif %} class="logo_footer">
                <img src="{{ logo.servingUrl|safe }}">
            </a>
        {% endfor %}
    </div>
{% endif %}