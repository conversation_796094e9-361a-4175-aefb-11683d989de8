.booking-data-popup {
  .fancybox-outer {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }

  div#wrapper_booking_fancybox {
    display: table;
    width: 100%;
    position: absolute;
    bottom: 0;
    top: 0;
    margin: auto;

    @include center_xy();
  }

  .adultos.numero_personas, .ninos.numero_personas, .bebes.numero_personas {
    & > label {
      display: none !important;
    }
  }
}

div#data {
  background: rgba($corporate_1, 0.7);

  .booking_title1, .best_price {
    display: none;
  }

  div#booking_engine_title {
    display: block;
    float: none;
    text-align: center;
    font-family: 'Montserrat', sans-serif;
  }

  #motor_reserva {
    width: 595px;
    margin: auto;
    display: table;
  }

  div#fecha_entrada, div#fecha_salida {
    width: 290px;
    float: left;
    height: 125px;
  }

  div#fecha_salida {
    float: right;
    margin-left: 0 !important;
  }

  #contador_noches {
    display: none;
  }

  label#titulo_fecha_entrada, label#titulo_fecha_salida {
    display: block;
    color: #999;
    width: 100% !important;
    text-align: center;
    text-transform: uppercase;
    font-size: 17px;
    font-weight: 500;
    font-family: 'Roboto', sans-serif;
    background: white;
    margin-bottom: 5px;
    padding: 9px 0;
  }

  #contenedor_fechas {
    width: 100%;
    margin-bottom: 15px;
  }

  .wrapper-old-web-support {
    display: none !important;
  }

  #fecha_entrada input, #fecha_salida input {
    border: 0 !important;
    height: 84px !important;
    width: 100% !important;
    text-align: center !important;
    box-sizing: border-box !important;
    font-size: 31px !important;
    color: #4b4b4b !important;
    padding-right: 40px;
    border-radius: 0;
    background: white url(/static_1/images/booking_5/entry_date.png) no-repeat 85% center !important;

    &::-webkit-input-placeholder {
      color: #4b4b4b !important;
    }
  }

  #contenedor_habitaciones {
    margin: auto;
    width: 290px;
    float: left;
    height: 125px;

    label {
      display: block;
      color: #999;
      width: 100% !important;
      text-align: center;
      text-transform: uppercase;
      font-size: 17px;
      float: none;
      font-weight: bolder;
      font-family: 'Montserrat', sans-serif;
      background: white;
      margin-bottom: 2px;
      padding: 9px 0;
    }

    select#selector_habitaciones {
      -webkit-border-radius: 0 !important;
      -moz-border-radius: 0 !important;
      border-radius: 0 !important;
      border: 0;
      width: 260px;
      float: left;
      height: 125px;
      background: white;
      -webkit-appearance: none;

      option {
        text-align: center;
      }
    }

    .selectric {
      height: 83px;
      border-radius: 0;
      margin-top: 0;

      p.label {
        color: #4b4b4b;
        text-align: center;
        box-sizing: border-box !important;
        font-size: 31px !important;
        padding-top: 22px;
      }

      .button {
        background: transparent !important;
        right: 27px;
      }
    }

    .selectricItems li {
      color: #4b4b4b;
      text-align: center;
      box-sizing: border-box !important;
      font-size: 21px !important;
      padding: 12px 12px 10px;
    }
  }

  .selectricWrapper {
    width: 100% !important;
  }

  #contenedor_opciones {
    float: right;
    margin-top: -125px;

    #hab1, #hab2, #hab3 {
      margin: auto;
      width: 290px;
      float: left;
      height: 125px;
    }

    #hab1 {
      margin-left: 305px;
    }

    #hab2, #hab3 {
      margin-top: 20px;
      display: block !important;
    }

    #hab3 {
      float: right;
    }

    label.numero_habitacion {
      color: #999;
      font-weight: 500;
      width: 100% !important;
      text-align: center;
      display: block !important;
      text-transform: uppercase;
      font-size: 17px;
      background: white;
      float: none;
      font-family: 'Roboto', sans-serif;
      margin-bottom: 2px;
      padding: 9px 0;
    }
  }

  .adultos.numero_personas, .ninos.numero_personas, .bebes.numero_personas {
    margin: 0;
    position: relative;
    display: inline-block;

    option {
      display: none;
    }
  }

  .adultos.numero_personas {
    width: 142.25px;
    text-align: center;
    float: left;
    margin-right: 5.5px;
  }

  .ninos.numero_personas {
    width: 142.25px;
    text-align: center;
    float: left;

    .selectricItems {
      left: -84px !important;
    }
  }

  .bebes.numero_personas {
    width: 32%;

    .selectricItems {
      left: -180px !important;
    }
  }

  .ninos {
    float: left;

    label#info_ninos {
      position: absolute;
      top: 20px;
      color: black;
      right: 0px;
      font-size: 9px !important;
      display: inline-block;
    }
  }

  .selectricWrapper.selector_adultos, .selectricWrapper.selector_ninos, .selectricWrapper.selector_bebes {
    .selectric {
      height: 83px;
      border-radius: 0;
      margin-top: 0;
    }

    p.label {
      color: #4b4b4b;
      text-align: center;
      padding-right: 0 !important;
      box-sizing: border-box !important;
      padding-top: 23px;
      font-size: 18px !important;
    }

    .button {
      background: transparent !important;
      width: 16px;
      height: 20px;
      top: 5px;
      right: 10px !important;
    }

    .selectricItems li {
      color: #4b4b4b;
      text-align: center;
      box-sizing: border-box !important;
      font-size: 16px !important;
      padding: 6px 12px 4px;
    }
  }

  fieldset#envio {
    width: 100%;
    margin-left: 0;

    input#promocode {
      float: left;
      width: 290px;
      border-radius: 0;
      border: 0;
      box-sizing: border-box;
      margin-top: 10px;
      height: 90px;
      text-align: center;
      background: rgba(255, 255, 255, 0.3);
      font-size: 31px !important;
      font-weight: 300;
      color: white;

      &::-webkit-input-placeholder {
        color: white;
        font-size: 18px;
        font-weight: 300;
        text-transform: uppercase;
      }
    }

    button#search-button {
      display: block;
      float: right;
      height: 90px;
      width: 290px;
      border-radius: 0;
      border: 0;
      box-sizing: border-box;
      margin-top: 10px;
      background: $corporate_1;
      color: white;
      text-transform: uppercase;
      font-size: 27px !important;
      cursor: pointer;
      @include transition(border-radius, .6s);

      &:hover {
        border-radius: 10px;
      }
    }
  }

  div#hab2, div#hab3 {
    .disabled_overlay {
      display: none;
    }

    &.disabled {
      opacity: 0.4;
      position: relative;

      .disabled_overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: block;
      }
    }
  }

  #booking_engine_title {
    #booking_title1, #best_price {
      display: none;
    }

    h4#booking_title2 {
      color: white;
      margin-bottom: 25px;
      text-transform: uppercase;
      font-size: 22px;
      margin-top: 0;
    }
  }

  #booking_engine_title #booking_title2 {
    &:after {
      content: '';
      display: block;
      width: 70px;
      height: 1px;
      background: white;
      margin: 10px auto;
    }

    span {
      font-weight: 300;
    }
  }

  #contenedor_opciones {
    div#hab2.disabled {
      display: none !important;

      & + #hab3 {
        display: none !important;
      }
    }
  }

  .selectricItems {
    width: 288px !important;
    top: 84% !important;
    left: 11px !important;
    z-index: 9999;
  }

  .destination_wrapper {
    width: 100%;
    margin-bottom: 15px;
    border-bottom: 0;

    label {
      display: none;
    }

    .destination_field {
      input {
        width: 100%;
        height: 55px;
        color: $corporate_2;
        padding-left: 55px;
        font-family: 'Roboto', sans-serif;
        font-weight: 500;

        &::-webkit-input-placeholder {
          color: $corporate_2;
          text-transform: uppercase;
          font-weight: bolder;
        }

        &:-moz-placeholder {
          /* Firefox 18- */
          color: $corporate_2;
        }

        &::-moz-placeholder {
          /* Firefox 19+ */
          color: $corporate_2;
        }

        &:-ms-input-placeholder {
          color: $corporate_2;
        }
      }
    }
  }

}

.booking-data-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: url(/img/#{$base_web}/close_button.png) no-repeat center;
  background: none;

  &:before {
    content: "x";
    color: white;
    font-size: 85px;
    line-height: 36px;
    font-family: 'Montserrat', sans-serif;
  }
}

.booking-data-popup .fancybox-outer {
  background: none;
}

.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  text-align: center;
  color: white;

  .phone_hotel_booking_popup, .email_hotel {
    display: inline-block;
    padding-left: 30px;
    line-height: 25px;
  }

  .phone_hotel_booking_popup {
    margin-right: 10px;
    background: url(/img/#{$base_web}/booking_icos/phone_ico.png) no-repeat left center;
  }

  .email_hotel {
    background: url(/img/#{$base_web}/booking_icos/mail_ico.png) no-repeat left center;
  }
}

div#data .selectricWrapper.selector_adultos .selectric {
}