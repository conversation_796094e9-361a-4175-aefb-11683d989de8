body {
  font-family: "Source sans pro";
  position: relative;
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  a {
    text-decoration: none;
  }
  strong {
    font-weight: 700;
  }
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  .popup-start {
    .fancybox-skin {
      background: transparent !important;
      webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;
    }
    .fancybox-outer {
      background: transparent !important;
    }
    .fancybox-close {
      top: 0 !important;
      right: 0 !important;
    }
  }

  .overlay-slider_wrapper {
    width: 100%;
    overflow: hidden;
    .overlay-slider {
      width: 150%;
      border-bottom-left-radius: 30%;
      border-bottom-right-radius: 30%;
      left: -25%;
      position: relative;
      overflow: hidden;
      margin-bottom: 100px;
    }
  }

  #slider_container {
    position: relative;

    .tp-revslider-slidesli {
      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1;
        background: linear-gradient(rgba(0, 0, 0, .95), rgba(0, 0, 0, 0));
        height: 500px;
      }
    }

    .center_xy {

      .title {
        font-family: $title_family;
      }
      hr {
        margin: 30px;
      }
      .text {
        font-family: $text_family;
        line-height: 35px;
      }
    }

    .tp-simpleresponsive .tp-caption {
      width: 100%;
      height: 100%;
      top: 0;
    }

    .tp-bullets {
      display: none !important;
    }

    .tp-leftarrow.default {
      background-image: none;
      @extend .fa-chevron-left;
        &:before {
          @extend .fa;
          @include center_xy;
          font-size: 30px;
          padding: 30px;
          color: white;
          z-index: 50;
        }
    }

    .tp-rightarrow.default {
      background-image: none;
      @extend .fa-chevron-right;
        &:before {
          @extend .fa;
          @include center_xy;
          font-size: 30px;
          padding: 30px;
          color: white;
          z-index: 50;
        }
    }
  }

  .inner_slider {
    width: 100vw;
    height: 500px;
    margin: auto;
    position: relative;
    overflow: hidden;
    img {
      @include center_image;
    }
  }

  .reservar_web_oficial {
    @include center_x;
    z-index: 20;
    bottom: 100px;
    a {
      display: inline-block;
      background-color: rgba($corporate_2, .8);
      padding: 15px 40px;
      color: white;
      text-align: center;
      font-family: $text_family;
      font-size: 12px;
      font-weight: 700;
      letter-spacing: 1px;
      @include transition(all, .6s);
      &:hover {
        background-color: $corporate_2;
      }
    }
  }

  .content_subtitle_wrapper {
    text-align: center;
    margin-bottom: 100px;
    .content_subtitle_title {
      font-family: $title_family;
      font-size: 38px;
      color: $corporate_1;
      margin-bottom: 50px;
      position: relative;
      z-index: 2;
      img {
        @include center_xy;
        z-index: -1;
      }
    }
    .content_subtitle_description {
      font-family: $text_family;
      font-size: 18px;
      padding: 0 200px;
      color: $corporate_1;
      .button_promotion {
        background-color: $corporate_1;
        padding: 20px 50px;
        text-transform: uppercase;
        color: white;
        text-align: center;
        &:hover {
          background-color: $corporate_1;
        }
      }
    }
  }

  /* Mis reservas */

  .content_access {
    background-color: white;
    padding: 0 calc((100% - 1140px) / 2);
    text-align: center;

    h3.section-title {
      color: $corporate_2;
      font-size: 45px;
      line-height: 47px;
      font-family: $title_family;
      font-weight: 700;
      text-align: center;
      padding-bottom: 50px;
      small {
        font-size: 30px;
      }
    }
    > div {
      display: none;
      text-align: center;
      font-family: $text_family;
      font-weight: 300;
      font-size: 20px;
      line-height: 26px;
      color: $corporate_3;
      padding: 0 200px;
    }
  }

  .cancel_booking_questions {
    padding: 20px;
    margin-bottom: 55px;

    #cancellation-reasons {
      float: none !important;
      margin: auto;
      text-align: center;
      padding: 15px;
    }

    .cancel_booking_reasons_wrapper {
      display: table;
      float: none;
      text-align: center;
      margin: auto;

      #cancelButton_reasons {
        float: none !important;
      }
    }
  }

  #my-bookings-form {
    padding: 0 0 50px 0;
    margin: auto;

    #reservation {
      margin-top: 0 !important;

      .modify_reservation_widget {
        margin: auto;
        margin-top: 40px;
        margin-bottom: 0;
      }

      .my-bookings-booking-info {
        margin: 40px auto 0;

        .fResumenReserva {
          margin: auto;
        }
      }
    }

    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        font-weight: 100;
      }

      select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 300px;
        margin: 10px auto;
        padding: 0 15px;
        height: 40px;
        text-align: center;
        font-size: 14px;
        background-color: white;
        border-radius: 0;
        border: 1px solid $corporate_1;
      }

      input {
        display: block;
        width: 300px;
        margin: 10px auto;
        height: 40px;
        text-align: center;
        font-size: 14px;
        border: 1px solid $corporate_1;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            height: 40px;
            text-transform: uppercase;
            font-size: 16px;
            color: white;
            font-family: "Roboto Condensed", sans-serif;
            border: 0;
            cursor: pointer;
            width: 100%;
            font-weight: 100;
            @include transition(background, .4s);

            &.cancelButton {
              background: $corporate_2;
              height: 40px;
              text-transform: uppercase;
              font-size: 16px;
              color: white;
              border: 0;
              cursor: pointer;
              width: 200px;
              font-weight: 100;
              @include transition(background, .4s);

              &:hover {
                background: darken($corporate_2, 10%);
              }
            }

            &.modify-reservation {
              background: $corporate_1;

              &:hover {
                background: darken($corporate_1, 20%);
              }
            }

            &.searchForReservation {
              background: $corporate_2;

              &:hover {
                background: darken($corporate_2, 20%);
              }
            }
          }
        }
      }
    }
    #cancelButton {
      display: none;
    }
  }

  #cancel-button-container{
         button {
              background: $corporate_2;
              height: 40px;
              text-transform: uppercase;
              font-size: 16px;
              color: white;
              border: 0;
              cursor: pointer;
              width: 200px;
              font-weight: 100;
              margin: 20px auto 0;
              @include transition(background, .4s);

              &:hover {
                background: darken($corporate_2, 10%);
              }
         }
       }

  .iframe_maps_wrapper {
    margin-bottom: 100px;
  }

  .banner_newsletter_wrapper {
    color: #153e5c;
    margin: 0 auto;
    text-align: center;
    width: initial;
    display: block;
    margin-top: -50px;
    margin-bottom: 50px;

    .newsletter_wrapper {
      text-align: center;

      .newsletter_container {
        padding-top: 0;
      }

      .newsletter_title, .newsletter_description {
        display: none;
      }

      form {
        padding: 0 30px;
        display: flex;
        flex-wrap: wrap;

        #suscTelephone,
        #suscEmail {
          border: 1px solid #153e5c;
          display: inline-block;
          padding: 10px 30px;
          vertical-align: middle;
          font-size: 16px;
          font-weight: 300;
          height: 45px;
          width: 100%;
          background-color: rgba(white,.8);
          margin-top: 10px;

          &.error_class {
            border-color: red;
          }
        }

        .button_newsletter {
          background: #153e5c;
          transition: background .4s;
          margin-left: 0;
          width: 100%;

          span {
            color: white;
          }

          &:hover {
            background: #f2be54;
          }
        }

        .check_newsletter {
          .newsletter_checkbox {
            input {
              border: 1px solid #153e5c;
            }

            a, label {
              color: #153e5c;
              padding-left: 0;

              &.error_class {
                color: red;
              }
            }
          }
        }
      }

      .social_newsletter {
        display: none;
      }
    }
  }


  .floatin_content_wrapper {
    .floatin_content {
      .contInput.captcha {
        margin: 20px 0;

        .g-recaptcha {
          display: inline-flex;
          justify-content: center;
        }
      }
    }
  }
}

.fancybox-wrap.fancy-booking-search_v2 {
    .description_bottom_popup_booking {
        color: white !important;
    }
}

.popup_inicio {
    a {
        &.fake_cursor {
            cursor: pointer;
        }
    }
}

/* Sarga specific */

body.sarga {
  header {
    #logoDiv {
      padding: 11px 0;
      img {
        max-width: 100%;
        max-height: 80px;
      }
    }
  }
}

/* Opinions */

iframe {
    width: 900px;
    height: 800px;
}

