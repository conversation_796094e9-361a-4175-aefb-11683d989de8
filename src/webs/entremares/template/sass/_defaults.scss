@import url('https://fonts.googleapis.com/css?family=Fira+Sans:300,400,700|Rufina:400,700|Playfair+Display:400,700');

//Base web (change too in template<PERSON>andler and in config.rb)
$base_web: "entrs";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #153e5c;
$corporate_2: #f2be54;
$corporate_3: #87aeb4;
$corporate_4: #E7EFF1;
$corporate_gray: #E0E0E0;

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

$title_family: 'Rufina', Sans-serif;
$text_family: 'Fira sans', Sans-serif;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined


@keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}

@-webkit-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}
@-moz-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}
@-o-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}

