#banner_icons_full_wrapper {
  margin-bottom: 200px;

  .banner_icon_text {
    text-align: center;
    .banner_icons_title {
      padding: 50px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      font-size: 20px;
      color: $corporate_2;
      font-family: $text_family;
      big {
        font-size: 30px;
        color: $corporate_1;
        font-family: $title_family;
        display: block;
      }
    }
  }

  .banner_icons_wrapper {
    padding-top: 40px;
    .icon {
      display: inline-block;
      background-size: contain;
      width: 20%;
      text-align: center;
      background-position: center;
      background-repeat: no-repeat;
      vertical-align: middle;
      &.linked {
        &:after {
          content: '';
          height: 1px;
          width: 50px;
          background: grey;
          position: absolute;
          right: 90px;
          opacity: 0;
        }
        &:hover:after {
          opacity: 1;
        }
      }
      i {
        display: block;
        font-size: 50px;
        color: $corporate_1;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        width: 100%;
        height: 100px;
        position: relative;
        &:before {
          @include center_xy;
        }
      }
      span {
        font-family: $text_family;
        font-size: 15px;
        color: $corporate_1;
        padding: 0 30px 10px;
        display: block;
        margin-top: -10px;
      }
    }
  }
}