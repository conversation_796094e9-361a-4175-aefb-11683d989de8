@import "defaults";
@import "plugins/fontawesome5pro";
@import "styles_mobile/2/2";

div[data-test-id="ChatWidgetMobileButton"] {
  z-index: 110 !important;
  top: 74% !important;
}

header {
  background-color: $corporate_1 !important;

  .mailto i {
    color: white !important;
  }
}

#full_wrapper_booking {
  .wrapper_booking_button {
    .submit_button {
      background: $corporate_2;
    }
  }
}

.breadcrumbs {
  background-color: $corporate_3 !important;
}

.section_content {
  h1, .content_subtitle_title, .section_title {
    color: $corporate_1;
    font-weight: 700;
    font-family: $title_family;
    margin-top: 30px !important;
    margin-bottom: 0 !important;
  }

  div.content, div.content_subtitle_description, .section-content {
    font-weight: 300;
    font-family: $text_family;
    color: $corporate_3;
    padding: 30px;
  }

  .banner_floating_wrapper {
    margin-bottom: 25px;

    .banner.banner_popup {
      .image {
        display: inline-block;
      }

      .content {
        display: inline-block;
      }
    }
  }
}

.btn {
  background-color: $corporate_3;
  color: white;
  padding: 10px;
}

.banner_footer_app {
  width: 100%;
  padding: 30px 0;
  margin-top: 30px;
  background-color: $corporate_1;
  text-align: center;

  a {
    color: white;
    font-size: 20px;
    font-family: $text_family;
  }
}

/* Cycle banners & Banner gallery */

div.cycle_banners_wrapper, div.banner_gallery_full_wrapper {
  width: 100%;
  background-color: white;
  margin-top: 0;
  margin-bottom: 30px;
  padding: 30px;
  box-sizing: border-box;

  .cycle_element, .banner_gallery {
    display: block;
    width: 100%;
    position: relative;
    margin-bottom: 50px;
    background-color: rgba($corporate_1, .15);

    .cycle_image, .banner_gallery_img {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 200px;

      a {
        width: 100%;
        height: 350px;
        position: relative;
        display: block;
        overflow: hidden;

        img {
          @include center_image;
        }
      }
    }

    .owl-nav {
      font-size: 25px;
      margin: 0 auto;
      width: 150px;
      font-weight: 300;

      .owl-prev, .owl-next {
        position: absolute;
        top: 20%;
        cursor: pointer;
        width: 40px;
        height: 40px;
        background-color: transparent;
        border-radius: 50%;
        color: white;
        display: inline-block;
        margin: 10px;

        i.fa {
          @include center_xy;
        }
      }

      .owl-prev {
        left: 0;
      }

      .owl-next {
        right: 0;
      }
    }

    .img {
      display: none;
    }

    .cycle_title, .title {
      text-transform: uppercase;
      font-size: 23px;;
      color: $corporate_1;
      font-weight: 700;
      font-family: $title_family;
    }

    .cycle_content, .banner_gallery_content {
      box-sizing: border-box;
      padding: 10px;
      position: relative;

      .cycle_description, .banner_gallery_hide {
        font-size: 14px;
        line-height: 26px;
        text-align: justify;
        display: block;
        background: transparent;
        float: none;
        padding: 30px 20px 20px;
        font-weight: 300;
        font-family: $text_family;
        color: $corporate_1;
      }

      .cycle_baner_see_more, .see_more {
        position: relative;
        display: block;
        vertical-align: middle;
        margin: 30px auto;
        text-align: center;
        background-color: $corporate_1;
        font-size: 20px;
        text-transform: uppercase;
        padding: 10px 0;
        color: white;

        a {
          color: white;

          span {
            position: relative;
          }
        }
      }
    }

    .cycle_baner_see_more {
      position: relative;
      display: block;
      vertical-align: middle;
      margin: 30px 50px 0;
      text-align: center;
      background-color: $corporate_1;
      font-size: 20px;
      text-transform: uppercase;
      padding: 10px 0;
      color: white;

      a {
        color: white;

        span {
          position: relative;
        }
      }
    }
  }
}

/* Banner carousel */

.banner_carousel_full_wrapper {
  position: relative;
  padding-bottom: 100px;

  .banner_carousel_text_wrapper:first-child {
    margin-top: 0;
  }

  .banner_carousel_text_wrapper {
    display: inline-block;
    vertical-align: top;
    position: relative;
    z-index: 5;
    margin-top: 100px;

    .banner_carousel_title {
      text-align: center;
      padding: 30px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      font-size: 20px;
      color: $corporate_2;
      font-family: $text_family;
      box-shadow: 0 1px 50px 1px grey;

      big {
        font-size: 30px;
        color: $corporate_1;
        font-family: $title_family;
        display: block;
      }
    }

    .banner_carousel_subtitle {
      text-align: center;
      padding: 10px;
      background-color: $corporate_1;
      font-size: 20px;
      color: white;
      font-family: $text_family;
      vertical-align: middle;
      border-bottom: 20px solid $corporate_4;

      img {
        width: 10%;
        vertical-align: middle;
      }
    }

    .banner_carousel_text {
      background-color: white;
      color: $corporate_1;
      font-family: $text_family;

      p {
        padding: 50px;
        background-size: 30%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }

    .see_more {
      display: block;
      text-align: center;
      padding: 20px;
      color: white;
      background-color: $corporate_2;
      font-family: $text_family;
      font-size: 22px;
      font-weight: 700;
    }
  }

  .banner_carousel_wrapper {
    display: inline-block;
    vertical-align: top;
    width: 100%;

    .img {
      width: 100%;
      height: 350px;
      position: relative;
      display: block;
      overflow: hidden;

      img {
        @include center_image;
      }
    }

    .owl-nav {
      font-size: 25px;
      margin: 0 auto;
      width: 150px;
      font-weight: 300;

      .owl-prev, .owl-next {
        position: absolute;
        top: 35%;
        cursor: pointer;
        width: 40px;
        height: 40px;
        background-color: transparent;
        border-radius: 50%;
        color: white;
        display: inline-block;
        margin: 10px;

        i.fa {
          @include center_xy;
        }
      }

      .owl-prev {
        left: 0;
      }

      .owl-next {
        right: 0;
      }
    }

    .owl-dots {
      background-color: black;

      .owl-dot {
        position: relative;
        display: inline-block;
        opacity: .6;
        vertical-align: middle;
        background-size: cover;
        background-repeat: no-repeat;
        cursor: pointer;

        &:before, &:after {
          content: '';
          @include center_xy;
          background-color: white;
        }

        &:before {
          width: 30px;
          height: 3px;
        }

        &:after {
          width: 3px;
          height: 30px;
        }

        &.active {
          opacity: 1;

          &:before, &:after {
            display: none;
          }
        }
      }
    }
  }

  .see_gallery {
    text-align: center;
    overflow: hidden;

    a {
      position: relative;
      display: block;
      vertical-align: middle;
      text-align: center;
      background-color: $corporate_2;
      font-size: 20px;
      text-transform: uppercase;
      color: white;
      padding: 10px 0;
      font-weight: 300;
      font-family: $text_family;

      span {
        position: relative;
      }
    }
  }
}

.contact_content_element {
  width: auto !important;
  padding: 0 30px;
}

/* Banner icons */

#banner_icons_full_wrapper {
  margin-bottom: 50px;

  .banner_icon_text {
    text-align: center;

    .banner_icons_title {
      padding: 50px;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      font-size: 20px;
      color: $corporate_2;
      font-family: $text_family;

      big {
        font-size: 30px;
        color: $corporate_1;
        font-family: $title_family;
        display: block;
      }
    }
  }

  .banner_icons_wrapper {
    padding-top: 40px;

    .icon {
      display: inline-block;
      background-size: contain;
      width: 50%;
      text-align: center;
      background-position: center;
      background-repeat: no-repeat;
      vertical-align: middle;

      i {
        display: block;
        font-size: 50px;
        color: $corporate_1;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        width: 100%;
        height: 100px;
        position: relative;

        &:before {
          @include center_xy;
        }
      }

      span {
        font-family: $text_family;
        font-size: 15px;
        color: $corporate_1;
        padding: 0 30px 10px;
        display: block;
      }
    }
  }
}

/* Offers */

.offer_content {
  .picture {
    padding: 10px;
    width: auto !important;

    img {
      position: relative !important;
      width: 100%;
      height: 100%;
      top: auto !important;
      left: auto !important;
      transform: none !important;
      min-height: auto !important;
    }
  }

  > h3 {
    padding-top: 30px !important;
  }

  .offer_links_wrapper {
    margin-top: 7px !important;
    animation: none !important;
  }
}

/* Opinions */

.embed-iframe {
  position: relative;
  height: calc(100vh - 300px);
  overflow: hidden;
  left: calc((-100vw + 100%) / 2);
  right: calc((-100vw + 100%) / 2);
  width: 100vw;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
  }
}

/*=== BANNER OFFERS ===*/
.banner_destacados_offers_title {
  padding: 50px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  font-size: 20px;
  color: $corporate_2;
  font-family: $text_family;

  big {
    font-size: 30px;
    color: $corporate_1;
    font-family: $title_family;
    display: block;
  }
}

.banner_special_offers {
  .offer {
    background-color: #133a57;
  }

  .offer_pic {
    background: black;

    img {
      opacity: 0.8;
    }
  }

  .offer_text {
    background-color: rgba(21, 62, 92, 1);
    font-family: "Rufina", Sans-serif;
    color: white;
    padding: 20px;

    .title {
      font-size: 26px;
    }

    .desc {
      font-family: "Fira sans", Sans-serif;
    }

    span.read_more {
      background: #f2be54;
      padding: 10px;
      position: relative;
      display: block;
      margin: 10px auto;
      width: 150px;
    }
  }
}

.banner_offers_wrapper {
  .owl-prev {
    position: absolute;
    top: 110px;
    color: white;
    font-size: 40px;
    left: 10px;
  }

  .owl-next {
    position: absolute;
    top: 110px;
    color: white;
    font-size: 40px;
    right: 10px;
  }
}

// Newsletter
.newsletter_wrapper {
  position: relative;
  padding: 20px;
  background-color: $corporate_1;
  text-align: left;
  color: white;
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background-color: white;
  }

  .newsletter_container {
    .newsletter_title {
      margin-bottom: 20px;
      font-family: $title_family;
      font-size: 26px;
      line-height: 35px;

      p {
        margin: 0;
      }
    }

    .newsletter_description {
      margin-bottom: 20px;
      font-family: $text_family;
      font-size: 14px;
    }

    form {
      display: flex;
      flex-wrap: wrap;

      #suscTelephone,
      #suscEmail {
        width: 100%;
        display: inline-block;
        vertical-align: middle;
        padding: 0 10px;
        margin-bottom: 20px;
        height: 40px;
        border: none;
        background-color: rgba(white, 0.8);
        font-weight: 300;
        font-size: 16px;

        &.error_class {
          outline: 1px solid red;
        }
      }

      .button_newsletter {
        display: inline-block;
        vertical-align: middle;
        width: 100%;
        line-height: 40px;
        padding: 0 10px;
        margin-bottom: 20px;
        background-color: white;
        color: $corporate_1;
        font-size: 16px;
        text-align: center;
        text-transform: uppercase;
        cursor: pointer;
      }

      .newsletter_checkbox {
        @include display_flex(nowrap);
        margin-bottom: 5px;

        input {
          display: inline-block;
          vertical-align: middle;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background: white;
          width: 13px;
          height: 13px;
          border: 2px solid white;
          cursor: pointer;
          outline: none;

          &:checked {
            background: $corporate_1;
          }
        }

        a, label {
          flex: 1;
          padding-left: 10px;
          font-family: $text_family;
          font-size: 12px;
          color: white;

          &.error_class {
            color: red;
          }
        }

        a {
          text-decoration: underline;
        }
      }
    }
  }
}

/* Footer */
footer {
  background-color: #87aeb4;
  padding: 20px 20px 50px 20px;
  font-size: 18px;
  position: relative;
  text-align: center;

  .social_footer {
    margin: 30px auto;
    line-height: 60px;

    a {
      background-color: #153e5c;
      border-radius: 50%;
      font-size: 11px;

      .fa {
        color: #87aeb4;
      }
    }
  }

  .desktop-version-link {
    margin-bottom: 30px;

    a {
      color: white;
    }
  }

  .footer_legal_text_wrapper {
    color: white;

    a {
      color: white;
    }
  }
}