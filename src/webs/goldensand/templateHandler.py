# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.constants.advance_configs_names import EMAIL_BOOKING, CONTACT_PHONES, MOBILE_TEMPLATE
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url, sanitize_html, get_text_version
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_language_title, get_language_code, get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "goldd"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		else:
			sectionToUse = {}

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
		                      'language_selected': get_language_title(language),
		                      'language_selected_key': get_language_code(language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
		                      'hotel_phone': get_config_property_value(CONTACT_PHONES),
		                      'image_offer_slider': get_pictures_from_section_name("ofertas_image_slider", language),
		                      'booking_engine_2': self.buildSearchEngine2(language),
		                      'click_call': get_section_from_section_spanish_name('click call', language),
							  'condiciones_uso': get_section_from_section_spanish_name("condiciones de uso", language),
							  'aviso_legal': get_section_from_section_spanish_name("aviso legal", language)}

		#Content Subtitle
		actual_section = get_section_from_section_spanish_name(sectionToUse.get('sectionName'), language)
		if actual_section.get('subtitle'):
			result_params_dict['content_subtitle'] = actual_section

		if get_config_property_value(EMAIL_BOOKING):
			result_params_dict['hotel_email'] = get_config_property_value(EMAIL_BOOKING).split(";")

		if not sectionToUse:
			news = self.buildNewsInfo(language)
			newsItemToUse = self.getCurrentNewsItem(news, language)
			result_params_dict['individual_news'] = newsItemToUse
			pictures_amount = len(result_params_dict['individual_news'].get('pictures'))
			if pictures_amount > 1:
				result_params_dict['individual_news']['pictures_amount'] = len(result_params_dict['individual_news'].get('pictures'))

		result_params_dict["bottom_popup"] = get_section_from_section_spanish_name("popup inicio footer", language)
		result_params_dict["newsletter_text"] = get_section_from_section_spanish_name("newsletter_text", language)

		if section_type == 'Mis Reservas':
			result_params_dict['content_access'] = True
			result_params_dict['my_booking'] = True

		if not section_type == 'Inicio':
			result_params_dict['inner_section'] = True
		else:
			result_params_dict['ticks_slider'] = get_pictures_from_section_name("_ticks_slider", language)
			home_slider_pictures = self.getMainGallery(language)
			if len(home_slider_pictures) == 1:
				result_params_dict['disabled_revolution'] = True

		if advance_properties.get('banners_x2'):
			result_params_dict['banners_x2'] = get_pictures_from_section_name(advance_properties['banners_x2'], language)

		if advance_properties.get('carousel_icos'):
			result_params_dict['carousel_icos'] = {'section': get_section_from_section_spanish_name(advance_properties.get('carousel_icos'), language), 'pictures': get_pictures_from_section_name(advance_properties.get('carousel_icos'), language)}

		if advance_properties.get('bottom_offers'):
			result_params_dict['offers_bottom'] = self.buildPromotionsInfo(language)
			# get offers link
			for x in result_params_dict['offers_bottom']:
				offer_picture_info = getPicturesForKey(language, str(x['offerKey']), allSections)
				if offer_picture_info[0].get('linkUrl', False):
					x['linked_section'] = offer_picture_info[0]['linkUrl']

		if advance_properties.get('faqs_bottom'):
			result_params_dict['faqs_bottom'] = {'section': get_section_from_section_spanish_name('faqs2', language), 'pictures': get_pictures_from_section_name('faqs2', language)}

		if advance_properties.get('map'):
			result_params_dict['map_iframe_access'] = get_section_from_section_spanish_name('Iframe google maps', language)

		if advance_properties.get('bannersx3'):
			result_params_dict['bannersx3'] = get_pictures_from_section_name(advance_properties['bannersx3'], language)
			for picture_element in result_params_dict['bannersx3']:
				advance_properties_picture = self.getSectionAdvanceProperties(picture_element, language)
				if advance_properties_picture.get('carousel'):
					picture_element['gallery_pictures'] = get_pictures_from_section_name(advance_properties_picture['carousel'], language)

		if advance_properties.get('bottom_carousel'):
			result_params_dict['bottom_carousel'] = get_pictures_from_section_name(advance_properties['bottom_carousel'], language)

		if section_type == 'Habitaciones':
			result_params_dict['rooms_blocks'] = get_pictures_from_section_name('_habitaciones_blocks', language)
			for room_element in result_params_dict['rooms_blocks']:
				room_element['pictures'] = get_pictures_from_section_name(room_element.get('linkUrl'), language)
				room_element['pictures'] = list(filter(lambda l: l.get("title") != "slider", room_element['pictures']))
				room_element['room_individual_link'] = get_section_from_section_spanish_name(room_element.get('linkUrl'), language)
				room_advance_config = self.getSectionAdvanceProperties(room_element, SPANISH)
				if room_advance_config.get('price'):
					room_element['price'] = room_advance_config['price']

				if room_advance_config.get('icos'):
					icos_section = get_pictures_from_section_name('rooms_icos', SPANISH)
					room_element['icos'] = []
					for ico_element in icos_section:
						if ico_element.get('title') in room_advance_config['icos']:
							room_element['icos'].append(ico_element)

		if section_type == 'Ofertas':
			result_params_dict['offers'] = self.buildPromotionsInfo(language)
			for offer_element in result_params_dict['offers']:
				offer_pictures = getPicturesForKey(language, str(offer_element['offerKey']), allSections)
				offer_element['pictures_info'] = offer_pictures

		if section_type == u'Localización':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			sectionTemplate = 'secciones/contact.html'
			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['title'] = mySectionParams['T_formulario_contacto']
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)
			result_params_dict['contact_html'] = contact_html
			result_params_dict['iframe_maps'] = get_section_from_section_spanish_name('Iframe google maps', language)

		if section_type == u"Galeria de Imagenes":
			result_params_dict['content_access'] = True

		if section_type == u'Noticias':
			result_params_dict['news'] = self.buildNewsInfo(language)
			for news_element in result_params_dict['news']:
				pictures_amount = len(news_element.get('pictures'))
				if pictures_amount > 1:
					news_element['pictures_amount'] = len(news_element.get('pictures'))

				news_element['share_description'] = get_text_version(news_element.get('description'))


		if section_type == u'Habitación Individual':
			all_rooms = get_pictures_from_section_name('_habitaciones_blocks', language)
			for number, room_element in enumerate(all_rooms):
				actual_room_section = build_friendly_url(str(sanitize_html(sectionToUse.get('title', ''), []))).strip().lower()
				room_element_section = build_friendly_url(str(sanitize_html(room_element.get('linkUrl', ''), []))).strip().lower()
				if room_element_section == actual_room_section:
					previous_room_number = number - 1
					next_room_number = 0 if len(all_rooms) - 1  == number else number + 1
					result_params_dict['previous_room'] = build_friendly_url(all_rooms[previous_room_number].get('linkUrl'))
					result_params_dict['next_room'] =  build_friendly_url(all_rooms[next_room_number].get('linkUrl'))
					break

			for room_element in all_rooms:
				if build_friendly_url(room_element.get('linkUrl')) == actual_section.get('friendlyUrl'):
					room_element['pictures'] = list(filter(lambda l: l.get("title") != "slider", get_pictures_from_section_name(section_name, language)))
					result_params_dict['individual_room'] = room_element

		if advance_properties.get("event_form"):
			result_params_dict['title_weeding_form'] = get_section_from_section_spanish_name('form_bodas_eventos', language)

		for section in allSections:
			if section.get('sectionName') == u'Gastronom\xeda':
				section['friendlyUrl'] = get_section_from_section_spanish_name('Los Fogones De Mi Madre Tiedra', language).get('friendlyUrl')
		return result_params_dict

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template


	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			picture_gallery = list(filter(lambda l: l.get("title") != "slider", picture_gallery))
			filters = OrderedDict()
			for x in picture_gallery:

				nameFilter = x.get('description', "")
				if not nameFilter:
					nameFilter = ""

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [x['servingUrl']]
				else:
					filters[nameFilter].append(x['servingUrl'])
			result['filters_gallery'] = filters

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_5/_booking_widget.html', params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		return options
	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		sectionToUse = self.getSectionParams(sectionFriendlyUrl, language)
		base_path = os.path.dirname(__file__)

		if sectionToUse:

			if user_agent_is_mobile():
				name_current_section = sectionToUse['sectionName'].lower().strip()
				type_current_section = sectionToUse['sectionType']
				advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

				additionalParams['custom_elements'] = ""

				if type_current_section == 'Habitaciones':
					rooms_blocks = get_pictures_from_section_name('_habitaciones_blocks', language)
					for room_element in rooms_blocks:
						get_content_link = get_section_from_section_spanish_name(room_element.get('linkUrl'), language)
						only_link = get_content_link.get('friendlyUrlInternational')
						room_element['linkUrl'] = only_link

				if type_current_section == u'Habitación Individual':
					rooms_blocks = get_pictures_from_section_name('_habitaciones_blocks', language)
					for room_element in rooms_blocks:
						actual_room_title = sanitize_html(sectionToUse.get('title', ''), []).strip().lower()
						actual_room_link = sanitize_html(room_element.get('linkUrl', ''), []).strip().lower()
						if actual_room_link == actual_room_title:
							sectionToUse['content'] = room_element.get('description')

				if advance_properties.get('bannersx3'):
					mini_dict = {
						'bannersx2': get_pictures_from_section_name(advance_properties.get('bannersx3'), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_bannersx2.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if type_current_section == u'Localización':
					sectionToUse['title'] = False

				if type_current_section == u'Galeria de Imagenes':
					section_pictures = getPicturesForKey(language, sectionToUse.get('key', ''), '')
					section_pictures = list(filter(lambda l: l.get("title") != "ico" and l.get("title") != "slider", section_pictures))

					if section_pictures:
						filters = OrderedDict()
						for x in section_pictures:
							if not 'slider' == (x.get('title') if x.get('title') else '').lower():
								candidate = x['servingUrl']

								if x.get('linkUrl', False):
									candidate = x['linkUrl']

								if not x.get('title', False):
									if x.get('description'):
										x['title'] = x.get('description')
									else:
										x['title'] = get_web_dictionary(language).get('T_mas_fotos', '')

								if not filters.get(x.get('title', ''), False):
									filters[x.get('title', '')] = [candidate]
								else:
									filters[x.get('title', '')].append(candidate)

						additionalParams['filtered_gallery_mobile'] = filters

						# show always images in rows of 3 but first one.
						for key, images in filters.items():
							additionalParams['filtered_gallery_mobile'][key] = {}
							additionalParams['filtered_gallery_mobile'][key]['total'] = len(images) - (
										(len(images) - 1) % 3)
							additionalParams['filtered_gallery_mobile'][key]['images'] = images

						context = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()))
						sectionTemplate = 'mobile_templates/' + get_config_property_value(MOBILE_TEMPLATE) + '/_gallery.html'
						return self.buildTemplate_2(sectionTemplate, context, False)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)


	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['horizontal_nolabel'] = True
		options['custom_new_title'] = get_section_from_section_spanish_name('saber mas', language).get('content', '')
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)