<link rel="stylesheet" href="/static_1/css/templates/mobile/rooms/rooms_3.css" type="text/css" media="screen"/>
<div class="rooms_render_content">
    <div class="rooms_wrapper">
        {% for room in rooms_information %}
            <div class="room_element">
                <div class="rooms_slider flex_room_{{ forloop.counter }}">
                    <ul class="slides">
                        {% if room.gallery %}
                            {% for picture in room.gallery %}
                                <li>
                                    <img class="room_picture" src="{{ picture.servingUrl|safe }}=s1900">
                                </li>
                            {% endfor %}
                        {% else %}
                            <li>
                                <img class="room_picture" src="{{ room.servingUrl|safe }}=s1900">
                            </li>
                        {% endif %}
                    </ul>
                </div>

                <h4 class="room_title">{{ room.title|safe }}</h4>
                <div class="since_price">
                    {% if room.price %}<div class="price">{{ T_desde }} <span>{{ room.price|safe }}</span> {{ T_noche }}</div>{% endif %}
                </div>

                <div class="room_description">
                    {{ room.description|safe }}
                    <div class="see_more_room"style="display: none">{{ T_ver_mas }}</div>

                    <div class="room_icos_wrapper">
                        {% for ico_element in room.icos %}
                            <img class="room_ico_image" src="{{ ico_element.servingUrl|safe }}">
                        {% endfor %}
                    </div>
                </div>

                <div class="booking_general_button">
                    <div class="button" {% if room.namespace and room.hotel_name %} data-hotelname="{{ room.hotel_name }}" data-namespace="{{ room.namespace }}" {% endif %}>
                        {{ T_reservar }}
                    </div>
                </div>



            </div>
            <script>
                $(function () {
                    $(".flex_room_{{ forloop.counter }}").flexslider({
                        controlNav: true
                    });
                });
            </script>
        {% endfor %}
    </div>
</div>


<script>
    $(function () {
        $(".room_description").each(function(){
            if($(this).find("hide").length){
                $(this).find(".see_more_room").show();
            }
        });

        $(".see_more_room").click(function () {
            $(this).parent().find("hide").slideToggle();
        });

    });

    $(".room_element .booking_general_button").click(function () {
        if ($(this).hasClass('opened')) {
            $(this).removeClass('opened');
            return;
        }
        $(".room_element .booking_general_button").each(function () {
            if ($(this).hasClass('opened')) {
                $(this).removeClass('opened');
                $(".hidden_booking_widget").slideUp('fast');
            }
        });
        $(this).addClass('opened');
        var x = $(".hidden_booking_widget").detach();
        x.insertAfter($(this));
    });
</script>
