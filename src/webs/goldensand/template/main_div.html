<div id="main-sections">
	<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner">
		{% for section in main_sections %}
		<div class="main-section-div-wrapper {% if section.subsections %}menu_with_subsection{% endif %}" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}
                <a>{{ section.title|safe }}</a>
            {% else %}
                {% if section.title %}
                <a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}">
                    <span itemprop="name">{{ section.title|safe }}</span>
                </a>
                {% endif %}
            {% endif %}

            {% if section.subsections %}
            <ul>
                {% if not section.disabled %}
                    <li class="main-section-subsection {{ section.title|lower }}">
                         {% if section.title %}
                             <a itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrl }}">
                                 <span itemprop="name">{{ section.title|safe }}</span>
                             </a>
                          {% endif %}
                    </li>
                {% endif %}
                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">
                         {% if subsection.title %}
                            <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                {{ subsection.title|safe}}
                            </a>
                          {% endif %}
                    </li>
                {% endfor %}
            </ul>
            {% endif %}
		</div>
        {% if not forloop.last %}
            <div class="barra"></div>
        {% endif %}
		{% endfor %}
	</ul>
</div>