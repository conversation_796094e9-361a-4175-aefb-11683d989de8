<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic' rel='stylesheet' type='text/css'>
<link href="//fonts.googleapis.com/css?family=Oswald:100,200,300,400,700|Raleway:300,400,700" rel="stylesheet">
<title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} {% endif %}</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

{% if namespace %}
	  <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1.1" type="image/x-icon">
{% else  %}
      <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
{% endif %}

<meta name="keywords" content="{{keywords|safe}}" />
<meta name="description" content="{{description|safe}}" />
<meta name="revisit-after" content="2 days" />
<meta http-equiv="Content-Language" content="{{language}}" />

<meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} {% endif %}" />
<meta name="dc.description" content="{{description|safe}}" />
<meta name="dc.keywords" content="{{keywords|safe}}" />
<meta name="dc.language" content="{{ language }}" />
<meta name="dc.creator" content="{{ hotel_name }}"/>
<meta name="dc.format" content="text/html" />
<meta name="dc.identifier" content="{{ hostWithoutLanguage}}{{ path }}" />
<script type="text/javascript">
    if(navigator.userAgent.match(/Android/i)
    || navigator.userAgent.match(/webOS/i)
    || navigator.userAgent.match(/iPhone/i)
    || navigator.userAgent.match(/iPad/i)
    || navigator.userAgent.match(/iPod/i)
    || navigator.userAgent.match(/BlackBerry/i)
    || navigator.userAgent.match(/Windows Phone/i)) {
     document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
    }
    if (navigator.userAgent.match(/iPad/i)) {
            document.write('<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ipad.css?v=1.1">');
    }
</script>

<!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

<!--[if lte IE 8]>
<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->


<!-- jquery -->
    {{ jquery|safe }}



<!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen" />




    {% if datepicker_theme %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.min.css"  />
    {% endif %}

<!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>


<!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css" />
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=1.12" />


<!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

<!--[if IE 9]>

<![endif]-->

    {{ extra_head|safe }}

</head>

<body itemscope itemtype="//schema.org/Hotel" class="{% if inner_section %}inner_section{% endif %}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}


{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">

{% if lang_management %}
<input type="hidden" id="lang_management" value="{{ lang_management }}">
{% endif %}
{% if lang_default %}
<input type="hidden" id="lang_default" value="{{ lang_default }}">
{% endif %}

{% block content %}

<!--EDIT HERE YOUR PAGE-->

{% endblock %}


<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>


{%  if google_analytics_id %}
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', '{{google_analytics_id}}']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();


</script>
{%  endif %}

{% block additional_js %}



    <!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{language_code}}.js" type="text/javascript"></script>

    <script type="text/javascript" src="/static_1/scripts/common.js"></script>

    <!-- lightbox -->
    <script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <script src="/static_1/lib/selectric/jquery.selectric.min.js" type="text/javascript"></script>

    {% if faqs_bottom or offers_bottom or rooms_blocks or bottom_carousel or images_section or bannersx3 %}
        <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>
    {% endif %}

    <!-- new booking engine -->
    <script src="/static_1/scripts/booking_5.js"></script>
    <script src="/static_1/scripts/booking.js?v=1.1"></script>
    <script async type="text/javascript" src="/js/{{ base_web }}/booking_custom.js"></script>
    <script src="/js/{{ base_web }}/booking_popup_personalized.js" type="text/javascript"></script>

     <!-- My specific js  -->
    <script async defer src="/static_1/lib/jquery.unveil/jquery.unveil.min.js"></script>
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1.2"></script>

    <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>


    <!-- KenBurn Slider ALWAYS AT THE END!!!!!!!! -->
     <!-- jQuery KenBurn Slider  -->
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.unified.js"></script>

    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>


{% endblock %}


{% include "_booking_personalized.html" %}

{% if sectionToUse.sectionType == 'Inicio' %}
    <script>
        {% if popup_inicio_automatico %}
            $(document).ready(function () {
                {% if cookies_on_popup_inicio_automatico %}

                    window.setTimeout(function () {
                        if (searchCookie("anuncio_fancy_{{ language }}")) {
                        }
                        else {
                            $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});
                            document.cookie = "anuncio_fancy_{{ language }}=1"
                        }
                    }, 800);

                {%  else %}


                    window.setTimeout(function() {
                        $.fancybox.open($(".popup_inicio"), {wrapCSS : 'popup-start'});
                    },800);


                {% endif %}
            });
        {% endif %}
    </script>
    {% if popup_inicio_automatico %}
        {% if popup_inicio_automatico.0.servingUrl %}
            <div style="display:none">
                <div class="popup_inicio" style="position:relative;display:table;">
                    {% if popup_inicio_automatico.0.linkUrl %}
                        <a href="{{ popup_inicio_automatico.0.linkUrl }}">{% endif %}<img
                            src="{{ popup_inicio_automatico.0.servingUrl }}=s850">
                    {% if popup_inicio_automatico.0.linkUrl %}</a>{% endif %}
                </div>
            </div>
        {% endif %}
    {% endif %}
{% endif %}


{% if bottom_popup %}
    <div class="bottom_popup" style="display: none">
        <div class="close_button">
            <img src="/static_1/images/close.png" alt="close" title="close"/>
        </div>

        <div class="container12">
            <div class="picture-bigskirt">
                <img src="{{ bottom_popup.pictures.0|safe }}" alt="close" title="close"/>
            </div>

            <div id="wrapper2">
                <div class="bottom_popup_text">{{ bottom_popup.content|safe }}</div>

                {% if bottom_popup_text %}
                    <button class="bottom_popup_button">{{ T_apuntate|safe }}</button>
                {% endif %}

                <input type="text" name="phone_input" placeholder="{{ T_phone_input }}">

                <div class="button-popup">
                    <a href="{{ host|safe }}/{{ bottom_popup_link.content|safe }}.html" onclick="call_me();return false;">{{ bottom_popup.subtitle|safe }}</a>
                </div>

                <p class='wait_message'>{{ T_calling  }}</p>
            </div>
        </div>
    </div>
{% endif %}



<div style="display: none;">
    <div id="data">
        <div id="wrapper_booking_fancybox">
            <div id="booking_widget_popup" class="booking_widget_fancybox">
                {{ booking_engine_2|safe }}
                {{ contact_booking_popup|safe }}
            </div>
        </div>
    </div>
</div>

<script async>
    html_ocupancy = '<div class="guest_selector"><label>{{ T_ocupacion }}</label><span class="placeholder_text">{{ T_seleccionar }}</span><b class="button"></b></div>';
    baby_tag = "{{ T_bebe }}";
    kid_tag = "{{ T_nino }}";
    babies_tag = "{{ T_bebes }}";
    kids_tag = "{{ T_ninos }}";
    number_babies_widget = {{ number_babies_widget }}
            number_kids_widget =
    {{ number_kids_widget }}

    $(function(){
        $("input.promocode_input").attr("placeholder", "");
        $(".promocode_label").html('{{ T_promocode|upper }}');
        $("#suscEmail").attr("placeholder", "{{ T_suscribase_newsletter }}");
    });

    $(window).load(function () {
        booking_deployment();
        $(window).scroll(function () {
            booking_deployment();
        })
    });
</script>

{{ extra_content_website|safe }}
</body>
</html>
