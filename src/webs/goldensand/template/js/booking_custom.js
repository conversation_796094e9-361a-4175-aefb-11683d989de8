$(window).load(function () {
    $(".baseline_datepicker .dates_selector_personalized").insertAfter(".stay_selection");


    $(html_ocupancy).insertAfter('.rooms_number_wrapper');
    $(".guest_selector").click(function () {
        $(".room_list_wrapper").slideToggle()
    });

    _initialize_datepicker_days();

    $(".start_end_date_wrapper").click(function () {
        $(".start_date_datepicker").slideDown();
    });

    $(".close_calendar").click(function(){
        $(".start_date_datepicker, .departure_datepicker").slideUp();
    });

    $(".destination_wrapper").click(function(){
        _open_selection_destiny();
    });

    $(".location_select_wrapper .black_overlay, .location_select_wrapper .close_button_image").click(function(){
       _close_selection_destiny();
    });

    $(".destiny_selector_element").click(function(){
        _hotel_selection($(this));
    });

    $(".see_all_hotels, .booking_0_hotel_selection").click(function(){
        _set_all_hotels_search($(this));
    });


    $(".button_promotion, .button-promotion").click(function(){
        _hotel_selection($(this));
    });

    $(document).on('mouseenter', '.departure_datepicker .ui-datepicker-calendar .ui-state-hover', function (e) {
        var c1 = $(this).closest('.ui-datepicker-group');    //closest datepicker...
        var day1 = $(this).text();
        var month1 = ("0" + $(this).parent().attr('data-month')).slice(-2);
        var year1 = $(this).parent().attr('data-year');
        var fullDate = day1 + "_" + month1 + "_" + year1;
        _calculate_days_hover(day1, month1, year1)
    });
});


var maxNumDays = 999;

limit_day = new Date();
limit_day.setDate(limit_day.getDate() + maxNumDays);

$(window).load(function(){

    $(".start_date_datepicker").datepicker({
        inline: true,
        altFormat: "dd/mm/yy",
        dateFormat: "dd/mm/yy",
        onSelect: function (dateText) {
            $("input[name='startDate']").val(this.value);
            _set_start_date(this.value);
            var miniumDate = $(this).datepicker('getDate', '+1d');
            miniumDate.setDate(miniumDate.getDate() + 1);
            datepicker_departure_date = $.datepicker.formatDate("dd-mm-yy", miniumDate);
            datepicker_departure_date = datepicker_departure_date.replace(new RegExp("-", "g"), '/');
            $("input[name='endDate']").val(datepicker_departure_date);
            _set_end_date(datepicker_departure_date);
            $('.departure_datepicker').datepicker('option', 'minDate', miniumDate);
            $(".end_date_personalized").trigger('click');
            _refresh_days_number();
            $(".start_date_datepicker").slideUp(function(){
                $(".departure_datepicker").slideDown();
            });
        },
        minDate: 0,
        maxDate: limit_day
    });

    $(".departure_datepicker").datepicker({
        inline: true,
        minDate: 1,
        maxDate: limit_day,
        altFormat: "dd/mm/yy",
        dateFormat: "dd/mm/yy",
        onSelect: function (dateText) {
            $("input[name='endDate']").val(this.value);
            _set_end_date(this.value);
            _refresh_days_number();
            $(".departure_datepicker").slideUp();
        },
        beforeShowDay: function (date) {
            date1 = $(".start_date_datepicker").val().split("/");
            date1 = new Date(parseInt(date1[2]), parseInt(date1[1]) - 1, parseInt(date1[0]));
            date2 = $(".departure_datepicker").val().split("/");
            date2 = new Date(parseInt(date2[2]), parseInt(date2[1]) - 1, parseInt(date2[0]));
            if (date > date1 && date < date2) {
                return [true, 'ui-datepicker-highlighted', ''];
            }

            if (date.getTime() === date1.getTime()) {
                return [true, 'ui-datepicker-start_date', ''];
            }
            return [true, '', ''];
        }
    });

     _refresh_days_number();

});


function _set_start_date(html_value) {
    $(".start_end_date_wrapper .start_date_personalized").html(html_value);
}

function _set_end_date(html_value) {
    $(".start_end_date_wrapper .end_date_personalized").html(html_value);
}

function _initialize_datepicker_days() {
    _set_start_date($("input[name='startDate']").val());
    _set_end_date($("input[name='endDate']").val());
}

function _refresh_days_number() {
    var a = $(".start_date_datepicker").datepicker('getDate').getTime(),
        b = $(".departure_datepicker").datepicker('getDate').getTime(),
        c = 24 * 60 * 60 * 1000,
        diffDays = Math.round(Math.abs((a - b) / (c)));

    $(".days_number_datepicker").html(diffDays)
}

function _calculate_days_hover(day, month, year){
    var date_to_check = new Date(year, month, day).getTime(),
        start_date = $(".start_date_datepicker").datepicker('getDate').getTime(),
        calc_method = 24 * 60 * 60 * 1000,
        days_difference = Math.round(Math.abs((start_date - date_to_check) / (calc_method)));

        $(".days_number_datepicker").html(days_difference);
}


function _open_selection_destiny(){
    $(".location_select_wrapper").css('opacity', '0').show();
    $(".location_select_wrapper").animate({'opacity': '1'}, 500);
}

function _close_selection_destiny(){
     $(".location_select_wrapper").animate({'opacity': '0'}, 500, function () {
         $(".location_select_wrapper").hide();
     });
}

function _hotel_selection(hotel_selection){
    var hotel_namespace = hotel_selection.attr('namespace'),
        hotel_url = hotel_selection.attr('hotel_url');

    $(".paraty-booking-form").each(function(){
        $(this).attr('action', hotel_url);
        $(this).find("#namespace").val(hotel_namespace);
        $(this).find(".destination").attr('placeholder', hotel_selection.text());
        $(this).find(".destination").val(hotel_selection.attr('hotel_name'));
    });

    _close_selection_destiny();
}

function _set_all_hotels_search(clicked_element){
    var all_namespaces = clicked_element.attr('namespaces'),
        hotel_name = clicked_element.attr('hotel_name');


    $(".paraty-booking-form").each(function(){
        $(this).attr('action', '/booking0');
        //$(this).find("#namespace").val(all_namespaces);
        $(this).find(".destination").val(hotel_name);

        if (!$(this).find("input[name='applicationIds']").length){
            $("<input type='hidden' id='applicationIds' name='applicationIds' value=''>").appendTo($(this));
        }else{
            $(this).find("input[name='applicationIds']").val(all_namespaces);
        }

        $(this).find("#applicationIds").val(all_namespaces)
    });

    _close_selection_destiny();
}