<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

<link href='//fonts.googleapis.com/css?family=Roboto:300,400,700' rel='stylesheet' type='text/css'>
<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic' rel='stylesheet' type='text/css'>
<title>{% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} </title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

{% if favicon %}
    <link rel="icon" href="{{ favicon }}" type="image/x-icon">
{% else %}
    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico?v=1" type="image/x-icon">
    {% endif %}
{% endif %}

<meta name="keywords" content="{{keywords|safe}}" />
<meta name="description" content="{{description|safe}}" />
<meta name="revisit-after" content="2 days" />

<meta http-equiv="Content-Language" content="{{language}}" />

<meta name="dc.title" content="{% if sectionName %} {{sectionName|safe}} - {% endif %} {{hotel_name|safe}}" />
<meta name="dc.description" content="{{description|safe}}" />
<meta name="dc.keywords" content="{{keywords|safe}}" />
<meta name="dc.language" content="{{ language }}" />
<meta name="dc.creator" content="{{ hotel_name }}"/>
<meta name="dc.format" content="text/html" />
<meta name="dc.identifier" content="{{ hostWithoutLanguage}}{{ path }}" />
<script type="text/javascript">
if(navigator.userAgent.match(/Android/i)
  || navigator.userAgent.match(/webOS/i)
  || navigator.userAgent.match(/iPhone/i)
  || navigator.userAgent.match(/iPad/i)
  || navigator.userAgent.match(/iPod/i)
  || navigator.userAgent.match(/BlackBerry/i)
  || navigator.userAgent.match(/Windows Phone/i)) {
    document.write('<meta name="viewport" content="width=1160, user-scalable=yes, maximum-scale=1.2, initial-scale=0.65">');
}
</script>
 <script type="text/javascript">
if (navigator.platform.match(/Ipad/i)) {
    document.write('<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ipad.css">');
}
</script>
<!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

<!--[if lte IE 8]>
<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->


<!-- jquery -->
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>



<!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen" />




    {% if datepicker_theme %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"  />
    {% endif %}

<!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>


<!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css" />
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles_{{namespace}}.css?v=1.47" />


<!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

<!--[if IE 9]>

<![endif]-->

    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>

<body class="{{namespace}}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{% if lang_management %}
<input type="hidden" id="lang_management" value="{{ lang_management }}">
{% endif %}
{% if lang_default %}
<input type="hidden" id="lang_default" value="{{ lang_default }}">
{% endif %}

{% block content %}

<!--EDIT HERE YOUR PAGE-->

{% endblock %}


<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js?v=1.1"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>
<script type="text/javascript" src="/static_1/scripts/common.js?v=1"></script>


{% block additional_js %}



    <!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{language_code}}.js" type="text/javascript"></script>

    <!-- Flex Slider-->
    <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>

    <!-- lightbox -->
    <script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <script src="/static_1/lib/selectric/jquery.selectric.min.js" type="text/javascript"></script>

     <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>

    <!-- Tiny Carousel -->
    <script type="text/javascript" src="/static_1/lib/tinycarousel/tinycarousel.js"></script>
    <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>

     <!-- Localización -->
    <script type="text/javascript" src="//maps.googleapis.com/maps/api/js?sensor=false&language={{ language }}"></script>
    <script type="text/javascript" src="/static_1/scripts/location.js"></script>



    <!-- new booking engine -->
    <script src="/static_1/scripts/booking.js?v=1.1"></script>
    <script type="text/javascript" src="/static_1/lib/spin.min.js"></script>

  <!-- My specific js  -->
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js"></script>



    <!-- KenBurn Slider ALWAYS AT THE END!!!!!!!! -->
    <!-- jQuery KenBurn Slider  -->
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.revolution.js"></script>

    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>


    <!-- Weather-->
    <script src="/static_1/lib/jquery.simpleweather/jquery.simpleWeather.min.js"></script>


    <script type="text/javascript">


        $(".button-promotion").fancybox({
            width: 800,
            beforeLoad: function () {
                $(".room-selector").val("1");
                $(".datepicker1").val("");
                $(".datepicker2").val("");
                $(".hab2").hide();
                $(".hab3").hide();
            }
        });


         $(".button-promotion2").fancybox({
                    width: 800,
                    beforeLoad: function () {
                        $(".room-selector").val("1");
                        $(".datepicker1").val("");
                        $(".datepicker2").val("");
                        $(".hab2").hide();
                        $(".hab3").hide();
            }
        });

        //Weather
    $.simpleWeather({
        location: 'Malaga',
        woeid: '',
        unit: 'c',
        success: function (weather) {
            var html = '<p class="number"><p class="location">' + 'Albufeira' + '</p><span>' + weather.temp + '&deg;' +
                weather.units.temp + ' | ' + weather.alt.temp + '&deg;' + weather.alt.unit + '</span>' + '<img class="weather-icon" src="/static_1/images/weather_real/' +
                weather.code + '.png"><span class="separator"></span>' + '</p>';
            $(".weather").html(html);
        },
        error: function (error) {
            $("#weather").html('<p>' + error + '</p>');
        }
    });

        {% if name|safe == 'COMA-RUGA' or name|safe == 'localhost' %}
            $(".children_selector > label").each(function () {
                $(this).html($(this).html() + ' (3-11) <span class="anyos">{{ T_anyos }}</span>');
            });
        {% endif %}


    </script>


    <div style="display: none;">
        <div id="data">
            <div id="wrapper_booking_fancybox">
                <div id="booking_widget_popup" class="booking_widget_fancybox">
                    {{ booking_engine }}
                </div>
            </div>
        </div>
    </div>




{% endblock %}

{{ extra_content_website|safe }}
{{ all_tracking_codes_footer|safe }}
</body>
</html>
