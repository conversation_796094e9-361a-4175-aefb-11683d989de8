{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}


    <section id="slider_container">
        {{ revolution_slider|safe }}
        <div id="wrapper_booking" class="container12">
            <div class="booking_widget">
                {{ booking_engine }}
            </div>
        </div>

        <section id="booking-section">
            <div id="booking-horizontal" class="container12">
                <div class="boking_widget_inline">
                    <h3 class="best-online">{{T_reserva_mejor|upper}}</h3>
                   {{booking_engine}}
                </div>
            </div>
        </section>

    </section>

{% endblock %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% endblock %}

{% if name == 'pinheiros' and full_gallery %}
<div class="mini_gallery_wrapper">
    <div class="flexslider">
        <ul class="slides">
            {% for banner in carousel_section %}
                <li>
                    <div class="text-bannerx2">
                        <a href="{{ banner.servingUrl }}=s1900" rel="lightbox[gallery]">
                            <img src="{{ banner.servingUrl }}">
                        </a>
                    </div>
                </li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endif %}

{% include "footer.html" %}


{% endblock %}