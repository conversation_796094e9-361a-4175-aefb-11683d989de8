<div class="banner_maxigallery_wrapper">
{% include "_imago.html" %}
<div class="container12">
    {% if banner_maxigallery.subtitle %}<h2>{{ banner_maxigallery.subtitle|safe }}</h2>{% endif %}
    <div class="banner_maxigallery_slides">
    {% for slide in banner_maxigallery_pics %}
        <div class="slide {% if loop.first %}active{% endif %}" data-link="{{ slide.linkUrl|safe }}">
            <img src="{{ slide.servingUrl|safe }}=s300" data-src="{{ slide.servingUrl|safe }}" alt="{{ slide.altText|safe }}">
            {% if slide.icon %}<i class="fa {{ slide.icon|safe }}"></i>{% endif %}
            <div class="data" style="display: none">
                <h3>{{ slide.title|safe }}</h3>
                <div class="banner_desc">{{ slide.description|safe }}</div>
                {% if slide.linkUrl %}<a href="{{ slide.linkUrl|safe }}" class="read_more">{{ T_ver_mas }}</a>{% endif %}
            </div>
        </div>
    {% endfor %}
    </div>
    <div class="banner_maxigallery">
        <div class="banner_pic">
            <img src="{{ banner_maxigallery_pics.0.servingUrl|safe }}=s800" alt="{{ banner_maxigallery_pics.0.altText|safe }}">
        </div><div class="banner_maxigallery_content">
            <h3>{{ banner_maxigallery_pics.0.title|safe }}</h3>
            <div class="banner_desc">{{ banner_maxigallery_pics.0.description|safe }}</div>
            {% if banner_maxigallery_pics.0.linkUrl %}<a href="{{ banner_maxigallery_pics.0.linkUrl|safe }}" class="read_more">{{ T_ver_mas }}</a>{% endif %}
        </div>
    </div>
</div>
</div>
<script>
$(window).load(function () {
    $(".banner_maxigallery_slides .slide").click(function () {
        $(".banner_maxigallery_slides .slide").removeClass("active");
        $(this).addClass("active");
        $(".banner_maxigallery").hide().promise().done(function () {
            setTimeout($(".banner_maxigallery").fadeIn(), 2000);
        });
            $(".banner_maxigallery").find("img").attr("src",$(this).find("img").attr("data-src")+"=s800");
            $(".banner_maxigallery").find("h3").html($(this).find(".title").html());
            $(".banner_maxigallery").find(".banner_maxigallery_content").html($(this).find(".data").html());
    });
});
</script>