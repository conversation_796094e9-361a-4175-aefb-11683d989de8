<footer>
    <div class="footer_content container12">

        <div class="pre_footer">
            {% if logo_footer %}<div class="logo_footer">
                <a href="{{host|safe}}/"><img src="{{ logo_footer.0.servingUrl|safe }}" alt="{{ logo_footer.0.altText|safe }}"></a>
            </div>{% endif %}
            <div class="menu_footer">
                {% include "main_div.html" %}
            </div>
            {% if newsletter %}{{ newsletter|safe }}{% endif %}
        <script>
            $(window).load(function () {
                $(".newsletter_container").prepend($(".newsletter_container .social_newsletter").detach());
                $(".newsletter_container .social_newsletter").prepend("<h3>{{ T_siguenos_en }}</h3>")
                $(".newsletter_container .input_email").attr("placeholder", "")
            });
        </script>
        </div>

        <div class="footer_legal_text_wrapper">
            <div class="footer_links_wrapper">
            {% if footer_links %}
                {% for link in footer_links %}
                    <a {% if link.description == "external" %}target="_blank"{% endif %} href="{% if link.description == 'popup' %}/{{ language }}/?sectionContent={{ link.linkUrl|safe }}" class="myFancyPopup fancybox.iframe{% else %}{{ link.linkUrl|safe }}{% endif %}" rel="nofollow">{{ link.title|safe }}</a>
                {% endfor %}
            {% endif %}
            {% for link in policies_section %}
                <a href="/{{ language }}/?sectionContent={{ link.friendlyUrl|safe }}" class="myFancyPopup fancybox.iframe" rel="nofollow">{{ link.title|safe }}</a>
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a>
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a>
            <a target="_blank" href="/rss.xml">RSS</a>
            </div>

            {% if texto_legal %}
                <div class="legal_text">{{ texto_legal|safe }}</div>
            {% endif %}
        </div>
    </div>


</footer>