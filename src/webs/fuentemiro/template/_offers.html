<div class="offers_wrapper offer_section">
    <div class="container12">
        {% for banner in offers %}<div class="offer">
            <div class="banner_image">
                <img src="{{ banner.picture|safe }}=s800" alt="{{ banner.name|safe }}">
                <div class="banner_title">
                   <span class="title">{{ banner.name|safe }}</span>
                   <span class="desc">{{ banner.description|safe }}</span>
                    <a href="#data" class="button_promotion"><i class="fa icon-booking"></i>{{ T_reservar }}</a>
                    {% if banner.linkUrl %}
                        <a href="{{ banner.linkUrl|safe }}" class="read_more"><i class="fa fa-file-text-o"></i>{% if banner.title %}{{ banner.title|safe }}{% else %}{{ T_ver_mas }}{% endif %}</a>
                    {% endif %}
                </div>
                <div id="shareSocialArea">

                    <div class="addthis_toolbox addthis_default_style" addthis:title="{{ banner.name|striptags|safe }}" addthis:description='{{ banner.description|striptags|safe }}'>
                        <a href="http://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                           class="addthis_button_compact" style="color:#5A5655"><span class="share_text"><i class="fa fa-share-alt"></i></span></a>
                    </div>
                </div>
            </div>
        </div>{% endfor %}
    </div>
</div>

<script type="text/javascript"> var addthis_config = {ui_language: "{{ language }}"} </script>
<script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>
