.banner_services_wrapper {
  position: relative;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  &:before {
    content: '';
    @include full_size;
    background-color: rgba($corporate_1, .85);;
  }
  .container12 {
    position: relative;
    padding: 70px 0;
    text-align: center;
    h2 {
      font-size: 70px;
      color: white;
      border-bottom: 1px solid rgba($corporate_2, .6);
      padding: 0 0 20px;
      text-align: right;
      font-family: 'Playfair Display', serif;
      font-style: italic;
    }
    .banner_services {
      padding: 30px 0;
      text-align: center;
      .banner_services_ico {
        width: 120px;
        display: inline-block;
        vertical-align: top;
        margin: 0 30px;
        color: white;
        text-align: center;
        overflow: hidden;
        .ico_content {
          i.fa {
            font-size: 70px;
            display: block;
            margin: auto;
          }
          span {
            display: block;
            letter-spacing: 2px;
            opacity: 0;
            @include transition(all, .6s);
            &:before, &:after {
              content: '';
              width: 1px;
              height: 30px;
              background-color: white;
              display: block;
              margin: 10px auto;
            }
          }
          &:hover {
            span {
              opacity: 1;
            }
          }
        }
      }
    }
    /*.read_more {
      display: inline-block;
      background-color: $corporate_2;
      border: 1px solid $corporate_2;
      color: $corporate_1;
      font-size: 18px;
      line-height: 18px;
      text-transform: uppercase;
      letter-spacing: 5px;
      padding: 20px 40px;
      @include transition(all, .6s);

      &:hover {
        background-color: rgba(darken($corporate_1,10%),.6);
        color: #333333;
      }
    }*/
  }
}