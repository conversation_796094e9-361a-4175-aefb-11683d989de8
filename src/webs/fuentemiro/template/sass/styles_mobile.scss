/* Base web (change too in templateHandler and in config.rb) */
$base_web: "fueno";

@import url('https://fonts.googleapis.com/css?family=Open+Sans:300,400,800|Playfair+Display:400i,700i,900i');

/* colors definitions */
$white: rgb(255, 255, 255);
$black: rgb(0, 0, 0);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

/* corporative colors definitions */
$corporate_1: #DB875A;
$corporate_2: #C9D7CC;
$corporate_3: #343434;

$title_size: 40px;
$title_family: "Playfair Display";
$description_size: 14px;
$line_height: 28px;

/* colors for booking widget*/
$booking_widget_color_1: $white;
$booking_widget_color_2: $corporate_1;
$booking_widget_color_3: gray;
$booking_widget_color_4: gray;

@import "styles_mobile/2/2";
@import "styles_mobile/2/carousel_icon";
@import "styles_mobile/2/bannersx2";

@import "mobile/bannerx3";
@import "mobile/banner_text";
@import "mobile/slider_icons";
@import "mobile/banner_offers";
@import "mobile/banner_carousel";

a {
  color: $corporate_1;
}

body {
  font-family: "Open Sans", sans-serif;

  .main-owlslider.slider-small {
    height: calc(100vh - 140px);
  }

  .section_content h2.section_title, .location_content h2.section-subtitle {
    text-align: left;
    padding: 20px 20px 0;
    font-style: italic;
    font-family: "Playfair Display", serif;
    font-size: 30px;
    color: $corporate_1;
    &:after {
      content: '';
      display: block;
      margin: 15px 0;
      width: 150px;
      height: 1px;
      background-color: $corporate_2;
    }
  }
  .location_content h1.section-title {
    display: none;
  }
  .section-content, .location_content .contact_content_element {
    text-align: left;
  }
  .location_content .contact_content_element {
    text-align: left;
    padding: 0 30px;
    box-sizing: border-box;
  }

  .location_wrapper {
    @include display_flex(nowrap);
    flex-direction: column;

    .map {
      order: 3;

      iframe {
        border: none;
      }
    }

    .location_content {
      order: 1;
    }

    #contact {
      order: 2;
    }
  }
}

#shareSocialArea {
  margin-top: 20px;
  margin-bottom: 20px;
  display: inline-block;
}