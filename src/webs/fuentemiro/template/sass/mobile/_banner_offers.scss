.banner_offers_wrapper {
  &:after {
    content: '';
    clear: both;
    display: block;
  }
  .offers_wrapper {
    width: 100%;
    overflow: hidden;
    float: left;
    background-color: $corporate_1;
    .offer {
      position: relative;
      width: 100%;
      height: 350px;
      overflow: hidden;
      img {
        @include center_image;
        @include transition(all, .6s);
      }
      span {
        @include center_xy;
        white-space: nowrap;
        display: inline-block;
        background-color: rgba($corporate_1, .75);
        padding: 15px;
        border-radius: 50px;
        color: white;
        font-size: 20px;
        @include transition(all, .6s);
      }
    }
    .owl-nav {
      & > div {
        @include center_y;
        color: white;
        cursor: pointer;
        font-size: 25px;
      }
      .owl-prev, .owl-next {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: $corporate_1;
        @include transition(all, .6s);
        i.fa {
          @include center_xy;
        }
      }
      .owl-prev {
        left: -25px;
        i.fa {
          margin-left: 7px;
        }
      }
      .owl-next {
        right: -25px;
        i.fa {
          margin-left: -7px;
        }
      }
    }
  }
  .offer_sec {
    width: 100%;
    float: left;
    position: relative;
    height: 350px;
    overflow: hidden;
    background-color: $corporate_1;
    img {
      @include center_image;
      opacity: .6;
    }
    .banner_title {
      @include center_xy;
      background-color: transparent;
      padding: 10px 15px;
      font-size: 30px;
      border-radius: 50px;
      border: 2px solid white;
      white-space: nowrap;
      span.title {
        display: inline-block;
        vertical-align: middle;
        color: white;
        letter-spacing: 0;
        text-transform: uppercase;
      }
      span.ico {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        width: 45px;
        height: 50px;
        font-size: 30px;
        color: white;
        .fa {
          @include center_xy;
        }
      }
    }
  }
}