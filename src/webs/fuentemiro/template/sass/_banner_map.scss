.banner_map_wrapper {
  position: relative;
  display: table;
  width: 100%;
  height: 650px;
  iframe {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
  }
  .map_widget {
      .iframe-google-maps-wrapper {
        background-color: $corporate_1;
        width: 380px;
        padding: 30px;
        h3 {
          font-size: 30px;
          font-style: italic;
          font-family: "Playfair Display", serif;
          margin-bottom: 30px;
          color: white;
          text-align: center;
        }
        input {
          display: inline-block;
          vertical-align: middle;
          padding: 15px;
          width: 180px;
          background-color: rgba(255,255,255,.6);
          border-width: 0;
        }
        button {
          display: inline-block;
          vertical-align: middle;
          padding: 12px 20px;
          text-transform: uppercase;
          background-color: white;
          font-size: 14px;
          border-color:transparent;
        }
        .go_map {
          display: block;
          text-align: center;
          i.fa {
            background-color: $corporate_2;
            color: $corporate_1;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-bottom: -20px;
            position: relative;
            &:before {
              @include center_xy;
            }
          }
        }
      }
  }
  .container12 {
    position: relative;
    z-index: 1;
  }
  .banner_map {
    width: 380px;
    background-color: $corporate_1;
    color: white;
    min-height: 650px;
    padding: 50px;
    letter-spacing: 2px;
    text-align: center;
    h3 {
      font-size: 30px;
      font-style: italic;
      font-family: "Playfair Display", serif;
      margin-bottom: 30px;
      big {
        font-size: 50px;
        font-weight: 700;
      }
      strong {
        font-weight: 700;
      }
      &:before, &:after {
        content: '';
        width: 1px;
        height: 40px;
        background-color: white;
        display: block;
        margin: 10px auto;
      }
    }
    a {
      color: white;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}