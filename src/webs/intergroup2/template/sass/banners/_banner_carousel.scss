.banner_carousel_wrapper {
  .banner {
    .image {
      position: relative;
      width: 100%;
      height: 540px;
      overflow: hidden;
      img {
        @include center_image;
      }
    }
    .banner_content {
      position: absolute;
      right: 0;
      left: 50%;
      bottom: 0;
      padding: 30px 40px;
      background-color: $corporate_1;
      color: white;
      .content_title {
        margin-bottom: 20px;
        .title {
          color: white;
          margin: 0;
          small {
            font-size: 22px;
            display: block;
          }
        }
      }
      .text {
        padding: 20px 0;
      }
      .link {
        display: inline-block;
        padding: 20px;
        text-transform: uppercase;
        color: white;
        border: 1px solid white;
        font-size: 18px;
        font-weight: 700;
        @include transition(all, .6s);
        &:hover {
          color: $corporate_1;
          background-color: white;
        }
      }
    }
  }
  .owl-nav {
    position: absolute;
    right: 20%;
    bottom: 45px;
    .owl-prev, .owl-next {
      color: white;
      font-size: 22px;
    }
  }
}