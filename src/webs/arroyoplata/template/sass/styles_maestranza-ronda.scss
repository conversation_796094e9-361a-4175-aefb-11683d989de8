

$corporate_1: rgb(168, 129, 86);
@import "styles";
@import "booking_engine";



.selectric .button{
  background: $corporate_1 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

.wrapper_booking_button button{
  background:$corporate_1;
}
.default_section_title{
  text-align: center;
}

.bannerx3_element .image_wrapper{
  height:230px;
  overflow:hidden;
  position:relative;

  img.banner_background{
    position:absolute;
    top:0;
    bottom:0;
    margin:auto;
  }
}

.tp-bullets .bullet {
  background: url("/img/arroa/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/arroa/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

#slider_container{
  .revowl_slider .owl-dots{
    width: fit-content;
    transform: translateX(calc(100% + 80px));
  }
}