<!-- Default Content -->
{% if default_content %}
    <div class="default_content">
        <h3 class="default_section_title">{{ default_content.subtitle|safe }}</h3>
        <div class="default_section_description">
            {{ default_content.content|safe }}
        </div>
    </div>
{% endif %}


<!-- Content Render -->
{% if automatic_content %}
    <div class="content_rendered">
        {{ content }}
    </div>
{% endif %}



{#Habitaciones#}
{% if rooms %}
    <div class="room_wrapper">
    {% for room in rooms %}
            <div class="rooms column6 {% cycle 'blockleft-room' 'blockright-room' %}"
                 id="room_block_{{ forloop.counter }}">
                <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);"
                   class="rooms-img">
                    <img src="/img/arroa/ico_fotos_blocks.png" class="ico_cam_room">
                    <img src="{{ room.pictures.0.servingUrl }}=s560" class="room_img">
                </a>

                <div class="rooms-description">
                    <h3 class="title-module"><span class="destino">{{ room.name|safe }}</span></h3>
                    {{ room.description|safe }}
                    <div class="room-links">
                        <span class="btn-corporate"><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                                                       class="button-promotion">{{ T_reservar }}</a></span>
                        <span class="btn-flecha"><a href="#" class="btn_vermas_room">{{ T_ver_mas }} </a></span>
                    </div>
                </div>
            </div>
    {% endfor %}
    </div>
{% endif %}


{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}
            {{ location_html.content|safe }}
        </div>

        <div class="form-contact column6">
            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>
    </div>

    {% if iframe_google_map %}
        <div class="iframe-google-maps-wrapper">
            <div id="slider_map_container">
                <div class="map" id="map-canvas" style="height:450px">
                     {{ iframe_google_map.content|safe }}
                </div>
            </div>
        </div>
    {% endif %}
{% endif %}


{# Ofertas #}
{% if blocks %}
<div class="top_content_wrapper container12">
    <div class="scapes-blocks">
        {% for block in blocks %}
            <div class="block {% cycle 'row1' 'row2' %}">
                <div class="description">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    <ul>
                        <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva">{{ T_reservar }}</a> </li>
                        <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                    <img src="{{block.picture}}=s1000" alt='{{ T_imagen }}-{{ forloop.counter }}' title='{{ T_imagen }}-{{ forloop.counter }}'>
                </a>
                <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    {{block.description|safe}}
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endif %}


{% if servicios %}
<div id="services">

    <div id="title_line" class="container12">
        <span><h3>{{ servicesTitle.content|safe }}</h3></span>
    </div>

    <div id="wrapper_services" class="container12">
    {% for service in servicios %}
        <div class="service column3" id="service_block{{forloop.counter}}">


                 <a>
                    <span style="background:url('{{ service.servingUrl }}') no-repeat;"></span>
                 </a>


            {% if service.title %}
                <div class="service_title">{{ service.title|safe }}</div>
            {% endif %}

            {% if service.description %}
                <div class="service_description">{{ service.description|safe }}</div>
            {% endif %}
        </div>
    {% endfor %}
    </div>
</div>
{% endif %}



{#Mini Gallery#}
{% if mini_gallery %}

    <div class="gallery-mosaic container12">
    {% if mini_gallery_title.subtitle %}<h3 class="gallery_title">{{ mini_gallery_title.subtitle|safe }}</h3>{% endif %}
    <div class="gallery_container">
    <div class="gallery-smalls column4">
        {% for picture in mini_gallery|slice:":7" %}
            {% if not forloop.first %}

                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe }}">
                        <img src="{{ picture.servingUrl }}=s285-c" alt="{{ T_imagen }}{{ forloop.counter }}" title="{{ T_imagen }}{{ forloop.counter }}">
                    </a>
                </div>


            {% else %}


                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe }}">
                        <img src="{{ picture.servingUrl }}=s285-c" alt="{{ T_imagen }}{{ forloop.counter }}" title="{{ T_imagen }}{{ forloop.counter }}">
                    </a>
                </div>
                </div>
                <div class="gallery-big column8">

            {% endif %}

        {% endfor %}
        </div>
        </div>
</div>

{% endif %}


{% if banners_see_more %}

    <div id="full-wrapper-banners-circles">
        <div id="wrapper-banners-circles" class="container12">

            {% for banner in banners_see_more %}
                <div class="wrapper-banner-circle">
                    <img class="banner-circle-top-img" src="{{ banner.servingUrl|safe }}"
                         alt="{{ banner.title|safe }}" title="{{ banner.title|safe }}">

                    <div class="content-banner-circle">
                        <h4 class="title-banner-circle">{{ banner.title|safe }}</h4>

                        <div class="description-banner-circle">{{ banner.description|safe }}</div>
                        <a href="{{ banner.linkUrl|safe }}" class="moreinfo-banner-circle">{{ T_ver_mas }} >></a>
                    </div>
                </div>

            {% endfor %}
        </div>
    </div>


{% endif %}


<!-- Banners x3 Bottom -->
{% if bannersx3 %}
    <div class="bannersx3_wrapper">
        {% for x in bannersx3 %}

            <div class="bannerx3_element">
                <a href="{{ x.linkUrl|safe }}">
                    <div class="image_wrapper">
                        <div class="overlay"></div>
                        <img class="banner_background" src="{{ x.servingUrl|safe }}=s900">
                        <div class="title">{{ x.title|safe }}</div>
                        <img class="plus_symbol" src="/img/{{ base_web }}/plus_symbol.png">
                    </div>
                    <div class="description_wrapper">
                        {{ x.description|safe }}
                    </div>
                </a>
            </div>

        {% endfor %}
    </div>
{% endif %}




<!-- Location Footer -->
{% if location_bottom %}
    <div class="location_bottom_wrapper">
        <div class="location_title column12">
            <h2>{{ location_bottom.subtitle|safe }}</h2>
        </div>
        <div class="location_bottom_description">
            {{ location_bottom.content|safe }}
        </div>
        <div class="location_bottom_map">
            {{ location_bottom.map|safe }}
        </div>
    </div>
{% endif %}