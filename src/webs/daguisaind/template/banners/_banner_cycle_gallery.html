<div class="banner_cycle_gallery_wrapper">
    {% for banner in banner_cycle_gallery %}
        <div class="banner">
            <div class="banner_content">
                {% if banner.title %}<div class="title">{{ banner.title|safe }}</div>{% endif %}
                {% if banner.description %}<div class="desc">{{ banner.description|safe }}</div>{% endif %}
            </div>
            <div class="banner_pic_wrapper">
                {% if banner.linkUrl %}
                    <div class="center_y">
                        <a href="{{ banner.linkUrl }}" class="link" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}>
                            <i class="fal fa-plus"></i>
                            <span>
                                {% if banner.btn_text %}{{ banner.btn_text }}{% else %}{{ T_ver_mas }}{% endif %}
                            </span>
                        </a>
                    </div>
                {% endif %}
                <div class="banner_pic owl-carousel">
                    {% for pic in banner.pics %}
                        <div class="pic"><img src="{{ pic.servingUrl }}=s1900" {% if pic.altText %}alt="{{ pic.altText|safe }}"{% endif %}></div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(function () {
       $(".banner_cycle_gallery_wrapper .banner_pic").owlCarousel({
            loop: false,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
            autoplay: false
        });

        {% if is_mobile %}
            $(".banner_cycle_gallery_wrapper .owl-item img").each(function () {
                var img_width = $(this).width(),
                    img_height = $(this).height();

                var target_class = img_width > img_height ? 'horizontal' : 'vertical';
                $(this).addClass(target_class);
            });
        {% endif %}
    })
</script>