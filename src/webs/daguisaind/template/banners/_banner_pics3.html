<div class="banner_pics3_wrapper">
    {% if banner_pics3.subtitle %}<div class="title">{{ banner_pics3.subtitle|safe }}</div>{% endif %}
    <div class="desc">
        {% if banner_pics3.content %}{{ banner_pics3.content|safe }}{% endif %}
        {% if '<modal' in banner_pics3.content %}
            <a href="#" class="btn open_modal">
                {{ T_leer_mas }}
                <i class="fal fa-long-arrow-right see_more" aria-hidden="true"></i>
            </a>
        {% endif %}
        {% if logo %}
            <img src="{{ logo.0.servingUrl }}" class="logo_svg_1">
        {% endif %}
    </div>
    <div class="banner_pics3">
        {% for pic in pics %}
            <a class="pic {% if pic.linkUrl %}pic_link" href="{{ pic.linkUrl }}{% endif %}">
                <img src="{{ pic.servingUrl }}{% if loop.index == 3 and not is_mobile %}=s1900{% else %}=s400-c{% endif %}" alt="{{ pic.altText|safe }}">
                {% if pic.linkUrl %}<span class="label">{% if pic.title %}{{ pic.title|safe }}{% else %}{{ T_ver_mas }}{% endif %}<i class="fal fa-long-arrow-right see_more" aria-hidden="true"></i></span>{% endif %}
            </a>
        {% endfor %}
    </div>
</div>
<script>
$(window).load(function () {
    {% if not is_mobile %}
        function banner_pics3_fx() {
            $(".banner_pics3_wrapper .banner_pics3").addnimation({parent:$(".banner_pics3_wrapper"),class:"fadeInUp",reiteration:false});
            $(".banner_pics3_wrapper .banner_pics3 .pic").addnimation({parent:$(".banner_pics3"),class:"fadeInRight",reiteration:false});
            $(".banner_pics3_wrapper .desc").addnimation({parent:$(".banner_pics3_wrapper"),class:"fadeIn",reiteration:false});
        }
        banner_pics3_fx();

        $(window).scroll(banner_pics3_fx);
    {% endif %}

    $(".banner_pics3_wrapper").find(".open_modal").click(function(e){
        e.preventDefault();
        let target_html = $(this).closest(".banner_pics3_wrapper").find(".desc")[0].outerHTML;
        $.fancybox.open(target_html, {
            baseClass: "banner_pics3_fancybox",
            width: 750,
            autoSize: false
        });
    });
});
</script>