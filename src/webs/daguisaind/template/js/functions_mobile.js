function booking_click(namespace) {

    if (namespace){
        $(".hotel_selector_mobile option[data-namespace="+namespace+"]").attr('selected', 'selected');
        $(".hotel_selector_mobile").trigger('change');
        $(".mobile_engine_action").trigger('click');
    }
}
$(window).load(function () {

    var offer_list_clicked = false;

    var selector = $(".hotel_selector_mobile");
    var selector_option = $(".hotel_selector_option");

    $(".button-promotion, .booking_room_button_element.button-promotion").click(function () {
        offer_list_clicked = false;
        selector_option.show();

        var namespace = $(this).attr("data-namespace");
        if (namespace){
            $(".hotel_selector_option#" + namespace).attr("selected", "selected");
            selector.trigger("change");
            selector.change();
        }

        var hotels_in = $(this).attr("data-hotel-in");

        if (hotels_in){
            offer_list_clicked = true;
            var hotels_in_array = hotels_in.split(";");
            selector_option.css('display', 'none');
            hotels_in_array.forEach(function (namespace, idx) {
                $(".hotel_selector_option#" + namespace).removeAttr('style');
            });
            if (hotels_in_array.length === 1){
                var target_option = $(".hotel_selector_option#" + hotels_in_array[0]);
                $(".hotel_selector_mobile option:selected").removeAttr("selected");
                target_option.attr("selected", "selected");
                selector.val(target_option.val());
                selector.trigger("change");
            }

        }
    });

});