.hotel_card {
  .title {
    text-align: center;
    img, span {
      display: inline-block;
      vertical-align: middle;
    }
    span {
      position: relative;
      margin: 0 0 0 30px;
      padding: 0 30px;
      font-size: 30px;
      font-family: "DIN alternate", sans-serif;
      font-weight: bold;
      &:before {
        content: '';
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba($corporate_2,.4);
        @include center_y;
        z-index: -1;
        left: 0;
      }
    }
  }
  .content {
    position: relative;
    margin: 40px 0 10px;
    .desc {
      width: 65%;
      height: 550px;
      padding: 100px 20% 100px 150px;
      position: relative;
      overflow: hidden;
      z-index: 10;
      color: lighten($corporate_1,20%);
      &:before, &:after {
        content: '';
        position: absolute;
        z-index: -2;
        top: 50%;
        right: 20%;
        -webkit-transform: translate(0,-50%) rotate(-25deg);
        -moz-transform: translate(0,-50%) rotate(-25deg);
        -ms-transform: translate(0,-50%) rotate(-25deg);
        -o-transform: translate(0,-50%) rotate(-25deg);
        transform: translate(0,-50%) rotate(-25deg);
        border-left:4px solid #efefef;
        border-right: 1px solid #efefef;
        width: 4px;
        height: 230%;
      }
      &:after {
        right: calc(20% + 80px);
        width: 6px;
        border-left:1240px solid #efefef;
        border-right: 6px solid #efefef;
      }
    }
    .gallery {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      .pic {
        height: 550px;
        width: 100%;
        overflow: hidden;
        position: relative;
        img {
          @include center_y;
          width: 70%;
          right: 0;
        }
      }
      .owl-nav {
        position: absolute;
        bottom: 80px;
        right: 0;
        z-index: 25;
        .owl-next, .owl-prev {
          color: white;
          display: inline-block;
          vertical-align: middle;
          padding: 0 10px;
          font-size: 40px;
          &.disabled {
            opacity: .5;
          }
        }
      }
    }
    .extra {
      background: $corporate_2;
      text-align: center;
      div, a {
        display: inline-block;
        vertical-align: middle;
        padding: 20px 25px;
        letter-spacing: 1px;
        color: $corporate_1;
        &:before {
          @extend .fal;
          font-size: 18px;
          margin-right: 15px;
        }
        &.address {
          @extend .fa-map-pin;
        }
        &.phone {
          @extend .fa-phone;
        }
        &.email {
          @extend .fa-envelope;
        }
      }
    }
  }
  .banner_gallery_wrapper {
    position: relative;
    margin-bottom: 50px;
    .banner_gallery_sec {
      width: 50%;
      height: 550px;
      padding: 100px 150px 100px 10%;
      position: relative;
      overflow: hidden;
      z-index: 10;
      text-align: right;
      margin-right: 0;
      margin-left: auto;
      color: lighten($corporate_1,20%);
      .banner_gallery_sec_title {
        font-size: 20px;
        font-weight: bold;
        font-family: "DIN alternate", sans-serif;
        margin-bottom: 30px;
        position: relative;
        color: $corporate_1;
        big {
          font-size: 40px;
          font-weight: 700;
        }
        &:before {
          content: '';
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: rgba($corporate_2, .4);
          position: absolute;
          top: -30px;
          right: -30px;
          z-index: -1;
        }
      }
      .banner_gallery_sec_desc {

      }
      a.link {
        display: block;
        margin: 10px 0 10px auto;
        width: 250px;
        text-align: left;
        color:$corporate_1;
        &:hover {
          i {
            background: $corporate_2;
            &:after {
              background: $corporate_2;
            }
          }
        }
        i {
          display: inline-block;
          vertical-align: middle;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: rgba($corporate_1,1);
          color: white;
          font-size: 24px;
          position: relative;
          margin-right: 30px;
          &:before {
            @include center_xy;
          }
          &:after {
            content: '';
            @include center_y;
            left: 100%;
            height: 1px;
            width: 20px;
            background: rgba($corporate_1,1);
          }
        }
        span {
          display: inline-block;
          vertical-align: middle;
          font-family: "DIN Alternate", sans-serif;
          font-size: 16px;
          font-weight: 500;
          letter-spacing: 1px;
          line-height: 20px;
          text-transform: uppercase;
        }
      }
      .btn {
        display:block;
        width: 270px;
        padding: 20px 0;
        text-transform: uppercase;
        text-align: center;
        font-size: 30px;
        color: white;
        position: relative;
        z-index: 4;
        overflow: hidden;
        margin: 30px 0 0 auto;
        &:after {
          content: '';
          @include full_size;
          z-index: -2;
          background: $corporate_2;
        }
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 200%;
          height: 0;
          z-index: -1;
          background: rgba(0,0,0,0.3);
          -webkit-transform: translate(-50%,-50%) rotate(45deg);
          -moz-transform: translate(-50%,-50%) rotate(45deg);
          -ms-transform: translate(-50%,-50%) rotate(45deg);
          -o-transform: translate(-50%,-50%) rotate(45deg);
          transform: translate(-50%,-50%) rotate(45deg);
          @include transition(all,.6s);
        }
        &:hover {
          color: white;
          &:before {
            height: 200px;
            -webkit-transform: translate(-50%,-50%) rotate(30deg);
            -moz-transform: translate(-50%,-50%) rotate(30deg);
            -ms-transform: translate(-50%,-50%) rotate(30deg);
            -o-transform: translate(-50%,-50%) rotate(30deg);
            transform: translate(-50%,-50%) rotate(30deg);
          }
        }
      }
      &:before, &:after {
        content: '';
        position: absolute;
        z-index: -2;
        top: 50%;
        left: 20%;
        -webkit-transform: translate(0,-50%) rotate(25deg);
        -moz-transform: translate(0,-50%) rotate(25deg);
        -ms-transform: translate(0,-50%) rotate(25deg);
        -o-transform: translate(0,-50%) rotate(25deg);
        transform: translate(0,-50%) rotate(25deg);
        border-right:4px solid #efefef;
        border-left: 1px solid #efefef;
        width: 4px;
        height: 230%;
      }
      &:after {
        left: calc(20% + 80px);
        width: 6px;
        border-right:1240px solid #efefef;
        border-left: 6px solid #efefef;
      }
    }
    .banner_gallery {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      .pic {
        height: 550px;
        width: 100%;
        overflow: hidden;
        position: relative;
        background: $corporate_1;
        &:hover {
          img {
            opacity: .6;
          }
          .center_y {
            opacity: 1;
          }
        }
        img {
          @include center_y;
          width: 80%;
          left: 0;
          @include transition(opacity,.6s);
        }
        .center_y {
          left: 130px;
          width: 40%;
          margin: auto;
          opacity: 0;
          @include transition(opacity,.6s);
          color: white;
          .banner_gallery_title {
            font-size: 40px;
            font-weight: bold;
            margin-bottom: 30px;
          }
          .btn {
            display: block;
            width: 200px;
            text-align: center;
            padding: 15px 0;
            margin-top: 30px;
            color: white;
            border: 1px solid white;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            font-size: 20px;
            &:before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 200%;
              height: 0;
              z-index: -1;
              background: rgba(0,0,0,0.3);
              -webkit-transform: translate(-50%,-50%) rotate(45deg);
              -moz-transform: translate(-50%,-50%) rotate(45deg);
              -ms-transform: translate(-50%,-50%) rotate(45deg);
              -o-transform: translate(-50%,-50%) rotate(45deg);
              transform: translate(-50%,-50%) rotate(45deg);
              @include transition(all,.6s);
            }
            &:hover {
              &:before {
                height: 200px;
                -webkit-transform: translate(-50%,-50%) rotate(30deg);
                -moz-transform: translate(-50%,-50%) rotate(30deg);
                -ms-transform: translate(-50%,-50%) rotate(30deg);
                -o-transform: translate(-50%,-50%) rotate(30deg);
                transform: translate(-50%,-50%) rotate(30deg);
              }
            }
          }
        }
      }
      .owl-nav {
        position: absolute;
        bottom: 80px;
        left: 0;
        z-index: 25;
        .owl-next, .owl-prev {
          color: white;
          display: inline-block;
          vertical-align: middle;
          padding: 0 10px;
          font-size: 40px;
          &.disabled {
            opacity: .5;
          }
        }
      }
    }
    .extra {
      position: absolute;
      bottom:0;
      left: 0;
      right: 0;
      z-index: 20;
      text-align: center;
      background: rgba($corporate_2,.8);
      a {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        padding: 25px 15px;
        font-size: 16px;
        color: lighten($black, 20%);
        text-transform: uppercase;
        border-bottom: 1px solid transparent;
        &.active {
          font-weight: bold;
          border-bottom-color: $black;
        }
        &:before {
          content: '';
          @include center_y;
          right: -3px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: lighten($black, 20%);
        }
      }
    }
  }
}