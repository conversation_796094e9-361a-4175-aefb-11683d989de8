//Base web (change too in config.rb)
$base_web: "dagud";
@import url('https://fonts.googleapis.com/css?family=Quicksand:300,500,700&display=swap');
@font-face {
    font-family: 'DIN alternate';
    src: url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.eot');
    src: url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.woff2') format('woff2'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.woff') format('woff'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.ttf') format('truetype'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.svg#dinregularalternate') format('svg');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN alternate';
    src: url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.eot');
    src: url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.woff2') format('woff2'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.woff') format('woff'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.ttf') format('truetype'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.svg#dinboldalternate') format('svg');
    font-weight: bold;
    font-style: normal;
}
// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #222222;
$black: #222222;
$corporate_2: #f7bb1e;
$corporate_3: #898989;

$title_family: "DIN alternate", sans-serif;
$text_family: "Quicksand", sans-serif;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;


@mixin title_styles() {
  font-family: $title_family;
  font-size: 45px;
  line-height: 47px;
  color: $corporate_1;
  i {
    position: relative;
    font-size: 60px;
    margin-right: 40px;
    &:after {
      content: '';
      position: absolute;
      top: -30px;
      left: -30px;
      z-index: -1;
      display: block;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $corporate_2;
      opacity: .4;
    }
  }
}

@mixin text_styles() {
  font-weight: 400;
  font-family: $text_family;
  font-size: 19px;
  line-height: 25px;
  color: $black;
}

@mixin btn_styles($color: $corporate_1, $width: 180px) {
  @if $is_mobile {
    $width: $width / 2
  }
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  color: $color;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
  letter-spacing: .5px;
  background: transparent;
  font-family: $btn_family;
  text-transform: uppercase;
  border: none;
  margin: 0;
  padding: 0;
  cursor: pointer;

  &:before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    background-color: transparent;
    border: 1px solid transparent;
    border-color: $color $color transparent transparent;
    @include transform(rotate(45deg));
    position: absolute;
    top: calc(50% - 4px);
    right: 0;
  }

  &:after {
    content: '';
    display: inline-block;
    width: $width;
    max-width: calc(#{$width} / 1.5);
    height: 1px;
    background-color: $color;
    margin-left: 15px;
    @include transition(all, .6s);
  }

  &.white {
    color: white;

    &:after {
      background-color: white;
    }
  }

  &:focus {
    outline: 0;
  }

  &:hover {
    &:after {
      max-width: $width;
    }
  }
}

@mixin base_banner_styles() {
  position: relative;
  padding: 40px calc((100% - 1140px) / 2);
  overflow: hidden;
}

@mixin owl_nav_styles($color: $corporate_1) {
  font-size: 32px;
  color: $color;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  @include transition(color, .6s);

  &.disabled {
    opacity: .4;
    cursor: default;
  }

  &:not(.disabled):hover {
    color: $corporate_2;
  }
}

@mixin owl_dots_styles($color: $corporate_2) {
  display: inline-block;
  vertical-align: middle;
  float: none;
  margin-right: 5px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: $color;
  @include transition(all, .2s);
  &:hover, &.active, &.selected {
    width: 10px;
    height: 10px;
    background-color: $corporate_1;
  }
}

