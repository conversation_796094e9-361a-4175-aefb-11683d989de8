html[lang=en] body {
  #full_wrapper_booking {
    .guest_selector {
      width: 180px;
    }
    .destination_wrapper.unselected {
      width: 320px;
    }
  }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  box-shadow: 0 5px 10px rgba($black, .3);
  border-radius: 0;
  .header_datepicker {
    background-color: $corporate_1 !important;
    margin: 0;
    .close_button_datepicker {
      top: 9px;
      background: white;
      color: $black;
    }
    .specific_date_selector {
      text-transform: uppercase;
      font-family: "DIN Alternate", sans-serif;
      &:before {
        display: none;
      }
    }
  }
  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    padding: 0;

    table.ui-datepicker-calendar {
      margin: 0 15px;
      width: calc(100% - 30px);
      th span {
        font-weight: bold;
        color: $corporate_2;
      }
      td {
        border-width: 0;
        background: transparent !important;
        height: 40px;
        padding: 5px 0;
        .ui-state-default {
          line-height: 30px;
          height: 30px;
          width: 30px;
          border-radius: 50%;
        }
        .ui-state-active {
          background: $corporate_2 !important;
        }
        .ui-state-hover {
          background: $corporate_1;
        }
        &.ui-datepicker-start_date, &.highlight, &.ui-datepicker-highlighted {
          .ui-state-default {
            background: $corporate_2 !important;
            color:white !important;
          }
        }
        &.ui-datepicker-start_date {
          .ui-state-default:before {
            display: none;
          }
        }
      }
    }
  }
  .ui-datepicker-header.ui-widget-header {
    border-bottom: 1px solid $black;
    font-family: "DIN Alternate", sans-serif;
    .ui-datepicker-prev, .ui-datepicker-next {
      background: transparent;
      .ui-icon {
        background: transparent;
        color: transparent;
        position: relative;
        @extend .fa-arrow-down;
        &:before {
          @extend .fal;
          color: $black;
          font-size: 20px;
          @include center_xy;
        }
      }
      &:hover, .ui-state-hover {
        background: transparent !important;
        .ui-icon:before {
          color: $corporate_2;
        }
      }
    }
  }
  .specific_month_selector {
    margin: 0;
    background: transparent;
    color: $corporate_2;
    strong {
      color: $black;
    }
  }
}
body #ui-datepicker-div span.ui-icon.ui-icon-circle-triangle-e,
body #ui-datepicker-div span.ui-icon.ui-icon-circle-triangle-w {
  &:before {
    @extend .fal;
  }
}

#full_wrapper_booking {
  background: rgba(white, .85);
  position: absolute;
  bottom: 0;
  top: auto;
  left: 0;
  right: 0;
  z-index: 20;
  padding-top: 15px;
  #full-booking-engine-html-7 {
    width: auto;
    .booking_form.paraty-booking-form {
      width: auto;
      text-align: center;
    }
    .wrapper-new-web-support.booking_form_title {
      background: transparent;
      text-align: center;
      margin: 0;
      top: auto;
      bottom: 100%;
      padding: 10px;
      left: calc((100% - 1200px) / 2);
      position: absolute;
      background: $corporate_2;
      color: white;
      border-radius: 5px 5px 0 0;
      .web_support_label_1, .web_support_label_2 {
        display:inline-block;
        font-weight: bold;
      }
      .web_support_number, .web_support_label_1 {
        font-size: 13px !important;
      }
    }
  }
  .destination_wrapper {
    position: relative;
    z-index: 10;
    display: inline-block;
    vertical-align: middle;
    background: transparent !important;
    border-bottom-width: 0;
    text-align: left;
    width: auto;
    .destination_field {
      position: relative;
      @extend .fa-search;
      &:before {
        @extend .fal;
        @include center_y;
        left: 0;
        font-size: 30px;
      }
      label {
        position: absolute;
        top: 15px;
        left: 45px;
        display: block;
        text-align: left;
        font-family: "DIN alternate", sans-serif;
        font-size: 14px;
        text-transform: uppercase;
        margin: 0;
      }
      input.destination {
        outline: none;
        cursor: pointer;
        background: transparent;
        text-transform: uppercase;
        padding: 33px 0 17px 45px;
        &::-webkit-input-placeholder {
          color: $corporate_1;
        }
        &::-moz-placeholder {
          color: $corporate_1;
        }
        &:-ms-input-placeholder {
          color: $corporate_1;
        }
        &:-moz-placeholder {
          color: $corporate_1;
        }
      }
    }
  }
  .hotel_selector {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;right: 0;
    z-index: 5;
    background: rgba(white,.95);
    height: 100vh;
    text-align: left;
    padding: 60px calc((100% - 800px) / 2);
    .close {
      position: absolute;
      top: 30px;
      left: 100px;
      width: 40px;
      height: 40px;
      cursor: pointer;
      @include transition(all,.6s);
      &.active {
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -o-transform: rotate(180deg);
        transform: rotate(180deg);
      }
      &:before, &:after {
        background: $corporate_1;
      }
      &:hover {
        &:before, &:after {
          background: $corporate_2;
        }
      }
    }
    .hotel_selector_search {
      position: relative;
      border-bottom: 1px solid $corporate_1;
      margin-bottom: 30px;
      i {
        position: absolute;
        top: 3px;
        left: 5px;
        padding: 5px;
        color: $corporate_2;
        font-size: 14px;
      }
      input {
        width: 100%;
        padding: 5px 5px 5px 45px;
        background: transparent;
        border-width: 0;
        color: $corporate_2;
        font-size: 14px;
        font-weight: 500;
        outline: none;
        &::-webkit-input-placeholder {
          color: $corporate_2;
        }
        &::-moz-placeholder {
          color: $corporate_2;
        }
        &:-ms-input-placeholder {
          color: $corporate_2;
        }
        &:-moz-placeholder {
          color: $corporate_2;
        }
      }
    }
    .hotel_selector_inner {
      li {
        display: inline-block;
        vertical-align: top;
        width: calc((100% / 3) - 15px);
        margin-right: 18px;
        margin-bottom: 20px;
        cursor: pointer;
        box-shadow: 0 0 15px rgba(0,0,0,0.15);
        &:nth-child(7),
        &:nth-child(16){
          margin-right: 0;
        }
        &:hover {
          .title_selector {
            background: $corporate_2;
          }
        }
        .hotel_pic {
          position: relative;
          overflow: hidden;
          padding: 40px;
          height: 120px;
          background: $corporate_1;
          .main {
            @include center_image;
            max-width: 101%;
            min-height: unset;
            opacity: .4;
          }
          .logo {
            @include center_xy;
            max-height: 80%;
          }
        }
        .title_selector {
          display: block;
          padding: 5px 25px;
          background: $corporate_1;
          color: white;
          font-size: 14px;
          font-weight: bold;
          text-align: center;
        }
        .hotel_location {
          background: white;
          font-size: 12px;
          padding: 3px 10px;
          i {
            margin-right: 10px;
          }
        }
      }
    }
  }
  .dates_selector_personalized {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    float: none;
    text-align: left;
    .dates_selector_label {
      width: 400px;
      position: absolute;
      top: 15px;
      left: 0;
      span {
        display: block;
        width: 50%;
        float: left;
        font-family: "DIN alternate", sans-serif;
        font-size: 14px;
        text-transform: uppercase;
      }
    }
    .start_end_date_wrapper {
      font-size: 0;
      padding: 0;
      display: table;
      width: 400px;
      background: transparent;
      .start_date_personalized, .end_date_personalized {
        font-size: 24px;
        font-weight: bold;
        width: 50%;
        float: left;
        padding: 35px 0 20px;
        position: relative;
        span {
          display: inline-block;
          margin: 0 3px;
          text-transform: uppercase;
        }
        &:before {
          content: '';
          position: absolute;
          right: 20px;
          top: 15px;
          bottom: 15px;
          width: 1px;
          background: rgba($black,.3);
        }
      }
      .nights_number_wrapper_personalized {
        display: none;
      }
    }
  }
  .rooms_number_wrapper {
    display: none;
  }
  .guest_selector {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding: 30px 0 20px;
    text-align: left;
    width: 280px;
    label {
      display: block;
      position: absolute;
      top: 15px;
      font-family: "DIN alternate", sans-serif;
      font-size: 14px;
      text-transform: uppercase;
    }
    .placeholder_text {
      font-size: 24px;
      font-weight: bold;
    }
    .button {
      display: none;
    }
  }

  .room_list_wrapper {
    display: none;
    position: absolute;
    bottom: 100%;
    left: 50%;
    z-index: 2;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    text-align: left;
    width: 300px;
    background: white;
    box-shadow: 0 5px 10px rgba(0,0,0,0.3);
    .room_list {
      .room {
        .adults_selector, .children_selector, .babies_selector {
          border-width: 0;
          height: 50px;
          text-align: center;
          width: calc(100%/3) !important;
          float: left;
          padding: 7px 0px 5px;

          label.adults_label, label.children_label, label[for=babiesRoom1] {
            font-size: 10px;
            text-transform: uppercase;
          }
          .room_selector{
            width: 100% !important;
            .selectricItems {
              display: none !important;
            }
            .selectric {
              height: 30px;
              .label {
                text-align: center;
                @extend .fa-plus;
                &:before {
                  position: absolute;
                  top: 50%;
                  left: 15px;
                  -webkit-transform: translateY(-50%);
                  -moz-transform: translateY(-50%);
                  -ms-transform: translateY(-50%);
                  -o-transform: translateY(-50%);
                  transform: translateY(-50%);
                  @extend .fal;
                  font-size: 18px;
                  margin-top: 5px;
                }
              }
              .button {
                text-indent: 0;
                height: auto;
                margin: 0;
                font-size: 0;
                line-height: 24px;
                background: transparent !important;
                text-shadow: 0 0 0 rgba(0,0,0,0) !important;
                @extend .fa-minus;
                &:before {
                  position: absolute;
                  top: 50%;
                  right: 15px;
                  -webkit-transform: translateY(-50%);
                  -moz-transform: translateY(-50%);
                  -ms-transform: translateY(-50%);
                  -o-transform: translateY(-50%);
                  transform: translateY(-50%);
                  display: inline-block;
                  vertical-align: middle;
                  @extend .fal;
                  font-size: 18px;
                  line-height: 24px;
                  margin-top: 5px;
                  color: #585d63;
                }
              }
            }
          }
        }
        &.room2, &.room3 {
          height: 50px;
          .adults_selector, .children_selector, .babies_selector {
            height: 40px;
            padding-top: 0;
            label {
              display: none;
            }
          }
        }
      }
    }
    .add_remove_room_wrapper {
      display: table;
      width: 100%;
      div {
        position: relative;
        float:left;
        text-align: center;
        padding: 10px;
        width: 50%;
        color: white;
        background: $corporate_2;
        &:nth-child(2) {
          background: $corporate_1;
        }
        &:before {
          content: '';
          @include full_size;
          background: rgba(0,0,0,.3);
          opacity: 0;
          @include transition(opacity,.6s);
        }
        &:hover {
          &:before {
            opacity: 1;
          }
        }
      }
    }
  }
  .wrapper_booking_button {
    display: inline-block;
    vertical-align: middle;
    width: 500px;
    .promocode_wrapper {
      display: inline-block;
      vertical-align: middle;
      width: 150px;
      border-top-width: 0;
      position: relative;
      padding: 30px 0 20px;
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 15px;
        bottom: 15px;
        width: 1px;
        background: rgba($black,.3);
      }
      label.promocode_label {
        display: none;
      }
      .promocode_input {
        text-align: center;
        padding: 10px;
        background: transparent;
      }
    }
    .submit_button {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 155px);
      background: transparent;
      color: white;
      letter-spacing: 2px;
      font-family: "DIN alternate", sans-serif;
      font-size: 34px;
      font-weight: normal;
      height: 100px;
      margin-top: -15px;
      position: relative;
      &:after {
        content: '';
        @include full_size;
        z-index: -2;
        background: $corporate_2;
      }
      &:before {
        content: '';
        @include full_size;
        z-index: -1;
        background: $black;
        height: 0;
        @include transition(height,.6s);
      }
      &:hover {
        &:before {
          height: 100%;
        }
      }
    }
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  width: 100%;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);

  .room_list_wrapper {
    bottom: auto;
    top: 100%;
  }
}

body.inner_section {
  #full_wrapper_booking {
    .room_list_wrapper {
      bottom: auto;
      top: 100%;
    }
  }
}

@media (max-width: 1490px) {
  div#full_wrapper_booking {
    .dates_selector_personalized {
      .dates_selector_label {
        width: 280px;
      }
      .start_end_date_wrapper {
        width: 280px;
        .start_date_personalized, .end_date_personalized {
          .year {
            display: none;
          }
        }
      }
    }
    .room_list_wrapper {
      right: 345px;
    }
    .wrapper_booking_button {
      width: 350px;
      .submit_button {
        font-size: 24px;
      }
    }
    #full-booking-engine-html-7 .wrapper-new-web-support.booking_form_title {
      left: calc((100% - 890px) / 2);
    }
  }
}
@media (max-width: 1220px) {
  div#full_wrapper_booking {
    .room_list_wrapper {
      right: 265px;
    }
  }
}

@media (max-width: 1601px) {
  #full_wrapper_booking #full-booking-engine-html-7{
    .wrapper-new-web-support.booking_form_title {
      left: calc((100% - 1200px) / 2);
    }
  }
}