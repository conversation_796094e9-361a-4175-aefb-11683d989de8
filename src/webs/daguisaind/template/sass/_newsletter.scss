.newsletter_wrapper {
  padding: 80px 0;
  clear: both;
  .newsletter_title {
    display: inline-block;
    vertical-align: top;
    width: 260px;
    margin-right: 40px;
    font-family: "DIN Alternate", sans-serif;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    big {
      font-size: 50px;
      font-weight: 700;
    }
    &:before {
      content: '';
      position: absolute;
      top: -30px;
      right: 0;
      z-index: -1;
      display: block;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $corporate_2;
      opacity: .4;
    }
  }
  .newsletter_description {
    display: none;
  }
  .newsletter_form {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 570px);
    @extend .fa-at;
    &:before {
      @extend .fal;
      position: absolute;
      z-index: -1;
      top: 20px;
      left: 15px;
      font-size: 30px;
    }
    .input_email {
      display: inline-block;
      vertical-align: middle;
      border: 2px solid rgba($corporate_1, .6);
      padding: 20px 20px 20px 65px;
      width: 340px;
      text-transform: uppercase;
      font-size: 20px;
      background: transparent;
      margin-bottom: 30px;
    }
    .button_newsletter {
      display: inline-block;
      vertical-align: middle;
      text-transform: uppercase;
      font-size: 20px;
      padding: 21px 10px;
      width: 200px;
      color: white;
      text-align: center;
      margin-left: 20px;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
      &:after {
        content: '';
        @include full_size;
        z-index: -2;
        background: $corporate_2;
      }
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200%;
        height: 0;
        z-index: -1;
        background: rgba(0,0,0,0.3);
        -webkit-transform: translate(-50%,-50%) rotate(45deg);
        -moz-transform: translate(-50%,-50%) rotate(45deg);
        -ms-transform: translate(-50%,-50%) rotate(45deg);
        -o-transform: translate(-50%,-50%) rotate(45deg);
        transform: translate(-50%,-50%) rotate(45deg);
        @include transition(all,.6s);
      }
      &:hover {
        color: white;
        &:before {
          height: 200px;
          -webkit-transform: translate(-50%,-50%) rotate(30deg);
          -moz-transform: translate(-50%,-50%) rotate(30deg);
          -ms-transform: translate(-50%,-50%) rotate(30deg);
          -o-transform: translate(-50%,-50%) rotate(30deg);
          transform: translate(-50%,-50%) rotate(30deg);
        }
      }
    }
    .check_newsletter {
      font-size: 14px;
      color: $black;
      margin-bottom: 10px;
      white-space: initial;

      @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
        white-space: initial;
      }
      a {
        color: $black;
      }
      input[type=checkbox] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: transparent;
        border: 1px solid $black;
        border-radius: 0;
        width: 12px;
        height: 12px;
        vertical-align: middle;
        position: relative;
        outline: none;
        margin-right: 15px;
        &:checked {
          &:before {
            content: '';
            width: 6px;
            height: 6px;
            background: $corporate_2;
            @include center_xy;
          }
        }
      }
    }
  }
  .social_newsletter {
    display: inline-block;
    vertical-align: top;
    width: 260px;
    text-align: center;
    padding: 5px;
    a {
      display: inline-block;
      vertical-align: middle;
      margin: 0 10px;
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      position: relative;
      font-size: 24px;
      &:before, &:after {
        content: '';
        border-radius: 50%;
        @include center_xy;
        @include transition(all,.6s);
      }
      &:before {
        width: 100%;
        height: 100%;
        background: $corporate_1;
      }
      &:after {
        background: $corporate_2;
        width: 0;
        height: 0;
      }
      &:hover {
        &:before {
          width: 50%;
          height: 50%;
        }
        &:after {
          width: 110%;
          height: 110%;
        }
      }
      i {
         @include center_xy;
        z-index: 2;
      }
    }
  }
  &.right_content {
    display: flex;
    flex-flow: row;
    align-items: flex-start;

    .newsletter_container {
      width: 35%;
      display: flex;
      position: relative;
      flex-flow: column;
      max-width: 390px;
      .newsletter_title {
        margin: auto;
        margin-bottom: 20px;
      }
      .newsletter_form {
        width: auto;
        .button_newsletter {
          margin-left: 0;
          margin-top: 10px;
        }
      }
      .checks_wrapper {
        display: flex;
        flex-flow: column;
        width: 100%;
        .check_newsletter {
          white-space: normal;
          label, a {
            font-size: 12px;
          }
        }
      }
      .social_newsletter {
        position: absolute;
        bottom: 25px;
        left: 210px;
      }
    }
    .newsletter_right {
      width: 55%;
      position: relative;

      .newsletter_right_item {
        position: relative;
        margin-bottom: 120px;

        .title_right {
          position: relative;
          display: flex;
          flex-flow: column;
          font-size: 26px;
          font-weight: 600;
          max-width: 660px;
          z-index: 1;

          small {
            display: block;
            font-weight: 400;
            font-size: 22px;
          }

          big {
            display: block;
            font-family: $title_family;
            font-size: 65px;
            font-weight: 700;
          }
        }

        .logo {
          position: absolute;
          top: 0;
          left: 300px;
          width: 220px;
          opacity: .5;

          img {
            width: 100%;
            height: auto;
          }
        }
      }

      /*.newsletter_bg {
        position: absolute;
        width: 250px;
        height: 270px;
        display: block;
        background-size: contain;
        background-repeat: no-repeat;
        opacity: .4;
        left: 395px;
        top: -30px;
      }*/
    }
  }
}

body.daguisa-goldenfenix {
  .newsletter_wrapper {
    &.right_content {
      max-width: 1440px;
      width: 90vw;
      margin: 0 auto;

      .newsletter_container {
        margin-top: 70px;
      }

      .newsletter_right {
        padding-left: 50px;

        .newsletter_right_item {
          &:last-of-type {
            margin-top: -30px;
          }

          .logo {
            left: 275px
          }

          .title_right {
            big {
              font-size: 50px;
            }
          }
        }
      }
    }
  }
}