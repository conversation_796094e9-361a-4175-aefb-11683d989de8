.banner_gallery_new_wrapper {
  position: relative;
  padding: 40px calc((100% - 1140px) / 2);
  background-color: white;

  .banner_gallery {
    .image {
      position: relative;
      display: inline-block;
      width: calc(60% + 100px);
      height: 545px;
      overflow: hidden;

      img {
        @include center_image;
        width: auto;
        max-height: 180%;
      }
    }

    .owl-nav {
      position: absolute;
      top: 490px;
      left: 20px;

      .owl-prev, .owl-next {
        @include owl_nav_styles(white);
        margin-right: 20px;
      }
    }

    .owl-dots {
      position: absolute;
      bottom: -30px;
      left: 0;

      .owl-dot {
        @include owl_dots_styles;
      }
    }
  }

  .banner_content {
    @include center_y;
    right: calc((100% - 1140px) / 2);
    z-index: 2;
    width: 450px;
    padding: 40px 40px;
    background-color: #F8FAFB;

    .title {
      @include title_styles;
      position: relative;
      font-size: 26px;
      letter-spacing: 0;

          &:before {
          content: '';
          position: absolute;
          top: -20px;
          left: -30px;
          z-index: -1;
          display: block;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: #F8BC00;
          opacity: .2;
        }

      .subtitle {
        &:first-of-type:after {
          width: 80px;
        }
      }

      big {
        font-size: 56px;
        letter-spacing: 1.12px;
        font-weight: bold;
      }
    }

    .text {
      @include text_styles;
      margin-top: 30px;

      modal {
        display: none;
      }

      .btn {
        display: inline-block;
        margin-top: 20px;
        color: $corporate_1;
      }
    }

    .link_wrapper {
      padding-top: 20px;
      text-align: center;
    }
  }
}

.fancybox-wrap {
  .logo_svg_1 {
    display: none;
  }

  .open_modal {
    display: none;
  }
}