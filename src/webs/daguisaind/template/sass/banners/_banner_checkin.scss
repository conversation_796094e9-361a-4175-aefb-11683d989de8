.banner_checkin_wrapper {
  display: flex;
  flex-flow: row;
  height: 350px;
  margin: 40px 0;
  padding: 0 15%;
  color: white;
  background: black;

  .left_block {
    width: 49%;
    position: relative;

    .title {
      position: relative;
      font-family: "DIN Alternate", sans-serif;
      font-size: 50px;
      font-weight: 700;
      color: $corporate_2;
      top: 25%;
      left: 40%;
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .desc {
      position: relative;
      top: 35%;
      left: 45%;
      -webkit-transform: translate(-50%, -50%);
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);

      ul {
        counter-reset: number;
        list-style-type: none;
      }

      /* The "\a0" is a space */
      ul li::before {
        color: $corporate_2;
        counter-increment: number;
        content: counter(number) "\a0";
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .right_block {
    width: 35%;
    display: flex;
    justify-content: center;
    position: relative;
    flex-direction: column;
    padding: 20px 0;

    .links {
      text-align: center;
    }

    img {
      width: max-content;
      margin: 0 auto;
    }

    a {
      color: white;
      margin-left: 15px;

      i {
        margin-right: 5px;
      }
    }
  }
}