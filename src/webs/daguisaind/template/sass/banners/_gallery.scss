.gallery_filter_wrapper {
	padding: 0 0 50px;
	h3 {
		margin: 50px 0 10px;
		font-size: 20px;
		color: $corporate_1;
		border-bottom: 1px solid $corporate_2;
		cursor: pointer;
		strong {
			font-weight: bold;
		}
		small {
		  font-family: "Dancing Script", sans-serif;
		  font-size: 200%;
		  color: black;
		}
		&:first-of-type {
			margin-top: 0;
		}
	}
	a {
		display: inline-block;
		vertical-align: top;
		width: 25%;
		height: 200px;
		overflow: hidden;
		position: relative;
		border: 1px solid white;
		img {
			@include center-image;
			@include transition(opacity, .6s);
		}
		span.overlay {
			@include center_xy;
			border: 1px solid white;
			padding: 10px 20px;
			text-transform: uppercase;
			color: white;
			opacity: 0;
			@include transition(opacity, .6s);
		}
		&:hover {
			background-color: $corporate_1;
			img {
				opacity: .3;
			}
			span {
				opacity: 1;
			}
		}
	}
}