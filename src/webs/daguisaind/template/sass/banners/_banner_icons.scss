.banner_icons_wrapper {
  text-align: center;
  padding: 60px 0 40px;

  .banner_icons_content {
    display: inline-block;
    vertical-align: middle;

    .title {
      text-align: left;
      font-family: "DIN Alternate", sans-serif;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .banner_icons {
      text-align: center;

      .icon {
        display: inline-block;
        vertical-align: middle;
        padding: 20px;

        &:first-of-type {
          padding-left: 0;
        }

        &:last-of-type {
          padding-right: 0;
        }

        i {
          display: inline-block;
          vertical-align: middle;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: $corporate_1;
          color: white;
          font-size: 40px;
          position: relative;
          margin-right: 50px;

          &:before {
            @include center_xy;
          }

          &:after {
            content: '';
            @include center_y;
            left: 100%;
            height: 1px;
            width: 40px;
            background: $corporate_1;
          }
        }

        span {
          display: inline-block;
          vertical-align: middle;
          font-family: "Quicksand", sans-serif;
          font-size: 14px;
          letter-spacing: 1px;
          line-height: 16px;
        }
      }
    }

    .owl-nav.disabled {
      display: block !important;

      .owl-prev {
        left: -30px;
      }

      .owl-next {
        right: -30px;
      }

      @media (max-width: 1240px) {
        display: none !important;
      }
    }
  }

  .owl-carousel {
    .owl-item {

      .picture_wrapper {

        @media (min-width: 1500px) {
          height: 550px;
        }


        @media (max-width: 1499px) {
          height: 365px;
        }
      }


      &:not(.active) {

        .picture_wrapper {

          .caption {
            display: none;
          }
        }
      }
    }

    .owl-nav {
      width: 100%;

      div {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        i {
          font-size: 1.5rem;
          color: $corporate_1;
        }
      }

      .owl-prev {
        left: 0;
      }

      .owl-next {
        right: 0;
      }

      @media (max-width: 1160px) {
        display: none;
      }
    }

    .owl-dots {
      display: flex;
      justify-content: center;

      .owl-dot {
        background-color: $corporate_3;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin: 0 5px;

        &.active {
          background-color: lighten($corporate_3, 10%);
        }
      }
    }
  }
}


@media (max-width: 1295px) and (min-width: 1000px) {
  div.banner_icons_wrapper {
    .banner_icons_content {
      .banner_icons {
        .icon {
          i {
            width: 50px;
            height: 50px;
            font-size: 25px;
          }

          span {
            font-size: 11px;
            letter-spacing: 0;
          }
        }
      }
    }
  }
}