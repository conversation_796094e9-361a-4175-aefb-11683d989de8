@import "styles_mobile";

#full_wrapper_booking .room_list_wrapper .rooms_wrapper {
  width: 95%;
}

   


body.daguisa-goldenfenix{
    $corporate1: rgb(5, 28, 44);
    $corporate2: rgb(202, 70, 45);

    .normal_section_mobile{
        h2.section_title, .section-content{
            color: $corporate1;
            i{
                &::after{
                    background: $corporate1;
                 }
            }
        }
    }

    .location_wrapper{
        .location_content{
            .section-subtitle{
                    i::after{
                        background: $corporate2;
                     }

            }
        }

     }
    .default_reservation_text{
        div p{
            color: $corporate1;
        }
     }
    .section.my_reservation_section{
        .section-title{
            i{
                &::after{
                    background: $corporate2;
                }
            }
            color: $corporate1;
         }
     }

    .my-reservation-form{
           a[data-role=button]{
                background-color: $corporate1;
           }
     }

    #contact .contact_button_wrapper{
        #contact-button{
            background-color: $corporate1;
        }

     }
    .event_form_wrapper{
        #contact-button{
            background-color: $corporate2;
         }
        .container12{
            h3{
                color: $corporate1;
            }
        }
        form{
            .info{
                .contInput{
                       a{
                        color: $corporate1;
                       }
                 }
           }
        }
    }

    .promotions_wrapper .offer_content{

        .button-promotion{
            background-color: $corporate1;
        }
        .desc {
          margin-bottom: 50px;
        }
    }

    .default_content_wrapper{
        .content{
            .breadcrumbs{
                background-color: $corporate1;
            }
        }
    }

    .rooms_wrapper{
        .room_block{
            .buttons{
                .room_link{
                    background-color: $corporate2;
                 }

                 .button-promotion{
                    background-color: $corporate1;
               }
           }
         }

       }

    .mobile_engine .mobile_engine_action{
        background-color: $corporate2;
     }
    #full_wrapper_booking {
        .guest_selector{
            .placeholder_text, label{
                color: $corporate1;
            }

         }

        .dates_selector_personalized{
            .start_end_date_wrapper{
                .start_date_personalized, .end_date_personalized{
                    .day, .month, .year{
                        color: $corporate1;
                  }
               }
            }
         }

        .wrapper_booking_button .submit_button{
            &::after{
                background: $corporate2;

            }
        }

        #full-booking-engine-html-7 .wrapper-new-web-support.booking_form_title{
        background: $corporate2;

        }
    }

    .banner_checkin_wrapper .left_block {
        .title{
            color: $corporate2;
        }
        .desc ul li{
            &::before{
                color: $corporate2;
          }
        }
    }

    .newsletter_wrapper {
        &.right_content .newsletter_right .newsletter_right_item .title_right{
            color: $corporate1;
        }


        .newsletter_container{
            .newsletter_title{
                color: $corporate1;
                &::before{
                    background: $corporate2;
                }
            }
         }
        .social_newsletter a{
            &::after{
                background: $corporate2;
           }
            &::before{
                background: $corporate1;
            }
        }

        .newsletter_form .button_newsletter{
            &::after{
                background: $corporate2;
           }
     }
  }
  header #social-right a{
    &::after{
        background: $corporate2;
    }
    &::before{
        background: $corporate1;
    }
   }

   .banner_pics3_wrapper{
     .title{
         color: $corporate1;
         &::before{
            background: $corporate2;
        }
     }
   }

   .banner_gallery_new_wrapper{

    .banner_content{
        .text{
            color: $corporate1;
           }

        .title{
            color: $corporate1;
            &::before{
                background: $corporate2;
           }
       }
    }
   }

   .banner_pics3_wrapper{
       .title{
            span{
                color: $corporate1;
            }
        }

        .desc{
            color: $corporate1;
         }
   }

   .banner_map_wrapper{
        .title{
            color: $corporate1;
            &::before{
                background: $corporate2;
            }
        }

   }


   .main_menu{
        &.open_menu{
            .main_ul{
                li a{
                    color: $corporate1;
                 }
            }

         }

        .social_menu{
            a i{
                background-color: $corporate1;
                }

            .mailto{
                i{
                    background-color: $corporate1;
                  }
              }
          }
       }

    nav{
        a{
            &.active{
                &::after{
                    background: none;
                 }
            }
         }
        &#main_menu{
            div#social{
                a{
                    &::after{
                        background: $corporate2;
                     }
                    &::before{
                        background: $corporate1;

                     }
                }
            }
        }

        }



   header {
       background: $corporate1;

        #logoDiv {
            a{
                img{
                    width: 300px;
                }
            }
        }

        .toggle_menu span{
            background: $corporate1;
         }

       .right_header{
           white-space: nowrap;
           #lang {

               .language_selected, .language_selector{
                 color: $corporate1;
                }

             }

           }

       #header_menu a span{
            color: $corporate1;
        }

       nav#main_menu #main-sections-inner .main-section-div-wrapper a:before{
            background: $corporate2;
        }
   }

    .rooms_full_wrapper .rooms_wrapper .room .room_content{
        .text{
            color: $corporate1;

            & .btn{
                color: $corporate1;
            }
         }
        .room_icons .icon i{
            background: $corporate1;
         }
        .title{
            color: $corporate1;
            &::before{
                background: $corporate2;
            }
        }

      .button_wrapper{
        &::after{
            background: $corporate2;
       }
   }
   }

   .content_subtitle_wrapper {
       .content_subtitle_description{
            color: $corporate1;
        }

       .content_subtitle_title{
         color: $corporate1;

           i{
                &::after{
                    background: $corporate2;
                }
           }
       }
   }


   .offers_wrapper .offer .links_wrapper .button_promotion{
        &::after{
            background: $corporate2;
       }
   }

   .banner_cycle_gallery_new .content_wrapper .banner {
       .banner_gallery .owl-nav{
           .owl-prev, .owl-next{
                &:hover{
                    color: $corporate2;
                }
           }

       }

       .banner_content {
            .text{
                color: $corporate1;
            }
           .title{
           h4, big{
                color: $corporate1;
            }
            &::before{
                background: $corporate2;
            }

        }
      }
    }

    .btn_personalized_1{
        &::after{
            background: $corporate1;
        }
    }

    .gallery_filter_wrapper h3{
        border-bottom: 1px solid $corporate2;
     }


 #my-bookings-form #my-bookings-form-fields ul li{
     button{
         &.searchForReservation{
            background: $corporate2;
        }
        &.modify-reservation{
            background: $corporate1;
        }
     }
     }

    .banner_icons_content{
           .title{
                color: $corporate1;
              }
     }

    .banner_icons_wrapper .banner_icons_content .banner_icons .icon i{

        background: $corporate1;
     }

    .banner_pics3_wrapper .title h2{
        color: $corporate1;
    }

    .banner_checkin_wrapper{
        background: $corporate1;
    }

    footer{
        background: $corporate1;
     }


}
