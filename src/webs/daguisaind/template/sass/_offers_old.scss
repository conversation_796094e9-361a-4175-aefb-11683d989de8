.offers_filters {
  padding: 0 calc((100% - 1110px) / 2);
  .filter_wrapper {
    display: inline-block;
    vertical-align: top;
    border: 1px solid $black;
    margin-right: 20px;
    position: relative;
    font-family: "DIN Alternate", sans-serif;
    .select2 {
      width: 270px !important;
      .selection {
        .select2-selection {
          background: transparent;
          border-width: 0;
          height: auto;
          .select2-selection__rendered {
            text-align: center;
            padding: 7px 10px 7px 40px;
            text-transform: uppercase;
            font-size: 18px;
            color: white;
          }
          .select2-selection__arrow {
            display: none;
          }
        }
      }
    }
    a {
      display: block;
      padding: 10px 10px 10px 40px;
      color: $black;
      text-transform: uppercase;
      font-size: 18px;
      width: 270px;
      text-align: center;
    }
    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: transparent;
      border-radius: 0;
      border-width: 0;
      padding: 10px;
      padding-left: 80px;
      text-align: center;
      width: 250px;
      text-transform: uppercase;
      font-size: 20px;
    }
    &:before {
      position: absolute;
      top: 15px;
      @extend .fal;
      left: 15px;
    }
    &.cat_filter {
      @extend .fa-filter;
      background: $black;
      color: white;
    }
    &.off_filter {
      @extend .fa-badge-percent;
    }
    &.paq_filter {
      @extend .fa-gift;
    }
    &.active {
      background: #efefef;
    }
  }
  .result_search {
    float: right;
    text-align: right;
    font-size: 20px;
    padding-top: 20px;
  }
}
.offers_wrapper {
  text-align: center;
  padding: 30px 0;
  .offer {
    display: inline-block;
    vertical-align: top;
    width: calc((100% / 3) - 40px);
    max-width: 500px;
    margin: 0 15px 30px;
    padding-bottom: 60px;
    position: relative;
    &:before {
      content: '';
      @include full_size;
      z-index: -10;
      border: 1px solid rgba($black, .15);
    }
    .offer_pic {
      position: relative;
      img {
        width: 100%;
        vertical-align: middle;
      }
      .offer_title {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 30px 0;
        background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.0));
        font-size: 36px;
        font-weight: bold;
        color: white;
        text-transform: uppercase;
      }
    }
    .offer_desc {
      padding: 30px;
      text-align: left;
    }
    .offer_hotels {
      padding-bottom: 30px;
      img {
        max-height: 70px;
        vertical-align: middle;
        padding: 10px 20px;
      }
    }
    .button_promotion {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      float: left;
      font-family: "DIN Alternate", sans-serif;
      color: white;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-size: 28px;
      font-weight: 500;
      padding: 15px 0;
      overflow: hidden;
      &:after {
        content: '';
        @include full_size;
        z-index: -2;
        background: $corporate_2;
      }
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200%;
        height: 0;
        z-index: -1;
        background: rgba(0,0,0,0.3);
        -webkit-transform: translate(-50%,-50%) rotate(45deg);
        -moz-transform: translate(-50%,-50%) rotate(45deg);
        -ms-transform: translate(-50%,-50%) rotate(45deg);
        -o-transform: translate(-50%,-50%) rotate(45deg);
        transform: translate(-50%,-50%) rotate(45deg);
        @include transition(all,.6s);
      }
      &:hover {
        color: white;
        &:before {
          height: 500px;
          -webkit-transform: translate(-50%,-50%) rotate(30deg);
          -moz-transform: translate(-50%,-50%) rotate(30deg);
          -ms-transform: translate(-50%,-50%) rotate(30deg);
          -o-transform: translate(-50%,-50%) rotate(30deg);
          transform: translate(-50%,-50%) rotate(30deg);
        }
      }
    }
  }
}