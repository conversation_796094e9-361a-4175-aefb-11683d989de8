<div class="banner_cycle_gallery_new">
    <div class="content_wrapper">
        {% for banner in banner_cycle_gallery_new %}
            <div class="banner all">
                <div class="banner_content">
                    {% if banner.title %}<h4 class="title">{{ banner.title|safe }}</h4>{% endif %}
                    {% if banner.description %}
                        <div class="text">
                            {{ banner.description|safe }}
                            {% if '<modal' in banner.description %}
                                <a href="#" class="btn open_modal">
                                    {{ T_leer_mas }}
                                    <i class="fal fa-long-arrow-right see_more" aria-hidden="true"></i>
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                {% if banner.pics %}
                    <div class="banner_gallery owl-carousel">
                        {% for img in banner.pics %}
                            {% if img.servingUrl %}
                                <div class="image">
                                    <img src="{{ img.servingUrl|safe }}=s800">
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
        $(".banner_cycle_gallery_new").find(".banner_gallery").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            autoplay: false,
        });

        $(".banner_cycle_gallery_new").find(".open_modal").click(function(e){
            e.preventDefault();
            let target_html = $(this).closest(".banner").find(".text")[0].outerHTML;
            $.fancybox.open(target_html, {
                width: 750,
                autoSize: false
            });
        });
    });
</script>