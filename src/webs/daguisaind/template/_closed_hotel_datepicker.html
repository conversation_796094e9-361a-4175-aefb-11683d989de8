{% if period_closed_hotel %}
    <script>
        $(window).load(function () {
            var target_min_date,
                period_closed = JSON.parse('{{ period_closed_hotel|safe }}');

            DP_extend_info.config.period_closed = period_closed;

            for (var n = 0; n < period_closed.length; n++) {
                var closed_hotel = period_closed[n].close,
                    open_hotel = period_closed[n].open;
                if (closed_hotel && new Date(closed_hotel) <= new Date()) {
                    if (open_hotel && new Date(open_hotel) >= new Date())
                        target_min_date = new Date(open_hotel);
                }
            }
            if (!target_min_date) {
                target_min_date = new Date()
            }

            if (target_min_date) {
                DP_extend_info.config.datepicker_sd_wrapper.datepicker('option', 'minDate', target_min_date);
                DP_extend_info.config.datepicker_ed_wrapper.datepicker('option', 'minDate', target_min_date);
                DP_extend_info.format_dates($.datepicker.formatDate("dd/mm/yy", target_min_date));
                DP_extend_info.set_datepicker_start_date($.datepicker.formatDate("dd/mm/yy", target_min_date));
            }
        });
    </script>
{% endif %}