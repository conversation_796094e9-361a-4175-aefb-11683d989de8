<div class="rooms_wrapper">
    <div class="rooms">
        {% for room in rooms %}
            <div class="room">
                <div class="banner_content">
                    {% if room.title %}<div class="title">{{ room.title|safe }}</div>{% endif %}
                    {% if room.description %}<div class="desc">{{ room.description|safe }}</div>{% endif %}
                    <div class="icons">
                        {% for icon in room.room_icons %}
                            <div class="icon">
                                <i class="{{ icon.ico }}"></i>
                                <span>{{ icon.description|safe }}</span>
                            </div>
                        {% endfor %}
                    </div>
                    <a href="#data" class="button_promotion" {% if room.room_filter %}room_filter="{{ room.room_filter|safe }}"{% endif %}>{{ T_reservar }}</a>
                </div>
                <div class="room_gallery owl-carousel">
                    {% for pic in room.pics %}
                        <div class="pic">
                            <img src="{{ pic.servingUrl }}=s1900" {% if pic.altText %}alt="{{ pic.altText|safe }}"{% endif %}>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
       $(".room .room_gallery").owlCarousel({
            loop: false,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
            autoplay: false
        });
    })
</script>