<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        <div class="main-section-div-wrapper"
             {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
            {% if section.subsections %}
                <a>{{ section.title|safe }}</a>
            {% else %}
                {% if section.title %}
                    <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank"
                       {% else %}href="{{ host|safe }}{% if not section.title == "Home" %}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}{% endif %}"{% endif %}>
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a {% if subsection.replace_link %}href="{{ subsection.replace_link|safe }}" target="_blank"
                                {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"{% endif %}
                                   {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>