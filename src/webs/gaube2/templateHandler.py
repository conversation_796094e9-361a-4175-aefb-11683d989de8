# -*- coding: utf-8 -*-
import copy
from collections import OrderedDict

from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_language_title, get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "gaub2"


class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'wifi_gratis': get_section_from_section_spanish_name('wifi_gratis', language),
							  'language_selected': get_language_title(language),
							  'bannersx4': get_pictures_from_section_name('bannersx4', language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'bannersx2': {'title': get_section_from_section_spanish_name('bannersx2', language),
							  'pictures': get_pictures_from_section_name('bannersx2', language)}
		}

		if user_agent_is_mobile():
			result_params_dict["fontawesome5"] = True

		#Automatic Section
		automatic_sections = {
			'Mis Reservas': True,
			'Galeria de Imagenes': True,
			u'Atención al cliente': True
		}

		if automatic_sections.get(section_type, False):
			result_params_dict['content_access'] = True


		actual_section = get_section_from_section_spanish_name(section_name, language)
		if actual_section.get('subtitle', False):
			result_params_dict['content_subtitle'] = actual_section

		if advance_properties.get('mini_gallery', False):
			result_params_dict['mini_gallery'] = get_section_from_section_spanish_name(advance_properties['mini_gallery'], language)

		if advance_properties.get('vive_blocks', False):
			result_params_dict['vive_blocks'] = get_pictures_from_section_name(advance_properties['vive_blocks'], language)

		if section_type == 'Habitaciones':
				result_params_dict['rooms_blocks'] = get_pictures_from_section_name('habitaciones_blocks', language)
				for x in result_params_dict['rooms_blocks']:
					x['pictures'] = get_pictures_from_section_name(x['linkUrl'], language)

		if section_type == 'Ofertas':
			result_params_dict['ofertas'] = self.buildPromotionsInfo(language)

		if section_type == u'Localización':
			result_params_dict['normal_content'] = ''
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = self.picturesInSlider(language)
			additionalParams4Contact['privacy_checkbox'] = True
			additionalParams4Contact['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html = get_section_from_section_spanish_name(u"localización", language)
			iframe_google_map = get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form = sectionToUse['subtitle']


			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form
			result_params_dict['localizacion_access'] = True
			result_params_dict['location_title'] = get_section_from_section_spanish_name(u'localización', language)
			result_params_dict['location_links'] = get_pictures_from_section_name('location links', language)
			result_params_dict['content_subtitle'] = False

		if advance_properties.get("services_icon_block"):
			result_params_dict['services_icon_block_section'] = get_section_from_section_spanish_name(
				advance_properties.get("services_icon_block"), language)
			result_params_dict['services_icon_block'] = get_pictures_from_section_name(
				advance_properties.get("services_icon_block"), language)

		booking_widget_options = copy.deepcopy(self.getBookingWidgetOptions(language))
		booking_widget_options['price_calendar_v2'] = False
		result_params_dict['booking_engine_no_v2_calendar'] = self.buildTemplate('booking/booking_engine_3/_booking_widget.html', booking_widget_options)

		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['my_booking_integrated'] = True
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:

				nameFilter = x.get('title', "")
				if not nameFilter:
					nameFilter = ""

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [x['servingUrl']]
				else:
					filters[nameFilter].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		currentSection = self.getSectionParams(sectionFriendlyUrl, language)
		advance_properties = self.getSectionAdvanceProperties(currentSection, language)
		additionalParams['custom_elements'] = ''
		section_name = ''
		base_path = os.path.dirname(__file__)
		if currentSection:
			section_name = currentSection['sectionName'].lower().strip()

			if advance_properties.get("mini_gallery"):
				mini_dict = {
					'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("mini_gallery"), language)
				}
				mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
				additionalParams['custom_elements'] += mini_html

			if user_agent_is_mobile():
				if advance_properties.get("services_icon_block"):
					context = {
						'services_icon_block_section': get_section_from_section_spanish_name(advance_properties.get("services_icon_block"), language),
						'services_icon_block': get_pictures_from_section_name(advance_properties.get("services_icon_block"), language)
					}

					mini_html = self.buildTemplate_2("banners/_services_icon_block.html", context, False, "gaube2")

					additionalParams['custom_elements'] += mini_html

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)


