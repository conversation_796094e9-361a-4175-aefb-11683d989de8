@import "compass";


//Base web (change too in templateHandler and in config.rb)
$base_web: "gaub2";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #1e3f08;
$corporate_2: #DB9501;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined


@import "booking/booking_engine";
@import "booking/selectric";
@import "booking_engine";

@import "template_specific";
