<form name="contact" method="post" id="my-bookings-form">

    <input type="hidden" name="action" id="action" value="reservation"/>

    <div id="my-bookings-form-fields">
        <input type="text" id="emailInput" name="email" class="bordeInput" placeholder="{{ T_email }}" value=""/>
        <input type="text" id="localizadorInput" name="localizador" placeholder="{{T_localizador}}" class="bordeInput" value=""/>
        <button type="submit" id="my-bookings-form-search-button" onClick="javascript:searchForReservation();return false;">{{T_buscar|safe}}</button>
    </div>



</form>

<div id="reservation-cancellation-popup" style="display:none">
    <form>
        <label>{{ T_razones_cancelacion|safe }}</label>
        <textarea id="cancellation-reasons" cols="65" rows="10" style="display: block"></textarea>
    </form>
    <div id="cancellation-reservation-buttons">
        <button id="cancellation-confirmation-button">{{ T_confirmar_cancelacion }}</button>
    </div>
</div>





<script>
    function searchForReservation() {

        email = $('#emailInput').val();
        localizador = $('#localizadorInput').val();


        if (!email && !localizador) {
            emailArray = $("input[name='email']");
            localizadorArray = $("input[name='localizador']");

            email = '';
            localizador = '';

            for (i = 0; i < emailArray.length; i++) {
                aux = $(emailArray[i]).val();
                if (aux != '') {
                    email = aux;
                    break;
                }
            }

            for (i = 0; i < localizadorArray.length; i++) {
                aux = $(localizadorArray[i]).val();
                if (aux != '') {
                    localizador = aux;
                    break;
                }
            }


        }

        $.ajax({
            url: '/utils?action=searchReservation',
            data: {'email': email, 'localizador': localizador},
            success: function (data) {

                if (data != '') {
                    $('#reservation').html(data);
                    $("#reservation").css('display', 'block');
                    $('#cancelButton').css('display', 'block');
                } else {
                    alert("{{T_reserva_no_encontrada|safe}}");
                }

            }
        });
    }
</script>