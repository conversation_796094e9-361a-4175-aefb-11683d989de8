{# Offers #}
<div class="cycle_banners_wrapper offers_wrapper">
    <div class="container12">
    {% for x in offers %}
        <div class="cycle_element {% cycle 'align_left' 'align_right' %}">

                <div class="exceded"><img src="{{ x.picture|safe }}"></div>
                <div class="cycle_text_wrapper">
                    <div class="center_div">
                        <h3 class="cycle_title">{{ x.name|safe }}</h3>
                        <div class="cycle_description">
                            {{ x.description|safe }}
                            <a href="#data" class="button-promotion" {% if x.linked_section %}style="border-bottom: 0"{% endif %}>{{ T_reservar }}</a>
                            {% if x.linked_section %}<a href="{{ x.linked_section|safe }}" style="border-top: 0;margin-top:0;">{{ T_ver_mas }}</a>{% endif %}
                        </div>
                    </div>
                </div>

        </div>
    {% endfor %}
    </div>
</div>

<script>
    $(".cycle_element").each(function(){
       if ($(this).find(".hide_book").length){
           $(this).find(".button-promotion").css('display', 'none');
       }
    });
</script>