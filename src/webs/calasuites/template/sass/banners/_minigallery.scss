.minigallery_wrapper {
  width: 1140px;
  margin: 0 auto;
  overflow: hidden;
  padding: $vertical_padding 0 0;
  .owl-item {
    height: 300px;
    overflow: hidden;

    img {
      width: auto;
      opacity:1;
      @include transition(all,.6s);
    }
    span {
      display: block;
      width: 90%;
      color: white;
      font-size: 20px;
      text-align: center;
      text-shadow: 0 0 5px rgba(0,0,0,.6);
      @include center_xy;
      i.fa {
        display: block;
        text-align: center;
        font-size: 25px;
      }
    }

    &:hover {
      img {
        opacity: .8;
      }
      .minigallery_desc {
        img {
          opacity: .9;
        }
      }
    }
    .minigallery_desc {
      img {
        opacity: .8;
      }
    }
  }

  .owl-nav {
    display: block !important;
    & > div {
      @include center_y;
      color: white;
      cursor: pointer;
      font-size: 25px;
    }
    .owl-prev, .owl-next {
      width: 100px;
      height: 100px;
      color: white;
      @include transition(all, .6s);
      i.fa {
        font-size: 48px;
        @include center_xy;
      }
      &:hover {
        color: $corporate_1;
      }
    }
    .owl-prev {
      left: -20px;
      i:before {
        content: "\f104";
      }
    }
    .owl-next {
      right: -20px;
      i:before {
        content: "\f105";
      }
    }
  }
}