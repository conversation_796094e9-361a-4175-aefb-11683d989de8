<header>


    <div id="wrapper-header" class="container12">

        <div id="logoDiv">
            <a href="{{host|safe}}/">
                <img src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>

        <div class="middle-header">
            <div class="top-row">

                <div class="text_official"><span class="official">{{ official_text.content }}</span>

                <div id="lang">
                    {% for key, value in language_codes.items %}
                        <a href="{% if key == 'es' %}/{% else %}{{ hostWithoutLanguage }}/{{ key }}/{% endif %}" {% if  key == language %} class="selected" {% endif %}>
                           <span class="option">{{ value|upper|slice:":3"|safe }}</span>
                        </a>
                        {% if not forloop.last %}<span class="separator">|</span>{% endif %}
                    {% endfor %}
                </div>
                </div>
            </div>

            <div class="bottom-row">
                <div id="social" class="column2">
                    {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/facebook.png?v=1" width="32" height="32"> </a>
                    {% endif %}
                    {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/twitter.png?v=1" width="32" height="32"> </a>
                    {% endif %}
                    {% if flickr_id %}
                    <a href="http://www.flickr.com/photos/{{flickr_id}}/" target="_blank"> <img src="/img/{{ base_web }}/social/flickr.png?v=1" width="32" height="32"> </a>
                    {% endif %}
                    {% if youtube_id %}
                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank"> <img src="/img/{{ base_web }}/social/youtube.png?v=1" width="32" height="32"> </a>
                    {% endif %}
                </div>


                <div id="top-sections" class="column4">
                    {% for section in top_sections %}
                        <a {% if section.friendlyUrl == 'blog.html' %}href="http://www.hotelcasadonfernando.com/" target="_blank"{% else %}href="{{ host|safe }}/{{ section.friendlyUrl }}"{% endif %} {% if section.title|safe == sectionToUse.title|safe %}class="header_active_section"{% endif %} >
                            <span>{{ section.title|safe }}</span>
                        </a>
                        {% if not forloop.last %}<a class="separator">|</a>{% endif %}
                    {% endfor %}
                </div>
            </div>

        </div>

    </div>

</header>


<nav id="main_menu">
    <div id="mainMenuDiv" class="container12">
        {% include "main_div.html" %}
    </div>
</nav>