{% extends "__base.html" %}

{% block content %}
<!--Header-->
 {% include "header.html" %}

{% block "slider" %}

{% if not interior %}
    <section id="slider_container">
        {% include "slider_revolution.html" %}
        <div id="wrapper_booking" class="container12">
            <div class="booking_widget">
                {{ booking_engine }}
            </div>
        </div>

        <div id="slider_map_container">
            {% if iframe_map %}<div class="iframe_map_wrapper">{{ iframe_map.content|safe }}</div>{% endif %}
        </div>
        <div class="container12">
            <div class="maps_switcher">{{ T_ver_en_mapa }}</div>
        </div>
    </section>
    <script>
        $(window).load(function () {
            $(".maps_switcher").click(function(){
                if ($(this).hasClass("active")) {
                    $(".forcefullwidth_wrapper_tp_banner").show();
                    $("#wrapper_booking").show();
                    $("#slider_map_container .iframe_map_wrapper").hide();
                    $(this).text("{{ T_ver_en_mapa }}");
                    $(this).removeClass("active");
                } else {
                    $(".forcefullwidth_wrapper_tp_banner").hide();
                    $("#wrapper_booking").hide();
                    $("#slider_map_container .iframe_map_wrapper").show();
                    $(this).text("{{ T_volver_atras }}");
                    $(this).addClass("active");
                }
            });
        });
    </script>


{% else %}

    <section id="slider_container">
        <div class="slider_inner_container">
            <img class="slider_image" src="{% if slider_image %}{{ slider_image.0.servingUrl }}{% else %}{{ pictures.0.servingUrl }}{% endif %}=s1600">
        </div>
        <div id="wrapper_booking" class="container12">
            <div class="booking_widget">
                {{ booking_engine }}
            </div>
        <span class="slider_text">{% if slider_image %}{{ slider_image.0.description|safe }}{% else %}{{ pictures.0.description|safe }}{% endif %}</span>
        </div>

        <div id="slider_map_container">
            <div class="map default_hide" id="map-canvas" style="height:600px;">

            </div>
        </div>

    </section>

{% endif %}

<div id="ticks-container">
    <div class="ticks" id="tick1">
        <img src="/img/{{ base_web }}/ticks/euro.png?v=1"> <span>{{ T_pago_directo }}</span>
    </div>


    <div class="ticks" id="tick2">
        <img src="/img/{{ base_web }}/ticks/pig.png?v=1"> <span>{{ T_sin_gastos }}</span>
    </div>


    <div class="ticks" id="tick3">
        <img src="/img/{{ base_web }}/ticks/shield.png?v=1"> <span>{{ T_reserva_segura }}</span>
    </div>
</div>

{% endblock %}

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
          {% include "_main_content.html" %}
    </div>
</section>

{% endblock %}


{% include "footer.html" %}


{% endblock %}