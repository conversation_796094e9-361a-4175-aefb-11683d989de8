{#Normal Content#}
{% if general_content %}
    <div class="normal_content{% if quita_huecos %} gallery{% endif %}">
        <h3 class="section_title">{{ general_content.subtitle|safe }}</h3>
        {{ general_content.content|safe }}
    </div>
{% endif %}



{#Normal Content#}
{% if normal_content %}
    <div class="normal_content{% if quita_huecos %} gallery{% endif %}">
        {% if flex_slider %}

            <div class="flexslider">
                <ul class="slides">
                    {% for slideFlex in flex_slider %}
                        <li>
                            <img src="{{ slideFlex.servingUrl }}=s1600" class="flex_image">
                        </li>
                    {% endfor %}
                </ul>
            </div>

        {% endif %}
        {% if section_name %}<h3 class="section_title">{{ section_name|safe }}</h3>{% endif %}
        {{ content }}
    </div>
{% endif %}



{#Habitaciones#}
{% if rooms %}
    <div class="room_wrapper">
    {% for room in rooms %}
            <div class="rooms column6 {% cycle 'blockleft-room' 'blockright-room' %}"
                 id="room_block_{{ forloop.counter }}">
                <a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);"
                   class="rooms-img">
                    <img src="/img/{{ base_web }}/ico_fotos_blocks.png" class="ico_cam_room">
                    <img src="{{ room.pictures.0.servingUrl }}=s560" class="room_img">
                </a>

                <div class="rooms-description">
                    <h3 class="title-module"><span class="destino">{{ room.name|safe }}</span></h3>
                    {{ room.description|safe }}
                    <div class="room-links">
                        <span class="btn-corporate"><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                                                       class="button-promotion">{{ T_reservar }}</a></span>
                        <span class="btn-flecha"><a href="javascript:showGallery2([ {% for picture in room.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);" class="btn_vermas_room">{{ T_ver_fotos }} </a></span>
                    </div>
                </div>
            </div>
    {% endfor %}
    </div>
{% endif %}



{#Ofertas#}
{% if blocks %}
    <div class="scapes-blocks">
        {% for block in blocks %}
            <div class="block {% cycle 'row1' 'row2' %}">
                <div class="description">
                    <h3 class="title-module">{{block.name|safe}}</h3>
                    <ul>
                        <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva">{{ T_reservar }}</a> </li>
                        <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>
                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                    <img src="{{block.picture}}=s1000">
                </a>

                <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                    {{block.description|safe}}
                </div>
            </div>

        {% endfor %}
    </div>
{% endif %}


{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}


            {{ location_html.content|safe }}


        </div>

        <div class="form-contact column6">

            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>
    </div>


    <div class="iframe-google-maps-wrapper">
        <div id="slider_map_container">
            <div class="map" id="map-canvas" style="height:450px">

            </div>
        </div>
    </div>

{% endif %}




{#Mini Gallery#}
{% if mini_gallery %}
    <div class="gallery-mosaic container12">
    <h3 class="gallery_title">{{ mini_gallery_title.subtitle|safe }}</h3>
    <div class="gallery_container">
    <div class="gallery-smalls column4">
        {% for picture in mini_gallery|slice:":7" %}
            {% if not forloop.first %}

                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a href="{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe }}">
                        <img src="{{ picture.servingUrl }}=s500" alt="">
                    </a>
                </div>


            {% else %}


                <div class="gallery-mosaic-item {% if forloop.first %}minigallery-last-item{% endif %}">
                    <a class="youtube_video" href="{% if picture.linkUrl %}{{ picture.linkUrl }}{% else %}{{ picture.servingUrl }}=s800" rel="lightbox[gallery]" title="{{ picture.name|safe }}{% endif %}">
                        <img src="{{ picture.servingUrl }}=s1600" alt="">
                    </a>
                </div>
                </div>
                <div class="gallery-big column8">

            {% endif %}

        {% endfor %}
        </div>
        </div>
</div>
{% endif %}


{# Banner of Hotels #}
{% if banners_hotels %}
    <div class="all_hotels banners_hotel_wrapper">
        {% for x in banners_hotels %}
            <a href="{{ x.linkUrl }}">
                <div class="hotel_element column4">
                    <div class="exceded">
                        <img src="{{ x.servingUrl }}">
                    </div>
                    <div class="text">
                        <p class="ban_title">{{ x.title|safe }}</p>
                        <p class="ban_description ban_title">{{ x.description|safe }}</p>
                    </div>
                </div>
            </a>
        {% endfor %}
    </div>
{% endif %}