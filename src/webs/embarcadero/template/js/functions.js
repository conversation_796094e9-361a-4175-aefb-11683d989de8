$(function () {

    $("#pikame").PikaChoose({showTooltips: true, carousel: false});

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });
    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });


    $(".button-promotion").fancybox({
        width: 300,
        maxwidth: 300,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });


    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');


    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}
});

(function () {
    var po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
})();


function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}
