//General

body {
  font-family: '<PERSON>';
  width: 100%;
  min-width: 1140px;
  overflow-x: hidden;
}

h3#title {
  text-transform: uppercase;
  color: $corporate_1;
  padding: 0px 59px 17px;
  display: table;
  font-size: 22px;
  margin: 0 auto 11px;
  font-weight: 100;
  background-size: 11px;
  width: 100%;
  box-sizing: border-box;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

//**************** Header *************//

header {
  width: 100%;
  height: 101px;
  color: white;
  overflow: visible;
  background: rgb(245, 245, 245);

  #wrapper-header {
    position: relative;
    z-index: 22;
    height: 50px;
  }

  a {
    color: $corporate_2;
    text-decoration: none;
  }

}

#logoDiv {
  margin-top: 0px;
  float: left;
  height: 101px;
  width: 240px;

  img {
    width: 100%;
  }
}

.middle-header {
  margin-right: 0px !important;
  margin-left: 60px;
}

.top-row, .bottom-row {
  overflow: auto;
}

.bottom-row {
  margin-top: 7px;
}

.text_official {
  padding-top: 13px;
  .official {
    font-weight: lighter;
    padding-left: 75px;
    display: block;
    float: left;
    padding-top: 2px;
  }
}

#lang {
  float: right;
  text-transform: uppercase;
  margin-right: 0px;
  font-weight: 400;
  font-size: 21px;

  .option {
    opacity: 0.8;
    cursor: pointer;
  }

  .selected .option {
    opacity: 1;
  }

  span.separator {
    margin-left: 0px;
    margin-right: 0px;
    font-weight: lighter;
    color: $corporate_2;
  }

  a {
    color: $corporate_2;
    font-weight: 100;

    &.selected {
      font-weight: bolder;
    }
  }
  :not(.selected) {
    opacity: 0.8;
  }

  span:first-child {
    padding-right: 5px;
    opacity: 1;
  }

  a span {
    padding-right: 0px;
  }
}

.web-oficial.interior {
  width: 1140px;
  margin: 0 auto;
  padding: 0px;
  font-size: 13px;
  background: $corporate_1;

  img {
    width: 20px;
    height: 20px;
    padding-top: 3px;
  }

  .tick_wrapper {
    padding-top: 4px;
    span {
      color: white !important;
    }
  }

}

.tick_wrapper {
  float: left;
  font-family: 'Source Sans Pro', sans-serif;
}

.tick_wrapper img {
  width: auto;
  height: 23px;
  padding-top: 4px;
}

.tick_center {
  display: table-caption;
  margin: 0 auto;
}

.en .web-oficial {
  width: 245px;
}

#social {
  width: auto;
  float: right;
  margin-right: 0px !important;

  img {
    width: 40px;
    height: 40px;
  }

  a:hover {
    opacity: 0.8;
  }
}

#top-sections {
  margin-top: 6px;
  text-align: right;
  margin-right: 0px !important;
  font-size: 18px;
  width: 525px;
  font-weight: lighter;

  a span {
    text-transform: uppercase;
    font-size: 16px;
  }

  a:hover, a.header_active_section {
    opacity: 0.8;
  }
}

#main_menu {
  background: $corporate_2;
  height: 40px;
}

#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  height: 40px;
  box-sizing: border-box;
}

#mainMenuDiv a {
  padding: 0px 0px 0px;
  text-decoration: none;
  color: white;
  display: inline-block;
  font-size: 17px;
  font-weight: 100;
  text-transform: uppercase;

  &:hover:not(.button_promotion) {
    padding-top: 0px;
    padding-bottom: 0px;
    opacity: 0.8;
  }
}

#mainMenuDiv a.button_promotion:hover {
  opacity: 0.8;
}

#section-active a {
  color: white;
  padding-bottom: 0px;
  font-weight: bolder;
}

#main-sections-inner {
  height: 34px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-top: 8px;

  ul {
    display: none;
  }

  li ul {
    position: absolute;
  }

  div li {
    float: none;
    display: block;
  }

  div ul {
    position: absolute;
  }

  div:hover > ul {
    display: block;
  }

  & > div {
    display: inline-block
  }
}

.main-section-div-wrapper {
  display: inline;
  text-align: justify;

  a {
    line-height: 23px;
    font-size: 15px;

    &:hover {
      color: $corporate-2;
    }
  }
}

//Top_menu//
span.separator {
  margin-left: 6px;
  margin-right: 2px;
}

a.separator {
  margin-left: 3px;
  color: $corporate_2;
}

.book_menu {
  color: white;
  font-weight: bolder;
}

.middle-header {
  float: right;
  width: 725px;
}

/*============ slider Container ===============*/

.tp-banner-container {
  width: 100% !important;
  min-width: 1140px !important;
}

section#slider_container {
  position: relative;
  min-height: 550px;
}

section#slider_container iframe {
  width: 100% !important;
}

.slider_inner_container .slider_image {
  width: auto;
  height: auto;
  position: fixed;
  max-width: none;
  min-width: 100%;
  top: 0px;
  z-index: -2;
  min-height: 715px;
}

span.slider_text {
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin: auto;
  height: 80px;
  display: table-caption;
  color: white;
  text-transform: uppercase;
  font-size: 77px;
  font-weight: 200;
  line-height: 65px;
}

/*=============== Booking Widget ================*/

div#wrapper_booking {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 435px;
}

.interior #wrapper_booking {
  height: 490px;
}

.spinner_wrapper {
  text-align: center;
  position: relative !important;
  width: 25px;
  height: 25px;
  margin: auto;
  right: 0px !important;
  bottom: 0px !important;
  padding: 15px;
  display: none;
}

.interior #wrapper_booking {
  height: 500px;
  bottom: auto;
}

.fancybox-inner {
  overflow: initial;
}

.border_booking {
  padding: 0px 10px 10px;
  height: auto !important;
  width: 370px !important;
  box-sizing: border-box;
  background: white;
}

#data {
  height: auto !important;
}

.booking_widget, #data {
  left: 0px;
  top: 0px;
  bottom: 0px;
  margin: auto;
  height: 405px;

  .booking_form {
    height: auto;
    box-sizing: border-box;
    margin: 0px !important;
    padding: 0px;
    width: 350px;

    .stay_selection {
      display: table;
      text-align: center;
      margin: 0 auto;
      padding-top: 20px;
    }

    .wrapper_booking_button button {
      border-radius: 0px;
      width: 245px;
      float: none;
      margin: 8px auto 0px;
      display: block;
      font-weight: lighter;
      font-size: 20px;
      font-family: "Source Sans Pro", sans-serif;
      background: $corporate_1 url("/static_1/images/booking/flecha_motor_der.png") no-repeat scroll 95% 50%;
      height: 40px;
      margin-bottom: 12px;
      cursor: pointer;
    }
  }

  .best_price {
    display: none;
  }

  h4.booking_title_2 {
    display: block;
    font-family: 'Oswald', sans-serif;
    text-transform: uppercase;
    font-weight: 300;
    font-size: 26px;
  }

  .booking_form_title {
    background: $corporate_1;
  }

  .booking_form_title:before {
    border-top: 8px solid $corporate_1;
    bottom: -7px;
  }

  .date_box {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    &:before {
      border-bottom: 8px solid rgb(234, 234, 234);
    }

    .date_day .day {
      border-bottom: 2px solid $corporate_1;
      font-weight: bolder;
      line-height: 50px;
      font-size: 54px;
    }

    .date_day .month {
      border-bottom: 2px solid $corporate_1;
    }
  }

  .selectric {
    background: rgb(234, 234, 234);
    border-radius: 0px;

    &:before {
      border-bottom: 8px solid #EAEAEA;
      top: -6px;
    }

    .button {
      display: none;
      background: url(/static_1/images/booking/arrow_down_big.png) no-repeat center center !important;
    }
  }

  .selectricItems {
    li.selected {
      background: #EFEFEF;
      color: #444;
      border-top-color: #E0E0E0;
    }

    li {
      font-weight: 500;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
      color: #a5a5a5;
      font-size: 25px;
      line-height: 40px;
      border-bottom: 1px solid #fff;
      padding: 10px 0px;
      width: 100%;
      text-align: center;

      &:hover {
        background: #EFEFEF;
        color: #444;
        border-top-color: #E0E0E0;
      }
    }

    ul {
      z-index: 40;
    }

    .room {
      padding-top: 17px;
    }
  }

  .room_title {
    padding-left: 30px;
  }

  .adults_selector {
    padding-left: 19px;
  }

  .promocode_input {
    background: rgb(234, 234, 234);
    width: 245px !important;
    margin-left: 51px;
    border-radius: 0px !important;
    padding-top: 11px;
    box-sizing: border-box;
  }

  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .wrapper_booking_button .promocode_text {
    color: rgb(134, 134, 134);
  }

  .promocode_text {
    font-size: 13px;
  }
}

.wrapper-new-web-support {
  opacity: 1 !important;
  border-radius: 0px !important;

  &:before {
    content: none;
  }
}

.interior .booking_widget {
  margin: 50px auto;
}

.booking_widget.location {
  right: 20.2vw;
}

.ui-widget-header {
  background: $corporate_1 url(images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x;
  border: 1px solid $corporate_1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  border: 1px solid $corporate_1;
  background: $corporate_1 url(images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x;
  font-weight: bold;
  color: white;
}

.ui-state-hover {
  background: $corporate_1;
}

.booking_form_title {
  background: $corporate_1;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding: 9px 0;
  position: relative;
}

.wrapper-new-web-support.booking_form_title {
  background: rgba(0, 0, 0, 0.64) !important;
}

.room {
  display: flex;
  align-items: flex-end;
  align-content: center;
  flex-wrap: nowrap;
  flex-direction: row;
  justify-content: center;
  padding-top: 50px;
  padding-left:0;

  .room_title {
    top:-45px;
    padding: 0;
  }
  .adults_selector {
    padding: 0;
  }
  .babies_selector {
    margin-left: 2px;
     .selectric .button {
      display: inherit;
    }
    .label {
      width: 70%;
    }
  }
  .adults_selector, .children_selector, .babies_selector {
    label {
      text-align: center;
      margin: 6px 0px 13px;
    }
  }
}


/*============ Mapas ===========*/

.maps_switcher, .street_switcher {
  position: absolute;
  bottom: 0px;
  z-index: 22;
  background: rgba(81, 81, 81, 0.74);
  text-align: center;
  width: 349px;
  color: white;
  box-sizing: border-box;
  text-transform: uppercase;
  font-size: 22px;
  padding: 10px;
  cursor: pointer;

  &:hover {
    background: $corporate_3;
  }
}

.street_switcher {
  bottom: 48px;
}

#street_view_container {
  height: 550px;
}

#map-canvas {
  height: 550px !important;

  * {
    max-width: none;
  }
}

#map-canvas.default_hide {
  display: none;
}

.interior #map-canvas {
  height: 510px !important;
}

#slider_map_container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .iframe_map_wrapper {
    display: none;
    width: 100%;
    height: 100%;

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}

/*========== Ticks Container ========*/
#ticks-container {
  text-align: center;
  overflow: hidden;
  background: $corporate_1;
  color: white;
  font-size: 13px;
  box-sizing: border-box;
  height: 54px;
  padding: 10px 0;

  .ticks {
    display: inline-block;
    line-height: 15px;
    text-align: left;
    font-weight: 100;
    padding-left: 6px;
    width: auto;

    span {
      display: block;
      float: right;
      padding-top: 10px;
    }

    img {
      vertical-align: middle;
    }
  }

  span.separator {
    font-size: 21px;
    vertical-align: middle;
    height: 5px;
    display: inline-block;
    margin-top: -20px;
  }

  div#tick1 {
    width: auto !important;
  }

  div#tick2 {
    width: auto !important;
  }
}

//******************* Content *******************//

section#content {
  background: white;
}

.normal_content {
  padding-top: 60px;
  text-align: center;
  font-size: 17px;
  line-height: 29px;
  width: 600px;
  font-weight: 100;
  margin: auto;
  display: table;

  strong {
    color: $corporate_1;
  }

  .section_title, .section-title {
    text-transform: uppercase;
    color: $corporate_1;
    padding: 0px 59px 17px;
    display: table;
    font-size: 22px;
    margin: 0 auto 11px;
    font-weight: 100;
    background-size: 11px;
    width: 100%;
    box-sizing: border-box;

    strong {
      font-weight: 300;
    }
  }

}

.interior .normal_content {
  margin-top: 30px;
  background: rgba(255, 255, 255, 0.8);
  padding: 25px;
  box-sizing: border-box;
  margin-bottom: 30px;
}

////*Gallery Mosaic*///////

.gallery_title {
  padding-top: 60px;
}

.gallery_title, .services_title {
  text-transform: uppercase;
  color: $corporate_1;
  padding: 0px 59px 17px;
  display: table;
  font-size: 22px;
  margin: 0 auto 16px;
  font-weight: 100;
  background-size: 11px;

  strong {
    font-weight: 300;
  }

}

.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 137%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;
  height: 360px;

  img {
    width: 100%;
    height: 97%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 17px auto 0px;
}

.gallery_container {
  margin-left: 22px;
}

/*========== Hotel Banners ==========*/
.all_hotels {
  display: table;
  margin: 60px auto;

  .ban_title {
    background: rgb(125, 125, 125);
    color: white;
    padding-bottom: 0px !important;
    padding-top: 15px !important;
  }

  .ban_description {
    padding-top: 0px !important;
    padding-bottom: 15px !important;
  }

  .exceded {
    margin-bottom: -4px;
    height: 182px;
    overflow: hidden;

    img {
      width: 100%;
    }
  }

  .hotel_element {
    .text p {
      padding: 13px 10px 10px;
      text-align: center;
      font-weight: 100;
      font-size: 22px;

      strong {
        font-weight: 400;
      }
    }
  }

  p.ban_description.ban_title {
    font-weight: 100;
    font-size: 15px;
  }

}

/*====== Footer =====*/
footer {
  background: $corporate_1;
  color: white;
  font-weight: 100;
  font-size: 15px;
  padding: 35px 0px 50px;

  a {
    text-decoration: none;
    color: white;
  }
}

.footer_column {
  text-align: center;

  h3.footer_column_title {
    font-weight: 300;
  }

  label#suscEmailLabel, h2#title_newsletter {
    display: none !important;
  }

  form.newsletter_form {
    margin-top: 22px;
  }

  .newsletter_container {
    width: auto;
  }

  input#suscEmail {
    width: 310px;
    font-size: 19px;
    padding: 12px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.5);
    border: 0;
  }

  .button_newsletter {
    width: 310px;
    height: 35px;
    font-size: 24px;
    margin-top: 9px;
    margin-left: 25px;
    font-family: 'Oswald';
    padding-top: 12px;
    text-transform: uppercase;
    color: $corporate_1;
    background-color: white;
    border: 0px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  .newsletter_checkbox a {
    text-decoration: underline;
  }

  div#footer_column_description {
    line-height: 26px;

    a:hover {
      opacity: 0.8;
    }
  }
}

.wrapper_footer_columns {
  margin-bottom: 30px;
}

.footer-copyright {
  a:hover {
    opacity: 0.8;
  }
}

.full-copyright {
  text-align: center;
}

div#div-txt-copyright {
  margin-bottom: 10px;
}

/*======= Habitaciones ======*/

.room_wrapper {
  margin-top: 70px;
}

div#description-main-section.content_rooms {
  box-sizing: border-box;
  margin-top: 0px;
}

section#top_content {
  padding-top: 200px;
}

.rooms-description {
  background: rgba(245, 245, 245, 0.8);
  padding: 20px;
  min-height: 200px;
  color: grey;
  position: relative;

  .destino {
    font-weight: 700;
    font-family: 'Oswald', sans-serif;
  }

  .title-module {
    font-size: 23px;
    color: $corporate_1;
    margin-bottom: 20px;

  }
}

.description-rooms {
  font-weight: lighter;
}

h4.title-module {
  font-size: 23px;
  color: $corporate_3;
  margin-top: 10px;
  margin-bottom: 5px;
}

.rooms {
  margin-bottom: 25px;
  position: relative;
}

.blockleft {
  margin-left: 0px;
}

.blockright {
  margin-right: 0px;
  margin-left: 30px;
}

.sub-description-rooms {
  margin: 10px 0 20px;
  font-weight: bold;
}

span.btn-corporate {
  position: absolute;
  top: 0;
  right: 0;
}

span.btn-corporate {
  position: absolute;
  top: 16px;
  right: 50px;
  text-transform: uppercase;
  color: white !important;
  background-color: $corporate_3;
  margin-right: 20px;
}

span.btn-corporate {
  padding: 6px 8px;
}

.btn-flecha {
  background-color: $corporate_1;
  height: 22px;
  padding: 5px 10px 9px;
  position: absolute;
  width: 10px;
  top: 16px;
  right: 15px;
  text-align: center;
}

.rooms {
  .room_img {
    width: 100%;

  }
  .ico_cam_room {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 20;
  }
  span.btn-corporate {
    right: 100px;
  }
  .btn-flecha {
    width: 70px;
    cursor: pointer;
    font-family: 'Oswald';

    &:hover {
      opacity: 0.8;
    }
  }
}

a.rooms-img {
  height: 220px;
  display: block;
  overflow: hidden;
  position: relative;
}

img.room_img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

a.btn_vermas_room {
  color: white !important;
  font-weight: 700;
  margin-left: 1px;
  text-align: center;
}

.room-links {
  a.button-promotion {
    color: white !important;
  }

  .btn-corporate:hover {
    opacity: 0.8;
  }
}

.blockleft-room {
  margin-left: 0px;
  margin-right: 30px;
}

.blockright-room {
  margin-right: 0px;
}

.myFancyPopupRooms {
  margin-left: 20px;
  margin-right: 20px;
  width: 550px;
}

.room-links a {
  text-decoration: none;
}

//************* Galeria *************//

//VIDEO ON GALLERY
#video_corporativo {
  position: relative;
  z-index: 9;
  width: 567px;
  height: 380px;
  left: 2px;

  iframe {
    width: 566px !important;
    height: 376px !important;
    padding-top: 2px;
  }
}

.visita_or_video_mobile {
  float: left;
}

// SPECIAL SEPARATION IN GALLERY SEPARATION

.main-gallery {
  padding: 0px !important;
  background-color: transparent !important;
}

.gallery_1 li .crop {
  width: 283px !important;
  height: auto;
  overflow: hidden !important;
  border: 0px solid white !important;
  box-sizing: border-box !important;
}

.gallery_1 li .crop img {

  min-height: 190px !important;
  min-width: 285px !important;
}

.normal_content.gallery {
  padding: 0px;
  width: 100%;
  margin-top: 56px
}

.section_title {
  font-size: 77px;
  font-weight: 200;
  color: #15abc3;
  text-align: center;
  width: 700px;
  margin: 25px auto 55px;
  line-height: 65px;
}

.gallery_1 li .crop {
  width: 283px;
  overflow: hidden;
  img {
    display: block;
    height: 212px;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery_1 li {
  margin: 1px;
  float: left;
  height: 212px;
}

ul.gallery_1 {
  margin: 12px 0px 35px;
  display: table;
}

.gallery_1 li:first-of-type {
  height: auto;
  width: 568px;

  .crop {
    width: 100% !important;
    height: auto !important;
    img {
      width: auto !important;
      height: 426px !important;
    }
  }
}

.interior .tp-banner-container {
  height: 669px !important;
}

.interior .tp-revslider-mainul {
  position: fixed !important;
  z-index: -30;
}

/*========= Ofertas =======*/
a.plus {
  padding: 7px 8px 9px !important;
}

a.play {
  padding: 10px 9px 5px !important;
}

.scapes-blocks {
  overflow: hidden;
  margin-top: 30px;
  margin-bottom: 10px;
}

.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;

  a img {
    margin-bottom: -5px;
    width: 100%;
  }

  .description {
    padding: 20px;
    position: relative;
    background-color: rgba(245, 245, 245, 0.8);
    padding-right: 160px;

    .title-module {
      font-size: 23px;
      color: $corporate_1;
      font-weight: 700;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 115px;
      right: 0;
      top: 34px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline;
        a {
          background-color: $corporate_2;
          top: -1px;
          color: white;
          padding: 7px 7px 8px;
          right: 97px;
          position: absolute;
          text-align: center;
          text-decoration: none;
          height: 21px;
          &:hover {
            opacity: 0.8;
          }
        }
        a.plus {
          padding: 10px 7px 5px;
          margin-right: -75px;
          height: 20px;
          background: $corporate_1;
        }

        a.play {
          padding: 10px 9px 5px;

          img {
            margin-top: 2px;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_1;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}

//********** Mis Reservas **********//

#reservation {
  margin-left: 292px;
  margin-bottom: 20px;
}

#cancellation-confirmation-button {
  background: $corporate_1;
  border: 1px solid white;
  color: white;
  padding: 4px;
  font-size: 16px;
  border-radius: 4px;
  width: 200px;
  margin-top: 5px;
}

#cancelButton {
  display: none;
  text-align: center;
  margin: 20px auto 0px;
  background: #553a2b;
  color: white;
  border: 0px;
  cursor: pointer;
  padding: 7px 26px;
}

#my-bookings-form #hotelSelect {
  display: initial !important;
}

#description-main-section {
  background: rgba(252, 241, 235, 0.85);;
  margin-top: 200px;
  margin-bottom: 20px;
  padding: 40px;
  font-weight: 300;

  .section-title, h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }

  p {
    margin-bottom: 9px;
    color: black;
    font-weight: 300;
  }

  #my-bookings-form-fields {
    #my-bookings-localizador-label, #my-bookings-form-search-button {
      margin-left: 20px;
    }

    label {
      color: #000000;
      font-weight: 300;
    }

  }

  input {
    background: white;
    margin-bottom: 9px;
  }

  button {
    background: $corporate_1;
    border: 1px solid white;
    color: white;
    padding: 4px;
    font-size: 16px;
    width: 90px;
    border-radius: 4px;
  }

}

div#my-bookings-form-fields {
  width: 296px;
  margin: 20px auto;
  text-align: left;

  label {
    width: 80px;
    display: inline-block;
  }
}

input#localizadorInput, input#emailInput {
  width: 200px;
}

#cancelButton {
  width: 160px;
}

button#my-bookings-form-search-button {
  text-align: center;
  display: table;
  margin: 20px auto 0px;
  background: $corporate_3;
  color: white;
  border: 0px;
  cursor: pointer;
  padding: 7px 26px;

  &:hover {
    opacity: 0.8;
    background: $corporate_1;
  }
}

/************  Location and Contact  *************/

.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }
}

.iframe-google-maps-wrapper {
  display: none;
}

.location-info-and-form-wrapper {
  margin-top: 30px;
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 20px;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: $corporate_1;
  width: 95%;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;

}

li.how-to-go {
  cursor: pointer;
  color: $corporate_1;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
  .car {
    background: url("/img/amera/icons_maps/car.png") left center no-repeat;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-right: 5px;
}

.form-contact #contactContent span.title a{
  color: black;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate_3 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate_1 !important;
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

/*===== Client Service ====*/
div#contactContent {
  font-family: 'Oswald' !important;
}

.info {
  label {
    display: block;
    margin-top: 20px;
  }

  input {
    width: 300px;
    font-size: 20px;
  }

  textarea#comments {
    width: 300px;
    border-color: rgb(213, 212, 212);
  }

  input#accept-term {
    width: auto;
  }

  .contInput.policy-terms a {
    text-decoration: none;
    color: black;
  }

  button#contact-button {
    border-radius: 0px !important;
    height: 30px !important;
    width: 130px !important;
    background: #7d7d7d !important;
    color: white !important;
    margin: 30px auto 0 !important;
    text-transform: uppercase !important;
    border: 0px !important;
    text-align: center !important;
    font-size: 18px;
    padding: 0px !important;
    line-height: 32px;
    display: block;

    &:hover {
      background: $corporate_1 !important;
    }
  }
}

/*======= Social Likes ======*/
div#facebook_like {
  width: 49.8%;
  float: left;
  text-align: right;
}

div#google_plus_one {
  float: right;
  width: 49.8%;
}