# -*- coding: utf-8 -*-
from booking_process.libs.communication import directDataProvider
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "embao"

class TemplateHandler(BaseTemplateHandler2):

	def buildRoomsFromSections(self, language):
		all_rooms = get_pictures_from_section_name("habitaciones_blocks", language)
		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures'] = get_pictures_from_section_name(sect_gallery, language)
			room['name'] = room['title']
		return all_rooms

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		section_name_key = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']
			section_name_key = sectionToUse.get('key','')

		result_params_dict = {
			'base_web': base_web,
			'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
			'footer_columns': get_pictures_from_section_name("footer_columns", language),
			'banners_hotels': get_pictures_from_section_name('hotel_banners', language),
			'iframe_map': get_section_from_section_spanish_name("Iframe google maps", language)
		}

		try:
			section_subtitle = get_section_from_section_spanish_name(sectionToUse['sectionName'], language).get('subtitle', '')
		except:
			section_subtitle = ''

		if section_subtitle:
			result_params_dict['general_content'] = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)


		normal_content = {
			u'Mis Reservas': True,
			u'Galeria de Imagenes': True,
			u'Atención al cliente': True
		}
		result_params_dict['normal_content'] = normal_content.get(section_type, False)


		if section_type != 'Inicio':
			result_params_dict['interior'] = True
			result_params_dict['interior_background'] = get_pictures_from_section_name("background " + section_name, language)


		# Mini Gallery
		if section_name_key:

			mini_gallery_name = ''
			carousel_section = ''
			all_properties_section = directDataProvider.get("WebPageProperty", {"entityKey": section_name_key, "languageKey": language}, limit=50)

			for property in all_properties_section:
				if property.value:
					if property.mainKey == "mini_gallery":
						carousel_section = property.value

			if carousel_section:
				result_params_dict['mini_gallery'] = get_pictures_from_section_name(carousel_section, language)
				result_params_dict['mini_gallery_title'] = get_section_from_section_spanish_name(carousel_section, language)


		# Habitaciones
		if section_type == 'Habitaciones':
			all_rooms = self.buildRoomsFromSections(language)
			result_params_dict['rooms'] = all_rooms

		#Galeria de Imagenes
		if section_type == u'Galeria de Imagenes':
			result_params_dict['quita_huecos'] = True
			result_params_dict['slider_image'] = get_pictures_from_section_name(u"slider imagenes", language)

		#Ofertas
		if section_type == 'Ofertas':
			result_params_dict["blocks"] = super(TemplateHandler, self).buildPromotionsInfo(language)

		#Localizacion y contacto
		if section_type == u'Localización':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''

			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = get_section_from_section_spanish_name(u"Localización", language)
			result_params_dict['subtitle_form'] = sectionToUse['subtitle']
			result_params_dict['general_content'] = ''


		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"


	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)


	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['promo_double'] = True
		options['caption_submit_book'] = True
		return options


	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result['visita_or_video_mobile'] = get_section_from_section_spanish_name(u"Video corporativo", language)
			result['quita_huecos'] = True

		if result:
			return result

		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)


	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):

		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_with_videos.html'
		}
		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template


