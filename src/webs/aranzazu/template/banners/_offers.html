<div class="offers_wrapper" >
    {% if 'ecork' in namespace_group and offers_filter %}
        <div class="offers_filter">
            <p>{{ namespace_group }}</p>
            <a href="#all" class="selected">{{ T_todos }}</a>
            {% for class, label in offers_filter.iteritems() %}
                <a href="#{{ class|safe }}">{{ label|safe }}</a>
            {% endfor %}
        </div>
    {% endif %}
    {% for offer in offers %}
        <div class="offer all {% for class_f in offer.filter_class %}{{ class_f }} {% endfor %}">
            {% if offer.picture %}
                <div class="offer_pic">
                    <img src="{{ offer.picture }}=s1900" alt="{{ offer.name|safe }}">
                </div>
            {% endif %}
            <div class="offer_content">
                <span class="icon_styles_2">
                    <i class="{% if offer.title %}{{ offer.title|safe }}{% else %}icon-percentage{% endif %}"></i>
                </span>
                {% if offer.name %}<h4 class="title">{{ offer.name|safe }}</h4>{% endif %}
                {% if offer.description %}<div class="text">{{ offer.description|safe }}</div>{% endif %}
            </div>
            <div class="links">
                {% if offer.linkUrl %}
                    {% if namespace == 'ecorkhotel' %}
                        <a href="{{ offer.linkUrl|safe }}" class="see_more_button" {% if "http" in offer.linkUrl %}target="_blank" {% endif %}>{{ T_ver_mas }}</a>
                    {% else %}
                        <a href="{{ offer.linkUrl|safe }}" class="icon_link with_btn_booking" {% if "http" in offer.linkUrl %}target="_blank" {% endif %}><i class="fal fa-plus"></i></a>
                    {% endif %}
                {% endif %}
                {% if not offer.extra_properties.hide_booking_button %}
                    <a href="#data" class="button_promotion btn btn_primary"
                       {% if offer.smartpackage %}data-smartpackage="true"{% endif %}
                       {% if offer.smartdateini %}data-smartdateini="{{ offer.smartdateini|safe }}"{% endif %}
                       {% if offer.smartdatefin %}data-smartdatefin="{{ offer.smartdatefin|safe }}"{% endif %}
                    ><span>{{ T_reservar }}</span></a>
                {% endif %}
            </div>
        </div>
    {% endfor %}
</div>
<script>
$(window).on("load", function() {
    $(".offers_filter a").click(function (e) {
        e.preventDefault();
        $(".offers_filter a").removeClass('selected');
        $(this).addClass('selected');
        let target_class = $(this).attr("href").replace("#","");
        if(target_class != "all"){
            $(".offer").fadeOut();
            $(".offer."+target_class).fadeIn();
        } else {
            $(".offer").fadeIn();
        }
    });
});
</script>
{% if not is_mobile and not user_isIpad %}
    <script>
        $(window).load(function () {
            function offers_fx() {
                for (x = 2; x <= $(".offer").length + 1; x++) {
                    $(".offers_wrapper .offer:nth-child(" + x + ")").addnimation({parent:$(".offers_wrapper"), class:"fadeInUp", delay: x * 80, reiteration: false});
                }
            }
            offers_fx();
            $(window).scroll(offers_fx);
        });
    </script>
{% endif %}