.logo_blocks_wrapper {
  margin: 120px auto;
  &.first {
    margin: 20px auto;
  }

  .logos_title {
    font-size: 36px;
    line-height: 43px;
    font-family: $lora;
    font-weight: bold;
    text-align: center;
    color: $corporate_1;
    margin-bottom: 40px;
  }

  .logos_images {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;

    img {
      width: auto;
      max-height: 170px;

    }
    .icon_wrapper {
      height: 100px;
      .logo_icon {
        position: relative;
        width: 50px;
        height: 50px;
        display: block;
        border-radius: 50%;
        font-size: 30px;
        margin: auto;
        color: white;

        &::before {
          @include center_xy;
          z-index: 1;
        }

        &::after {
          position: absolute;
          content: '';
          background-color: $corporate_3;
          border: 7px solid lighten($corporate_3, 10%);
          top: -7px;
          bottom: -7px;
          left: -7px;
          right: -7px;
          border-radius: 50%;
        }
      }

      .logo_description {
        text-align: center;
        width: 110px;
        margin: 15px auto auto;
        display: block;
      }
    }
    &.owl-carousel {
      .logo_image {
        margin: auto;
      }
      .owl-stage {
        @include display_flex;
        align-items: center;
        justify-content: center;
        height: 190px;
        -webkit-flex-wrap: unset;
        -moz-flex-wrap: unset;
        -ms-flex-wrap: unset;
        -o-flex-wrap: unset;
        flex-wrap: unset;
      }
      .owl-nav {
        top: 50%;
        position: absolute;
        @media (max-width: 1160px) {
          display: inherit;
        }
        .owl-prev {
          left: 30px;
        }
        .owl-next {
          right: 30px;
        }
      }
    }
  }
}