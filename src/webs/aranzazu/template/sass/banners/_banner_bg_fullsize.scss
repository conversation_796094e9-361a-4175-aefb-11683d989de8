.banner_bg_fullsize {
  @include overlay(black, .45);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: relative;
  display: flex;
  align-items: center;
  padding: 125px 0;

  .container12 {
    .content_wrapper {
      text-align: center;
      position: relative;
      z-index: 2;

      .title {
        font-size: 40px;
        color: white;
        line-height: 48px;
        text-transform: uppercase;
        font-family: $lora;
        margin-bottom: 72px;

        b {
          font-weight: bold;
        }
      }

      .tick_element {
        color: white;
        display: inline-block;
        margin-right: 20px;

        &:last-of-type {
          margin-right: 0;
        }

        .tick_icon {
          font-size: 42px;
          margin-bottom: 20px;
          padding-top: 10px;
        }

        .tick_description {
          font-size: 15px;
          font-weight: bold;
          letter-spacing: 0.05em;
          font-family: $raleway;
          line-height: 20px;
        }
      }

      .see_more {
        background: $corporate_1;
        margin: auto;
        text-align: center;
        font-size: 20px;
        font-weight: lighter;
        font-family: $lora;
        text-transform: uppercase;
        z-index: 2;
        padding: 23px 30px;
        color: white;
        position: relative;
        margin-right: 50px;

        &:before {
          content: '';
          position: absolute;
          top: -10px;
          left: -10px;
          right: -10px;
          bottom: -10px;
          background: rgba($corporate_1, 0.8);
          z-index: -1;
          @include transition(all, .5s);
        }

        &:hover:before {
          background: rgba($corporate_1, 1);
        }
      }

      .ticks_wrapper {
        margin-bottom: 100px;

        &.owl-carousel {
          padding: 0 300px;

          .owl-nav {
            .owl-prev {
              left: 300px;
            }

            .owl-next {
              right: 300px;
            }
          }
        }
      }
    }
  }
}

