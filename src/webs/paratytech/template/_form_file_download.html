<div class="form_contact_wrapper file_download_form">
    <form name="contact" id="contact_file_download" method="post" action="/utils/?action=contact">
        <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe}}"/>
        <div class="row form" style="padding-top:0px;padding-bottom:0px;">
            <div class="box">
                <input class="name form-control contacto-form" id="name" name="name" type="text" placeholder="{{ T_nombre }}">
                <input class="name form-control contacto-form" id="surname" name="surname" type="text" placeholder="{{ T_apellidos }}">
                <input class="mail form-control contacto-form" id="email" name="email" type="text" placeholder="{{ T_email }}">
                <input class="business form-control contacto-form" id="business" name="business" type="text" placeholder="{{ T_empresa }}">
            </div>

            <div class="lopd_button">
                <input type="checkbox" name="privacy" value="privacy" id="privacy"/>
                <a href="#lopd_content">{{ lopd.subtitle|safe }}</a>
            </div>

            <div id="lopd_content" style="display: none">{{ lopd.content|safe }}</div>

            <div style="float:right" id="contact-button-wrapper">
                <div id="file-contact-button"
                     style="width:auto;padding-top:8px;padding-bottom:8px;padding-left:10px; padding-right:10px;background: white; cursor: pointer">
                    {% if download_button %}{{ download_button|safe }}{% else %}{{ T_enviar }}{% endif %}
                </div>
            </div>
        </div>
    </form>
</div>



<script>
    $("#file-contact-button").click(function(){
        perform_file_download_send("/utils/?action=promopopup&language=" + $("input[name='actual_language']").val());
    });

    function perform_file_download_send(url_target){
        if ( $("#contact_file_download").valid() ) {
            $.post(url_target,
                {
                    'name': $("#name").val(),
                    'surname': $("#surname").val(),
                    'company': $("#business").val(),
                    'email': $("#email").val(),
                    'list_id': '{% if actual_language == 'SPANISH' %}9739952{% else %}9739978{% endif %}'
                },

                function(data) {
                    _send_thanks_email($("#name").val(), $("#email").val());
                    $("#name").val("");
                    $("#surname").val("");
                    $("#business").val("");
                    $("#email").val("");
                    var target_file_to_download = "{{ form_file_download }}";

                    if (!(target_file_to_download.indexOf('.pdf') > -1)){
                        window.open(target_file_to_download, '_blank');
                    }else {
                        download_file('/file-download?file=' + target_file_to_download, 'data');
                    }

                    _send_event_ping('Newsletter', 'Suscribe', 'Ebook Agosto 2017');
                    alert("{{ T_thanks }}");
                }
            );
        }
    }

    function _send_thanks_email(name, email){
        $.post('/thanks-email-user', {'name': name, 'email': email});
    }
</script>
