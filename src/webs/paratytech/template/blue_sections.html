{% if blue_block %}
<div class="blue_sections_wrapper">
    <div class="container12">
        <h2 class="blue_title">{{ blue_block.subtitle|safe }}</h2>
        <div class="blue_description">{{ blue_block.content|safe }}</div>
        {% if blue_block.pictures %}
            {% if blue_icons %}
                <div class="icons_blue">
                    {% for x in blue_block_pictures %}
                        {% if forloop.counter|divisibleby:2 %}
                            <div class="icon_element even">
                                <div class="icon_image">
                                    {% if x.servingUrl %}<img data-src="{{ x.servingUrl|safe }}" alt="{{ x.altText }}"/>{% endif %}
                                </div>
                                <div class="icon_text">{{ x.title|safe }}</div>
                            </div>
                        {% else %}
                            <div class="icon_element">
                                <div class="icon_text">{{ x.title|safe }}</div>
                                <div class="icon_image">
                                    {% if x.servingUrl %}<img data-src="{{ x.servingUrl|safe }}" alt="{{ x.altText }}"/>{% endif %}
                                </div>
                            </div>
                        {% endif %}
                        {% if forloop.counter|divisibleby:2 and not forloop.last%}<hr class="separator"/>{% endif %}
                    {% endfor %}
                </div>

                <div class="icons_blue_hidden">
                    {% for x in blue_block_pictures %}
                        <div class="icon_element">
                            <div class="icon_image">
                                {% if x.servingUrl %}<img data-src="{{ x.servingUrl|safe }}" alt="{{ x.altText }}"/>{% endif %}
                            </div>
                            <div class="icon_text">{{ x.title|safe }}</div>
                        </div>
                        {% if not forloop.last %}<hr class="separator"/>{% endif %}

                    {% endfor %}

                </div>
            {% else %}
                <div class="blue_images">
                    {% for x in blue_block.pictures %}
                        {% if forloop.counter == 7 %}
                            </div>
                            <div class="blue_images_hidden" style="display: none">
                        {% endif %}
                        <div class="images">
                            <img data-src="{{ x|safe }}=s1900" alt="{{ x.altText }}"/>
                        </div>
                    {% endfor %}
                </div>
                {% if blue_block.pictures|length > 6 %}
                    <div class="more_images">
                        <img src="/img/parah/more_images.png" alt=""/>
                    </div>
                {% endif %}

            {% endif %}
        {% endif %}
    </div>
</div>


<script>
    $(window).load(function () {
        function blue_section_fx() {
            var window_height = parseInt($(window).height());
            $(".blue_sections_wrapper .container12 > *").each(function (index) {
                if ($(this).hasClass("blue_title") || $(this).hasClass("blue_description")) {
                    $(this).addnimation({class: "fadeIn",classOut: "fadeOut", windowDiference: window_height - 100,});
                }
                else {
                    $(this).children().each(function () {
                        if ($(this).hasClass("separator")){
                            $(this).addnimation({class: "fadeIn",classOut: "fadeOut", windowDiference: window_height - 100});
                        }
                        else if ($(this).hasClass("even")){
                            $(this).addnimation({class: "fadeInRight",classOut: "fadeOutRight", windowDiference: window_height - 100});
                        }
                        else {
                            $(this).addnimation({class: "fadeInLeft",classOut: "fadeOutLeft", windowDiference: window_height - 100});
                        }
                    });
                }
            });
        }

        blue_section_fx();
        $(window).scroll(blue_section_fx);
    });
</script>
{% endif %}