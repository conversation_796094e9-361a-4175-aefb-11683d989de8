{% spaceless %}
<!DOCTYPE html>
<html lang="{{ language_code }}" {% if seo_html %}{{ seo_html|safe }}{% endif %}>

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
        {{ sectionName|safe }} {% else %}  {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    <link rel="icon" href="/static_1/images/favicon.ico?v=1" type="image/x-icon">


    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    {% if canonical_link or custom_canonical %}
        <link rel="canonical" href="{% if canonical_link %}{{ canonical_link|safe }}{% else %}{{ custom_canonical|safe }}{% endif %}" />
    {% endif %}
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="dc.title"
          content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
              {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description"
          content="{% if sectionName %} {{ sectionName|safe }} - {% endif %}{{ description|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>

    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="theme-color" content="#446ca9">
    <script type="text/javascript">
        if (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) {
            document.write('<meta name="viewport" content="initial-scale=1.0, maximum-scale=1" />');
        }
        if (navigator.userAgent.match(/iPad/i)) {
            document.write('<meta name="viewport" content="width=1000, initial-scale=1.0, maximum-scale=0.67" />');
        }
    </script>

    <meta name="google-site-verification" content="HqkoUPsBetQJ-gL3pZcsFoVTnQ7xHA2wBD2R9JnveZQ"/>

    <!--[if lt IE 7]><script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script><![endif]-->

    <!--[if lte IE 8]><script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->

    {{ jquery|safe }}

    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>

    <link rel="stylesheet" type="text/css" href="/css/parah/styles.css?v=3.54"/>

    <style type="text/css">.aviso_cookie {display:block; width: 100%; top: 0; position: fixed; z-index: 50000; border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-right-radius: 0px; border-bottom-left-radius: 1px; background: rgba(0, 0, 0, 0.6);}.texto_cookie{ text-align: center; font-family: 'Roboto', sans-serif; font-weight: 300; font-size: 12px; color: white; line-height: 14px; padding-top: 3px; padding-bottom: 3px; padding-left: 15%; padding-right: 15%;}span.titulo_cookie{font-weight: 400;}.cookie_div_cerrar{right: 0;top: 0; position:absolute}.cookie_boton_cerrar{border-radius: 100%; background-color: rgba(0, 0, 0, 0.45); border: 0; color: white;width: 20px;height: 20px;margin-right: 3px;font-size: 12px;padding: 0px;}</style>

    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>

<body class="{{ language }}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ rich_snippet|safe }}
{{ all_tracking_codes_body|safe }}

{% if popup_loading and ini %}
<div class="white_overlay"></div>
<div class="loading_site" {% if popup_loading_background %}style="background-image: url('{{ popup_loading_background.servingUrl }}=s1900');"{% endif %}>
    <div class="loading_content">
        {% if popup_loading_logo %}
            <div class="popup_loading_logo"><img src="{{ popup_loading_logo.servingUrl|safe }}=s800" {% if popup_loading_logo.altText %} alt="{{ popup_loading_logo.altText|safe }}" {% else %}{% if popup_loading_logo.title %} alt="{{popup_loading_logo.title|safe}}" {% endif %}{% endif %}></div>
        {% endif %}
        {% if popup_loading.subtitle %}<div class="popup_loading_title">{{ popup_loading.subtitle|safe }}</div>{% endif %}
        <div class="dots_loader"><div class="dot"></div><div class="dot"></div><div class="dot"></div><div class="dot"></div><div class="dot"></div></div>
    </div>
</div>

<script>
$(window).load(function () {
    setTimeout(function () {$(".loading_site").animate({ width: 0 },1000);$(".white_overlay").animate({ width: 0 },1500);},1000);
});
</script>
{% endif %}

{% block content %}

    <!--EDIT HERE YOUR PAGE-->

{% endblock %}

{% if bottom_popup and ini %}

    <div class="bottom_popup" style="display: none;">
        <a class="myFancyPopupFaldon" href="#popup_inicial_base">
            <div id="wrapper2">
                <div class="bottom_popup_text">{{ bottom_popup.content|safe }}</div>
                <button class="bottom_popup_button">{{ T_apuntate }}</button>
            </div>
        </a>
    </div>

    {% if bottom_popup.popup_pictures %}
        <div style="display:none;">

            <div class="popup_inicial" id="popup_inicial_base">
                {% if bottom_popup.popup_pictures.0.linkUrl %}
                    <a style="color: white;" href="{{ bottom_popup.popup_pictures.0.linkUrl|safe }}">
                {% endif %}
                <div class="popup_img">

                    <img data-src="{{ bottom_popup.popup_pictures.0.servingUrl|safe }}=s600"/>

                    <div class="popup_text">

                        <div class="popup_title">{{ bottom_popup.popup_pictures.0.title|safe }}</div>
                        <div class="popup_description">{{ bottom_popup.popup_pictures.0.description|safe }}</div>

                    </div>
                </div>
                {% if bottom_popup.popup_pictures.0.linkUrl %}
                    </a>
                {% endif %}

                {% if not bottom_popup.hide_form %}
                    <div class="popup_form">
                        <form action="" method="post" class="form_popup">
                            <input id="id_email" type="text" name="email" maxlength="150"
                                   placeholder="{{ T_introduce_email_placeholder }}">
                            <button class="popup_button">{{ T_enviar }}</button>
                            <div class="lopd_button">
                                <input type="checkbox" name="privacy" value="privacy" id="privacy"/>
                                <a href="/{{ language_code }}/?sectionContent=politica-de-privacidad.html" class="popup_lopd myFancyPopup fancybox-iframe">{{ lopd.subtitle|safe }}</a>
{#                                <a href="#lopd_content" class="myFancyPopup fancybox-iframe">{{ lopd.subtitle|safe }}</a>#}
                            </div>
                            <div id="lopd_content" style="display: none">{{ lopd.content|safe }}</div>
                            <div class="spinner_wrapper_faldon" style="display:none;"><img
                                    src='/static_1/images/spinner.gif' width="30" height="30"></div>
                        </form>
                    </div>
                {% endif %}


            </div>
        </div>
    {% endif %}

    <script>
        $(window).load(function () {
            var busca = -1;
            var x = document.cookie;
            if (x) {
                var y = x.split(";");
                for (i in y) {
                    busca = y[i].search("popup_faldon");
                    if (busca > -1) {break;}
                }
            }
            if (busca < 0) {
                if (searchCookie("anuncionews_fancy_{{ language }}")) {
                }
                else {
                    window.setTimeout(function () {
                        $.fancybox($("#popup_inicial_base"), {
                            width: "auto",
                            height: "auto",
                            fitToView: false,
                            autoSize: false,
                            padding: 0,
                            wrapCSS: 'fancybox-news'
                        });
                    }, 6000);
                    document.cookie = "anuncionews_fancy_{{ language }}=1"
                }
            }
            $(".popup_inicial a").click(function () {
                cookies = document.cookie ? document.cookie.split(";") : $(this).die("click");
                for (x in cookies) {
                    console.log(cookies[x]);
                    if (cookies[x].search('popup_faldon')) {document.cookie = "popup_faldon=1";break;}
                }
            });
        });
    </script>
{% endif %}

{# Messages Form #}
<input type="hidden" name="thanks_newsletter" value="{{ T_gracias_newsletter|safe }}"/>
<input type="hidden" name="error_newsletter" value="{{ T_campo_obligatorio|safe }}"/>
<input type="hidden" name="error_invalido" value="{{ T_campo_valor_invalido|safe }}"/>
<input type="hidden" name="email_warning" value="{{ T_not_confirmed_email_warning|safe }}"/>
<input type="hidden" name="actual_language" value="{{ actual_language }}"/>
{% if aviso_cookie %}
<input type="hidden" name="aviso_cookie" value="True"/>
{% endif %}

{{ scripts_to_render_desktop|safe }}
<script type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{ language_code }}.js"></script>
<script src="/static_1/lib/flexslider/jquery.flexslider-min.js"></script>
<script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
<script async type="text/javascript" src="/js/parah/common_paraty.js?v=1.4"></script>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script async src="/static_1/lib/jquery.unveil/jquery.unveil.min.js"></script>

<!-- My Functions -->
<script async src="/js/parah/form.js?v=2.45"></script>
<script src="/js/parah/functions.js?v=2.25"></script>



<script type="text/javascript">
    /* <![CDATA[ */
    goog_snippet_vars = function () {
        var w = window;
        w.google_conversion_id = 12345678;
        w.google_conversion_label = "abcDeFGHIJklmN0PQ";
        w.google_conversion_value = 13.00;
        w.google_conversion_currency = "USD";
        w.google_remarketing_only = false;
    }
    // NO CAMBIE EL CÓDIGO SIGUIENTE.
    goog_report_conversion = function (url) {
        goog_snippet_vars();
        window.google_conversion_format = "3";
        var opt = new Object();
        opt.onload_callback = function () {
            if (typeof(url) != 'undefined') {
                window.location = url;
            }
        }
        var conv_handler = window['google_trackConversion'];
        if (typeof(conv_handler) == 'function') {
            conv_handler(opt);
        }
    }
    /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion_async.js"></script>

{{ all_tracking_codes_footer|safe }}
{{ extra_content_website|safe }}
</body>
</html>
{% endspaceless %}