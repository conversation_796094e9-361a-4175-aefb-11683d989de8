$(document).ready(function(){

    jQuery.validator.addMethod("phone", function(phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 && phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");

    // Contact Form

    var error_newsletter = $("input[name='error_newsletter']").val();
    var error_invalido = $("input[name='error_invalido']").val();
    var error_warning = $("input[name='email_warning']").val();

    var validate_form_1 = {
        rules: {
            name: "required",
            privacy: "required",
            email: {
                required: true,
                email: true
            },
            emailConfirmation: {
                required: true,
                equalTo: "#email",
                email: true
            },
            telephone: {
                required: true,
                phone: true
            },
            hotel: "required",
            province: "required"
        },
        messages: {
            name: error_newsletter,
            privacy: error_newsletter,
            email: {
                required: error_newsletter,
                email: error_invalido
            },
            emailConfirmation: {
                required: error_newsletter,
                email: error_invalido,
                equalTo: error_warning
            },
            telephone: {
                required: error_newsletter,
                phone: error_invalido
            },
            hotel: error_newsletter,
            province: error_newsletter
        }
    };

    if ($("#business").length) {
        validate_form_1['rules']['business'] = "required";
        validate_form_1['messages']['business'] = error_newsletter;
    }

    $("#contact").validate(validate_form_1);


    $("#contact-button").click(function(){
        var url_to_send = $("input[name='acumba_api']").length ? "/utils/?action=promopopup" : "/utils/?action=contact";
        if ($("input[name='actual_language']").length){
            url_to_send += "&language=" + $("input[name='actual_language']").val();
        }

        var data_to_send = {
            'name': $("#name").val(),
            'telephone': $("#telephone").val(),
            'email': $("#email").val(),
            'comments': $("#comments").val(),
            'section': $("#section-name").val(),
            'destination_email':  $("#destination_email").val(),
            'g-recaptcha-response': $("#g-recaptcha-response").val(),
            'agency_name': $("#hotel").val(),
            'province': $("#province").val(),
            'language': $("#language").val()
        };

        if ($("#acumba_api").length) {
            data_to_send['list_id'] = $("#acumba_api").val();
        }

        if ($("#mailerlite_list").length) {
            data_to_send['list_id'] = $("#mailerlite_list").val();
            data_to_send['newsletter'] = true;
        }

        if ( $("#contact").valid() ) {
            if(!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
                $.post(
                    url_to_send, data_to_send,
                    function (data) {
                        if ($("#custom_message").length) {
                            $.post('/thanks-email-user',
                                {
                                    'name': $("#name").val(),
                                    'email': $("#email").val(),
                                    'custom_message': $("#custom_message").val(),
                                    'language': $("input[name='actual_language']").val(),
                                    'g-recaptcha-response': $("#g-recaptcha-response").val()
                                });
                        }
                        if ($("#contact #promotions").is(":checked")) {
                            $.post("/utils?action=promopopup", {
                                'email': data_to_send['email'],
                                'language': $("input[name='actual_language']").val()
                            });
                        }
                        $(".launch_contact").trigger("click");
                        $("#name").val("");
                        $("#telephone").val("");
                        $("#email").val("");
                        $("#emailConfirmation").val("");
                        $("#comments").val("");
                        $("#business").val("");
                        _send_event_ping('Contact', 'Send form', 'Contact us section');
                        goog_report_conversion();
                    }
                );
            }
        }
    });

    // Popup Newsletter

    $(".form_popup").validate({
        rules: {
            privacy: "required",
            email: {
                required: true,
                email: true
            }
        },
        messages: {
            privacy: $("input[name='error_newsletter']").val(),
            email: {
                required: $("input[name='error_newsletter']").val(),
                email: $("input[name='error_invalido']").val()
            }
        }
    });

    $(".popup_button").click(function() {

        if ($(".form_popup").valid()) {
            $(".popup_button").css("display", "none");
            $(".spinner_wrapper_faldon").css("display", "block");
            $.post("/utils?action=newsletter&language=" + $("input[name='actual_language']").val(), {
                'email': $(this).parent().find("#id_email").val()
            }, function(data) {
                $(".form_popup").append("<h3 class='popup_message'>" + $("input[name='thanks_newsletter']").val() + "</h3>");
                $(".spinner_wrapper_faldon").css("display", "none");
                $(".form_popup #id_email").removeClass("error");
                $(".popup_button").css("display", "block");
                $(".bottom_popup").hide("slow");
                document.cookie = "popup_faldon=1"
                window.setTimeout(function() {
                    $.fancybox.close();
                    $('.popup_message').remove();
                }, 5000);
            });
        } else {
            $(".fancybox-news .lopd_button a").css({color: 'red'})
        }

        return false;
    });

    // Home Newsletter

    $("#newsletter_web #form-newsletter").validate({
		rules: {
            privacy: "required",
            promotions: "required",
            suscEmail: {
                required: true,
                email: true
            }
        },
        messages: {
            privacy: $("input[name='error_newsletter']").val(),
            promotions: $("input[name='error_newsletter']").val(),
            suscEmail: {
                required: $("input[name='error_newsletter']").val(),
                email: $("input[name='error_invalido']").val()
            }
        }
	});
	$("#newsletter_web #form-newsletter #newsletter-button").click(function(){
        if ($("#newsletter_web #form-newsletter").valid()) {
            $.post("/utils?action=newsletter&language=" + $("input[name='actual_language']").val(),
			{
				'email': $("#newsletter_web #suscEmail").val()
			},
			function(data) {
				alert($("input[name='thanks_newsletter']").val());
				$("#newsletter_web #suscEmail").val("");

			}
		    );
        } else {
            $("#newsletter_web .lopd_button a").css({color: 'red'});
            $("#newsletter_web .promotions_button a").css({color: 'red'});
        }
	});

    $("#contact_file_download").validate({
        rules: {
            name: "required",
            surname: "required",
            privacy: "required",
            email: {
                required: true,
                email: true
            }
        }
    });

    $(".contact_file_download").each(function(){
        $(this).validate({
            rules: {
                name: "required",
                surname: "required",
                privacy: "required",
                email: {
                    required: true,
                    email: true
                }
            }
        });
    });
});

