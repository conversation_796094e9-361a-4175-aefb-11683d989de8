<form action="/utils/?action=contact" class="event_form" id="contact" method="post" name="contact"
      novalidate="novalidate">
    <input name="section" type="hidden" value="Formulario empresas"/>

    <div class="info">
        <div class="contInput">
            <label class="title" for="name">{{ T_nombre_y_apellidos }}(*)</label>
            <input class="bordeInput" id="name" name="name"
                   type="text" value=""/>
        </div>

        <div class="contInput">
            <label class="title" for="telephone">{{ T_telefono }}(*)</label>
            <input class="bordeInput" id="telephone"
                   name="telephone" type="text" value=""/>
        </div>

         <div class="contInput">
            <label class="title" for="email">{{ T_email }}(*)</label>
            <input class="bordeInput" id="email"
                   name="email" type="text" value=""/>
        </div>

        <div class="contInput">
            <label class="title" for="emailConfirmation">{{ T_confirm_email }}(*)</label>
            <input class="bordeInput"
                   id="emailConfirmation"
                   name="emailConfirmation"
                   type="text" value=""/>
        </div>

        <div class="contInput">
            <label class="title" for="event_date">{{ T_fecha_evento }}(*)</label>
            <input class="bordeInput" id="event_date"
                   name="event_date" type="text" value=""/>
        </div>

        <div class="contInput">
            <label class="title" for="num_persons">{{ T_numero_personas_evento }}(*)</label>
            <input class="bordeInput" id="num_persons"
                   name="num_persons" type="text" value=""/>
        </div>

        <div class="contInput textarea_comments">
            <label class="title" for="comments">{{ T_comentarios }}</label>
            <textarea class="bordeInput" id="comments"
                   name="comments" type="text" rows="4" cols="50" value=""></textarea>
        </div>

        <p>
            (*) {{ T_campo_obligatorio }}</p>

        <div class="contInput">
            <input id="privacity" name="privacity" type="checkbox"/>{{ T_acepto_clausulas|safe }}.
        </div>
        <button id="submit-button" onclick="return false;" type="submit">{{T_enviar}}</button>
    </div>
</form>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

    $(document).ready(function () {

        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
            rules: {
                name: "required",
                telephone: "required",
                event_date: "required",
                num_persons: "required",
                privacity: "required",
                email: {
                    required: true,
                    email: true
                },
                emailConfirmation: {
                    required: true,
                    equalTo: "#email",
                    email: true
                }
            },
            messages: {
                name: "Este campo es obligatorio",
                telephone: "Este campo es obligatorio",
                event_date: "Este campo es obligatorio",
                num_persons: "Este campo es obligatorio",
                privacity: "Por favor acepta la condiciones de privacidad para continuar",
                email: {
                    required: "Este campo es obligatorio",
                    email: "El valor de este campo no es correcto"
                },
                emailConfirmation: {
                    required: "Este campo es obligatorio",
                    email: "El email introducido no coincide"
                }
            }

        });

        $("#submit-button").click(function () {
            if ($("#contact").valid()) {
                $.post(
                        "/utils/?action=contact",
                        {
                            'name': $("#name").val(),
                            'email': $("#email").val(),
                            'telephone': $("#telephone").val(),
                            'event_date': $("#event_date").val(),
                            'num_persons': $("#num_persons").val(),
                            'comments': $("#comments").val()
                        },

                        function (data) {
                            alert("{{ T_gracias_contacto }}");
                        }
                );
            }
        });

    });
</script>