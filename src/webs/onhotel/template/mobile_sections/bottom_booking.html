{# Destacados Blocks #}
<div class="section_icos_slider_wrapper container_90">


    <div class="section_icos_slider">
        <ul class="slides">
            {% for x in select_section_image %}
                <li>
                    <div class="section_icos">
                        <a href="{{ x.linkUrl }}">
                            <div class="image_title_destac">
                                <img src="{{ x.servingUrl }}" class="image_destac">
                            </div>
                        </a>
                    </div>


                </li>
            {% endfor %}
        </ul>
    </div>


</div>

<link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox.css" type="text/css" media="screen" />
<link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox-buttons.css" type="text/css" media="screen" />
<link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox-thumbs.css" type="text/css" media="screen" />
<script type="text/javascript" src="/static_1/lib/fancybox4Mobile/lib/jquery.mousewheel-3.0.6.pack.js"></script>
<script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/jquery.fancybox.pack.js"></script>
<script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-buttons.js"></script>
<script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-media.js"></script>
<script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-thumbs.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script>
    $(function () {
        $('.section_icos_slider').flexslider({
            directionNav: true,
            animation: "slide",
            slideshow: true,
            controlNav: false
        });


    });
</script>