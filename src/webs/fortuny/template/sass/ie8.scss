.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled = false) !important;
}

#top-sections {
  width: 500px;
  a {
    width: 130px;
    display: inline-block;
  }

  .second_top_link {
    width: 200px;
  }
}

#lang {
  width: 102px;
}

#social {
  width: 130px;
}

.ticks_wrapper {
  .ticks {
    width: 220px;
  }
}

.bannersx3_wrapper {
  .bannerx3_title {
    font-size: 14px !important;
    width: 350px;
    background: black;
  }
}

.bannersx2_wrapper {
  .bannerx2_title {
    font-size: 14px !important;
    width: 400px;
    background: black;
  }
}

.banner_center_container {
  .bannerx2_title {
    margin-top: 50px;
  }
}

.offers_wrapper {
  .offer_description {
    display: none;
  }
}