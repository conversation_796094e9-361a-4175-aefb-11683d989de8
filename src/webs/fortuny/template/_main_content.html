{# Banners x3 #}
{% if bannersx3 %}
    <div class="bannersx3_wrapper">
        {% for x in bannersx3 %}
            {% if not x.flexslider %}
                {% if x.linkUrl|safe != "None" %}<a href="{{ x.linkUrl }}">{% endif %}
                    <div class="bannerx3_element {% cycle '' 'middle' '' %}">
                        <div class="overlay_{{ forloop.counter }} overlay_general"></div>
                        <img src="{{ x.servingUrl }}">
                        <p class="bannerx3_title"><img class="ico" src="{{ x.description|safe }}"><span>{{ x.title|safe }}</span></p>
                    </div>
                {% if x.linkUrl|safe != "None" %}</a>{% endif %}
            {% else %}
                <div class="flexslider_bannerx3">
                    <ul class="slides">
                        <li>
                            <div class="bannerx3_element_list">
                                <div class="overlay_{{ forloop.counter }} overlay_general"></div>
                                <img data-src="{{ x.servingUrl }}" lazy="true">
                                <p class="bannerx3_title">
                                    <img class="ico" src="{{ x.description|safe }}" ><span>{{ x.title|safe }}</span>
                                </p>
                            </div>
                        </li>
                        {% for y in x.flexslider %}
                            <li>
                                <div class="bannerx3_element_list">
                                    <div class="overlay_{{ forloop.parentloop.counter }} overlay_general"></div>
                                    <img data-src="{{ y.servingUrl }}" lazy="true">
                                    <p class="bannerx3_title"><img class="ico" src="{{ y.description|safe }}"><span>{{ y.title|safe }}</span></p>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endfor %}
    </div>
{% endif %}



{# Banners x2 #}
{% if bannersx2 %}
    <div class="bannersx2_wrapper">
        {% for x in bannersx2 %}
            {% if not x.flexslider %}
                {% if x.linkUrl|safe != "None" %}<a href="{{ x.linkUrl }}">{% endif %}
                    <div class="bannerx2_element">
                        <img src="{{ x.servingUrl }}">
                        <p class="bannerx2_title"><img class="ico" src="{{ x.description|safe }}"><span>{{ x.title|safe }}</span></p>
                    </div>
                {% if x.linkUrl|safe != "None" %}</a>{% endif %}
            {% else %}
                <div class="flexslider_bannerx2">
                    <ul class="slides">
                            <li>
                                <div class="bannerx2_element_list">
                                    <img data-src="{{ x.servingUrl }}" lazy="true">
                                    <p class="bannerx2_title"><img class="ico" src="{{ x.description|safe }}"><span>{{ x.title|safe }}</span></p>
                                </div>
                            </li>
                        {% for y in x.flexslider %}
                            <li>
                                <div class="bannerx2_element_list">
                                    <img data-src="{{ y.servingUrl }}" lazy="true">
                                    <p class="bannerx2_title"><img class="ico" src="{{ y.description|safe }}"><span>{{ y.title|safe }}</span></p>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endfor %}
    </div>
{% endif %}


{# Content by Subtitle #}
{% if content_subtitle %}
    <div class="content_subtitle_wrapper">
        <h3 class="content_subtitle_title">{{ content_subtitle.subtitle|safe }}</h3>
        <div class="content_subtitle_description">{{ content_subtitle.content|safe }}</div>
    </div>
{% endif %}


{# Content Automatic #}
{% if content_access %}
    <div class="automatic_content_wrapper">
        {{ content }}
    </div>
{% endif %}


{# Ofertas #}
{% if offers_access %}
    <div class="offers_wrapper">

        {% for x in offers %}
            <div class="room_element offers_element" {% cycle '' 'style="margin-left:13px;margin-right:13px"' '' %}>
                <div class="exceded">
                    <img src="{{ x.picture|safe }}">
                    <div class="offer_description">{{ x.description|safe }}</div>
                </div>
                <div class="description_wrapper">
                    <div class="buttons_wrapper">
                        <a href="#room_description_{{ forloop.counter }}" class="see_more_room">{{ T_ver_mas }}</a>
                        <a href="#data" class="button-promotion">{{ T_reservar }}</a>
                    </div>
                    <div class="description_room">
                        <span class="title">{{ x.name|safe }}</span>
                        <span class="description">{{ x.description|safe }}</span>
                    </div>
                </div>
            </div>

            <div id="room_description_{{ forloop.counter }}" class="description_room" style="display: none">
                <span class="title">{{ x.name|safe }}</span>
                <span class="description">{{ x.description|safe }}</span>
            </div>
        {% endfor %}
    </div>
{% endif %}


{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}

            {{ location_html.content|safe }}

        </div>

        <div class="form-contact column6">
            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>
    </div>
{% endif %}


{# Rooms #}
{% if bannerx2 %}
<div class="banners_x2_wrapper">

    {% for banner in bannerx2 %}
        <div class="bannerx2_row {% cycle 'left' 'right' %}">
            <div class="bannerx2_text" style="background: {% if banner.background %}{{ banner.background|safe }};{% else %}rgb(237, 235, 236);{% endif %}">
                <div class="banner_center_container">
                    <h3 class="bannerx2_title">{{ banner.title|safe }}</h3>
                    <div class="bannerx2_description">{{ banner.description|safe }}</div>
                    <div class="rooms_buttons_wrapper">
                        <a class="button-promotion room_promotion" href="#data">{{ T_reservar }}</a>
                        <div class="button_wrapper_rooms" num="{{ forloop.counter }}">{{ T_ver_mas }}</div>
                    </div>
                </div>
            </div>

            <div class="hidden_cycle num{{ forloop.counter }}" style="display: none">
                <p class="bannerx2_title">{{ banner.title|safe }}</p>
                <p class="bannerx2_description">{{ banner.description|safe }}</p>
            </div>

            {% if banner.flexslider %}

                 <div class="bannerx2_image">
                    <ul class="slides">
                        <li>
                            <a href="javascript:showGallery2([ {% for picture in banner.pictures %} {href : '{{ picture.servingUrl }}=s800'}, {% endfor %} ]);" class="rooms-img">
                                <img src="{{ banner.servingUrl }}=s1600">
                            </a>
                        </li>
                        {% for image in banner.flexslider %}
                            <li>
                                <img src="{{ image.servingUrl }}=s1600"/>
                            </li>
                        {% endfor %}
                    </ul>

                      <ul class="flex-controlador">
                      <span class="flex_prev"></span>
                          {% for banner in banner.flexslider %}
                            <li><span class="circle_green"></span></li>
                          {% endfor %}
                            <li><span class="circle_green"></span></li>
                      <span class="flex_next"></span>
                      </ul>
                </div>

            {% else %}
                <div class="bannerx2_image">
                    <a href="javascript:showGallery2([ {% for picture in banner.pictures %} {href : '{{ picture.servingUrl }}=s800'}, {% endfor %} ]);"
                       class="rooms-img">
                        <img src="{{ banner.servingUrl }}=s1600">
                    </a>
                </div>
            {% endif %}
        </div>
    {% endfor %}

</div>
{% endif %}

{# Service Elements #}
{% if service_elements %}
    <div class="services_wrapper">
        <h3 class="service_titles">{{ service_elements_title.subtitle|safe }}</h3>
        <div class="service_elements_wrapper">
            {% for x in service_elements %}

                <div class="service_element">
                    <img src="{{ x.servingUrl|safe }}" class="service_image">
                    <span class="service_text">{{ x.title|safe }}</span>
                </div>

            {% endfor %}
        </div>
    </div>
{% endif %}

{# Iframe Google Maps #}
{% if google_maps %}
    </div>
    <div class="google_maps_wrapper">
        <div class="container12">
            {{ google_maps.content|safe }}
        </div>
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}


{# Images Carousel #}
{% if mini_gallery %}
    <div class="mini_gallery_wrapper">
        <h3 class="mini_gallery_title">{{ mini_gallery.subtitle|safe }}</h3>
        <div class="flexslider">
            <ul class="slides">
                {% for x in mini_gallery.pictures %}
                    <li>
                        <div class="text-bannerx2">
                            <a href="{{ x|safe }}=s1900" rel="lightbox[gallery]">
                                <img data-src="{{ x|safe }}" lazy="true">
                            </a>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
{% endif %}