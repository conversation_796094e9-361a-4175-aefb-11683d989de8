<div id="main-sections">
	<ul id="main-sections-inner" class="container">
		{% for section in main_sections %}
            {% if section.title %}
		<li class="main-section-div-wrapper {% if forloop.counter == 1 %} first-section {% endif %}" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}

					<a>{{ section.title|safe }}</a>
            {% else %}

                {% if section.title %}

                    <a {% if forloop.last %}style="border-right: 0"{% endif %} href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}">
                        {{ section.title|safe }}
                    </a>

                {%  endif %}

            {% endif %}

            {% if section.subsections %}
            <ul>


                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">

                        {% if subsection.title %}

                            <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                            {{ subsection.title|safe}}
                            </a>

                        {% endif %}


                    </li>
                {% endfor %}
            </ul>
            {% endif %}


		</li>
            {% endif %}
		{% endfor %}

	</ul>
</div>