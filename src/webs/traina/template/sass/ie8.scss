.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled = false) !important;
}

.service_wrapper {
  width: 160px;
}

.location-info-and-form-wrapper {
  background: #d3d3d3;
}

#my-bookings-form-search-button {
  padding: 0px 7px 0px;
  font-size: 20px;
  background: #012b67;
  color: white;
  border: 0px;
  height: 24px;
  display: table;
  text-align: center;
  margin: 10px auto 0px;
}

.rooms_number .selectric .label {
  padding-top: 26px;
}

.rooms-description .destino {
  font-size: 18px;
}

.scapes-blocks .block .description {
  background: #d3d3d3;
}

.bannerx2_wrapper p.banner_title strong {
  font-size: 30px;
}