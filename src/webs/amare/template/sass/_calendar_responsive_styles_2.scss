@media only screen and (max-width: 1140px) {
  .container12 {
    width: 100%;
  }
}

@media only screen and (max-width: 780px) {
  #slider_container {
    .slider_title_container .title_text h4 {
      line-height: 1;
    }
  }

  #custom_landing_element {
    #prices-calendar {
      .button {
        font-size: 21px !important;
      }
    }

    #selectorRooms {
      width: auto !important;
      padding: 0 !important;
    }

    .actual_selection_info_wrapper {
      .nights_number_wrapper {
        .nights_number {
          display: block;
          line-height: 1;
        }

        label {
          display: block;
          line-height: 1;
          margin-top: 0;
          font-size: 15px;
        }
      }

      .selection_price_wrapper {
        width: 110px;
        font-size: 10px;
      }

      .label_actual_selection {
        width: auto;
        padding-right: 20px;
      }
    }

    .header_wrapper_calendar_availability .popup_helper_wrapper.search_button {
      display: none !important;
    }
  }
}

@media (min-width: 721px) {
  .hotel_phone_mobile_wrapper {
    display: none !important;
  }
}

@media (max-width: 720px) {
  .logotype_container .phones_wrapper {
    display: none;
  }

  .hotel_phone_mobile_wrapper {
    display: inline-block !important;
  }

  footer.footer_landing {
    & > div {
      width: 100% !important;
      text-align: center !important;
      margin-bottom: 10px;
    }
  }
}