# -*- coding: utf-8 -*-
import re

from booking_process.utils.namespaces.namespace_utils import get_application_id
from booking_process.constants.advance_configs_names import CONTACT_PHONES, NEWSLETTER_POPUP_THANKS, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
	get_section_from_section_spanish_name_with_properties
from utils.flask_requests import response_utils
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "flat2"


class TemplateHandler(BaseTemplateHandler2):

	def get(self, *args):
		response_utils.add_response_headers({
			'X-XSS-Protection': '1; mode=block',
			'X-Content-Type-Options': 'nosniff'
		})

		host = os.environ.get('HTTP_HOST')
		reg_exp = "\/([\w]{2})\/"
		language_in_url = re.search(reg_exp, host)
		if language_in_url:
			response_utils.add_response_headers({
				'Content-Language': language_in_url.group().replace("/", "")
			})
		else:
			response_utils.add_response_headers({
				'Content-Language': 'es'
			})

		return super(TemplateHandler, self).get(*args)

	def get_opinions(self, language):
		opiniones = {}
		opinions_section = get_section_from_section_spanish_name('_opiniones', language)
		opinions = get_pictures_from_section_name('_opiniones', language)
		opinion_link = get_section_from_section_spanish_name('opiniones', language)
		value = 0

		for opinion in opinions:
			if opinion['title']:
				for field in opinion['title'].split("@"):
					if 'name' in field:
						opinion['name'] = field.split("=")[1]
					if 'grade' in field:
						opinion['grade'] = field.split("=")[1]
						value += float(opinion['grade'].replace(",", "."))
					if 'channel' in field:
						opinion['channel'] = field.split("=")[1]

		if opinions_section:
			opiniones['content'] = opinions_section['content']
			opiniones['title'] = opinions_section['title']
			opiniones['subtitle'] = opinions_section['subtitle']

		if len(opinions) > 0:
			opiniones['opiniones'] = opinions
			opiniones['cantidad'] = len(opinions)
			opiniones['media'] = value / len(opinions)
			opiniones['media'] = round(opiniones['media'], 1)

		if opinion_link:
			opiniones['link'] = opinion_link['friendlyUrlInternational']

		return opiniones

	def get_revolution_initial_height(self):

		language_aux = "SPANISH"

		sections = self.getSections(language_aux)

		sectionToUse = self.getCurrenSection(sections)
		section_name = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()


		if section_name == "inicio":
			return "880"
		else:
			return "575"

	def buildSearchEngine(self, language, selectOptions=None):

		options = super(TemplateHandler, self).getBookingWidgetOptions(language, selectOptions)
		options['select_options'] = []
		params = self.getBookingWidgetOptions(language, selectOptions)
		if not params.get("namespace"):
			params['namespace'] = get_application_id()
		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'primera_opcion': get_section_from_section_spanish_name("primera opcion", language).get("subtitle", ""),
							  'newsletter_info': get_section_from_section_spanish_name("Newsletter", language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'bottom_popup': get_section_from_section_spanish_name("popup inicio footer", language),
							  'ticks_header': get_pictures_from_section_name("ticks_header", language),
							  'contact_phone': get_config_property_value(CONTACT_PHONES),
							  'pendule': get_pictures_from_section_name("_floating_pendule", language)
		}

		advanceProperties = self.getSectionAdvanceProperties(sectionToUse, language)

		if section_type == 'Inicio':
			result_params_dict['opinions'] = self.get_opinions(language)

		if section_name == 'opiniones':
			result_params_dict['opinions_inner'] = self.get_opinions(language)

		result_params_dict['banners_inicio'] = get_pictures_from_section_name("banner inicio", language)

		sections_with_slidergallery = {
			"inicio": True
		}
		section_with_slidergallery = sections_with_slidergallery.get(section_name, False)
		result_params_dict['section_with_slidergallery'] = section_with_slidergallery

		if section_with_slidergallery:
			result_params_dict['pictures_slider'] = get_pictures_from_section_name(u"imágenes", language)
			result_params_dict['pictures_slider_title'] = get_section_from_section_spanish_name(u"imágenes", language).get("subtitle","")

		sections_with_banner_inicio_top = {
			"inicio": True
		}
		banner_inicio_top = sections_with_banner_inicio_top.get(section_name, False)
		result_params_dict['banner_inicio_top'] = banner_inicio_top


		sections_with_banner_inicio_foot = {
			"inicio": False
		}
		banner_inicio_foot = sections_with_banner_inicio_foot.get(section_name, True)
		result_params_dict['banner_inicio_foot'] = banner_inicio_foot

		if section_name == u'localización y contacto':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			additionalParams4Contact['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html=get_section_from_section_spanish_name(u"Localización", language)

			if location_html['title'].lower().strip() == u"localizaci&oacute;n":
				location_html['title'] = "localizacion"

			iframe_google_map = get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form = sectionToUse['subtitle']


			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form

		elif section_name == 'alojamiento':
			all_rooms = self.buildRoomInfo(language)
			cont_room = 1
			for room in all_rooms:
				if room["visibleInWeb"]:
					room["counter"] = cont_room
					cont_room += 1

			result_params_dict['rooms'] = all_rooms

			if advanceProperties.get("banner_icons"):
				result_params_dict['banner_icons'] = get_pictures_from_section_name(advanceProperties.get("banner_icons"), language)

			result_params_dict['content_rooms'] = get_section_from_section_spanish_name(u"alojamiento", language).get("content", "")


		elif section_name == 'nuestras ofertas':

			all_promotions = self.buildPromotionsInfo(language)


			cont_room=1
			for room in all_promotions:
				if room["visibleInWeb"]:
					room["counter"]= cont_room
					cont_room= cont_room +1

			result_params_dict['promotions'] = all_promotions

		elif section_name == 'golf':


			all_promotions = get_pictures_from_section_name(u"golf blocks", language)

			cont_room=1
			for room in all_promotions:
				room["visibleInWeb"] = True
				room["counter"]= cont_room
				room["name"]= room["title"]
				room["promocode"]= room["linkUrl"].replace("http_", "")
				cont_room= cont_room +1

			result_params_dict['promotions'] = all_promotions

			result_params_dict['content_promotions'] = get_section_from_section_spanish_name(u"golf", language).get("content","")

		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			visita_or_video_mobile = get_section_from_section_spanish_name(u"Video popup home", language)

			result['visita_or_video_mobile'] = visita_or_video_mobile
			if (not visita_or_video_mobile):
				quita_huecos = True
				result['quita_huecos'] = quita_huecos

			result['picturesInfo'] = self.additionalInfoForPictures(section, language)

		if result:
			return result

		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_with_videos.html'
		}

		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))

		return template

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''

			if user_agent_is_mobile():
				if advance_properties.get("cycle_banners"):
					cycle_dict = dict(get_web_dictionary(language))
					cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("cycle_banners"), language)
					cycle_html = self.buildTemplate_2("mobile_templates/1/_cycle_banners_v1.html", cycle_dict, False)
					additionalParams['custom_elements'] += cycle_html

				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("get_subtitle"):
					additionalParams['get_subtitle'] = True

			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def buildNewsletter2(self, language, name=False, date=False, social=False, check_newsletter=False, background=True,fontawesome5=False):
		template_values = dict(get_web_dictionary(language))
		template_values['language_code'] = language.upper()
		template_values['language'] = get_language_code(language)
		template_values['name_in_form'] = name
		template_values['social'] = self.getSocialDictionary() if social else False
		template_values['background'] = background
		template_values['check_newsletter'] = check_newsletter
		template_values['newsletter_banner'] = get_section_from_section_spanish_name("_newsletter_banner", language)
		template_values['newsletter_custom_check'] = get_section_from_section_spanish_name("_newsletter_custom_check", language)

		advance_newsletter = self.getSectionAdvanceProperties(template_values['newsletter_banner'], language)

		newsletter_thanks = get_config_property_value(NEWSLETTER_POPUP_THANKS)
		if newsletter_thanks:
			popup_thanks_section = get_section_from_section_spanish_name_with_properties(newsletter_thanks, language)
			template_values['newsletter_thanks'] = popup_thanks_section

		if advance_newsletter.get("email_placeholder"):
			template_values['T_introduce_email_placeholder'] = advance_newsletter.get("email_placeholder")

		content = self.buildTemplate_2('_newsletter_5.html', template_values, False, "flathotel2")

		return content