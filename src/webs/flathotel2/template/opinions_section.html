<script type="text/javascript" src="/js/flat2/forms.js"></script>
<section id="top_content">
    <div class="top_content_wrapper container12">
        <div class="opinions-total">
            <div class="header">
                <div class="yellow_left">
                    <img src="/img/flat2/icon-opiniones.png" alt="opinions icon">
                    <span class="media_opinion">{{opinions_inner.media|safe}}</span>
                    <span class="number_opinions">{{opinions_inner.cantidad|safe}} {{ T_opiniones }}</span>
                </div>
                <h3 class="title-module">{{opinions_inner.subtitle|safe}}</h3>

                <a href="#opinions-form" class="opinions-button myFancyPopupAuto2">
                    {{ T_deja_opinion }}
                </a>
            </div>


            <table>
                {% for opinion in opinions_inner.opiniones %}
                <tr>
                    <td class="name">{{opinion.grade|safe}}<br/>



                    </td>
                    <td class="opinion-description">{{ opinion.description|safe }}</td>
                    <td class="calification">

                        {% if opinion.channel == 'facebook' %}
                        <a  target="_blank" rel=noreferrer>
                            <img src="/img/flat2/channels/facebook_channel.jpg" alt="facebook channel"/>
                        </a>
                        {% endif %}

                        {% if opinion.channel == 'tripadvisor' %}
                        <a  target="_blank" rel=noreferrer>
                            <img src="/img/flat2/channels/trip_channel.jpg" alt="tripadvisor channel"/>
                        </a>
                        {% endif%}

                        {% if opinion.channel == 'booking' %}
                        <a  target="_blank" rel=noreferrer>
                            <img src="/img/flat2/channels/booking_channel.jpg" alt="booking channel"/>
                        </a>
                        {% endif%}

                        {% if opinion.channel == 'hcheck' %}
                        <a  target="_blank" rel=noreferrer>
                            <img src="/img/flat2/channels/hcheck_channel.jpg" alt="hcheck channel"/>
                        </a>
                        {% endif%}
                    </td>
                </tr>
                {% endfor %}
            </table>

    </div>
</section>


<div id="opinions-form" style="display:none" class="form-general form-opinion">
    <h3>{{ T_enviar_opinion }}</h3>

    <form action="/utils/?action=contact" id="opinions-form-inner">
        <ul>
            <li>
                <label for="name">{{T_nombre_y_apellidos}} (*)</label>
                <input type="text" id="name" name="name" class="bordeInput" value=""/>
            </li>

            <li>
                <label for="email">{{T_email}} (*)</label>
                <input type="text" id="email" name="email" class="bordeInput" value=""/>
            </li>

            <li>
                <label for="opinion" style="text-transform:uppercase;">{{ T_opiniones }} (*)</label>
                <textarea name="opinion" id="opinion" cols="30" rows="10"></textarea>
            </li>
        </ul>
        <button><a id="opinions-button" class="btn-corporate">{{T_enviar|safe}}</a></button>
    </form>
</div>