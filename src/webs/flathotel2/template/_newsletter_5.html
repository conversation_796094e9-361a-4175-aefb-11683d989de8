<div class="newsletter_wrapper" {% if background|default("true") %}{% if newsletter_banner.pictures %}style="background-image: url({{ newsletter_banner.pictures.0|safe }}=s1900)"{% endif %}{% endif %}>
    <div class="newsletter_container container12">
        <div class="newsletter_title">{{ newsletter_banner.subtitle|safe }}</div>
        <div class="newsletter_description">{{ newsletter_banner.content|safe }}</div>
        <form class="newsletter_form" action="/utils?action=newsletter" method="post">
            {% if name_in_form %}
                <input id="suscName" type="text" name="suscName" maxlength="150" placeholder="{{ T_nombre }}">
            {% endif %}
            <input id="suscEmail" class="input_email" type="text" name="suscEmail" placeholder="{{ T_introduce_email_placeholder|safe }}"/>

            <button class="button_newsletter"><span>{{ T_enviar }}</span></button>


        {% if newsletter_custom_check and newsletter_custom_check.content %}
            {{ newsletter_custom_check.content|safe }}
        {% else %}
            {% if check_newsletter %}
                <div class="check_newsletter">
                    <div class="newsletter_checkbox">
                        <input class="check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                        <a data-fancybox data-options='{"caption" : "{{ T_lopd }}", "src" : "/{{language}}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200"  class="myFancyPopup fancybox.iframe newsletter_popup" href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                    </div>
                </div>
            {% endif %}

            <div class="check_newsletter">
                <div class="newsletter_checkbox">
                    <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="privacy"/>
                    <label for="promotions">{{T_acepto_promociones}}</label>
                </div>
            </div>
        {% endif %}
        </form>
        {% if social %}
            <div class="social_newsletter">
                {% if social.facebook_id %}
                    <a href="https://www.facebook.com/{{ social.facebook_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.flickr_id %}
                    <a href="https://www.flickr.com/photos/{{ social.flickr_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-flickr" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.twitter_id %}
                    <a href="https://twitter.com/#!/{{ social.twitter_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{ social.google_plus_id }}" target="_blank" rel="publisher nofollow">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.youtube_id %}
                    <a href="https://www.youtube.com/{{ social.youtube_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.pinterest_id %}
                    <a href="http://es.pinterest.com/{{ social.pinterest_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.instagram_id %}
                    <a href="http://www.instagram.com/{{ social.instagram_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.blog_id %}
                    <a href="{{ social.blog_id|safe }}" target="_blank" class="blog_link" rel="nofollow">BLOG</a>
                {% endif %}
                {% if social.linkedin_id %}
                    <a href="https://es.linkedin.com/{{ social.linkedin_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-linkedin" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.tripadvisor_id %}
                    <a href="https://www.tripadvisor.es/{{ social.tripadvisor_id }}" target="_blank" rel="nofollow">
                        <i class="fa fa-tripadvisor" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{% if newsletter_thanks %}
    <style>
        .newsletter_thanks_popup_wrapper .fancybox-outer {
          background-color: #fff;
          padding: 30px !important;
        }
        .newsletter_thanks_popup_wrapper .fancybox-outer .fancybox-inner {
            width: auto!important;
        }
        .newsletter_thanks_popup_wrapper .newsletter_thanks_popup {
          text-align: center;
        }
        .newsletter_thanks_popup_wrapper .newsletter_thanks_popup .popup_description {
          width: 100%;
          color: #2c73b5;
          padding: 10px;
        }
    </style>
    <div class="newsletter_thanks_popup" style="display: none;">
        {% if newsletter_thanks.pictures %}
            <div class="popup_picture">
                <img src="{{ newsletter_thanks.pictures.0 }}">
            </div>
        {% endif %}
        <div class="popup_description"
             style="{% if newsletter_thanks.color_text %}color: {{ newsletter_thanks.color_text|safe }}{% endif %}">
            {{ newsletter_thanks.content|safe }}
        </div>
    </div>
{% endif %}

<script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

    $(window).load(function () {
        $(".newsletter_form").validate({
                                           rules: {
                                               {% if check_newsletter %}privacy: "required",{% endif %}
                                               promotions: "required",
                                               suscEmail: {
                                                   required: true,
                                                   email: true
                                               }
                                           },
                                           messages: {
                                               suscEmail: {
                                                   required: "{{ T_campo_obligatorio|safe }}",
                                                   email: "{{ T_campo_valor_invalido|safe }}"
                                               },
                                               privacy: "{{ T_campo_obligatorio|safe }}",
                                               promotions: "{{ T_campo_obligatorio|safe }}",
                                           },
                                           highlight: function (input) {
                                               $(input).parent().find("a").addClass('error_class');
                                               $(input).parent().find("label").addClass('error_class');
                                               $(input).parent().find("#suscEmail").addClass('error_class');
                                           },
                                           errorPlacement: function (error, element) {
                                               //this keeps enable the validation but hides the error message
                                           }
                                       });

        $(".button_newsletter").click(function () {
            if ($(".newsletter_form").valid()) {

                $.post("/utils?action=newsletter&language={{ language_code }}",
                       {
                           'email': $("#suscEmail").val()
                       },

                       function (data) {
                           {% if newsletter_thanks %}
                               $.fancybox($(".newsletter_thanks_popup"), {
                                    padding: 0,
                                    wrapCSS: "newsletter_thanks_popup_wrapper",
                                    width: 640,
                                    height: 'auto',
                                    fitToView: false,
                                    autoSize: false,
                                    modal: true
                                });
                               setTimeout(function(){
                                    window.location.href = window.location.href;
                                }, 5000);
                           {% else %}
                               alert("{{ T_gracias_newsletter }}");
                           {% endif %}
                           $("#suscEmail").val("");
                       }
                );

            } else {
                alert("{{ T_campos_obligatorios|safe }} ");
                console.log("invalid");
            }

        });

    });
</script>