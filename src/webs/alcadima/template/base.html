<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
<link href='//fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>
<title>{% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} </title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

{% if namespace %}
	  <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
{% endif %}

<meta name="keywords" content="{{keywords|safe}}" />
<meta name="description" content="{{description|safe}}" />
<meta name="revisit-after" content="2 days" />
<meta name="google-site-verification" content="TOhYj4JKzxfvdI9H1pmUctdwgWHMK0nclYqF1qobYrg" />
<meta http-equiv="Content-Language" content="{{language}}" />

<meta name="dc.title" content="{% if sectionName %} {{sectionName|safe}} - {% endif %} {{hotel_name|safe}}" />
<meta name="dc.description" content="{% if sectionName %} {{sectionName|safe}} - {% endif %}{{description|safe}}" />
<meta name="dc.language" content="{{ language }}" />
<meta name="dc.creator" content="{{ hotel_name }}"/>
<meta name="dc.format" content="text/html" />
<meta name="dc.identifier" content="{{ hostWithoutLanguage}}{{ path }}" />
 <script type="text/javascript">
if(navigator.userAgent.match(/Android/i)
  || navigator.userAgent.match(/webOS/i)
  || navigator.userAgent.match(/iPhone/i)
  || navigator.userAgent.match(/iPad/i)
  || navigator.userAgent.match(/iPod/i)
  || navigator.userAgent.match(/BlackBerry/i)
  || navigator.userAgent.match(/Windows Phone/i)) {
    document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
}
</script>

<!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

<!--[if IE 8]>
<style type="text/css">
#booking{
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType=1, StartColorStr='#CC000000', EndColorStr='#CC000000')";
    zoom: 1!important;
}
header{
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType=1, StartColorStr='#CC000000', EndColorStr='#CC000000')";
    zoom: 1!important;
}
</style>
<![endif]-->

<!-- jquery -->

    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.0/jquery.min.js"></script>


<!-- jQuery KenBurn Slider  -->
{#    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/jquery.themepunch.plugins.min.js"></script>#}
{#    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/jquery.themepunch.revolution.min.js"></script>#}
{#    <script type="text/javascript" src="/static_1/lib/rs-plugin/videojs/video.dev.js"></script>#}
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.revolution.js"></script>

<!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen" />
{#    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/videojs/video-js.min.css" media="screen" />#}

<!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    {% if datepicker_theme %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"  />
    {% endif %}

<!-- lightbox -->
    <script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>

<!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/sectionsStyle.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css" />
    <link rel="stylesheet" type="text/css" href="/css/alcaa/styles.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/hotelStyle.css" />

<script src="/js/alcaa/unslider.min.js"></script>

<script type="text/javascript" src="/static_1/lib/tinycarousel/tinycarousel.js"></script>

<script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>

</head>

<body>

{% block content %}
{% endblock %}

<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>
<script type="text/javascript" src="/static_1/scripts/common.js"></script>
<script type="text/javascript">

{#<!-- Google Analytics -->#}
  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', '{{google_analytics_id}}']);
  _gaq.push(['_trackPageview']);
 
  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();

</script>

<script type="text/javascript">

$(function() {
    $('.slider').unslider({
		fluid: true,
        keys: true,
        dots: true,
        delay: 4000
    });
});

</script>

<script type="text/javascript">

$(document).ready(function(){
	$('#carousel').tinycarousel({
        pager: true,
        display: 1,
        interval:true
    });
});

</script>

<script type="text/javascript" src="/static_1/plugins/bookingwidget/jquery.bookingwidget.js"></script>

<script type="text/javascript">

function showGallery(elements){
	$.fancybox(elements,{'prevEffect' : 'none',
	           'nextEffect' : 'none',
	           'type' : 'image',
	           'arrows' : true,
	           'nextClick' : true,
	           'mouseWheel' : true,
	           'helpers' : {
					title : {
						type : 'outside'
					},
					overlay : {
						opacity : 0.8,
						css : {
							'background-color' : '#000'
						}
					},
					thumbs : {
						width : 50,
						height : 50
					}
				}
	}
	           );
};


$(".myFancyPopup").fancybox({
	maxWidth	: 800,
	maxHeight	: 600,
	fitToView	: false,
	width		: '70%',
	height		: '70%',
	autoSize	: false,
	aspectRatio : false,
	closeClick	: false,
	openEffect	: 'none',
	closeEffect	: 'none'
});

$(document).ready(function (){
    $(".modal-booking-widget").bookingWidget({spinnerColor: "white"});
});

$(".button-promotion").fancybox({
  width: 800,
  beforeLoad: function() {
    $(".room-selector").val("1");
    $(".datepicker1").val("");
    $(".datepicker2").val("");
    $(".hab2").hide();
    $(".hab3").hide();
  }
});

$(function(){
	if (window.PIE) {
		$(".css3").each(function(){
			PIE.attach(this);
		});
	}
});

if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}

</script>


</body>
</html>
