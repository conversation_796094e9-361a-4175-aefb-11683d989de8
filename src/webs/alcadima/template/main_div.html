<div id="main-sections">
	<ul id="main-sections-inner" class="container">
		{% for section in main_sections %}
		<div class="main-section-div-wrapper" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}
					<a href="{{host|safe}}/{{section.friendlyUrl}}">
						{{ section.title|safe }}
					</a>

				<ul>

				{% for subsection in section.subsections %}
					<li>
						<a href="{{host|safe}}/{{subsection.friendlyUrl}}">
						{{ subsection.title|safe}}
						</a>
					</li>
				{% endfor %}
				</ul>

			{% else %}
				<a href="{{host|safe}}/{{section.friendlyUrl}}">{{ section.title|safe}}</a>
			{% endif %}
		</div>
		{% endfor %}
	</ul>
</div>