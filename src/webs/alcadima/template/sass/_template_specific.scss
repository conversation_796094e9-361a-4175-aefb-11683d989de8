/* FOR DEVELOPMENT ONLY ----- */

body {
  line-height: 24px;
  font-family: 'Lato', arial;
  color: $gray-2;
}

strong {
  font-weight: bold;
}

#ui-datepicker-div {
  font-size: 12px;
  z-index: 1200;
}

a {
  text-decoration: none;
}

.wrapper-old-web-support {
  color: rgb(63, 36, 3);
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.zvr-widget {
  overflow: hidden;
  font-family: Arial, Helvetica, sans-serif !important;
  font-size: 13px !important;
  float: right;
  text-align: right;
  width: 49%;
}

.widget_container {
  background-color: rgb(243, 244, 248);
  width: 392px;
  padding: 20px 0px;
  margin-bottom: 12px;
  box-shadow: 0px 0px 12px rgb(243, 244, 248);
}

/************* header *************/

header {
  position: absolute;
  width: 100%;
  min-width: 1140px;
  top: 0;
  z-index: 800;
  background: rgba(255, 255, 255, 0.6);
  color: $corporate-3;
}

#logoDiv {
  margin: 10px 0;
}

#languageDiv {
  text-align: right;
  font-size: 12px;
  text-transform: uppercase;
  margin-top: 10px;
}

#topMenuDiv {
  text-align: right;
  font-size: 12px;
  margin-top: 10px;
}

#topMenuDiv a, #languageDiv a {
  text-decoration: none;
  color: $corporate-3;
}

#social {
  float: right;
  margin-top: 5px;
}

#social a {
  text-decoration: none;
  color: $white;
}

#languageDiv a {
  padding: 3px 5px;
  margin: 0 5px;
}

#languageDiv a:hover {
  padding: 3px 5px;
  background-color: $corporate-3;
  color: $white;
  border-radius: 10px;
}

#languageDiv .selected {
  padding: 3px 5px;
  background-color: $corporate-3;
  color: $white;
  border-radius: 10px;
}

#wrapper_nav {
  background-color: $gray-4;
  @include box-shadow($gray-2 0 0 15px);
}

#mainMenuDiv {
  font-size: 14px;
  z-index: 99;
  clear: both;
}

#mainMenuDiv a {
  padding: 5px 20px;
  text-decoration: none;
  text-transform: uppercase;
  display: inline-block;
  color: $corporate-3;
}

#mainMenuDiv a:hover {
  color: $corporate-3;
  background-color: $gray-3;
}

#section-active {
  text-decoration: none;
  text-transform: uppercase;
  display: inline-block;
  color: $corporate-3;
  background-color: $gray-3;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
  background-color: $white;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
  border-bottom: 1px solid $gray-3;
}

#main-sections-inner {
  text-align: justify;
  height: 34px;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper li {
  background-color: $gray-4;
}

.main-section-div-wrapper li:hover {
  background-color: $gray-3;
}

/************* slider y banner *************/

#slider_container {
  position: relative;
}

#booking {
  width: 240px;
  position: absolute;
  top: 152px;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 500;
}

#ticks-container {
  color: $corporate-3;
  font-size: 11px;
  padding-left: 10px;
  padding-bottom: 5px;
  text-align: center;
  line-height: 14px;
  z-index: 500;
}

/************* banners top **************/

#wrapper_services {
  margin-top: 25px;
}

.services_column {
  text-align: center;

  .services_column_title {
    color: $corporate-2;
    font-size: 14px;
    margin-top: -15px;
    padding: 5px;
    display: block;
  }
  .services_column_description {
    color: $gray-2;
    line-height: 18px;
    font-size: 13px;
    padding: 0 10px 10px;
    display: block;
  }
}

/************* contenido *************/

#content {
  background: $white url("/img/alcaa/fondo_content.png");
  position: relative;
  @include box-shadow($gray-1 0 0 15px);
  z-index: 1000;

  h3 {
    font-size: 18px;
    padding-bottom: 30px;
    font-weight: bold;
    color: $corporate-2;
  }
  h4 {
    font-size: 16px;
    padding: 25px 0 20px;
    font-weight: bold;
  }
}

.content-page-wrapper {
  padding: 25px 0 0;
  clear: left;
}

#main_text {
  padding-bottom: 15px;
  font-size: 15px;

  li {
    list-style: disc;
    margin-left: 40px;
  }
}

.banners_home_right {
  margin-top: 55px;
  text-align: center;

  .banners_home_right_title {
    color: $corporate-1;
    font-size: 14px;
    padding: 5px;
    display: block;
  }
  .banners_home_right_description {
    color: $gray-2;
    line-height: 18px;
    font-size: 13px;
    padding: 0 10px 10px;
    display: block;
  }
  img {
    @include box-shadow($gray-3 3px 3px 4px);
  }
}

#banners_wrapper {
  margin-top: 50px;

  .banners_lateral {
    margin-bottom: 10px;
    text-align: center;

    img {
      width: 100%;
    }
    .banners_lateral_title {
      color: $corporate-2;
      font-size: 14px;
      padding: 5px;
      display: block;
    }
    .banners_lateral_description {
      color: $gray-2;
      line-height: 18px;
      font-size: 13px;
      margin-top: -10px;
      padding: 0 5px 10px;
      display: block;
    }
  }
}

/************* banners bottom ************/

#wrapper_banners_bottom {
  padding: 20px 0;
}

#banners_bottom_container {
  background: $white url("/img/alcaa/fondo_content.png");
}

.banners_bottom {
  position: relative;
  font-size: 14px;
  overflow: hidden;
  height: 247px;

  .banner_bottom_title {
    font-size: 18px;
    padding-bottom: 5px;
    font-weight: bold;
  }
  .banner_bottom_description {
    color: $white;
    background-color: $corporate-4;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    padding: 5px 10px;
    position: absolute;
    height: 50px;
    bottom: 0;
    -webkit-transform: translateY(53px);
    transform: translateY(53px);
    -webkit-transition: opacity 1s, -webkit-transform 1s;
    transition: opacity 1s, transform 1s;
  }
}

.banners_bottom:hover .banner_bottom_description {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.9;
}

/************* footer *************/

footer {
  padding: 10px 0 30px;
  background: $corporate-1;
}

.footer_column {
  margin-top: 10px;
  color: $corporate-3;
  font-size: 14px;

  a {
    color: $corporate-3;
    font-size: 14px;
  }
  h3 {
    margin: 0 0 15px;
    font-weight: bold;
  }
}

#footer {
  text-align: center;
}

.wrapper_bottom_footer {
  background-color: $corporate-3;
  position: relative;

  #footer_bottom_text {
    font-size: 10px;
    margin-top: 10px;
    line-height: 14px;
    color: $white;
  }
  a {
    color: $white;
    text-decoration: none;
  }
}

.banners_footer {
  float: left;
  margin: 10px;
}

#newsletter {
  color: $corporate-3;
  border-radius: 10px;
  clear: left;
}

#suscEmail {
  width: 222px !important;
  height: 20px;
}

#newsletter h2 {
  margin: 0 0 15px;
  color: $corporate-3;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold;
}

#newsletter input {
  margin: 10px 0;
}

#newsletter #form-newsletter {
  margin: 0;
}

#newsletter .bordeInput {
  border: 1px solid $gray-1 !important;
  width: 150px;
}

#newsletter button {
  width: 90px;
  margin: 0 auto;
  background: $corporate-3;
  color: $white;
  border: none;
  text-align: center;
  font-size: 14px;
  border-radius: 5px;
  padding: 4px 0px;
  cursor: pointer;
  font-family: 'Roboto', arial;
}

#newsletter button:hover {
  background: $corporate-2;
}

/*********************** banners footer ************************/

.banners_footer_wrapper {
  position: relative;
  top: -20px;
  width: 800px;
  height: 80px;
  background-color: white;
  @include box-shadow($gray-1 0 0 15px);
  margin: 0 auto;

  .banners_footer {
    float: left;
  }
}

/***************** otros retoques *****************/

button {
  width: 120px;
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  background-color: $corporate-3;
  color: $white;
  font-size: 18px;
  cursor: pointer;
  overflow: visible;
}

button:hover {
  background-color: $corporate-2;
}

.contVerDetalles a {
  color: $corporate-2;
}

#workWithUs {
  display: none;
}

#my-bookings-form, #contactContent {
  margin-top: 25px;

  .bordeInput {
    width: 250px;
    margin: 5px 0 15px;
    padding: 3px 0;
    display: block;
  }
  p {
    margin-bottom: 15px;
    font-size: 15px;
  }
}

#cancelButton {
  display: none;
}

.promotions-wrap {
  padding: 0 30px;
}

ul.gallery li {
  list-style: none !important;
  margin: 10px 6px !important;
  width: 125px !important;
  height: 95px !important;
  border: solid 1px #cccccc;
  background-color: #ffffff;
  padding: 3px;
}

ul.gallery li img {
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
  height: 100%;
  background: none !important;
  border: none !important;
}

#contact input, #contact textarea {
  border: 1px solid $gray-3 !important;
}

#contact #contact-button-wrapper {
  float: left !important;
  width: auto;
}

#contactContent h1 {
  color: $gray-2 !important;
}

#itemDescription {
  width: 420px;
}

#link_ver_mas {
  display: none;
}