$(window).load(function () {

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");
        }

        var package_preselection = $(this).attr('data-smartpackage');
        if (package_preselection) {
            $(".paraty-booking-form").each(function () {
                var package_preselection = $("<input type='hidden' name='package_preselection' value='true'></input>");
                package_preselection.addClass('hidden_package_preselection');
                $(this).append(package_preselection);
                $(this).find(".promocode_wrapper").addClass("promocode_hidden");
            });
        } else {
            $(".paraty-booking-form").each(function () {
                $(this).find(".promocode_wrapper").removeClass("promocode_hidden");
            });
        }

        smartDatas($(this));
    });

    effects_sass();

    $(".lang_selected").click(function () {
        $(".lang_options_wrapper").slideToggle();
    });

    $("#menu_controller").click(function () {
        $(this).toggleClass('opened');
        $(".full_screen_menu").toggleClass("active");
        $("body").toggleClass("overflow-hidden");
    });

    var m = new Date();
    var dateString = ("0" + m.getUTCDate()).slice(-2) + "/" + ("0" + (m.getUTCMonth()+1)).slice(-2) + "/" + m.getUTCFullYear();
    DP_extend_info.update_dates_v7(dateString);

    $(".hotel_search_input").keydown(function () {
         setTimeout(function(){
             searchHotelElement();
         }, 100);
    });

    $(".social_rooms, .close_social_rooms_button").click(function (e) {
        e.preventDefault();
        $(".social_rooms").toggleClass('active');
        $(".social_elements_wrapper").toggleClass('active');
    });

});

$(window).scroll(function() {
    showWidget();
});

function showWidget() {
    var main_menu = $("#main_menu"),
    booking_widget = $("#full_wrapper_booking"),
    actual_position = $(window).scrollTop(),
    slider_height = $("#slider_container").height(),
    menu_showed = booking_widget.hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        booking_widget.addClass('floating_booking').addClass('showed');
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        booking_widget.removeClass("floating_booking").removeClass('showed');
        $(".full_screen_menu").removeClass("active");
    }
}

function searchHotelElement(){
    var searched_hotel = $(".hotel_search_input").val();

    $(".hotel_selector li").each(function(){
        var actual_html = $(this).html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();


        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).css('display', 'none');
        }else{
            $(this).css('display', 'block');
        }

        if(searched_hotel == ""){
            $(this).css('display', 'block')
        }
    })
}

function booking_click(namespace) {

    prepare_booking_popup();
    open_booking_full();

    if (!only_execute_once) {
        only_execute_once = true;
    }

    if (namespace){
        $(".hotel_selector #" + namespace).trigger('click');
    }
}

function animate_scroll(animate_selector) {
    var element_identifier = $(animate_selector),
        element_top_position = (element_identifier.offset().top),
        body = $("html, body");

    body.stop().animate({scrollTop: element_top_position}, 500, 'swing');
}

function smartDatas(selector) {
    var promocode = selector.attr("data-promocode"),
        smart_promocode = selector.attr('data-smartpromocode'),
        data_ini = selector.attr("data-smartdateini"),
        data_fin = selector.attr("data-smartdatefin");

    if (promocode) {
        $("#data .paraty-booking-form input[name=promocode]").val(promocode);
    }

    if (smart_promocode) {
        $("#data .paraty-booking-form input[name=promocode]").val(smart_promocode);
    }

    if(data_ini) {
        $("#data input[name=startDate]").val(data_ini);

        if (data_fin) {
            $("#data input[name=endDate]").val(data_fin);
        } else {
            var end_date = $.datepicker.parseDate("dd/mm/yy", data_ini);
            end_date.setDate(end_date.getDate() + 1);
            end_date = $.datepicker.formatDate("dd/mm/yy", end_date);
            $("#data input[name=endDate]").val(end_date);
            $("#data input[name=endDate]").datepicker("option", "minDate", end_date);
        }
    }
}