<div class="detailed_room_wrapper {% if offer_individual %}offer_individual_element{% endif %}">

    <div class="room_detail_image_wrapper container12">
        <img class="room_detail_image" src="{{ offer_details.pictures.0.servingUrl|safe }}=s1900" alt="{{offer_details.pictures.0.altText}}">
    </div>

    <div class="room_details_text">
        <h1 class="room_title">
            {{ offer_details.subtitle|safe }}
        </h1>
        {% if not offer_individual and not disabled_pictures_see_more %}
            <a href="javascript:showGallery2([ {% for picture in offer_details.pictures %} {href : '{{ picture.servingUrl }}=s1900'}, {% endfor %} ]);"
               class="see_more_pictures_detailed">
                <span>{{ T_ver_fotos }}</span>
                <img class="see_more_img" src="/img/{{ base_web }}/right_arrow_corp.png?v=1">
            </a>
        {% endif %}
        {% if indidividual_room_element or individual_offer_button %}
        <a href="#data" class="button-promotion"
           {% if individual_offer_promocode %}data-promocode="{{ individual_offer_promocode }}"{% endif %}
           {% if individual_offer_smartdatefin %}data-smartdatefin="{{ individual_offer_smartdatefin }}"{% endif %}
           {% if individual_offer_smartdateini %}data-smartdateini="{{ individual_offer_smartdateini }}"{% endif %}
           {% if booking_disabled_until %}disabled_booking="{{ booking_disabled_until }}"{% endif %}>{{ T_reservar }}</a>
        {% endif %}
        <div class="room_description">{{ offer_details.content|safe }}</div>
        {% if offer_individual %}
            <div id="shareSocialArea">
                <script type="text/javascript"> var addthis_config = {ui_language: "es"} </script>
                <div class="addthis_toolbox addthis_default_style" addthis:title="{{ offer_details.subtitle|safe }}" addthis:description='{{ offer_details.description_share|safe }}'>
                    <a href="http://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                       class="addthis_button_compact" style="color:#5A5655"><span class="share_text">{{ T_compartir }}</span></a>
                    <span class="addthis_separator">|</span>
                    <a class="addthis_button_facebook"></a>
                    <a class="addthis_button_google"></a>
                    <a class="addthis_button_twitter"></a>
                    <a class="addthis_button_favorites"></a>
                </div>
                <script type="text/javascript"
                        src="https://s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>
            </div>
        {% endif %}

        <div id="unavailable_offer">
            {{ T_no_disponible_hasta }} <strong>{{ booking_disabled_until }}</strong>
        </div>
    </div>

</div>