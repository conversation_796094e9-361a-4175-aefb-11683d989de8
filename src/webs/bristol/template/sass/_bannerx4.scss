.bannerx4_wrapper {
  display: table;
  width: 100%;
  .banner {
    width: calc(100% / 4);
    background-color: black;
    float: left;
    position: relative;
    height: 400px;
    overflow: hidden;
    &:before {
      content: '';
      @include full_size;
      background-color: rgba(black, .4);
      z-index: 1;
      @include transition(all, .6s);
    }
    img {
      @include center_image;
    }
    .banner_title {
      @include center_xy;
      z-index: 5;
      width: 100%;
      text-align: center;
      .title {
        font-family: $title_family;
        font-size: $title_size;
        font-style: italic;
        letter-spacing: 1px;
        color: white;
      }
      .link_wrapper {
        visibility: hidden;
        overflow: hidden;
        max-height: 0;
        @include transition(all, .6s);
        svg {
          @include svg_styles;
          * {
            fill: white;
          }
        }
        .link {
          display: inline-block;
          padding: 15px 20px;
          background-color: $corporate_2;
          color: white;
          font-size: 18px;
          text-transform: uppercase;
          @include transition(all, .6s);
          &:hover {
            background-color: $corporate_1;
          }
        }
      }
    }
    &:hover {
      .link_wrapper {
        visibility: visible;
        max-height: 150px;
      }
    }
  }
}