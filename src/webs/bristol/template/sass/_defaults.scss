//Base web (change too in template<PERSON>and<PERSON> and in config.rb)
$base_web: "brisl";

@import url('https://fonts.googleapis.com/css?family=Barlow:300,400,700|Libre+Baskerville:400,400i,700&display=swap');
// colors definitions
$white: rgb(255, 255, 255);
$black: rgb(0, 0, 0);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #002855;
$corporate_2: #3AAFA9;
$corporate_3: #7C7C7C;
$corporate_4: #fafafa;
$black: #333;
$gray: #7C7C7C;
$lightgray: #E0E0E0;

$title_family: "Libre Baskerville", sans-serif;
$text_family: "Barlow", sans-serif;
$title_size: 32px;
$description_size: 19px;
$line_height: 25px;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

@mixin title_styles() {
  font-family: $title_family;
  font-size: $title_size;
  color: $corporate_1;
  font-style: italic;
  letter-spacing: 1px;
  line-height: 34px;
  text-align: center;
  .subtitle {
    font-size: 18px;
    color: $corporate_2;
    letter-spacing: 0;
    font-style: normal;
    font-family: $text_family;
    display: block;
  }
  svg {
    @include svg_styles;
  }
}

@mixin text_styles() {
  text-align: center;
  font-weight: 300;
  font-size: $description_size;
  line-height: $line_height;
  color: $gray;
}

@mixin btn_styles() {
  display: inline-block;
  padding: 10px 50px;
  cursor: pointer;
  border: none;
  font-family: $text_family;
  font-size: 18px;
  color: white;
  text-transform: uppercase;
  background-color: $corporate_1;
  @include transition(all, .6s);
  &:hover {
    background-color: $corporate_2;
  }
}

@mixin svg_styles {
  display: block;
  margin: 0 auto;
  height: auto;
  width: 100px;
  * {
    fill: $gray;
  }
}

