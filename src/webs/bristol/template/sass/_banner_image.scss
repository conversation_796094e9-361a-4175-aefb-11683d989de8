.banner_iamge_full_wrapper {
  padding: 50px 0;
  .banner_image_text {
    padding: 30px 0;
    .title {
      @include title_styles;
      font-size: 24px;
      padding: 0 250px 30px;
    }
    .text {
      @include text_styles;
    }
  }
  .banner_image_wrapper {
    display: table;
    width: 100%;
    .banner {
      width: 100%;
      height: 500px;
      margin-bottom: 30px;
      display: block;
      position: relative;
      overflow: hidden;
      background-color: black;
      img {
        @include center_image;
      }
      .banner_title {
        @include center_xy;
        width: 600px;
        background-color: rgba(white, .9);
        text-align: center;
        padding: 30px 0;
        @include transition(all, .6s);
        h2 {
          @include title_styles;
          @include transition(all, .6s);
          padding-bottom: 50px;
        }
        svg {
          @include center_xy;
          @include svg_styles;
          width: 150px;
          * {
            @include transition(all, .6s);
          }
        }
        .desc {
          @include text_styles;
          @include transition(color, .6s);
        }
        .link {
          font-size: 18px;
          text-transform: uppercase;
          color: $corporate_1;
          @include transition(color, .6s);
        }
      }
      &:hover {
        .banner_title {
          background-color: rgba($corporate_1, .8);
          h2, .desc, .link {
            color: white;
          }
          svg * {
            fill: white;
          }
        }
      }
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}