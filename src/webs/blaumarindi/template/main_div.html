<nav id="main_menu" itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner">
    <div class="main_menu">
        {% if header_menu_logo %}
            <a href="{{host|safe}}/"><img src="{{ header_menu_logo.0 }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}" class="header_menu_logo"/></a>
    {% endif %}
        {% for section in main_sections %}
        {% if section.subsections %}
            <div class="section_with_subsections">
                <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank" {% elif not section.disabled %} href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}" {% endif %}
                        {% if sectionToUse.title == section.title %}id="section-active" {% endif %}
                        {% if section.disabled %}class="disabled"{% endif %}>
                    {{ section.title|safe }}
                </a>
                <ul class="subsections_wrapper">
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                                   {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        {% else %}
            {% if section.title %}
                <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank" {% else %} href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}" {% endif %}
                {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    <span itemprop="name">{{ section.title|safe }}</span>
                </a>
            {% endif %}
        {% endif %}
        {% endfor %}
    </div>
    <div class="social">
        {%if facebook_id %}
            <a href="http://www.facebook.com/{{facebook_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-facebook-f"></i>
            </a>
        {% endif %}
        {% if instagram_id %}
            <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank" rel="nofollow">
                <i class="fab fa-instagram"></i>
            </a>
        {% endif %}
        {% if twitter_id %}
            <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-twitter"></i>
            </a>
        {% endif %}
        {% if linkedin_id %}
            <a href="http://www.linkedin.com/company/{{ linkedin_id }}/" target="_blank" rel="nofollow">
                <i class="fab fa-linkedin"></i>
            </a>
        {% endif %}
        {% if google_plus_id %}
            <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-google-plus"></i>
            </a>
        {% endif %}
        {% if youtube_id %}
            <a href="https://www.youtube.com/{{youtube_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-youtube"></i>
            </a>
        {% endif %}
        {% if pinterest_id %}
            <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank" rel="nofollow">
                <i class="fab fa-pinterest-p"></i>
            </a>
        {% endif %}
    </div>
</nav>