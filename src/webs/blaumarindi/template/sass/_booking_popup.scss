body #ui-datepicker-div {
  padding-bottom: 0;
  td a, td span {
    padding: .5em;
  }
  .ui-datepicker-header a .ui-icon:before {
    font-family: "Font Awesome 5 Pro", sans-serif;
  }
}

.booking-data-popup {
  .fancybox-outer {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }
}
div#data {
  background: rgba($corporate_1, 0.75);
  margin: 0;

  div#wrapper_booking_fancybox {
    display: table;
    width: 100%;
    position: absolute;
    bottom: 0;
    top: 0;
    margin: auto;

    @include center_xy();
  }

  .destination_wrapper {
    float: none;
    display: block;
    border-width: 0;
    label {
      font-size: 20px;
      text-transform: uppercase;
    }
    .destination_field {
      @extend .fa-angle-down;
      &:before {
        @extend .fa;
        @include center_y;
        right: 10px;
      }
      &:after {
        background: transparent !important;
      }
      textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        width: 100%;
        height: 50px;
        font-size: 20px;
        padding: 10px;
        resize: none;
        border-width: 0;
      }
    }
  }
  .hotel_selector {
    position: absolute;
    background: white;
    box-shadow: 0 0 30px rgba(0,0,0,0.3);
    width: 500px;
    z-index: 4;
    box-sizing: border-box;
    top: 89px;
    .hotel_selector_filter {
      display: none;
    }
    .hotel_selector_inner {
      width: 100%;
      height: 100%;
      overflow: auto;
      display: inline-block;
      vertical-align: top;
      padding: 15px 0;
    }
    .title_group {
      display: none;
      color:black;
      padding: 13px 10px 3px;
    }
    ul {
      li {
        display: block;
        padding: 5px 10px;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        font-weight: 400;
        @include transition(all, .4s);
        .title_selector {
          color: #4b4b4b;
        }
        &.hotel_selector_option {
          padding-left: 20px;
        }
        &:hover .title_selector {
          color: $corporate_1;
        }
      }
    }
  }

  .adultos.numero_personas, .ninos.numero_personas, .bebes.numero_personas {
    & > label {
      display: none !important;
    }
  }

  .booking_title1, .best_price {
    display: none;
  }
  #contador_noches {
    display: none;
  }

  #contenedor_hotel {
    .selector_hoteles {
      background-color: white;
      margin: 0 0 20px;
      width: 100%;
      input {
        color: #4B4B4B;
      }
      &:after {
        color: $corporate_1
      }
    }
  }

  #motor_reserva {
    width: 595px;
    margin: auto;
    display: table;
  }

  div#fecha_entrada, div#fecha_salida {
    width: 290px;
    float: left;
    height: 125px;
  }

  div#fecha_salida {
    float: right;
    margin-left: 0 !important;
  }

  label#titulo_fecha_entrada, label#titulo_fecha_salida {
    display: block;
    color: #999;
    width: 100% !important;
    text-align: center;
    text-transform: uppercase;
    font-size: 17px;
    font-weight: 500;
    background: white;
    margin-bottom: 2px;
    padding: 9px 0;
  }

  #contenedor_fechas {
    width: 100%;
    margin-bottom: 16px;
  }

  .wrapper-old-web-support {
    display: none !important;
  }

  #fecha_entrada input, #fecha_salida input {
    border: 0 !important;
    height: 84px !important;
    width: 100% !important;
    text-align: center !important;
    box-sizing: border-box !important;
    font-size: 31px !important;
    color: #4b4b4b !important;
    padding-right: 40px;
    border-radius: 0;
    background: white url(/static_1/images/booking_5/entry_date.png) no-repeat 85% center !important;

    &::-webkit-input-placeholder {
      color: #4b4b4b !important;
    }
  }

  #contenedor_habitaciones {
    margin: auto;
    width: 290px;
    float: left;
    height: 125px;

    label {
      display: block;
      color: #999;
      width: 100% !important;
      text-align: center;
      text-transform: uppercase;
      font-size: 17px;
      float: none;
      background: white;
      margin-bottom: 2px;
      padding: 9px 0;
    }

    select#selector_habitaciones {
      -webkit-border-radius: 0 !important;
      -moz-border-radius: 0 !important;
      border-radius: 0 !important;
      border: 0;
      width: 260px;
      float: left;
      height: 125px;
      background: white;
      -webkit-appearance: none;

      option {
        text-align: center;
      }
    }

    .selectric {
      height: 83px;
      border-radius: 0;
      margin-top: 0;

      p.label {
        color: #4b4b4b;
        text-align: center;
        box-sizing: border-box !important;
        font-size: 31px !important;
        padding-top: 22px;
      }

      .button {
        background: transparent !important;
        right: 27px;
      }
    }

    .selectricItems li {
      color: #4b4b4b;
      text-align: center;
      box-sizing: border-box !important;
      font-size: 21px !important;
      padding: 12px 12px 10px;
    }
  }

  .selectricWrapper {
    width: 100% !important;
  }

  #contenedor_opciones {
    float: right;
    margin-top: -125px;

    #hab1, #hab2, #hab3 {
      margin: auto;
      width: 290px;
      float: left;
      height: 125px;
    }

    #hab1 {
      margin-left: 305px;
    }

    #hab2, #hab3 {
      margin-top: 16px;
      display: block !important;
    }

    #hab3 {
      float: right;
    }

    label.numero_habitacion {
      color: #999;
      font-weight: 500;
      width: 100% !important;
      text-align: center;
      display: block !important;
      text-transform: uppercase;
      font-size: 17px;
      background: white;
      float: none;
      margin-bottom: 2px;
      padding: 9px 0;
    }
  }

  .adultos.numero_personas, .ninos.numero_personas, .bebes.numero_personas {
    margin: 0;
    position: relative;
    display: inline-block;

    option {
      display: none;
    }
  }

  .adultos.numero_personas, .ninos.numero_personas {
    width: 144px;
    text-align: center;
    float: left;
    margin-right: 2px;
  }

  .ninos.numero_personas {
    margin-right: 0;
    .selectricItems {
      left: -84px !important;
    }
  }

  #contenedor_opciones.contenedor_opciones_babies {
    .adultos.numero_personas, .ninos.numero_personas, .bebes.numero_personas {
      width: 95px;
      margin-right: 2px;
    }
    .bebes.numero_personas {
      margin-right: 0;
      .selectricItems {
        left: -180px !important;
      }
    }
  }
  .bebes.numero_personas {
    width: 32%;

    .selectricItems {
      left: -180px !important;
    }
  }

  .ninos {
    float: left;

    label#info_ninos {
      position: absolute;
      top: 20px;
      color: black;
      right: 0px;
      font-size: 9px !important;
      display: inline-block;
    }
  }

  .selectricWrapper.selector_adultos, .selectricWrapper.selector_ninos, .selectricWrapper.selector_bebes {
    .selectric {
      height: 83px;
      border-radius: 0;
      margin-top: 0;
    }

    p.label {
      color: #4b4b4b;
      text-align: center;
      padding-right: 0 !important;
      box-sizing: border-box !important;
      padding-top: 23px;
      font-size: 18px !important;
      &:lang(de) {
        font-size: 16px !important;
      }
    }

    .button {
      background: transparent !important;
      width: 16px;
      height: 20px;
      top: 5px;
      right: 10px !important;
    }

    .selectricItems li {
      color: #4b4b4b;
      text-align: center;
      box-sizing: border-box !important;
      font-size: 16px !important;
      padding: 6px 12px 4px;
    }
  }

  fieldset#envio {
    width: 100%;
    margin-left: 0;

    input#promocode {
      float: left;
      width: 290px;
      border-radius: 0;
      border: 0;
      box-sizing: border-box;
      margin-top: 10px;
      height: 90px;
      text-align: center;
      background: rgba(255, 255, 255, 0.3);
      font-size: 31px !important;
      font-weight: 300;
      color: white;

      &::-webkit-input-placeholder {
        color: white;
        font-size: 18px;
        font-weight: 300;
        text-transform: uppercase;
      }
    }

    button#search-button {
      cursor: pointer;
      display: block;
      position: relative;
      float: right;
      height: 90px;
      width: 290px;
      border-radius: 0;
      border: 0;
      box-sizing: border-box;
      margin-top: 10px;
      background: $corporate_2;
      color: white;
      text-transform: uppercase;
      font-size: 27px !important;
      @include transition(border-radius, .6s);

      &:hover {
        background-color: darken($corporate_2,10%);
      }
    }
  }

  div#hab2, div#hab3 {
    .disabled_overlay {
      display: none;
    }

    &.disabled {
      opacity: 0.4;
      position: relative;

      .disabled_overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: block;
      }
    }
  }

  #booking_engine_title {
    #booking_title1, #best_price {
      display: none;
    }

    h4#booking_title2 {
      color: white;
      margin-bottom: 25px;
      text-transform: uppercase;
      font-size: 30px;
      text-align: center;
      margin-top: 0;
      span {
        font-weight: 300;
      }
    }
  }

  #contenedor_opciones {
    div#hab2.disabled {
      display: none !important;

      & + #hab3 {
        display: none !important;
      }
    }
  }

  .selectricItems {
    width: 288px !important;
    top: 94% !important;
    left: 11px !important;
    z-index: 9999;
  }

  #contenedor_hotel {
    .hotel_selector {
      overflow: visible !important;
      .hotel_search_input_wrapper {
        display: none;
      }
      .selector_view {
        max-height: 90vh;
      }
      ul {
        li {
          display: block;
          padding: 5px 10px;
          color: #444;
          cursor: pointer;
          font-size: 11px;
          text-transform: uppercase;
          @include transition(opacity, .4s);
          &.title_group {
            color: $corporate_1;
            font-weight: bold;
          }
          &.hotel_selector_option {
            padding-left: 20px;
          }
          &:hover {
            opacity: .8;
          }
        }
      }
    }
  }

  .destination_wrapper {
    width: 100%;
    background-color: white !important;
    color: $corporate_2;
    margin-bottom: 15px;
    border-bottom: 0;

    label {
      display: none;
    }

    .destination_field {
      input {
        width: 100%;
        height: 55px;
        color: $corporate_2;
        padding-left: 15px;
        font-weight: 500;

        &::-webkit-input-placeholder {
          color: $corporate_2;
          text-transform: uppercase;
          font-weight: bolder;
        }

        &:-moz-placeholder {
          /* Firefox 18- */
          color: $corporate_2;
        }

        &::-moz-placeholder {
          /* Firefox 19+ */
          color: $corporate_2;
        }

        &:-ms-input-placeholder {
          color: $corporate_2;
        }
      }
      &:after {
        color: $corporate_2;
      }
    }
  }

}

.booking-data-popup .fancybox-skin {
  background: transparent;
}
.booking-data-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: url(/img/#{$base_web}/close_button.png) no-repeat center;
  background: none;

  &:before {
    content: "x";
    color: white;
    font-size: 85px;
    line-height: 36px;
  }
}

.booking-data-popup .fancybox-outer {
  background: none;
}

.contact_bottom_popup {
  display: block;
  width: 535px;
  margin: 20px auto;
  text-align: center;
  color: white;

  .phone_hotel_booking_popup, .email_hotel {
    display: inline-block;
    padding-left: 30px;
    line-height: 25px;
  }

  .phone_hotel_booking_popup {
    margin-right: 10px;
    background: url(/img/#{$base_web}/booking_icos/phone_ico.png) no-repeat left center;
  }

  .email_hotel {
    background: url(/img/#{$base_web}/booking_icos/mail_ico.png) no-repeat left center;
  }
}

div#data .selectricWrapper.selector_adultos .selectric {
}