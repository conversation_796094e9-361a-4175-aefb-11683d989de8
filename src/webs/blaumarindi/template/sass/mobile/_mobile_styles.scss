@import "banner_club";
@import "banner_destacados";
@import "banner_extra_text";
@import "banner_icons";
@import "banner_images";
@import "banner_map";
@import "banner_offers";
@import "banner_x3";
@import "club_landing";
@import "cycle_banner";
@import "engine_mobile";
@import "gallery_home";

.link_tour_virtual {
  position: absolute;
  z-index: 2;
  top: 9px;
  right: 5px;
  width: 35px;
  padding: 5px;
  background: white;
  border-radius: 50%;
  height: 20px;
}

.logo_360 img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.fancybox-slide--iframe {
  padding: 45px 0;
}

.main_menu ul {
  height: calc(100% - 100px);
}


body {
  .banner_packages_wrapper {
    .pictures {
      height: 250px;

      img {
        @include cover_image;
      }
    }
  }

  &.inner_section .default_content_wrapper .content > .section_content {
    padding-top: 30px;

    > div:nth-of-type(odd) {
      background-color: white;

      > div:nth-of-type(odd) {
        background-color: white;

        .banner {
          &:nth-child(odd) {
            background-color: white;
          }

          &:nth-child(even) {
            background-color: $corporate_2;
          }
        }

        &.section_content {
          > div:nth-of-type(odd) {
            background-color: white;

            .banner {
              &:nth-child(odd) {
                background-color: white;
              }

              &:nth-child(even) {
                background-color: $corporate_2;
              }
            }
          }

          > div:nth-of-type(even) {
            background-color: $corporate_2;

            .banner {
              &:nth-child(odd) {
                background-color: $corporate_2;
              }

              &:nth-child(even) {
                background-color: white;
              }
            }
          }
        }
      }

      > div:nth-of-type(even) {
        background-color: $corporate_2;

        .banner {
          &:nth-child(odd) {
            background-color: $corporate_2;
          }

          &:nth-child(even) {
            background-color: white;
          }
        }

        &.section_content {
          > div:nth-of-type(odd) {
            background-color: $corporate_2;

            .banner {
              &:nth-child(odd) {
                background-color: $corporate_2;
              }

              &:nth-child(even) {
                background-color: white;
              }
            }
          }

          > div:nth-of-type(even) {
            background-color: white;

            .banner {
              &:nth-child(odd) {
                background-color: white;
              }

              &:nth-child(even) {
                background-color: $corporate_2;
              }
            }
          }
        }
      }
    }

    > div:nth-of-type(even) {
      background-color: $corporate_2;

      > div:nth-of-type(odd) {
        background-color: $corporate_2;

        .banner {
          &:nth-child(odd) {
            background-color: $corporate_2;
          }

          &:nth-child(even) {
            background-color: white;
          }
        }

        &.section_content {
          > div:nth-of-type(odd) {
            background-color: $corporate_2;

            .banner {
              &:nth-child(odd) {
                background-color: $corporate_2;
              }

              &:nth-child(even) {
                background-color: white;
              }
            }
          }

          > div:nth-of-type(even) {
            background-color: $corporate_2;

            .banner {
              &:nth-child(odd) {
                background-color: $corporate_2;
              }

              &:nth-child(even) {
                background-color: white;
              }
            }
          }
        }
      }

      > div:nth-of-type(even) {
        background-color: white;

        .banner {
          &:nth-child(odd) {
            background-color: white;
          }

          &:nth-child(even) {
            background-color: $corporate_2;
          }
        }

        &.section_content {
          > div:nth-of-type(odd) {
            background-color: white;

            .banner {
              &:nth-child(odd) {
                background-color: white;
              }

              &:nth-child(even) {
                background-color: $corporate_2;
              }
            }
          }

          > div:nth-of-type(even) {
            background-color: $corporate_2;

            .banner {
              &:nth-child(odd) {
                background-color: $corporate_2;
              }

              &:nth-child(even) {
                background-color: white;
              }
            }
          }
        }
      }
    }
  }
}