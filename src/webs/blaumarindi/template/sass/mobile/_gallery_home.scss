.banner_gallery_content {
  .content_title {
    @include title_styles;
    margin: 40px 0 0;
  }

  .content_text {
    @include text_styles;
    padding-top: 15px;
  }
}

.gallery_filter_wrapper {
  @include base_banner_styles;
  @include display_flex;
  width: 100%;
  padding: 20px calc((100% - 1140px) / 2);

  .gallery_group_wrapper {
    display: inline-block;
    width: 100%;
    margin-bottom: 30px;

    .image_wrapper {
      position: relative;
      display: inline-block;
      width: 100%;
      height: 260px;
      overflow: hidden;

      &:not(:last-of-type) {
        margin-bottom: 30px;
      }

      img {
        @include cover_image;
      }
    }
  }
}

.btn_link_gallery {
  padding: 10px 35px 5px;
  border-radius: 2px;
  @include btn_styles(true);
  margin: 0 0 50px;
}