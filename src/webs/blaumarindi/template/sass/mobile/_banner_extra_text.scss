.extra_text_wrapper {
   @include base_mobile_styles;

    .extra_text_subtitle_title {
      @include title_styles;
    }
    .extra_text_subtitle_description {
      @include text_styles;

      .hide {
        max-height: 0;
        opacity: .4;
        overflow: hidden;
        @include transition(all, .6s);
      }

      .button_wrapper_see_more {
        margin-top: 30px;
        a {
          @include btn_styles_2;
        }
      }

      &.active {
        .hide {
          max-height: 2100px;
          opacity: 1;
        }

        .button_wrapper_see_more {

          span {
            font-size: 0;

            &:before {
              content: attr(read-less-text);
              font-size: 21px;
            }
          }
        }
      }
    }
  }