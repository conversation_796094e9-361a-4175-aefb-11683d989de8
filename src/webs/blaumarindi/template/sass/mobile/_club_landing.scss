.landing_image {
  height: calc(100vh - 120px);
  margin-top: -5px;
  position: relative;

  &::before {
    content: '';
    @include full_size;
    background: linear-gradient(to bottom, rgba(black, 0.3), transparent);
  }

  > img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .description_text {
    @include center_xy;
    top: 40%;
    width: 80%;
    @include title_styles;
    font-size: 42px;
    line-height: 48px;
    color: white;
    text-align: center;

    img {
      display: block;
      width: 90px;
      margin: 0 auto 30px;
    }
     .subtitle {
      margin-top: 10px;
      text-transform: none;
      font-family: $text_family;
      color: white;
      font-size: 18px;
      line-height: 24px;
    }
  }
}