.cycle_banner_wrapper {
  .cycle_banner {
    .banner {
      @include base_mobile_styles;
      width: 100%;
      background-color: inherit;
      padding: 20px 20px 40px 20px;

      .banner_grid {
        .pic {
          width: 100%;
          height: 100%;
          img {
            @include cover_image;
          }
        }
      }

      .banner_info {
        padding-top: 20px;
        text-align: left;

        .title {
          @include banner_title_styles_2($color_text);
        }
        .desc {
          @include text_styles;
          text-align: left;
          padding: 20px 0;
        }
      }

      &:nth-of-type(odd) {
        background-color: inherit;
      }

      .cycle_banner_gallery {
        @include display_flex;
        width: 100%;
        padding-top: 20px;

        .pic {
          position: relative;
          display: inline-block;
          width: calc((100% / 3) - (20px / 3));
          height: 150px;

          &:not(:nth-of-type(3n)) {
            margin-right: 10px;
          }
          &:nth-of-type(n + 4) {
            display: none;
          }
          img {
            @include cover_image;
          }
        }
      }
    }
  }
}