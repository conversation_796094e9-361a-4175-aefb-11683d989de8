#full_wrapper_booking {
  &:lang(ru) {
    .booking_form .dates_selector_personalized .start_end_date_wrapper .start_date_personalized,
    .booking_form .dates_selector_personalized .start_end_date_wrapper .end_date_personalized,
    .booking_form .rooms_number_wrapper,
    .booking_form .guest_selector .placeholder_text {
      font-size: 12px!important;
    }
  }
  #wrapper_booking {
    position: absolute;
    left: 0;
    right: 0;
    width: 830px;
    bottom: 100px;
    z-index: 1100;
    font-family: $text_family;
    &.fixed {
      @include full_size;
      transform: none;
      width: 100%;
      #full-booking-engine-html-7 .booking_form {
        top: -200px;
      }
    }
  }

  &.floating_booking {

    #wrapper_booking {
      position: fixed;
      top: 80px;
      width: 1080px;
      bottom: auto;
    }

    #full-booking-engine-html-7 {
      .booking_form {
        padding: 0 !important;
        margin-left: 20px;

        .dates_selector_personalized {
          display: inline-flex;
          width: auto;

          .start_end_date_wrapper {
            .start_date_personalized, .end_date_personalized {
              width: 160px;

              .label {
                display: none;
              }
            }
          }
        }

        .rooms_number_wrapper, .guest_selector {
          margin-top: 0;
        }

        .wrapper_booking_button {
          margin-top: 0;
          width: auto;
          padding: 0;
          background-color: transparent !important;

          .promocode_wrapper {
            padding: 10px 10px;
            width: 100px;
            background-color: $widget_bg;
            margin-right: $input_margin;
            border-radius: 2px;
          }

          .submit_button {
            @include btn_styles(true);
            position: relative;
            right: auto;
            top: auto;
            bottom: auto;
            width: 180px;
            height: 100%;
            padding-top: 17px;

            &:lang(de) {
              padding: 10px 25px;
            }

            &:lang(fr) {
              padding: 10px 15px;
            }
          }
        }
      }
    }
    @media only screen and (device-width: 768px) {
      #wrapper_booking {
        width: 920px;
      }
      #full-booking-engine-html-7 .booking_form .wrapper_booking_button {
        width: 150px;
        .promocode_wrapper {
          display: none;
        }
        .submit_button {
          padding: 10px 5px;
          width: 135px;
        }
      }
    }
  }

  #full-booking-engine-html-7 {
    width: 100%;
    z-index: 2;

    .booking_form_title {
      display: none;
    }

    .booking_form {
      @include display_flex;
      width: 100%;
      max-width: 1140px;
      padding-right: 230px !important;
      margin: 0 auto;
      position: relative;
      top: -55px;

      .dates_selector_personalized {
        @include display_flex;
        width: 100%;
        margin: 0;

        .dates_selector_label {
          display: none;
        }

        .start_end_date_wrapper {
          @include display_flex;
          width: 100%;
          padding: 0;
          font-size: 0;
          height: 100%;
          background: none;
          float: none;
          margin: 0;

          .nights_number_wrapper_personalized {
            display: none;
          }

          .start_date_personalized, .end_date_personalized {
            @include option_styles;
            @include input_base_styles;
            @include before_icon;
            margin-top: 0;

            .label {
              padding-right: 15px;
            }

            span:not(.label) {
              padding: 0 3px;
            }
          }

          .start_date_personalized .label.departure {
            display: none;
          }

          .end_date_personalized .label.entry {
            display: none;
          }
        }
      }

      .rooms_number_wrapper {
        @include input_base_styles;
        padding-right: 0;
        cursor: pointer;

        &:before {
        content: '\f236';
        font-family: "Font Awesome 5 Pro";
        display: inline-block;
        font-size: 18px;
        font-weight: 300;
        color: $option_color;
        margin-right: -9px;
        margin-left: 5px;
        z-index: 2;
  }
        width: 190px;



        &:lang(en) {
          //width: 170px;
        }

        .rooms_label {
          display: none;
        }

        .selectricWrapper {
          @include display_flex;
          width: 100% !important;

          .selectric {
            @include display_flex;
            width: 100%;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            margin: 0;
            height: auto;

            .label {
              @include option_styles;
              margin: 0;
            }

            .button {
              display: none;
            }
          }
          .selectricItems {
            width: 120% !important;
            left: -20px;
          }
        }
      }

      .guest_selector {
        @include input_base_styles;
        @include before_icon('\f007');
        width: $width_m;

        label {
          display: none;
        }

        .placeholder_text {
          @include option_styles;

          &:lang(de) {
            font-size: 12px;
          }
        }

        b.button {
          display: none;
        }
      }

      .room_list_wrapper {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        width: 100%;
        background: $corporate_2;
        float: none;
        display: none;
        vertical-align: middle;
        animation: deploy_engine 1s;

        .close_guest_selector {
          position: absolute;
          width: 40px;
          height: 40px;
          border-width: 0;
          border-radius: 0;
          z-index: 1010;
          left: 50px;
          top: 50px;
          cursor: pointer;
          &:before, &:after {
            background: $black;
          }
        }
        .room_list {
          @include center_xy;
          width: auto;
          text-align: center;
          white-space: nowrap;
          .add_room {
            display: inline-block;
            position: relative;
            cursor: pointer;

            i {
              color: white;
              font-weight: 300;
              width: 30px;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              margin-right: 10px;
              border: 1px solid transparent;
              background: $corporate_3;
              height: 30px;
              vertical-align: middle;
              border-radius: 50%;
              font-size: 18px;
            }
          }
          .room_info_wrapper {
            display: table;
            width: 100%;
            height: auto;
            font-weight: 600;
            position: relative;
            color: $black;
            text-transform: uppercase;
            .hotel_name_rooms.with_name {
              background: rgba($corporate_3, .1);
              font-size: 18px;
              padding: 10px;
              line-height: 25px;
              margin-bottom: 5px;
            }
            .dates_wrapper {
              width: 315px;
              font-family: $title_family;
              display: inline-block;
              background: rgba($corporate_3, .2);
              line-height: 25px;
              font-size: 21px;
              letter-spacing: 1.5px;
              padding: 12px 40px 8px;
              margin-bottom: 50px;
            }
          }
          .wrapper_booking_button_guest {
            display: block;
            width: 280px;
            margin: 50px auto 0;
            .promocode_wrapper, .submit_button {
              display: block;
              margin: auto;
              width: 100%;
              padding:0;
              margin: 5px 0;
            }
            .promocode_wrapper {
              border-top: none;
              padding-bottom: 15px;
              .promocode_input {
                width: 65%;
                margin: auto;
                height: 50px;
                border-left: 60px solid transparent;
                outline: none;
                &::-webkit-input-placeholder {
                  color: $corporate_3;
                  font-family: $subtitle_family;
                }
                &::-moz-placeholder {
                  color: $corporate_3;
                  font-family: $subtitle_family;
                }
                &:-ms-input-placeholder {
                  color: $corporate_3;
                  font-family: $subtitle_family;
                }
                &:-moz-placeholder {
                  color: $corporate_3;
                  font-family: $subtitle_family;
                }
              }
            }
            .submit_button_guests {
              width: 70%;
              height: 50px;
              background: $corporate_1;
              font-family: $title_family;
              line-height: 35px;
              color: $black;
              letter-spacing: 3px;
              font-size: 29px;
              margin: auto;
              padding: 0;
              @include transition(all, .5s);
              &:hover {
                background: $corporate_3;
                color: white;
              }
            }
          }
          &.size_2 {
            .room1 {
              border-color: $corporate_3;
              .room_title,
              .remove_room,
              label,
              .room_selector .selectric .label,
              .room_selector .selectric .label:before,
              .room_selector .selectric .button:before {
                color: $corporate_3;
              }
              .children_selector {
                border-left-color: $corporate_3;
              }
            }
          }
          &.size_3 {
            .room1, .room2 {
              border-color: $corporate_3;
              .room_title,
              .remove_room,
              label,
              .room_selector .selectric .label,
              .room_selector .selectric .label:before,
              .room_selector .selectric .button:before {
                color: $corporate_3;
              }
              .children_selector {
                border-left-color: $corporate_3;
              }
            }
          }
          .room {
            position: relative;
            display: flex;
            align-items: center;
            vertical-align: middle;
            height: auto;
            font-family: $subtitle_family;
            width: 320px;
            padding: 0;
            text-align: center;
            border: 1px solid $corporate_3;
            margin: 30px 0 20px;

            &.room_with_babies {
              width: 500px;

              .children_selector, .adults_selector, .babies_selector {
                width: 33% !important;
              }
            }

            .room_title {
              position: absolute;
              top: -25px;
              margin: 0 15px 0 0;
              display: block;
              font-size: 14px;
              color: $corporate_3;
              font-weight: 500;
              text-align: left;
              text-transform: uppercase;
            }
            .remove_room {
              position: absolute;
              top: -30px;
              cursor: pointer;
              right: 15px;
            }
            label {
              display: inline-block;
              font-family: $subtitle_family;
              text-transform: capitalize;
              color: $corporate_3;
              letter-spacing: 1.2px;
              font-size: 14px;
            }
            .room_selector {
              display: inline-block;
              vertical-align: middle;
              .selectricItems {
                display: none !important;
              }
              .selectric {
                height: 30px;
                margin: 0 0 0 5px;
                background: transparent;
                .label {
                  text-align: center;
                  margin-left: -5px;
                  color: $black;
                  font-family: $title_family;
                  font-size: 33px;
                  line-height: 30px;
                  font-weight: bold;
                  &:before {
                    cursor: pointer;
                    content: '+';
                    position: absolute;
                    top: 55%;
                    left: 5px;
                    -webkit-transform: translateY(-50%);
                    -moz-transform: translateY(-50%);
                    -ms-transform: translateY(-50%);
                    -o-transform: translateY(-50%);
                    transform: translateY(-50%);
                    font-family: $title_family;
                    font-weight: bold;
                    font-size: 33px;
                    color: $black;
                  }
                }
                .button {
                  position: absolute;
                  top: 0;
                  text-indent: 0;
                  height: auto;
                  color: white;
                  margin: 0;
                  font-size: 0;
                  line-height: 24px;
                  right: -5px;
                  background: transparent !important;
                  text-shadow: 0 0 0 rgba(0,0,0,0) !important;
                  &:before {
                    content: '-';
                    cursor: pointer;
                    position: absolute;
                    top: 55%;
                    right: 15px;
                    -webkit-transform: translateY(-50%);
                    -moz-transform: translateY(-50%);
                    -ms-transform: translateY(-50%);
                    -o-transform: translateY(-50%);
                    transform: translateY(-50%);
                    display: inline-block;
                    vertical-align: middle;
                    font-family: $title_family;
                    font-weight: bold;
                    font-size: 33px;
                    color: $black;
                  }
                }
              }
            }
            .adults_selector, .children_selector, .babies_selector {
              height: auto;
              padding: 14px 0;
              border-left-width: 0;
            }

            .children_selector, .babies_selector {
              border-left: 1px solid $black;

              .range-age {
                display: block;
                font-size: 10px;
              }

              .selectric .label {
                margin-top: -17px;
              }
            }
          }
        }
      }
      .wrapper_booking_button {
        @include input_base_styles;
        width: $width_s;
        position: unset;

        .promocode_wrapper {
          display: inline-block;
          height: 100%;
          width: 100%;
          padding: 0;
          position: relative;
          border: none;

          .promocode_label {
            display: none;
          }

          .promocode_input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: 0;
            text-align: center;
            margin: 0;
            @include promocode_placeholder;

            &:focus {
              outline: 0;
            }

            &::-webkit-input-placeholder {
              @include promocode_placeholder;
            }

            &:-moz-placeholder {
              @include promocode_placeholder;
            }

            &::-moz-placeholder {
              @include promocode_placeholder;
            }

            &:-ms-input-placeholder {
              @include promocode_placeholder;
            }
          }
        }

        .submit_button {
          @include btn_styles;
          font-size: 29px;
          letter-spacing: 3.5px;
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 230px;
          border-radius: 2px;
          height: calc((#{$widget_height} * 2) + #{$input_margin});
          @include transition(background-color, .6s);
          &:lang(en),
          &:lang(de) {
            padding: 10px 25px;
          }

          &:lang(fr) {
            padding: 10px 15px;
          }
        }
      }
    }
  }
}


.selectricWrapper {
  .selectricHideSelect, .selectricInput {
    display: none;
  }

  &.selectricOpen {
    .selectricItems {
      display: block;
      border: 1px solid #9b9b9b;
    }
  }

  .selectricItems {
    position: absolute;
    top: calc(100% + 10px);
    background-color: white;
    display: none;
    z-index: 9999;

    ul {
      margin: 0;
      list-style: none;

      li {
        @include option_styles;
        margin: 0;
        display: block;
        background-color: white;
        padding: 10px 5px;
        background-image: none;
        @include transition(all, .4s);

        &:hover {
          background-color: rgba($corporate_1, .6);
          color: white;
        }

        &.selected {
          background-color: $corporate_1;
          color: white;
        }
      }
    }
  }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  box-shadow: 0 5px 10px rgba($corporate_2, .3);
  border-radius: 0;
  color: $color_text;

  .header_datepicker {
    background-color: $corporate_3 !important;
    margin: 0;

    .close_button_datepicker {
      top: 9px;
      background: transparent;
      color: $black;
    }

    .specific_date_selector {
      text-transform: uppercase;
      font-family: $title_family;

      &:before {
        display: none;
      }
    }
  }

  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    padding: 0;

    table.ui-datepicker-calendar {
      margin: 0 15px;
      width: calc(100% - 30px);

      th span {
        font-weight: bold;
        color: $black;
      }

      td {
        border-width: 0;
        background: transparent !important;
        height: 40px;
        padding: 5px 0;

        .ui-state-default {
          line-height: 30px;
          height: 30px;
          width: 30px;
          border-radius: 50%;
        }

        .ui-state-active {
          background: $corporate_1 !important;
        }

        .ui-state-hover {
          background: $corporate_2;
        }

        &.ui-datepicker-start_date, &.highlight, &.ui-datepicker-highlighted {
          .ui-state-default {
            background: $corporate_1 !important;
            color: white !important;
          }
        }

        &.ui-datepicker-start_date {
          .ui-state-default:before {
            display: none;
          }
        }
      }
    }
  }

  .ui-datepicker-header.ui-widget-header {
    border-bottom: none;
    font-family: $subtitle_family;
    font-weight: 300;

    .ui-datepicker-prev, .ui-datepicker-next {
      background: transparent;

      .ui-icon {
        background: transparent;
        color: white;
        position: relative;
        @extend .fa-arrow-down;

        &:before {
          @extend .fal;
          color: $color_text;
          font-size: 20px;
          @include center_xy;
          display: none;
        }
      }

      &:hover, .ui-state-hover {
        background: transparent !important;

        .ui-icon:before {
          color: $corporate_3;
        }
      }
    }
  }

  .months_selector_container {
    margin-top: 160px;
    .cheapest_month_selector {
      background: $corporate_1;
      border-radius: 0;
    }

    .months_selector_wrapper {
      border-color: white;

      .month_selector {
        border-color: white;
        background: #FAFAFA;

        &:nth-of-type(odd) {
          border-color: white;
        }
      }
    }
  }

  .specific_month_selector, .go_back_button {
    margin: 0;
    background: transparent;
    color: $color_text;
    font-weight: 300;

    strong {
      color: $corporate_3;
    }
  }
}

.fancybox-overlay {
  z-index: 5000;
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  width: 100vw;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  z-index: 8500 !important;
  max-width: 100%;
  min-width: 1140px;
  border-radius: 0;
  background: $corporate_2;

  &:before {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    content: '';
    display: none;
    background: url("/img/#{$base_web}/wave-patern-small.png");
    height: 120px;
  }
  .header_datepicker {
    background: transparent !important;
    &:before {
      content: '';
      display: block;
      height: 20px;
    }
    &:after {
      content: '';
      display: block;
      margin: 10px auto 20px;
      width: 150px;
      height: 10px;
      background: url("/img/#{$base_web}/wave-aqua-small.png");
    }
    .close_button_datepicker {
      @include icon-xcross;
      width: 40px;
      height: 40px;
      border-width: 0;
      border-radius: 0;
      left: 50px;
      top: 50px;
      &:before, &:after {
        background: $black;
      }
    }
    .specific_date_selector {
      display: none;
    }
  }
  &[datepicker=endDate] {
    .header_datepicker {
      .specific_date_selector {
        &:before {
          content: '<';
        }
      }
    }
  }

  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed{
    @include center_y;
    left: 0;
    right: 0;
    margin: auto;
    .ui-widget-content {
      margin: auto;
      width: 670px !important;
      .ui-datepicker-group {
        width: calc(50% - 10px);
      }
      .ui-datepicker-group-last {
        margin-left: 20px;
        .ui-widget-header {
          margin-left: 10px;
        }
      }
      .ui-widget-header {
        background: $corporate_3 !important;
        padding: 5px;
        margin-right: 10px;
        color: white;
        .ui-datepicker-prev, .ui-datepicker-next {
          background: transparent;
          color: white;
          -webkit-transform: rotate(0) !important;
          -moz-transform: rotate(0) !important;
          -ms-transform: rotate(0) !important;
          -o-transform: rotate(0) !important;
          transform: rotate(0) !important;
          font-family: "Font Awesome 5 Pro";
          font-weight: 300;
          top: 4px;
          &:hover, &.ui-state-hover {
            background: transparent !important;
            color: $corporate_1 !important;
          }
          .ui-icon {
            background: transparent !important;
          }
          &:before {
            @include center_xy;
            font-size: 24px;
          }
        }
        .ui-datepicker-prev {
            &:before {
              content: '\f104';
            }
        }
        .ui-datepicker-next {
            &:before {
              content: '\f105';
            }
        }
        .ui-datepicker-title {
          font-family: $subtitle_family;
          font-weight: 300;
          color: white !important;
        }
      }
      .ui-datepicker-calendar {
        th {
          font-size: 18px;
          font-weight: bold;
          color: $corporate_3;
        }
        td {
          border-width: 0;
          height: 35px;
          span, a {
            font-size: 18px;
            line-height: 35px;
            font-weight: 300;
            font-family: $subtitle_family;
            color: $black !important;
            &.ui-state-active {
              background: transparent !important;
            }
            &.ui-state-hover {
              background: white !important;
              border-radius: 30px;
            }
          }
          &.ui-state-disabled {
            background: white !important;
          }
          &.ui-datepicker-start_date {
            border-radius: 30px 0 0 30px;
            span, a {
              background: white !important;
              color: black !important;
              border-radius: 30px 0 0 30px;
              &:before {
                color: black !important;
                background: white !important
              }
            }
          }

          &.highlight {
            background: white !important;
            span, a {
              color: black !important;
              background: white !important;
            }
          }
          &.last-highlight-selection {
            background: white !important;
            color: black !important;
            border-radius: 0 30px 30px 0;
            span, a {
              background: white !important;
            }
          }
        }
      }
    }
  }
  .current_nights_selector {
    display: none;
  }
  .months_selector_container {
    .months_selector_title {
      text-transform: uppercase;
      color: $black;
    }
    .months_selector_wrapper {
      margin: auto;
      width: 300px;
    }
  }
  .go_back_button, .cheapest_month_selector {
    display: block;
    width: 300px;
    margin: 0 auto;
    padding: 5px 10px;
    text-align: center;
    background: $corporate_1;
    margin-top: 15px;
    color: $black;
    text-decoration: none;
    strong {
      font-weight: bold;
      color: $black;
    }
  }
  .specific_month_selector {
    border-radius: 10px !important;
    display: block;
    padding: 10px 50px;
    text-align: center;
    background: white;
    font-size: 16px;
    color: $black;
    text-decoration: none;
    @include center_x;
    top: 20%;
    font-family: $subtitle_family;
    strong {
      color: $black;
      font-weight: 300 !important;
    }
  }
  .go_back_button {
    background: $black;
    color: white;
    strong {
      color: white;
    }
  }
}

.booking_steps{
  position: fixed;
  left: 0;
  right: 0;
  width: 710px;
  z-index: 8501;
  margin: auto;
  top: 0;
  border-top: 60px solid transparent;
  text-align: center;
  font-family: $title_family;
  &.hidden {
    display: none !important;
  }
  &.fancybox-margin {
    margin-right: auto;
  }
  .step {
    height: 40px;
    display: inline-block;
    padding: 10px 20px;
    width: 50%;
    text-transform: uppercase;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0.4px;
    border: 1px solid $corporate_3;
    background: transparent;
    color: $corporate_3;
    cursor: pointer;
    &.current_step {
      background: $corporate_3;
      color: white;
      &.step_1, &.step_2 {
        &:after {
          border-color: transparent transparent transparent $corporate_3;
        }
      }
    }
    &.step_1, &.step_2 {
      padding-right: 0;
      &:before, &:after {
        @include center_y;
        right: -41px;
        content: '';
        border-width: 20px;
        border-style: solid;
        border-color: transparent transparent transparent $corporate_3;
      }
    }
    &.step_1 {
      position: relative;
      z-index: 2;
    }
    &.step_2 {
      position: relative;
      z-index: 1;
      &:before, &:after {
        @include center_y;
        right: -37px;
        content: '';
        border-width: 19px;
        border-style: solid;
        border-color: transparent transparent transparent $corporate_2;
      }
    }
    &.step_2, &.step_3 {
      padding-left: 30px;
    }
  }
}