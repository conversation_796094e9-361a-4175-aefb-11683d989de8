.banner_icons_full_wrapper {
  @include base_banner_styles;

  .banner_icons_content {
    .content_title {
      @include title_styles;
      text-align: left;
    }

    .content_text {
      @include text_styles;
      padding-top: 15px;
    }
  }


  .banner_icons_wrapper {
      &:not(.owl-carousel){
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 50px;
      }
         width: 100%;


    .banner {
      display: inline-block;
      width: 100%;
      padding: 10px 30px 10px 20px;
      text-align: left;
      @include transition(all, .6s);

      .icon {
        @include icon_styles;
      }

      .text {
        @include text_styles;
        display: block;
        padding-top: 15px;
        text-align: left;
      }

      &:hover {
       transform: translateY(-20px);
      }
    }
    .banner_icons_link {
      display: block;
      width: 100%;
      padding-top: 30px;

    }
    &.owl-carousel{
       &.owl-loaded{
        display: block;
       }

      .owl-item .banner{
        width: 100%;
        padding-top: 20px;
      }
      .owl-dots:not(.disabled){
        width: 100%;
        display: flex;
        justify-content: end;
        margin: 10px 0;
        .owl-dot{
          display: inline-block;
          margin-right: 10px;
          width: 12px;
          height: 12px;
          border: 1px solid $black;
          transition: all .4s;
          &:hover, &.active{
            background: $black;
          }
        }
      }
    }
  }

  &.meetings {
    background: $corporate_2;

    .banner_icons_content .content_title {
      letter-spacing: 2.8px
    }
  }
}