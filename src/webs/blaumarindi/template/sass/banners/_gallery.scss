.gallery_filter_wrapper {
  @include base_banner_styles;
  @include display_flex;
  width: 100%;

  .gallery_group_wrapper {
    display: inline-block;
    width: calc((100% / 3) - 20px);
    margin-bottom: 30px;

    &:not(:nth-of-type(3n)) {
      margin-right: 30px;
    }

    .image_wrapper {
      position: relative;
      display: inline-block;
      width: 100%;
      height: 260px;
      overflow: hidden;

      &:not(:last-of-type) {
        margin-bottom: 30px;
      }

      img {
        @include cover_image;
      }
    }

    &:nth-of-type(1), &:nth-of-type(3n + 1) {
      .image_wrapper:nth-of-type(2) {
        height: 550px;
      }
    }

    &:nth-of-type(2), &:nth-of-type(3n + 2) {
      .image_wrapper:nth-of-type(1) {
        height: 550px;
      }
    }

    &:nth-of-type(3n) {
      .image_wrapper:nth-of-type(3) {
        height: 550px;
      }
    }
  }
}