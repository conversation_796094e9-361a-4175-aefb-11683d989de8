.offers_wrapper {
  .offers {
    .banner {
      @include base_banner_styles;
      @include display_flex;
      width: 100%;

      &.actives {
        flex-direction: row-reverse;
        background-color: $corporate_2;

        .content {
          padding: 0 50px 0 0;
        }
      }

      .pictures {
        position: relative;
        display: inline-block;
        width: 50%;

        .pic {
          position: relative;
          display: inline-block;
          width: 100%;
          height: 335px;

          img {
            @include cover_image;
          }
        }
      }

      .content {
        position: relative;
        display: inline-block;
        z-index: 5;
        width: 50%;
        padding-left: 50px;
        text-align: left;

        .title {
          @include banner_title_styles_2($color_text);
          font-size: 22px;
          line-height: 28px;
          padding-bottom: 40px;
        }

        .desc {
          @include text_styles;
          text-align: left;

          modal {
            display: none;
          }

          .open_modal {
            display: inline;
            text-decoration: underline;
            color: $color_text;
          }
        }

        .extra_info {
          .icons {
            padding: 25px 0;

            label, .icon {
              display: inline-block;
              vertical-align: middle;
              padding: 0 10px 10px 0;
            }

            label {
              font-family: $title_family;
              font-weight: 700;
              color: $color_text;
              margin-right: 10px;
              text-transform: uppercase;
              font-size: 16px;
              letter-spacing: 1.9px;
            }

            .icon {
              position: relative;

              &:hover {
                span {
                  margin-bottom: 10px;
                  opacity: 1;
                }
              }

              i {
                display: inline-block;
                vertical-align: middle;
                padding: 0 6px;
                font-size: 24px;
                color: $corporate_3;
              }

              span {
                @include center_x;
                top: 100%;
                text-align: center;
                white-space: nowrap;
                background: $corporate_3;
                color: white;
                padding: 10px;
                border-radius: 3px;
                display: inline-block;
                opacity: 0;
                font-family: $title_family;
                @include transition(all, .6s);

                &:after {
                  content: '';
                  @include center_x;
                  bottom: 100%;
                  border-width: 7px;
                  border-style: solid;
                  border-color: transparent transparent $corporate_3 transparent;
                }
              }
            }
          }

          .info {
            font-size: 12px;
            font-weight: normal;

            i, span {
              display: inline-block;
              vertical-align: middle;
            }

            i {
              width: 30px;
              font-size: 20px;
              color: $corporate_3;
            }

            span {
              font-size: 15px;
              letter-spacing: 1px;
            }
          }

          .button_promotion {
            margin-top: 30px;
          }
        }
      }
    }

    .owl-dots {
      display: block;
      padding-top: 30px;
      text-align: left;
      position: absolute;
      @include center_x;
      top: 86%;

      .owl-dot {
        @include owl_dots_styles;
      }
    }

    .owl-nav {
      position: absolute;
      right: 20px;
      top: 260px;
      display: block;
      padding-top: 30px;
      text-align: left;

      .owl-prev, .owl-next {
        @include owl_nav_styles;
        max-width: 20px;
      }

      .owl-next {
        transform: rotate(180deg);
      }
    }
  }
}
