.banner_gallery_content {
  .content_title {
    @include title_styles;
    margin: 40px 0 0;
  }

  .content_text {
    @include text_styles;
    padding-top: 15px;
  }
}

.gallery_filter_wrapper {
  @include base_banner_styles;
  @include display_flex;
  width: 100%;

  .gallery_group_wrapper {
    display: inline-block;
    width: calc((100% / 3) - 20px);
    margin-bottom: 30px;

    &:not(:nth-of-type(3n)) {
      margin-right: 30px;
    }

    .image_wrapper {
      position: relative;
      display: inline-block;
      width: 100%;
      height: 260px;
      overflow: hidden;

      &:not(:last-of-type) {
        margin-bottom: 30px;
      }

      img {
        @include cover_image;
      }
    }

    &:nth-of-type(1), &:nth-of-type(3n + 1) {
      .image_wrapper:nth-of-type(2) {
        height: 550px;
      }
    }

    &:nth-of-type(2), &:nth-of-type(3n + 2) {
      .image_wrapper:nth-of-type(1) {
        height: 550px;
      }
    }

    &:nth-of-type(3n) {
      .image_wrapper:nth-of-type(3) {
        height: 550px;
      }
    }
  }
}

.gallery_filter_wrapper.grid {
      display: grid;
      .gallery_group_wrapper {
        width: 100%;
      }
      .image_wrapper {
        width: 30%;
        height: auto !important;
      }
}

.btn_link_gallery {
  padding: 10px 35px 5px;
  border-radius: 2px;
  @include btn_styles(true);
  margin: -40px 0 50px !important;
  left: 50%;
  transform: translateX(-50%);
}