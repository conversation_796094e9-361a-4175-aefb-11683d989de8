$widget_height: 49px;
$width_s: 170px;
$width_m: 210px;
$width_l: 290px;
$input_margin: 10px;
$horizontal_padding: 15px;
$vertical_padding: 10px;
$widget_bg: white;
$label_color: $black;
$option_color: $black;

@mixin input_base_styles() {
  position: relative;
  display: inline-flex;
  justify-content: space-around;
  align-items: center;
  float: none;
  background-color: $widget_bg !important;
  height: $widget_height;
  width: $width_l;
  padding: $vertical_padding $horizontal_padding;
  margin: $input_margin $input_margin 0 0;
  border-radius: 2px;
}

@mixin label_styles() {
  text-align: center;
  color: $label_color;
  text-transform: uppercase;
  display: inline-block;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 1.9px;
  font-family: $subtitle_family;
  font-weight: 300;
  cursor: pointer;
}

@mixin option_styles() {
  display: inline-block;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 1.9px;
  color: $option_color;
  text-align: center;
  text-transform: uppercase;
  font-family: $subtitle_family;
  font-weight: 300;
  cursor: pointer;
}

@mixin before_icon($icon_code: '\f133') {
  &:before {
    content: $icon_code;
    font-family: "Font Awesome 5 Pro";
    display: inline-block;
    margin-right: 10px;
    font-size: 18px;
    font-weight: 300;
    color: $option_color;
  }
}

@mixin promocode_placeholder() {
  font-size: 13px;
  letter-spacing: 1.5px;
  font-weight: 300;
  font-family: $subtitle_family;
  text-align: center;
  color: $label_color;
}