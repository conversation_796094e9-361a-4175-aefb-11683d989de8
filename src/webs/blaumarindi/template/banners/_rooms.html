<div class="rooms_wrapper">
    {% if rooms_filter.items()|length > 1 %}
        <div class="rooms_filter">
            {#  <a href="#all" class="active">
                {{ T_ver_todos }}
            </a>
            #}
            {% for class_fitler, filter in rooms_filter.items() %}<a href="#{{ class_fitler }}" {% if loop.first %}class="active"{% endif %}>
            {{ filter|safe }}
            {% if filter and "tooltip" in filter %}<i class="fal fa-info-circle"></i>{% endif %}
        </a>{% endfor %}
        </div>
    {% endif %}
    <div class="rooms">
        {% for room in rooms %}
        <div class="room animate_reveal_on_scroll {{ room.title|safe }} {{ room.group|safe }} {% if not loop.index % 2 == 0 %}actives{% endif %}">
            {% if room.tour_virtual_link %}
            <a data-fancybox data-type="iframe" data-src="{{ room.tour_virtual_link }}"
               class="myFancyPopup fancybox.iframe link_tour_virtual"
               href="{{ room.tour_virtual_link }}">
                <div class="logo_360">
                    <img loading="lazy"
                         src="https://storage.googleapis.com/cdn.paraty.es/blaumar-blaumar/files/logo_360.svg"
                         alt="{{ hotel_name }}">
                </div>
            </a>
            {% endif %}
                <div class="pictures owl-carousel">
                    {% for pic in room.pictures %}
                        <a href="{{ pic }}=s1900" class="pic" rel="lightbox[{{ room.key|safe }}]"><img src="{{ pic }}=s700" alt="{{ room.subtitle|safe }}"></a>
                    {% endfor %}
                </div><div class="content">
                    <div class="title">{{ room.subtitle|safe }} {% if room.extra_info %}<span class="label_extra_info">{{ room.extra_info|safe }}</span>{% endif %}</div>
                    <div class="desc">
                        {{ room.content|safe }}
                        {% if "<modal" in room.content %}
                            <a href="#" class="open_modal">{{ T_ver_detalles }}</a>
                        {% endif %}
                    </div>

                    <div class="extra_info">
                        <div class="icons">
                            <label>{{ T_equipamiento }}:</label>
                            {% for icon in room.equipment %}
                                <div class="icon">
                                    <i class="{{ icon.ico|safe }}"></i>
                                    <span>{{ icon.label|safe }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        {% if room.measurements %}<div class="info"><i class="fal fa-ruler"></i> <span>{{ room.measurements|safe }}</span></div>{% endif %}
                        {% if room.ocuppancy %}<div class="info"><i class="fal fa-user"></i> <span>{{ room.ocuppancy|safe }}</span></div>{% endif %}
                        <a href="#data" class="btn_personalized_1 button_promotion">{{ T_reservar if not language in "fr" else T_reservas }}</a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
<script>
$(window).load(function () {
    $(".rooms_filter a").click(function (e) {
        e.preventDefault();
        $(this).addClass("active").siblings().removeClass("active");
        let filter_class = $(this).attr("href").replace("#","");
        if(filter_class == "all") {
            $(".room").slideDown(600);
            $(".room").removeClass('actives');
            $(".room:odd").addClass('actives');
        } else {
            $(".room").slideUp(600);
            $(".room").removeClass('actives');
            $(".room."+filter_class+':even').addClass('actives');
            $(".room."+filter_class).slideDown(600);
        }
    });
    $(".rooms .room .pictures").owlCarousel({
        loop: true,
        nav: true,
        dots: true,
        items: 1,
        navText: ['<img src="/img/{{ base_web }}/arrow.svg"/>', '<img src="/img/{{ base_web }}/arrow.svg"/>'],
        animateIn: "fadeIn",
        animateOut: "fadeOut",
        autoplayTimeout: 10000,
        autoplaySpeed: 1000,
        dotsSpeed: 1000,
        autoplay: false
    });
});
</script>