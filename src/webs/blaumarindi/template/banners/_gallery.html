{% if gallery_pics %}
    {% if gallery_pics|length >= 9 %}
        {% set temp_pic_list = gallery_pics %}
        {# Splitting all the elements in the lists of 9 #}
        {% set splitted_list = temp_pic_list|batch(9)|list %}

    <div class="gallery_filter_wrapper">
        {% for sublist in splitted_list %}
            {% if sublist|length == 9 %}
                <div class="gallery_group_wrapper">
                    {% for pic in sublist %}
                        <a {% if pic.video %}data-src="{{ pic.video|safe }}" data-fancybox data-type="iframe" class="myFancyPopup fancybox.iframe image_wrapper animate_reveal_on_scroll"{% else %}
                            href="{{ pic.servingUrl }}=s1900" rel="lightbox[gallery_section_pics]" class="image_wrapper animate_reveal_on_scroll" {% endif %}>
                            <img src="{{ pic.servingUrl }}=s700" alt="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}" title="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}">
                            {% if pic.description %}<span class="img_info">{{ pic.description|safe }}</span>{% endif %}
                        </a>
                    {% endfor %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
    {% else %}
        <div class="gallery_filter_wrapper grid">
            <div class="gallery_group_wrapper">
                {% for pic in gallery_pics %}
                    <a {% if pic.video %}data-src="{{ pic.video|safe }}" data-fancybox data-type="iframe" class="myFancyPopup fancybox.iframe image_wrapper animate_reveal_on_scroll"{% else %}
                        href="{{ pic.servingUrl }}=s1900" rel="lightbox[gallery_section_pics]" class="image_wrapper animate_reveal_on_scroll" {% endif %}>
                        <img src="{{ pic.servingUrl }}=s700" alt="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}" title="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}">
                        {% if pic.description %}<span class="img_info">{{ pic.description|safe }}</span>{% endif %}
                    </a>
                {% endfor %}
            </div>
        </div>
    {% endif %}
{% endif %}
