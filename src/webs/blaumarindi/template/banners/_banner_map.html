<div class="banner_map_wrapper animate_reveal_on_scroll" {% if banner_map_pics and banner_map_pics.0.servingUrl %}style="background-image: url('{{ banner_map_pics.0.servingUrl }}')" {% endif %}>
    {% if iframe_map_location %}
        <div class="banner_map_iframe">
            {{ iframe_map_location|safe }}
        </div>
    {% endif %}
    <div class="banner_map">
        {% if banner_map_content.subtitle %}<h2>{{banner_map_content.subtitle|safe}}</h2>{% endif %}
        {% if banner_map_content.content %}<div class="desc">{{banner_map_content.content|safe}}</div>{% endif %}


        {% if banner_map_pics and banner_map_pics.0.linkUrl %}
            <a class="map_info" href="{{ banner_map_pics.0.linkUrl }}" target="_blank">{{ T_como_llegar }}</a>
        {% endif %}
{#        <div class="map_widget" style="display: none">#}
{#            <!-- --- --- --- -- Widget como llegar -- --- --- --- -->#}
{#            <div class="iframe-google-maps-wrapper">#}
{#                <div class="go_map"><i class="fa fa-times"></i></div><input type="text" class="place" placeholder="{{ T_desde }}..."><button class="go">{{ T_como_llegar }}</button>#}
{##}
{#                <script language="JavaScript">#}
{#                    $(".go").click(function () {#}
{#                        mapaUrl = "https://www.google.com/maps?saddr=@place@&daddr={% if banner_map_location %}{{ banner_map_location|safe }}{% else %}Hotel+Blaumar+Salou{% endif %}&output=embed";#}
{#                        newurl = mapaUrl.replace("@place@", $(".place").val());#}
{##}
{#                        $(".banner_map_iframe iframe").attr("src",newurl);#}
{#                    });#}
{#                </script>#}
{#            </div>#}
{#        </div>#}
    </div>
</div>
{#<script>#}
{#    $(window).load(function () {#}
{#        $(".map_info").click(function () {#}
{#            $(this).slideUp();#}
{#            $(".map_widget").slideDown();#}
{#            $(".banner_map_wrapper .banner_map_iframe").addClass("active");#}
{#        });#}
{#        $(".go_map").click(function () {#}
{#            $(".map_widget").slideUp();#}
{#            $(".map_info").slideDown();#}
{#            $(".banner_map_wrapper .banner_map_iframe").removeClass("active");#}
{#        });#}
{#    });#}
{#</script>#}