<div class="banner_newsletter_wrapper" {% if banner_newsletter_pics.0.servingUrl %}style="background-image: url('{{ banner_newsletter_pics.0.servingUrl }}')"{% endif %}>
    <div class="container12">
        {% if banner_newsletter_sec.subtitle %}
        <div class="content_title">
            <h3 class="title">
                {{ banner_newsletter_sec.subtitle|safe }}
            </h3>
        </div>
        {% endif %}

        <div class="form_wrapper">
            <form action="" class="newsletter_form">
                <div class="top_form">
                    <div class="inputs_wrapper {{ language_code }}">
                        <input type="text" name="suscEmail" id="suscEmail" placeholder="{{ T_introduce_email_placeholder }}">
                        {% if language_code == 'es' or language_code == 'ca' %}
                        <select required name="provincia">
                            <option value="">{{ T_provincia }}</option>
                            <option value="Álava">Álava</option>
                            <option value="Albacete">Albacete</option>
                            <option value="Alicante">Alicante</option>
                            <option value="Almería">Almería</option>
                            <option value="Asturias">Asturias</option>
                            <option value="Ávila">Ávila</option>
                            <option value="Badajoz">Badajoz</option>
                            <option value="Baleares">Baleares</option>
                            <option value="Barcelona">Barcelona</option>
                            <option value="Burgos">Burgos</option>
                            <option value="Cáceres">Cáceres</option>
                            <option value="Cádiz">Cádiz</option>
                            <option value="Cantabria">Cantabria</option>
                            <option value="Castellón">Castellón</option>
                            <option value="Ceuta">Ceuta</option>
                            <option value="Ciudad Real">Ciudad Real</option>
                            <option value="Córdoba">Córdoba</option>
                            <option value="Cuenca">Cuenca</option>
                            <option value="Gerona">Gerona</option>
                            <option value="Granada">Granada</option>
                            <option value="Guadalajara">Guadalajara</option>
                            <option value="Guipúzcoa">Guipúzcoa</option>
                            <option value="Huelva">Huelva</option>
                            <option value="Huesca">Huesca</option>
                            <option value="Jaén">Jaén</option>
                            <option value="La Coruña">La Coruña</option>
                            <option value="La Rioja">La Rioja</option>
                            <option value="Las Palmas">Las Palmas</option>
                            <option value="León">León</option>
                            <option value="Lérida">Lérida</option>
                            <option value="Lugo">Lugo</option>
                            <option value="Madrid">Madrid</option>
                            <option value="Málaga">Málaga</option>
                            <option value="Melilla">Melilla</option>
                            <option value="Murcia">Murcia</option>
                            <option value="Navarra">Navarra</option>
                            <option value="Orense">Orense</option>
                            <option value="Palencia">Palencia</option>
                            <option value="Pontevedra">Pontevedra</option>
                            <option value="Salamanca">Salamanca</option>
                            <option value="Segovia">Segovia</option>
                            <option value="Sevilla">Sevilla</option>
                            <option value="Soria">Soria</option>
                            <option value="Tarragona">Tarragona</option>
                            <option value="Tenerife">Tenerife</option>
                            <option value="Teruel">Teruel</option>
                            <option value="Toledo">Toledo</option>
                            <option value="Valencia">Valencia</option>
                            <option value="Valladolid">Valladolid</option>
                            <option value="Vizcaya">Vizcaya</option>
                            <option value="Zamora">Zamora</option>
                            <option value="Zaragoza">Zaragoza</option>
                        </select>
                        {% endif %}
                    </div>
                    <button class="btn btn_dark button_newsletter">Confirmar suscripción</button>
                </div>
                <div class="bottom_form">
                    <div class="legal_checkbox_wrapper">
                        <input type="checkbox" name="privacy">
                        <label for="">
                            <a data-fancybox
                               data-options='{"caption" : "{{ T_lopd }}", "src" : "/{{ language_code }}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                               data-width="1200" class="myFancyPopup fancybox.iframe newsletter_popup"
                               href="{{ language_code }}/?sectionContent=politica-de-privacidad.html"
                               rel="nofollow">{{ T_lopd }}</a>
                        </label>
                    </div>
                    <div class="legal_checkbox_wrapper">
                        <input type="checkbox" name="promotions">
                        <label for="">{{ T_acepto_promociones }}</label>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

    $(window).load(function () {
        $(".newsletter_form").validate({
            rules: {
                privacy: "required",
                promotions: "required",
                suscEmail: {
                    required: true,
                    email: true
                },
                provincia: {
                    required: true
                },
            },
            messages: {
                suscEmail: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                provincia: {
                    required: "{{ T_campo_obligatorio|safe }}"
                },
                privacy: "{{ T_campo_obligatorio|safe }}",
                promotions: "{{ T_campo_obligatorio|safe }}",
            },
            highlight: function (input) {
                $(input).parent().find("a").addClass('error_class');
                $(input).parent().find("label").addClass('error_class');
                $(input).parent().find("#suscEmail").addClass('error_class');
            },
            errorPlacement: function (error, element) {
                //this keeps enable the validation but hides the error message
            }
        });

        $(".button_newsletter").click(function () {
            if ($(".newsletter_form").valid()) {

                $.post("/utils?action=newsletter&language={{ language_code }}",
                    {
                        'email': $(".newsletter_form #suscEmail").val()
                    },

                    function (data) {
                        {% if newsletter_thanks %}
                            $.fancybox($(".newsletter_thanks_popup"), {
                                padding: 0,
                                wrapCSS: "newsletter_thanks_popup_wrapper",
                                width: 640,
                                height: 'auto',
                                fitToView: false,
                                autoSize: false,
                                modal: true
                            });
                            setTimeout(function () {
                                window.location.href = window.location.href;
                            }, 5000);
                        {% else %}
                            alert("{{ T_gracias_newsletter }}");
                        {% endif %}
                        $("#suscEmail").val("");
                    }
                );

            } else {
                alert("{{ T_campos_obligatorios|safe }} ");
                console.log("invalid");
            }

        });

    });
</script>