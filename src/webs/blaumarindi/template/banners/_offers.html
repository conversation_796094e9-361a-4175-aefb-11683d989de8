<div class="offers_wrapper">
    <div class="offers">
        {% for banner in offers %}
            <div class="banner {% if not loop.index % 2 == 0 %}actives{% endif %}">
                <div class="pictures">
                    {% if banner.picture %}
                        <a href="{{ banner.picture|safe }}=s1900" class="pic" rel="lightbox"><img src="{{ banner.picture|safe }}=s700" alt="{{ banner.name|safe }}"></a>
                    {% endif %}
                </div>
                <div class="content">
                    <div class="title">{{ banner.name|safe }}</div>
                    <div class="desc">
                        {{ banner.description|safe }}
                        {% if "<modal" in banner.description %}
                            <a href="#" class="open_modal">{{ T_ver_detalles }}</a>
                        {% endif %}
                    </div>

                    <div class="extra_info">
                        <a href="#data" class="btn_personalized_1 button_promotion" {% if banner.smartDatasAttributes %}{{ banner.smartDatasAttributes|safe }}{% endif %}>{{ T_reservar }}</a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{#<script>#}
{#$(window).load(function () {#}
{#    $(".offers_wrapper .offers.owl-carousel").owlCarousel({#}
{#        loop: true,#}
{#        nav: true,#}
{#        dots: true,#}
{#        items: 1,#}
{#        navText: ['<img src="/img/{{ base_web }}/arrow.svg"/>', '<img src="/img/{{ base_web }}/arrow.svg"/>'],#}
{#        animateIn: "fadeIn",#}
{#        animateOut: "fadeOut",#}
{#        autoplayTimeout: 10000,#}
{#        autoplaySpeed: 1000,#}
{#        dotsSpeed: 1000,#}
{#        autoplay: false#}
{#    });#}
{#});#}
{#</script>#}