<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">
<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }} - {{ hotel_name|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    {% if favicon %}
        <link rel="icon" href="{{ favicon }}" type="image/x-icon">
    {% endif %}
    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    {% if share_picture %}
        <meta property="og:image" content="{{ share_picture|safe }}" />
    {% endif %}
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>
    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="format-detection" content="telephone=no">
    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>
    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.9.0/css/v4-shims.css">
    {% if namespace %}
        <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles_{{ namespace }}.css?v=@@automatic_version@@"/>
    {% else %}
        <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=@@automatic_version@@"/>
    {% endif %}
    <!--[if IE 8]><link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" /><![endif]-->
    <!--[if lte IE 7]><script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script><![endif]-->
    <!--[if lte IE 8]><script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->

    {{ jquery|safe }}
    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>
<body class="{% if home %}home_section{% else %}inner_section{% endif %} {% if landing_section %}landing_section{% endif %} {% if extra_class %}{{ extra_class }}{% endif %}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">
{% if lang_management %}<input type="hidden" id="lang_management" value="{{ lang_management }}">{% endif %}
{% if lang_default %}<input type="hidden" id="lang_default" value="{{ lang_default }}">{% endif %}
{% if home and preloading_logo %}
    <div class="preloading">
        {% if preloading_background %}
            <img src="{{ preloading_background.0.servingUrl }}=s1900" class="preloading_background" alt="{{ preloading_background.0.altText|safe }}=s1900">
        {% endif %}
        <div class="center_xy">
            <img src="{{ preloading_logo.0.servingUrl }}" class="preloading_logo" alt="{{ preloading_logo.0.altText }}">
            {% if preloading_logo.0.description %}
                <div class="desc">{{ preloading_logo.0.description|safe }}</div>
            {% endif %}
        </div>
    </div>
{% endif %}
{% block content %}<!--EDIT HERE YOUR PAGE-->{% endblock %}
{% block additional_js %}
    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1.1"></script>
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js"></script>


    <script type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/datepicker_custom.js?v=1.13"></script>
    <script>
        $(function () {
        adult_tag = "{{ T_adulto }}";
        adults_tag = "{{ T_adultos }}";
        DP_extend_info.config.booking_version = '7';
        DP_extend_info.config.months_show = 2;
        DP_extend_info.config.months_show_highlight = true;
        DP_extend_info.config.force_hightlight = true;
        DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            var html_date = "<span class='label entry'>{{ T_entrada }} |</span><span class='label departure'>{{ T_salida }} |</span><span class='day'>%d</span> <span class='month'>%m</span> <span class='year'>%y</span>",
                motnh_short_names = $.datepicker._defaults.monthNamesShort,
                month_name = motnh_short_names[(parseInt(dateComponents[1]) - 1)];

            synchronizeDatepicker();
            return html_date.replace("%d", dateComponents[0]).replace("%m", month_name).replace("%y", dateComponents[2]);
        };

        $(window).on('load',function () {
            var target_min_date;
            {% if period_closed_hotel %}
                console.log("PERIOD CLOSED WORKING")
                var period_closed = JSON.parse('{{ period_closed_hotel|safe }}');
                DP_extend_info.config.period_closed = period_closed;

                for (var n=0;n < period_closed.length;n++){
                    var closed_hotel = period_closed[n].close,
                        open_hotel = period_closed[n].open;
                    if (closed_hotel && new Date(closed_hotel)<= new Date()){
                        if (open_hotel && new Date(open_hotel)>= new Date())
                        target_min_date = new Date(open_hotel);
                    }
                }
                if (!target_min_date) {target_min_date = new Date()}
            {% endif %}

            DP_extend_info.init();

            if (target_min_date) {
                DP_extend_info.config.datepicker_sd_wrapper.datepicker('option', 'minDate', target_min_date);
                DP_extend_info.config.datepicker_ed_wrapper.datepicker('option', 'minDate', target_min_date);
                DP_extend_info.format_dates($.datepicker.formatDate("dd/mm/yy", target_min_date));
                DP_extend_info.set_datepicker_start_date($.datepicker.formatDate("dd/mm/yy", target_min_date));
                DP_extend_info.config.datepicker_sd_wrapper.datepicker( "option", "beforeShowDay", sd_beforeShowDay );
                DP_extend_info.config.datepicker_sd_wrapper.datepicker( "option", "onChangeMonthYear", datepicker_changeMonth );
                DP_extend_info.config.datepicker_ed_wrapper.datepicker( "option", "beforeShowDay", ed_beforeShowDay );
                DP_extend_info.config.datepicker_ed_wrapper.datepicker( "option", "onChangeMonthYear", datepicker_changeMonth );
            }

            setTimeout(function(){
                $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room1 .room_title").html("{{ T_alojamiento }} 1");
                $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room2 .room_title").html("{{ T_alojamiento }} 2");
                $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room3 .room_title").html("{{ T_alojamiento }} 3");
            }, 3000);
            $(".guest_selector label").html("{{ T_personas }}");
            prepare_countdowns("{{ T_dias }}", "{{ T_horas }}", "{{ T_minutos }}", "{{ T_segundos }}");
            });
            $("label.dates_selector_label").html("<span>{{ T_entrada }}</span><span>{{ T_salida }}</span>");
            $(".promocode_input").attr("placeholder","{{ T_promocode }}");




    });


        transfer_url_params_to_booking(['psh_ctid', 'psh_cpid']);
    </script>
    <input type="hidden" id="widget_room_tag" value="{{ T_habitacion }}">
    <input type="hidden" id="widget_rooms_tag" value="{{ T_habitaciones }}">
    <script type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_7.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js?v=1.23"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1.50"></script>
    <script async type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js"></script>
{% endblock %}
{{ extra_content_website|safe }}

{{ all_tracking_codes_footer|safe }}
{% if extra_bottom_script %}
    {{ extra_bottom_script|safe }}
{% endif %}
</body>
</html>