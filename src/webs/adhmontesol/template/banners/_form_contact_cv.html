<div class="contact_form_wrapper_cv">
    <form name="contact" id="contact" method="post" action="/utils/?action=work_with_us">
        <input type="hidden" name="action" id="action" value="contact"/>
        <div class="info">
            <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>
            <div class="contInput">
                <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
            </div>
            <div class="contInput">
                <input type="text" id="surname" name="surname" class="bordeInput" value=""
                       placeholder="{{ T_apellidos }}"/>
            </div>
            <div class="contInput">
                <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                       placeholder="{{ T_telefono }}"/>
            </div>
            <div class="contInput">
                <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
            </div>
            <div class="contInput cv_field">
                <input type="file" id="file_cv" name="file" value=""/>
                <input id="uploadFile" placeholder="{{T_adjunta_cv}} (Max. 3Mb)" disabled="disabled" />
                <input type="hidden" id="url_4_upload" name="url_4_upload" value=""/>
            </div>
            <div class="contInput area">
                <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                          value=""
                          placeholder="{{ T_comentarios }}"></textarea>
            </div>
            {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src='https://www.google.com/recaptcha/api.js'></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}
            <div class="contInput policy-terms">
                <div>{{ T_campos_obligatorios }}</div>
                <input type="checkbox" id="accept-term" name="accept_term"/>
                {% if is_mobile %}
                    <a class="myFancyPopup fancybox.iframe" data-fancybox data-options='{"caption" : "{{ T_politica_de_privacidad }}", "src" : "/{{ language }}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" href="/{{ language }}/?sectionContent=politica-de-privacidad.html">{{ T_lopd }}</a>
                {% else %}
                    <a class="myFancyPopup fancybox.iframe" href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                {% endif %}
            </div>
            {% if is_mobile %}
                <div class="contact_button_wrapper">
                    <a data-role="button">
                        <div id="contact-button">
                            Enviar
                        </div>
                    </a>
                </div>
            {% else %}
                <button id="contact-button" onclick="return false;">
                    <span class="icon"><i class="fas fa-angle-right"></i></span>
                    {{ T_enviar }}
                </button>
            {% endif %}

        </div>
    </form>
</div>

{% if is_mobile %}

    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>
{% endif %}
    <script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">
    $(window).load(function () {
        {% if is_mobile %}
            $.i18n.load(messages);
        {% endif %}
        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
           rules: {
               name: "required",
               surname: "required",
               email: {
                   required: true,
                   email: true
               },
               telephone: {
                   required: true,
                   phone: true
               },
               comments: "required",
               accept_term: "required"
           },
           messages: {
               name: "{{ T_campo_obligatorio}}",
               surname: "{{ T_campo_obligatorio}}",
               email: {
                   required: "{{ T_campo_obligatorio|safe }}",
                   email: "{{ T_campo_valor_invalido|safe }}"
               },
               emailConfirmation: {
                   required: "{{ T_campo_obligatorio|safe }}",
                   email: "{{ T_campo_valor_invalido|safe }}",
                   equalTo: "{{T_not_confirmed_email_warning|safe}}"
               },
               telephone: {
                   required: "{{ T_campo_obligatorio|safe }}",
                   phone: "{{ T_campo_valor_invalido|safe }}"
               },
               comments: "{{ T_campo_obligatorio|safe }}",
               accept_term: "{{ T_campo_obligatorio|safe }}"
           }

       });

        $("#contact-button").click(function () {
            var data = {
                'name': $("#name").val(),
                'surname': $("#surname").val(),
                'telephone': $("#telephone").val(),
                'email': $("#email").val(),
                'comments': $("#comments").val(),
                'g-recaptcha-response': $("#g-recaptcha-response").val()
            };

            var url_for_upload = "",
                url_cv_download = "",
                confirma_no_cv=1,
                send_email = 1;

            $.ajax({
                url: "/get_upload_url",
                type: 'GET',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (returndata) {
                 url_for_upload = returndata;
                }
              });

            if ($(this).closest("form").find("#file_cv").length && url_for_upload && $(this).closest("form").find("#file_cv").val()) {
                confirma_no_cv = 0;
                var formData = new FormData($(this).closest("form")[0]);
                $.ajax({
                    url: url_for_upload,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        url_cv_download = returndata;
                    }
                });
            }
            if (!confirma_no_cv && (!url_cv_download || url_cv_download=="NO_FILE_UPLOAD")){
                alert("ERROR UPLOADING CURRICULUM VITAE");
                console.log("URL: " + url_cv_download);
                window.location.reload();
                return false;
            }

            if (confirma_no_cv )  {
                if (confirm($.i18n._("confirm_no_cv"))   ){

                }
                else{
                    send_email = 0;
                }
            }

            data['url_file_download'] = url_cv_download;
            if ($('g-recaptcha').length) {
                if ($("#g-recaptcha-response").val()) {
                    if (send_email) {
                        if ($("#contact").valid()) {
                            $.post(
                                "/utils/?action=work_with_us", data,
                                function (data) {
                                    alert("{{ T_gracias_contacto }}");
                                    $("#name").val("");
                                    $("#surname").val("");
                                    $("#telephone").val("");
                                    $("#email").val("");
                                    $("#emailConfirmation").val("");
                                    $("#comments").val("");
                                }
                            );
                        }
                    }
                } else {
                    $(".g-recaptcha > div").css('border', '1px solid red');
                }
            } else {
                if (send_email) {
                    if ($("#contact").valid()) {
                        $.post(
                            "/utils/?action=work_with_us", data,
                            function (data) {
                                alert("{{ T_gracias_contacto }}");
                                $("#name").val("");
                                $("#surname").val("");
                                $("#telephone").val("");
                                $("#email").val("");
                                $("#emailConfirmation").val("");
                                $("#comments").val("");
                            }
                        );
                    }
                }
            }
        })
    });
</script>