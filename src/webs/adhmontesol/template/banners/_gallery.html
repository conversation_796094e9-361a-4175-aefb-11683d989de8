<div class="gallery_filter_wrapper">
    <div class="filters">
        <a class="filter active" data-filter="all">{{ T_ver_todas }}</a>
        {% for gallery, pics in gallery_filter.items() %}
            <a class="filter" data-filter="{{ gallery|safe }}">{{ gallery|safe }}</a>
        {% endfor %}
    </div>

    {% set gallery_counter = 0 %}
    {% for gallery, pics in gallery_filter.items() %}
        <div class="gallery_block" data-item="{{ gallery|safe }}">
            {% set gallery_counter = gallery_counter + 1 %}
            <div class="gallery_title">
                {{ gallery|safe }}
            </div>
            {% for pic in pics %}
                {% if loop.first %}
                    <div class="gallery_group_wrapper">{% endif %}
            {% if pic.description %}
                <div class="gallery_description">{{ pic.description|safe }}</div>
            {% else %}
                <a {% if pic.video or pic.linkUrl %}href="{{ pic.video|safe if pic.video else pic.linkUrl }}" data-fancybox data-type="iframe"
                   class="myFancyPopup fancybox.iframe image_wrapper with_svg_logo video_square"{% else %}
                   href="{{ pic.servingUrl }}=s1900" rel="lightbox[gallery_{{ gallery_counter }}]"
                   class="image_wrapper with_svg_logo" {% endif %}>
                    <div class="img_overlay"></div>
                    <img src="{{ pic.servingUrl }}=s600"
                         alt="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}"
                         title="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}">
                </a>
            {% endif %}
            {% if loop.index %4 == 0 and not loop.first %}</div>
                <div class="gallery_group_wrapper">{% endif %}
            {% if loop.last %}</div>{% endif %}
            {% endfor %}
        </div>
    {% endfor %}
</div>

<script>
    $(window).on("load", function () {
        // Filters
        $(".filter").click(function (e) {
            $(this).siblings(".active").removeClass("active");
            $(this).addClass("active");

            filter_gallery($(this).attr("data-filter"));
        });

        function filter_gallery(string) {
            // If slide up/down causes a lag of owl carousel then replace it with an animate without display: none

            $(".gallery_block").slideUp();
            if (string == "all") {
                $(".gallery_block").slideDown();
            } else {
                $(`.gallery_block[data-item=${string}]`).slideDown();
            }
        }
    })
</script>