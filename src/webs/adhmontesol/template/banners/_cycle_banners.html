<div class="cycle_banners_wrapper">
    {% for banner in cycle_banners %}
        <div class="cycle_element">
            {% if banner.gallery %}
                <div class="cycle_gallery owl-carousel">
                    {% for pic in banner.gallery %}
                        <img src="{{ pic.servingUrl }}=s1600" alt="" class="gallery_element">
                    {% endfor %}
                </div>
            {% else %}
                <div class="cycle_picture">
                    <img src="{{ banner.servingUrl }}=s1600" alt="">
                </div>
            {% endif %}

            <div class="cycle_content">
                <div class="banner_title">{{ banner.title|safe }}</div>
                <div class="banner_description">{{ banner.description|safe }}</div>
                {% if banner.book_button %}
                    <a class="btn_more button_promotion" href="#data">
						<span class="icon">
							<i class="fas fa-angle-right"></i>
						</span>
                        {{ T_reservar }}
                    </a>
                {% else %}
                    {% if banner.transfer_section %}
                        <a class="btn_more transfer" href="#transfer">
                            <span class="icon">
                                <i class="fas fa-angle-right"></i>
                            </span>
                            {% if transfer_sec.transfer_form %}
                                {{ transfer_sec.transfer_form|safe }}
                            {% endif %}
                        </a>
                    {% else %}
                        {% if banner.linkUrl %}
                            <a class="btn_more" href="{{ banner.linkUrl }}">
                                <span class="icon">
                                    <i class="fas fa-angle-right"></i>
                                </span>
                                {{ T_descubre_mas }}
                            </a>
                        {% endif %}
                    {% endif %}
                {% endif %} 
            </div>
        </div>
    {% endfor %}
    {% if transfer_sec %}
        {% include "banners/_transfer_form.html" %}
    {% endif %}
</div>

<script>
    $(window).on("load", function() {
        var owl_config = {
            items: 1, // 3
            mouseDrag: true, // true
            nav: true, // false
            navText: [
                '<i class="fal fa-long-arrow-left"></i>',
                '<i class="fal fa-long-arrow-right"></i>'
            ], // [&#x27;next&#x27;,&#x27;prev&#x27;]
            dots: false, // true
            smartSpeed: 500 // 250
        }

        var $owl = $(".cycle_gallery.owl-carousel").owlCarousel(owl_config);

        $(".cycle_gallery").each(function() {
            $(this).find(".owl-item").height($(this).height());
        });
    });
</script>