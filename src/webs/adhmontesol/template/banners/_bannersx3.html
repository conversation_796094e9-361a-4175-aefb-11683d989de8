<div class="container12">
    <div class="component_wrapper bannersx3" style="{% if not full_banner %}padding-top: 0{% endif %}">
        <div class="items_wrapper {% if is_mobile %}owl-carousel{% endif %}">
            {% for banner in bannersx3 %}
                <div class="item">
                    <img src="{{ banner.servingUrl }}" alt="Image">
                    <div class="caption">
                        {{ banner.title|safe }}
                        <div class="desc">{{ banner.description|safe }}</div>
                        <a href="{{ banner.linkUrl }}" class="btn_more">
							<span class="icon">
								<i class="fas fa-angle-right"></i>
							</span>
                            {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

{% if is_mobile %}
    <script>
        $(window).on("load", function () {
            var owl_config = {
                items: 1,
            }

            var $owl = $(".bannersx3 .items_wrapper.owl-carousel").owlCarousel(owl_config);
        });
    </script>
{% endif %}