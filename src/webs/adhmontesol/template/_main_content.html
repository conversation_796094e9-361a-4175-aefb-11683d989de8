{# Content subtitle #}
{% if home and content_subtitle %}
    <div class="content_subtitle_wrapper home container12">
        <div class="component_wrapper">
            {% if content_subtitle.pictures|length == 2 %}
                <div class="image_wrapper">
                    <div class="image">
                        <img src="{{ content_subtitle.pictures.0 }}" alt="">
                    </div>
                    <div class="image">
                        <img src="{{ content_subtitle.pictures.1 }}" alt="">
                    </div>
                </div>
            {% endif %}
            <div class="content_wrapper">
                <div class="title_wrapper">{{ content_subtitle.subtitle|safe }}</div>
                <div class="content">
                    <div class="desc">{{ content_subtitle.content|safe }}</div>
                </div>
            </div>
        </div>
    </div>
{% else %}
    {% if content_subtitle %}
        <div class="content_subtitle_wrapper container12">
            <div class="component_wrapper">
                <div class="path">
                    <a href="#">{{ T_inicio }}</a>
                    <a href="#">{{ content_subtitle.title|safe }}</a>
                </div>
                <div class="text_wrapper">
                    {{ content_subtitle.subtitle|safe }}
                    {% if content_subtitle.content %}
                        <div class="desc {% if "gallery_title" in content_subtitle.content %}custom_gallery{% endif %}" {% if main_text_height %}data-force_height="{{ main_text_height|safe }}"{% endif %}>{{ content_subtitle.content|safe }}</div>

                        <a class="btn_more_deploy">
                            <span class="text">{{ T_leer_mas }}</span>
                            <span class="icon">
                                <i class="fas fa-angle-down"></i>
                            </span>
                        </a>
                    {% endif %}

                </div>
            </div>
        </div>

        <script>
            $(window).on("load", function () {
                var desc_wrrapper = $(".content_subtitle_wrapper .text_wrapper .desc"),
                    button = $(".content_subtitle_wrapper .text_wrapper .btn_more_deploy"),
                    force_height = $(".content_subtitle_wrapper .text_wrapper .desc").data("force_height"),
                    main_height = 150;

                if (force_height) main_height = force_height;

                if (desc_wrrapper.height() > main_height) {
                    button.css({display: "block"});
                    desc_wrrapper.attr("data-height", desc_wrrapper.height());
                    desc_wrrapper.height(main_height);
                    desc_wrrapper.addClass("fold");

                    button.on("click", function () {
                        if (desc_wrrapper.hasClass("fold")) {
                            button.find(".text").text("{{ T_leer_menos }}");
                            button.addClass("active");
                            desc_wrrapper.removeClass("fold");
                            desc_wrrapper.css("height", desc_wrrapper.data("height"));
                        } else {
                            button.find(".text").text("{{ T_leer_mas }}");
                            button.removeClass("active");
                            desc_wrrapper.addClass("fold");
                            desc_wrrapper.css("height", main_height);
                        }
                    });
                }
            })
        </script>
    {% endif %}
{% endif %}

{% if popup_inicio %}
    <div class="popup_inicio_wrapper">
       <a class="close_popup_inicio"><i class="fa fa-times"></i></a>
       <img src="{{ popup_inicio[0].servingUrl|safe }}" class="img_wrapper">
    </div>
{% endif %}

{% if content_access or common_section %}
    <div class="content_access" {% if gallery_section %}style="margin: 0;" {% endif %}>{{ content|safe }}</div>
{% endif %}

{% if entry %}
    {% include "banners/_entry.html" %}
{% endif %}

{% if news %}
    <div class="news_widget_wrapper">
        <div class="entry_widget big_entry">
            <div class="image">
                <a href="/{{ path }}/{{ news.0.friendlyUrl }}">
                    <img src="{{ news.0.picture|safe }}{% if 'cdn2.paraty' in news.0.picture %}=s800{% endif %}" alt="">
                </a>
            </div><div class="content">
                <div class="date"><span>{{ news.0.creationDate|safe }}</span></div>
                <div class="title">{{ news.0.name|safe }}</div>
                <div class="entry_desc">{{ news.0.shortDesc|striptags|safe }}</div>
                <a href="/{{ path }}/{{ news.0.friendlyUrl }}" class="read_more"><span>{{ T_leer_mas }}</span></a>
                <div class="tags">{% for tag in news.0.tag_list %}{{ tag|safe }}{% endfor %}</div>
            </div>
        </div>
        {% include "banners/_news.html" %}
    </div>
{% endif %}

{% if rooms %}
    {% include "banners/_rooms.html" %}
{% endif %}

{% if offers %}
    {% include "banners/_offers.html" %}
{% endif %}

{% if gallery_filter %}
    {% include "banners/_gallery.html" %}
{% endif %}

{% if cycle_banners %}
    {% include "banners/_cycle_banners.html" %}
{% endif %}

{% if contact_form %}
    {% include "banners/_form_contact.html" %}
{% endif %}

{% if contact_form_cv %}
    {% include "banners/_form_contact_cv.html" %}
{% endif %}

{% if full_banner %}
    {% include "banners/_full_banner.html" %}
{% endif %}

{% if bannersx3 %}
    {% include "banners/_bannersx3.html" %}
{% endif %}

{% if parallax_1 and not contact_form %}
    {% include "banners/_parallax_1.html" %}
{% endif %}

{% if location_banner %}
    {% include "banners/_banner_location.html" %}
{% endif %}

{% if parallax_1 and contact_form %}
    {% include "banners/_parallax_1.html" %}
{% endif %}

{% if parallax_2 and not is_mobile %}
    {% include "banners/_parallax_2.html" %}
{% endif %}

{% if parallax_2_carousel and not is_mobile %}
    {% include "banners/_parallax_2_carousel.html" %}
{% endif %}

{% if minigallery and not is_mobile %}{{ minigallery|safe }}{% endif %}

{% include "banners/_effects.html" %}