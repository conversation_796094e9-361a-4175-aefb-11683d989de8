@font-face {
	font-family: 'Voyage';
	src: url('/static_inj/fonts/Voyage/Voyage-Regular.otf');
	font-style: normal;
	font-weight: 400;
}
@font-face {
	font-family: 'BentonSans';
	src: url('/static_inj/fonts/benton_sans/BentonSans-Regular.otf');
	font-style: normal;
	font-weight: 400;
}
@font-face {
	font-family: 'BentonSans';
	src: url('/static_inj/fonts/benton_sans/BentonSans-Book.otf');
	font-style: normal;
	font-weight: 200;
}

//Base web (change too in config.rb)
$base_web: "adhml";

// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #955243;
$corporate_2: #333F48;
$corporate_3: #A9876C;
$black: #333;

// Fonts
$voyage-font: 'Voyage', serif;
$benton-font: 'BentonSans', sans-serif;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

@mixin title_styles() {
  font-family: $voyage-font;
  font-size: 32px;
  line-height: 38px;
  color: $corporate_1;
}

@mixin text_styles() {
  font-weight: 300;
  font-family: $benton-font;
  font-size: 19px;
  line-height: 25px;
  color: $black;
}

@mixin btn_styles() {

}

// Mixins
@mixin transform2($action: translate(-50%, -50%)) {
	-webkit-transform: $action;
	-moz-transform: $action;
	-ms-transform: $action;
	-o-transform: $action;
	transform: $action;
}

@mixin transition2($action: all 1s) {
	-webkit-transition: $action;
	-moz-transition: $action;
	-ms-transition: $action;
	-o-transition: $action;
	transition: $action;
}

@mixin center ($axis: both) {
	position: absolute;

	@if $axis==X {
		left: 50%;
		@include transform2(translateX(-50%));
	}

	@else if $axis==Y {
		top: 50%;
		@include transform2(translateY(-50%));
	}

	@else {
		top: 50%;
		left: 50%;
		@include transform2();
	}
}

@mixin fill ($axis: both, $position: absolute) {
	position: $position;

	@if $axis==X {
		left: 0;
		right: 0;
	}

	@else if $axis==Y {
		top: 0;
		bottom: 0;
	}

	@else {
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}
}

@mixin overlay($color: #000000, $opacity: 0.5) {
	&::before {
		@include fill;
		content: '';
		background-color: $color;
		opacity: $opacity;
		z-index: 1;
	}
}

@mixin display-flex {
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
}

@mixin flex-wrap($wrap : wrap) {
	-webkit-flex-wrap: $wrap;
	-moz-flex-wrap: $wrap;
	-ms-flex-wrap: $wrap;
	-o-flex-wrap: $wrap;
	flex-wrap: $wrap;
}

@mixin cover_image {
	height: 100%;
	width: 100%;
	object-fit: cover;
}

@mixin clearfix() {
	&::after {
		content: "";
		clear: both;
		display: table;
	}
}

@mixin title_wrapper_styles {
		.subtitle {
			font-family: $benton-font;
			font-weight: 300;
			font-size: 30px;
			text-transform: uppercase;
			letter-spacing: 3px;
			line-height: 30px;
			color: $corporate_2;
		}

		.title {
			font-family: $voyage-font;
			font-weight: 700;
			font-size: 60px;
			letter-spacing: 3.6px;
			line-height: 60px;
			color: $corporate_1;
		}
}

@mixin desc_styles {
	font-family: $benton-font;
	font-weight: 300;
	font-size: 16px;
	letter-spacing: 0.8px;
	line-height: 25px;
	color: $corporate_2;
	margin-bottom: 30px;
}
