.cycle_banners_wrapper {
  .cycle_element {
    display: grid;
    grid-template-columns: 50% 50%;
    margin: 80px 0;
    position: relative;

    &:first-of-type {
      margin-top: 0;
    }

    &:before {
      content: "";
      position: absolute;
      right: 75%;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: 5;
      background-image: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0));
      opacity: 0.99;
    }

    &:nth-child(even) {
      .cycle_picture, .cycle_gallery {
        grid-column: 2;
      }

      .cycle_content {
        grid-column: 1;
        justify-self: flex-end;
      }
      &:before {
        background-image: linear-gradient(-90deg, #ffffff, rgba(255, 255, 255, 0));
        left: 75%;
        right: 0;
      }
      .cycle_gallery .owl-nav {
        right: 40px;
        left: auto;
      }
    }

    .cycle_gallery {
      grid-column: 1;
      grid-row: 1;
      position: relative;

      .owl-item {
        img {
          min-height: 100%;
          min-width: 100%;
          object-fit: cover;
          min-height: 500px;
        }
      }

      .owl-nav {
        padding-bottom: 25px;
        text-align: right;
        position: absolute;
        top: 104%;
        left: 40px;

        .owl-prev, .owl-next {
          padding: 0;
          margin: 0;
          outline: none;
          display: inline-block;

          i {
            color: $corporate_2;
            font-size: 26px;

          }

          &:hover {
            background: none;
            color: $corporate_2;
          }
        }

        .owl-next {
          margin-left: 10px;
        }
      }
    }

    .cycle_picture {
      position: relative;
      overflow: hidden;
      grid-column: 1;
      grid-row: 1;

      img {
        @include cover_image;
        min-height: 500px;
      }
    }
    .cycle_content {
      grid-column: 2;
      grid-row: 1;
      padding: 100px 50px 50px;
      display: flex;
      flex-flow: column;
      max-width: calc((1140px / 2) - 70px);

      .banner_title {
        color: $corporate_1;
        font-family: $voyage_font;
        font-size: 46px;
        font-weight: bold;
        letter-spacing: 3px;
        margin-bottom: 55px;
      }

      .banner_description {
        width: 100%;
        line-height: 25px;
        margin-right: 0;
        font-size: 14px;
        font-weight: 300;
        color: $corporate_2;
        font-family: $benton-font;

        b, strong {
          font-weight: bold;
        }
      }

      .btn_more {
        display: table;
        margin-top: 50px;
        margin-right: auto;
        line-height: 40px;
        padding: 0 20px;
        margin-left: 15px;
        font-family: $voyage-font;
        font-weight: 400;
        font-size: 20px;
        letter-spacing: 1.6px;
        color: $corporate_2;
        border-radius: 20px;
        cursor: pointer;
        @include transition(background-color, .4s);

        .icon {
          position: relative;
          margin-right: 5px;

          &::before {
            @include center(Y);
            right: 50%;
            content: "";
            height: 1px;
            width: 35px;
            background-color: $corporate_1;
          }

          &::after {
            content: "";
            @include center();
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: $corporate_1;
            opacity: 0.25;
            z-index: -1;
            @include transition(background-color, .4s);
          }

          i {
            @include center();
            color: $corporate_1;
            font-size: 11px;
          }
        }
      }

      .btn_more:hover {
        background-color: rgba($corporate_1, 0.25);

        .icon {
          &::after {
            background-color: transparent;
          }
        }
      }
    }
  }
}