.parallax_2_carousel.component_wrapper {
	height: 570px;
	position: relative;
	margin-top: 130px;
	padding: 0;

	.owl-item {
		height: 570px;
		.parallax_pic {
			height: 570px;
			position: relative;
			&::before {
				content: "";
				@include fill(X);
				top: 0;
				bottom: 65%;
				background-image: linear-gradient(180deg, rgba(white, 1), rgba(white, 0));
			}
			&::after {
				content: "";
				@include fill(X);
				top: 65%;
				bottom: 0;
				background-image: linear-gradient(0deg, rgba(black, 0.4), rgba(black, 0));
			}
		}
	}
	.owl-nav {
		position: absolute;
		bottom: 60px;
		right: 170px;

      .owl-prev, .owl-next {
        padding: 0;
        margin: 0;
        outline: none;
        display: inline-block;

        i {
          color: white;
          font-size: 26px;
        }

        &:hover {
          background: none;
          color: $corporate_2;
        }
      }

      .owl-next {
        margin-left: 10px;
      }
    }

	.owl-dots {
		position: absolute;
		bottom: 60px;
		left: 170px;
		display: flex;
		flex-flow: row nowrap;

		.owl-dot {
			margin: 0 5px;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			position: relative;

			&:before {
				content: '';
				top: -3px;
				bottom: -3px;
				left: -3px;
				right: -3px;
				position: absolute;
				border: 1px solid white;
				border-radius: 50%;
			}

			&.active {
				background-color: white;
			}
		}
	}
}