.bannersx3 {
  padding: 130px 0;

  .items_wrapper {
    width: 100%;
    height: 395px;
    @include display-flex();
    justify-content: space-between;

    .item {
      width: calc((100% - 50px) / 3);
      height: 100%;
      overflow: hidden;
      position: relative;

      @include overlay(#050505, 0);

      &::before {
        @include transition2(all 0.8s);
      }

      &::after {
        @include fill(X);
        top: 30%;
        bottom: 0;
        content: '';
        background: linear-gradient(180deg, rgba(black, 0), rgba(black, 1));
        opacity: 0.4;
        z-index: 1;
        @include transition2(all 0.8s);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .caption {
        @include fill();
        z-index: 2;
        color: white;
        padding: 50px 25px 35px;
        @include transform2(translateY(275px));
        @include transition2(all 0.8s);

        .title {
          font-family: $benton-font;
          font-weight: 300;
          font-size: 30px;
          letter-spacing: 3px;
          text-transform: uppercase;
          margin-bottom: 40px;
          line-height: 31px;
        }

        .subtitle {
          font-family: $voyage-font;
          font-weight: 700;
          font-size: 32px;
          letter-spacing: 0.64px;
          line-height: 24px;
          width: 70%;
          margin-bottom: 15px;
        }

        .desc {
          font-family: $benton-font;
          font-weight: 300;
          font-size: 16px;
          letter-spacing: 0.32px;
          line-height: 25px;
          width: 80%;
          margin-bottom: 30px;
        }

        .btn_more {
          display: inline-block;
          position: absolute;
          bottom: 25px;
          left: 25px;
          line-height: 50px;
          padding: 0 20px;
          margin-left: 15px;
          font-family: $voyage-font;
          font-weight: 400;
          font-size: 20px;
          letter-spacing: 1.6px;
          border-radius: 30px;
          cursor: pointer;
          @include transition(background-color, .4s);

          .icon {
            position: relative;
            margin-right: 5px;

            &::before {
              @include center(Y);
              right: 50%;
              content: "";
              height: 1px;
              width: 35px;
              background-color: white;
            }

            &::after {
              content: "";
              @include center();
              width: 50px;
              height: 50px;
              border-radius: 50%;
              background-color: $corporate_1;
              opacity: 0.7;
              z-index: -1;
              @include transition(background-color, .4s);
            }

            i {
              @include center();
              color: white;
              font-size: 13px;
              font-weight: 100;
            }
          }
        }

        .btn_more:hover {
          background-color: rgba($corporate_1, 0.7);

          .icon {
            &::after {
              background-color: transparent;
            }
          }
        }
      }
    }

    .item:hover {
      &::before {
        opacity: 0.5;
      }

      &::after {
        opacity: 0;
      }

      .caption {
        @include transform(none);
      }
    }
  }
}