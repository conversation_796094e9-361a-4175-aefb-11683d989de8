.component_wrapper.full_banner {
  padding: 0;
  height: 700px;
  position: relative;
  background-position: center 100px;
  background-repeat: no-repeat;
  background-size: cover;

  &::before {
    content: "";
    @include fill(X);
    top: 0;
    bottom: 45%;
    background: linear-gradient(#FFF 0%, #FFF 50%, rgba(#FFFFFF, .6) 75%, rgba(#FFFFFF, 0) 100%);
  }

  .content_wrapper {
    padding: 50px 0;
    @include clearfix();

    .title_wrapper,
    .desc_wrapper {
      float: left;
      width: 50%;
    }
  }

  .title_wrapper {
    .subtitle {
      font-family: $benton-font;
      font-weight: 300;
      font-size: 25px;
      text-transform: uppercase;
      letter-spacing: 3px;
      line-height: 30px;
      color: $corporate_2;
    }

    .title {
      font-family: $voyage-font;
      font-weight: 700;
      font-size: 45px;
      letter-spacing: 3px;
      line-height: 50px;
      color: #BFBB98;
    }
  }

  .desc_wrapper {
    float: right !important;

    .desc {
      display: block;
      width: 340px;
      margin-top: 90px;
      margin-left: auto;
      padding: 50px 30px 30px;
      background-color: rgba(white, .9);
      font-family: $benton-font;
      font-weight: 300;
      font-size: 14px;
      letter-spacing: 0.2px;
      line-height: 21px;
      color: $corporate_2;
    }
  }
}
