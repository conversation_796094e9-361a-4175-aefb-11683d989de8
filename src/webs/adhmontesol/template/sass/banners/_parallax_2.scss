.parallax_2.component_wrapper {
	height: 570px;
	position: relative;
	margin-top: 30px;
	padding: 0;
	.parallax_pic {
		height: 570px;
		@media not screen and (min-device-width : 768px) and (max-device-width : 1024px) {
			background-attachment: fixed;
		}
		background-position: center;
		background-repeat: no-repeat;
		background-size: cover;
	}
	.video_square {
	  &:before {
		content: '\f144';
		font-family: "Font Awesome 5 Pro", Sans-Serif;
		@include center_xy;
		font-size: 65px;
		color: white;
		font-weight: 400;
		opacity: 1;
		visibility: visible;
		pointer-events: none;
		@include transition(all, .6s);
		z-index: 6;
	  }
	  &:hover {
		&:before {
		  color: $corporate_2;
		}
	  }
	}

	&::before {
		content: "";
		@include fill(X);
		top: 0;
		bottom: 65%;
		background-image: linear-gradient(180deg, rgba(white, 1), rgba(white, 0));
		opacity: 0.99;
	}
}