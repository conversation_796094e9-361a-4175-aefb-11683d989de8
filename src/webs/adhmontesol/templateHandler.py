# -*- coding: utf-8 -*-
import logging
from collections import OrderedDict

from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY, INSTAGRAM_TOKEN
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url, unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey, getLogotypes
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type, \
    get_section_from_section_spanish_name_with_properties
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.development.dev_booking_utils import DEV, DEV_NAMESPACE
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.general_utils_methods import smart_truncate
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

TEMPLATE_NAME = "adhmontesol"
# Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4] + TEMPLATE_NAME[-1:]


class TemplateHandler(BaseTemplateHandler2WithRedirection):

    def getAdditionalParams(self, currentSectionName, language, allSections):
        sectionToUse = self.getCurrenSection(allSections)
        if not sectionToUse:
            sectionToUse = {}

        params = {
            'base_web': base_web
        }

        if not user_agent_is_mobile():
            params.update(self.getDesktopData(sectionToUse, language))
        else:
            script_args = {
                "section_type": sectionToUse.get("sectionType")
            }

            params_mobile = {
                "custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
                "extra_bottom_script": self.buildTemplate_2("mobile/_script_mobile.html", script_args, False,
                                                            TEMPLATE_NAME),
                "fontawesome5": True,
            }

            all_slider_pictures = getPicturesForKey(language, "frontPictures", [])
            slider_pictures = []
            for pic in all_slider_pictures:
                advance_property = self.getSectionAdvanceProperties(pic, language)
                if not advance_property.get("only_desktop"):
                    slider_pictures.append(pic)

            mini_dict = {
                'pictures': slider_pictures
            }
            mini_dict.update(get_web_dictionary(language))

            mini_html = self.buildTemplate_2("mobile/_slider_mobile.html", mini_dict, False, TEMPLATE_NAME)
            params_mobile['custom_slider_home'] = mini_html
            params.update(params_mobile)

        return params

    def getDesktopData(self, section, language):
        section_type = ''
        section_name = ''
        if section:
            section_type = section['sectionType']
            section_name = section['sectionName']
        else:
            section = {}

        result_params_dict = {
            'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
            'booking_engine_2': self.buildSearchEngine2(language),
            'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True),
            'partners_footer': get_pictures_from_section_name("_partners_footer", language),
            'logo_footer': getLogotypes("footer"),
            'links_footer': get_pictures_from_section_name("_links_footer", language),
            'info_footer': get_section_from_section_spanish_name("_info_footer", language),
            'social': self.getSocialDictionary(),
            'popup_inicio': get_pictures_from_section_name("_popup_inicio_automatico_2", language)
        }

        pictures_preloading = get_pictures_from_section_name("_preloading", language)
        result_params_dict['preloading_background'] = list(filter(lambda x: x.get('title') == "background", pictures_preloading))
        result_params_dict['preloading_motif'] = list(filter(lambda x: x.get('title') == "motif", pictures_preloading))
        result_params_dict['preloading_logo'] = list(filter(lambda x: x.get('title') == "logo", pictures_preloading))

        news_section = build_friendly_url(get_web_dictionary(language)['T_noticias'])
        path = get_language_code(language) + "/" + news_section.replace(".html", "")
        result_params_dict['path'] = path

        all_sections = self.getSections(language)
        submenu_sections = self.getSectionsFor(language, all_sections, "Secciones en submenu")
        main_sections = self.getSectionsFor(language, all_sections, "Secciones en menu principal")
        for menu in submenu_sections:
            menu.update(get_properties_for_entity(menu.get("key"), language))

            if menu.get("special_title"):
                menu['special_title'] = unescape(menu['special_title'])

        for menu in main_sections:
            menu.update(get_properties_for_entity(menu.get("key"), language))

            if menu.get("special_title"):
                menu['special_title'] = unescape(menu['special_title'])

        result_params_dict['submenu'] = submenu_sections
        result_params_dict['main_sections'] = main_sections

        if section.get('subtitle'):
            result_params_dict['content_subtitle'] = section

        automatic_content = {
            'Mis Reservas': True
        }
        if automatic_content.get(section_type):
            result_params_dict['content_access'] = True

        result_params_dict.update(self.getDataSection(section, language))
        result_params_dict.update(self.getExtraBanners(section, language))

        return result_params_dict

    def getDataSection(self, section, language):
        result = {}
        section_type = section.get("sectionType")
        if section_type == "Inicio":
            result['home'] = True

        elif section_type == "Habitaciones":
            result['rooms'], result['room_filters'] = self.getRooms(language)

        elif section_type == "Ofertas":
            result['offers'] = self.getOffers(language)

        elif section_type == u"Localización":
            result['contact_form'] = True
            result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
            result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')


        elif section_type == 'Galeria de Imagenes':
            gallery_pics = self.get_hotel_gallery(language)
            group = OrderedDict()
            for pic in gallery_pics:
                group.setdefault(pic.get('title'), [])
                group[pic.get('title')].append(pic)
                pic_properties = self.getSectionAdvanceProperties(pic, language)
                pic['video'] = pic_properties.get('video')
            result['gallery_filter'] = group
            result['content_access'] = False

        elif section_type == "Noticias":
            result['news'] = self.getNews(language)

        elif not section:
            news = self.getNews(language)
            result['individual_news'] = True
            result['entry'] = self.getCurrentNewsItem(news, language)
            result['foto_collage'] = self.get_hotel_gallery(language)
            result['social'] = self.getSocialDictionary()
            result['news_widget'] = self.getNews(language, 3)
            result['sectionToUse'] = {}

        return result

    def getExtraBanners(self, section, language):
        result = {}
        advance_properties = self.getSectionAdvanceProperties(section, language)

        if advance_properties.get("minigallery"):
            minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
            result['minigallery_for_mobile'] = minigallery_images
            mini_dict = {'minigallery': minigallery_images, 'num_items': 5, 'margin': 5}
            minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
            result["minigallery"] = minigallery_html

        if advance_properties.get("full_banner"):
            result['full_banner'] = get_section_from_section_spanish_name(advance_properties['full_banner'], language)

        if advance_properties.get("bannersx3"):
            result['bannersx3'] = self.getPicturesProperties(language, advance_properties['bannersx3'])

        if advance_properties.get("main_text_height"):
            result['main_text_height'] = advance_properties.get("main_text_height")

        if advance_properties.get("parallax_1"):
            result['parallax_1'] = get_section_from_section_spanish_name(advance_properties['parallax_1'], language)

        if advance_properties.get("parallax_2"):
            result['parallax_2'] = get_section_from_section_spanish_name(advance_properties['parallax_2'], language)
            result['parallax_2_pictures'] = self.getPicturesProperties(language, advance_properties['parallax_2'])

        if advance_properties.get("parallax_2_carousel"):
            result['parallax_2_carousel'] = get_section_from_section_spanish_name(advance_properties['parallax_2_carousel'], language)
            result['parallax_2_carousel_pictures'] = get_pictures_from_section_name(advance_properties['parallax_2_carousel'], language)

        if advance_properties.get("banner_icons_test"):
            result['banner_icons_test'] = get_section_from_section_spanish_name(advance_properties['banner_icons_test'], language)
            result['banner_icons_test_pics'] = get_pictures_from_section_name(advance_properties['banner_icons_test'], language)

        if advance_properties.get("location_banner"):
            result['location_banner'] = get_section_from_section_spanish_name(advance_properties['location_banner'], language)

        if advance_properties.get("cycle_banners"):
            cycle_banners = self.getPicturesProperties(language, advance_properties['cycle_banners'])

            for pic in cycle_banners:
                if pic.get("gallery"):
                    pic['gallery'] = get_pictures_from_section_name(pic['gallery'], language)

                if pic.get("transfer_section"):
                    result['transfer_sec'] = get_section_from_section_spanish_name_with_properties(pic.get("transfer_section"), language)

            result['cycle_banners'] = cycle_banners

        if advance_properties.get("special_title"):
            result['special_title'] = unescape(advance_properties['special_title'])

        if advance_properties.get("form_contact_cv"):
            result['contact_form_cv'] = True
            if not DEV:
                result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

        popup_inicio = get_pictures_from_section_name("_popup_inicio_automatico_2", language)
        if popup_inicio:
            result['popup_inicio'] = popup_inicio

        return result

    def getRooms(self, language):
        rooms = get_sections_from_type(u"Habitación Individual", language)
        room_filters = []
        for room in rooms:
            room.update(self.getSectionAdvanceProperties(room, language))
            room_pictures = getPicturesForKey(language, room.get("key"), [])
            room['gallery'] = []
            room['icons'] = []
            for pic in room_pictures:
                if pic.get("title") == "main":
                    room['main'] = pic

                elif pic.get("title") == "gallery":
                    room['gallery'].append(pic)

                elif pic.get("title") == "icon":
                    room['icons'].append(pic)

            if room.get("filter"):
                if room.get("filter") not in room_filters:
                    room_filters.append(room.get("filter"))

        rooms = sorted(rooms, key=lambda l: l.get("order", "999"))

        return rooms, room_filters

    def getOffers(self, language):
        offers = self.buildPromotionsInfo(language)

        return offers

    def getTemplateUrl(self, section=None):
        return thisUrl

    def get_revolution_initial_height(self):
        return "650"

    def get_revolution_initializer(self):
        return True

    def get_revolution_full_screen(self):
        return "on"

    def get_revolution_masterspeed(self):
        return "1200"

    def get_revolution_transition(self):
        return "fade"

    def buildSearchEngine(self, language, selectOptions=None):
        params = self.getBookingWidgetOptions(language, selectOptions)
        return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(TemplateHandler, self).getBookingWidgetOptions(language)
        options['caption_submit_book'] = True
        options['departure_date_select'] = True
        options['T_put_promocode'] = "PROMOCODE"

        return options

    def buildSearchEngine2(self, language, selectOptions=None):
        params = self.getBookingWidgetOptions2(language, selectOptions)
        return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

    def getBookingWidgetOptions2(self, language, selectOptions=None):
        options = super(TemplateHandler, self).getBookingWidgetOptions(language)
        options['caption_submit_book'] = True
        return options

    def getParamsForSection(self, section, language):
        result = {}

        if section['sectionType'] == "Mis Reservas":
            result = super(TemplateHandler, self).getParamsForSection(section, language)
            result['disable_content'] = True

        if result:
            return result
        else:
            return super(TemplateHandler, self).getParamsForSection(section, language)

    def buildNewsletter2(self, language, name=False, date=False, social=False, check_newsletter=False, background=True,fontawesome5=False):
        template_values = self.newsletter_data(language, name=name, social=social, check_newsletter=check_newsletter, background=background, fontawesome5=fontawesome5)

        try:
            instagram_token = get_config_property_value(INSTAGRAM_TOKEN)
            if instagram_token:
                template_values['newsletter_pictures'] = get_pictures_from_section_name("_gallery_newsletter", language)

        except Exception as e:
            logging.error("Error with instagram api")
            logging.error(e)

        content = self.buildTemplate_2('banners/_newsletter.html', template_values, allowMobile=False, additional_path=TEMPLATE_NAME)

        return content

    def buildContentForSection(self, sectionFriendlyUrl, language,
                               sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
        currentSection = self.getSectionParams(sectionFriendlyUrl, language)

        if currentSection:
            if user_agent_is_mobile():
                html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

                if html_to_render:
                    return html_to_render
                else:
                    additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

                additionalParams['get_subtitle'] = True

        return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
                                                                   additionalParams)

    def getHtmlTypeSectionMobile(self, section, language):
        section_type = section['sectionType']
        language_dict = get_web_dictionary(language)
        extra_banners = self.getExtraBanners(section, language)

        if section_type == "Habitaciones":
            language_dict = get_web_dictionary(language)
            extra_banners.update(language_dict)
            extra_banners['is_mobile'] = True
            extra_banners['content_subtitle'] = section
            extra_banners['rooms'], extra_banners['room_filters'] = self.getRooms(language)
            result = "<div class='section_content'>"

            result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

            result += "</div>"

            return result

        elif section_type == "Ofertas":
            return False

    def getHtmlExtraBannersMobile(self, section, language):
        extra_banners = self.getExtraBanners(section, language)
        language_dict = get_web_dictionary(language)
        extra_banners.update(language_dict)
        extra_banners['is_mobile'] = True
        extra_banners['language'] = get_language_code(language)
        extra_banners['base_web'] = base_web
        if not section:
            section = {}

        section_type = section.get('sectionType')

        if section_type and section_type == "Normal":
            extra_banners['content_subtitle'] = section

        result = "<div class='section_content'>"

        result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

        if extra_banners.get("minigallery_for_mobile"):
            args = {
                'minigallery_mobile': extra_banners["minigallery_for_mobile"]
            }
            result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

        result += "</div>"

        return result

    def getNews(self, language, limit=None):
        news = self.buildNewsInfo(language)

        for item in news:
            description_sanitize = item.get('description', '')
            item['shortDesc'] = smart_truncate(description_sanitize, remove_html_tags=True, length=150)

            item['comments'] = []
            for pic in item.get('pictures'):
                if pic.get("enabled"):
                    if not pic.get('servingUrl'):
                        item['comments'].append(pic)
            if item.get('tags'):
                item['tag_list'] = item.get('tags', '').split("@@")

        if limit:
            return news[:limit]
        else:
            return news

    def renderNewsPage(self, language, country, template_path="secciones/newsPage.html"):

        content_to_render = ''

        if user_agent_is_mobile():
            base_path = os.path.dirname(__file__)
            result_params_dict = {}
            news = self.getNews(language)
            result_params_dict['entry'] = self.getCurrentNewsItem(news, language)
            news_section = build_friendly_url(get_web_dictionary(language)['T_noticias'])
            path = get_language_code(language) + "/" + news_section.replace(".html", "")
            result_params_dict['path'] = path
            result_params_dict['is_mobile'] = True
            result_params_dict['news_widget'] = self.getNews(language, 5)
            mySectionParams = dict(list(result_params_dict.items()) + list(get_web_dictionary(language).items()))
            fullPath = os.path.join(base_path, 'template/banners/_entry.html')
            content_to_render = buildTemplate(fullPath, mySectionParams)
            news = self.buildNewsInfo(language)
            newsItemToUse = self.getCurrentNewsItem(news, language)
            engine = None
            if newsItemToUse:
                sections = self.getSections(language)
                params = self.getCommonParams(language, sections)
                params['content'] = content_to_render
                if DEV and DEV_NAMESPACE:
                    params['namespace'] = DEV_NAMESPACE
                else:
                    params['namespace'] = get_namespace()

                params = dict(list(params.items()) + list(self.getAdditionalParams('', language, sections).items()))
                fullPath = os.path.join(os.path.dirname(__file__), '../../templates/mobile_templates/2/inner_section.html')

                engine = buildTemplate(fullPath, params)

            return engine

        return super(TemplateHandler, self).renderNewsPage(language, country, template_path)
