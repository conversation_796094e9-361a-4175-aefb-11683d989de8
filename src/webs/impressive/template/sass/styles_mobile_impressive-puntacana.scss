@import "defaults";
@import "styles_mobile/2/2";


header,
.breadcrumbs,
nav,
.mobile_engine,
.mailto {
  display: none !important;
}

body {
  overflow-x: hidden;
  padding: 0;
}

.default_content_wrapper .content .section_content {
  width: 100vw;
  padding: 0 20px;
  color: $black !important;
  font-family: $text_family;

  .logo_description {
    max-width: 50vw;
    height: auto;
  }

  .title_description {
    font-family: $title_family;
    margin: 0;
  }

  #promocodeFormCompany {
    width: 100%;

    fieldset {
      border: none;
      padding: 0;
    }

    input,
    textarea,
    button {
      width: 100%;
    }

    button {
      border-radius: 0;
      border-width: 0;
      text-transform: none;
      background: $corporate_1;
      color: white;
      font-weight: 500;
      font-size: 18px;
      font-family: $title_family;
      text-transform: uppercase;
      transition: all .4s;
      padding: 10px 0;
    }
  }
}

.my_reservation_section {
  margin-top: 40px;
}

@import "agencies_login";

