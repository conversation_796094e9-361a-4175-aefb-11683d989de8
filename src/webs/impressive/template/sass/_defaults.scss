//Base web (change too in config.rb)
$base_web: "impre";

@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Jomolhari&display=swap');

// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #84754e;
$corporate_2: #0E0A07;
$black: #333;

$title_family: '<PERSON><PERSON><PERSON><PERSON>', serif;
$text_family: 'Raleway', sans-serif;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

@mixin title_styles() {
  font-family: $title_family;
  font-size: 32px;
  line-height: 38px;
  color: $corporate_1;
}

@mixin text_styles() {
  font-weight: 300;
  font-family: $text_family;
  font-size: 19px;
  line-height: 25px;
  color: $black;
}

@mixin btn_styles() {

}

