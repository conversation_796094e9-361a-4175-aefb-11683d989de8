<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">
<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }} - {{ hotel_name|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1.21" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}
    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>
    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="format-detection" content="telephone=no">
    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=1.33"/>
    <!--[if IE 8]><link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" /><![endif]-->
    <!--[if lte IE 7]><script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script><![endif]-->
    <!--[if lte IE 8]><script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->

    {{ jquery|safe }}
    {{ extra_head|safe }}
    {{ all_tracking_codes_header|safe }}
</head>
<body itemscope itemtype="//schema.org/Hotel" {% if not home %}class="inner_section" {% endif %}>
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">
{% if lang_management %}<input type="hidden" id="lang_management" value="{{ lang_management }}">{% endif %}
{% if lang_default %}<input type="hidden" id="lang_default" value="{{ lang_default }}">{% endif %}
{% block content %}<!--EDIT HERE YOUR PAGE-->{% endblock %}

{% if bottom_popup and bottom_popup_text %}
    <div class="bottom_popup" style="display: none">
        <div class="close_button">
            <img src="/static_1/images/close.png" alt="close" title="close"/>
        </div>
        <div id="wrapper2">
                <div class="bottom_popup_text">{{ bottom_popup.content|safe }}</div>
                <button class="bottom_popup_button">{{ T_apuntate|safe }}</button>
        </div>
    </div>

    <script>
        $(".bottom_popup_button").click(function () {
            $.fancybox.open($(".popup_inicial"), {wrapCSS: 'popup-start'});
        })
    </script>

    <div style="display:none;">
        <div class="popup_inicial" style="background:url({{ bottom_popup_text.pictures.0 }});">
        <p class="popup_description">
            {{ bottom_popup_text.content|safe }}
        </p>
        <form action="" method="post" class="form_popup">
            <ul>
                <li>
                <input id="id_email" type="text" name="email" maxlength="150" placeholder="{{ T_introduce_email_placeholder }}">
                </li>
            </ul>
            <div class="check_newsletter">
                <div class="newsletter_checkbox">
                    <input class="check_privacy" id="accept-term" name="accept_term" type="checkbox" value="privacy"/>
                    <a href="/{{language}}/?sectionContent=politica-de-privacidad.html" class="myFancyPopup fancybox.iframe newsletter_popup" rel="nofollow">{{T_lopd}}</a>
                </div>
            </div>
            <div class="check_newsletter">
                <div class="newsletter_checkbox">
                    <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="privacy"/>
                    <label for="promotions">{{T_acepto_promociones}}</label>
                </div>
            </div>
            <button class="popup_button">{{ bottom_popup_text.subtitle|safe }}</button>
            <div class="spinner_wrapper_faldon" style="display:none;"><img src='/static_1/images/spinner.gif' width="30" height="30" alt="spinner" title="spinner"></div>
        </form>
        </div>
        {% if  bottom_popup_background.0.description %}
        <div class="popup_thanks" style="background:url({{ bottom_popup_background.0.servingUrl }}=s800);">

            <p class="popup_description">
                <div id="new_gracias_newsletter">{{ bottom_popup_background.0.description|safe }}</div>
            </p>

            <button class="apply_code" data-promocode="{{ bottom_popup_promocode|safe }}">{{ bottom_popup_background.0.title|safe }}</button>

        </div>
        {% endif %}
    </div>

{% endif %}

{% block additional_js %}
    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1.2"></script>
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js"></script>
    <script>$(function () {
        DP_extend_info.config.booking_version = '7';
        DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            return "<div class='day'>" + dateComponents[0] + "</div>/<div class='month'>" + dateComponents[1] + "</div>/<div class='year'>" + dateComponents[2] + "</div> ";
        };
    });
    $(window).load(function () {
        DP_extend_info.init();
        $(".dates_selector_label").html("<span>{{ T_entrada }}</span><span>{{ T_salida }}</span>");
        $("input.promocode_input").removeAttr("placeholder");
        $(".wrapper_booking_button label.promocode_label").html("{{ T_tienes_promocode|safe }}");
        $(".button_newsletter span").html("{{ T_suscribete }}");
    })
    </script>
    <script async type="text/javascript" src="/static_1/scripts/booking_7.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js?v=1.16"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1.15"></script>
    <script async type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js"></script>

    <div style="display: none;">
        <div id="data">
            <div id="wrapper_booking_fancybox">
                <div id="booking_widget_popup" class="booking_widget_fancybox">{{ booking_engine_2|safe }}</div>
            </div>
        </div>
    </div>
{% endblock %}
{{ extra_content_website|safe }}
{{ all_tracking_codes_footer|safe }}
</body>
</html>