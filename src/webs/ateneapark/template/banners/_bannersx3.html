<div class="bannerx3_wrapper container12">
    {% for banner in bannersx3 %}<a {% if banner.linkUrl %}href="{{ banner.linkUrl|safe }}" {% if 'http:' in banner.linkUrl %}target="_blank"{% endif %}{% endif %} class="banner">
        <img src="{{ banner.servingUrl|safe }}=s800" alt="{{ banner.altText }}">
        <div class="banner_content">
            {% if banner.title %}<h3>{{ banner.title|safe }}</h3>{% endif %}
            {% if banner.description %}<div class="text">{{ banner.description|safe }}</div>{% endif %}
        </div>
    </a>{% endfor %}
</div>

{#

{% if not isMobile %}
    <script>
        $(window).load(bannerx3_bounce);

        $(window).scroll(bannerx3_bounce);

        function bannerx3_bounce() {
            try{
            $(".bannerx3_wrapper .banner:nth-child(1)").addnimation({parent: $(".bannerx3_wrapper"), class:"bounceInUp", speed: 1500});
            $(".bannerx3_wrapper .banner:nth-child(2)").addnimation({parent: $(".bannerx3_wrapper"), class:"bounceInLeft", windowDiference: 200, speed: 1500});
            $(".bannerx3_wrapper .banner:nth-child(3)").addnimation({parent: $(".bannerx3_wrapper"), class:"bounceInRight", windowDiference: 200, speed: 1500});
            } catch (e) {

        }
        }
    </script>
{% endif %}

#}