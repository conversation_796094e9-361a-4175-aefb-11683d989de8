header {
  width: 100%;
  position: relative;
  z-index: 30;
  #wrapper-header {
    background-color: white;
    padding: 20px 0;

    .contact_header {
      text-align: left;
      font-size: 17px;
      display: inline-block;
      vertical-align: middle;
      width: calc(50% - 155px);
      color: $corporate_3;
      a {
        i {
          color: $corporate_2;
          font-size: 22px;
          vertical-align: middle;
        }
        span {
          color: $corporate_3;
          vertical-align: middle;
          font-weight: $font-weight;
        }
        &:hover {
          i {
            color: $corporate_1;
          }
          span {
            color: #5a5a5a;
          }
        }
      }
    }

    #logoDiv {
      width: 300px;
      display: inline-block;
      vertical-align: middle;
      img {
        max-width: 100%;
      }
    }


    .rrss_header {
      text-align: right;
      display: inline-block;
      vertical-align: middle;
      width: calc(50% - 155px);
      #top-sections {
        display: inline-block;
        a {
          i {
            font-size: 22px;
            color: $corporate_2;
          }
          span {
            color: $corporate_3;
            font-family: $text_family;
            font-weight: $font-weight;
            font-size: 17px;
            margin-left: 5px;
          }
          &:hover {
            i {
              color: $corporate_1;
            }
            span {
              color: #5a5a5a;
            }
          }
        }
      }
    #social {
      display: inline-block;
      margin-left: 20px;
      a {
         color: $corporate_2;
         font-size: 22px;
          &:hover {
            color: $corporate_1;
          }
        }
      }
    }

  }


  .main_menu_wrapper {
    background-color: $corporate_1;
    font-family: $title_family;
    font-size: 18px;
    text-align: center;
    #main_menu {
      display: inline-block;
      vertical-align: middle;

      #mainMenuDiv {
        width: 1070px;
        height: 45px;
        #main-sections-inner {

          &:after {
            content: '';
            display: inline-block;
            margin-left: 100%;
          }

          .main-section-div-wrapper {
            display: inline-block;
            @include transition(background-color, .4s);
            padding: 12px 20px;
            height: 45px;

            &#section-active, &:hover {
              background-color: $corporate_2;

              a {
                color: white;
              }
            }

            a {
              text-transform: uppercase;
              color: $corporate_3;
              @include transition(color, .4s);
            }
          }
        }
      }
    }
  }


  #lang {
        display: inline-block;
        vertical-align: middle;
        width: 45px;
        height: 45px;
        a {
            display: block;
            background-color: rgba($corporate_1,.85);
            color: white;
            box-sizing: content-box;
            max-height: 0;
            max-width: 50px;
            overflow: hidden;
            float: left;
            width: 30px;
            font-family: $text_family;
            border-bottom: 1px solid white;
            border-bottom-width: 0;
            padding: 0 12px;
            @include transition(all, .6s);
            &.selected {
                background-color: rgba($corporate_2,.85);
                max-height: 45px;
                padding: 12px;
            }
            &:hover {
                background-color: rgba($corporate_1,.5);;
            }
        }
        &:hover {
            a {
                max-height: 45px;
                padding: 11px;
                border-bottom-width: 1px;
            }
        }
    }

}