.banner_gallery_wrapper {
    margin-bottom: 100px;
    h3 {
      font-family: $title_family;
      font-size: 35px;
      font-weight: 700;
      color: $corporate_1;
      letter-spacing: 1px;
      text-align: center;
      margin: 0 auto 30px;
    }
    .banner_gallery {
        position: relative;
        .banner {
            display: block;
            position: relative;
            width: 100%;
            height: 350px;
            overflow: hidden;
            img {
                @include center_xy;
                max-width: none;
                width: auto;
                height: 100%;
            }
        }
        &:hover {
            .owl-nav {opacity: 1;}
        }
        .owl-nav {
            position: absolute;
            bottom:30px;
            opacity: 0;
            left: 0;
            right: 0;
            @include transition(all,.6s);
            .owl-prev,
            .owl-next {
                position: absolute;
                color: white;
                font-size: 30px;
                background-color: rgba($corporate_1,.8);
                width: 30px;
                height: 30px;
                i.fa {
                    @include center_xy;
                }
                &:hover {
                    background-color: white;
                    color: $corporate_1;
                }
            }
            .owl-prev {
                left: 0;
            }
            .owl-next {
                right: 0;
            }
        }
    }
}