# -*- coding: utf-8 -*-
import copy
import logging
import re
import urllib
from collections import OrderedDict

import os

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.constants.web_configs_names import BONO_GIFT_CUSTOM
from booking_process.constants.advance_configs_names import CONTACT_PHONES, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url, unescape
from paraty_commons_3.data_structures.advanced_dictionaries import LazyDict
from booking_process.utils.data_management.hotel_data import get_host
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.language.language_utils import get_language_code, get_web_dictionary
from utils.general_utils_methods import smart_truncate
from utils.mobile.mobile_utils import user_agent_is_mobile, user_agent_is_tablet
from utils.mobile.sections_mobile import get_params_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.templates.template_utils import buildTemplate
from utils.web.content.builders.contentBuilder import content_by_config_processor
from utils.web.promotionUtils import process_smartData
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

TEMPLATE_NAME = "daguisacorp"
#Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4]+TEMPLATE_NAME[-1:]

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		params = {
			'base_web': base_web
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			script_args = {
				"section_type": section_type
			}

			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True,
				"extra_bottom_script": self.buildTemplate_2("mobile/_script_mobile.html", script_args, False, TEMPLATE_NAME),
			}
			params.update(params_mobile)

		if section_type == "Galeria de Imagenes":
			gallery_pics = self.get_hotel_gallery(language)
			group = OrderedDict()
			for pic in gallery_pics:
				group.setdefault(pic.get('title'), [])
				group[pic.get('title')].append(pic)
				pic_properties = self.getSectionAdvanceProperties(pic, language)
				pic['video'] = pic_properties.get('video')
			params['gallery_filter'] = group

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
			'phone_header': get_config_property_value(CONTACT_PHONES),
			'header_logos': get_pictures_from_section_name("_header_logos", language),
			'footer_columns': get_pictures_from_section_name("_footer_columns", language),
			'hotels': self.get_hotels(language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True,fontawesome5=True),
			'language_selected': get_language_code(language)
		}

		news_section = build_friendly_url(get_web_dictionary(language)['T_noticias'])
		path = get_language_code(language) + "/" + news_section.replace(".html", "")
		result_params_dict['path'] = path

		if user_agent_is_tablet():
			result_params_dict['user_isIpad'] = True

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Mis Reservas Corp': True
		}

		# bono_gift_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		# if bono_gift_custom and (section_name.lower() == bono_gift_custom.get("section", "").lower()):
		# 	result_params_dict['content_access'] = True

		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		result_params_dict['breadcrumbs'] = copy.deepcopy(self.build_bread_road(language, section))
		result_params_dict['breadcrumbs']['section'] = section

		if section_type == u"Habitación Individual":
			result_params_dict['custom_slider_title'] = section.get('subtitle')
			result_params_dict['breadcrumbs']['custom_bread_title'] = section.get('subtitle')

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = ''
		if section:
			section_type = section.get("sectionType")
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if section_type == "Inicio":
			result['home'] = True

		elif section_type == 'Mis Reservas Corp':
			result['bookingsection'] = True

		elif section_type == "Habitaciones":
			result['hotels_section'] = True
			result['hotels_cat'] = self.get_hotels_category(language)
			result['hotels_des'] = self.get_hotels_destiny(language)
			result['hotels_ser'] = self.get_hotels_services(language)

		elif section_type == "Ofertas":
			result['offers'] = self.getOffers(language)

		elif section_type == 'Oferta Individual':
			result['booking_button'] = True
			result['offer_landing'] = True
			mini_dict = self.getSectionAdvanceProperties(section, language)
			result['smartDatasAttributes'] = process_smartData(mini_dict)

		elif section_type == u"Habitación Individual":
			result['content_subtitle'] = None
			mini_dict = self.getSectionAdvanceProperties(section, language)
			mini_dict['sutitle'] = section.get("subtitle")
			mini_dict['content'] = section.get("content")
			mini_dict['namespace'] = section.get("title")
			hotel_pics = get_pictures_from_section_name(section.get("title"), language)
			mini_dict['gallery'] = list(filter(lambda x: x.get('title') not in ['main', 'slider', 'logo', 'logo_black'], hotel_pics))
			mini_dict['main'] = list(filter(lambda x: x.get('title') == 'main', hotel_pics))
			mini_dict['logo'] = list(filter(lambda x: x.get('title') == 'logo', hotel_pics))
			mini_dict['logo_black'] = list(filter(lambda x: x.get('title') == 'logo_black', hotel_pics))

			result['pictures'] = list(filter(lambda x: x.get('title') == 'slider', hotel_pics))
			if not result['pictures']:
				result['pictures'] = hotel_pics

			if mini_dict.get('banner_gallery'):
				mini_dict['banner_gallery_sec'] = get_section_from_section_spanish_name(mini_dict.get('banner_gallery'), language)
				mini_dict['banner_gallery'] = get_pictures_from_section_name(mini_dict.get('banner_gallery'), language)
			language_dict = get_web_dictionary(language)
			mini_dict.update(language_dict)
			result['hotel_card'] = self.buildTemplate_2("_ficha_hotel.html", mini_dict, False, TEMPLATE_NAME)

		elif section_type == u"Localización":
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')

		elif section_type == "Noticias":
			result['news'] = self.getNews(language)

		elif section_type == 'Form Promo Empresa':
			self.buildJobsPromotion(result, language)

			if advance_properties.get('hide_form'):
				result['hide_form'] = True

		elif not section_type:
			news = self.getNews(language)
			news_section = get_sections_from_type(u"Noticias", language)

			if news_section:
				result['custom_slider_pic'] = news_section[0].get("pictures")

			result['entry'] = self.getCurrentNewsItem(news, language)
			if result['entry']:
				language_code = get_language_code(language)
				path_canonical = '/'.join(self.getPathComponents())
				if 'es' in language_code:
					result['entry']['canonical_url'] = '%s/%s' % (get_host(language_code), path_canonical)
				else:
					result['entry']['canonical_url'] = '%s/%s/%s' % (get_host(language_code), language_code, path_canonical)

			result['social'] = self.getSocialDictionary()
			result['news_widget'] = self.getNews(language, 5)

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get('cycle_news'):
			cycle_news = self.getPicturesProperties(language, advance_properties.get('cycle_news'))
			result['cycle_news'] = self.getNewsCustom(cycle_news, language)

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result['minigallery_for_mobile'] = minigallery_images
			mini_dict = {'minigallery': minigallery_images,'num_items': 5,'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		if advance_properties.get('banner_icons'):
			result['banner_icons'] = self.getPicturesProperties(language, advance_properties.get('banner_icons'))
			result['banner_icons_sec'] = get_section_from_section_spanish_name(advance_properties.get('banner_icons'), language)

		if advance_properties.get('banner_hotels_category'):
			result['banner_hotels_category'] = self.getPicturesProperties(language, advance_properties.get('banner_hotels_category'), ['btn_text','filter'])
			for x in result['banner_hotels_category']:
				if x.get("filter"):
					x['filter'] = normalizeForClassName(x["filter"])
					result['banner_hotels_category_filter'] = True

				if x.get("gallery"):
					x['images'] = get_pictures_from_section_name(x['gallery'], language)

		if advance_properties.get('banner_grid'):
			result['banner_grid'] = self.getPicturesProperties(language, advance_properties.get('banner_grid'))
			result['banner_grid_sec'] = get_section_from_section_spanish_name(advance_properties.get('banner_grid'), language)

		"""
		if advance_properties.get('banner_grid'):
			result['banner_grid'] = get_pictures_from_section_name(advance_properties.get('banner_grid'), language)
			result['banner_grid_sec'] = get_section_from_section_spanish_name(advance_properties.get('banner_grid'), language)
		"""

		if advance_properties.get("banner_offers"):
			result['offers'] = get_pictures_from_section_name(advance_properties['banner_offers'], language)
			result['banner_offers'] = True
			for banner in result['offers']:
				banner['name'] = banner.get("title", "")
				banner['picture'] = banner.get("servingUrl", "")

		if advance_properties.get("form_contact_cv"):
			result['form_contact_cv'] = unescape(advance_properties.get("form_contact_cv"))

		if not advance_properties.get("hide_banner_map"):
			result['banner_map'] = get_section_from_section_spanish_name("_banner_map", language)
			map_pics = self.getPicturesProperties(language, "_banner_map", ['top','left','right'])
			result['banner_map_hoteles'] = list(filter(lambda x: x.get('title') != 'background', map_pics))
			result['banner_map_back'] = list(filter(lambda x: x.get('title') == 'background', map_pics))
			hotels = self.get_hotels(language)
			for hotel_map in result['banner_map_hoteles']:
				for hotel in hotels:
					if hotel_map.get('title') == hotel.get('id'):
						hotel_map.update(hotel)

		bono_gift_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		if bono_gift_custom and section:
			if bono_gift_custom.get("section") == section.get('sectionName'):
				section_template = 'banners/bono_gift_custom.html'
				bono_params = self.get_bono_params(section, language)
				bono_params.update(get_web_dictionary(language))
				result['bono_gift_html'] = self.buildTemplate_2(section_template, bono_params, False, TEMPLATE_NAME)

		return result

	def getRooms(self, language):
		rooms = get_pictures_from_section_name("_habitaciones_blocks", language)

		return rooms

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)

		return offers

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_initializer(self):
		return True

	def get_revolution_full_screen(self):
		return "on"

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"
		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.get_hotels(language)
		mini_dict['hotels_grouped'] = self.get_hotels_category(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, TEMPLATE_NAME)
		if user_agent_is_mobile():
			options['hotels_list_mobile'] = self.get_hotels(language)

		# To prevent options from apppearing in the booking widget
		options['selectOptions'] = None

		# URL associated to the corporate page
		options['bookingUrl'] = "https://daguisa-corpo-dot-daguisa-hotels.appspot.com/booking0"

		# List of hotels in which we want to perform the search
		options['applicationIds'] = "daguisa-goldenfenix;daguisah-goldenfenix;daguisa-tulip;daguisa-euroski;daguisa-canillo;daguisa-pasdelacasa"

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.get_hotels(language)
		mini_dict['hotels_grouped'] = self.get_hotels_category(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, TEMPLATE_NAME)
		return options

	def getParamsForSection(self, section, language):
		result = {}
		section_type = ''
		if section:
			section_type = section.get('sectionType')

		if section_type == 'Mis Reservas Corp':
			widget_hotels = self.get_hotels(language)
			for x in widget_hotels:
				split_url = x.get("url_booking","").split('/')
				only_host = split_url[0]
				x['domain'] = "https://" + x.get("id") + only_host
			result['selectOptions'] = widget_hotels
			result['modify_reservation'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation_content'] = True

		if section_type == 'Normal':
			result['use_h1'] = True

		if section_type == 'Oferta Individual':
			advance_properties = self.getSectionAdvanceProperties(section, language)
			result['smartDatasAttributes'] = process_smartData(advance_properties)
			super(TemplateHandler, self).getParamsForSection(section, language)

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = ''
		if section:
			section_type = section['sectionType']
		language_dict = get_web_dictionary(language)

		if section_type == "Habitaciones":
			mini_dict = {
				'hotels_cat': self.get_hotels_category(language),
				'hotels_des': self.get_hotels_destiny(language),
				'hotels_ser': self.get_hotels_services(language),
				'hotels': self.get_hotels(language),
				'is_mobile': True
			}
			mini_dict.update(language_dict)
			return self.buildTemplate_2('_hotels.html', mini_dict, False, TEMPLATE_NAME)

		elif section_type == "Ofertas":
			params = {
				'elements': self.getOffers(language)
			}
			params.update(language_dict)
			return self.buildTemplate_2("mobile/_promotions.html", params, False, TEMPLATE_NAME)


		elif section_type == "Noticias":
			params = {
				'news': self.getNews(language)
			}
			params.update(language_dict)
			return self.buildTemplate_2("mobile_templates/2/_news.html", params, False, TEMPLATE_NAME)

		elif section_type == 'Form Promo Empresa':
			result = {
				'base_web': base_web,
				'actual_section': section,
			}
			advance_properties = self.getSectionAdvanceProperties(section, language)
			self.buildJobsPromotion(result, language)

			if advance_properties.get('hide_form'):
				result['hide_form'] = True


			result.update(language_dict)

			return self.buildTemplate_2("mobile/_job_promotions.html", result, False, TEMPLATE_NAME)

		elif section_type == u"Habitación Individual":
			mini_dict = self.getSectionAdvanceProperties(section, language)
			mini_dict['sutitle'] = section.get("subtitle")
			mini_dict['content'] = section.get("content")
			mini_dict['namespace'] = section.get("title")
			mini_dict['is_mobile'] = True
			hotel_pics = get_pictures_from_section_name(section.get("title"), language)
			mini_dict['gallery'] = list(filter(lambda x: x.get('title') not in ['main', 'logo', 'logo_black'], hotel_pics))
			mini_dict['main'] = list(filter(lambda x: x.get('title') == 'main', hotel_pics))
			mini_dict['logo'] = list(filter(lambda x: x.get('title') == 'logo', hotel_pics))
			mini_dict['logo_black'] = list(filter(lambda x: x.get('title') == 'logo_black', hotel_pics))
			if mini_dict.get('banner_gallery'):
				mini_dict['banner_gallery_sec'] = get_section_from_section_spanish_name(mini_dict.get('banner_gallery'), language)
				mini_dict['banner_gallery'] = get_pictures_from_section_name(mini_dict.get('banner_gallery'), language)
			mini_dict.update(language_dict)
			return self.buildTemplate_2("_ficha_hotel.html", mini_dict, False, TEMPLATE_NAME)

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		extra_banners['language'] = get_language_code(language)
		extra_banners['hotels'] = self.get_hotels(language)
		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		if extra_banners.get("minigallery_for_mobile"):
			args = {
				'minigallery_mobile': extra_banners["minigallery_for_mobile"]
			}
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

		#result += "<script async type='text/javascript' src='js/" + base_web + "/functions_mobile.js?v=1.12'></script>"
		result += "<script async type='text/javascript' src='/static_1/js/select2/select2.min.js'></script>"
		result += "</div>"

		return result

	def get_hotels(self, language):
		hotel_list = get_sections_from_type(u"Habitación Individual", language)
		returnhotels = []
		for hotel_element in hotel_list:
			hotel_element['value'] = copy.deepcopy(hotel_element.get('subtitle'))
			hotel_properties = self.getSectionAdvanceProperties(hotel_element, language)
			hotel_element.update(hotel_properties)
			hotel_element['cat'] = normalizeForClassName(hotel_element.get("category",''))
			hotel_element['id'] = hotel_element.get('title')
			hotel_element['namespace'] = hotel_element.get('title')
			hotel_element['class_place'] = normalizeForClassName(hotel_element.get('place'))
			service_list = hotel_element.get('services','').split(";")
			all_services = get_pictures_from_section_name("_services_icons", language)
			hotel_element['service_list'] = []
			for service in all_services:
				if service.get('title') in service_list:
					hotel_element['service_list'].append(service)
			hotel_element['order'] = int(hotel_element.get('order')) if hotel_element.get('order') else 0
			hotel_element['price_label'] = unescape(hotel_element.get('price_label', ''))
			hotel_pics = get_pictures_from_section_name(hotel_element.get("title"), language)
			hotel_element['gallery'] = list(filter(lambda x: x.get('title') not in ['main', 'logo', 'logo_black'], hotel_pics))
			hotel_element['main'] = list(filter(lambda x: x.get('title') == 'main', hotel_pics))
			hotel_element['logo'] = list(filter(lambda x: x.get('title') == 'logo', hotel_pics))
			hotel_element['logo_black'] = list(filter(lambda x: x.get('title') == 'logo_black', hotel_pics))
			if not hotel_element['logo_black']:
				hotel_element['logo_black'] = hotel_element['logo']

			returnhotels.append(hotel_element)

		returnhotels.sort(key=lambda k: int(k['order']))

		return returnhotels

	def get_hotels_category(self, language):
		hotel_list = self.get_hotels(language)
		group = OrderedDict()
		for hotel in hotel_list:
			group.setdefault(hotel.get('category'), {"destinos": OrderedDict()})
			group[hotel.get('category')]["class_name"] = normalizeForClassName(hotel.get('category'))
			group[hotel.get('category')]["destinos"].setdefault(hotel.get('place'), [])
			group[hotel.get('category')]["destinos"][hotel.get('place')].append(hotel)
			if group[hotel.get('category')].get("n_hoteles", False):
				group[hotel.get('category')]["n_hoteles"] = group[hotel.get('category')]["n_hoteles"] + 1
			else:
				group[hotel.get('category')]["n_hoteles"] = len(
					group[hotel.get('category')]["destinos"][hotel.get('place')])

		return group

	def get_hotels_destiny(self, language):
		hotel_list = self.get_hotels(language)
		group = OrderedDict()
		for hotel in hotel_list:
			group.setdefault(hotel.get('place'), {"destinos": OrderedDict()})
			group[hotel.get('place')]["class_name"] = normalizeForClassName(hotel.get('place'))
			group[hotel.get('place')]["destinos"].setdefault("hotels", [])
			group[hotel.get('place')]["destinos"]["hotels"].append(hotel)
			if group[hotel.get('place')].get("n_hoteles", False):
				group[hotel.get('place')]["n_hoteles"] = group[hotel.get('place')]["n_hoteles"] + 1
			else:
				group[hotel.get('place')]["n_hoteles"] = len(group[hotel.get('place')]["destinos"]["hotels"])

		return group

	def get_hotels_services(self, language):
		hotel_list = self.get_hotels(language)
		group = []
		services = get_pictures_from_section_name("_services_icons", language)
		availables_services = []
		for hotel in hotel_list:
			hotel_services = hotel.get('services', '').split(";")
			availables_services.extend(hotel_services)
		for service in services:
			if service.get('title') in availables_services:
				group.append(service)

		return group

	def getNews(self, language, limit=None):
		news = self.buildNewsInfo(language)
		for item in news:
			item['shortDesc'] = smart_truncate(item.get('description', None), length=250)
			item['comments'] = []
			for pic in item.get('pictures'):
				if pic.get("enabled"):
					comment = pic
					if not comment.get('servingUrl'):
						comment['pic'] = 'https://lh3.googleusercontent.com/PxhCbuVTWji6swvvbH5lEytdDHGaiCJ8qavBeBEW5TnR1G-DVbrhX6KHP24BMVDpNSC4FLKqlb-SWF2juCXrPmR86gXAKQ'
						item['comments'].append(comment)
			item['author_pic'] = 'https://lh3.googleusercontent.com/PxhCbuVTWji6swvvbH5lEytdDHGaiCJ8qavBeBEW5TnR1G-DVbrhX6KHP24BMVDpNSC4FLKqlb-SWF2juCXrPmR86gXAKQ'
		if limit:
			return news[:limit]
		else:
			return news

	def getNewsCustom(self, cycle_news, language):
		custom_news_ordered = OrderedDict()
		if cycle_news:
			custom_news_ordered = sorted(cycle_news, key=lambda l: l.get("date"), reverse=True)
			return custom_news_ordered

	def renderNewsPage(self, language, country, template_path="secciones/newsPage.html"):
		if user_agent_is_mobile():
			news = self.buildNewsInfo(language)
			newsItemToUse = self.getCurrentNewsItem(news, language)
			engine = None
			if newsItemToUse:
				sections = self.getSections(language)
				params = self.getCommonParams(language, sections)

				newsItemToUse['subtitle'] = newsItemToUse.get("name")
				newsItemToUse['content'] = newsItemToUse.get("description")
				new_dict = {
					"news": newsItemToUse
				}
				content_to_render = self.buildTemplate_2("mobile_templates/2/_news.html", new_dict, False)
				params['content'] = content_to_render
				params['namespace'] = "daguisa-corpo" if DEV else get_namespace()
				params = LazyDict(dict(list(params.items()) + list(self.getAdditionalParams('', language, sections).items())))
				fullPath = os.path.join(os.path.dirname(__file__),'../../templates/mobile_templates/2/inner_section.html')
				engine = buildTemplate(fullPath, params)
			return engine
		return super(TemplateHandler, self).renderNewsPage(language, country, template_path)

	def getSectionAdvanceProperties(self, sectionToUse, language):
		if not sectionToUse:
			return {}

		return get_properties_for_entity(sectionToUse.get('key', False), language)

	def _get_all_jobs_types(self, result_params_dict):
		available_types = []
		for job_element in result_params_dict['job_promotions']:
			if job_element.get('type'):
				available_types.append(job_element['type'])

		result_params_dict['jobs_types'] = set(available_types)

	def buildJobsPromotion(self, result, language):
		result['work_form'] = True
		result['job_promotions'] = get_pictures_from_section_name('_work_blocks', language)
		result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
		# destiny_hotels = _get_destiny_and_hotels(language, None, True, True)
		for job_element in result['job_promotions']:

			try:
				job_title_url = re.sub("(<strong>.*<\/strong>)", '', job_element.get('title', ''))
				job_element['title_url'] = urllib.quote(job_title_url, safe='')
			except Exception as e:
				logging.info("Cant generate url for job offer: %s" % e)

			advance_properties_job = self.getSectionAdvanceProperties(job_element, language)
			if advance_properties_job.get('type'):
				job_element['type'] = advance_properties_job['type']
				self._get_all_jobs_types(result)

			if advance_properties_job.get('popup_info'):
				job_element['popup_info'] = unescape(advance_properties_job['popup_info'])

			if job_element.get("title", ""):
				job_element['job_classname'] = normalizeForClassName(unescape(job_element.get("title", "")))

			if advance_properties_job.get('hotels'):
				job_element['hotels'] = advance_properties_job['hotels']
				hotels_available = advance_properties_job['hotels'].split(";")
				job_element['destiny_info'] = []
				job_element['hotels_available'] = []

				# for destiny_element in destiny_hotels:
				# 	for hotel_element in destiny_element['hotels_sections']:
				# 		if hotel_element.get('hotel_namespace') in hotels_available:
				# 			if not job_element['destiny_info']:  # To add destiny_element only once
				# 				job_element['destiny_info'].append(destiny_element)
				#
				# 			job_element['hotels_available'].append(hotel_element.get('title'))
				#
				# _get_destinies_of_jobs(result)

	def get_bono_params(self, section, language):
		bono_params = {
			'language': language
		}
		default_section_template = 'secciones/bono_gift_custom.html'
		processed_content = content_by_config_processor(section, bono_params, default_section_template, language)
		bono_params = processed_content.additional_params
		default_section_template = processed_content.section_template
		if user_agent_is_mobile():
			bono_params, default_section_template, mobile_allow = get_params_mobile(self, bono_params, section,
																					default_section_template, language)
		return dict(list(bono_params.items()) + list(section.items()))