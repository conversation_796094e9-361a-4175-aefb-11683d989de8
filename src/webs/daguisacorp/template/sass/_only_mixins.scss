@mixin transition($prop: all, $time: 1s) {
    -webkit-transition: $prop $time;
    -moz-transition: $prop $time;
    -ms-transition: $prop $time;
    -o-transition: $prop $time;
    transition: $prop $time;
}

@mixin translate($x: 0, $y: 0) {
    -webkit-transform: translate($x, $y);
    -moz-transform: translate($x, $y);
    -ms-transform: translate($x, $y);
    -o-transform: translate($x, $y);
    transform: translate($x, $y);
}

@mixin transform($transform) {
    -webkit-transform: $transform;
    -moz-transform: $transform;
    -ms-transform: $transform;
    -o-transform: $transform;
    transform: $transform;
}

@mixin center_xy {
    position: absolute;
    top: 50%;
    left: 50%;
    @include translate(-50%, -50%);
}

@mixin center_x {
    position: absolute;
    left: 50%;
    @include translate(-50%, 0%);
}

@mixin center_y {
    position: absolute;
    top: 50%;
    @include translate(0%, -50%);
}

@mixin center_image {
    @include center_xy;
    min-width: 100%;
    min-height: 100%;
    max-width: none;
}

@mixin cover_image {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

@mixin contain_image {
    height: 100%;
    width: 100%;
    object-fit: contain;
}

@mixin ellipsis($lines) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $lines; /* -> number of lines to show, NO height required in the container <- */
    -webkit-box-orient: vertical;
    
    hr {
        display: none;
    }
}

@mixin d_grid($number_of_cols, $auto_height_rows, $gap: 20px) {
    display: grid;
    grid-template-columns: repeat($number_of_cols, 1fr);
    grid-auto-rows: $auto_height_rows;
    grid-gap: $gap;
    justify-content: center;
}

@mixin overlay($color: #000000, $opacity: .5) {
    position: relative;
    
    &::before {
        @include full_size;
        content: '';
        background-color: $color;
        opacity: $opacity;
        z-index: 0;
    }
}

@mixin flex_xy {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin full_size {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

@mixin icon-xcross {
    &:before, &:after {
        content: '';
        width: 100%;
        height: 2px;
        background: white;
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%) rotate(45deg);
        -moz-transform: translate(-50%, -50%) rotate(45deg);
        -ms-transform: translate(-50%, -50%) rotate(45deg);
        -o-transform: translate(-50%, -50%) rotate(45deg);
        transform: translate(-50%, -50%) rotate(45deg);
    }
    &:after {
        -webkit-transform: translate(-50%, -50%) rotate(-45deg);
        -moz-transform: translate(-50%, -50%) rotate(-45deg);
        -ms-transform: translate(-50%, -50%) rotate(-45deg);
        -o-transform: translate(-50%, -50%) rotate(-45deg);
        transform: translate(-50%, -50%) rotate(-45deg);
    }
}

@mixin display_flex($wrap: wrap) {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: $wrap;
    -moz-flex-wrap: $wrap;
    -ms-flex-wrap: $wrap;
    -o-flex-wrap: $wrap;
    flex-wrap: $wrap;
}