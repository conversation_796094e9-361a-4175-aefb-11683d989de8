//Base web (change too in config.rb)
$base_web: "dagup";
@import url('https://fonts.googleapis.com/css?family=Quicksand:300,500,700&display=swap');
@font-face {
    font-family: 'DIN alternate';
    src: url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.eot');
    src: url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.woff2') format('woff2'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.woff') format('woff'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.ttf') format('truetype'),
         url('/static_1/fonts/DINalternate/din_alternate_regular-webfont.svg#dinregularalternate') format('svg');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN alternate';
    src: url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.eot');
    src: url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.woff2') format('woff2'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.woff') format('woff'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.ttf') format('truetype'),
         url('/static_1/fonts/DINalternate/din_alternate_bold-webfont.svg#dinboldalternate') format('svg');
    font-weight: bold;
    font-style: normal;
}
// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #222222;
$black: #222222;
$corporate_2: #f7bb1e;
$corporate_3: #898989;

$title_family: "DIN alternate", sans-serif;
$text_family: "Quicksand", sans-serif;

// colors for booking widget
$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

@mixin title_styles() {
  font-family: $title_family;
  font-size: 45px;
  line-height: 47px;
  color: $corporate_1;
  font-weight: 700;
  i {
    position: relative;
    font-size: 60px;
    margin-right: 40px;
    &:after {
      content: '';
      position: absolute;
      top: -30px;
      left: -30px;
      z-index: -1;
      display: block;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: $corporate_2;
      opacity: .4;
    }
  }
}

@mixin text_styles() {
  font-weight: 400;
  font-family: $text_family;
  font-size: 19px;
  line-height: 25px;
  color: $black;
}

@mixin btn_styles() {

}

