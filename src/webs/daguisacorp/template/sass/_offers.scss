.offers_filters {
  width: 1110px;
  margin: auto;

  .filter_wrapper {
    display: inline-block;
    vertical-align: top;
    border: 1px solid $black;
    margin-right: 20px;
    position: relative;
    font-family: "DIN Alternate", sans-serif;
    .select2 {
      width: 270px !important;
      .selection {
        .select2-selection {
          background: transparent;
          border-width: 0;
          height: auto;
          .select2-selection__rendered {
            text-align: center;
            padding: 7px 10px 7px 40px;
            text-transform: uppercase;
            font-size: 18px;
            color: white;
          }
          .select2-selection__arrow {
            display: none;
          }
        }
      }
    }
    a {
      display: block;
      padding: 10px 10px 10px 40px;
      color: $black;
      text-transform: uppercase;
      font-size: 18px;
      width: 270px;
      text-align: center;
    }
    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: transparent;
      border-radius: 0;
      border-width: 0;
      padding: 10px;
      padding-left: 80px;
      text-align: center;
      width: 250px;
      text-transform: uppercase;
      font-size: 20px;
    }
    &:before {
      position: absolute;
      top: 15px;
      @extend .fal;
      left: 15px;
    }
    &.cat_filter {
      @extend .fa-filter;
      background: $black;
      color: white;
    }
    &.off_filter {
      @extend .fa-badge-percent;
    }
    &.paq_filter {
      @extend .fa-gift;
    }
    &.active {
      background: #efefef;
    }
  }
  .result_search {
    float: right;
    text-align: right;
    font-size: 20px;
    padding-top: 20px;
  }
}
.offers_wrapper {
  text-align: center;
  padding: 30px 0;
  width: 1110px;
  margin: auto;

  .offer {
    display: inline-block;
    vertical-align: top;
    width: calc((100% / 3) - (40px / 3));
    height: 750px;
    margin: 0 15px 30px;
    padding-bottom: 160px;
    position: relative;
    background: #f5f5f5;

    &:nth-of-type(1), &:nth-of-type(3), &:nth-of-type(4), &:nth-of-type(6), &:nth-of-type(7){
      margin: 0;
      margin-bottom: 30px;
    }

    &:before {
      content: '';
      @include full_size;
      z-index: -10;
      border: 1px solid rgba($black, .15);
    }
    .offer_pic {
      position: relative;
      height: 260px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .offer_title {
      position: relative;
      padding: 30px 30px 0;
      font-size: 20px;
      letter-spacing: 1.6px;
      line-height: 25px;
      font-weight: bold;
      height: 90px;
      color: $corporate_1;
      text-transform: uppercase;
      text-align: left;
      box-sizing: border-box;
    }

    .offer_desc {
      padding: 20px 30px 0;
      text-align: left;
      font-weight: 300;
      font-size: 15px;
      letter-spacing: 1px;
      max-height: 115px;
      height: 115px;
      overflow: hidden;
      margin-bottom: 5px;
      box-sizing: border-box;
      position: relative;
    }
    .offer_hotels {
      padding: 0 30px;
      text-align: left;
      margin-bottom: 20px;

      .hotel_logo {
        display: inline-block;
        height: 50px;
        width: calc((100% - 40px) / 3 - 15px); // 15px - inline-block fix
        margin-bottom: 20px;
        margin-left: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .hotel_logo:nth-child(3n + 1) {
        margin-left: 0;
      }

      /*img {
        max-height: 50px;
        vertical-align: middle;
        padding: 0 20px;
        padding-left: 0;
      }*/
    }
    .links_wrapper {
      position: absolute;
      bottom: 0;
      .button_promotion, .link {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        float: left;
        font-family: "DIN Alternate", sans-serif;
        color: white;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 27px;
        font-weight: 500;
        padding: 15px 0;
        overflow: hidden;
        margin: 0 20px 10px;
        box-sizing: border-box;
        width: calc(100% - 40px);
        z-index: 1;

        &:after {
          content: '';
          @include full_size;
          z-index: -2;
          background: $corporate_2;
        }
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 200%;
          height: 0;
          z-index: -1;
          background: rgba(0,0,0,0.3);
          -webkit-transform: translate(-50%,-50%) rotate(45deg);
          -moz-transform: translate(-50%,-50%) rotate(45deg);
          -ms-transform: translate(-50%,-50%) rotate(45deg);
          -o-transform: translate(-50%,-50%) rotate(45deg);
          transform: translate(-50%,-50%) rotate(45deg);
          @include transition(all,.6s);
        }
        &:hover {
          color: white;
          &:before {
            height: 500px;
            -webkit-transform: translate(-50%,-50%) rotate(30deg);
            -moz-transform: translate(-50%,-50%) rotate(30deg);
            -ms-transform: translate(-50%,-50%) rotate(30deg);
            -o-transform: translate(-50%,-50%) rotate(30deg);
            transform: translate(-50%,-50%) rotate(30deg);
          }
        }

        &.link.see_more {
          color: $corporate_1;
          border: 2px solid $corporate_1;

          &:after {
            background: transparent;
          }
        }
      }
    }

  }
}

.description_popup_wrapper {
  display: none;

  .offer_title {
    font-size: 20px;
    letter-spacing: 1.6px;
    line-height: 25px;
    font-weight: bold;
    color: #222222;
    text-transform: uppercase;
    text-align: left;
  }

  .offer_desc {
    padding: 20px 0 0;
    text-align: left;
    font-weight: 300;
    font-size: 15px;
    overflow: hidden;
    margin-bottom: 20px;
    box-sizing: border-box;
  }
}