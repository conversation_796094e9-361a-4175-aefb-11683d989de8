.contact_form_wrapper {
  padding: 0 0 100px;
  overflow: hidden;

  h3 {
    @include title_styles;
    text-align: center;
    padding-bottom: 50px;
  }

  #contact {
    display: table;
    margin: auto;
    .info {
      display: table;
      position: relative;
    }
    .contInput {
      display: inline-block;
      float: left;
      padding: 10px 0 10px 20px;
      position: relative;

      &:nth-of-type(-n+3) {
        width: calc((100% - 5px) / 3);
        padding-top: 20px;
      }

      &:nth-of-type(4) {
        width: 100%;
      }

      &:nth-of-type(3), &:nth-of-type(5) {
        margin-right: 0;
      }

      input:not([type="checkbox"]), textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 14px;
        width: 100%;
        margin-bottom: 20px;
        border:1px solid $black;
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 10px 0;
    }

    a.myFancyPopup {
      display: inline-flex;
      color: $black;
      width: 97%;
      margin-left: 5px;
      margin-top: 5px;
    }

    #contact-button {
      position: relative;
      right: 0;
      bottom: 0;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border-width: 0;
      background: transparent;
      display: inline-block;
      vertical-align: middle;
      width: 270px;
      float: right;
      font-family: "DIN Alternate", sans-serif;
      color: white;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-size: 28px;
      font-weight: 500;
      padding: 15px 0;
      overflow: hidden;
      &:after {
        content: '';
        @include full_size;
        z-index: -2;
        background: $gray-2;
      }
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200%;
        height: 0;
        z-index: -1;
        background: rgba(0,0,0,0.3);
        -webkit-transform: translate(-50%,-50%) rotate(45deg);
        -moz-transform: translate(-50%,-50%) rotate(45deg);
        -ms-transform: translate(-50%,-50%) rotate(45deg);
        -o-transform: translate(-50%,-50%) rotate(45deg);
        transform: translate(-50%,-50%) rotate(45deg);
        @include transition(all,.6s);
      }
      &:hover {
        color: white;
        &:before {
          height: 500px;
          -webkit-transform: translate(-50%,-50%) rotate(30deg);
          -moz-transform: translate(-50%,-50%) rotate(30deg);
          -ms-transform: translate(-50%,-50%) rotate(30deg);
          -o-transform: translate(-50%,-50%) rotate(30deg);
          transform: translate(-50%,-50%) rotate(30deg);
        }
      }
    }
  }

  &.cv {
    padding: 0 0 100px;
    .info .contInput {
      #comments {
        height: 120px !important;
      }
    }
    #contact {
      .contInput.area {
        width: 50%;
      }
      .policy-terms {
        right: 50px;
        #accept-term {
          vertical-align: bottom;
        }
        a {
          color: black;
        }
      }
    }
  }
}