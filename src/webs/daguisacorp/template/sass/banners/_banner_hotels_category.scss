.banner_hotels_category_wrapper {
  .banner {
    position: relative;
    display: table;
    width: 100%;
    margin-bottom: 10px;
    &:hover .banner_slider .hotel {
      .hotel_pic {
        img {
          opacity: .6;
        }
      }
      .center_y {
        opacity: 1;
      }
    }
    &:nth-child(even) {
      .banner_content {
        margin-left: auto;
        margin-right: 0;
        text-align: right;
        background: linear-gradient(-90deg, #efefef 55%, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0));
        .banner_text {
          &.banner_hotels_category_filter {
            left: auto;
            right: 100px;
          }
        }


        &:before, &:after {
          right: auto;
          -webkit-transform: translate(0, -50%) rotate(25deg);
          -moz-transform: translate(0, -50%) rotate(25deg);
          -ms-transform: translate(0, -50%) rotate(25deg);
          -o-transform: translate(0, -50%) rotate(25deg);
          transform: translate(0, -50%) rotate(25deg);
        }
        &:before {
          left: 20%;
          border-left: 1px solid #efefef;
          border-right: 4px solid #efefef;
        }
        &:after {
          left: calc(20% + 60px);
          border-left: 6px solid #efefef;
          border-right: 840px solid #efefef;
        }
        i {
          &:after {
            left: auto;
            right: -30px;
          }
        }
        .title, .desc {
          margin-right: 0;
          margin-left: auto;
        }
      }
      .banner_slider {
        .hotel {
          .hotel_pic {
            img {
              right: auto;
              left: 0;
            }
          }
          .center_y {
            left: 0;
            right: auto;
          }
        }
        .owl-nav {
          right: auto;
          left: 0;
        }
      }
    }
    .banner_content {
      width: 80%;
      height: 550px;
      padding: 70px 100px 100px;
      position: relative;
      z-index: 10;
      overflow: hidden;
      background: linear-gradient(90deg, #efefef 55%, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0));
      .banner_text {
        &.banner_hotels_category_filter {
          @include center_y;
          right: auto;
          left: 100px;
          margin-top: -35px;
        }
      }
      &:before, &:after {
        content: '';
        position: absolute;
        z-index: -2;
        top: 50%;
        right: 20%;
        -webkit-transform: translate(0, -50%) rotate(-25deg);
        -moz-transform: translate(0, -50%) rotate(-25deg);
        -ms-transform: translate(0, -50%) rotate(-25deg);
        -o-transform: translate(0, -50%) rotate(-25deg);
        transform: translate(0, -50%) rotate(-25deg);
        border-left: 4px solid #efefef;
        border-right: 1px solid #efefef;
        width: 4px;
        height: 200%;
      }
      &:after {
        right: calc(20% + 60px);
        width: 6px;
        border-left: 840px solid #efefef;
        border-right: 6px solid #efefef;
      }
      i {
        position: relative;
        font-size: 50px;
        margin-bottom: 20px;
        &:before {
          position: relative;
          z-index: 2;
        }
        &:after {
          content: '';
          position: absolute;
          top: -30px;
          left: -30px;
          z-index: 1;
          display: block;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: $corporate_2;
          opacity: .4;
        }
      }
      .title {
        font-family: "DIN Alternate", sans-serif;
        font-size: 48px;
        font-weight: bold;
        width: 390px;
      }
      .desc, .experience_desc {
        font-family: "Quicksand", sans-serif;
        font-size: 16px;
        font-weight: 400;
        color: #555555;
        width: 570px;
      }
    }
    .banner_slider {
      @include full_size;
      overflow: hidden;
      .hotel {
        .hotel_pic {
          width: 100%;
          height: 550px;
          overflow: hidden;
          background: black;
          position: relative;
          img {
            @include center_y;
            right: 0;
            width: 85%;
            opacity: 1;
            @include transition(opacity, .6s);
          }
        }
        .center_y {
          left: auto;
          right: 0;
          width: 50%;
          text-align: center;
          opacity: 0;
          margin-bottom: -35px;
          @include transition(opacity, .6s);
          &.banner_button {
            text-align: initial;
          }
          img {
            width: auto;
            margin: 0 auto 20px;
          }
          a.link {
            display: inline-block;
            vertical-align: middle;
            margin: 0 10px;
            color: white;
            &.banner_button {
              margin-left: 20%;
            }
            &:hover {
              i {
                background: $corporate_2;
                &:after {
                  background: $corporate_2;
                }
              }
            }
            i {
              display: inline-block;
              vertical-align: middle;
              width: 60px;
              height: 60px;
              border-radius: 50%;
              background: rgba(white, .8);
              color: $black;
              font-size: 24px;
              position: relative;
              margin-right: 30px;
              &:before {
                @include center_xy;
              }
              &:after {
                content: '';
                @include center_y;
                left: 100%;
                height: 1px;
                width: 20px;
                background: rgba(white, .8);
              }
            }
            span {
              display: inline-block;
              vertical-align: middle;
              font-family: "DIN Alternate", sans-serif;
              font-size: 16px;
              font-weight: 500;
              letter-spacing: 1px;
              line-height: 20px;
              text-transform: uppercase;
            }
          }
          .button {
            display: table;
            margin: 30px auto;
            text-shadow: 0 0 0 transparent !important;
            a {
              display: inline-block;
              vertical-align: middle;
              width: 270px;
              float: left;
              font-family: "DIN Alternate", sans-serif;
              color: white;
              text-transform: uppercase;
              letter-spacing: 1px;
              font-size: 28px;
              font-weight: 500;
              padding: 15px 0;
              overflow: hidden;
              position: relative;
              background-color: $gray-2;
              &:before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 200%;
                height: 0;
                background: rgba(0, 0, 0, 0.3);
                -webkit-transform: translate(-50%, -50%) rotate(30deg);
                -moz-transform: translate(-50%, -50%) rotate(30deg);
                -ms-transform: translate(-50%, -50%) rotate(30deg);
                -o-transform: translate(-50%, -50%) rotate(30deg);
                transform: translate(-50%, -50%) rotate(30deg);
                @include transition(all, .6s);
              }
              &:hover {
                color: $gray-2;
                &:before {
                  background: white;
                  height: 200px;
                }
              }

              &.button_promotion {
                background-color: $corporate_2;
                &:hover {
                  color: $corporate_2;
                }
                &:before {
                  background: white;
                }
              }
              span {
                position: relative;
              }
            }
          }
        }
      }

      .owl-nav {
        position: absolute;
        top: 10px;
        right: 0;
        z-index: 25;
        .owl-next, .owl-prev {
          color: white;
          display: inline-block;
          vertical-align: middle;
          padding: 0 10px;
          font-size: 40px;
          &.disabled {
            opacity: .5;
          }
        }
      }
    }

    .hotels_row {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 20;
      text-align: center;
      background: rgba($corporate_2, .8);
      a {
        display: inline-block;
        vertical-align: middle;
        padding: 25px;
        font-size: 16px;
        color: $black;
        border-bottom: 1px solid transparent;
        &.active {
          font-weight: bold;
          border-bottom-color: $black;
        }
      }
    }
    &.experience_banner {
      .banner_content {
        width: 70%;
        .desc.experience_desc {
          width: 570px;
        }
      }
    }
  }
}

@media screen and (min-width: 770px) {
  .banner_hotels_category_wrapper .banner {
    .banner_slider .owl-nav {
      top: 10px;
      right: 20px;
    }
    &.experience_banner {
      .banner_content {
        width: 80%;
      }
    }
  }
}