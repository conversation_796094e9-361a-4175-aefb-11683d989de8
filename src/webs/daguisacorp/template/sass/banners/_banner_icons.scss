.banner_icons_wrapper {
  text-align: center;
  padding: 60px 0 40px;
  .banner_icons_content {
    display: inline-block;
    vertical-align: middle;
    .title {
      text-align: left;
      font-family: "DIN Alternate", sans-serif;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .banner_icons {
      text-align: center;
      .icon {
        display: inline-block;
        vertical-align: middle;
        padding: 20px;
        &:first-of-type {
          padding-left: 0;
        }
        &:last-of-type {
          padding-right: 0;
        }
        i {
          display: inline-block;
          vertical-align: middle;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: $corporate_1;
          color: white;
          font-size: 40px;
          position: relative;
          margin-right: 50px;
          &:before {
            @include center_xy;
          }
          &:after {
            content: '';
            @include center_y;
            left: 100%;
            height: 1px;
            width: 40px;
            background: $corporate_1;
          }
        }
        span {
          display: inline-block;
          vertical-align: middle;
          font-family: "Quicksand", sans-serif;
          font-size: 14px;
          letter-spacing: 1px;
          line-height: 16px;
        }
      }
    }
  }
}


@media (max-width: 1295px) and (min-width: 1000px) {
  div.banner_icons_wrapper {
    .banner_icons_content {
      .banner_icons {
        padding: 0 150px;
      }
    }
  }
}