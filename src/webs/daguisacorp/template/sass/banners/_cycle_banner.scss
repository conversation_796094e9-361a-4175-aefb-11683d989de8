.cycle_banners_wrapper {
  padding: 20px calc((100% - 1140px) / 2);
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }

  .cycle_banner_element {
    @include display_flex;
    width: 100%;
    border: 1px solid rgba(grey, .4);
    margin-bottom: 30px;

    .image_wrapper {
      position: relative;
      display: inline-block;
      width: 470px;
      height: 430px;
      overflow: hidden;

      img {
        @include center_image;
        width: auto;
        max-height: 130%;
      }
    }

    .read_more {
      display: none;
    }
    .cycle_content {
      width: 664px;
      display: inline-block;
      background-color: white;
      padding: 30px;

      .cycle_title {
        border-bottom: 1px solid rgba(grey, .4);
        padding: 0 0 10px;
        margin-bottom: 30px;
        text-align: left;

        .main_title {
          font-family: $title_family;
          font-size: 48px;
          line-height: 1;
          font-weight: bolder;
        }

        .date {
          margin-top: 6px;
          font-size: 20px;
          padding: 15px 0;
          display: inline-block;
          color: $corporate_1;
        }
        .source {
          margin-top: 6px;
          font-size: 20px;
          display: inline-block;
          margin-left: 30px;
          padding: 15px 0;
          color: $corporate_1;
        }
      }

      .cycle_description {
        position: relative;
        font-size: 16px;
        color: #555555;
        line-height: 22px;

        &.exceded {
          max-height: 180px;
          overflow: hidden;
          @include transition(max-height, 0.5s);

          & + a + .read_more, & + .read_more {
            display: inline-block;
            color: $corporate_1;
            text-decoration: none;
            text-decoration: underline;
            margin-top: 20px;
            @include transition(color, .6s);
            font-weight: 700;
            font-size: 15px;
            letter-spacing: 0.6px;
            margin-left: 30px;
            height: 20px;
            vertical-align: top;
            overflow: hidden;
            cursor: pointer;

            &:hover {
              color: $corporate_1;
            }

            .more {
              transition: margin 0.5s;
            }

            &.active {
              .more {
                margin-top: -20px;
              }
            }
          }

          & + .read_more {
            margin-left: 0;
          }

          &.visible {
            max-height: fit-content;
          }
        }

        .link {
          color: $corporate_2;
          display: block;
          text-decoration: underline;
          font-weight: 700;
          font-size: 15px;
          letter-spacing: 0.6px;
        }
      }

      .cycle_link {
        color: $corporate_2;
        text-decoration: none;
        margin-top: 20px;
        @include transition(color, .6s);
        font-weight: 700;
        font-size: 15px;
        letter-spacing: 0.6px;
        display: inline-block;

        &:hover {
          color: $corporate_1;
        }
      }
    }

    &:nth-of-type(even) {
      flex-direction: row-reverse;
    }
  }

  &.individual_cycle {
    .corporate_button1.cycle_link {
      text-transform: uppercase;
      font-size: 22px;
      margin-top: 50px;
    }
  }
}