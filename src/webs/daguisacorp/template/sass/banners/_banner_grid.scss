.banner_grid_wrapper {
  padding: 20px 0;
  display: table;
  width: 100%;
  .banner {
    width: calc((100% - 70px) / 3);
    height: calc((100vw - 70px) / 3);
    position: relative;
    overflow: hidden;
    float: left;
    margin-right: 35px;
    margin-bottom: 25px;
    background: $corporate_1;
    &:first-of-type {
      background: transparent;
      &:before {
        display: none;
      }
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .gid_first {
      @include center_y;
      left: 40px;
      right: 40px;
      .grid_title {
        font-family: "DIN Alternate", sans-serif;
        font-size: 20px;
        margin-bottom: 20px;
        position: relative;
        big {
          font-size: 50px;
          font-weight: 700;
        }
        &:before {
          content: '';
          position: absolute;
          top: -30px;
          left: 50%;
          z-index: -1;
          -webkit-transform: translateX(-50%);
          -moz-transform: translateX(-50%);
          -ms-transform: translateX(-50%);
          -o-transform: translateX(-50%);
          transform: translateX(-50%);
          display: block;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: $corporate_2;
          opacity: .4;
        }
      }
      .btn {
        position: relative;
        display: block;
        margin: 30px 0;
        width: 150px;
        padding: 15px 0;
        text-align: center;
        text-transform: uppercase;
        color: $corporate_1;
        font-size: 18px;
        border: 1px solid $corporate_1;
        overflow: hidden;
        @include transition(color,.6s);
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 200%;
          height: 0;
          z-index: -1;
          background: $corporate_1;
          -webkit-transform: translate(-50%,-50%) rotate(45deg);
          -moz-transform: translate(-50%,-50%) rotate(45deg);
          -ms-transform: translate(-50%,-50%) rotate(45deg);
          -o-transform: translate(-50%,-50%) rotate(45deg);
          transform: translate(-50%,-50%) rotate(45deg);
          @include transition(all,.6s);
        }
        &:hover {
          color: white;
          &:before {
            height: 200px;
            -webkit-transform: translate(-50%,-50%) rotate(30deg);
            -moz-transform: translate(-50%,-50%) rotate(30deg);
            -ms-transform: translate(-50%,-50%) rotate(30deg);
            -o-transform: translate(-50%,-50%) rotate(30deg);
            transform: translate(-50%,-50%) rotate(30deg);
          }
        }
      }
    }
    img {
      @include center_image;
      max-width: 101%;
      @include transition(all,1s);
    }
    &:before {
      content: '';
      @include full_size;
      z-index: 10;
      background: linear-gradient(to bottom, rgba(0,0,0,0),rgba(0,0,0,0) 50%,rgba(0,0,0,0.8));
    }
    &:hover {
      .banner_content {
        bottom: 50%;
        transform: translateY(50%);
        .desc {
          max-height: 300px;
        }
      }
      img {
        opacity: .3;
      }
    }
    .banner_content {
      position: absolute;
      bottom:0;
      left: 0;right: 0;
      z-index: 20;
      text-align: center;
      color: white;
      @include transition(all,1s);
      .title {
        font-family: "Quicksand", sans-serif;
        font-size: 40px;
        margin-bottom: 20px;
        i {
          display: inline-block;
          vertical-align: middle;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: white;
          color: $corporate_1;
          font-size: 40px;
          position: relative;
          margin-right: 50px;
          &:before {
            @include center_xy;
          }
          &:after {
            content: '';
            @include center_y;
            left: 100%;
            height: 1px;
            width: 40px;
            background: white;
          }
        }
      }
      .desc {
        padding: 0 100px;
        font-family: "Quicksand", sans-serif;
        max-height: 0;
        overflow: hidden;
        @include transition(all,1s);
        a.btn {
          position: relative;
          display: block;
          margin: 30px auto;
          width: 150px;
          padding: 15px 0;
          text-align: center;
          text-transform: uppercase;
          color: white;
          font-size: 18px;
          border: 1px solid white;
          overflow: hidden;
          @include transition(color,.6s);
          &:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200%;
            height: 0;
            z-index: -1;
            background: white;
            -webkit-transform: translate(-50%,-50%) rotate(30deg);
            -moz-transform: translate(-50%,-50%) rotate(30deg);
            -ms-transform: translate(-50%,-50%) rotate(30deg);
            -o-transform: translate(-50%,-50%) rotate(30deg);
            transform: translate(-50%,-50%) rotate(30deg);
            @include transition(all,.6s);
          }
          &:hover {
            color: $corporate_1;
            &:before {
              height: 200px;
            }
          }
        }
      }
    }
  }
}


@media screen and (min-width: 770px) {
  .banner_grid_wrapper .banner .gid_first .grid_title big {
    font-size: 46px;
  }
}