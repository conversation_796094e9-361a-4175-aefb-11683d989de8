@import "../only_mixins";

.wrapper_gift_bono {
  max-width: 1140px;
  margin: auto;
  text-align: left;
  padding: 70px 0;
  .title {
    padding-bottom: 30px;
    margin-left: 50px;
    line-height: 41px;
    h1 {
      font-size: 40px;
    }
  }
}
.gift_bono_content {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  .default_text {
    font-size: 16px;
    padding-bottom: 40px;
    line-height: 25px;
  }
  .left_content_wrapper, .right_content_wrapper {
    width: calc(50% - 105px);
    margin: 0 auto;
  }
  .left_content_wrapper {
    .discount_text {
      text-align: center;
    }
  }
  .right_content_wrapper {
    .discount_text {
      text-align: center;
      font-style: italic;
      font-size: 18px;
    }
    .gift_wrapper {
      background: #0A5689;
      height: 315px;
      border-radius: 25px;
      padding: 25px;
      position: relative;
      margin: 20px 0;
      color: white;
      span {
        display: block;
        margin-top: 5px;
        margin-bottom: 5px;
        text-align: left;
        text-transform: uppercase;
      }

      .logo_bono {
        max-height: 40px;
        margin-bottom: 40px;
      }

      .input_wrapper {
        display: inline-block;
        margin-top: -15px;
        position: relative;
        .message {
          display: none;
          @include center_x;
          top: -15px;
          width: 60%; // Should be equal to underline width
          text-align: left;
          font-size: 12px;
          white-space: nowrap;
        }
        .input_price, .input_price_custom {
          -webkit-appearance:none;
          -moz-appearance:   none;
          -ms-appearance:    none;
          -o-appearance:     none;
          appearance:        none;
          border: 0;
          background: transparent;
          font-size: 42px;
          color: white;
          font-weight: bold;
          width: 130px;
          height: 60px;
          outline: none;
          text-align: right;
          &::placeholder {
            color: white;
            white-space:pre-line;
            font-weight: lighter;
            position:relative;
            top: -12px;
            font-size: 18px;
            text-align: left;
          }
        }
        .currency {
          display: inline-block;
          font-size: 42px;
        }
        label.error {
          @include center_x;
          top: 75px;
          width: 60%; // Should be equal to underline width
          white-space: nowrap;
        }

        &.focus {
          &.other {
            .custom_price {
              display: none;
            }
            .message {
              display: block;
            }
            .input_price::placeholder {
              opacity: 0;
            }
            &:before {
              content: "";
              height: 1px;
              width: 60%;
              background: white;
              display: block;
              @include center_x;
              top: 100%;
            }
          }
          &:not(.other) {
            .custom_price {
              font-size: 42px;
              color: white;
              font-weight: bold;
              position: absolute;
              top: 5px;
              right: 31px;
            }
            .input_price {
              opacity: 0;
            }
          }
        }
      }
      .button_bono {
        display: inline-block;
        float: right;
        .buy_bono {
          cursor: pointer;
          background: white;
          border-radius: 50px;
          box-shadow: 0px 3px 6px #00000029;
          color: #2B2B2B;
          font-size: 16px;
          appearance: none;
          border: none;
          padding: 15px 40px;
          text-transform: uppercase;
          font-weight: 600;
          &.disabled {
            background: lightgray;
          }
        }
      }
      &:before {
        display: none !important;
        content: "";
        display: block;
        width: calc(100% + 1px);
        height: 180px;
        background: url("/static_1/images/lazo.png");
        background-repeat: no-repeat;
        bottom: -55px;
        @include center_x;
      }
    }
    .hotel_selector_wrapper {
      width: 100%;
      display: block;
      margin: 10px 0 20px;
      .select2-container--default {
        width: 100% !important;
        .select2-selection--single {
          border-radius: 0;
          outline: none;
          .select2-selection__rendered {
            padding-left: 20px;
            padding-right: 35px;
            font-weight: 600;
          }

          .select2-selection__arrow {
            top: 2px;
            right: 10px;
            &:before {
              content: "\f078";
              font-family: "Font Awesome 5 Pro";
              @include center_xy;
              font-weight: 300;
              color: #333;
              font-size: 17px;
            }
            b {
              display: none;
            }
          }
        }
        .select2-search--dropdown .select2-search__field {
          border: none;
          border-bottom: 2px solid #333;
        }
        &.select2-container--open .select2-selection--single .select2-selection__arrow {
          transform: rotateX(180deg);
        }
      }
    }
    .table_prices_wrapper {
      margin-top: 35px;
      .table_prices {
        display: grid;
        grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);
        margin-bottom: 20px;
        .price_cell {
          cursor: pointer;
          padding: 20px;
          text-align: center;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
    .conditions_wrapper {
      margin-top: 40px;
      .conditions_title {
        margin-bottom: 20px;
      }
      .condition {
        display: grid;
        grid-template-columns: 30px auto;
        align-items: center;
        font-size: 15px;
        margin: 10px 0;
        i {
          vertical-align: middle;
        }
      }
    }
  }
}

.wrapper_gift_bono.mobile_v {
  padding: 70px 20px 20px;
  .title {
    margin: auto;
  }
  .gift_bono_content {
    flex-flow: column;
    .left_content_wrapper, .right_content_wrapper {
      width: auto;
    }
    .left_content_wrapper {
      .discount_text {
        display: none;
      }
    }
    .right_content_wrapper {
      .discount_text {
        font-size: 15px;
      }
      .gift_wrapper {
        height: 255px;
        padding: 20px 25px;
        .input_wrapper {
          vertical-align: middle;
          margin-right: 10px;
          .message {
            top: -10px;
          }
          .input_price {
            width: 90px;
            font-size: 32px;
            padding: 0;
          }
          label.error {
            left: 20%;
          }
        }
        .button_bono {
          float: none;
          .buy_bono {
            font-size: 15px;
            padding: 10px 25px;
          }
        }
      }
      .conditions_wrapper {
        text-align: left;
      }
    }
  }
}

.select2-container.select2-container--default {
  .select2-search--dropdown .select2-search__field {
    border: none;
    border-bottom: 2px solid #333;

    &:focus {
      outline: none;
    }
  }
  .select2-results__option--disabled {
    display: none;
  }
}

.select2-results {
  strong {
    font-weight: 600 !important;
  }
}



//Responsive
@media screen and (max-width: 600px){
  .logo_bono {
    display: block;
  }

  .wrapper_gift_bono {
    .input_price_custom {
      width: 80px!important;
    }
  }
}