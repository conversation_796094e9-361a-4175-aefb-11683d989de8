body {
  font-family: "Quicksand", sans-serif;
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  a {
    text-decoration: none;
  }
  strong {
    font-weight: 700;
  }
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  #slider_container {
    position: relative;

    .inner_slider {
      width: 100%;
      height: 400px;
      position: relative;
      overflow: hidden;
      img {
        @include center_image;
      }
    }
  }

  .content_subtitle_wrapper {
    padding: 30px calc((100% - 1140px) / 2) 100px;

    .main_title {
      margin: 0;
      font-style: italic;
      font-size: 40px;
      line-height: 25px;
      font-weight: bolder;
      margin-top: 7px;
    }
    .content_subtitle_title {
      @include title_styles;
    }
    .content_subtitle_description {
      @include text_styles;
      padding-top: 50px;
      a {
        color: $black;
        text-decoration: underline;
      }
    }

    .button_promotion_individual_offer {
      display: inline-block;
      float: left;
      width: 135px;
      text-align: center;
      padding: 15px 0;
      color: white;
      font-size: 20px;
      font-weight: bold;
      text-transform: uppercase;
      position: relative;
      overflow: hidden;
      background-color: $corporate_2;
      margin-top: 20px;

      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200%;
        height: 0;
        background: rgba(0, 0, 0, 0.3);
        -webkit-transform: translate(-50%, -50%) rotate(45deg);
        -moz-transform: translate(-50%, -50%) rotate(45deg);
        -ms-transform: translate(-50%, -50%) rotate(45deg);
        -o-transform: translate(-50%, -50%) rotate(45deg);
        transform: translate(-50%, -50%) rotate(45deg);
        @include transition(all, .6s);
      }

      &:hover {
        color: white;

        &:before {
          height: 200px;
          -webkit-transform: translate(-50%, -50%) rotate(30deg);
          -moz-transform: translate(-50%, -50%) rotate(30deg);
          -ms-transform: translate(-50%, -50%) rotate(30deg);
          -o-transform: translate(-50%, -50%) rotate(30deg);
          transform: translate(-50%, -50%) rotate(30deg);
        }
      }

      span {
        position: relative;
      }

    }
  }

  /* Mis reservas */
  .bookingsection > div {
    display: none;
  }
  #my-bookings-form {
    #reservation {
      .modify_reservation_widget {
        margin: auto;
        margin-top: 40px;
        margin-bottom: 0;
        padding: 20px;

        #info_ninos {
          display: none !important;
        }

        #contenedor_opciones {
          margin: 0 auto 10px;

          .ninos-con-babies {
            margin-right: 15px;
          }
        }

        #contenedor_fechas {
          text-align: center;

          #fecha_entrada, #fecha_salida {
            display: inline-block;
            float: none;
          }
        }

        #contenedor_habitaciones {
          text-align: center;

          label {
            display: inline-block;
            float: none;
          }

          select {
            display: inline-block;
            float: none;
          }
        }

        #envio {
          text-align: center;
        }
      }

      .my-bookings-booking-info {
        margin: 40px auto 0;

        .fResumenReserva {
          margin: auto;
        }
      }
    }
    #modify-button-container {
      display: none;
    }

    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
      }

      input, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 350px;
        margin: 10px auto;
        height: 40px;
        border-radius: 0;
        text-align: center;
        font-size: 14px;
        border: 1px solid #DDD;
      }

      select {
        padding: 0 0 0 15px;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            height: 40px;
            text-transform: uppercase;
            font-size: 16px;
            color: white;
            border: 0;
            cursor: pointer;
            width: 100%;
            font-weight: 400;
            background: $corporate_1;
            font-family: $text_family;
            @include transition(background, .4s);

            &.modify-reservation {
              background: $corporate_1;

              &:hover {
                background: darken($corporate_1, 10%);
              }
            }

            &.searchForReservation {
              background: $corporate_2;

              &:hover {
                background: darken($corporate_2, 10%);
              }
            }
          }
        }
      }
    }

    #cancelButton {
      display: none;
      background: $corporate_2;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 40px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_2, 10%);
      }
    }
  }

  /* Iframe Map */

  .iframe_map_wrapper {
    iframe {
      width: 100%;
    }
  }
  .modal {
    @include full_size;
    position: fixed;
    z-index: 2000;
    background: rgba(0,0,0,0.8);
    display: none;
    .modal_close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      @include icon-xcross;
    }
    .modal_content {
      @include center_xy;
      background: white;
    }
  }
  .cols {
    &.colx2 {
      margin-bottom: 15px;
      p {
        text-align: center;
        width: 49%;
        padding: 15px 30px;
        font-size: 16px;
        display: inline-block;
        vertical-align: top;
      }
    }
    &.colx3 {
      p {
        text-align: center;
        width: 33%;
        padding: 15px 30px;
        font-size: 16px;
        display: inline-block;
        vertical-align: top;
      }
    }
  }

}

html[lang="fr"] {
  .hotels_filters .filter_wrapper {
    width: 285px;
    .select2 {
      width: 285px !important;
      .selection .select2-selection .select2-selection__rendered .select2-search input {
        width: 285px !important;
      }
    }
  }
  .hotel_card .banner_gallery_wrapper .banner_gallery_sec a.link {
    width: 260px;
  }

}