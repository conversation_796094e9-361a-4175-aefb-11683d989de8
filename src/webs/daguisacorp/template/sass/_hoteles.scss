.hotels_filters {
  padding: 0 calc((100% - 1110px) / 2);
  .filter_wrapper {
    display: inline-block;
    vertical-align: top;
    border: 1px solid $black;
    margin-right: 20px;
    position: relative;
    .select2 {
      .selection {
        .select2-selection {
          background: transparent;
          border-width: 0;
          .select2-selection__rendered {
            text-align: center;
            li:not(.select2-search) {
              &:first-of-type {
                margin-left: 50px;
              }
              &.select2-selection__choice {
                background: transparent;
                border-radius: 0;
                border-color: rgba($black, .3);
                padding: 5px 10px;
              }
              & + .select2-search {
                input {
                  padding: 10px 0;
                }
              }
            }
            .select2-search {
              input {
                text-align: center;
                text-transform: uppercase;
                padding: 10px 10px 10px 50px;
                font-family: $title_family;
                font-weight: 700;

                &:focus {
                  &::-webkit-input-placeholder {
                    color: transparent;
                  }

                  &::-moz-placeholder {
                    color: transparent;
                  }

                  &:-ms-input-placeholder {
                    color: transparent;
                  }

                  &:-moz-placeholder {
                    color: transparent;
                  }
                }
              }
            }
          }
        }
      }
    }
    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: transparent;
      border-radius: 0;
      border-width: 0;
      padding: 10px;
      padding-left: 80px;
      text-align: center;
      width: 250px;
      text-transform: uppercase;
      font-size: 20px;
    }
    &:before {
      position: absolute;
      top: 15px;
      @extend .fal;
      left: 15px;
    }
    &.cat_filter {
      @extend .fa-filter;
    }
    &.des_filter {
      @extend .fa-map-marker-alt;
    }
    &.ser_filter {
      @extend .fa-concierge-bell;
    }
  }
  .result_search {
    float: right;
    text-align: right;
    font-size: 20px;
    padding-top: 20px;
  }
}

.hotels {
  padding: 30px calc((100% - 1140px) / 2);
  text-align: center;
  @include display_flex;
  .hotel {
    display: inline-block;
    vertical-align: top;
    width: calc(50% - 35px);
    margin: 0 15px 30px;
    position: relative;
    text-align: left;
    &:before {
      content: '';
      @include full_size;
      z-index: -10;
      border: 1px solid rgba($black, .15);
    }
    &:hover {
      .hotel_pic {
        img.back {
          opacity: .6;
        }
        .center_y {
          opacity: 1;
        }
      }
    }
    .hotel_pic {
      width: 100%;
      height: 300px;
      position: relative;
      overflow: hidden;
      background: $black;
      img.back {
        @include center_image;
        min-width: 101%;
        @include transition(opacity,.6s);
      }
      .center_y {
        width: 100%;
        text-align: center;
        opacity: 0;
        @include transition(opacity,.6s);
        img {
          display: block;
          width: auto;
          margin: 0 auto 30px;
          vertical-align: middle;
        }
        a.link {
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
          color:white;
          &:hover {
            i {
              background: $corporate_2;
              &:after {
                background: $corporate_2;
              }
            }
          }
          i {
            display: inline-block;
            vertical-align: middle;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(white,.8);
            color: $black;
            font-size: 24px;
            position: relative;
            margin-right: 30px;
            &:before {
              @include center_xy;
            }
            &:after {
              content: '';
              @include center_y;
              left: 100%;
              height: 1px;
              width: 20px;
              background: rgba(white,.8);
            }
          }
          span {
            display: inline-block;
            vertical-align: middle;
            font-family: "DIN Alternate", sans-serif;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 1px;
            line-height: 20px;
            text-transform: uppercase;
          }
        }
      }
      .price {
        position: absolute;
        top: 15px;
        right: 15px;
        border-radius: 50%;
        padding: 20px 0;
        background: rgba($corporate_2,.8);
        text-align: center;
        font-family: "Quicksand", sans-serif;
        font-size: 14px;
        line-height: 14px;
        width: 90px;
        span {
          font-size: 25px;
          line-height: 25px;
        }
      }
    }
    .hotel_name {
      background: $black;
      text-align: center;
      color: white;
      padding: 15px 0;
      font-family: "DIN Alternate", sans-serif;
      font-size: 20px;
      font-weight: bold;
      text-transform: uppercase;
    }
    .hotel_location {
      padding: 20px 20px 0;
      font-size: 16px;
      i {
        margin: 0 30px 0 10px;
        font-size: 20px;
      }
    }
    .hotel_services {
      padding: 15px 20px 85px;
      .service {
        display: inline-block;
        vertical-align: middle;
        border: 1px solid $black;
        margin: 0 5px 5px 0;
        text-align: center;
        width: 150px;
        padding: 5px 0;
        font-size: 14px;
        text-transform: uppercase;
      }
    }
    .buttons {
      display: table;
      width: 100%;
      text-align: center;
      text-shadow: 0 0 0 transparent !important;
      position: absolute;
      left: 0;
      bottom: 0;
      overflow: hidden;
      a {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        float: left;
        font-family: "DIN Alternate", sans-serif;
        color: white;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 28px;
        font-weight: 500;
        padding: 15px 0;
        overflow: hidden;
        position: relative;
        &:after {
          content: '';
          @include full_size;
          z-index: -2;
          background: $gray-2;
        }
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 200%;
          height: 0;
          z-index: -1;
          background: rgba(0,0,0,0.3);
          -webkit-transform: translate(-50%,-50%) rotate(45deg);
          -moz-transform: translate(-50%,-50%) rotate(45deg);
          -ms-transform: translate(-50%,-50%) rotate(45deg);
          -o-transform: translate(-50%,-50%) rotate(45deg);
          transform: translate(-50%,-50%) rotate(45deg);
          @include transition(all,.6s);
        }
        &:hover {
          color: white;
          &:before {
            height: 200px;
            -webkit-transform: translate(-50%,-50%) rotate(30deg);
            -moz-transform: translate(-50%,-50%) rotate(30deg);
            -ms-transform: translate(-50%,-50%) rotate(30deg);
            -o-transform: translate(-50%,-50%) rotate(30deg);
            transform: translate(-50%,-50%) rotate(30deg);
          }
        }
        &.button_promotion, &.button-promotion {
          &:after {
            content: '';
            @include full_size;
            z-index: -2;
            background: $corporate_2;
          }
        }
      }
    }
  }
}