@import "styles_mobile";

body {
  //Cycle banners
  .cycle_banners_wrapper {
    display: block;
    background: white !important;

    .cycle_banner_element {
      margin-bottom: 30px;

      .image_wrapper {
        width: 100%;
        overflow: hidden;
        height: 170px;
        position: relative;

        img {
          position: absolute;
          top: -50%;
          left: -50%;
          right: -50%;
          bottom: -50%;
          margin: auto;
          min-width: 100%;
          min-height: 100%;
          width: 100%;
        }
      }

      .cycle_content {
        border: 1px solid #cacaca;
        border-top: 0;
        padding: 20px;

        .cycle_title {
          font-size: 21px;
          color: $corporate_1;
          margin-bottom: 20px;
          border-bottom: 1px solid #cacaca;
          padding-bottom: 10px;

          font-family: $title_family;
          .main_title {
            margin: 0;
          }
        }
        .date {
          font-size: 16px;
          padding: 15px 0;
          display: inline-block;
          color: $corporate_1;
        }
        .source {
          font-size: 16px;
          margin-left: 20px;
          padding: 15px 0;
          display: inline-block;
          color: $corporate_1;
        }

        .read_more {
          height: 50px !important;
        }

        .cycle_description {
          font-size: 12px;
          color: #424242;
          line-height: 20px;
          letter-spacing: 0.2px;
          overflow: hidden;
          transition: max-height 0.5s;

          &.exceded {
            max-height: 80px;
            overflow: hidden;
            @include transition(max-height, 0.5s);

            & + a + .read_more, & + .read_more {
              display: inline-block;
              color: $corporate_1;
              text-decoration: underline;
              margin-top: 20px;
              @include transition(color, .6s);
              font-weight: 700;
              font-size: 15px;
              letter-spacing: 0.6px;
              margin-left: 30px;
              height: 20px;
              vertical-align: top;
              overflow: hidden;
              cursor: pointer;

              &:hover {
                color: $corporate_1;
              }

              .more {
                transition: margin 0.5s;
              }

              &.active {
                .more {
                  margin-top: -20px;
                }
              }
            }

            & + .read_more {
              margin-left: 0;
            }

            &.visible {
              max-height: 1300px;
            }
          }
        }
      }

      .see_more_button {
        font-style: italic;
        font-size: 14px;
        color: $corporate_1;
        margin-top: 20px;
        display: none;
        text-align: right;
      }

      &.exceded_height {
        .cycle_description {
          max-height: 440px;
        }

        .see_more_button {
          display: block;
        }
      }

      &.deploy {
        .cycle_description {
          max-height: 3000px;
        }

        .see_more_button {
          display: none;
        }
      }
    }
  }

  .entry_wrapper {
    a {
      color: black;
    }
    .hide {
      position: absolute;
    }
  }

  .gift_bono_content .right_content_wrapper .gift_wrapper:before {
    display: none !important;
  }
}

html {
  scroll-behavior: smooth;
}

