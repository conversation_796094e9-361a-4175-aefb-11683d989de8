#weather_wrapper {
  right: 0;
  top: calc(100% - 40px);
  z-index: 2000;
  background-color: $corporate_2;
  position: absolute;
  width: 360px;
  padding-right: 30px;
  display: none;
  .weather_element {
    display: inline-block;
    text-align: center;
    color: white;
    padding: 10px 5px;
    font-size: 14px;
    width: 60px;
    @include transition(background-color, .6s);
    .weather_day {
      text-transform: uppercase;
    }
    .weather_icon {
      padding: 5px 7px;
      width: 50px;
      height: 50px;
    }

    .weather_low {
      opacity: .6;
      font-size: 12px;
    }
    &:hover {
      background-color: $corporate_1;
    }
  }
}

