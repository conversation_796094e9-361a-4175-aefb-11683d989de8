var JobApplication = function() {
    return {
        init: function() {
            this.prepare_options();
            this.prepare_listeners();
        },

        prepare_listeners: function() {
            var filters_wrapper = $("#filter_wrapper_banner");
            filters_wrapper.find(".filter_title").click(function(){
                $(this).parent().toggleClass('active');
            });

            $(".see_more_job_description").click(function(){
                JobApplication.open_job_description($(this));
            });

            filters_wrapper.find("#clear_filters_button").click(function(){
                JobApplication.clear_filters();
            });

            filters_wrapper.find("input[type='checkbox']").change(function () {
                filters_wrapper.find("input[type='checkbox']").each(function () {
                    $(this).prop("checked", false);
                });

                $(this).prop("checked", true);
               JobApplication.apply_filters();
            });

            $(".subscribe_job").click(function(){
                JobApplication.scroll_to_form();
                var this_job = $(this).data("job_name"),
                    select_jobs = $("#vacant_posts option");

                if (this_job) {
                    select_jobs.each(function () {
                        if ($(this).data("job_name") && $(this).data("job_name").indexOf(this_job) > -1) {
                            $(this).attr("selected", "true");
                            $("#vacant_posts").trigger("change");
                        }
                    });
                }
            });
        },

        scroll_to_form: function() {
            $('html, body').animate({
                scrollTop: $(".job_applications_form").offset().top - 60
            }, 2000);
        },

        open_job_description: function(clicked_element) {
            var job_wrapper = clicked_element.closest(".job_element");
            var target_html = job_wrapper.find(".more_info_job_wrapper").html();
            JobApplication.open_popup_v2(target_html);
        },
        open_popup_v2: function (target_html) {
            var target_html_to_open = $("<div></div>");
            target_html_to_open.html(unescape(target_html));
            $.fancybox(target_html_to_open, {
                'wrapCSS': 'popup_v2',
                'maxWidth': 600,
                'width': 600,
                'height': 'auto',
                'minHeight': 'auto',
                'autoSize': false,
                'fitToView': true,
                'helpers': {
                    overlay: {
                        css: {
                            'background': 'rgba(66,66,66, 0.8)'
                        }
                    }
                }
            });
        },

        prepare_options: function() {
            //Jobs types
            var available_types = [];
            $(".job_element").each(function(){
                var type = $(this).attr('type');
                if (available_types.indexOf(type) === -1){
                    available_types.push(type);
                }
            });
            available_types.forEach(function(element, n){
                var option_element = $("<div class='option_element'></div>"),
                    radio_button = $("<input type='checkbox' name='types_filter'>"),
                    label_element = $("<label></label>");

                radio_button.attr('id', 'type' + n);
                label_element.attr('for', 'type' + n);

                label_element.html(element);
                radio_button.attr('value', element);
                option_element.append(radio_button).append(label_element);
                $(".laboral_offer_type .options_list").append(option_element);
            });

            this.update_visible_jobs();
        },

        apply_filters: function(){
            var selected_radio_value = $("#filter_wrapper_banner input:checked");
            if (!selected_radio_value.length) {
                $(".job_element").removeClass('hide');
                return;
            }

            if (selected_radio_value) {
                var elements_to_find = {
                    'destinies_filter': "destiny",
                    'types_filter': 'type'
                };

                selected_radio_value.each(function(){
                    var input_name = $(this).attr('name'),
                        input_value = $(this).attr('value'),
                        target_find = elements_to_find[input_name];

                    $(".job_element").each(function(){
                        var found = false;
                        if ($(this).attr(target_find) === input_value) {
                            found = true;
                        }
                        // if (!found  && !$(this).hasClass('match')) {
                        if (!found) {
                            $(this).addClass('prepare_hide');
                        } else {
                            $(this).removeClass('prepare_hide hide').addClass('match');
                        }
                    });
                });

                $(".prepare_hide").removeClass('prepare_hide match').addClass('hide');
            }

            this.update_visible_jobs();
        },

        update_visible_jobs: function() {
            var jobss_element_amount = $(".available_offers_wrapper .job_element:visible").length;
            $(".job_offers_found .jobs_number").html(jobss_element_amount);
        },

        clear_filters: function(){
            $("#filter_wrapper_banner").find("input:checked").prop('checked', false);
            $(".job_element").removeClass('hide');
            this.update_visible_jobs();
        },
    };
}();

$(function(){
    JobApplication.init();
});

/*======== Forms =======*/
$("form.group_contact_form").validate({
    rules: {
        name: "required",
        vacant_posts: "required",
        file: "required",
        cp: "required",
        telephone: {
            required: function (element) {
                return !$("#telephone").val() > 0 || !$("#mobile").val() > 0;
            }
        },
        //comments: "required",
        email: {
            required: true,
            email: true
        },
        privacy: "required"
    }, highlight: function (element) {
        $(element).addClass('input-error');
    }, unhighlight: function (element) {
        $(element).removeClass('input-error');
    }
});

function adjust_blocks_see_more(){
    $(".job_element").each(function(){
        var target_block_height = $(this).find(".job_content .job_description"),
            actual_height = target_block_height.height();

        if (actual_height > 80){
            target_block_height.addClass('exceded');
        }
    })
}

$(".job_content .read_more").click(function(){
    $(this).toggleClass('active');
    $(this).closest('.job_content').find('.job_description').toggleClass('visible');
});

adjust_blocks_see_more();

function send_work_form(form) {

    if ($(this).hasClass("disabled")){
        return false;
    }

    var url_for_upload = "",
        url_cv_download = "";

    $(this).addClass('disabled');

    if (form.valid()) {
        var final_form_data = {};
        var formData_dict = form.serializeArray();

        for(var i=0; i < formData_dict.length; i++) {
            final_form_data[formData_dict[i]['name']] = formData_dict[i]['value']
        }

        console.log(final_form_data);

        $.ajax({
            url: "/get_upload_url",
            type: 'GET',
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                url_for_upload = returndata;
            }
        });

        if (form.find("#file").length && url_for_upload && form.find("#file").val()) {
            formData_dict = new FormData(form[0]);

            console.log("Envio");
            console.log(formData_dict);

            $.ajax({
                url: url_for_upload,
                type: 'POST',
                data: formData_dict,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (returndata) {
                    url_cv_download = returndata;
                }
            });
        }

        var formData_to_send = final_form_data;

        formData_to_send['url_file_download'] = url_cv_download;

        console.log($.param(formData_to_send));

        $.ajax({
            url: "/utils/?action=work_with_us",
            type: 'POST',
            data: formData_to_send,
            async: false,
            cache: false,
            success: function(){
                _thanks_work_form();
            }
        });
    }

    $(this).removeClass("disabled");

    return false;
}

function _thanks_work_form(){
    $('form.group_contact_form')[0].reset();
    alert($("#thanks_job").val());
}
