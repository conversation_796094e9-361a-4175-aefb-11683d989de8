<div class="gallery_filter_wrapper container12">{% set gallery_counter = 0 %}
    {% for gallery, pics in gallery_filter.items() %}
        {% set gallery_counter = gallery_counter + 1 %}
        <h2 class="gallery_title">{{ gallery|safe }}</h2>
        {% for pic in pics %}<a {% if pic.video %}data-src="{{ pic.video|safe }}" data-fancybox data-type="iframe" class="myFancyPopup fancybox.iframe"{% else %}
                href="{{ pic.servingUrl }}=s1900" rel="lightbox[gallery_{{ gallery_counter }}]" {% if pic.description %} - {{ pic.description|safe }}{% endif %}"{% endif %}><img src="{{ pic.servingUrl }}=s400" alt="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}" title="{{ gallery|safe }}{% if pic.description %} - {{ pic.description|safe }}{% endif %}">{% if pic.description %}<span class="overlay">{{ pic.description|safe }}</span>{% endif %}</a>{% endfor %}
    {% endfor %}
</div>