<div class="destination_wrapper unselected">
  <div class="destination_field">
      <label for="destination">{{ T_seleccionar }}</label>
      <textarea class="destination" readonly="readonly" type="text" name="destination" placeholder="{{ T_hotel }} / {{ T_destino }}"></textarea>
    <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="">
  </div>
</div>

<div class="hotel_selector">
    <div class="close icon-xcross"></div>
    <div class="hotel_selector_search">
        <i class="fas fa-search"></i>
        <input type="text" class="searching_hotel" placeholder="{{T_buscar }} {{ T_hotel }}/{{ T_destino }}">
    </div><div class="hotel_selector_inner">
        <ul>{% for hotel in hotels %}
            <li id="{{ hotel.namespace }}" class="{{ hotel.namespace }} filter_all filter_{{ hotel.destiny_class }} hotel_selector_option"
                {% if hotel.kids_range %}data-kidsrange="{{ hotel.kids_range|safe }}"{% endif %}>
                <div class="hotel_pic">
                    {% if hotel.main %}<img src="{{ hotel.main.0.servingUrl }}=s300-c" class="main" {% if hotel.main.0.altText %}alt="{{ hotel.main.0.altText|safe }}"{% endif %}>{% endif %}
                    {% if hotel.logo %}<img src="{{ hotel.logo.0.servingUrl }}=s150" class="logo" {% if hotel.logo.0.altText %}alt="{{ hotel.logo.0.altText|safe }}"{% endif %}>{% endif %}
                </div>
                <span class="title_selector">{{ hotel.value|safe }}</span>
                <div class="hotel_location"><i class="fal fa-map-pin"></i><span>{{ hotel.place|safe }}</span></div>
            </li>
            <input type="hidden" id="url_booking_{{ hotel.namespace }}" value="{{ hotel.url_booking }}">
            <input type="hidden" id="namespace_{{ hotel.namespace }}" value="{{ hotel.namespace }}">
        {% endfor %}</ul>
    </div>
</div>
