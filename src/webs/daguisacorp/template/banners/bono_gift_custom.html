<style>
    .gift_bono_content .right_content_wrapper .table_prices_wrapper .table_prices .price_cell {
        border-top: 1px solid {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
        border-top: 1px solid {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
        border-left: 1px solid {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
    {% if section_properties.color2|safe not in section_properties.color1|safe %}color: {{ section_properties.color2|safe }};{% endif %}
    }

    .gift_bono_content .right_content_wrapper .table_prices_wrapper .table_prices .price_cell:hover, .gift_bono_content .right_content_wrapper .table_prices_wrapper .table_prices .price_cell.selected  {
        background: {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
        color: white;
    }

    .gift_bono_content .right_content_wrapper .table_prices_wrapper .table_prices .price_cell:nth-child(3n) {
        border-right: 1px solid {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
    }
    .gift_bono_content .right_content_wrapper .table_prices_wrapper .table_prices .price_cell:nth-child(n + 4) {
        border-bottom: 1px solid {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
    }
    .gift_bono_content .right_content_wrapper .gift_wrapper {
        background: {% if section_properties.custom_gift_color %}{{ section_properties.custom_gift_color }}{% else %}{{ section_properties.color2|safe }}{% endif %};
        {% if section_properties.custom_image %}background: url("{{ section_properties.custom_image }}");background-size: cover;{% endif %}
    }
    .gift_bono_content .right_content_wrapper .gift_wrapper .button_bono .buy_bono {
        {% if section_properties.custom_btn_color %}color: {{ section_properties.custom_btn_color|safe }};{% else %}{% if section_properties.color2|safe not in section_properties.color1|safe %}color: {{ section_properties.color2|safe }};{% endif %}{% endif %}
        {% if section_properties.custom_btn_bg %}background: {{ section_properties.custom_btn_bg|safe }};{% else %}{% if section_properties.color1|safe not in section_properties.color2|safe %}background: {{ section_properties.color1|safe }};{% endif %}{% endif %}
    }
    .gift_bono_content .right_content_wrapper .conditions_wrapper .condition i {
        color: {{ section_properties.color1|safe }};
    }
    .gift_bono_content .right_content_wrapper .hotel_selector_wrapper .select2 .select2-selection--single .select2-selection__rendered {
        color: {{ section_properties.color2|safe }};
    }
    .gift_bono_content .right_content_wrapper .hotel_selector_wrapper .select2 .select2-selection--single {
        border-color: {% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %} !important;
    }
    .select2-container.select2-container--default .select2-search--dropdown .select2-search__field {
        border-color:{% if section_properties.custom_table_color %}{{ section_properties.custom_table_color }}{% else %}{{ section_properties.color2|safe }}{% endif %} !important;
    }
</style>

<div class="wrapper_gift_bono {% if mobile %}mobile_v{% endif %}">
    <div class="title">
        {{ section_content.subtitle|safe }}
    </div>
    <div class="gift_bono_content">
        <div class="left_content_wrapper">
            <div class="default_text">
                {{ section_content.content|safe }}
            </div>
        </div>
        <div class="right_content_wrapper">
            {% if section_properties.discount_text %}
                <div class="discount_text">
                    {{ section_properties.discount_text|safe }}
                </div>
            {% endif %}
            <form class="gift_wrapper" method="GET" action="/booking3_bonos">
                <input type="hidden" name="language" value="{{ language }}">
                {% if section_properties.custom_image %}
                    <input type="hidden" id="card_design" name="card_design" value="{{ section_properties.custom_image }}">
                {% endif %}
                <input type="hidden" id="card_design" name="card_design" value="">
                <div class="title_wrapper">
                    <span class="title_info">{{ T_bono_regalo }}</span>
                    <img src="{{ logotype.0 }}" alt="" class="logo_bono">
                </div>
                <div class="input_wrapper">
                    <div class="message">{{ T_inserta_importe }}</div>
                    <label for="price" class="custom_price"></label>
                    <input class="input_price_custom" name="input_price_custom" style="display: none" type="text">
                    <input class="input_price" name="price" type="text" readonly required placeholder="{{ T_seleccionar_importe }}">
                    <div class="currency" style="display: none">€</div>
                </div>
                <div class="button_bono">
                    <button class="buy_bono" type="submit">
                        {{ T_comprar }}
                    </button>
                </div>
            </form>
            {% if hotels_list %}
                 <div class="hotel_selector_wrapper">
                    <div class="booking-form-field">
                        <select name="hotel_selector" id="hotel_selector" class="booking-form-control my-select-2">
                            <option value="" selected="selected" disabled>{{ T_elija_hotel|safe }}</option>
                                {% for destiny_name, destiny_hotels in hotels_list %}
                                    <optgroup label="{{ destiny_name|safe }}">
                                        {% for hotel in destiny_hotels %}
                                            <option value="{{ hotel.url_booking }}">{{ hotel.pictureTitle|safe }}</option>
                                        {% endfor %}
                                    </optgroup>
                                {% endfor %}
                        </select>
                    </div>
                </div>
            {% endif %}
            <div class="table_prices_wrapper">
                <div class="table_prices_content">
                    {% if section_properties.prices_ranges %}
                        <div class="prices_ranges" style="display: none">
                            {{ section_properties.prices_ranges|safe }}
                        </div>
                    {% else %}
                        <div class="table_prices">
                            <div class="price_cell">100€</div>
                            <div class="price_cell">200€</div>
                            <div class="price_cell">300€</div>
                            <div class="price_cell">400€</div>
                            <div class="price_cell">500€</div>
                            <div class="price_cell other">{{ T_other }}</div>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% if section_images %}
                <div class="conditions_wrapper">
                <div class="conditions_title">
                    {{ T_condiciones }}:
                </div>
                {% for image in section_images %}
                    <div class="condition">
                        {% if image.title != "card" %}
                            {% if image.title %}
                                {{ image.title|safe }}
                            {% else %}
                                <img src="{{ image.servingUrl }}" >
                            {% endif %}
                            {{ image.description|safe }}
                        {% endif %}
                    </div>
                {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
    {% if section_properties.discount %}<input type="hidden" name="discount" value="{{ section_properties.discount|safe }}">{% endif %}
</div>

{% if mobile %}
    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
{% endif %}

{% if hotels_list %}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
{% endif %}
<script type="text/javascript">
    $(window).on("load", function () {
        let prices_ranges = $(".prices_ranges").html().replace(/[€¢$]/g, '').trim().split("-"),
            table_base = $("<div class='table_prices'></div>"),
            input_discounts = $("input[name=discount]"),
            input_prices = $("input.input_price"),
            input_price_custom = $("input.input_price_custom"),
            label_custom_price = $("label.custom_price");

        var prevent_calc = true;
        input_prices.val('');

        $("body").addClass("bonos_section");

        {% if hotels_list %}
           $('.my-select-2').select2();
           $('.my-select-2').one('select2:open', function(e) {
                $('input.select2-search__field').prop('placeholder', '{{ T_buscar_hotel }}');
           });
           $(".button_bono .buy_bono").attr("disabled", "disabled").addClass("disabled");
            $('#hotel_selector').on("change", function() {
                $("form.gift_wrapper").prop("action", $(this).val())
                $(".button_bono .buy_bono").removeAttr("disabled").removeClass("disabled");
            });
            if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
              $('.my-select-2').val('').trigger('change');
            }
        {% endif %}

        if (prices_ranges.length) {
            console.log(prices_ranges);
            $.each(prices_ranges, (index, item) => {
                if (!isNaN(item)) {
                    table_base.append(`<div class="price_cell">${item} €</div>`)
                } else {
                    table_base.append(`<div class="price_cell other">${item}</div>`)
                }
            });
            $(".table_prices_content").append(table_base);
        }

        $(document).on("click", ".table_prices .price_cell", function () {
            let prices_cell = $(".table_prices .price_cell");
            prevent_calc = false;
            prices_cell.removeClass('selected');
            $(this).addClass('selected');
            input_prices.val($(this).html().replace(/[^0-9]/g, ''));
            input_price_custom.val($(this).html().replace(/[^0-9]/g, ''));
            input_price_custom.removeAttr("disabled");
            $(".input_wrapper").addClass("focus")
            $(".gift_bono_content .left_content_wrapper .discount_text").fadeIn();
            $(".input_wrapper .currency").fadeIn();

            if (input_discounts.length) {
                if (input_discounts.val().indexOf(":") > - 1) {
                    let step = input_discounts.val().split(":")[0],
                        discount = input_discounts.val().split(":")[1];

                    discount = parseInt(parseFloat(input_prices.val()) / parseInt(step)) * parseFloat(discount);
                    label_custom_price.html(parseFloat(input_prices.val()) + parseFloat(discount));
                } else {
                    let final_discounts = parseFloat(input_discounts.val().replace("%", ""));
                    label_custom_price.html(input_prices.val() * (100.0 + final_discounts) / 100.0);
                }
            }

            if ($(this).hasClass("other")) {
                input_prices.val("").removeAttr("readonly").hide();
                input_price_custom.val("").show().focus();
                $(".input_wrapper").addClass("other");
                $('.gift_wrapper').validate().settings.rules.input_price_custom = {
                    required: true,
                    number: true
                };
            } else {
               input_prices.attr("readonly", "readonly").hide();
               input_price_custom.val("").show();
               $("label[for=input_price_custom]").hide();
               $(".input_wrapper").removeClass("other");
               $('.gift_wrapper').validate().settings.rules.input_price_custom = {};
               $('.gift_wrapper').valid()
            }
        });
        input_price_custom.on("keyup", function () {
            input_prices.val($(this).val());
            $('.gift_wrapper').valid();
        });
        $(".button_bono .buy_bono").on("click", function () {
            if ($('.gift_wrapper').valid()) {
                if (!prevent_calc) {
                    input_prices.val(input_prices.val() * 100);
                    prevent_calc = true
                }
            }
        });

        if (typeof($.validator) !== "undefined") {
                prepare_validator_form();
        } else {
            $.getScript("/static_1/lib/jquery.validate.min.js", function () {
                prepare_validator_form();
            });
        }
    });

    function prepare_validator_form() {
        $("form.gift_wrapper").validate({
            rules: {
                price: {
                    required: true,
                    number: true
                }
            }
        });
    }
</script>