{% if job_promotions %}
    {% include "banners/_jobs_available_offers.html" %}
{% endif %}

<div id="work_cv_post" class="job_applications_form contact_wrapper groups_contact">
    <h3 class="contact_form_title">{{ T_envianos_tu_cv }}</h3>
    <form name="contact" id="contact" method="post" action="/utils/?action=work_with_us" class="group_contact_form">
        <input type="hidden" name="action" id="action" value="contact"/>
        <div class="info block_inputs">
            <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

            <div class="contInput">
                <label for="vacant_posts" class="title">{{ T_puesto_deseado }}</label>
                <select name="vacant_posts" id="vacant_posts">
                    <option value="">{{ T_seleccionar }}...</option>
{#                    <option value="General">{{ T_general }}...</option>#}
                    {% for job_element in job_promotions %}
                        {% if job_element.title %}
                            <option value="{{ job_element.title|safe }}" {% if job_element.job_classname %}data-job_name="{{ job_element.job_classname|safe }}"{% endif %}>{{ job_element.title|safe }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>

            <div class="contInput">
                <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                <input type="text" id="name" name="name" class="bordeInput" placeholder="{{ T_introduzca_nombre_apellido }}" value=""/>
            </div>

            <div class="contInput">
                <label for="cp" class="title">{{ T_codigo_postal }}</label>
                <input type="text" id="cp" name="cp" class="bordeInput" placeholder="{{ T_codigo_postal }}" value=""/>
            </div>

            <div class="contInput">
                <label for="email" class="title">{{ T_email }}</label>
                <input type="text" id="email" name="email" class="bordeInput" placeholder="{{ T_introduzca_email }}" value=""/>
            </div>

            <div class="contInput">
                <label for="telephone" class="title">{{ T_telefono }}</label>
                <input type="text" id="telephone" name="telephone" class="bordeInput" placeholder="{{ T_introduzca_num_tel }}" value=""/>
            </div>

            <div class="contInput">
                <label for="file" class="title">{{ T_adjunta_cv }}</label>
                <input type="file" id="file" name="file" value="" style="width: 320px;">
            </div>

            <div class="contInput area">
                <label for="comments" class="title">{{ T_comentarios }}</label>
                <textarea type="text" id="comments" name="comments" class="bordeInput"
                          value="" placeholder="{{ T_comentarios }}"></textarea>
            </div>

            {% if captcha_box %}
                <div class="contInput captcha">
                    <script src='https://www.google.com/recaptcha/api.js'></script>
                    <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                </div>
            {% endif %}

            <div id="contact-button" class="submit_button_contact_form" onclick="send_work_form($('.group_contact_form'))">
                {{ T_enviar }}
            </div>

            <div class="privacy_wrapper">
                <input class="bordeInput check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/>
                <span class="title"><a href="/{{ language }}/?sectionContent=politica-de-privacidad.html"
                                       class="myFancyPopup fancybox.iframe">{{ T_lopd }}</a></span>
            </div>
        </div>
    </form>
    <input type="hidden" value="{{ T_gracias_contacto }}" id="thanks_job">
</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript" src="/js/{{ base_web }}/job_application.js?v=1.64"></script>