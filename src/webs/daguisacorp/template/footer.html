<footer>

<div class="footer_columns">
    {% for col in footer_columns %}
        <div class="col">
            {% if col.servingUrl %}<img src="{{ col.servingUrl }}=s500" alt="{{ col.altText|safe }}">{% endif %}
            {% if col.title %}<div class="title">{{ col.title|safe }}</div>{% endif %}
            {% if col.description %}<div class="desc">{{ col.description|safe }}</div>{% endif %}
            {% if loop.first %}
                <div id="social">
                    {%if facebook_id %}
                        <a href="https://www.facebook.com/{{facebook_id}}" target="_blank">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                    {% if twitter_id %}
                        <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                    {% if google_plus_id %}
                        <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                            <i class="fab fa-google-plus" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                    {% if youtube_id %}
                        <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                    {% if pinterest_id %}
                        <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                            <i class="fab fa-pinterest-p" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                    {% if instagram_id %}
                        <a href="https://www.instagram.com/{{ instagram_id }}" target="_blank">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    {% endfor %}
</div>
<script>$(window).load(function(){
    var max_col = 0;
    $(".footer_columns .col").each(function () {
        if($(this).height() > max_col) {
            max_col = $(this).height();
        }
    });
    $(".footer_columns .col").height(max_col);
});</script>

{% if texto_legal %}
    <div class="legal_text">{{ texto_legal|safe }}</div>
{% endif %}

<div class="footer_links_wrapper">
    {% for x in policies_section %}
        <a href="{% if x.custom_link %}{{ x.custom_link }}{% else %}/{{ language }}/?sectionContent={{ x.friendlyUrl }}{% endif %}"
           class="{% if not x.custom_link %}myFancyPopup fancybox.iframe{% endif %}">{{ x.title|safe }}</a> |
    {% endfor %}
    <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
       title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
    <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
    <a target="_blank" href="/rss.xml">RSS</a>
</div>

</footer>