{% if not banner_offers %}
    <div class="offers_filters">
        <div class="cat_filter filter_wrapper">
            <select name="cat_filter" id="cat_filter" class="select2">
                <option value="all">{{ T_todos_hoteles_2 }}</option>
                {% for hotel in hotels %}
                    <option value="{{ hotel.id }}">{{ hotel.subtitle|safe }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="off_filter filter_wrapper">
            <a href="#">{{ T_ofertas }}</a>
        </div>
        <div class="paq_filter filter_wrapper">
            <a href="#">{{ T_paquetes }}</a>
        </div>
    </div>
{% endif %}
<div class="offers_wrapper">
    {% for offer in offers %}
        <div class="offer {% if not banner_offers %}{% if "p" in offer.priority|lower %}paq{% endif %} {% if offer.extra_properties.hotels_in %}{% for hotel in hotels %}{% if hotel.id in offer.extra_properties.hotels_in %}{{ hotel.id }} {% endif %}{% endfor %}{% else %}all {% endif %}{% endif %}">
            <div class="offer_pic">
                <img src="{{ offer.picture }}=s500-c" {% if offer.altText %}alt="{{ offer.altText|safe }}"{% endif %}>
            </div>
            <h2 class="offer_title">{{ offer.name|safe }}</h2>
            {% if offer.description %}
                <div class="offer_desc">
                    {{ offer.description|safe }}
                </div>
            {% endif %}
            {% if not banner_offers %}
                <div class="offer_hotels">
                    {%  if offer.extra_properties.hotels_in %}
                        {% for hotel in hotels %}
                            {% if hotel.id in offer.extra_properties.hotels_in %}
                                <div class="hotel_logo">
                                    <img src="{{ hotel.logo_black.0.servingUrl }}" {% if hotel.logo_black.0.altText %}alt="{{ hotel.logo_black.0.altText }}"{% endif %}>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        {% for hotel in hotels %}
                            <div class="hotel_logo">
                                <img src="{{ hotel.logo_black.0.servingUrl }}" {% if hotel.logo_black.0.altText %}alt="{{ hotel.logo_black.0.altText }}"{% endif %}>
                            </div>
                        {% endfor %}
                    {% endif %}
                </div>
            {% endif %}
            <div class="links_wrapper">
                {% if offer.linkUrl %}
                    <a href="{{ offer.linkUrl|safe }}" {% if "http" in offer.linkUrl %}target="_blank" {% endif %} class="link see_more">{{ T_saber_mas }}</a>
                {% else %}
                    <a href="#hidden_description_offer_{{ loop.index }}" class="link see_more myFancyPopupAuto">{{ T_saber_mas }}</a>
                {% endif %}

                {% if not banner_offers %}
                    <a href="#data" class="{% if is_mobile %}button-promotion{% else %}button_promotion{% endif %}" {% if offer.extra_properties.hotels_in %}data-hotel-in="{{ offer.extra_properties.hotels_in }}"{% endif %} {{ offer.smartDatasAttributes|safe }}>{{ T_reservar }}</a>
                {% endif %}
            </div>

            <div class="description_popup_wrapper" id="hidden_description_offer_{{ loop.index }}">
                <h2 class="offer_title">{{ offer.name|safe }}</h2>
                {% if offer.description %}
                    <div class="offer_desc">
                        {{ offer.description|safe }}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}
</div>
<script>$(window).load(function(){
    {% if not banner_offers %}
        $("#cat_filter").change(function () {
            var namespace = $(this).val();
            $(".offers_wrapper .offer").slideUp(500).promise().done(function () {
                if(namespace != "all"){
                    $(".offers_wrapper .offer."+namespace).slideDown(500);
                    $(".offers_wrapper .offer.all").slideDown(500);
                } else {
                   $(".offers_wrapper .offer").slideDown(500);
                }
            $(".off_filter, .paq_filter").removeClass("active");
            });
        });
        $(".off_filter a").click(function (e) {
            e.preventDefault();
            $(".paq_filter").removeClass("active");
            $(this).parent().addClass("active");
            var namespace = $("#cat_filter").val();
            $(".offers_wrapper .offer").slideUp(500).promise().done(function () {
                if(namespace != "all"){
                    $(".offers_wrapper .offer."+namespace+":not(.paq)").slideDown(500);
                    $(".offers_wrapper .offer.all:not(.paq)").slideDown(500);
                } else {
                    $(".offers_wrapper .offer:not(.paq)").slideDown(500);
                }
            });
        });
        $(".paq_filter a").click(function (e) {
            e.preventDefault();
            $(".off_filter").removeClass("active");
            $(this).parent().addClass("active");
            var namespace = $("#cat_filter").val();
            $(".offers_wrapper .offer").slideUp(500).promise().done(function () {
                if(namespace != "all"){
                    $(".offers_wrapper .offer."+namespace+".paq").slideDown(500);
                    $(".offers_wrapper .offer.all.paq").slideDown(500);
                } else {
                    $(".offers_wrapper .offer.paq").slideDown(500);
                }
            });
        {% endif %}
    });
});</script>
