$(function () {

    $("#pikame").PikaChoose({showTooltips: true, carousel: false});

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });
    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".button-promotion, .button_promotion").fancybox({
        width: 800,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
            $('.fancybox-inner').off('scroll');
        }
    });



    $(".booking_widget.inline .entry_date").click(function(){
        $("#ui-datepicker-div").css({
            'top':'40px'
        });
    });
    $(".booking_widget.inline .departure_date").click(function(){
        $("#ui-datepicker-div").css({
            'top':'40px'
        });
    });

    $('#lang').click(function() {
        $('#language-selector-options').slideDown('fast');
	});

	$('#lang').mouseleave(function() {
		$('#language-selector-options').slideUp('fast');
	});

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');


    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }


    if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}

    $(".block-room").each(function () {
        check_hide = $(this).find(".hide_me").length
        if (check_hide) {
            $(this).find('.see_more').css('display', 'inline-block')
        }
    });

    $(".see_more").fancybox({
        width: 800,
        height: 'auto',
        fitToView: false,
        autoSize: false,
        padding: 20
    });

/*    $(".tp-rightarrow").appendTo(".tp-bullets");
    $(".tp-leftarrow").appendTo(".tp-bullets");*/

    slider_height = $("#slider_container").height() + $("header").height();
    $(window).scroll(function () {
        repositionBooking();
    });

    repositionBooking();

     /****************** Sorteo form *********************/

     if (typeof $.i18n == 'object') {
        if (typeof messages == 'object') {
            $.i18n.load(messages);
        }
     }

    $("#contact_salones").validate({
      rules: {
        name: "required",
        email: {
          required: true,
          email: true
        }
      },
      messages: {
            email: $.i18n._("campo_valor_invalido"),
            name: $.i18n._("campo_obligatorio")

        }
        ,highlight: function(element) {
            $(element).addClass('input-error');
        }, unhighlight: function(element) {
            $(element).removeClass('input-error');
        }
    });

    $("#button-send").click(function(){

      if ( $("#contact_salones").valid() ) {


        $.post(
          "/utils/?action=contact",
          {
            'name': $("#name").val(),
            'telephone': $("#telephone").val(),
            'city': $("#city").val(),
            'country': $("#country").val(),
            'mobile': $("#mobile").val(),
            'email': $("#email").val(),
            'comments': $("#comments").val(),
            'destination_email' :$("#destination_email").val()

          },

          function(data) {
             alert($.i18n._("gracias_contacto"));
            $("#name").val("");
            $("#email").val("");
            $("#comments").val("")

          }
        );

      }
      else{
          $("label.error").css("display", "none");

      }
    });

    $("#button-google").click(function () {

        var map = $("#map-layer");
        map.toggleClass("active");
        map.hasClass("active") ? $("#button-google a").text($.i18n._("volver_atras")) : $("#button-google a").text("Google Maps");
        return false;
    });


});

//Overrides Promocode input functionality to load on start
    $('.promocode_text').ready(function () {
        $('.promocode_input').toggle('fast');
    });

function repositionBooking() {
    actual_height = $(window).scrollTop();
    if (actual_height > slider_height) {
        $("#wrapper_booking.inline").slideDown();
    } else {
        $("#wrapper_booking.inline").slideUp();
    }
}


(function () {
    var po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
})();


function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}
