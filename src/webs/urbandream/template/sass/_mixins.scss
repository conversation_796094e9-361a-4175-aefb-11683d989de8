@mixin center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  -ms-transform: translate(-50%,-50%);
  -o-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
}

@mixin center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%,0%);
  -moz-transform: translate(-50%,0%);
  -ms-transform: translate(-50%,0%);
  -o-transform: translate(-50%,0%);
  transform: translate(-50%,0%);
}

@mixin center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%,-50%);
  -moz-transform: translate(0%,-50%);
  -ms-transform: translate(0%,-50%);
  -o-transform: translate(0%,-50%);
  transform: translate(0%,-50%);
}