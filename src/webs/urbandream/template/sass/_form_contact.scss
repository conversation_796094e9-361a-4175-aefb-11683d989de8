.contact_form_wrapper {
  display: inline-block;
  width: 100%;
  float: left;
  padding: 0 0 80px;
  background-color: white;
  * {
    box-sizing: border-box;
  }
  h3 {
    text-align: center;
    color: #4B4B4B;
    font-size: 25px;
    font-weight: 100;
    padding: 10px;
    strong {
      font-weight: 700;
    }
  }

  #contact {
    display: table;
    width: 980px;
    margin: auto;
    background-color: #E6E6E6;

    .top_form {
      background-color: #EDEDED;
      text-align: right;
      display: table;
      width: 100%;
      font-size: 14px;
      color: #4B4B4B;
      border-top: 2px solid $corporate_2;
      border-bottom: 5px solid white;
      .selector_hotel {
        display: inline-block;
        position: relative;
        float: left;
        padding: 0;
        margin: 10px;
        width: 250px;
        @extend .fa-angle-down;
        &:before {
          font-family: "Fontawesome", sans-serif;
          color: $corporate_2;
          @include center_y;
          right: 10px;
        }
        select {
          position: relative;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background: transparent;
          border: 1px solid #BBB;
          border-radius: 0;
          box-shadow: 0 0 0 rgba(0,0,0,0);
          padding: 7px;
          margin: 0;
          width: 100%;
          font-size: 14px;
        }
      }
      span {
        display: inline-block;
        vertical-align: middle;
        padding: 17px 10px 17px 0;
      }
      input[type=checkbox] {
        display: inline-block;
        vertical-align: middle;
      }
    }
    label {
          padding: 15px 0 0;
          display: block;
          font-size: 14px;
          color: lighten($corporate_2, 30%);
          &.error {
            position: absolute;
            bottom: -20px;
            padding: 5px 10px;
            color: #943E46;
            background-color: #f8d7da;
            border-color: #f5c6cb;
            border-radius: 5px;
            z-index: 2;
          }
        }
    .contInput {
      display: inline-block;
      float: left;
      width: 100%;
      padding: 10px 0 10px 20px;
      position: relative;

      &:nth-of-type(-n+4) {
        width: calc((100% - 20px)/2);
      }

      &:nth-of-type(-n+2) {
        padding-top: 20px;
      }

      &:nth-of-type(3), &:nth-of-type(4), &:nth-of-type(5), &:nth-of-type(6) {
        width: calc((100% - 20px)/2);
        .fa {
          top:15px;
        }
      }

      &:nth-of-type(7) {
        .fa {
          top:15px;
        }
      }

      &:nth-of-type(5), &:nth-of-type(7) {
        margin-right: 0;
      }

      &:nth-of-type(8), {
        padding: 0 20px 20px;
        input[type=file] {
          position: absolute;
          left: 20px;
          right: 20px;
          top: 5px;
          padding-top: 10px;
          padding-left: 400px;
        }
        .fa {
          top: 5px;
        }
      }

      .fa {
        width: 40px;
        height: 40px;
        color: $corporate_2;
        position: absolute;
        top: 25px;
        left: 20px;
        z-index: 2;

        &:before {
          @include center_xy;
        }
      }

      input {
        width: 100%;
        height: 50px;
        padding-left: 40px;
        border: 0;

        &#accept-term {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }
      }

      textarea {
        width: calc(100% - 20px);
        padding-left: 40px;
        padding-top: 20px;
        border-color: transparent;
      }
    }
    .policy-terms {
      text-align: center;
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: #999;
    }

    #contact-button {
      display: block;
      margin: auto;
      width: calc(100% - 40px);
      background: $corporate_1;
      color: white;
      padding: 20px 0;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 100;
      margin-bottom: 10px;
      cursor: pointer;
      &:hover {
        background-color: darken($corporate_1,10%);
      }
    }
  }

  #cv_hotel_selector {
    -webkit-appearance: none;
    width: 100%;
    height: 50px;
    padding-left: 40px;
    border: 0;
    border-radius: 0;
    background: white;

    & + .fa {
      left: auto !important;
      right: 0;
    }
  }
}