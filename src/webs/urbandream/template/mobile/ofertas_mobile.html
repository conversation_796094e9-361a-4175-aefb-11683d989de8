<div class="top_content_wrapper container12">

        <div class="scapes-blocks">
            {% for block in blocks %}

                {% if i_am_mobile %}
                 <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                        <img src="{{block.picture}}=s1000">
                    </a>
                {% endif %}

                <div class="block {% cycle 'row1' 'row2' %}">
                    <div class="description">
                        <h3 class="title-module">{{block.promo_title|safe}}</h3>
                    {% if i_am_mobile %}
                        <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}">
                            {{block.description|safe}}
                        </div>
                    {% endif %}

                        <ul>
                            <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion oferta-reserva">{{ T_reservar }}</a> </li>

                            <li><a href="#event-modal-{{ forloop.counter }}" class="plus myFancyPopupAuto">{{ T_ver_mas }}</a></li>

                        </ul>
                    </div>
                {% if not i_am_mobile %}
                    <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %} enlace_offer">
                        <img src="{{block.picture}}=s1000" alt="{{block.name|safe}}" title="{{block.name|safe}}">
                    </a>
                {% endif %}

                {% if not i_am_mobile %}
                    <div id="event-modal-{{ forloop.counter }}" class="mobiledescription event-modal-mobile-{{ forloop.counter }}" style="display: none">
                        {{block.description|safe}}
                    </div>
                {% endif %}

                </div>

            {% endfor %}

        </div>


    </div>