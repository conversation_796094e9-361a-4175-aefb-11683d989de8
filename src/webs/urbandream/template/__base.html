<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <link href='https://fonts.googleapis.com/css?family=Oswald:400,700,300' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Lato:400,700,300' rel='stylesheet' type='text/css'>
    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ sectionName|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
    {% endif %}

    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <script type="text/javascript">
        if (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPad/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) {
            document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
        }
    </script>

    <!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

    <!--[if lte IE 8]>
<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->


    <!-- jquery -->
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>

    <!-- Font Awesome for icons -->
    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
          integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">


    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen"/>
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen"/>




    {% if datepicker_theme %}
        <link type="text/css" rel="stylesheet"
              href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
        <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"/>
    {% endif %}

    <!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>


    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css"/>
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=3.63"/>


    <!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

    <!--[if IE 9]>
<style>

.inline .rooms_number .selectric .label {
    left:20px;
}
.inline .room .selectric .label{
    top:18px;
    left:10px;
    display:block !important;
}


</style>
<![endif]-->

    {{ extra_head|safe }}

</head>

<body itemscope itemtype="//schema.org/Hotel" class="{{ namespace }}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}


<meta itemprop="description" content="{{ description_microdata }}">


{% block content %}

    <!--EDIT HERE YOUR PAGE-->

{% endblock %}


<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>


{%  if google_analytics_id %}
    <!-- Google Analytics -->
    <script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', '{{ google_analytics_id }}', 'auto');
    ga('send', 'pageview');

    </script>
    <!-- End Google Analytics -->
{%  endif %}

{% block additional_js %}



    <!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{ language_code }}.js" type="text/javascript"></script>

    <script type="text/javascript" src="/static_1/scripts/common.js"></script>

    <!-- lightbox -->
    <script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <script src="/static_1/lib/selectric/jquery.selectric.min.js" type="text/javascript"></script>

    <!-- new booking engine -->
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css?v=3">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css?v=1.111">
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js?v=1"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js?v=1.123"></script>
    <script>$(function(){DP_extend_info.config.booking_version = '3';DP_extend_info.init()})</script>
    <script src="/static_1/scripts/booking_2.js?v=4"></script>
    <script type="text/javascript" src="/static_1/lib/spin.min.js"></script>

    <!-- My specific js  -->
    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js?v=23.11"></script>
    <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>

    <!-- KenBurn Slider ALWAYS AT THE END!!!!!!!! -->
    <!-- jQuery KenBurn Slider  -->
    <script type="text/javascript"
            src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript"
            src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.revolution.js"></script>

    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>


{% endblock %}




{% if sectionToUse.sectionType == 'Inicio' or inner_popup %}
    <script>
        {% if popup_inicio_automatico %}
            $(document).ready(function () {
                {% if cookies_on_popup_inicio_automatico %}

                    window.setTimeout(function () {
                        {% if inner_popup %}
                            if (searchCookie("anuncio_fancy_{{ sectionToUse.sectionName|slugify }}_{{ language }}=1")) {
                        {% else %}
                            if (searchCookie("anuncio_fancy_{{ language }}")) {
                        {% endif %}
                        }
                        else {
                            $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});
                            {% if inner_popup %}
                                document.cookie = "anuncio_fancy_{{ sectionToUse.sectionType|slugify }}_{{ language }}=1";
                            {% else %}
                                document.cookie = "anuncio_fancy_{{ language }}=1";
                            {% endif %}
                        }
                    }, 800);

                {%  else %}
                    $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});
                {% endif %}
            });
        {% endif %}
    </script>
    {% if popup_inicio_automatico %}
        {% if popup_inicio_automatico.0.servingUrl %}
            <div style="display:none">
                <div class="popup_inicio" style="position:relative;">
                    {% if popup_inicio_automatico.0.linkUrl %}
                        <a href="{{ popup_inicio_automatico.0.linkUrl }}">{% endif %}<img
                            src="{{ popup_inicio_automatico.0.servingUrl }}=s850">
                    {% if popup_inicio_automatico.0.linkUrl %}</a>{% endif %}
                </div>
            </div>
        {% endif %}
    {% endif %}
{% endif %}

{% if sectionToUse.sectionType == 'Inicio' %}

    {% if bottom_popup  %}
    <div class="bottom_popup" style="display: none">


     <div class="picture-bigskirt">
            <img src="{{ bottom_popup.pictures.0|safe }}" alt="close" title="close"/>
        </div>
        <div class="close_button">
            <img src="/static_1/images/close.png" alt="close" title="close"/>
        </div>
            <div id="wrapper2">
                <div class="bottom_popup_text">{{ bottom_popup.content|safe }}</div>
            </div>
    </div>

    {%  endif %}

{% endif %}



<div style="display: none;">
    <div id="data">
        <div id="wrapper_booking_fancybox">
            <div id="booking_widget_popup" class="booking_widget_fancybox">
                {{ booking_engine }}
            </div>
        </div>
    </div>
</div>

<script async>
    $(function () {
        try {
            ga(function (tracker) {
                client_id = tracker.get('clientId');
            });

            $(".paraty-booking-form").each(function () {
                var analytics_user_input = $('<input>').attr({
                                                                 type: 'hidden',
                                                                 id: '_ga',
                                                                 name: '_ga',
                                                                 value: client_id
                                                             });

                $(this).append(analytics_user_input)

            })
        } catch (err) {
            console.log("Google Analytics defined incorrectly (May use a old version)");
        }
    })
</script>


{{ extra_content_website|safe }}
</body>
</html>
