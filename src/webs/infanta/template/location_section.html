<div class="location_section_wrapper">
    {% if pictures.0.servingUrl %}
        <div class="image_location_wrapper">
            <img class="location_image" src="{{ pictures.0.servingUrl }}=s1900">
        </div>
    {% else %}
        <div class="container12" style="padding: 20px 0;"></div>
    {% endif %}


    <div class="location_wrapper_text">
        <h3 class="location_title">{{ content_location.subtitle|safe }}</h3>

        <div class="location_content">{{ content_location.content|safe }}</div>
    </div>
</div>


</div>

<div class="contact_iframe_background">
    <div class="contact_iframe_wrapper container12">
        <div class="contact_form">
            {{ contact_html|safe }}
{#            {% if captcha_box %}#}
{#                    <div class="contInput captcha">#}
{#                        <script src='https://www.google.com/recaptcha/api.js'></script>#}
{#                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>#}
{#                    </div>#}
{#                {% endif %}#}
        </div>
        <div class="iframe_wrapper">
            <iframe src="https://www.google.com/maps?q={{ google_maps }}&output=embed&maptype=satellite"
                    style="width: 100%;height: 635px;" frameborder="0" class="mapa"></iframe>
        </div>
    </div>
</div>
{#<script type="text/javascript">#}
{#    $(window).load(function () {#}
{##}
{#        $("#contact-button").click(function () {#}
{#            var form = $(this).closest("#contact");#}
{##}
{#            if (form.valid()) {#}
{#                var form_data = form.serialize();#}
{#                if ($("#g-recaptcha-response").val()) {#}
{#                $.post(#}
{#                    "/utils/?action=contact", form_data,#}
{#                    function (data) {#}
{#                        alert($.i18n._("gracias_contacto"));#}
{#                        form.trigger("reset");#}
{#                    }#}
{#                );#}
{#                } else {#}
{#                      $(".g-recaptcha > div").css('border', '1px solid red');#}
{#                    }#}
{#                }#}
{#        });#}
{##}
{##}
{#    });#}
{#</script>#}

<div id="wrapper_content" class="container12">