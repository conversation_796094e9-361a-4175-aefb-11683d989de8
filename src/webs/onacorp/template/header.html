<header>
    <div class="float_left">
        <div class="menu_toggle"><span>Menu</span></div>
        <a href="{{host|safe}}/" id="logoDiv">
            {% if inner_logo %}
                <img itemprop="logo" src="{{ inner_logo.0 }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/><span>o</span>
            {% else %}
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/><span>o</span>
            {% endif %}
        </a>
        {% if hotel_info and hotel_info.title %}
            <div class="hotel_info_header">
                <h5>{{hotel_info.title|safe}}
                    <span class="stars">{% for foo in range(hotel_info.stars) %}<i class="fas fa-star"></i>{% endfor %}</span>
                </h5>
                <span class="body2">
                    {% if hotel_info.country %}{{ hotel_info.country|safe }},{% endif %}
                    {% if hotel_info.destiny %}{{ hotel_info.destiny|safe }}{% endif %}
                </span>
            </div>
        {% endif %}
    </div>
    <div class="float_right">
        <div id="top-sections"><i class="fas fa-info-circle"></i>
            {% for section in top_sections %}
                <a href="{{ host|safe }}/{{ section.friendlyUrl }}"><span>{{ section.title|safe }}</span></a>
                {%if not loop.last %}<span class="separator"></span>{% endif %}
            {% endfor %}
            {% if phone_contact %}<div class="phone {% if phone_contact_popup %}open_popup_phone{% else %}toolkit{% endif %}">
                <i class="fal fa-phone"></i>
                {% if phone_contact_popup %}
                    <div class="popup_phone" style="display: none">
                    <div class="icon-xcross close_popup_phone"></div>
                    <div class="center_xy content">
                        {% if phone_contact_popup.subtitle %}
                            <div class="subtitle">{{ phone_contact_popup.subtitle|safe }}</div>
                        {% endif %}
                        {% if phone_contact_popup.content %}
                            <div class="desc">{{ phone_contact_popup.content|safe }}</div>
                        {% endif %}
                    </div>
                    </div>
                {% else %}
                    <span class="tooltip">{{ phone_contact }}</span>
                {% endif %}

            </div>{% endif %}
            <div class="toolkit language_icon">
                <i class="fal fa-globe"></i>
                <span class="tooltip">{% for key, value in language_codes.items() %}
                    <a href="{{ hostWithoutLanguage }}/{% if not key == 'es' %}{{ key }}/{% endif %}" {% if  key == language %} class="selected" {% endif %}>
                       {{ value|upper }}
                    </a>
                {% endfor %}</span>
            </div>
{#            <div class="search">#}
{#                <i class="far fa-search"></i>#}
{#            </div>#}
            <div class="user">
                <label><span>1</span></label>
                <i class="fal fa-user-circle"></i>

                <div class="popup" {% if not home %}style="display: none;" {% endif %}>
                    <div class="close icon-xcross"></div>
                    {% include "svg/bubble1.html" %}

                    {% for message in user_messages %}<a {% if message.linkUrl %}href="{{ message.linkUrl }}"{% endif %} class="content">
                        {% if message.title == "login" %}{{message.description|safe}}{% endif %}
                    </a>{% endfor %}

                </div>
            </div>
            <div class="booking_button">
                <a href="#data" class="button-promotion"
                {% if hotel_info and hotel_info.namespace %}
                    data-namespace="{{ hotel_info.namespace }}"
                {%  endif %}>{{ T_reservar }}</a>
            </div>
        </div>
    </div>
</header>
{% include "main_div.html" %}