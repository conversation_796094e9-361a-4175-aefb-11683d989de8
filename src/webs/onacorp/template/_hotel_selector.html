<div class="destination_wrapper">
  <div class="destination_fieldo">
    <input class="destination" readonly="readonly" type="text" name="destination" placeholder="{{ T_hotel }} - {{ T_destino }}">
    <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="">
  </div>
</div>

<div class="hotel_selector">
    <div class="close icon-xcross"></div>
    <div class="center_xy">
        <div class="hotel_selector_search">
            <i class="fas fa-search"></i>
            <input type="text" class="searching_hotel" placeholder="{{T_buscar }} {{ T_hotel }}/{{ T_destino }}">
        </div><div class="hotel_selector_filter tabs">
            <a href="#" class="destiny_filter tab selected" data-destiny="destiny_selector_inner">{{ T_destinos }}</a>
            <a href="#" class="destiny_filter tab" data-destiny="hotel_selector_inner">{{ T_hoteles }}</a>
            <a href="#" class="destiny_filter tab load_map" data-destiny="map_selector_inner">{{ T_ver_mapa }}</a>
        </div><div class="destiny_selector_inner active">
            {% for pais, group_dict in hotels_grouped.items() %}
            {% for destiny, value in group_dict.destinies.items() %}
                <div class="destiny"><a href="#" class="destiny_tag {% for country, hotel_list in value.countries.items() %}{% for hotel in hotel_list %}{{ hotel.namespace }} {% endfor %}{% endfor %}" data-destiny="{{ value.class_name }}">{{ destiny|safe }} <span class="n_hotels" data-nhoteles="{{ value.n_hoteles }}">({{value.n_hoteles}} {% if value.n_hoteles > 1 %}{{T_hoteles}}{% else %}{{T_hotel}}{% endif %})</span> <i class="fal fa-plus"></i></a>
                    <div class="countries">
                        {% for country, hotel_list in value.countries.items() %}
                        <a href="#" class="country {% for hotel in hotel_list %}{{ hotel.namespace }} {% endfor %}">{{country|safe}}</a>
                            <ul>{% for hotel in hotel_list %}
                                <li id="{{ hotel.namespace }}" class="{{ hotel.namespace }} filter_all filter_{{ hotel.destiny_class }} hotel_selector_option" hotel_type="{{ hotel.category_class|safe }}">
                                    <span class="title_selector">{{ hotel.value|safe }}</span>
                                </li>
                            {% endfor %}</ul>
                        {% endfor %}
                        {% if value.n_hoteles > 1 %}
                        <span class="booking_0_hotel_selection" namespaces="{% for country, hotel_list in value.countries.items() %}{% for hotel in hotel_list %}{{ "r__" if not "ona" in hotel.namespace }}{{ hotel.namespace }}{{ ';' if not loop.last }}{% endfor %}{{ ';' if not loop.last }}{% endfor %}" hotel_name="{{ destiny|safe }}">
                            {{ T_todos_los_alojamiento }}
                        </span>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
            {% endfor %}
        </div><div class="hotel_selector_inner">
            <ul>{% for hotel in hotels %}
                <li id="{{ hotel.namespace }}" class="{{ hotel.namespace }} filter_all filter_{{ hotel.destiny_class }} hotel_selector_option"
                lat="{{ hotel.poi_lat|safe }}" lng="{{ hotel.poi_lng|safe }}">
                    <span class="title_selector">{{ hotel.value|safe }}</span>
                </li>
                <input type="hidden" id="url_booking_{{ hotel.namespace }}" value="{{ hotel.url_booking }}">
                <input type="hidden" id="namespace_{{ hotel.namespace }}" value="{{ hotel.namespace }}">
            {% endfor %}</ul>
        </div><div class="map_selector_inner">
            <div id="map" class="map_selector_wrapper"></div>
            {% for hotel in hotels %}
                <div class="hotel_card {{ hotel.namespace }}" style="display: none">
                    <h5 class="title_selector">{{ hotel.value|safe }}</h5>
                    <div class="subtitle1">{% if hotel.country != hotel.destiny %}{{hotel.country|safe}}, {% endif %}{{hotel.destiny|safe}}</div>
                    <div class="bottom">
                        <h5 class="price"><span>{{ hotel.price_label|safe }}</span><br>{{ hotel.price|safe }}€</h5>
                        <a href="#" data-namespace="{{ hotel.namespace }}" class="button-promotion btn btn-rounded-aqua btn-small">{{ T_reservar }}</a>
                    </div>

                </div>
            {% endfor %}
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.2.1/css/ol.css"
      type="text/css">
<script src="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.2.1/build/ol.js"></script>
<script src="/js/onacp/maps_controller.js" type="text/javascript"></script>

<script type="text/javascript">
    $(window).load(function () {
        $(".load_map").click(function () {
            $("#map").html("");
            setTimeout(function (){
                MapController.initialize_map();
                MapController.initialize_markers(retreive_hotel_markers());
            },500);
        });
    });
    function retreive_hotel_markers(){
        var markers = [];
        $(".hotel_selector_inner li").each(function(){
            var lat = $(this).attr('lat'),
                lng = $(this).attr('lng'),
                namespace = $(this).attr('id');
            markers.push({lat: lat, lng: lng, namespace: namespace})
        });
        return markers
    }
</script>
