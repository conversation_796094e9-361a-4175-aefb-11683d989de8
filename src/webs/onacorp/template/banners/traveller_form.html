<div class="contact_form_wrapper travel_form_wrapper {% if travel_form_ticks %}travel_form_ticks{% endif %}">
    {% if travel_form_ticks %}
        <div class="travel_form_ticks">
            {% for tick_element in travel_form_ticks %}
                <div class="tick_element">
                    <img src="{{ tick_element.servingUrl }}" alt="">
                    <div class="content">
                        <div class="title">{{ tick_element.title|safe }}</div>
                        <div class="description">{{ tick_element.description|safe }}</div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <form name="traveller_form" id="traveller_form" method="post" onsubmit="return false">
        <div class="info">
            <div class="contInput">
                <label>{{ T_email }}*</label>
                <input type="email" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}" required/>
            </div>
            <div class="contInput">
                <label>{{ T_nombre }}*</label>
                <input type="text" id="nombre" name="nombre" class="bordeInput" value="" placeholder="{{ T_nombre }}" required/>
            </div>
            <div class="contInput">
                <label>{{ T_apellidos }}*</label>
                <input type="text" id="apellido" name="apellido" class="bordeInput" value="" placeholder="{{ T_apellidos }}" required/>
            </div>
            <div class="contInput">
                <label>{{ T_titulo_personal }}*</label>
                <div class="select"><select class="form-control required sb-personal-selection" id="personal_title" name="personal_title">
                    <option value="Mr">Mr.</option>
                    <option value="Mrs">Mrs.</option>
                </select></div>
            </div>
            <div class="contInput">
                <label>{{ T_fecha_nacimiento }}</label>
                <input type="date" id="birthday" name="birthday" class="bordeInput" placeholder="{{ T_fecha_nacimiento }} (dd/mm/yyy)" value=""/>
            </div>
            <div class="contInput">
                <label for="telephone" class="title">{{ T_telefono }}</label>
                <input type="text" id="telephone" name="telephone" class="bordeInput" placeholder="{{ T_introduzca_num_tel }}" value=""/>
            </div>
            <div class="contInput">
                <label>{{ T_pais }}*</label>
                <div class="select">
                <select placeholder="¿Dónde vives?" class="form-control required sb-country-selection" id="country" name="country">
                <option value="ES">
                    SPAIN </option>
                <option value="US">
                    U.S.A </option>
                <option value="GB">
                    UNITED KINGDOM </option>
                <option value="AD">
                    ANDORRA </option>
                <option value="AE">
                    UNITED ARAB EMIRATES </option>
                <option value="AF">
                    AFGHANISTAN </option>
                <option value="AG">
                    ANTIGUA AND BARBUDA </option>
                <option value="AI">
                    ANGUILA </option>
                <option value="AL">
                    ALBANIA </option>
                <option value="AM">
                    ARMENIA </option>
                <option value="AN">
                    NETHERLANDS ANTILLES </option>
                <option value="AO">
                    ANGOLA </option>
                <option value="AQ">
                    ANTARCTICA </option>
                <option value="AR">
                    ARGENTINA </option>
                <option value="AS">
                    AMERICAN SAMOA </option>
                <option value="AT">
                    AUSTRIA </option>
                <option value="AU">
                    AUSTRALIA </option>
                <option value="AW">
                    ARUBA </option>
                <option value="AX">
                    ALAND ISLANDS </option>
                <option value="AZ">
                    AZERBAIYÁN </option>
                <option value="BA">
                    BOSNIA AND HERZEGOVINA </option>
                <option value="BB">
                    BARBADOS </option>
                <option value="BD">
                    BANGLADESH </option>
                <option value="BE">
                    BELGIUM </option>
                <option value="BF">
                    BURKINA FASO </option>
                <option value="BG">
                    BULGARIA </option>
                <option value="BH">
                    BAHREIN </option>
                <option value="BI">
                    BURUNDI </option>
                <option value="BJ">
                    BENIN </option>
                <option value="BL">
                    SAINT BARTHÃ © LEMY </option>
                <option value="BM">
                    BERMUDA SHORTS </option>
                <option value="BN">
                    BRUNEI DARUSSALAM </option>
                <option value="BO">
                    BOLIVIA </option>
                <option value="BQ">
                    BONAIRE, SINT EUSTATIUS AND SABA </option>
                <option value="BR">
                    BRAZIL </option>
                <option value="BS">
                    BAHAMAS </option>
                <option value="BT">
                    BHUTAN </option>
                <option value="BV">
                    BOUVET ISLAND </option>
                <option value="BW">
                    BOTSWANA </option>
                <option value="BY">
                    BELARUS </option>
                <option value="BZ">
                    BELIZE </option>
                <option value="CA">
                    CANADA </option>
                <option value="CC">
                    COCOS ISLANDS (KEELING) </option>
                <option value="CD">
                    CONGO DR </option>
                <option value="CF">
                    CENTRAL AFRICAN REPUBLIC </option>
                <option value="CG">
                    CONGO </option>
                <option value="CH">
                    SWITZERLAND </option>
                <option value="CI">
                    IVORY COAST </option>
                <option value="CK">
                    COOK ISLANDS </option>
                <option value="CL">
                    CHILE </option>
                <option value="CM">
                    CAMEROON </option>
                <option value="CN">
                    CHINA </option>
                <option value="CO">
                    COLOMBIA </option>
                <option value="CR">
                    COSTA RICA </option>
                <option value="CU">
                            </select></div>
            </div>
            <div class="contInput">
                <label>{{ T_idioma }}*</label>
                <div class="select"><select class="form-control required sb-language-selection" id="newsletter_language" name="newsletter_language">
                    <option value="ING">English</option>
                    <option value="ES">Spanish</option>
                    <option value="FRA">French</option>
                    <option value="RUS">Russian</option>
                </select></div>
            </div>

            {% if captcha_box %}
                <div class="contInput captcha">
                    <script src='https://www.google.com/recaptcha/api.js'></script>
                    <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                </div>
            {% endif %}
            <div class="contInput policy-terms">
                <div>{{ T_campos_obligatorios }}</div>
                <input type="checkbox" id="accept-term" name="accept_term" required/>
                <a class="myFancyPopup fancybox.iframe"
                   href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                <div class="promotions">
                    <input class="check_privacy" id="promotions" name="promotions" type="checkbox" value="promotions" required/>
                    <span for="promotions">{{T_acepto_promociones}}</span>
                </div>
            </div>
            <button id="contact-button" class="btn">{{ T_enviar }}</button>
        </div>
    </form>
</div>
<div class="thanks_wrapper">
    <div class="close_thanks"><i class="fal fa-times"></i></div>
    <span>{{ T_gracias_club }}</span>
</div>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">
    $(window).load(function () {
        $("#traveller_form").validate({
            rules: {
                newsletter_language: "required",
                country: "required",
                nombre: "required",
                personal_title: "required",
                email: "required",
                apellido: "required"
            },
            messages: {
                newsletter_language: "{{ T_campo_obligatorio }}",
                country: "{{ T_campo_obligatorio }}",
                personal_title: "{{ T_campo_obligatorio }}",
                nombre: "{{ T_campo_obligatorio }}",
                email: "{{ T_campo_obligatorio }}",
                apellido: "{{ T_campo_obligatorio }}"
            },
            submitHandler: function (form) {
                var form_data = $(form);
                console.log("VOY");
                if ($("#g-recaptcha-response").length < 1 || $("#g-recaptcha-response").val()) {
                    console.log("ENTRA");
                    $.ajax({
                        type: "POST",
                        url: "/utils?action=newsletter",
                        data: form_data.serialize(),
                        success: function(data) {
                            $(".thanks_wrapper").addClass("active");
                            $("#email").val("");
                            $("#personal_title").val("");
                            $("#nombre").val("");
                            $("#telephone").val("");
                            $("#birthday").val("");
                            $("#apellido").val("");
                            $("#newsletter_language").val("");
                            $("#country").val("");
                            {% if is_mobile %}
                                $(".paraty-booking-form").append("<input type='hidden' id='logged_tabs' name='logged_tabs' value='true'>");
                                createCookie('travel_user', true);
                            {% else %}
                                $(".booking_form").append("<input type='hidden' id='logged_tabs' name='logged_tabs' value='true'>");
                                createCookie('travel_user', true);
                            {% endif %}
                        },
                        error: function(data) {
                            console.log(data);
                        },
                    });
                } else {
                  $(".g-recaptcha > div").css('border', '1px solid red');
                }
            }
        });
        $(".thanks_wrapper .close_thanks").on("click", function () {
            $(".thanks_wrapper").removeClass("active");
        });
    });
</script>