<div class="contact_form_wrapper">
    <form name="contact" id="contact" method="post" action="/utils/?action=contact">
        <h3>{% if hotel_info.table_book_text %}{{ hotel_info.table_book_text|safe }}{% else %}Reservar Mesa{% endif %}</h3>
        <input type="hidden" name="action" id="action" value="contact"/>
        <div class="info">
            <input type="hidden" name="section" id="section-name" value="{% if hotel_info.table_book_text %}
                    {{ hotel_info.table_book_text|safe }}{% else %}Reservar Mesa{% endif %} - {{ sectionName|safe }}"/>
            <div class="contInput">
                <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}" required/>
            </div>
            <div class="contInput">
                <input type="email" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}" required/>
            </div>
            <div class="contInput">
                <input type="number" id="telephone" name="telephone" class="bordeInput" value="" placeholder="{{ T_telefono }}" required/>
            </div>
            <div class="contInput">
                <input type="text" id="check_date" name="check_date" class="bordeInput datepicker" value="" placeholder="{{ T_fecha }}" required/>
            </div>
            <div class="contInput">
                <select name="arrive_time" id="arrive_time" class="select2">
                    <optgroup label="{{ T_dia }}">
                        <option value="12:00">12:00</option>
                        <option value="12:30">12:30</option>
                        <option value="13:00">13:00</option>
                        <option value="13:30">13:30</option>
                        <option value="14:00">14:00</option>
                        <option value="14:30">14:30</option>
                        <option value="15:00">15:00</option>
                        <option value="15:30">15:30</option>
                        <option value="16:00">16:00</option>
                    </optgroup>
                    <optgroup label="{{ T_noche }}">
                        <option value="19:00">19:00</option>
                        <option value="19:30">19:30</option>
                        <option value="20:00">20:00</option>
                        <option value="20:30">20:30</option>
                        <option value="21:00">21:00</option>
                        <option value="21:30">21:30</option>
                        <option value="22:00">22:00</option>
                        <option value="22:30">22:30</option>
                        <option value="23:00">23:00</option>
                    </optgroup>
                </select>
            </div>
            <div class="contInput contInputx2 area">
                <textarea type="text" id="comments" name="comments" class="bordeInput" value="" placeholder="{{ T_comentarios }}" required></textarea>
            </div>
            {% if captcha_box %}
                <div class="contInput captcha">
                    <script src='https://www.google.com/recaptcha/api.js'></script>
                    <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                </div>
            {% endif %}
            <div class="contInput policy-terms">
                <div>{{ T_campos_obligatorios }}</div>
                <input type="checkbox" id="accept-term" name="accept_term" required/>
                <a class="myFancyPopup fancybox.iframe"
                   href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
            </div>
            <button id="contact-button" class="btn" onClick="return false;">{{ T_enviar }}</button>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(window).load(function () {

        $("#contact-button").click(function () {
            var form = $(this).closest("#contact");

            if (form.valid()) {
                var form_data = form.serialize();
                if ($("#g-recaptcha-response").val()) {
                $.post(
                    "/utils/?action=contact", form_data,
                    function (data) {
                        alert($.i18n._("gracias_contacto"));
                        form.trigger("reset");
                    }
                );
                } else {
                      $(".g-recaptcha > div").css('border', '1px solid red');
                    }
                }
        });
    });
</script>