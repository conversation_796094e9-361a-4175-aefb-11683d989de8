<section class="baner_hotels">
    {% if section.subtitle %}<div class="title">{{ section.subtitle|safe }}</div>{% endif %}
    {% if section.content %}<div class="desc">{{ section.content|safe }}</div>{% endif %}
    {% if hotels_filters_filters %}
    <div class="hotels_filters filters">
        <a href="#{{ hotels_class }}" class="filter selected">{{ T_destacados }}</a>
        {% for filter, value in hotels_filters_filters.items() %}
            <a href="#{{ filter }}" class="filter">{{ value|safe }}</a>
        {% endfor %}
        <a href="{{ hotels_link }}" class="filter all">{{ T_ver_todos }}</a>
    </div>
    {% endif %}
    <div class="hotels_wrapper active {% if hotels|length > 4 %}owl-carousel{% endif %}" id="{{ hotels_class }}">
        {% for hotel in hotels %}<div class="hotel">
            <div class="pics owl-carousel">{% for pic in hotel.gallery[:5] %}
                <a href="{{hotel.friendlyUrlInternational}}" rel="lightbox[{{ hotel.namespace }}]" class="pic">
                    <img src="{{pic.servingUrl}}=s450-c" alt="{{pic.altText}}">
                </a>
            {% endfor %}</div>
            <div class="main_content">
                <a href="{{hotel.friendlyUrlInternational}}" class="overlay_link"></a>
                <a href="#{{ hotel.namespace }}" class="hotel_fav"><i class="fas fa-heart"></i></a>
                <div class="subtitle1">{% if hotel.country != hotel.destiny %}{{hotel.country|safe}}, {% endif %}{{hotel.destiny|safe}}</div>
                <h3 class="hotel_card_title"><b>{{hotel.value|safe}}</b></h3>
{#                  <div class="subtitle2">{{hotel.subtitle|safe}}</div>#}
                <div class="price">
                    <label>{{hotel.price_label|safe}}</label><br>
                    {{hotel.price|safe}}<span>€</span>
                    {% if hotel.score %}<div class="score">{{hotel.score}}<span>/10</span></div>{% endif %}
                </div>
                <div class="links">
                    <a href="{{hotel.friendlyUrlInternational}}">
                        <i class="fas fa-info"></i>
                        <span>{{T_ver_mas}}</span>
                    </a>
                    <a href="#map_{{ hotels_class }}{{ loop.index }}" class="see_map_hotel">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{T_mapa}}</span>
                    </a>
                    {% for pic in hotel.gallery[:5] %}
                    <a href="{{ pic.servingUrl }}=s1900" rel="lightbox[g{{ hotel.namespace }}]" {% if not loop.first %}style="display: none;"{% endif %}>
                        {% if loop.first %}
                            <i class="fas fa-image-polaroid"></i>
                            <span>{{T_fotos}}</span>
                        {% endif %}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>{% endfor %}
    </div>
{% if hotels_filters %}
    {% for filter, hotels_filter in hotels_filters.items() %}
    <div class="hotels_wrapper {% if hotels_filter|length > 4 %}owl-carousel{% endif %}" id="{{ filter }}" style="display: none">
        {% for hotel in hotels_filter %}<div class="hotel">
            <div class="pics owl-carousel">{% for pic in hotel.gallery[:5] %}
                <a href="{{hotel.friendlyUrlInternational}}" rel="lightbox[{{ hotel.namespace }}]" class="pic">
                    <img src="{{pic.servingUrl}}=s450-c" alt="{{pic.altText}}">
                </a>
            {% endfor %}</div>
            <div class="main_content">
                <a href="{{hotel.friendlyUrlInternational}}" class="overlay_link"></a>
                <a href="#{{ hotel.namespace }}" class="hotel_fav"><i class="fas fa-heart"></i></a>
                <div class="subtitle1">{% if hotel.country != hotel.destiny %}{{hotel.country|safe}}, {% endif %}{{hotel.destiny|safe}}</div>
                <h3 class="hotel_card_title"><b>{{hotel.value|safe}}</b></h3>
{#                  <div class="subtitle2">{{hotel.subtitle|safe}}</div>#}
                <div class="price">
                    <label>{{hotel.price_label|safe}}</label><br>
                    {{hotel.price|safe}}<span>€</span>
                    {% if hotel.score %}<div class="score">{{hotel.score}}<span>/10</span></div>{% endif %}
                </div>
                <div class="links">
                    <a href="{{hotel.friendlyUrlInternational}}">
                        <i class="fas fa-info"></i>
                        <span>{{T_ver_mas}}</span>
                    </a>
                    <a href="#map_{{ filter }}{{ loop.index }}" class="see_map_hotel">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{T_mapa}}</span>
                    </a>
                    {% for pic in hotel.gallery[:5] %}
                    <a href="{{ pic.servingUrl }}=s1900" rel="lightbox[g{{ hotel.namespace }}]" {% if not loop.first %}style="display: none;"{% endif %}>
                        {% if loop.first %}
                            <i class="fas fa-image-polaroid"></i>
                            <span>{{T_fotos}}</span>
                        {% endif %}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>{% endfor %}
    </div>
    {% endfor %}
{% endif %}
</section>
{% for hotel in hotels %}{% if hotel.iframe_map and not is_mobile %}<div class="map_wrapper" id="map_{{ hotels_class }}{{ loop.index }}">
    <div class="close_map"><i class="fal fa-times"></i></div>
    <div class="map">
        {{ hotel.iframe_map|safe }}
    </div>
</div>{% endif %}{% endfor %}
{% if hotels_filters %}
{% for filter, hotels_filter in hotels_filters.items() %}
{% for hotel in hotels_filter %}{% if hotel.iframe_map and not is_mobile %}<div class="map_wrapper" id="map_{{ filter }}{{ loop.index }}">
    <div class="close_map"><i class="fal fa-times"></i></div>
    <div class="map">
        {{ hotel.iframe_map|safe }}
    </div>
</div>{% endif %}{% endfor %}
{% endfor %}
{% endif %}
<script>$(window).load(function () {
    $(".baner_hotels .hotels_wrapper .hotel").each(function(){
        var hotelPicsCarousel = $(this).find('.pics'),
            interval;
        hotelPicsCarousel.owlCarousel({
            loop: true,
            nav: false,
            dots: true,
            items: 1,
            margin: 0,
            mouseDrag: false,
            touchDrag: false,
            pullDrag: false,
            freeDrag: false,
            autoplay: false
        });
        $(this).hover(
            function(){
                interval = setInterval(function() {
                    hotelPicsCarousel.trigger('next.owl.carousel');
                    $(".baner_hotels .hotels_wrapper").trigger('prev.owl.carousel');
                }, 5000);
            },
            function(){
                clearInterval(interval);
            }
        );
    });
    var banner_hotels_carouse_params = {
        loop: true,
        nav: true,
        dots: false,
        navText: ['<i class="fal fa-angle-left"></i>', '<i class="fal fa-angle-right"></i>'],
        margin: 8,
        autoplay: false,
        responsive : {
            0 : {
                items : 1
            },
            1140 : {
                items : 3
            },
            1200 : {
                items : 4
            }
        }
    };
    {% if hotels|length > 4 %}$(".baner_hotels .hotels_wrapper.active").owlCarousel(banner_hotels_carouse_params);{% endif %}
    {% if hotels_filters_filters %}
        $(".hotels_filters .filter:not(.all)").click(function (e) {
            e.preventDefault();
            let target_banner = $(this).attr("href");
            $(this).addClass("selected").siblings().removeClass("selected");
            $(target_banner).addClass("active").siblings().removeClass("active");
            $(".hotels_wrapper").fadeOut().promise().done(function () {
                if($(target_banner).find(".hotel").length > 4) {
                    $(target_banner).owlCarousel(banner_hotels_carouse_params);
                }
                $(target_banner).fadeIn();
            });
        });
    {% endif %}
});</script>