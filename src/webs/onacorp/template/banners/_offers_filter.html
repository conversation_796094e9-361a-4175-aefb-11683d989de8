<section class="offers_filter {% if cascade %}offers_filter_cascade{% endif %}" id="{{ section.title|safe }}">
    {% if content_section.subtitle %} {{content_section.subtitle|safe}} {% else %}<h3>{{ T_ofertas_destacadas }}</h3>{% endif %}
    {% if content_section.description %} {{content_section.description|safe}} {% endif %}
    {% if filters %}<div class="filters">
        {% for x in filters %}
            <a href="{% if x.linkUrl %}{{ x.linkUrl }}{% else %}#{% endif %}" data-offer-filter="{% if x.description %}{{ x.description|safe }}{% else %}all{% endif %}" class="filter {% if loop.first %}selected{% endif %}">{{x.title|safe}}</a>
        {% endfor %}
        {% if link_ofertas %}
            <a href="{{ link_ofertas.friendlyUrlInternational }}" class="filter">{{ T_todas }}</a>
        {% endif %}
    </div>{% endif %}
    <div class="offers_wrapper {% if not cascade %}owl-carousel {% endif %}">

    </div>

    <div class="hidden_offer_filters_banners hide_element">
        {% if extra_banner %}{{ extra_banner|safe }}{% endif %}
        {% for offer in offers %}
            <div class="offer {% if offer.size %}{{offer.size|safe}}{%endif%} all {% for filter in offer.filters %}{{ filter }} {% endfor %}
                {% if offer.show_hover %}show_hover{% endif %}">
                <div class="pic">
                    <img src="{{offer.picture|safe}}" alt="{{offer.name|safe}}">
                </div>
                <div class="content_top">
                    <div class="price">{{offer.title|safe}}</div>
                    <div class="desc">{{offer.description|safe}}</div>
                </div>
                <div class="content">
                    <div class="title">{{offer.name|safe}}</div>
                    <a  {% if offer.linkUrl %}
                            href="{{ offer.linkUrl }}" class="btn_link"
                            {% else %}href="#data" class="btn_link button-promotion"
                        {% endif %}
                        data-filter="{% for filter in offer.filters %}{{ filter }} {% endfor %}"
                        {% if offer.smartDatasAttributes %}{{ offer.smartDatasAttributes|safe }}{% endif %}>
                        <span>{% if offer.linkUrl %}
                            {% if offer.btn_text %}{{ offer.btn_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                        {% else %}{{ T_reservar }}{% endif %}</span>
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>
<script>
    $(window).load(function () {
        setTimeout(function () {
            {% if not cascade %}
            var offers_filter_carousel = {
                loop: false,
                nav: true,
                dots: false,
                items: 4,
                margin: 5,
                navText: ['<i class="fal fa-angle-left"></i>', '<i class="fal fa-angle-right"></i>'],
                autoplay: false,
                responsive: {
                    0: {
                        items: 1
                    },
                    720: {
                        items: 2
                    },
                    1140: {
                        items: 4
                    }
                }
             };
            var offers_filters_carousel = $(".offers_filter .offers_wrapper").owlCarousel(offers_filter_carousel);
            {% endif %}
            if ($('.offers_filter .filters .filter').length) {
                $('.offers_filter .filters .filter').click(function (e) {
                    if ($(this).attr("href") === "#") {
                        e.preventDefault();
                        var $item = $(this);
                        var filter = $item.data('offer-filter');

                        $item.addClass('selected').siblings().removeClass('selected');
                        var new_content_replacement = [];

                        $(".hidden_offer_filters_banners .offer").each(function () {
                            if ($(this).hasClass(filter)) {
                                new_content_replacement.push($(this).clone());
                            }
                        }).promise().done(function () {
                            var target_html = $.map(new_content_replacement, function (e) {
                                return e[0].outerHTML
                            }).join("<!---->");
                            if ($(".offers_wrapper .offer").length) {
                                $(".offers_wrapper .offer").slideUp(function () {
                                    $(".offers_wrapper").html(target_html);
                                    {% if not cascade %}
                                    offers_filters_carousel.trigger('replace.owl.carousel', target_html).trigger('refresh.owl.carousel');
                                    {% endif %}
                                });
                            } else {
                                $(".offers_wrapper").html(target_html);
                                {% if not cascade %}
                                offers_filters_carousel.trigger('replace.owl.carousel', target_html).trigger('refresh.owl.carousel');
                                {% endif %}
                            }

                        });
                        if ($(".offers_filter .offers_wrapper .extra_banner").length) {
                            $(".offers_filter .offers_wrapper").prepend($(".offers_filter .offers_wrapper .extra_banner"));
                        }
                    }
                }).first().trigger('click');
            } else {
                //If doesn't have filters initialize with all
                var new_content_replacement = [];
                $(".hidden_offer_filters_banners .offer").each(function () {
                    new_content_replacement.push($(this).clone());
                 }).promise().done(function () {
                    var target_html = $.map(new_content_replacement, function (e) {
                        return e[0].outerHTML
                    }).join("<!---->");
                    $(".offers_wrapper").html(target_html);
                    {% if not cascade %}
                    offers_filters_carousel.trigger('replace.owl.carousel', target_html).trigger('refresh.owl.carousel');
                    {% endif %}
                 });
            }
        }, 1000);
    });</script>
</section>