<script async type="text/javascript" type="text/javascript" src="/js/onacp/snap.svg-min.js"></script>
<div style="display: none" class="extra_code_mobile">
{% if social_dict %}
    <li class="hotel_social">
    <div class="wave-aqua-small"></div>
    {% for social_id in social_dict %}
        <a href="{{ social_id.link|safe }}" alt="{{ social_id.text|safe }}" target="_blank">
            <i class="{{ social_id.ico|safe }}"></i>
        </a>
    {% endfor %}
    </li>
{% endif %}

{% if user_messages %}
<div class="ona_user_widget" style="display: none">
    <div class="user">
        <label><span>1</span></label>
        <i class="fas fa-user-circle"></i>
    </div>
    <div class="ona_user_popup">
        <div class="close icon-xcross"></div>
        {% include "svg/bubble1.html" %}
        {% for message in user_messages %}<a {% if message.linkUrl %}href="{{ message.linkUrl }}"{% endif %} class="content">
            {% if message.title == "login" %}{{message.description|safe}}{% endif %}
        </a>{% endfor %}
    </div>
</div>
{% endif %}

{% if phone_contact %}<div class="phone_action">
    <a href="tel:{{ phone_contact }}"><i class="far fa-phone"></i></a>
</div>{% endif %}

{% if temporal_link and temporal_link.0.linkUrl and temporal_link.0.title %}
<a href="{{ temporal_link.0.linkUrl|safe }}" class="search_action">
    <i class="{{ temporal_link.0.title|safe }}"></i>
</a>
{% endif %}
{% if widget_search and not temporal_link %}
<div class="search_action">
    <i class="far fa-search"></i>
</div>
{% include "_widget_search.html" %}
{% endif %}

</div>
<script>
$(function () {
    //Place IT
    $(".main_menu .main_ul").append($(".extra_code_mobile .hotel_social").detach());
    {% if user_messages %}
    $("header").append($(".ona_user_widget .user").detach());
    $("body").append($(".ona_user_popup").detach());
    {% endif %}
    {% if widget_search %}
        $("nav").prepend($(".search_action").detach());
        $("body").append($(".widget_search_wrapper").detach());
    {% endif %}
    {% if phone_contact %}$("nav").prepend($(".phone_action").detach());{% endif %}



    // Work IT
    {% if user_messages %}
    $("header").on("click", ".user", function () {
        $(".ona_user_popup").slideDown();
        $(this).find("label").hide();
        $(".default_content_wrapper, header, nav, .mobile_engine").addClass("blur");
    });
    $("body").on("click", ".ona_user_popup .close", function () {
        $(".ona_user_popup").slideUp();
        $(".default_content_wrapper, header, nav, .mobile_engine").removeClass("blur");
    });
    {% endif %}
    {% if widget_search %}
    $("nav").on("click", ".search_action", function () {
        $(".widget_search input").focus();
    });
    {% endif %}
})
</script>