#slider_container .widget_search_wrapper {
  .widget_spinner {
    .pic {
      img {
        position: unset;
        -webkit-transform: unset;
        -moz-transform: unset;
        -ms-transform: unset;
        -o-transform: unset;
        transform: unset;
        min-height: unset;
        min-width: unset;
      }
    }
  }
}

.widget_search_wrapper {
  padding: 0;
  background: white;
  &.active {
    top: auto;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100vh;
    position: fixed;
    .widget_spinner {
      opacity: 1;
    }
  }
  .widget_spinner {
    opacity: 0;
    width: 300px;
    min-height: 300px;
    @include transition(opacity,1s);
    .pic {
      text-align: center;
      img {
        width: auto;
        margin: auto;
      }
      .title {
        margin-top:30px;
        font-weight: bold;
        font-size: 30px;
      }
    }
  }
  .widget_search {
    position: relative;
    padding-left: calc((100% - 1065px) / 2);
    i.fa-search {
      @include center_y;
      left: calc((100% - 1065px) / 2);
      font-size: 18px;
    }
    input {
      position: relative;
      z-index: 2;
      border-width: 0;
      padding: 10px 35px;
      font-size: 24px;
      background: transparent;
      display: inline-block;
      vertical-align: middle;
      outline: none;
      width: calc(100% - 250px);
    }
    button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border-width: 0;
      background: $corporate_1;
      color: white;
      cursor: pointer;
      width: 240px;
      font-size: 24px;
      padding: 10px;
      display: inline-block;
      vertical-align: middle;
      float:right;
      text-transform: uppercase;
      &:hover {
        color: $corporate_2;
      }
    }
  }
  .widget_result {
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    padding: 20px calc((100% - 1140px) / 2);
    max-height: calc(100vh - 70px);
    overflow: auto;
    .destino {
      display: inline-block;
      vertical-align: top;
      float:left;
      width: 260px;
      height: 200px;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
      margin-right: 5px;
      margin-bottom: 5px;
      background: $corporate_1;
      .hidden {display: none;}
      a {
        @include full_size;
        z-index: 10;
      }
      img {
        @include center_image;
        opacity: .6;
      }
      .title {
        @include center_y;
        width: 100%;
        text-align: center;
        color:white;
        .subtitle1 {
          color: white;
        }
      }
    }
    .hotel {
      color:$corporate_1;
      margin-top: 20px;
      clear: both;
      &:hover {
        h3 {
          color: $corporate_2;
        }
      }
      .hidden {display: none;}
    }
  }
}

.filter_list_wrapper {
  display: inline-block;
  vertical-align: top;
  .filter_list {
    width: 250px;
    padding: 20px 15px 0;
    border:1px solid $corporate_2;
    margin-bottom: 10px;
    &.active {
      padding-bottom: 20px;
    }
    &.filter_price {
      background: lighten($corporate_2, 50%);
      border: none;
      padding: 20px 18px 30px;
      position: relative;
      b.filter_list_title {
        width: 140px;
        text-align: left;
        margin-bottom: 15px;
      }
      .price_text {
        font-weight: normal;
      }
      .input_slide {
        margin-top: 80px;
        display: block;

        input[type="range"] {
          appearance: none;
          -moz-appearance: none;
          -webkit-appearance: none;
          display: block;
          background: lightgray;
          width: 210px;
          height: 2px;
          background-image: -webkit-gradient(linear, 0% 0%, 100% 0%, to(rgb(0, 45, 66)), to(rgb(211, 211, 211)));
          background-image: -moz-linear-gradient(left center,
              $corporate_1 100%, $corporate_1 100%,
              lightgray 0%, lightgray 0%);

          &::-webkit-slider-thumb {
            appearance: none;
            -webkit-appearance: none;
            border: 1px solid $corporate_1;
            height: 10px;
            width: 10px;
            border-radius: 50%;
            background: $corporate_1;
            cursor: pointer;
            margin-top: 0;
          }
          &::-moz-range-thumb, &::-ms-thumb {
            appearance: none;
            -webkit-appearance: none;
            border: 1px solid $corporate_1;
            height: 10px;
            width: 10px;
            border-radius: 50%;
            background: $corporate_1;
            cursor: pointer;
          }
          &::-webkit-slider-runnable-track, &::-moz-range-track, &::-ms-track {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 210px;
            height: 2px;
            cursor: pointer;
            border: none;
            background: lightgray;
          }
          &:focus {
            outline: none;
            &::-webkit-slider-thumb {
              margin-top: -4px;
            }
            &::-webkit-slider-runnable-track {
              height: 2px;
            }
          }
          &::-ms-track {
            width: 100%;
            cursor: pointer;
            background: transparent;
            border-color: transparent;
            color: transparent;
          }
        }
      }
      .text_slide {
        position: absolute;
        bottom: 60px;
        font-size: 10px;
        font-weight: bolder;
        text-transform: uppercase;
        b {
          font-size: 18px;
          display: block;
        }
        &.to {
          right: 20px;
          text-align: right;
        }
        &.from {
          left: 20px;
        }
      }
    }

    b.filter_list_title {
      display: block;
      text-align: center;
      cursor: pointer;
    }
    i {
      position:relative;
      display: block;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      border:1px solid $corporate_1;
      color: $corporate_1;
      font-size: 10px;
      margin:10px auto;
      cursor: pointer;
      &:before {
        @include center_xy;
      }
    }
    .filter_element {
      padding: 2px 0;
      span {
        font-size: 14px;
        font-weight: normal;
        display: inline-block;
        vertical-align: middle;
        line-height: 15px;
      }
    }
  }
}

.hotels_filter_wrapper {
  padding: 50px calc((100% - 1065px) / 2) 100px;
  .result_wrapper {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 265px);
    margin-left: 10px;
    .result_text_wrapper {
      .desc {
        font-weight: normal;
      }
    }
    .result_filters {
      margin-top: 20px;
      .r_filter {
        @extend .overline;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        padding: 5px 30px 5px 10px;
        margin: 3px 5px 3px 0;
        border: 1px solid $corporate_1;
        color: $corporate_1;
        //@extend .fa-times;
        &:before {
          //@extend .fal;
          @include center_y;
          right: 10px;
          font-size: 10px;
        }
        &.main_pais {
          color: $corporate_2;
          border-color: $corporate_2;
          padding: 5px 10px;
          &:before {
            display: none;
          }
        }
      }
    }
    .result {
      position: relative;
      display: table;
      padding: 0;
      width: 100%;
      h5 {
        display: block;
        clear: both;
        padding-top: 20px;
        text-transform: uppercase;
        margin-bottom: 5px;
      }
      .hotel_element {
        position: relative;
        display: inline-block;
        vertical-align: top;
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 5px;
        margin-right: 5px;
        float: left;
        &:hover {
          .pics {
            opacity:1;
            .owl-dots {
              opacity: 1;
              .owl-dot {
                &.active {
                  span:before {
                    width: 100%;
                    @include transition(width,3s);
                  }
                }
              }
            }
          }
          .main_content {
            padding: 40px 20px 20px;
            background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.6));
            .hotel_fav {
              top: 40px;
            }
            .tags {
              opacity: 1;
            }
            .links {
              opacity: 1;
              padding-bottom: 15px;
              a {
                display: none;
                &:first-of-type {
                  display: inline-block;
                }
              }
            }
            .price {
              bottom:40px;
            }
          }
        }
        .pics {
          display: inline-block;
          vertical-align: bottom;
          width: 260px;
          height: 410px;
          a {
            display: block;
            width: 100%;
            height: 410px;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            img {
              @include center_image;
              width: auto;
            }
          }
          .owl-dots {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            opacity: 0;
            border-radius: 10px 10px 0 0;
            padding: 0 10px 15px;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0));
            text-align: center;
            .owl-dot {
              display: inline-block;
              vertical-align: middle;
              padding: 0 2px;
              width: calc(100% / 5);
              span {
                position: relative;
                display: block;
                width: 100%;
                height: 3px;
                border-radius: 0;
                background: rgba(white, .5);
                &:before {
                  content: '';
                  @include full_size;
                  background: white;
                  width: 0;
                }
              }
            }
          }
        }
        .main_content {
          position: absolute;
          top: 0;
          bottom:0;
          left: 0;
          right: 0;
          z-index: 10;
          padding: 20px;
          border-radius: 10px;
          @include transition(all,.6s);
          background: linear-gradient(rgba(0,0,0,.4), rgba(0,0,0,0.4));
          .overlay_link {
            display: block;
            @include center_y;
            left: 0;
            right: 0;
            height: calc(100% - 125px);
          }
          .hotel_fav {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
            color: white;
            opacity: .5;
            text-shadow: 0 0 5px rgba(0,0,0,0.3);
            @include transition(all,.6s);
            &:hover {
              opacity: 1;
            }
            &.active {
              opacity: 1;
              i {
                font-weight: 900;
              }
            }
          }
          .hotel_card_title, .subtitle1 {
            color:white;
          }
          .subtitle1 {
            font-size: 12px;
          }
          .subtitle2 {
            padding: 3px 0;
          }
          .tags {
            opacity: 0;
            padding: 15px 0;
            @include transition(all,.6s);
            .tag {
              display: inline-block;
              vertical-align: middle;
              background: rgba(255,255,255,1);
              padding: 3px 10px;
              margin-right: 7px;
              margin-bottom: 7px;
              text-transform: uppercase;
              color: $corporate_2;
              font-weight: bold;
              font-size: 12px;
              border-radius: 5px;
            }
          }
          .links {
            position: absolute;
            bottom:0;
            left: 0;
            right: 0;
            opacity: 0;
            text-align: center;
            @include transition(all,.6s);
            a {
              display: none;
              vertical-align: middle;
              color: white;
              text-transform: uppercase;
              font-weight: bold;
              font-size: 12px;
              margin: 0 auto;
              padding: 0 5px;
              width: calc((100% / 3) - 5px);
              i {
                color: white;
              }
              &:hover {
                color: $corporate_2;
                i {
                  color: $corporate_2;
                }
              }
            }
          }
          .price {
            position: absolute;
            bottom:20px;
            left: 20px;
            right: 20px;
            text-transform: uppercase;
            text-align: left;
            font-weight: bold;
            font-size: 37px;
            color: white;
            @include transition(all,.6s);
            .score {
              position: absolute;
              bottom: 0;
              right: 0;
              font-weight: normal;
              font-size: 20px;
              span {
                font-size: 12px;
              }
            }
            label {
              font-weight: lighter;
              font-size: 12px;
            }
            span {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}