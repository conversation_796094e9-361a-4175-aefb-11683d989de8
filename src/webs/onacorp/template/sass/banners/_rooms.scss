.rooms_wrapper {
  padding: 85px calc((100% - 1040px) / 2);
  .rooms_wrapper_title {
    margin-bottom: 20px;
  }
  .rooms {
    .room {
      position: relative;
      width: calc(100% - 4px);
      height: 400px;
      border:1px solid rgba($navy4, .3);
      border-radius: 10px;
      margin: 10px auto 0;
      .pics {
        width: 100%;
        height: 170px;
        border-radius: 10px 10px 0 0;
        overflow: hidden;
        a {
          display: block;
          width: 100%;
          height: 170px;
          position: relative;
          overflow: hidden;
          img {
            @include center_image;
            width: auto;
          }
        }
        .owl-dots {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          display: flex;
          padding: 10px;
          background: linear-gradient(rgba($corporate_1,0.6), rgba($corporate_1,0));
          .owl-dot {
            display: inline-block;
            width: 100%;
            padding: 0 2px;
            opacity: .5;
            &.active {
              opacity: 1;
            }
            span {
              position: relative;
              display: block;
              width: calc(100% - 5px);
              height: 3px;
              border-radius: 0;
              background: white;
              &:after {
                content: '';
                @include center_y;
                left: 0;
                right: 0;
                height: 20px;
              }
            }
          }
        }
      }
      .content {
        padding: 7px 18px 0;
        margin: 10px 0;
        .top {
          margin-bottom: 15px;
          font-size: 12px;
          font-weight: bold;
          .icon {
            display: inline-block;
            vertical-align: middle;
            margin-right: 30px;
            i {
              vertical-align: middle;
              color: $aqua1;
              font-size: 13px;
              padding-right: 5px;
            }
            span {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
        .room_title {
          margin-bottom: 5px;
          font-size: 16px;
        }
        .desc {
          font-size: 12px;
          hide {
            display: none;
          }
        }
        .read_more {
          display: block;
          margin-top: 5px;
          padding: 10px 0;
          @include overline;
          font-size: 10px;
          &:hover {
            span {
              opacity: .6;
            }
          }
          span, i {
            display: inline-block;
            vertical-align: middle;
          }
          i {
            margin: 0 5px;
            border: 1px solid $aqua1;
            color: $aqua1;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            position: relative;
            &:before {
              @include center_xy;
            }
          }
        }
      }
      .price {
        position: absolute;
        bottom:0;
        left:0;
        right: 0;
        text-align: right;
        .label {
          display: inline-block;
          vertical-align: middle;
          font-size: 10px;
          padding-right: 12px;
          font-weight: normal;
        }
        .amount {
          display: inline-block;
          vertical-align: middle;
          font-size: 30px;
          font-weight: 400;
          margin-right: 15px;
          small {
            font-size: 22px;
          }
        }
        .btn {
          display: block;
          margin: 5px 15px 15px;
          font-size: 14px;
          font-weight: 400;
          border-radius: 10px;
          background: $corporate_1;
          color:white;
          &:hover {
            color: $corporate_2;
          }
        }
      }
    }
    .owl-nav {
      @include center_y;
      left: 0;
      right: 0;
      .owl-prev, .owl-next {
        @include center_y;
        font-size: 30px;
        &.disabled {
          opacity: .3;
        }
      }
      .owl-prev {
        left:-20px;
      }
      .owl-next {
        right: -20px;
      }
    }
  }
  .room_popup {
    display: none;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    background: rgba($corporate_1,.9);
    .close_room_popup {
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 50px;
      color: white;
      cursor: pointer;
    }
    .center_xy {
      background: white;
      width: 500px;
      border-radius: 10px;
      max-width: calc(100vw - 60px);
      max-height: calc(100vh - 70px);
      overflow: auto;
      .pics, .content {
        display: block;
        width: 100%;
      }
      .pics {
        width: 100%;
        height: 250px;
        position: relative;
        border-radius: 10px 10px 0 0;
        overflow: hidden;
        a {
          display: block;
          width: 100%;
          height: 250px;
          position: relative;
          overflow: hidden;
          img {
            @include center_image;
            width: auto;
            max-width: 101%;
          }
        }
        .owl-dots {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          display: flex;
          padding: 10px;
          background: linear-gradient(rgba($corporate_1,0.6), rgba($corporate_1,0));
          .owl-dot {
            display: inline-block;
            width: 100%;
            padding: 0 2px;
            opacity: .5;
            &.active {
              opacity: 1;
            }
            span {
              display: block;
              width: calc(100% - 5px);
              height: 3px;
              border-radius: 0;
              background: white;
            }
          }
        }
      }
      .content {
        padding: 20px;
        margin: 10px 0;
        .top {
          margin-bottom: 15px;
          font-size: 12px;
          font-weight: bold;
          .icon {
            display: inline-block;
            vertical-align: middle;
            margin-right: 30px;
            i {
              vertical-align: middle;
              color: $aqua1;
              font-size: 13px;
              padding-right: 5px;
            }
            span {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
        .room_title {
          margin-bottom: 5px;
          font-size: 16px;
        }
        .desc {
          font-size: 12px;
          hide {
            display: block;
          }
        }
        .read_more {
          display: none;
        }
      }
    }
  }
}

