@font-face {
    font-family: 'Back to black';
    src: url('/static_1/fonts/Backtoblack/back-to-black.ttf');
    font-weight: normal;
    font-style: normal;
}

.banner_especial_promotion_wrapper {
    background-color: $aqua3;
    padding: 40px 0;
    
    .container12 {
        position: relative;
        display: flex;
        flex-flow: row nowrap;
        
        .content_wrapper {
            width: 55%;
            position: relative;
            padding-left: 130px;
            
            .content_title {
                span {
                    display: block;
                    
                    &.top_title {
                        color: $aqua1;
                        font-size: 20px;
                    }
                    
                    &.sub_title {
                        font-size: 22px;
                    }
                }
            }
            
            .desc {
                font-size: 22px;
                font-weight: 500;
            }
            
            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                background-image: url("/img/#{$base_web}/money.svg");
                width: 100px;
                height: 100px;
                background-size: 100% auto;
                background-repeat: no-repeat;
            }
            
            &::after {
                position: absolute;
                content: '';
                width: 2px;
                background-color: $black;
                top: 0;
                bottom: 0;
                right: 0;
            }
        }
        
        .link_wrapper {
            width: 45%;
            padding-left: 60px;
            font-size: 22px;
            
            .btn_link {
                font-size: 18px;
                position: relative;
                color: $black;
                display: inline-block;
                cursor: pointer;
                
                &::before {
                    position: absolute;
                    content: '';
                    height: 2px;
                    background-color: $black;
                    left: 0;
                    right: 0;
                    bottom: -5px;
                    transition: all .4s;
                }
                
                &:hover {
                    &:before {
                        bottom: 3px;
                    }
                }
            }
        }
        
        .especial_promotion_modal {
            background-color: white;
            border-radius: 20px;
            padding: 40px 0;
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            box-shadow: 0px 2px 13px 1px rgba(0, 0, 0, 0.26);
            z-index: 4;
            
            &::before {
                position: absolute;
                content: '';
                width: 2px;
                background-color: $aqua1;
                top: 152px;
                bottom: 40px;
                left: 50%;
                transform: translateX(-50%);
            }
            
            .close_modal_promotion {
                position: absolute;
                top: 20px;
                right: 20px;
                font-size: 30px;
                color: $aqua1;
                cursor: pointer;
            }
            
            .top_content {
                padding: 0 80px;
                margin-bottom: 50px;
                
                .content_title {
                    h3 {
                        font-size: 40px;
                    }
                    
                    span {
                        font-size: 22px;
                        font-weight: 500;
                    }
                }
            }
            
            .modal_content_wrapper {
                position: relative;
                display: flex;
                flex-flow: row nowrap;
                
                .content_option {
                    padding: 0 80px;
                    width: 50%;
                    
                    .title {
                        font-family: 'Back to black';
                        color: $aqua1;
                        font-size: 36px;
                        margin-bottom: 50px;
                        
                        span {
                            display: inline-block;
                            background-color: $black;
                            color: white;
                            font-size: 22px;
                            font-weight: 700;
                            padding: 5px 10px;
                        }
                    }
                    
                    .desc {
                        
                        ul {
                            list-style: none;
                            margin-top: 30px;
                            
                            li {
                                position: relative;
                                padding-left: 30px;
                                margin-bottom: 30px;
                                
                                &::before {
                                    position: absolute;
                                    content: '';
                                    top: 4px;
                                    left: 0;
                                    width: 12px;
                                    height: 12px;
                                    border-radius: 50%;
                                    background-color: $aqua3;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    &.double {
        .container12 {
            .promotion_column {
                width: 50%;
                position: relative;
                &:before {
                    position: absolute;
                    content: '';
                    width: 3px;
                    background-color: #000;
                    top: 0;
                    bottom: 0;
                    left: 95%;
                }
                &:nth-of-type(2) {
                    .content_wrapper {
                        padding-left: 140px;
                        &:before {
                            left: 5px;
                            position: absolute;
                            content: '';
                            top: 0;
                            background-image: url("/img/#{$base_web}/heart.png");
                            width: 120px;
                            height: 100px;
                            background-size: 100% auto;
                            background-repeat: no-repeat;
                        }
                    }
                    .link_wrapper {
                        margin: auto auto auto 140px;
                        .btn_link {
                        }
                    }
                    &:before {
                        display: none;
                    }
                }
                .content_wrapper {
                    width: 100%;
                    &:after {
                        display: none;
                    }
                    .content_title {
                        padding-right: 10px;
                        .title {
                            .light {
                                display: inline;
                                font-weight: 400;
                            }
                        }
                        .sub_title {
                            font-size: 17px;
                        }
                    }
                }
                .link_wrapper {
                    padding: 0;
                    margin: auto auto auto 130px;
                    .btn_link {
                        font-size: 16px;
                    }
                }
            }
        }
    }
}