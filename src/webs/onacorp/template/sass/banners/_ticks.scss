.datepicker_wrapper_element {
  .ticks_wrapper, .dates_shortcut, .hotel_mark {
    display: block;
  }
}
.hotel_mark {
  display: none;
}
.dates_shortcut {
  display: none;
  width: 800px;
  margin: 30px auto 0;
  a {
    position: relative;
    display: inline-block;
    vertical-align: bottom;
    margin-right: 20px;
    padding: 10px;
    min-width: 100px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    color: $navy1;
    text-align: center;
    @include transition(padding,.6s);
    &:before {
      content: '';
      display: block;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      width: 0;
      height: 2px;
      background: $aqua1;
      @include transition(width,.6s);
    }
    &:hover {
      padding: 5px 10px 15px;
      &:before {
        width: 100%;
      }
    }
  }
}
.ticks_wrapper {
  display: none;
  width: 800px;
  margin: auto;
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  .tick {
    display: inline-block;
    float: left;
    width: calc((100% - 60px) / 4);
    padding: 15px 0;
    margin-right: 20px;
    margin-bottom: 0;
    text-align: center;
    border: 1px solid $aqua1;
    background: white;
    @include transition(margin,.1s);
    i {
      display: block;
      margin-bottom: 15px;
      font-size: 24px;
    }
    span {
      font-size: 18px;
      font-weight: bold;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}