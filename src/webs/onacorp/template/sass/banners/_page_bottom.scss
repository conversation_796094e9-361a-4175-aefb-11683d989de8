.page_bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30px calc((100% - 900px) / 2);
  background: $aqua3;
  .content {
    text-align: left;
  }
  h5 {
    display: inline-block;
    color: $aqua1;
  }
  a {
    @include link();
    display: inline-block;
    padding-right: 20px;
    font-size: 12px;
    font-weight: bold;
    &:hover {
      padding-right: 35px;
      &:after {
        width: 25px;
      }
    }
    &:after {
      width: 15px;
      @include transition(width,.6s);
    }
    &:before {
      width: 3px;
      height: 3px;
      border-width: 2px 2px 0 0;
    }
  }
  .abstrac_top, .abstrac_bottom {
    position: absolute;
    display: block;
    width: 150px;
    height: 75px;
    background: url("/img/#{$base_web}/wave-patern-big.png");
    background-size: 35px;
    background-attachment: fixed;
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      right: 15px;
      width: 75px;
      height: 75px;
      border-radius: 50%;
      background: $aqua1;
    }
  }
  .abstrac_top {
    top:-35px;
    left: calc((100% - 1240px) / 2);
  }
  .abstrac_bottom {
    bottom:-35px;
    right: calc((100% - 1240px) / 2);
  }
}