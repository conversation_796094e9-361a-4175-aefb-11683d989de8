.page.active section.banner_gallery_tabs {
  height: 100vh;
  padding: 0;
}
.page section.banner_gallery_tabs {
  @include transition(opacity, 1s);
}
section.banner_gallery_tabs {
  position: relative;
  .tabs_wrapper {
    position: absolute;
    top: 100px;
    left: 100px;
    z-index: 3;
    .tab {
      color: white;
      text-align: center;
      font-size: 12px;
      font-weight: bold;
      min-width: 100px;
      margin-right: 5px;
      &:before {
        background: $aqua1;
      }
    }
  }
  .gallery_wrapper {
    .tab_cotent {
      position: relative;
      height: 100vh;
      .pic {
        &:before {
          content: '';
          @include full_size;
          background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.15) 150px,rgba(0,0,0,0.15) calc(100% - 150px),rgba(0,0,0,0.8));
        }
        img {
          @include center_image;
          z-index: -1;
        }
      }
      h3 {
        font-size: 20px;
      }
      .content {
        @include center_y;
        left: 100px;
        width: 50%;
        color: white;
        .title {
          margin-bottom: 50px;
          .homey {
            color: $corporate_1;
            margin-top: 15px;
            i {
              margin-top: -15px;
              margin-right: -20px;
              color: white;
              background: $corporate_2;
              border-radius: 50%;
              width: 140px;
              vertical-align: middle;
              height: 140px;
              position: relative;
              z-index: -1;
              &:before {
                font-size: 70px;
                @include center_xy;
              }
            }
            .image_circle {
              background: $corporate_2;
              border-radius: 50%;
              width: 135px;
              margin-right: -22px;
              height: 130px;
              vertical-align: sub;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              img {
                width: auto;
              }
            }
          }
        }
        .desc {
          color: $corporate_1;
          * {
            color: $corporate_1;
          }
          ul li {
            font-size: 18px;
            padding: 5px 0;
            position: relative;
            width: max-content;
            &:after {
              content: '';
              opacity: 0;
              position: absolute;
              @include transition(opacity, 1s);
              top: 50%;
              -webkit-transform: translateY(-50%);
              -moz-transform: translateY(-50%);
              -ms-transform: translateY(-50%);
              -o-transform: translateY(-50%);
              transform: translateY(-50%);
              right: -60px;
              display: inline-block;
              vertical-align: middle;
              height: 1px;
              background: #002D42;
              width: 40px;
            }
            &:before {
              content: '';
              position: absolute;
              opacity: 0;
              @include transition(opacity, 1s);
              top: 50%;
              -webkit-transform: translateY(-50%) rotate(45deg);
              -moz-transform: translateY(-50%) rotate(45deg);
              -ms-transform: translateY(-50%) rotate(45deg);
              -o-transform: translateY(-50%) rotate(45deg);
              transform: translateY(-50%) rotate(45deg);
              right: -60px;
              width: 7px;
              height: 7px;
              border: 1px solid #002D42;
              border-bottom-width: 0;
              border-left-width: 0;
            }
            &:hover {
              &:after, &:before {
                opacity: 1;
                @include transition(opacity, 1s);
              }
            }
          }
        }
      }
    }
    .owl-dots {
      position: absolute;
      bottom: 25vh;
      left: 100px;
      margin: 0;
      opacity: 1;
      z-index: 40;
      .owl-dot {
        display: inline-block;
        padding-right: 15px;
        opacity: .5;
        span {
          display: block;
          background: $aqua1;
          width: 40px;
          height: 3px;
        }
        &.active {
          opacity: 1;
        }
      }
    }
  }
}