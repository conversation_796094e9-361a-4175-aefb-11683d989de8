#slider_container {
  padding: 0;
  height: auto;
  position: relative;
  background: $corporate_1;
  &.full_slider_container {
    height: 100vh;
  }
  &.page {
    height: calc(100vh - 165px);
    .tp-banner-container {
      height: calc(100vh - 165px) !important;
    }
  }
  .caption {
    width: 100% !important;
    top: 0 !important;
    left: 0 !important;
    bottom: 0;
    padding: 50px 140px !important;
    .cartela1 {
      @include center_y;
      width: 100%;
      text-transform: none;
      color: white;
    }
    .cartela_homey {
      @include center_y;
      width: 100%;
      text-transform: none;
      color: white;
      h1 {
        color: $corporate_1;
        margin-left: -35px;
        margin-bottom: -17px;
        margin-top: 15px;
        i {
          margin-top: -15px;
          margin-right: -20px;
          color: white;
          background: $corporate_2;
          border-radius: 50%;
          width: 140px;
          vertical-align: middle;
          height: 140px;
          position: relative;
          z-index: -1;
          &:before {
            font-size: 70px;
            @include center_xy;
          }
        }
        .image_circle {
          background: $corporate_2;
          border-radius: 50%;
          width: 135px;
          margin-right: -22px;
          height: 130px;
          vertical-align: sub;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
      h2 {
        font-weight: bold;
        span {
          position: relative;
          font-weight: normal;
          &:after {
            content: "";
            width: 100%;
            background: url("/img/#{$base_web}/wave-line-aqua.png");
            height: 10px;
            background-repeat: no-repeat;
            bottom: -15px;
            right: 0;
            position: absolute;
          }
        }
      }
      a {
        background: $corporate_1;
        color: white;
        font-size: 22px;
        border-radius: 30px;
        padding: 14px 34px;
        display: inline-block;
        width: auto;
        margin-top: 20px;
        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
  .tp-bullets {
    bottom: 25vh !important;
    left: 140px !important;
    margin: 0 !important;
    opacity: 1 !important;
    z-index: 40;
    .bullet {
      position: relative;
      background: white;
      width: 40px;
      height: 3px;
      margin-right: 15px;
      opacity: .5;
      &.selected {
        opacity: 1;
      }
      &:after {
        content: '';
        @include center_y;
        left: 0;
        right: 0;
        height: 20px;
      }
    }
  }
  .tp-leftarrow, .tp-rightarrow {
    display: none;
  }
  .inner_slider {
    position: relative;
    height: 400px;
    overflow: hidden;
    border-bottom: 1px solid $navy1;
    &.full_slider {
      height: 100vh;
      .innner_slider_carousel {
        .pic {
          height: 100vh;
        }
      }
    }
    .pic_slider {
      @include center_image;
    }
    .innner_slider_carousel {
      .pic {
        position:relative;
        width: 100%;
        height: 400px;
        overflow: hidden;
        img {
          @include center_image;
          max-width: 100%;
          width: auto;
        }
      }
      .caption_slider {
        position:absolute;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        bottom: 0;
        padding: 50px 140px !important;
        background: rgba(51, 51, 51, .3);
        .cartela1 {
          @include center_y;
          width: 100%;
          text-transform: none;
          color: white;
          h1 {
            font-size: 72px;
          }
          h2 {
            font-size: 45px;
            padding: 20px 0;
          }
        }
        video {
          object-fit: cover;
        }
      }
      .owl-dots {
        position:absolute;
        bottom: 25vh !important;
        left: 140px !important;
        margin: 0 !important;
        opacity: 1 !important;
        z-index: 40;
        .owl-dot {
          position: relative;
          display: inline-block;
          background: white;
          width: 40px;
          height: 3px;
          margin-right: 15px;
          opacity: .5;
          &.active {
            opacity: 1;
          }
          &:after {
            content: '';
            @include center_y;
            left: 0;
            right: 0;
            height: 20px;
          }
        }
      }
    }
  }
}