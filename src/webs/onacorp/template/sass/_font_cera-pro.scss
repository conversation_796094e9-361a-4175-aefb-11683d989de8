@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Italic.eot');
    src: local('Cera Pro Italic'), local('CeraPro-Italic'),
        url('/static_1/fonts/cera_pro/CeraPro-Italic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Italic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Regular.eot');
    src: local('Cera Pro Regular'), local('CeraPro-Regular'),
        url('/static_1/fonts/cera_pro/CeraPro-Regular.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Regular.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-BlackItalic.eot');
    src: local('Cera Pro Black Italic'), local('CeraPro-BlackItalic'),
        url('/static_1/fonts/cera_pro/CeraPro-BlackItalic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-BlackItalic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-BlackItalic.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Light.eot');
    src: local('Cera Pro Light'), local('CeraPro-Light'),
        url('/static_1/fonts/cera_pro/CeraPro-Light.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Light.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-BoldItalic.eot');
    src: local('Cera Pro Bold Italic'), local('CeraPro-BoldItalic'),
        url('/static_1/fonts/cera_pro/CeraPro-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-BoldItalic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Black.eot');
    src: local('Cera Pro Black'), local('CeraPro-Black'),
        url('/static_1/fonts/cera_pro/CeraPro-Black.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Black.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Medium.eot');
    src: local('Cera Pro Medium'), local('CeraPro-Medium'),
        url('/static_1/fonts/cera_pro/CeraPro-Medium.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Medium.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Bold.eot');
    src: local('Cera Pro Bold'), local('CeraPro-Bold'),
        url('/static_1/fonts/cera_pro/CeraPro-Bold.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Bold.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-Thin.eot');
    src: local('Cera Pro Thin'), local('CeraPro-Thin'),
        url('/static_1/fonts/cera_pro/CeraPro-Thin.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-Thin.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-MediumItalic.eot');
    src: local('Cera Pro Medium Italic'), local('CeraPro-MediumItalic'),
        url('/static_1/fonts/cera_pro/CeraPro-MediumItalic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-MediumItalic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-MediumItalic.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-LightItalic.eot');
    src: local('Cera Pro Light Italic'), local('CeraPro-LightItalic'),
        url('/static_1/fonts/cera_pro/CeraPro-LightItalic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-LightItalic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
}
@font-face {
    font-family: 'Cera Pro';
    src: url('/static_1/fonts/cera_pro/CeraPro-ThinItalic.eot');
    src: local('Cera Pro Thin Italic'), local('CeraPro-ThinItalic'),
        url('/static_1/fonts/cera_pro/CeraPro-ThinItalic.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/cera_pro/CeraPro-ThinItalic.woff') format('woff'),
        url('/static_1/fonts/cera_pro/CeraPro-ThinItalic.ttf') format('truetype');
    font-weight: 100;
    font-style: italic;
}
