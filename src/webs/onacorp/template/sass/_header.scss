header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1002;
  padding: 50px 60px;
  background: linear-gradient(rgba(0,0,0,0.6),rgba(0,0,0,0));
  @include transition(all, .6s);
  &.fixed {
    background: white;
    padding: 0 0 0 60px;
    .menu_toggle {
      padding-top: 5px;
      span {
        opacity: 0;
        display: none;
      }
      &:before, &:after {
        background: $aqua1;
      }
    }
    #logoDiv {
      width: 70px;
      padding-right: 0;
      img {
        width: 0;
        opacity: 0;
      }
      span {
        width: 50px;
        height: 50px;
        opacity: 1;
      }
    }
    .hotel_info_header {
      max-width: 400px;
    }
    #lang {
      opacity: 0;
      padding-bottom: 0;
      margin-top: -20px;
    }
    #top-sections {
      i.fa-info-circle {
        color: $aqua1;
      }
      .phone, .search, .user, .language_icon {
        color: $aqua1;
      }
      a {
        color: $aqua1;
      }
      .booking_button {
        width: 200px;
        margin-left: 20px;
      }
    }
  }
  .popup_phone {
    @include full_size;
    position: fixed;
    z-index: 100;
    background: rgba($corporate_1,.8);
    .close_popup_phone {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      left: 20px;
      cursor: pointer;
      &:before, &:after {
        background: white;
      }
    }
    .content {
      background: white;
      border-radius: 10px;
      color: $corporate_1;
      padding: 20px;
      .subtitle {
        padding-bottom: 30px;
      }
      .icon_list {
        text-align: left;
        li {
          display: block;
          a {
            color: $navy1 !important;
          }
        }
      }
    }
  }
  .menu_toggle {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    color: white;
    padding-top: 20px;
    width: 45px;
    height: 40px;
    text-align: right;
    cursor: pointer;
    @include transition(padding, .6s);
    span {
      position: absolute;
      top: 0;
      left: 0;right: 0;
      padding: 3px;
      opacity: 1;
      @include transition(opacity, .6s);
    }
    &:before, &:after {
      content: '';
      display: block;
      width: 100%;
      height: 3px;
      background: white;
      border-radius: 20%;
      margin: 8px 0 0 auto;
      @include transition(width, .6s);
    }
    &:after {
      width: 50%;
      top: 35px;
    }
    &:hover {
      &:before {
        width: 50%;
      }
      &:after {
        width: 100%;
      }
    }
  }

  #logoDiv {
    display: inline-block;
    vertical-align: middle;
    width: 250px;
    white-space: nowrap;
    padding: 0 0 0 30px;
    img {
      vertical-align: middle;
      opacity: 1;
      width: 100%;
      @include transition(all, .6s);
    }
    span {
      display: inline-block;
      width: 0;
      height: 0;
      opacity: 0;
      color: $aqua1;
      line-height: 45px;
      font-size: 48px;
      font-weight: bold;
      @include transition(all, .6s);
    }
  }
  .hotel_info_header {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    max-width: 0;
    overflow: hidden;
    @include transition(all, .6s);
    .stars i {
      display: inline-block;
      vertical-align: top;
      font-size: 10px;
      padding-top: 3px;
    }
  }
  #lang {
    text-align: right;
    color: white;
    @include body2;
    padding-bottom: 7px;
    @include transition(all, .6s);
    .currency, .lang_selector {
      display: inline-block;
      vertical-align: middle;
      a {
        color: white;
      }
    }
  }
  #top-sections {
    text-align: right;
    i.fa-info-circle {
      color: white;
    }
    .separator {
      display: inline-block;
      vertical-align: middle;
      height: 20px;
      width: 1px;
      background: white;
      margin: 0 15px;
    }
    .phone, .search, .user, .language_icon {
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      color: white;
      cursor: pointer;
      padding-left: 7px;
      i {
        font-size: 20px;
      }
    }
    .phone, .search, .language_icon {
      padding-right: 7px;
    }
    .phone {
      & > i {
        &:before {
          display:block;
          -webkit-transform: rotateY(180deg);
          -moz-transform: rotateY(180deg);
          -ms-transform: rotateY(180deg);
          -o-transform: rotateY(180deg);
          transform: rotateY(180deg);
        }
      }
    }
    .language_icon {
      span {
        padding:3px;
        a {
          display: block;
          padding: 3px 7px;
          font-weight: bold;
          color: $corporate_1;
          &:hover {
            background: $aqua3;
          }
        }
      }
    }
    .user {
      position: relative;
      i {
        cursor: pointer;
        font-size: 25px;
      }
      label {
        display: none;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        position: absolute;
        top: -7px;
        right: -7px;
        background: red;
        color: white;
        border: 1px solid white;
        span {
          @include center_xy;
          font-size: 12px;
        }
      }
      .popup {
        width: 350px;
        position: absolute;
        right: 0;
        top: calc(100% + 30px);
        color: $navy1;
        .close {
          display: block;
          position: absolute;
          top: 50px;
          right: 40px;
          width: 15px;
          height: 15px;
          cursor: pointer;
          &:before, &:after {
            background: $navy1;
          }
        }
        svg {
          * {
            fill: rgba($aqua4, .6);
          }
        }
        .content {
          @include body1;
          position: absolute;
          top: 75px;
          left: 50px;
          right: 75px;
          img {
            width: 50px;
          }
        }
      }
    }
    a {
      color: white;
    }
    .booking_button {
      display: inline-block;
      vertical-align: middle;
      overflow: hidden;
      width: 0;
      text-align: center;
      @include transition(width, .6s);
      a {
        display: block;
        height: 50px;
        line-height: 50px;
        font-size: 24px;
        font-weight: 500;
        color:$navy1;
        text-transform: uppercase;
        background: $aqua1;
      }
    }
  }
  .float_left {
    width: 45%;
  }
  .float_right {
    width: 52%;
  }
}
#main_menu {
  @include full_size;
  position: fixed;
  height: 100vw;
  width: 0;
  z-index: 1002;
  overflow: hidden;
  background: white;
  @include transition(width, 1s);
  &.opened {
    width: 100vw;
    &:before {
      width: 100%;
      border-radius: 0;
      @include transition(all, 1.5s);
    }
    .close {
      &:before, &:after {
        left: 50%;
        top: 50%;
      }
      &:before {
        -webkit-transform: translate(-50%,-50%) rotate(45deg);
        -moz-transform: translate(-50%,-50%) rotate(45deg);
        -ms-transform: translate(-50%,-50%) rotate(45deg);
        -o-transform: translate(-50%,-50%) rotate(45deg);
        transform: translate(-50%,-50%) rotate(45deg);
      }
      &:after {
        width: 100%;
        -webkit-transform: translate(-50%,-50%) rotate(-45deg);
        -moz-transform: translate(-50%,-50%) rotate(-45deg);
        -ms-transform: translate(-50%,-50%) rotate(-45deg);
        -o-transform: translate(-50%,-50%) rotate(-45deg);
        transform: translate(-50%,-50%) rotate(-45deg);
      }
    }
    .banner_extra_menu {
      opacity: 1;
    }
  }
  &:before {
    content: '';
    @include full_size;
    width: 0;
    z-index: -1;
    background: $aqua3;
    border-radius: 0 50% 50% 0;
    @include transition(all, .5s);
  }
  .close {
    position: absolute;
    top: 50px;
    left: 50px;
    width: 50px;
    height: 50px;
    cursor: pointer;
    &:before, &:after {
      background: $aqua1;
      height: 3px;
      border-radius: 20%;
      left: 0;
      top: 20px;
      -webkit-transform: rotate(0) translate(0,0);
      -moz-transform: rotate(0) translate(0,0);
      -ms-transform: rotate(0) translate(0,0);
      -o-transform: rotate(0) translate(0,0);
      transform: rotate(0) translate(0,0);
      @include transition(all, 1s);
    }
    &:after {
      top: 40px;
      left: 50%;
      width: 50%;
    }
  }
  .main_menu {
    @include center_y;
    top: 50vh;
    left: 0;
    margin-left: 100px;
    .main-section-div-wrapper {
      a {
        display: block;
        @extend h3;
        color:$navy1;
        margin-bottom: 15px;
        position: relative;
        &:hover {
          &:before {
            width: 100%;
          }
        }
        &:before {
          content: '';
          height: 3px;
          background: $aqua2;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          width: 0;
          @include transition(width, .6s);
        }
      }
    }
    .social {
      margin-top: 15px;
      display: inline-block;
      .wave-aqua {
        margin-bottom: 20px;
      }
      a {
        position: relative;
        display: inline-block;
        color: $navy1;
        width: 50px;
        height: 50px;
        border: 2px solid $navy1;
        border-radius: 50%;
        margin-right: 10px;
        @extend h4;
        @include transition(all, .6s);
        &:before {
          content: '';
          @include center_xy;
          background: $aqua1;
          width: 0;
          height: 0;
          border-radius: 50%;
          @include transition(all, .6s);
        }
        i {
          @include center_xy;
        }
        &:hover {
          color: white;
          border-color:$aqua2;
          &:before {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .banner_extra_menu {
    position: absolute;
    top: 50px;
    bottom: 100px;
    right: 0;
    width: calc(50vw - 150px);
    color: $navy1;
    opacity: 0;
    @include transition(opacity, 1s);
    .content {
      position: absolute;
      top: 100px;
      right: 0;
      left: 0;
      padding: 100px 50px;
      h3 {
        margin-bottom: 30px;
      }
      .link {
        display: inline-block;
        margin-top: 30px;
      }
    }
    svg {
      position: relative;
      width: 50vw;
      right: 0;
      * {
        fill: url(#bubble2-gradient) $aqua1;
      }
    }
  }
}