.hotels_filter_wrapper {
  padding: 30px 0;
  .filter_list_wrapper {
    position: fixed;
    top: 0;
    left: -100vw;
    width: 100vw;
    right: auto;
    bottom:0;
    overflow: auto;
    z-index: 101;
    padding: 80px 0 100px;
    background: white;
    @include transition(all,.6s);
    &.fixed {
      left: 0;
      .filter_toggle {
        display: block;
        right: 10px;
        opacity: 1;
      }
    }
    .filter_list .filter_element span.checkmark {
      margin-top: 0;
    }
    .btn {
      display: table;
      margin: 30px auto;
      font-size: 18px;
      padding: 10px 25px;
      font-weight: bold;
      box-sizing: border-box;
    }
    .filter_toggle {
      display: none;
      color: white;
      background: $corporate_1;
      width: 70px;
      height: 70px;
      border-radius: 50%;
      box-shadow: 0 5px 10px rgba(0,0,0,0.5);
      @include center_x;
      position: fixed;
      bottom: 30px;
      font-size: 20px;
      opacity: 0;
      @include transition(all,.6s);
      i {
        @include center_xy;
        font-size: 30px;
      }
    }
    text-align: left;
    .filter_list {
      margin: 0 auto 20px;
    }
  }
  .result_wrapper {
    text-align: left;
    width: calc(100% - 20px);
    margin: 0;
    .result_text_wrapper {
      padding: 30px 20px 0;
      .desc {
        font-size: 12px;
      }
    }
    .result {
      box-sizing: border-box;
      display: block;
      h5 {
        position: unset;
      }
      .result_group {
        width: 300px;
        margin: auto;
        .hotel_element {
          display: block;
          width: 300px;
          max-width: 100%;
          float: none;
          margin: 0 auto 10px;

          .pics {
            width: 100%;
            .owl-dots {
              .owl-dot {
                box-sizing:border-box;
              }
            }
          }
          .content {
            width: 100%;
            box-sizing: border-box;
            .button-promotion {
              position: unset;
              display: block;
              margin-top: 40px;
            }
          }
          .links {
            a {
              box-sizing: border-box;
            }
          }
        }
      }
    }
  }
}

.widget_search_wrapper {
  opacity: 0;
  z-index: 101;
  height: 0;
  overflow: hidden;
  position: relative;
  &.active {
    overflow: visible;
    opacity:1;
  }
  .widget_search {
    position: unset;
    i.fa-search {
      top: 17px;
      left: 5px;
      -webkit-transform: unset;
      -moz-transform: unset;
      -ms-transform: unset;
      -o-transform: unset;
      transform: unset;
      background: $corporate_2;
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      &:before {
        @include center_xy;
      }
    }
    input {
      width:calc(100% - 10px);
      font-size: 14px;
      box-sizing: border-box;
      border-radius: 20px;
      border: 1px solid $corporate_2;
      margin: 16px 5px;
      padding: 10px 10px 10px 45px;
    }
    button {
      width: 40px;
      height: 40px;
      border-radius:50%;
      padding: 0;
      position: absolute;
      top: 17px;
      right: 5px;
      z-index: 100;
      i {
        @include center_xy;
        font-size: 20px;
      }
    }
  }
  .widget_result {
    top: 60px;
    padding:20px 20px 170px;
    .destino {
      width: auto;
      height: auto;
      float: none;
      background: transparent;
      img {
        display:none;
      }
      .title {
        position: unset;
        -webkit-transform: unset;
        -moz-transform: unset;
        -ms-transform: unset;
        -o-transform: unset;
        transform: unset;
        text-align: left;
        h3, .subtitle1 {
          display: inline-block;
          color: $corporate_1;
        }
      }
    }
  }
}

.hotels_filter_wrapper .result_wrapper .result .hotel_element {
  &:hover {
    .main_content .overlay_link {
      display: block;
    }
  }
  .main_content .overlay_link {
    display: none;
  }
}