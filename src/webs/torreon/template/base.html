<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="{{ language }}"  xml:lang="{{ language }}">

<head>
    <title>{% if sectionName %} {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} </title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>

    <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">

    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>

    <meta name="google-site-verification" content="Wmnn4OF8GnPUmusZU_r8w9mZUz0FwHiE4sS9scD5yEg" />

    <meta name="dc.title" content="{% if sectionName %} {{ sectionName|safe }} - {% endif %} {{ hotel_name|safe }}"/>
    <meta name="dc.description"
          content="{% if sectionName %} {{ sectionName|safe }} - {% endif %}{{ description|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="viewport" content="width=1000, initial-scale=1.0, maximum-scale=0.67">

    <!-- jquery -->


    <!--[if lte IE 6]>
	<script type="text/javascript">
	    alert('{{ T_explorer6_no_soportado }}');
	</script>
    <![endif]-->

    <link href='//fonts.googleapis.com/css?family=Roboto:400,700' rel='stylesheet' type='text/css'>

    <script type="text/javascript" src="/static_1/lib/jquery-1.7.min.js"></script>

    <!-- jquery datepicker -->
    <script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>




    {% if datepicker_theme %}
        <link type="text/css" rel="stylesheet"
              href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
        <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"/>
    {% endif %}

    <!--[if lt IE 9]>
<script type="text/javascript" src="/static_1/js/PIE.js"></script>
<![endif]-->

    <!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>

    <!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/sectionsStyle.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/960/960.css"/>

    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css"/>
    <link rel="stylesheet" type="text/css" href="/css/torrn/bookingEngine.css"/>

    <link rel="stylesheet" type="text/css" href="/css/torrn/booking_engine_modal.css"/>


    <link rel="stylesheet" type="text/css" href="/css/torrn/templateSpecific.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/hotelStyle.css"/>


    <link href='//fonts.googleapis.com/css?family=Oswald:400,300' rel='stylesheet' type='text/css'>

    <!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/videojs/video-js.min.css" media="screen" />



<!--[if IE 8]>
<style type="text/css">

    img.defaultimg{
       width: 130%!important;
    }

</style>
<![endif]-->

    <!--[if lt IE 8]>
<style type="text/css">
#booking-engine {
	width: 400px;
	height: 350px;
	position: relative !important;
	margin-top: -450px;
	margin-bottom: 100px;
	background: white;
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e6ffffff', endColorstr='#ffffff',GradientType=1 ); /* IE6-9 */
}

#main-sections-inner li:hover ul {
  display: block;
  height: 100px;
}

#content {
  margin-top: 50px;
}

hr {
  display: none;
}

#booking-engine {
	width: 340px
}

.main-section-div-wrapper {
	float: left;
}

</style>



<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->



    <!-- Chat Vita-Mediterraneo -->
    {% if namespace == "vita-mediterraneo" %}
        <!--Start of Zopim Live Chat Script-->
        <script type="text/javascript">
            window.$zopim || (function (d, s) {
                var z = $zopim = function (c) {
                    z._.push(c)
                }, $ = z.s =
                        d.createElement(s), e = d.getElementsByTagName(s)[0];
                z.set = function (o) {
                    z.set.
                            _.push(o)
                };
                z._ = [];
                z.set._ = [];
                $.async = !0;
                $.setAttribute('charset', 'utf-8');
                $.src = '//v2.zopim.com/?1uSUif3C4gWid8p7GGMKZgdKhZ2j4fBX';
                z.t = +new Date;
                $.
                        type = 'text/javascript';
                e.parentNode.insertBefore($, e)
            })(document, 'script');
        </script>
        <!--End of Zopim Live Chat Script-->
    {% endif %}
{{ extra_head|safe }}
</head>

<body class="{{ namespace }} {{ hotel_class }}" itemscope itemtype="//schema.org/Hotel">

<meta itemprop="description" content="{{ description_microdata }}">


{% block content %}
{% endblock %}


<script type="text/javascript" src="/static_1/plugins/bookingwidget/jquery.bookingwidget.js"></script>
<!-- lightbox -->
<script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>


<!-- jQuery KenBurn Slider  -->
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/jquery.themepunch.plugins.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/jquery.themepunch.revolution.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/videojs/video.dev.js"></script>
    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>

  <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>


<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/i18n/messages_{{language}}.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>
<script type="text/javascript" src="/static_1/scripts/common.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-ui-accordion.js"></script>
<script type="text/javascript">

    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', '{{google_analytics_id}}', 'auto');
    ga('send', 'pageview');

    // Load the plugin.
    ga('require', 'linker');

    // Define which domains to autoLink.
    ga('linker:autoLink', ['appspot.com', 'vitahoteliers.com'], false, true);

    (function() {
    var po = document.createElement('script'); po.type = 'text/javascript'; po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(po, s);
  })();

</script>


<script type="text/javascript">

    $(window).unload(function () {
    });

    $(document).ready(function () {
        $("#pikame").PikaChoose({showTooltips: true, carousel: false});


        //Patch while we find what is happening
        $("#adultsRoom1").val("2");
        $("#adultsRoom2").val("2");
        $("#adultsRoom3").val("2");
    });

    function showGallery(elements) {
        $.fancybox(elements, {'prevEffect': 'none',
                    'nextEffect': 'none',
                    'type': 'image',
                    'arrows': true,
                    'nextClick': true,
                    'mouseWheel': true,
                    'helpers': {
                        title: {
                            type: 'outside'
                        },
                        overlay: {
                            opacity: 0.8,
                            css: {
                                'background-color': '#000'
                            }
                        },
                        thumbs: {
                            width: 50,
                            height: 50
                        }
                    }
                }
        );
    }
    ;


    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });

    $(".myFancyPopupAuto").fancybox({
        width: "auto",
        height: "auto",
        fitToView: false,
        autoSize: false
    });

    $(function () {
        if (window.PIE) {
            $(".css3").each(function () {
                PIE.attach(this);
            });
        }
    });



    $(".modal-booking-widget").bookingWidget({spinnerColor: "white"});

    $(".button-promotion").fancybox({
        width: 800,

        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });

    $(".accordion" ).accordion({
        heightStyle: "content",
        collapsible: true
    });

    if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}


</script>



{% if celebrations %}
    <script type="text/javascript">
    $(document).ready(function(){
        window.setTimeout(function() {
         $.fancybox.open($("#popup-banner"), {wrapCSS : 'popup-start'})

     }, 800);

    $("#booking").hide();
});

    </script>

{%  endif %}


{% if sectionToUse.sectionType == 'Inicio'%}
<script>
{% if popup_inicio_automatico %}
    $(document).ready(function(){
        window.setTimeout(function() {

            if(searchCookie("anuncio_fancy_{{ language }}")){

            }
            else{
                $.fancybox.open($(".popup_inicio"), {wrapCSS : 'popup-start'});
                document.cookie="anuncio_fancy_{{ language }}=1"
            }

        }, 800);
    });
    {% endif %}
</script>
{% if popup_inicio_automatico %} {% if popup_inicio_automatico.0.servingUrl %}
<div style="display:none">
        <div class="popup_inicio" style="position:relative;">
           {% if popup_inicio_automatico.0.linkUrl %}<a href="{{ popup_inicio_automatico.0.linkUrl }}">{% endif %}<img src="{{ popup_inicio_automatico.0.servingUrl }}=s850">{% if popup_inicio_automatico.0.linkUrl %}</a>{% endif %}
        </div>
    </div>

    {%  endif %}
{% endif %}
{% endif %}


</body>
</html>
