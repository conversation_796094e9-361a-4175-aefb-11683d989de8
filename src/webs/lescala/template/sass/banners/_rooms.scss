.rooms_full_wrapper {
  .filter_rooms {
    position: relative;
    width: 100%;
    z-index: 100;
    padding: 0 calc((100% - 1140px) / 2);
    display: flex;
    justify-content: center;
    background: $corporate_1;
    &.showed {
      position: fixed;
      top: 157px;
    }

    .filter {
      display: inline-block;
      color: white;
      border: 1px solid $corporate_1;
      border-bottom: 0;
      text-align: center;
      text-transform: uppercase;
      padding: 20px;
      cursor: pointer;
      font-size: 19px;
      font-weight: 400;
      @include transition(all, .6s);
      &:hover, &.active {
        background-color: $corporate_1;
        color: white;
        font-weight: 700;
      }
    }
  }

  .rooms_wrapper {
    padding: 50px 0;

    .room {
      position: relative;
      padding: 20px 0;
      overflow: hidden;
      display: grid;
      grid-template-columns: 50% 50%;
      justify-content: center;
      grid-auto-flow: dense;
      align-items: center;
      margin-bottom: 30px;

      .room_gallery {
        position: relative;

        .image {
          position: relative;
          display: inline-block;
          width: 100%;
          height: 545px;
          overflow: hidden;

          img {
            @include center_image;
            width: auto;
          }

          .search_icon {
            display: inline-block;
            position: absolute;
            z-index: 5;
            top: 40px;
            left: 45px;

            i {
              filter: drop-shadow(1px 1px 12px grey);
              color: white;
              font-size: 50px;
              @include transition(color, .6s);

              &:hover {
                color: $corporate_1;
              }
            }
          }
        }

        .owl-nav {
          position: absolute;
          bottom: 70px;
          left: 0;
          width: 150px;

          .owl-prev, .owl-next {
            display: inline-block;
            vertical-align: middle;

            i:before {
              filter: drop-shadow(1px 1px 12px grey);
              color: white;
              font-size: 32px;
              font-weight: 600;
            }
          }
        }
      }

      .room_content {
        display: inline-block;
        padding: 40px 30px 40px 100px;
        background-color: white;

        .title {
          display: block;
          color: $corporate_2;
          font-weight: lighter;
          font-style: normal;
          font-size: 58px;
          line-height: 58px;
          padding-bottom: 13px;

          strong {
            color: $corporate_1;
            text-transform: uppercase;
            line-height: 50px;
            font-weight: 600;
            font-style: italic;
            font-size: 28px !important;
            display: block;
          }
          small {
            display: block;
            font-size: 35px;
          }
        }

        .room_icons {
          display: flex;
          align-items: center;
          padding: 20px 0;

          .icon {
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;

            &:not(:last-of-type) {
              margin-right: 8px;
            }

            i {
              font-size: 30px;
              color: $corporate_1;
              display: inline-block;
              vertical-align: middle;
              padding: 5px 0;
            }

            .icon_text {
              display: inline-block;
              vertical-align: middle;
              font-size: 10px;
              margin-left: 3px;
              color: $black;
              text-align: left;
              white-space: nowrap;
              max-width: 0;
              visibility: hidden;
              @include transition(all, .6s);
            }

            &:hover {
              .icon_text {
                max-width: 300px;
                visibility: visible;
              }
            }
          }
        }

        .text {
          @include text_styles;
          margin-bottom: 25px;
          font-size: 25px;
          line-height: 33px;
          font-style: italic;
          font-weight: 700;

          span {
            color: #c3c141;
            font-weight: 600;
          }
        }

        .link_wrapper {
          padding-top: 20px;
          text-align: center;
        }

        .button_wrapper {
          display: flex;
          a {
            width: 185px;
            height: 50px;
            padding: 30px;
            font-size: 18px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-radius: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s;
            &:first-of-type {
              background-color: $corporate_1;
              color: white;

              &:hover {
                background-color: $corporate_2;
              }
            }

            &:last-of-type {
              border: 2px solid $corporate_2;
              margin-left: 25px;
              transition: 0.3s;
              padding-bottom: 26px;

              &:hover {
                color: $corporate_1;
                border: 2px solid $corporate_1;
                margin-left: 25px;
              }
            }
          }
        }
      }

      &:nth-of-type(even) {
        background-color: white;

        .room_gallery {
          grid-column:2;
          text-align: right;

          .image {
            .search_icon {
              position: absolute;
              top: 40px;
              right: 30px;
            }
          }

          .owl-nav {
            left: auto;
            right: 0;
          }

          .owl-dots {
            left: auto;
            right: 0;
          }
        }

        .room_content {
          right: auto;
          left: calc((100% - 1140px) / 2);

          .svg_logo {
            * {
              fill: darken($grey, 3%);
            }
          }
        }
      }
    }
  }
}