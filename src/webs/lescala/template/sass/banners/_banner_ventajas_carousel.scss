.content__slider-advantages__wrapper {
    margin-top: 60px;
    margin-bottom: 60px;
    
    &.container12 {
        width: 850px;
    }
    
    .slider-advantages {
        padding: 0 100px;
        position: relative;
        
        .advantages_element {
            text-align: center;
            position: relative;
            
            .advantages_icon i {
                font-size: 50px;
                color: #2e4d5a;
                border: 3px solid transparent;
            }
            .advantages_title {
                margin-top: -3px;
                font-size: 15px;
                text-transform: uppercase;
                color: #c3c233;
                font-weight: 600;
            }
            .advantages_title strong {
                color: #2e4d5a;
                font-size: 17px;
                margin-top: 15px;
                display: block;
            }
        }
    }
    
    .owl-carousel {

        .owl-stage-outer {
            z-index: 1;
        }

        .owl-nav {
            position: absolute;
            width: 100%;
            top: 0;
            bottom: 0;
            
            > div {
                width: 30px;
                font-size: 40px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                
                i {
                    width: 30px;
                    height: 30px;
                    position: relative;
                    
                    &:before {
                        top: 85%;
                        left: 50%;
                        font-size: 35px;
                        color: $corporate_2;
                        font-weight: 600;
                        transform: translate(-50%, -50%);
                    }
                }
            }
        }
    }
}