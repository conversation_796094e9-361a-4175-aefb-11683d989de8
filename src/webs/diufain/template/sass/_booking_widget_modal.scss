#motor_reserva.modal-booking-widget {
  width: 256px !important;
}

.modal-booking-widget {
  width: 50px;
}

.modal-booking-widget {
  width: 50px;
}

.modal-booking-widget fieldset {
  float: none;
}

.modal-booking-widget .adultos {
  margin: 0 30px 0 0 !important;
}

.modal-booking-widget .contenedor_opciones {
  width: 230px;
}

.modal-booking-widget .adultos select.selector_adultos,
.modal-booking-widget .contenedor_opciones .ninos select,
.modal-booking-widget .contenedor_opciones .numero_personas select {
  width: 100px;
}

.modal-booking-widget .contenedor_opciones .numero_personas select {
  float: none;
}

.modal-booking-widget .contenedor_opciones .numero_personas {

}

.modal-booking-widget .ninos .info_ninos {
  left: 169px !important;
  width: 80px;
}

#contenedor_opciones {
  width: 10px !important;
  height: auto;
}

.fancybox-wrap {
  width: auto !important;
}

.fancybox-outer {
  padding: 0 !important;
}

.fancybox-inner {
  overflow: hidden !important;
}

.booking-widget {
  background-color: $white;
}

.booking-widget fieldset {
  margin: 8px 0 0;
}

.booking-widget label {
  color: $gray-1;
  display: block;
  font-size: 12px;
}

.booking-widget .numero_habitacion {
  display: none;
}

.modal-form {
  padding: 12px;
}

.modal-form .booking_title1 {
  display: none;
}

.modal-form .booking_title2 {
  text-align: center;
  background-color: $corporate-1;
  padding-top: 10px;
  font-size: 30px;
  color: $white;
}

.modal-form .best_price {
  text-align: center;
  background-color: $corporate-1;
  padding-bottom: 10px;
  color: $white;
}

.modal-form .best_price {
  margin-bottom: 20px;
  font-size: 18px;
}

.modal-form #selector_hotel {
  width: 100%;
}

.modal-form #hotel_destino {
  width: 100%;
}

.modal-form .colocar_fechas {
  float: left;
}

.modal-form .fecha_salida {
  padding-top: 10px;
  clear: both;
}

.modal-form .fecha_entrada input, .modal-form .fecha_salida input {
  height: 18px;
  width: 91px;
  border: 1px solid $gray-1;
  border-radius: 4px;
  cursor: pointer;
  background: #f0f0f0 url(/img/maria/date_icon.jpg) no-repeat 72px;
  background-size: 18px;
  float: right;
}

.modal-form .contador_noches {
  display: none;
}

.modal-form .contenedor_habitaciones {
  margin-top: 10px;
  margin-bottom: 10px;
}

.modal-form .contenedor_habitaciones select {
  width: 95px !important;
  float: right;
}

.modal-form .contenedor_habitaciones label {
  float: left;
  width: 108px;
}

.modal-form .contenedor_opciones {
  margin-bottom: 10px;
}

.modal-form .contenedor_opciones select {
  float: right;
  width: 92px;
}

.modal-form .hab1 {
  position: relative;
}

.modal-form .hab2, .modal-form .hab3 {
  display: none;
}

.modal-form .numero_habitacion {
  display: none;
}

.modal-form .adultos {
  float: left;
  margin: 0 15px 0 0;
}

.modal-form .ninos {
  float: right;
}

.modal-form .info_ninos {
  position: absolute;
  line-height: 10px;
  text-align: center;
  top: 4px !important;
  left: 155px !important;
  font-size: 9px !important;
}

.modal-form .envio {
  min-height: 100px;
}

.modal-form .envio input {
  margin: 0 9px 0 0;
  color: black;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #464646;
  width: 100% !important;
}

.modal-form .envio button {
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  cursor: pointer;
  overflow: visible;
  font-size: 24px;
  display: block;
  margin: 30px auto 0;
  background-color: $corporate-1;
  color: $white;
}

.modal-form .envio button:hover {
  background: $corporate-2;
}

.modal-form .spinner {
  top: 70px !important;
  left: 120px !important;
}
