# -*- coding: utf-8 -*-
import copy
import logging
import os
from collections import OrderedDict

from flask import request

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.constants.advance_configs_names import CONTACT_PHONES, PHONE_WEB_SUPPORT, KEY_DOMAIN, \
	AGES_RANGE_IN_WIDGET, AGES_RANGE_BABYS_IN_WIDGET
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import set_namespace, get_namespace
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "jases"

SPANISH_EXTRA = {
"see_room": u"Ver habitación",
"see_opinions": u"Ver opiniones",
"T_estado_civil": u"""Estado Civil""",
"T_soltero": u"""Soltero""",
"T_pareja_hecho": u"""Pareja de hecho""",
"T_casado": u"""Casado(a)""",
"T_divorciado": u"""Divorciado(a)""",
"T_separado": u"""Separado(a)""",
"T_viudo": u"""Viudo(a)""",
"T_area_profesional": u"""Area profesional""",
"T_administracion_finanzas": u"""Administrativa y financiera""",
"T_animacion_turistica": u"""Animación turística o deportiva""",
"T_banquetes_grupos": u"""Banquetes y grupos""",
"T_comunicacion_marketing": u"""Comunicación y marketing""",
"T_comercial": u"""Comercial/Ventas""",
"T_cocina_pasteleria": u"""Cocina, despensa y pasteleria""",
"T_logistica": u"""Compras y logística""",
"T_golf": u"""Golf""",
"T_salud_spa": u"""Health Clubs/SPA """,
"T_mantenimiento_jardin": u"""Mantenimiento, piscinas y jardines""",
"T_calidad_ambiente": u"""Calidad/Ambiente""",
"T_habitaciones_pisos": u"""Habitaciones/Pisos""",
"T_recepcion": u"""Recepción y porteria""",
"T_recursos_humanos": u"""Recursos humanos""",
"T_restaurante_bar": u"""Restaurante/Bar""",
"T_seguridad": u"""Seguridad""",
"T_tecnologia": u"""Sistemas de información""",
"T_relaciones_publicas": u"""Relaciones Públicas""",
"T_otros": u"""Otros""",
"T_formacion_academica": u"""Formação académica""",
"T_educacion_basica": u"""Ensino Básico""",
"T_educacion_secundaria": u"""Secundário""",
"T_bachillerato": u"""Bacharelato""",
"T_licenciatura": u"""Licenciatura""",
"T_master": u"""Mestrado""",
"T_doctorado": u"""Doutoramento""",
"T_area": u"""Area""",
"T_cualificacion_turismo": u"""Formação em hotelaria ou turismo*""",
"T_cursos": u"""Cursos""",
"T_aleman": u"""Alemão""",
"T_espanol": u"""Espanhol""",
"T_frances": u"""Francês""",
"T_ingles": u"""Inglês """,
"T_portugues": u"""Português""",
"T_conocimientos_informaticos": u"""Conhecimentos informáticos""",
"T_situacion_profesional": u"""Situação profissional""",
"T_empleado": u"""Empregado""",
"T_desempleado": u"""Desempregado""",
"T_motivos_candidatura": u"""Diga-nos o porquê da sua candidatura""",
"T_fecha_incorporacion": u"""Por favor indique a sua disponibilidade""",
"T_inmediata": u"""Imediata""",
"T_otra": u"""Outra""",
"T_nos_encontro": u"""Como obteve conhecimento da nossa empresa""",
"T_familia": u"""Tem algum familiar ou amigo(a) a colaborar connosco?""",
"T_si": "Sim",
"T_no": u"Não",
"T_reservas": "Reservas",
"T_programas": u"Programas y experiencias",
"T_eventos": u"Eventos especiales, personales y profesionales",
"T_wellness": u"Bienestar y Wellness",
"T_gastronomia": u"Gastronomía",
"T_campanas": u"Campañas especiales",
"T_curiosidades": u"¡Cuéntanos tus mayores curiosidades!",
"T_sr": u"Sr.",
"T_sra": u"Sra.",
"T_prof": u"Prof.",
"T_dr": u"Dr."
}

PORTUGUESE_EXTRA = {
"see_room": "Ver quarto",
"see_opinions": u"Ver todas as opiniões",
"T_estado_civil": u"""Estado Civil""",
"T_soltero": u"""Solteiro""",
"T_pareja_hecho": u"""Unido(a) de fato""",
"T_casado": u"""Casado(a)""",
"T_divorciado": u"""Divorciado(a)""",
"T_separado": u"""Separado(a) de fato""",
"T_viudo": u"""Viuvo(a)""",
"T_preferencia_unidad": u"""Tem preferência por alguma unidade em particular?""",
"T_regimen_trabajo": u"""Regime""",
"T_tiempo_completo": u"""Tempo inteiro""",
"T_media_jornada": u"""Tempo Parcial (Part-time)""",
"T_practicas": u"""Estágio curricular""",
"T_area_profesional": u"""Área profissional""",
"T_administracion_finanzas": u"""Administrativo e financeiro""",
"T_animacion_turistica": u"""Animação turística ou desportiva""",
"T_banquetes_grupos": u"""Banquetes e grupos""",
"T_comunicacion_marketing": u"""Comunicação e marketing""",
"T_comercial": u"""Comercial/Vendas""",
"T_cocina_pasteleria": u"""Cozinha, copa e pastelaria""",
"T_logistica": u"""Compras e logística""",
"T_golf": u"""Golf""",
"T_salud_spa": u"""Health Clubs/SPA """,
"T_mantenimiento_jardin": u"""Manutenção, piscinas e jardins""",
"T_calidad_ambiente": u"""Qualidade/Ambiente""",
"T_habitaciones_pisos": u"""Quartos/Andares""",
"T_recepcion": u"""Recepção e portaria""",
"T_recursos_humanos": u"""Recursos humanos""",
"T_restaurante_bar": u"""Restaurante/Bar""",
"T_seguridad": u"""Segurança""",
"T_tecnologia": u"""Sistemas de informação""",
"T_relaciones_publicas": u"""Relações Públicas""",
"T_otros": u"""Outra""",
"T_formacion_academica": u"""Formação académica""",
"T_educacion_basica": u"""Ensino Básico""",
"T_educacion_secundaria": u"""Secundário""",
"T_bachillerato": u"""Bacharelato""",
"T_licenciatura": u"""Licenciatura""",
"T_master": u"""Mestrado""",
"T_doctorado": u"""Doutoramento""",
"T_area": u"""Area""",
"T_cualificacion_turismo": u"""Formação em hotelaria ou turismo*""",
"T_cursos": u"""Cursos""",
"T_aleman": u"""Alemão""",
"T_espanol": u"""Espanhol""",
"T_frances": u"""Francês""",
"T_ingles": u"""Inglês """,
"T_portugues": u"""Português""",
"T_conocimientos_informaticos": u"""Conhecimentos informáticos""",
"T_situacion_profesional": u"""Situação profissional""",
"T_empleado": u"""Empregado""",
"T_desempleado": u"""Desempregado""",
"T_motivos_candidatura": u"""Diga-nos o porquê da sua candidatura""",
"T_fecha_incorporacion": u"""Por favor indique a sua disponibilidade""",
"T_inmediata": u"""Imediata""",
"T_otra": u"""Outra""",
"T_nos_encontro": u"""Como obteve conhecimento da nossa empresa""",
"T_familia": u"""Tem algum familiar ou amigo(a) a colaborar connosco?""",
"T_si": "Sim",
"T_no": u"Não",
"T_reservas": "Reservas",
"T_programas": u"Programas e experiências",
"T_eventos": u"Eventos especiais, pessoais e profissionais",
"T_wellness": u"Bem-estar e Wellness",
"T_gastronomia": u"Gastronomia",
"T_campanas": u"Campanhas especiais",
"T_curiosidades": u"Indique-nos as suas maiores curiosidades!",
"T_sr": u"Sr.",
"T_sra": u"Sra.",
"T_prof": u"Prof.",
"T_dr": u"Dr."
}

ENGLISH_EXTRA = {
"see_room": "See room",
"see_opinions": u"See all reviews",
"T_estado_civil": u"""Marital Status""",
"T_soltero": u"""Single""",
"T_pareja_hecho": u"""Common law partner""",
"T_casado": u"""Married""",
"T_divorciado": u"""Divorced""",
"T_separado": u"""Separated from common law partner""",
"T_viudo": u"""Widowed""",
"T_preferencia_unidad": u"""Tem preferência por alguma unidade em particular?""",
"T_regimen_trabajo": u"""Regime""",
"T_tiempo_completo": u"""Tempo inteiro""",
"T_media_jornada": u"""Tempo Parcial (Part-time)""",
"T_practicas": u"""Estágio curricular""",
"T_area_profesional": u"""Professional Area""",
"T_administracion_finanzas": u"""Administrative and financial""",
"T_animacion_turistica": u"""Tourist or sports entertainment""",
"T_banquetes_grupos": u"""Banquets and groups""",
"T_comunicacion_marketing": u"""Communication and marketing""",
"T_comercial": u"""Commercial/Sales""",
"T_cocina_pasteleria": u"""Kitchen, store and bakery""",
"T_logistica": u"""Purchasing and logistics""",
"T_golf": u"""Golf""",
"T_salud_spa": u"""Health Clubs/Spa""",
"T_mantenimiento_jardin": u"""Maintenance, pools and garden""",
"T_calidad_ambiente": u"""Quality/environment""",
"T_habitaciones_pisos": u"""Rooms/apartments""",
"T_recepcion": u"""Reception and Porter's""",
"T_recursos_humanos": u"""Human resources""",
"T_restaurante_bar": u"""Restaurant/bar""",
"T_seguridad": u"""Security""",
"T_tecnologia": u"""Computing systems""",
"T_relaciones_publicas": u"""Public Relations""",
"T_otros": u"""Outra""",
"T_formacion_academica": u"""Formação académica""",
"T_educacion_basica": u"""Ensino Básico""",
"T_educacion_secundaria": u"""Secundário""",
"T_bachillerato": u"""Bacharelato""",
"T_licenciatura": u"""Licenciatura""",
"T_master": u"""Mestrado""",
"T_doctorado": u"""Doutoramento""",
"T_area": u"""Area""",
"T_cualificacion_turismo": u"""Formação em hotelaria ou turismo*""",
"T_cursos": u"""Cursos""",
"T_aleman": u"""Alemão""",
"T_espanol": u"""Espanhol""",
"T_frances": u"""Francês""",
"T_ingles": u"""Inglês """,
"T_portugues": u"""Português""",
"T_conocimientos_informaticos": u"""Conhecimentos informáticos""",
"T_situacion_profesional": u"""Situação profissional""",
"T_empleado": u"""Empregado""",
"T_desempleado": u"""Desempregado""",
"T_motivos_candidatura": u"""Diga-nos o porquê da sua candidatura""",
"T_fecha_incorporacion": u"""Por favor indique a sua disponibilidade""",
"T_inmediata": u"""Imediata""",
"T_otra": u"""Outra""",
"T_nos_encontro": u"""Como obteve conhecimento da nossa empresa""",
"T_familia": u"""Tem algum familiar ou amigo(a) a colaborar connosco?""",
"T_si": "Sim",
"T_no": u"Não",
"T_reservas": "Bookings",
"T_programas": u"Programs and experiences",
"T_eventos": u"Special events, personal and professional",
"T_wellness": u"Wellness",
"T_gastronomia": u"Gastronomy",
"T_campanas": u"Special campaigns",
"T_curiosidades": u"Tell us your biggest curiosities!",
"T_sr": "Mr.",
"T_sra": "Mrs.",
"T_prof": "Prof.",
"T_dr": "Dr."
}

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)


		result_params_dict = {'base_web': base_web,
							  'aviso_legal': get_section_from_section_spanish_name("aviso legal", language),
							  'footer_columns': get_pictures_from_section_name("_footer columns", language),
							  'booking_engine_2': self.buildSearchEngine2(language),
							  'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
							  'newsletter': self.buildNewsletter2(language, check_newsletter=True),
							  'popup_loading': get_section_from_section_spanish_name("_popup_loading", language),
		                      'footer_sections': get_pictures_from_section_name("_footer_links", language),
							  'phone_number': get_config_property_value(CONTACT_PHONES),
							  'support_web_number': get_config_property_value(PHONE_WEB_SUPPORT)
							  }

		popup_loading_pictures = get_pictures_from_section_name("_popup_loading", language)
		if popup_loading_pictures:
			for pic in popup_loading_pictures:
				if pic.get("title") == "background":
					result_params_dict['popup_loading_background'] = pic
				elif pic.get("title") == "logo":
					result_params_dict['popup_loading_logo'] = pic
			if not result_params_dict.get("popup_loading_background"):
				result_params_dict['popup_loading_background'] = popup_loading_pictures[0]

		result_params_dict["banner_icos_section"] = get_section_from_section_spanish_name("_banner_experiencias", language)
		result_params_dict["banner_icos"] = get_pictures_from_section_name("_banner_experiencias", language)

		result_params_dict['text_legal_picture'] = get_pictures_from_section_name("texto legal", language)

		if sectionToUse.get('subtitle'):
			result_params_dict['content_subtitle'] = sectionToUse

		#Automatic Content
		automatic_content = {
		'Galeria de Imagenes': True,
		'Mis Reservas Corp': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		if section_type == "Inicio":
			result_params_dict['home'] = True
			result_params_dict['logo_slider'] = get_pictures_from_section_name("floating_pendule", language)
			result_params_dict['ticks_slider'] = get_pictures_from_section_name("_slider_ticks", language)
			popup_newsletter = get_section_from_section_spanish_name("_popup_newsletter", language)
			popup_newsletter_pictures = get_pictures_from_section_name("_popup_newsletter", language)
			floating_elements = get_pictures_from_section_name("floating_pendule", language)

			if user_agent_is_mobile():
				args = {}
				if popup_newsletter and popup_newsletter_pictures:
					result_params_dict['fontawesome5'] = True
					args['popup_newsletter'] = popup_newsletter,
					args['popup_newsletter_pictures'] = popup_newsletter_pictures
				if floating_elements:
					args['floating_elements'] = floating_elements
				params = dict(list(args.items()) + list(get_web_dictionary(language).items()))
				result_params_dict['custom_element_home'] = self.buildTemplate_2("banners/_custom_element_home.html",
																					 params, False, 'jasehotels')

			else:
				result_params_dict['popup_newsletter'] = popup_newsletter
				result_params_dict['popup_newsletter_pictures'] = popup_newsletter_pictures

		if section_type == "Ofertas":
			self.getOffers(language, result_params_dict, advance_properties)


		if section_type == "Habitaciones":
			result_params_dict["banner_hoteles_destinos"] = self.getHotelsList(language)
			result_params_dict["banner_hoteles_no_slider"] = True
			result_params_dict["banner_hoteles"] = self.getHotels(language)

		if section_type == u"Habitación Individual":
			result_params_dict["banner_destinos_section"] = get_pictures_from_section_name("_destinos_blocks", language)

		if section_type == u"Localización":
			result_params_dict["form_contact"] = True
			result_params_dict["poi_hoteles"] = self.getHotels(language)

		if section_type == "Customizada":
			result_params_dict['form_contact_work'] = True
			try:
				result_params_dict['extra_terms'] = eval(language + "_EXTRA")
			except:
				result_params_dict['extra_terms'] = eval("PORTUGUESE_EXTRA")

		result_params_dict = self.getExtraBanners(advance_properties, result_params_dict, language)


		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def getOffers(self, language, result_params_dict, advance_properties):
		actual_namespace = get_namespace()
		result_params_dict['filterhotels'] = self.getHotels(language)
		result_params_dict['ofertas'] = {}
		try:
			for hotel in result_params_dict['filterhotels']:
				set_namespace(hotel.get('id'))
				offers = self.buildPromotionsInfo(language)
				hotel_domain = get_config_property_value(KEY_DOMAIN)
				offers_filtered = []
				for x in offers:
					pictures_offer = getPicturesForKey(language, str(x.get("offerKey")), [])
					x['hotel_name'] = hotel.get("value")
					if pictures_offer:
						logging.info(pictures_offer[0].get("linkUrl"))
						x['linkUrl'] = hotel_domain + "/" + pictures_offer[0].get("linkUrl")

				if advance_properties.get('tag_filter'):
					tag_filter = advance_properties['tag_filter']
					for i in offers:
						if i['extra_properties'].get('tag') == tag_filter:
							offers_filtered.append(i)

					result_params_dict['ofertas'][hotel.get('id')] = offers_filtered

				else:
					result_params_dict['ofertas'][hotel.get('id')] = offers
		finally:
			set_namespace(actual_namespace)

	def getExtraBanners(self, advance_properties, result_params, language):
		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result_params["minigallery"] = self.buildTemplate_2("_minigallery.html", {'minigallery': minigallery_images,'num_items': 5}, False, 'jasehotels')
			result_params['minigallery_content'] = get_section_from_section_spanish_name(advance_properties.get("minigallery"), language)

		if advance_properties.get("gallery_big"):
			result_params["gallery_big"] = get_pictures_from_section_name(advance_properties.get("gallery_big"), language)

		if advance_properties.get("info_block"):
			info_blocks = get_pictures_from_section_name(advance_properties.get("info_block"), language)
			for x in info_blocks:
				advance_block = self.getSectionAdvanceProperties(x, language)
				if advance_block.get("flexslider"):
					x['flexslider'] = list(filter(lambda l: l.get("title") != "paralax", get_pictures_from_section_name(advance_block.get("flexslider"), language)))
			result_params['info_blocks'] = info_blocks

		if advance_properties.get("banner_destinos"):
			result_params["banner_destinos_title"] = get_section_from_section_spanish_name(advance_properties.get("banner_destinos"), language)
			result_params["banner_destinos"] = get_pictures_from_section_name("_destinos_blocks", language)

		if advance_properties.get("banner_hoteles"):
			result_params["banner_hoteles"] = self.getHotels(language)

		if advance_properties.get("cycle_banners"):
			result_params["cycle_banners"] = self.getPicturesProperties(language, advance_properties.get("cycle_banners"), ['btn_text', 'btn_booking', 'btn_form', 'external'])

		if advance_properties.get("awards_banner"):
			result_params['awards_banner'] = get_pictures_from_section_name(advance_properties.get("awards_banner"), language)

		if advance_properties.get("virtual_tour"):
			result_params['virtual_tour'] = get_section_from_section_spanish_name(advance_properties['virtual_tour'], language)

		if advance_properties.get("dropdown"):
			result_params['dropdown'] = self.getPicturesProperties(language, advance_properties.get("dropdown"), ['jobs'])
			for x in result_params['dropdown']:
				x['jobs'] = get_pictures_from_section_name(x.get("jobs"), language)

		if advance_properties.get("banner_blocks"):
			result_params["family_blocks_section"] = get_pictures_from_section_name(advance_properties.get("banner_blocks"), language)

		if advance_properties.get("photo_overlay_text"):
			result_params["photo_overlay_text"] = True

		if advance_properties.get("newsletter_form"):
			result_params['newsletter_form'] = True
			try:
				result_params['extra_terms'] = eval(language + "_EXTRA")
			except:
				result_params['extra_terms'] = eval("PORTUGUESE_EXTRA")

		return result_params

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_5/_booking_widget.html', params, allowMobile=False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True

		if user_agent_is_mobile():
			show_babies = get_config_property_value(AGES_RANGE_BABYS_IN_WIDGET)
			if show_babies:
				options['showBabiesAgeRange'] = show_babies.split(":")

			show_kids = get_config_property_value(AGES_RANGE_IN_WIDGET)
			if show_kids:
				options['shoeKidsAgeRange'] = show_kids.split(":")

		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.getHotelsList(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, 'jasehotels')

		if user_agent_is_mobile():
			mobile_hotels = copy.deepcopy(self.getHotels(language))
			final_hotels_list = []
			for location in mobile_hotels:
				for hotel in location.get('group_list', []):
					hotel['id'] = 'https://' + hotel['namespace'] + hotel['booking_url'] + "/booking1"
					final_hotels_list.append(hotel)


			options['hotels_list_mobile'] = self.getHotels(language)

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		mini_dict = dict(get_web_dictionary(language))
		mini_dict['hotels'] = self.getHotelsList(language)
		options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, 'jasehotels')
		return options

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''
			section_name = ''
			section_type = ''
			result_dict = {'base_web': base_web}

			if currentSection:
				section_name = currentSection['sectionName'].lower().strip()
				section_type = currentSection['sectionType']

			if user_agent_is_mobile():

				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("gallery_big"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("gallery_big"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("newsletter_form"):
					args = {}
					try:
						args['extra_terms'] = eval(language + "_EXTRA")
					except:
						args['extra_terms'] = eval(language + "_EXTRA")
					args['language_code'] = get_language_code(language)
					params = dict(list(args.items()) + list(get_web_dictionary(language).items()))

					mini_html = self.buildTemplate_2("/banners/_newsletter_form.html", params, False, base_web)
					additionalParams['custom_elements'] += mini_html

				if section_type == "Ofertas":
					offers_dict = dict(get_web_dictionary(language))
					self.getOffers(language, offers_dict, advance_properties)

					offers_html = self.buildTemplate_2("/mobile/_promotions.html", offers_dict, False, 'jasehotels')
					return offers_html

				if section_type == "Habitaciones":
					result_dict['show_hotels'] = True
					result_dict['list_hotels'] = self.getHotels(language)
					result_dict['filter'] = request.values.get('filter')
					result_dict['hoteles'] = self.getHotelsList(language)
					result_dict['poi_hoteles'] = self.getHotels(language)
					result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
					return self.buildTemplate_2("/mobile/_hotel_list_mobile.html", result, False, 'jasehotels')

				if section_type == "Customizada" and advance_properties.get("dropdown"):
					result_dict['content_subtitle'] = currentSection
					result_dict['dropdown'] = self.getPicturesProperties(language,
																		   advance_properties.get("dropdown"),
																		   ['jobs'])
					for x in result_dict['dropdown']:
							x['jobs'] = get_pictures_from_section_name(x.get("jobs"), language)
					result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
					return self.buildTemplate_2("/mobile/_main_content_mobile.html", result, False, 'jasehotels')

				if section_type == u"Habitación Individual":
					result_dict['content_subtitle'] = None
					result_dict['destinos'] = self.getPicturesProperties(language, "_destinos_blocks", ['inner_pictures', 'link_text'])
					result_dict['slider_title'] = currentSection.get('subtitle')
					result_dict['slider_contet'] = currentSection.get('content')
					result_dict['list_hotels'] = self.getHotels(language)
					for destiny in result_dict['destinos']:
						destiny['classname'] = normalizeForClassName(destiny.get('title', ''))
					result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
					return self.buildTemplate_2("/mobile/_destiny_list_mobile.html", result, False, 'jasehotels')

				if section_type == u'Localización':
					result_dict['contact_access'] = True
					result_dict['content_subtitle'] = None
					result_dict['slider_title'] = currentSection.get('subtitle')
					result_dict['slider_contet'] = currentSection.get('content')
					result_dict['iframe_google_maps'] = get_section_from_section_spanish_name('Iframe google maps', language)
					result_dict['hoteles'] = self.getHotelsList(language)
					result_dict['poi_hoteles'] = self.getHotels(language)
					result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
					return self.buildTemplate_2("/mobile/_contact_form_mobile.html", result, False, 'jasehotels')

			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result['images'] = OrderedDict()
			gallery_pictures = self.get_hotel_gallery(language)
			filtered_filters = filter(
				lambda x: x.get('title') and not x.get('title') in ['ico', 'slider'] and not 'youtube' in [
					x.get('description', '')], gallery_pictures)
			all_filter_together = map(lambda x: x.get('title'), filtered_filters)
			all_available_filters = []
			for filter_element in all_filter_together:
				if not filter_element in all_available_filters:
					all_available_filters.append(filter_element)
			result['filters'] = all_available_filters
			for picture_element in gallery_pictures:
				if picture_element.get('linkUrl'):
					picture_element['video'] = picture_element['linkUrl']
			gallery_pictures_filtered = list(filter(lambda x: x.get('title') != 'slider', gallery_pictures))
			slider_pictures = list(filter(lambda x: x.get('title') == 'slider', gallery_pictures))
			result['images'] = {'images_blocks': gallery_pictures_filtered}

		if section['sectionType'] == 'Mis Reservas Corp':
			widget_hotels = self.getHotels(language)
			build_url = widget_hotels[0]['url_booking']
			split_url = build_url.split('/')
			only_host = split_url[0]
			for x in widget_hotels:
				#x['domain'] = "https://" + x.get("namespace") + x.get("url_booking")
				x['domain'] = "https://" + x.get("namespace") + only_host
				x['value'] = x.get("title")
			result['selectOptions'] = widget_hotels
			result['modify_reservation'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getHotels(self, language):

		hotels_list = self.getPicturesProperties(language, "_hotel_blocks",['destino', 'name', 'namespace', 'url_booking', 'poi_coordinates', 'poi_description', 'no_dispo'])
		available_namespaces = ';'.join(map(lambda x: x.get('namespace'), hotels_list))
		for hotel_element in hotels_list:
			if hotel_element.get('poi_coordinates'):
				poi_cordinates = hotel_element.get('poi_coordinates').split(',')
				if len(poi_cordinates) == 2:
					hotel_element['lat'] = poi_cordinates[0]
					hotel_element['lng'] = poi_cordinates[1]
			if hotel_element.get('poi_description'):
				hotel_element['poi_description'] = unescape(hotel_element.get('poi_description', '')).replace("\n", "")
			if hotel_element.get('no_dispo'):
				hotel_element['no_dispo'] = unescape(hotel_element.get('no_dispo', '')).replace("\n", "")
			hotel_element['domain'] = ''
			hotel_element['title'] = hotel_element.get('title')
			hotel_element['value'] = hotel_element.get('name')
			hotel_element['id'] = hotel_element.get('namespace')
			hotel_element['name'] = hotel_element.get('title')
			hotel_element['namespaces_list'] = available_namespaces

		return hotels_list

	def getHotelsList(self,language):
		list_hotels = {}
		hotels = self.getHotels(language)
		for hotel in hotels:
			if list_hotels.get(hotel['destino']):
				list_hotels[hotel['destino']].append(hotel)
			else:
				list_hotels[hotel['destino']] = []
				list_hotels[hotel['destino']].append(hotel)

		return list_hotels
