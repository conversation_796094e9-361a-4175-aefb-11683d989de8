$(window).load(function () {

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");

        }
    });

    $(".lang_selected").click(function () {
        $(".lang_options_wrapper").slideToggle();
    });

    $(".hotel_search_input").keydown(function () {
         setTimeout(function(){
             searchHotelElement();
         }, 100);
    });

    $(".hotel_selector_option_no_dispo").click(function () {
        var popup_index = $(this).attr("data-popup_dispo");
        $(".popup_no_dispo_"+popup_index).slideToggle();
    });

    $(".no_dispo_wrapper i.fa").click(function () {
        $(this).parent().slideToggle();
    });
});

$(window).scroll(function() {
    showWidget();
});

function showWidget() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        if ($("#full_wrapper_booking").css('display') == "block") {
            $("#full_wrapper_booking").hide();
        }
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed').slideDown();
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking.floating_booking").slideUp().promise().done(function () {
            $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed').fadeIn();
        });
    }
}



function searchHotelElement(){
    var searched_hotel = $(".hotel_search_input").val();

    $(".boking_widget_inline .hotel_selector li").each(function(){
        var actual_html = $(this).html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();


        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).css('display', 'none');
        }else{
            $(this).css('display', 'block');
        }

        if(searched_hotel == ""){
            $(this).css('display', 'block')
        }
    })
}

function booking_click(namespace) {
    if(namespace.indexOf("popup_hotel_no_dispo_") < 0) {
        prepare_booking_popup();
        open_booking_full();
        if (!only_execute_once) {
            only_execute_once = true;
        }
        if (namespace){
            $(".hotel_selector #" + namespace).trigger('click');
        }

    } else {
        $("."+namespace).slideToggle();
    }
}