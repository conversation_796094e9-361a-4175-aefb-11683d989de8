<!--# Custom JS map-->
<div class="all_hotels_map_wrapper map">
    <div id="map" style="height: 600px;width: 100%;"></div>
</div>
<script>
    var styles = {
        default: null,
        USpaces: [
                {
                    "featureType": "administrative",
                    "elementType": "labels.text.fill",
                    "stylers": [
                        {
                            "color": "#444444"
                        }
                    ]
                },
                {
                    "featureType": "landscape",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#f2f2f2"
                        }
                    ]
                },
                {
                    "featureType": "poi",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "road",
                    "elementType": "all",
                    "stylers": [
                        {
                            "saturation": -100
                        },
                        {
                            "lightness": 45
                        }
                    ]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "simplified"
                        }
                    ]
                },
                {
                    "featureType": "road.arterial",
                    "elementType": "labels.icon",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "transit",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#86BFCE"
                        },
                        {
                            "visibility": "on"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry.fill",
                    "stylers": [
                        {
                            "color": "#232325"
                        }
                    ]
                }
            ]
      };

  var hotels = [];
  var locations = [];

  {% for poi in poi_hoteles %}
  {% if poi.lat and poi.lng %}
      hotels.push("{{poi.poi_description|safe}} <a href='javaScript:booking_click(\"{{poi.namespace|safe}}\")'>{{T_reservar}}</a>");
      locations.push({lat: {{poi.lat|safe}}, lng: {{poi.lng|safe}}});
  {% endif %}
  {% endfor %}

  function initMap(center_lat, center_lng) {

        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: 4,
          center: {lat: 35.437, lng: -3.819},
          styles: styles['USpaces']
        });

        if(center_lat && center_lng) {
            var pt = new google.maps.LatLng(center_lat, center_lng);
            map.setCenter(pt);
            map.setZoom(17);
        }

        var markers = locations.map(function(location, i) {
          var marker = new google.maps.Marker({
            position: location,
            animation: google.maps.Animation.DROP,
            icon: '/img/{{ base_web }}/maps/marker-maps.png'
          });
            var infowindow = new google.maps.InfoWindow({
              content: hotels[i]
            });

            marker.addListener('click', function() {
              infowindow.open(marker.get('map'), marker);
            });
          return marker;
        });
        var markerCluster = new MarkerClusterer(map, markers,
            {imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'});

        var bounds = new google.maps.LatLngBounds();
        if(center_lat && center_lng) {
            bounds.extend(new google.maps.LatLng(center_lat, center_lng));
            bounds.extend(new google.maps.LatLng(center_lat+0.02, center_lng+0.02));
        } else {
            for (var x=0;x<locations.length;x++){
                bounds.extend(new google.maps.LatLng(locations[x].lat, locations[x].lng));
            }
        }
        map.fitBounds(bounds);
  }

</script>

<script src="/js/{{base_web}}/markerclusterer.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDrqbq8aY4Pb75flAajzNaAXtfN1PJkZXM&callback=initMap" async defer></script>