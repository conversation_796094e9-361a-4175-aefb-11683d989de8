//Base web (change too in templateHandler and in config.rb)
$base_web: "jases";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #48A4C3;
$corporate_2: #2B464B;
$corporate_3: #4B4B4B;

$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

