body.disable_scroll {
  overflow-y: hidden;
}

.popup_newsletter_wrapper {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  opacity: 0;
  position: fixed;
  z-index: 1500;
  right: 10px;
  bottom: 130px;
  transform: translateX(500px);
  transition: all 0.6s;

  .content_wrapper {
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    background-color: rgb(255, 255, 255);
    color: rgb(0, 0, 0);
    width: 90%;
    position: relative;

    .close_button {
      cursor: pointer;

      i {
        font-size: 30px;
        position: absolute;
        top: -10px;
        right: -10px;
        background: white;
        border-radius: 50%;
      }
    }

    .picture_wrapper {
      height: 180px;
      display: none;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .text_content_wrapper {
      padding: 25px;

      .title_wrapper {
        margin-bottom: 26px;
        font-size: 26px;
        letter-spacing: 1px;
        line-height: 22px;
        font-weight: normal;
      }

      .desc_wrapper {
        margin-bottom: 26px;
        font-size: 16px;
        letter-spacing: 0.6px;
        line-height: 20px;
        overflow: hidden;
      }

      .button_wrapper {
        a {
          color: rgb(255, 255, 255);
          background-color: rgb(50, 79, 84);
          font-size: 16px;
          letter-spacing: 1.6px;
          line-height: 20px;
          padding: 6px 15px;
          transition: background-color 0.35s ease 0s;
          max-width: 100%;
          white-space: nowrap;
          display: block;
        }
      }
    }
  }
  &.popup_active {
    background-color: rgba(0, 0, 0, 0.8);
    left: 0;
    top: 0;
    right: auto;
    bottom: auto;
    opacity: 1;
    width: 100%;
    height: 100vh;
    transform: none;

    .picture_wrapper {
      display: block;
    }
  }

  &.small_active {
    opacity: 1;
    transform: none;
  }
}