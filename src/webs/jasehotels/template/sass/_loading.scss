.loading_site, .white_overlay {
  @include full_size;
  z-index: 10000;
  position: fixed;
  background-position: center;
  background-size: cover;
  background-color: white;
  box-shadow: 0 0 30px rgba(0,0,0,.3);
  overflow: hidden !important;
  &:before {
    content: '';
    @include full_size;
    background-color: rgba(white,.8);
  }
  .loading_content {
    @include center_xy;
    text-align: center;

    .popup_loading_logo {
      margin-bottom: 35px;

      img {
        vertical-align: middle;
      }
    }

    h2 {
      font-family: "gotham", sans-serif;
      font-size: 20px;
      font-weight: 700;
      letter-spacing: 3px;
      color: #333;
    }
    .gif_wrapper {
      display: block;
      margin: 21px auto;
      width: 100%;
      height: 105px;
      .default_line_loading {
        background-color: #3484b2;
        height: 50px;
        width: 20px;
        display: inline-block;
        border-radius: 100%;
        -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
        -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
        -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
        animation: sk-stretchdelay 2.3s infinite ease-in-out;
        margin-right: 6px;
      }

      @-webkit-keyframes sk-stretchdelay {
        0%, 40%, 100% {
          transform: scaleY(0.4);
        }
        20% {
          transform: scaleY(1.0);
        }
      }

      @keyframes sk-stretchdelay {
        0%, 40%, 100% {
          transform: scaleY(0.4);
        }
        20% {
          transform: scaleY(1.0);
        }
      }
      .default_line_loading {
        background-color: #3e646b;
      }
    }
  }
}
.white_overlay {
  background: #6AB2D8 !important;
  &:before {
    right: 20%;
    background: #3e646b;
  }
}

.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: bold;
}

.tp-loader {
    background: none;
}