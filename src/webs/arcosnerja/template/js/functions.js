$(window).load(function () {

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");

        }
    });

    showMenu();
});

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("header").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed');
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed');
    }
}

$.simpleWeather({
    location: 'Malaga',
    woeid: '',
    unit: 'c',
    success: function (weather) {
        var weather_icon = weather.code,
            weather_img = '/static_1/images/weather/1/' + weather_icon + '.png',
            weather_temp = Math.round(weather.temp);

        $("#weather_temp_footer").find(".temp").html(weather_temp + "º");
        $("#weather_img_footer").find(".img").attr("src", weather_img);
    },
    error: function (error) {
        $("#weather").html('<p>' + error + '</p>');
    }
});

// init Masonry
var $grid = $('.gallery_1').masonry({
  itemSelector: '.crop',
  percentPosition: true,
  columnWidth: '.crop',
    gutter: 5
});

// layout Masonry after each image loads
$grid.imagesLoaded().progress( function() {
  $grid.masonry('layout');
});