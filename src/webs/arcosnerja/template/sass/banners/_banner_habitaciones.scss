.banner_habitaciones_full_wrapper {
  position: relative;
  padding: 80px 0;
  text-align: center;
  h3 {
    font-family: $title_family;
    font-size: 40px;
    color: $title_color;
    font-weight: 300;
    padding-bottom: 50px;
  }
  svg {
    @include center_x;
    top: 135px;
    width: 80px;
    * {
      fill: rgb(244, 128, 125);
    }
  }
  .banner_habitaciones_wrapper {
    display: table;
    width: 100%;
    .banner_habitaciones {
      display: inline-block;
      vertical-align: top;
      margin-right: 25px;
      width: calc(100% / 2 - 25px);
      margin-bottom: 50px;
      .image {
        position: relative;
        width: 100%;
        height: 250px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .content {
        .title {
          display: block;
          font-size: 22px;
          padding: 30px 0 20px;
          color: $corporate_2;
          font-family: $title_family;
        }
        .text {
          display: block;
          padding: 0 20px;
          font-size: 19px;
          font-weight: 300;
          color: $text_color;
          font-family: $text_family;
        }
        .link {
          display: inline-block;
          padding: 20px 70px;
          font-size: 18px;
          font-family: $title_family;
          color: $corporate_1;
          border: 2px solid $corporate_1;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-top: 15px;
          @include transition(all, .6s);
          &:hover {
            background-color: $corporate_1;
            color: white;
          }
        }
      }
      &:last-of-type {
        margin-right: 0;
      }
    }
    &.triple {
      display: block;
      .banner_habitaciones {
        width: 100%;
      }
      .owl-nav {
        .owl-prev {
          float: left;
          position: absolute;
          top: 130px;
          left: -70px;
          font-size: 45px;
        }
        .owl-next {
          float: right;
          position: absolute;
          top: 130px;
          right: -70px;
          font-size: 45px;
        }
      }
    }
  }
  &.rooms {
    padding-top: 20px;
  }
  &:not(.rooms) {
    .hide_in_home {
      display: none;
    }
  }
}