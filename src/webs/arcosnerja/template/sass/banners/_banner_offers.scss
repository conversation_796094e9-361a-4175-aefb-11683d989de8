.banner_offers_wrapper, .section_offers_wrapper {
  background-color: #F4F4F4;
  padding: 50px calc((100% - 1140px) / 2);
  h3 {
    font-family: $title_family;
    font-size: 40px;
    color: $title_color;
    font-weight: 300;
    padding-bottom: 30px;
    text-align: center;
  }
  .banner_offers {
    text-align: center;
    .offer {
      width: 85%;
      margin: 0 auto;
      &:hover {
        .offer_image {
          &:before {
            opacity: 1;
          }
          .center_xy {
            -webkit-transform: scale(1);
            -moz-transform: scale(1);
            -ms-transform: scale(1);
            -o-transform: scale(1);
            transform: scale(1);
            span {
              opacity: 1;
            }
          }
          svg {
            bottom: -6px;
          }
        }
      }
      .offer_image {
        position: relative;
        width: 100%;
        height: 330px;
        overflow: hidden;
        &:before {
          content: '';
          @include full_size;
          z-index: 2;
          background: rgba($corporate_2, .6);
          opacity: 0;
          @include transition(all, .4s);
        }
        img {
          @include center_image;
        }
        .center_xy {
          z-index: 3;
          -webkit-transform: scale(0);
          -moz-transform: scale(0);
          -ms-transform: scale(0);
          -o-transform: scale(0);
          transform: scale(0);
          @include transition(all, .6s);
          span {
            @include center_xy;
            text-align: center;
            color: white;
            font-size: 36px;
            line-height: 42px;
            font-weight: 300;
            font-family: $title_family;
            opacity: 0;
            @include transition(all, 1s);
            strong {
              font-size: 28px;
              line-height: 20px;
            }
          }
        }
        svg {
          @include center_x;
          width: 280px;
          bottom: -100%;
          @include transition(all, .8s);
          * {
            fill: rgba(255, 255, 255, .8);
          }
        }
      }
      .offer_content {
        background: white;
        .title {
          position: relative;
          text-transform: uppercase;
          font-weight: 700;
          color: $corporate_2;
          text-align: center;
          padding: 20px;
          font-size: 32px;
          letter-spacing: 1px;
          font-family: $title_family;
          &:before {
            content: '';
            @include center_x;
            bottom: 10px;
            width: 50px;
            height: 3px;
            background-color: $corporate_1;
          }
        }
        .desc {
          padding: 0 35px 20px;
          text-align: center;
          font-size: 19px;
          font-weight: 300;
          line-height: 22px;
          font-family: $text_family;
          position: relative;
          .hide_in_home {
            display: none;
          }
          &::after {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 50px;
            width: 100%;
            content: "";
            background: linear-gradient(to top, #fff 0%, rgba(255, 255, 255, 0) 100%);
            pointer-events: none;
          }
          &.expanded {
            &::after {
              display: none;
            }
          }
        }
        .btn {
          &.btn_half {
            a {
              display: inline-block;
              vertical-align: middle;
              width: 50%;
            }
          }
          a {
            display: block;
            padding: 20px;
            text-align: center;
            text-transform: uppercase;
            color: white;
            letter-spacing: 1px;
            background: $corporate_1;
            font-weight: 700;
            @include transition(all, .6s);
            &:hover {
              background: darken($corporate_1, 10%);
            }
            &:nth-of-type(2) {
              background-color: $corporate_2;
              &:hover {
                background-color: darken($corporate_2, 10%);
              }
            }
          }
        }
        .read_more {
          display: inline-block;
          padding: 20px 70px;
          font-size: 18px;
          font-family: $title_family;
          color: $corporate_1;
          border: 2px solid $corporate_1;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-top: 20px;
          margin-bottom: 20px;
          @include transition(all, .6s);
          cursor: pointer;
          &:hover {
            background-color: $corporate_1;
            color: white;
          }
        }
      }
    }

    .owl-nav {
      display: block;
      background: transparent;
      width: 100%;
      height: 0;
      @include center_y;
      .owl-prev, .owl-next {
        position: absolute;
        right: auto;
        left: 0;
        display: inline-block;
        i.fa {
          position: relative;
          z-index: 5;
          color: $corporate_2;
          font-size: 60px;
          line-height: 100px;
          padding: 0 10px;
        }
      }
      .owl-next {
        position: absolute;
        left: auto;
        right: 0;
      }
    }
  }
}

.section_offers_wrapper .desc {
    &::after {
      display: none;
    }
}


.banner_offers_wrapper .banner_offers .offer .offer_content .title {
  &:lang(en) {
      height: 120px;
  }
}