.banner_icons_full_wrapper {
  background-repeat: repeat;
  background-color: rgba(0,0,0,0.8);
  padding: 60px;
  background-image: none !important;
  display: flex;
  h3 {
    text-align: center;
    font-size: 40px;
    font-family: $title_family;
    color: white;
    font-weight: 300;
    padding-top: 20px;
    padding-bottom: 0;
    padding-left: 10%;
  }
  .banner_icons_wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    .icon {
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      width: calc(100% / 6);
      i {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        font-size: 34px;
        color: white;
        background-color: $corporate_1;
        &:before {
          @include center_xy;
        }
      }
      span {
        display: block;
        text-align: center;
        margin-top: 20px;
        font-family: $text_family;
        color: white;
        font-weight: 300;
      }
    }
  }
}