/*.cycle_banners_wrapper {
  padding: 50px calc((100% - 1140px) / 2);

  .cycle_element {
    display: table;
    width: 100%;
    position: relative;
    margin-bottom: 80px;

    &:nth-child(odd) {
      .cycle_image {
        float: left;
      }

      .cycle_content {
        float: right;
      }
    }

    &:nth-child(even) {
      .cycle_image {
        float: right;
      }

      .cycle_content {
        float: left;
      }
    }

    .cycle_image {
      display: inline-block;
      width: 65%;
      position: relative;
      overflow: hidden;

      img {
        @include center_image;
        max-width: none;
        min-width: 100%;
        min-height: 100%;
        width: auto;
      }
    }

    .cycle_content {
      display: inline-block;
      width: 35%;
      box-sizing: border-box;
      padding: 55px;
      position: relative;

      .cycle_title {
        font-size: 24px;
        color: $corporate_1;
        margin-bottom: 40px;
        font-weight: 400;
        font-family: $title_family;
      }

      .cycle_description {
        font-size: 19px;
        font-weight: 300;
        font-family: $text_family;
        strong {
          color: $corporate_1;
        }
      }

      .cycle_baner_see_more {
        text-align: center;
        a {
          position: relative;
          display: block;
          vertical-align: middle;
          margin: 30px auto;
          text-align: center;
          background-color: $corporate_1;
          font-size: 20px;
          text-transform: uppercase;
          color: white;
          padding: 10px 0;
          @include transition(background, .4s);

          &:hover {
            background: darken($corporate_1, 10%);
          }
        }
      }
    }
  }
}*/

.cycle_banners_wrapper {
  padding: 50px calc((100% - 1140px) / 2);

  .cycle_element {
    padding: 50px 0;
    display: flex;
    vertical-align: top;
    align-items: center;
    border-bottom: 1px solid #DDD;
    .cycle_image {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 760px;
      height: 400px;
      overflow: hidden;
      img {
        @include center_xy;
        min-height: 100%;
        min-width: 100%;
        width: auto;
        max-width: none;
      }
    }
    .cycle_content {
      display: inline-block;
      vertical-align: top;
      width: 440px;
      float: right;
      box-sizing: border-box;
      padding-left: 45px;
      z-index: 2;
      @include transition(top, .6s);
      .cycle_title {
        font-size: 24px;
        color: $corporate_1;
        margin-bottom: 40px;
        font-weight: 400;
        font-family: $title_family;
      }

      .cycle_description {
        font-size: 19px;
        font-weight: 300;
        font-family: $text_family;
        strong {
          color: $corporate_1;
        }
        .hide_text {
          display: none;
        }
      }

      .cycle_baner_see_more {
        text-align: center;
        a {
          position: relative;
          display: block;
          vertical-align: middle;
          margin: 30px auto;
          text-align: center;
          background-color: $corporate_1;
          font-size: 20px;
          text-transform: uppercase;
          color: white;
          padding: 10px 0;
          @include transition(background, .4s);

          &:hover {
            background: darken($corporate_1, 10%);
          }
        }
      }
    }
    &:nth-child(even) {
      flex-direction: row-reverse;
      .cycle_content {
        padding-left: 0;
        padding-right: 45px;
      }
    }
    &:last-of-type {
      border-bottom-width: 0;
    }
    &:first-of-type {
      padding-top: 0;
    }

    &.cycle2 {
      .cycle_image {
        width: 300px;
        height: 300px;
      }
      .cycle_content {
        width: 900px;
      }
    }
  }
}