.minigallery_wrapper {
  width: 100%;
  overflow: hidden;
  margin-bottom: 80px;
  .owl-item {
    background-color: $corporate_1;
    height: 300px;
    overflow: hidden;

    img {
      width: auto;
      opacity: 1;
      @include transition(all, .6s);
    }
    span {
      display: block;
      width: 90%;
      color: white;
      font-size: 20px;
      text-align: center;
      text-shadow: 0 0 5px rgba(0, 0, 0, .6);
      @include center_xy;
      i.fa {
        display: block;
        text-align: center;
        font-size: 25px;
      }
    }

    &:hover {
      img {
        opacity: .4;
      }
      .minigallery_desc {
        img {
          opacity: .8;
        }
      }
    }
    .minigallery_desc {
      img {
        opacity: .4;
      }
    }
  }

  .owl-nav {
    & > div {
      @include center_y;
      display: block;
      color: white;
      cursor: pointer;
      font-size: 60px;
      width: 100px;
      height: 100px;
      line-height: 100px;
      padding: 0 10px;
      overflow: hidden;
      &:before {
        content: '';
        width: 100px;
        height: 100px;
        display: block;
        background-color: rgba($corporate_1, .6);
        @include center_y;
        left: -50%;
        border-radius: 50%;
        @include transition(background-color, .6s);
      }
      i.fa {
        position: relative;
        z-index: 5;
        &.fa-chevron-right:before {
          content: "\f105";
        }
        &.fa-chevron-left:before {
          content: "\f104";
        }
      }
      &:hover {
        &:before {
          background-color: $corporate_1;
        }
      }
    }

    .owl-prev {
      left: 0;
    }

    .owl-next {
      right: 0;
      text-align: right;
      &:before {
        left: auto;
        right: -50%;
      }
    }
  }
}