.detailed_room_wrapper {
  padding: 50px calc((100% - 1140px) / 2);
  .room_detail_image_wrapper {
    width: 100%;
    height: 550px;
    position: relative;
    overflow: hidden;

    .room_detail_image {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }

  a.see_more_pictures_detailed {
    position: absolute;
    z-index: 1;
    bottom: 25px;
    right: 25px;
    text-transform: uppercase;
    text-decoration: none;
    color: white;

    span {
      display: inline-block;
      vertical-align: middle;
    }

    .fa {
      border: 2px solid white;
      position: relative;
      vertical-align: middle;
      width: 42px;
      height: 42px;
      margin-left: 10px;

      &:before {
        @include center_xy;
        font-size: 21px;
      }
    }
  }

  .room_content {
    display: flex;
    .room_details_text {
      display: inline-block;
      width: 50%;
      z-index: 2;
      position: relative;
      background: white;
      padding: 35px 40px 40px;

      h1.room_title {
        font-family: $title_family;
        font-size: 40px;
        color: $corporate_2;
        font-weight: 300;
        padding-bottom: 50px;
      }

      .room_description {
        display: block;
        font-size: 19px;
        font-weight: 300;
        color: $text_color;
        line-height: 27px;
        font-family: $text_family;
      }
    }

    .minigallery_room_wrapper {
      display: inline-block;
      width: 50%;
      margin-top: 30px;

      .minigallery_element {
        display: inline-block;
        float: left;
        width: calc(25% - 10px);
        height: 138px;
        margin-right: 13px;
        position: relative;
        overflow: hidden;
        margin-top: 13px;

        &:nth-child(4n), &:last-child {
          margin-right: 0;
        }

        &:nth-child(-n+4) {
          margin-top: 0;
        }

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }
    }
  }
  .room_icons_wrapper {
    display: table;
    width: 100%;
    text-align: center;
    .icon {
      display: inline-block;
      vertical-align: top;
      width: calc(100% / 8);
      padding: 10px;
      text-align: center;
      i {
        display: block;
        font-size: 42px;
        margin-bottom: 5px;
        color: $corporate_1;
      }
      .text {
        font-size: 16px;
        font-weight: 300;
        color: $text_color;
        font-family: $text_family;
      }
    }
  }
}

.plaza-cavana {

  .detailed_room_wrapper .room_content {

    > strong {
      width: 50%;
    }
    .minigallery_room_wrapper {
      width: 100%;
    }
  }
}