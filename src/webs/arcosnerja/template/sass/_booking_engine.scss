.datepicker_wrapper_element .header_datepicker, .datepicker_wrapper_element_2 .header_datepicker, .datepicker_wrapper_element_3 .header_datepicker {
  background-color: $corporate_1;
}

#full_wrapper_booking {
  position: absolute;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  top: 0;
  opacity: 0;
  @include transition(opacity, 1s);

  &.default_view {
    opacity: 1;
  }

  .cancel_booking_link {
    display: none;
  }
  #full-booking-engine-html-7 {
    display: inline-block;
    width: 100%;
    text-align: center;

    .booking_form_title {
      display: none;
    }

    .booking_form {
      display: inline-block;
      text-align: left;
      position: relative;

      .stay_selection {
        display: inline-block;
        vertical-align: middle;

        .entry_date_wrapper, .departure_date_wrapper {
          display: inline-block;
          vertical-align: middle;
          width: 200px;
          background-color: white;
          margin-right: 2px;

          label {
            display: none;
          }

          .date_box {
            padding: 10px 10px 10px 35px;
            position: relative;
            font-weight: 300;

            &:before {
              content: '\f133';
              font-family: "Fontawesome";
              color: $corporate_1;
              @include center_y;
              left: 10px;
              font-size: 18px;
            }

            &:after {
              @include arrow;
            }

            .date_day {
              border-bottom: 0 !important;
              display: inline-block;
              margin-top: 2px;
              font-size: 16px;
            }

            .date_year {
              display: none;
            }
          }
        }
      }

      .dates_selector_personalized {
        display: none;
      }

      .rooms_number_wrapper {
        display: inline-block;
        vertical-align: middle;
        width: 220px;
        background-color: white;
        margin-right: 2px;
        padding: 10px 10px 10px 35px;
        position: relative;

        &:before {
          content: '\62';
          font-family: "icomoon";
          color: $corporate_1;
          @include center_y;
          left: 10px;
          font-size: 22px;
        }

        label {
          display: none;
        }
      }

      .guest_selector {
        display: inline-block;
        vertical-align: middle;
        background-color: white;
        width: 185px;
        margin-right: 2px;
        padding: 10px 10px 10px 35px;
        font-size: 16px;
        font-weight: 300;
        position: relative;
        .button {
          display: none;
        }

        &:before {
          content: '\f0c0';
          font-family: "Fontawesome";
          color: $corporate_1;
          @include center_y;
          left: 10px;
        }

        &:after {
          @include arrow;
        }

        label {
          display: none;
        }
      }

      .room_list_wrapper {
        display: none;
        background-color: #fff;
        position: absolute;
        top: calc(100% + 20px);
        left: 635px;
        width: 185px;
        padding: 10px;
        box-shadow: 0 0 10px 0 rgba(black, .3);

        .room_list {
          margin: auto;
          list-style: none;

          .room {
            &.room1 {
              .adults_selector, .children_selector {
                margin-top: 0;
              }
            }

            .room_title {
              display: none;
            }

            .adults_selector, .children_selector {
              display: inline-block;
              width: calc(50% - 5px);
              float: left;
              margin-top: 10px;
            }

            .adults_selector {
              margin-right: 10px;
            }
          }
        }
      }

      .wrapper_booking_button {
        display: inline-block;
        vertical-align: middle;

        .promocode_wrapper {
          display: inline-block;
          vertical-align: middle;
          margin-right: 2px;

          .promocode_label {
            display: none;
          }

          .promocode_input {
            background-color: white;
            height: 38px;
            width: 160px;
            text-align: center;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding-top: 5px;
            font-size: 16px;
            border: 0;
            border-radius: 0;
            text-transform: uppercase;
            &::-webkit-input-placeholder {
              color: $title_color;
              font-size: 16px;
              font-family: $title_family;
              font-weight: 400;
            }
            &::-moz-placeholder {
              color: $title_color;
              font-size: 16px;
              font-family: $title_family;
              font-weight: 400;
            }
            &:-ms-input-placeholder {
              color: $title_color;
              font-size: 16px;
              font-family: $title_family;
              font-weight: 400;
            }
            &:-moz-placeholder {
              color: $title_color;
              font-size: 16px;
              font-family: $title_family;
              font-weight: 400;
            }
          }
        }

        .submit_button {
          display: inline-block;
          vertical-align: middle;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 0;
          font-family: $title_family;
          border-radius: 0;
          background-color: $corporate_1;
          color: white;
          text-transform: uppercase;
          height: 38px;
          width: 125px;
          font-size: 16px;
          font-weight: 400;
          cursor: pointer;
          @include transition(background-color, .6s);
          &:hover {
            background-color: $corporate_2;
          }
        }
      }

      .selectricWrapper {
        position: relative;
        width: auto;

        .selectricHideSelect, .selectricInput {
          display: none;
        }

        &.selectricOpen {
          .selectricItems {
            display: block;
          }
        }

        .selectric {
          display: inline-block;
          width: 100%;
          font-weight: 300;
          position: relative;
          height: auto;

          &:before {
            @include arrow;
          }

          .label {
            color: $text_color;
            margin: 0;
            font-size: 16px;
            line-height: normal;
            overflow: visible;
          }

          .button {
            display: none;
          }
        }

        .selectricItems {
          position: absolute;
          top: calc(100% + 10px);
          background-color: white;
          display: none;
          z-index: 5;

          ul {
            margin: 0;
            list-style: none;

            li {
              margin: 0;
              background-color: white;
              text-align: center;
              color: $text_color;
              padding: 10px 5px;
              font-weight: 600;
              cursor: pointer;
              @include transition(all, .4s);

              &:hover {
                background-color: rgba($corporate_1, .6);
                color: white;
              }

              &.selected {
                background-color: $corporate_1;
                color: white;
              }
            }
          }
        }
      }
    }
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  width: 100%;
}