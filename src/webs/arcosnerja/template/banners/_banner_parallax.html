<div class="banner_parallax_wrapper">
    {% for banner in banner_parallax %}
        <a {% if banner.linkUrl %} href="{{ banner.linkUrl|safe }}" {% endif %} class="banner_parallax {% if banner.linkUrl %}with_link{% endif %}" {% if banner.servingUrl %}style="background-image: url('{{ banner.servingUrl|safe }}=s1600')"{% endif %}>
            {% if banner.description %}
                <div class="content">
                    {% if banner.title %}<h4>{{ banner.title|safe }}</h4>{% endif %}
                    <div class="waves">
                        {% include "banners/waves.html" %}<br>
                        {% include "banners/waves.html" %}<br>
                        {% include "banners/waves.html" %}
                    </div>
                    <div class="text">{{ banner.description|safe }}</div>
                </div>
            {% endif %}
        </a>
    {% endfor %}
</div>

{#
{% if not is_mobile %}
    <script>
    $(window).load(function () {
        function banner_parallax_fx() {
            $(".banner_parallax_wrapper .banner_parallax").addnimation({parent:$(".banner_parallax_wrapper"),class:"jackInTheBox"});
        }
        banner_parallax_fx();
        $(window).scroll(banner_parallax_fx);
    });
    </script>
{% endif %}
#}