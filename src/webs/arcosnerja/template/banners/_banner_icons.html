<div class="banner_icons_full_wrapper" {% if banner_icons_pattern and banner_icons_pattern.0.servingUrl %} style="background-image: url('{{ banner_icons_pattern.0.servingUrl|safe }}');" {% endif %}>
    {% if banner_icons_text and banner_icons_text.subtitle %}<h3>{{ banner_icons_text.subtitle|safe }}</h3>{% endif %}
    <div class="banner_icons_wrapper container12">
        {% for icon in banner_icons %}
            <div class="icon">
                <i class="fa {{ icon.title|safe }}"></i>
                {% if icon.description %}
                    <span>{{ icon.description|safe }}</span>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>

{% if not is_mobile %}
    <script>
    $(window).load(function () {
        function banner_icons_fx() {
            for (x = 1; x <= {{ banner_icons|length }}; x++) {
                //$(".banner_icons_wrapper .icon:nth-child(" + x + ")").addnimation({parent:$(".banner_icons_full_wrapper"), class:"fadeInLeftBig", classOut:"fadeOutRightBig", delay: x * 200});
                $(".banner_icons_wrapper .icon:nth-child(" + x + ")").addnimation({parent:$(".banner_icons_full_wrapper"), class:"fadeInLeftBig", delay: x * 80, reiteration: false});
            }
        }
        banner_icons_fx();
        $(window).scroll(banner_icons_fx);
    });
    </script>
{% endif %}