<div class="banner_news_full_wrapper container12">
    <div class="background"><i class="fa icon-desktop"></i></div>
    {% if banner_news_text and banner_news_text.subtitle %}<h3>{{ banner_news_text.subtitle|safe }}</h3>{% endif %}
    {% include "banners/waves.html" %}
    <div class="banner_news_wrapper owl-carousel">
        {% for new in banner_news %}
            <div class="new_home">
                {% if new.author %}<span class="author">{{ new.author|safe }}: </span>{% endif %}
                {% if new.name %}<span class="title">"{{ new.name|safe }}"</span>{% endif %}
                {% if new.friendlyUrl %}<div class="link"><a href="/{{ path }}/{{ new.friendlyUrl }}" class="btn_content">{{ T_leer_mas }}</a></div>{% endif %}
            </div>
        {% endfor %}
    </div>
</div>

<script>$(window).load(function () {
    $(".banner_news_wrapper").owlCarousel({
        loop: true,
        nav: true,
        dots: true,
        navText: ['<i class="fa fa-angle-left" aria-hidden="true"></i>', '<i class="fa fa-angle-right" aria-hidden="true"></i>'],
        items: 1,
        margin: 0,
        autoplay: true
    });
});</script>

{% if not is_mobile %}
    <script>
    $(window).load(function () {
        function banner_news_fx() {
            $(".banner_news_full_wrapper .banner_news_wrapper").addnimation({parent:$(".banner_news_full_wrapper"),class:"shake", reiteration: false});
        }
        banner_news_fx();
        $(window).scroll(banner_news_fx);
    });
    </script>
{% endif %}