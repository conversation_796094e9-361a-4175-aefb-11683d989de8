<div class="section_offers_wrapper">
    <div class="banner_offers">
        {% for offer in offers %}<div class="offer {% if offer.priority and 'P' in offer.priority %}paquete{% else %}oferta{% endif %}">
            <div class="offer_image">
                <img data-src="{{ offer.picture }}=s500-c" alt="{{ offer.name }}" lazy="true">
                <div class="center_xy"><span>{% if offer.picDesc %}{{ offer.picDesc|safe }}{% endif %}</span></div>
            </div>
            <div class="offer_content">
                <div class="title">{{ offer.name|safe }}</div>
                <div class="desc">{{ offer.description|safe }}</div>
            </div>
            <div class="btn {% if offer.linkUrl %}btn_half{% endif %}">
                <a href="#data" class="button_promotion" data-not_in="{{ offer.not_in }}">{{ T_reservar }}</a><!--
                -->{% if offer.linkUrl %}<a href="{{ offer.linkUrl }}">{{ T_ver_mas }}</a>{% endif %}
            </div>
        </div>{% endfor %}
    </div>
</div>

<script>

    $(window).load(function () {
        offers_height();
    });

    function offers_height() {
        max_height = 0;
        $(".offer").each(function () {
            actual_height = $(this).height();

            if (actual_height > max_height) {
                max_height = actual_height
            }
        });

        $(".offer").height(max_height);
    }

</script>