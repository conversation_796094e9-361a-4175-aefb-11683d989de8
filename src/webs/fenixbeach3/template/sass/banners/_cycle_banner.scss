.cycle_banners_wrapper {
    padding: 60px 0 150px;
    display: inline-block;
    width: 100%;


    .cycle_element {
        display: block;
        width: 100%;
        height: 495px;
        position: relative;

        &:nth-child(odd) {

            .cycle_image {
                float: left;
            }

            .cycle_content {
                float: right;
            }
        }

        &:nth-child(even) {

            .cycle_image {
                float: right;
            }

            .cycle_content {
                float: left;
            }
        }

        .cycle_image {
            display: inline-block;
            width: 50%;
            height: 495px;
            position: relative;
            overflow: hidden;

            img {
                @include center_image;
                max-width: none;
                min-width: 100%;
                min-height: 100%;
                width: auto;
            }
        }

        .cycle_content {
            display: inline-block;
            width: 50%;
            height: 495px;
            background-color: $lightgrey;
            box-sizing: border-box;
            padding: $xl_pad;
            position: relative;

            .title {
                font-size: $font_lg;
                color: $black;
                margin-bottom: 40px;
                font-weight: 700;
            }

            .desc {
                font-size: 1rem;
                font-weight: 300;
                color: $black;

                strong  {
                    font-weight: 700;
                }
            }

            .cycle_baner_see_more {
                text-align: center;
                position: absolute;
                bottom: 0;
                left: 60px;
                right: 60px;


                a {
                    position: relative;
                    display: block;
                    vertical-align: middle;
                    margin: 30px auto;
                    text-align: center;
                    background-color: $corporate_2;
                    font-size: 20px;
                    text-transform: uppercase;
                    color: white;
                    padding: 10px 0;
                    font-weight: 300;

                    span {
                        position: relative;
                    }

                    &:before {
                        content: '';
                        @include full_size;
                        background-color: rgba(0, 0, 0, 0.3);
                        width: 0;
                        @include transition(all, .6s);
                    }

                    &:hover {
                        &:before {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}