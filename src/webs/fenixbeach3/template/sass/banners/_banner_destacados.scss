.banner_destacados_wrapper {
     @extend .sec_pad;
     position: relative;
     background-color: white;

     .services {
         position: relative;
         text-align: center;
         margin: auto;

         .service {
             position: relative;
             display: inline-block;
             border-radius: 10px;
             width: 350px;
             background-color: $corporate_1;
             @include transition(all, .6s);

             span {
                 display: block;
                 width: 100%;
                 padding: 20px 0;
                 text-align: center;
                 font-size: 1rem;
                 font-weight: 500;
                 color: white;
             }

             i.fa {
                 @include center_y;
                 left: 20px;
                 color: white;
                 width: 30px;
                 height: 30px;
                 font-size: 45px;

                 &:before {
                     @include center_xy;
                }
             }

             &.service_link:hover {
                 background-color: $corporate_2;
                 cursor: pointer;
             }
         }
     }

     /*.owl-nav {
         font-size: 25px;
         position: absolute;
         bottom: 0;
         left: 0;
         right: 0;

         .owl-prev, .owl-next {
             position: absolute;
             bottom: 130px;
             cursor: pointer;
             width: 40px;
             height: 40px;
             border-radius: 50%;
             background-color: transparent;
             color: $corporate_1;
             display: inline-block;
             margin: 10px;
             @include transition(all, .6s);

             i.fa {
                 @include center_xy;
             }

             &:hover {
                 background-color: $corporate_1;
                 color: white;
             }
         }

         .owl-prev {
             left: -65px;
             bottom: 0;
         }

         .owl-next {
             right: -65px;
             bottom: 0;
         }
     }*/
}
