.section_offers_wrapper {
    padding: 20px calc((100% - 1140px) / 2);

    .banner_offers {
        text-align: center;
        min-height: 600px;

        .offer {
            background-color: white;
            position: relative;
            display: inline-block;
            vertical-align: top;
            width: calc((100% - 60px) / 3);
            margin: 0 10px 20px;

            &:hover {

                .offer_image {

                    &:before {
                        opacity: 1;
                    }

                    .center_xy {
                        transform: scale(1);

                        span {
                            opacity: 1;
                        }
                    }
                }
            }

            .offer_image {
                position: relative;
                width: 100%;
                height: 330px;
                overflow: hidden;

                &:before {
                    content: '';
                    @include full_size;
                    z-index: 2;
                    background: rgba($corporate_3, .8);
                    opacity: 0;
                    @include transition(all, .6s);
                }

                img {
                    @include center_image;
                }

                .center_xy {
                    z-index: 3;
                    transform: scale(0);
                    @include transition(all, .6s);

                    span {
                        @include center_xy;
                        text-align: center;
                        color: white;
                        font-size: 1rem;
                        line-height: 20px;
                        font-weight: 700;
                        opacity: 0;
                        @include transition(all, 1s);

                        strong {
                            font-size: $font_xl;
                            line-height: 25px;
                        }
                    }
                }
            }

            .offer_content {

                .title {
                    text-transform: uppercase;
                    font-weight: 700;
                    text-align: center;
                    padding: 30px 15px 0;
                    font-size: 1rem;
                    letter-spacing: 1.5px;
                    color: $grey;
                }

                .desc {
                    padding: 30px 15px 60px;
                    text-align: center;
                    font-size: 1rem;
                    margin: 0;

                    strong {
                        font-weight: 700;
                    }
                }
            }

            .btn {
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: $corporate_1;
                padding: 0;
                color: white;
                &.btn_half {
                    a {
                        display: inline-block;
                        width: 50%;
                        color: white;
                    }
                }
                a {
                    display: block;
                    padding:  10px 7.5px;
                    text-align: center;
                    text-transform: uppercase;
                    color: white;
                    letter-spacing: 1px;
                    font-weight: 700;
                    @include transition(all, .6s);

                    &:nth-of-type(2) {
                        background-color: $corporate_3;
                    }

                    &:hover {
                        background-color: darken($corporate_1, 20%);
                    }
                }
            }
        }
    }
}

/*
.section_offers_wrapper {

    .banner_offers {
        text-align: left;

        .offer {
            display: inline-block;
            vertical-align: top;
            width: calc((100% - 60px) / 3);
            margin: 0 10px 20px;
        }
    }
}

*/


