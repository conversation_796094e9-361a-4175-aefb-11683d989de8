header {

    .top_header {
        background-color: $lightgrey;
        text-align: right;
        padding: 5px 0;

        .container_top {
            display: flex;
            padding: $xs_pad 0;
            color: $corporate_3;

            > div {
                width: 50%;
            }

            .phone_wrapper {
                text-align: left;
            }

            .links_wrapper {
                text-align: right;


                > div {
                    display: inline-block;
                    position: relative;
                    padding: 0 0 0 25px;
                    margin-left: 25px;

                    &::before {
                        position: absolute;
                        font-family: "icomoon", "Font Awesome 5 Pro";
                        color: $corporate_1;
                        font-size: 20px;
                        top: 0;
                        bottom: 0;
                        left: 0;
                    }

                    &:after {
                        position: absolute;
                        content: '';
                        top: 0;
                        bottom: 0;
                        left: -15px;
                        width: 1px;
                        background-color: $black;
                    }

                    &#social {

                        a {
                            color: $corporate_1;
                            font-size: 20px;

                            i {

                                &::before {
                                    position: absolute;
                                    top: 5px;
                                    left: 0;
                                }
                            }
                        }
                    }

                    &#top-sections {
                        a:first-of-type {
                            padding-right: 5px;
                        }
                        a:last-of-type {
                            position: relative;
                            padding-left: 10px;
                            &:after {
                                position: absolute;
                                content: '';
                                top: 0;
                                bottom: 0;
                                left: 0;
                                width: 1px;
                                background-color: #277c9c;
                            }
                        }
                        &::before {
                            content: '\ea03';
                        }
                    }

                    &#lang {
                        position: relative;

                        &::before {
                            content: '\f7a2';
                        }

                        .lang_options_wrapper.active {
                            opacity: 1;
                            margin-top: 0;
                        }

                        .lang_selected {
                            display: inline-block;
                            cursor: pointer;
                            @include transition(color, .6s);

                            i {
                                padding-left: 7.5px;
                            }

                            &:hover {
                                color: $corporate_2;
                            }
                        }

                        .lang_options_wrapper {
                            position: absolute;
                            top: 38px;
                            right: 0;
                            left: 0;
                            opacity: 0;
                            margin-top: -25px;
                            background: white;
                            filter: drop-shadow(0 0 15px rgba(0, 0, 0, 0.15));
                            border-radius: 5px;
                            @include transition(all, .6s);
                            z-index: 1;

                            &:before {
                                content: '';
                                display: block;
                                @include center_x;
                                top: -20px;
                                border: 10px solid transparent;
                                border-color: transparent transparent white transparent;
                            }

                            a {
                                display: block;
                                padding: 8px 15px;
                                text-align: center;
                                color: $black;
                                @include transition(color, .6s);
                                text-transform: capitalize;

                                &:hover {
                                    color: $corporate_1;
                                }

                                &:first-of-type a {
                                    border-radius: 5px 5px 0 0;
                                }

                                &:last-of-type a {
                                    border-radius: 0 0 5px 5px;
                                }
                            }
                        }
                    }

                    &:first-child {

                        &::after {
                            display: none;
                        }

                    }
                }
            }
        }
    }

    .bottom_header {
        background-color: $corporate_1;

        .container_bottom {
            position: relative;

            #logoDiv {
                float: left;
                position: absolute;
                top: 50%;
                transform: translate(0%, -50%);
                left: 0;

                img {
                    height: 70px;
                }
            }

            #main_menu {
                float: right;

                #main-sections-inner {

                    .main-section-div-wrapper {
                        display: inline-block;
                        position: relative;

                        a {
                            display: inline-block;
                            color: white;
                            text-transform: uppercase;
                            padding: 50px 20px;
                            position: relative;
                        }

                        &#section-active {

                            a {
                                background-color: white;
                                color: $corporate_1;
                            }
                        }

                        &::after {
                            content: "";
                            width: 5px;
                            height: 5px;
                            display: inline-block;
                            background: white;
                            border-radius: 50%;
                            position: absolute;
                            top: 50%;
                            transform: translate(0%, -50%);
                            right: 0;
                        }
                        &:last-of-type::after {
                            display: none;
                        }
                    }
                }
            }
        }
    }
}