//Base web (change too in config.rb)
$base_web: "feni3";

$corporate_1: #9cd6e5;
$corporate_2: #59cdea;
$corporate_3: #277c9c;

$light_corportate_1: lighten($corporate_1, 35%);
$black: $corporate_3;
$grey: #acacac;
$lightgrey: #f2f2f2;

$body_text_color: $black;
$light_body_text_color: lighten($body-text-color, 50%);
$links_color: $corporate_3;

/* Fonts */
$primary_font: 'Lato', sans-serif;
/* End */

/* Font sizes */
$font_xl: 3rem;
$font_lg: 2rem;
$font_sm: .85rem;
/* End */

$shadow: 0px 10px 25px -15px rgba(0, 0, 0, 0.30);
$light_shadow: 0px 10px 25px -15px rgba(0, 0, 0, 0.15);

$radius: 4px;

$main_pad: 15px;
$xs_pad: $main_pad / 2;
$lg_pad: $main_pad * 2;
$xl_pad: $main_pad * 4;
$xxl_pad: 100px;

// colors for booking widget
$booking_widget_color_1: white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: $grey; //label texts
$booking_widget_color_4: $grey; //not used, but must be defined


@mixin title_styles() {
  font-family: $primary_font;
  font-size: 32px;
  line-height: 38px;
  color: $corporate_1;
}


@mixin text_styles() {
  font-weight: 300;
  font-family: $primary_font;
  font-size: 19px;
  line-height: 25px;
  color: $black;
}

@mixin clearfix {

    &::after {
        display: block;
        clear: both;
        content: "";
    }
}

@mixin x {
    outline: 1px solid red;
}

@mixin btn_styles() {

}

@mixin d-flex ($width) {

    display: flex;
    flex-flow: row nowrap;

    > div {
        width: $width;
    }
}


@mixin svg_styles {
  display: block;
  margin: 0 auto;
  height: auto;
  width: 100px;
  * {
    fill: $grey;
  }
}



/* utils */
.text_left {
    text-align: left;
}

.text_right {
    text-align: right;
}

.text_center {
    text-align: center;
}

.text_primary {
    color: $corporate_1!important;
}

.desc {
    margin: 15px 0 10px 0;
}

.lead {
    font-weight: 300;
    font-size: 1.3em;
}

.sec_pad {
    padding: $xl_pad 0;

    @media (max-width: 767px) {
        padding: $lg_pad 0;
    }
}

.d-none {
    display: none!important;
}

.d-block {
    display: d-block!important;
}

.btn {
    position: relative;
    border: none;
    text-align: center;
    padding: 8px 25px 8px;
    text-decoration: none;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 1.2rem;
    transition: all .5s;

    a {
        color: inherit!important;
    }

    &.btn_primary {
        background-color: $corporate_1;
        color: white;
    }

    &.btn_secondary {
        background-color: $corporate_2;
        color: white;
    }

    &.btn_tertiary {
        background-color: $corporate_3;
    }

    &.btn_white {
        background-color: white;
    }

    &.btn_light {
        background-color: $lightgrey;
    }

    &.btn_dark {
        background-color: $black;
        color: white;
    }

    &.btn_block {
        width: 100%;
    }

    /* luego insertar unicode correspondiente en el before. EJ en los Process Buttons */
    &.btn_icon {
        position: relative;

         &::before {
             position: absolute;
             font-size: 1.2em;
             left: 15px;
             font-family: 'icomoon' , 'Font Awesome 5 Pro';
        }
    }
}

.bg_primary {
    background-color: $corporate_1;
}

.bg_secondary {
    background-color: $corporate_2;

    > * {
        color: white!important;
    }
}

.bg_grey {
    background-color: $lightgrey;
}

.bg_custom {
    background: linear-gradient(-160deg, #ffffff 20%, $lightgrey 20%, $lightgrey);
}

.bg_gradient {
    position: relative;

    &::before {
        position: absolute;
        content: '';
        height: 80px;
        outline: 1px solid red;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgb(255,255,255);
        background: linear-gradient(180deg, rgba(255,255,255,0) 50%, rgba(172,172,172,0.10968137254901966) 75%);
    }
}

.oval_shape {
    width: 100px;
    height: 300px;
    background-color: blue;
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
}


/* Tooltips */
.has_tooltip {
    display: inline-block;
}

[data-tooltip] {
    position: relative;

    &:before, &:after {
        position: absolute;
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
        transition: all 0.15s cubic-bezier(0.5, 1, 0.25, 1);
        z-index: 1;
    }

    &:before {
        padding: 5px;
        width: 110px;
        border-radius: 3px;
        background: $corporate_2;
        color: $corporate_1;
        content: attr(data-tooltip);
        text-align: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.2;
    }

    &:after {
        border: 8px solid transparent;
        width: 0;
        content: "";
        font-size: 0;
        line-height: 0;
    }

    &:hover {

        &:before, &:after {
            visibility: visible;
            opacity: 1;
        }
    }

    &.has_tooltip {

            &:before {
            bottom: 100%;
            left: 50%;
            margin-bottom: 5px;
            transform: translateX(-50%);
        }

        &:after {
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-top: 8px solid $corporate_2;
            border-bottom: none;
        }

        &:hover {
            &:before, &:after {
            transform: translateX(-50%) translateY(-5px);
            }
        }
    }
}

.bg_secondary {

    [data-tooltip] {

        &:before {
            background: $corporate_1;
            color: $corporate_2;
        }

        &.has_tooltip {

            &:after {
                border-top: 8px solid $corporate_1;
            }
        }
    }
}
/* End tooltips*/


/* End utils */
