@-webkit-keyframes opacity_in {
  0%, 5% {
    opacity: 0;
  }
  50%, 100% {
    opacity: 1;
  }
}
@keyframes opacity_in {
  0%, 5% {
    opacity: 0;
  }
  50%, 100% {
    opacity: 1;
  }
}
#full_wrapper_booking {
  position: absolute;
  padding: 0;
  width: 1140px;
  min-width: 1140px;
  background: transparent;
  margin: 0 auto 50px;
  z-index: 1000;
  text-align: center;
  left: 0;
  right: 0;
  bottom: 0;
  animation: opacity_in 1s linear both;
  animation-delay: 1s;

  #menu_controller {
    display: none;
  }

  .footer_widget {
    padding: 1px 0;
    background-color: $corporate_3;
    color: white;
    .container12 {
      text-align: left;
    }
    h3, .phone, .email, .my_bookings {
      display: inline-block;
      vertical-align: middle;
      padding: 0 20px;
      font-size: 12px;
      letter-spacing: 1px;
      i.fa {
        display: none;
      }
    }
    h3 {
      font-size: 14px;
      border-right: 1px solid white;
    }
    .phone {
      border-right: 1px solid white;
      font-size: 14px;
    }
    .email {
      a {
        color: white;
        @include transition(opacity, .6s);
        &:hover {
          opacity: .6;
        }
      }
    }
    .my_bookings {
      float: right;
      width: 240px;
      font-size: 14px;
      padding: 5px 0;
      text-align: center;
      a {
        color: white;
        text-decoration: underline;
        @include transition(opacity, .6s);
        &:hover {
          opacity: .6;
        }
      }
    }
  }
  /*======== Booking Widget =======*/

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    display: table;
    width: 100%;
    margin: auto !important;
    position: relative;

    .promocode_header {
      display: none;
    }

    .nights_number_wrapper_personalized {
      display: none;
      background: white;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    position: relative;
    width: 100%;

      &::before {
          position: absolute;
          content: '';
          background-color: white;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          opacity: .9;
      }
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: black;
  }
  .dates_selector_personalized {
    margin: 0;
    .dates_selector_label {
      text-align: left;
      //font-family: "Oswald", sans-serif;
      font-size: 12px;
      font-weight: normal;
      letter-spacing: 1px;
      color: #999;
      width: 100%;
      margin-top: 20px;
      left: 55px;
      span {
        display: inline-block;
        float: left;
        width: 50%;
      }
    }
  }
  .start_end_date_wrapper {
    height: auto;
    padding: 0 0 20px;
    margin-top: 20px;
    background: none;
    width: auto;
    color: $corporate_3;
    font-size:0;
    .start_date_personalized, .end_date_personalized {
      font-size: 14pt;
      font-weight: 300;
      position: relative;
      padding: 30px 50px 0 50px;
      border-right: 1px solid lightgrey;
      height: 80px;
      display:inline-block;
      color: $corporate_3;

      > div {
        display: inline-block;
        padding: 0 3px;

          &:last-child {
              font-weight: 500;
              text-transform: uppercase;

              div {
                  display: block;
                  width: 100%;
                  text-align: left;
              }
          }
      }

      .day {
        font-size: 50pt;
        line-height: 35pt;
        font-weight: 700;
      }



        &::after {
          content:'\f107';
          display: inline-block;
          font-family: "Font Awesome 5 Pro", sans-serif;
          color: $corporate_3;
          position: absolute;
          bottom: 10px;
          right: 25px;
      }
    }
    .start_date_personalized {
      border-left: 1px solid lightgrey;
      position: relative;
    }
  }

  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    display: none;
    text-align: center;
    background: none;
    opacity: 1;
    margin-top: 7px;
    font-size: 13px !important;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {}

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 180px;
    height: 60px;
    padding-bottom: 0;
    margin-top: 40px;
    margin-right: 0;
    border-left-width: 0;
    border-right-width: 0;
    background: transparent;
    position: relative;
    z-index:2;

    .rooms_number {
      height: 50px;
      padding: 0 0 10px 15px;
      box-sizing: border-box;
      background-position-y: 40%;
      .label {
        color: $corporate_3;
        font-family: "lato", sans-serif;
        font-size: 50pt;
        line-height: 40pt;
          font-weight: 700;
      }
      .button {
        display: none;
      }
      .selectricItems {
        overflow: inherit !important;
        box-shadow: 0 0 30px rgba(0,0,0,.3);
        margin-top: 23px;
        border-width: 0;
        border-top:2px solid $corporate_1;
        li {
          border-width: 0;
          background: white;
          &:hover {
            background-color: $corporate_3;
            color:white;
          }
          &.selected {
            background-color: $corporate_1;
            color:white;
          }
        }
      }
    }

    &:after {
      content:'\f107';
      display: inline-block;
      font-family: "Font Awesome 5 Pro", sans-serif;
      color: $corporate_3;
      z-index: -1;
      position: absolute;
      bottom: 10px;
      right: 25px;
    }
  }

  .room_list_wrapper {
    display: none;
    vertical-align: top;
    float: left;
    width: 300px;
    position: absolute;
    left: 660px;
    top: 120px;
    z-index: 10;
    padding: 10px;
    box-shadow: 0 0 30px rgba(0,0,0,0.3);
    background-color: white;

    .selectricItems {
      width: 105px !important;
    }

    .room {
      position: relative;
      background: white;
      margin-bottom: 5px;
      height: 45px;

      &.room1, &.room2, &.room3 {
         overflow:visible !important;
        .children_selector, .babies_selector, .adults_selector {
          width: 50%;
          position: relative;
          height: 45px;
          background-color: #FAFAFA;
          border: 1px solid white;
        }
        .adults_selector {
          border-right-width: 0;
        }
        .babies_selector {
          border-left-width: 0;
        }
        .remove_room_element {
          @include center_y;
          right: 5px;
          border-radius: 50%;
          width: 15px;
          height: 15px;
          &:before{
            @include center_xy;
            content: '\f00d';
            color:#999;font-size: 10px;
            font-family: "Font Awesome 5 Pro", sans-serif;
          }
        }

        .selectric {
          height: 20px;

          .label {
            line-height: 20px;
            text-align: center;
          }

          .button {
            margin-top: 0;
            display: none;
          }
        }
      }

      &.room3, &.room2 {
        border-bottom-width: 0;
        border-top-width: 0;
        height: 35px;

        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 35px;
        }
      }

      &.room3 {
        border-top: 0;
      }
    }

      .buttons_container_guests {
        margin-top: 10px;
        padding-top: 10px;
        border-top:1px solid lightgrey;
        .close_guesst_button, .save_guest_button {
          display: inline-block;
          position: relative;
          padding: 10px;
          cursor: pointer;
          color:white;
          font-size: 10px;
          text-transform: uppercase;
          text-align: center;
          width: 50%;
          background-color: $corporate_1;
          span {
            position: relative;
            z-index: 2;
          }
          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background: #2D9E48;
            width: 0;
            z-index: 1;
            @include transition(width, .4s);
          }

          &:hover:before {
            width: 100%;
          }
        }
        .close_guesst_button {
          background-color: $corporate_3;
          &:before {
            background: #E5392E;
          }
        }
      }
  }

  .wrapper_booking_button {
    position: relative;
    display: inline-block;
    width: auto;
    float: right;
    height: 120px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: flex;
      align-items: center;
      width: 290px;
      height: 60px;
      background: $lightgrey;
      border-top-width: 0;
      position: relative;
      text-transform: uppercase;
      padding: 12px 15px;

      input.promocode_input {
        position: relative;
        z-index: 5;
        margin-top: 0;
        color: black;
        text-transform: uppercase;
        font-size: 10pt;
        letter-spacing: 1px;
        padding: 10px 5px;
        text-align: center;
        background-color: $lightgrey;
      }
    }

    .submit_button {
      width: 290px;
      height: 60px;
      display: block;
      vertical-align: top;
      color: white;
      font-size: 18pt;
      background: $corporate_2;
      font-weight: 300;
      letter-spacing: 2px;
      padding: 12px 10px;
      margin-top: 0;
      @-moz-document url-prefix() {
        padding: 11px 10px;
      }

      overflow: hidden;
      @include transition(all, .6s);
      &:hover {
        background-color: darken($corporate_2, 10%);
      }
    }
  }
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 150px;
  height: 80px;
  box-sizing: border-box;
  cursor: pointer;
  background: transparent;
  border-left: 1px solid lightgrey;
  margin-top: 20px;
  padding-top: 80px;
  position: relative;
  label {
    top: -10px;
  }

  span.placeholder_text {
    @include center_y;
    color: $corporate_3;
    font-size: 14pt;
//    line-height: 16pt;
    left: 10px;
    font-weight: 300;
    display: block;
    padding-top: 40px;
    padding-left: 13px;
    box-sizing: border-box;
    background: none;
    background-position-y: 0;
    .guest_adults {
        font-size: 50pt;
        line-height: 18pt;
        font-weight: 700;
    }
    &.selected_value {
      color: #585d63;
      font-size: 21px;
      padding-top: 3px;
      background-position-y: 8px;
      font-weight: 600;
    }
  }

  & > label {
    text-transform: uppercase;
    font-size: 10px;
    cursor: pointer;
  }

  b.button {
    display: none;
  }
  &:after {
    content:'\f107';
    display: inline-block;
    font-family: "Font Awesome 5 Pro", sans-serif;
    color: $corporate_3;
    position: absolute;
    bottom: 10px;
    right: -15px;
  }
}

.selectricWrapper .selectric .button, .guest_selector .button {
  background: none;
}

.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

#booking .room_list label {
  display: block !important;
  font-weight: 700;
  color: $corporate_1;
  //font-family: "Poppins", sans-serif;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 185px !important;
      margin-left: -10px !important;
    }
  }
}
#booking label.dates_selector_label, #booking label.rooms_label, #booking .guest_selector label {
  display: block;
  position: absolute;
  top:-5px;
  left: 15px;
  font-size: 12px;
  font-weight: normal;
  //font-family: "Oswald", sans-serif;
  letter-spacing: 1px;
  color: #999;
}
#booking label.rooms_label {
  top: -25px;
}

.hotel_selector {
  display: none;
}


.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: 300;
    font-size: 13px;
    padding-left: 15px;
    cursor: pointer;
    color: black;
    width: 220px;
  }
  .destination_field {
    position: relative;
  }
  .destination_field:after {
    background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
    color: #585d63;
    font-size: 23px;
    margin-left: 0;
    text-indent: 999px;
    font-weight: 600;
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    content: '';
    display: block;
  }
}

div#full_wrapper_booking.inner_engine,
div#full_wrapper_booking.floating_booking.showed {
  margin-bottom: 0;
  bottom: auto;
  top: 100%;
  background-color: $corporate_3;
  width: 100%;

  #full-booking-engine-html-7 {
    box-shadow: 0 0 0 transparent !important;

    form.booking_form {
      background-color: transparent;

      &::before {
          opacity: 0;
      }

      label.dates_selector_label, label.rooms_label, .guest_selector label {
        color: $corporate_3;
      }

      .start_end_date_wrapper .start_date_personalized,
      .start_end_date_wrapper .end_date_personalized,
      .rooms_number_wrapper .rooms_number .label,
      .guest_selector span.placeholder_text {
        color: white;
      }

      .wrapper_booking_button {

        .promocode_wrapper {
            background-color: $lightgrey;
            height: 50px;
            display: flex;
            align-items: center;
        }

        .submit_button {
          &:hover {
            background-color: darken($corporate_2, 10%);
          }
        }
      }
    }
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  display: block !important;
  bottom: auto;
  width: 100%;

  #booking label.dates_selector_label, #booking label.rooms_label, #booking .guest_selector label {
    display: none;
  }
  #full-booking-engine-html-7 {
    box-shadow: 0 0 30px rgba(0,0,0,.3);
  }
  .start_end_date_wrapper {
    .start_date_personalized, .end_date_personalized {
      padding-top: 0;
      height: 60px;
      color: white;
      border-color: white;

      .day {
        font-size: 35pt;
      }

      &:after {
        bottom: 10px;
        color: white;
      }
    }
  }
  .rooms_number_wrapper {
    height: 63px;
    margin-top: 15px;
    padding-top: 0;
    .rooms_number {
      padding-top: 0;
      padding-bottom: 0;
      .label {
        font-size: 30pt;
      }
    }

    &:after {
      color: white;
    }
  }

  .guest_selector {
    margin-top: 15px;
    padding-top: 40px;
    height: 60px;
    border-color: white;

    .placeholder_text {
      margin-top: 0;
      padding-top: 0;
      .guest_adults {
        font-size: 30pt;
      }
    }
    &:after {
      bottom: 6px;
      color: white;
    }
  }
  .room_list_wrapper {
    top: 77px;
  }
  .wrapper_booking_button {
    height: 60px;
    .promocode_wrapper {
      padding: 0 20px 0;
      background-color: white;
    }
    .submit_button {
      height: 50px;
      font-size: 16pt;
      padding: 12px 10px 13px;
    }
  }
}
header #main_menu .menu_inner {
  #main-sections-inner {
    display: inline-block;
    width: calc(100% - 180px);
  }
}
.booking_button_inner {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  text-align: center;
  text-transform: uppercase;
  padding: 10px 30px;
  font-size: 14px;
  color: white;
  background-color: $corporate_2;
  z-index: 999;
  span {
    position: relative;
    z-index: 2;
  }
  &:hover:before {
    width: 100%;
  }

  &:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: darken($corporate_1, 10%);
    width: 0;
    z-index: 1;
    @include transition(width, .4s);
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  border-radius: 0;
  border-top:2px solid $corporate_1;
  margin-top: 0;
  box-shadow: 0 0 30px rgba(0,0,0,.3);
  &.datepicker_wrapper_up {
    margin-bottom: -35px;
    border-bottom:2px solid $corporate_1;
    border-top-width:0;
    &:after, &:before {
      position: absolute;
      left: 0;
      margin: 0 auto;
      right: 0;
      top:auto;
      bottom: -24px;
      content: "";
      z-index: 9;
      width: 0;
      height: 0;
      border: 12px solid transparent;
      border-top-color: white;
      @-moz-document url-prefix() {
            display: none;
        }
    }

    &:before {
      bottom: -30px;
      border: 14px solid transparent;
      border-top-color: $corporate_1;
    }
  }
  .header_datepicker {
    background-color: $corporate_1;
    color: white;
    .specific_date_selector {
      font-weight: 700;
      //font-family: "Poppins", sans-serif;
    }
    .close_button_datepicker {
        border-width:0;
          &:before{
            @include center_xy;
            content: '\f00d';
            color:lighten($corporate_3, 10%);font-size: 10px;
            font-family: "Font Awesome 5 Pro", sans-serif;
          }
    }
  }
  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    .ui-widget-header {
        background: $corporate_3 !important;
        .ui-datepicker-title {
            color: white !important;
        }
    }
    .ui-datepicker-header {
      .ui-corner-all {
        background-color: transparent !important;
        @extend .fa-angle-down;
        &:before {
          @extend .fa;
          @include center_xy;
          color: white;
        }
        span {
          background: transparent !important;
        }
      }
    }

    .ui-datepicker td {
      border-color: white;
      &.ui-state-disabled {
        opacity: .15;
      }
      & .ui-state-active {
        background-color: $corporate_1 !important;
      }
      &.ui-datepicker-start_date {
        span.ui-state-default {
          &:before {
            border-color: transparent transparent transparent $corporate_1;
          }
        }
      }
    }
  }
  .specific_month_selector, .go_back_button {
    background-color: transparent;
    color: $corporate_1;
    border-radius: 0;
    strong {
      color: $corporate_3;
    }
  }

}

body:not(.no_hover_datepicker) {
  .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-datepicker td.last-highlight-selection {
        background-color: $corporate_1 !important;
        a {
          background-color: transparent !important;
          color:white !important;
        }
        &:before {
          content: '';
          @include center_y;
          left: -10px;
          z-index: 50;
          border: 5px solid transparent;
          border-color: transparent $corporate_1 transparent transparent;
        }
      }
}