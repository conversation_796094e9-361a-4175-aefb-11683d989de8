<div class="banner_destacados_wrapper">
    <div class="container12">
        {% if banner_destacados_sec.subtitle %}
            <div class="content_title text_center">
                <h2 class="title">{{ banner_destacados_sec.subtitle|safe }}</h2>
            </div>
        {% endif %}
        <div class="services destacados {% if banner_destacados|length > 3 and not mobile_version %} owl-carousel {% endif %}">
            {% for service in banner_destacados %}
                <a class="service{% if service.linkUrl %} service_link" href="{{ service.linkUrl|safe }}{% endif %}">
                    {% if service.description %}<span>{{ service.description|safe }}</span>{% endif %}
                    {% if service.title %}
                        <i class="fa {{ service.title|safe }}"><span class="overlay"></span></i>
                    {% else %}
                        <img src="{{ service.servingUrl|safe }}" alt="">
                    {% endif %}
                </a>
            {% endfor %}
        </div>
    </div>
</div>
{% if not mobile_version %}
<script>
$(window).load(function () {
    function banner_destacados_fx() {
        $(".banner_destacados_wrapper .service").addnimation({parent:$(".banner_destacados_wrapper"),class:"bounceInLeft"});
        $(".banner_destacados_wrapper .service:nth-child(2n)").addnimation({parent:$(".banner_destacados_wrapper"),class:"bounceInUp"});
        $(".banner_destacados_wrapper .service:nth-child(3n)").addnimation({parent:$(".banner_destacados_wrapper"),class:"bounceInRight"});
    }
    banner_destacados_fx();
    $(window).scroll(banner_destacados_fx);
});
</script>
{% endif %}

{% if banner_destacados|length > 3 and not mobile_version %}
    <script>
    $(window).load(function () {
       $(".services.destacados").owlCarousel({
            loop: true,
            nav: true,
            dots: true,
            items: 3,
            margin: 0,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            smartSpeed: 600,
            fluidSpeed: 600,
            navSpeed: 600,
            dotsSpeed: 600,
            dragEndSpeed: 600,
            autoplay: true
        });
    });
    </script>
{% endif %}