
<div class="wrapper_content banner_carousel_wrapper">
    <div class="carousel_wrapper">
        <div class="owl-carousel">
            {% for x in banner_carousel %}
            <div class="picture_wrapper">
                <img src="{{ x.servingUrl|safe }}" alt="">
                <a href="{{ x.servingUrl|safe }}" class="picture_link"  rel="lightbox[rooms_gallery]">{% if x.title %}{{ x.title|safe }}{% else %}{{ T_imagen }}{% endif %}</a>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script type="text/javascript">

    $(window).load(function () {

        {% if is_mobile %}
            $(".banner_carousel_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: false,
                navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                dots: true,
                items: 1,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                dotsSpeed: 600,
                dragEndSpeed: 600,
                autoplay: true
            });
        {% else %}
            $(".banner_carousel_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                dots: true,
                items: 5,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                dotsSpeed: 600,
                dragEndSpeed: 600,
                autoplay: true
            });
        {% endif %}
    });

</script>