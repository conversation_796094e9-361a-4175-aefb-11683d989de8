<div class="gallery_filters">
    <a href="#" data-filter="all" class="apply_filter active">{{ T_ver_todas }}</a>{% for class,filter in filters_gallery.items() %}<a href="#" data-filter="{{ class|safe }}" class="apply_filter">{{ filter|safe }}</a>{% endfor %}
</div>
<div class="gallery_filter_wrapper">
    {% for filter,gallery in pics_gallery.items() %}
        <div class="gallery_filter">
            <div class="gallery_photos">
                {% for image in gallery %}
                    {% if "youtube" in image.servingUrl %}<a class="iframe_video">
                            <iframe src="{{ image.servingUrl|safe }}" frameborder="0"></iframe>
                        </a>{% else %}<a href="{{ image.servingUrl|safe }}=s1024" rel="lightbox[gallery_carousel_{{ image.class_filter }}]" class="{{ image.class_filter }} all">
                        <img src="{{ image.servingUrl|safe }}=s350-c">
                    </a>{% endif %}
                {% endfor %}
            </div>
        </div>
    {% endfor %}
</div>
<script>

    $(window).load(function () {
        resize_video();
        $(".apply_filter").click(function (e) {
            e.preventDefault();
            var data_filter = $(this).attr("data-filter");
            $(".apply_filter").removeClass("active");
            $(this).addClass("active");
            $(".gallery_filter .gallery_photos a").slideUp().promise().done(function () {
                $(".gallery_filter .gallery_photos a."+data_filter).slideDown();
            });
        });
    });

    $(window).resize(resize_video);
    function resize_video() {
        $("a.iframe_video").height($("a.iframe_video").width() - 1);
    }

</script>