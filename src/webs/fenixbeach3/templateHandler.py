# -*- coding: utf-8 -*-
from collections import OrderedDict
from copy import deepcopy

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_language_title, get_language_code, get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

TEMPLATE_NAME = "fenixbeach3"
#Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4]+TEMPLATE_NAME[-1:]

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True
			}

			mobile_functions = "<script src='js/%s/mobile_functions.js'></script>" % base_web
			params_mobile['custom_element_home'] += mobile_functions
			params_mobile['custom_element_inner'] = mobile_functions

			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True),
			'language_selected': get_language_title(language)
		}

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		if section_type == "Inicio":
			result['home'] = True
			slider_container = get_section_from_section_spanish_name("_slider_content", language)
			if slider_container:
				result['slider_content'] = slider_container
				result['slider_content_properties'] = self.getSectionAdvanceProperties(slider_container, language)

		elif section_type == "Habitaciones":
			result['rooms'] = self.getRooms(language)


		elif section_type == "Ofertas":
			result['offers'] = self.getOffers(language)

		elif section_type == 'Galeria de Imagenes':
			result['gallery_section'] = True
			pics = OrderedDict()
			filters = OrderedDict()
			pics['random'] = map(lambda l: l, get_pictures_from_section_name(section.get('sectionName'), language))
			for pic in pics['random']:
				filters.setdefault(normalizeForClassName(pic.get('title')), pic.get('title'))
				pic['class_filter'] = normalizeForClassName(pic.get('title'))

			result['pics_gallery'] = pics
			result['filters_gallery'] = filters


		elif section_type == u"Localización":
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if section.get('sectionType') == 'Inicio':
			result['home'] = True

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result['minigallery_for_mobile'] = minigallery_images
			mini_dict = {'minigallery': minigallery_images,'num_items': 5,'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		if advance_properties.get("ventajas_banner"):
			result['ventajas_banner'] = self.getPicturesProperties(language, advance_properties['ventajas_banner'], ['icon'])
			result['ventajas_section'] = get_section_from_section_spanish_name(advance_properties['ventajas_banner'], language)

		if advance_properties.get("banner_carousel"):
			result['banner_carousel_section'] = get_section_from_section_spanish_name(advance_properties['banner_carousel'], language)
			result['banner_carousel'] = get_pictures_from_section_name(advance_properties['banner_carousel'], language)

			for x in result['banner_carousel']:

				if x.get('linkUrl'):
					result['banner_carousel_url'] = x['linkUrl']


		if advance_properties.get("bannerx4"):
			args = ['link_text']
			result["bannerx4"] = self.getPicturesProperties(language, advance_properties.get("bannerx4"), args)

		if advance_properties.get("banner_destacados"):
			result["banner_destacados"] = get_pictures_from_section_name(advance_properties.get("banner_destacados"), language)
			result["banner_destacados_sec"] = get_section_from_section_spanish_name(advance_properties.get("banner_destacados"), language)

		if advance_properties.get("cycle_banner"):
			result["cycle_banner"] = self.getPicturesProperties(language, advance_properties.get("cycle_banner"), ['link_text', 'external_link'])

		return result

	def getRooms(self, language):
		extra_properties = ['icons', 'gallery']
		rooms = deepcopy(self.getPicturesProperties(language, "_habitaciones_blocks", extra_properties))
		romm_icons = self.getPicturesProperties(language, "_rooms_icons", ['icon'])
		for room in rooms:
			room['images'] = get_pictures_from_section_name(room.get('gallery'), language)

			room['room_icons'] = []
			room_icons = room.get('icons', '').split(';')
			for x in room_icons:
				for y in romm_icons:
					if x == y.get('title'):
						service_config = y.get('icon').split(";") if y.get('icon') else ''
						if service_config:
							serviceToAppend = {'ico': service_config[0], 'description': y.get('description')}
							if len(service_config) > 1:
								serviceToAppend = {'ico': service_config[0], 'color': service_config[1],
								                   'description': y.get('description')}
							room['room_icons'].append(serviceToAppend)
						break


		return rooms

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)

		return offers

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result = self.getPicturesForGallerySection(language)

		elif section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			result['disable_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		section_name = section['sectionName']
		language_dict = get_web_dictionary(language)

		if section_type == "Habitaciones":
			return False

		elif section_type == "Ofertas":
			return False

		elif section_type == u"Localización":
			mini_dict = {
				"iframe_google_maps": get_section_from_section_spanish_name("Iframe google maps", language).get('content', ''),
				"captcha_box": get_config_property_value(PUBLIC_CAPTCHA_KEY),
				"section_content": get_section_from_section_spanish_name(section_name, language),
				"language_code": get_language_code(language)
			}
			mini_dict.update(language_dict)
			return self.buildTemplate_2("mobile/_location.html", mini_dict, False, 'fenixbeach3')


	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		if extra_banners.get("minigallery_for_mobile"):
			args = {
				'minigallery_mobile': extra_banners["minigallery_for_mobile"]
			}
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

		result += "</div>"

		return result