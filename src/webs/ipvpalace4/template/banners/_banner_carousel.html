<div class="banner_carousel_full_wrapper animate_reveal_on_scroll">
    {% if banner_carousel_content and banner_carousel_content.subtitle %}
        <div class="banner_carousel_content">
            <h2 class="content_title">{{ banner_carousel_content.subtitle|safe }}</h2>
            {% if banner_carousel_content.content %}
                <div class="content_text">{{ banner_carousel_content.content|safe }}</div>
            {% endif %}
        </div>
    {% endif %}
    <div class="banner_carousel_wrapper owl-carousel">
    {% for banner in banner_carousel_pics %}
        <div class="banner animate_reveal_on_scroll">
            {% if banner.servingUrl %}
                <div class="image_wrapper">
                    <img src="{{ banner.servingUrl|safe }}" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                </div>
            {% endif %}
            <div class="banner_content">
                {% if banner.title %}
                    <h4 class="title">{{ banner.title|safe }}</h4>
                {% endif %}
                {% if banner.description %}
                    <div class="text">{{ banner.description|safe }}</div>
                {% endif %}
                {% if banner.linkUrl %}
                    <a class="btn_personalized_1 bordered" href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %} target="_blank"{% endif %}>
                        {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_saber_mas }}{% endif %}
                    </a>
                {% endif %}
            </div>
        </div>
    {% endfor %}
    </div>
</div>

<script>
$(window).load(function () {
    $(".banner_carousel_wrapper.owl-carousel").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        {% if is_mobile %}
        items: 1,
        margin: 0,
        {% else %}
        items: 3,
        margin: 30,
        {% endif %}
        navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
        autoplay: false
    });
});
</script>