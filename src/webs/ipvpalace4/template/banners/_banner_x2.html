<div class="banner_x2_full_wrapper">
    {% if banner_x2_content and banner_x2_content.subtitle %}
        <div class="banner_x2_content">
            <h2 class="content_title">{{ banner_x2_content.subtitle|safe }}</h2>
            {% if banner_x2_content.content %}
                <div class="content_text">{{ banner_x2_content.content|safe }}</div>
            {% endif %}
        </div>
    {% endif %}
    <div class="banner_x2_wrapper">
    {% for banner in banner_x2_pics %}
        <div class="banner animate_reveal_on_scroll">
            {% if banner.servingUrl %}
                <div class="image_wrapper {% if banner.gallery %}owl-carousel{% endif %}">
                    <img src="{{ banner.servingUrl|safe }}" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                    {% if banner.gallery %}
                        {% for pic in banner.gallery %}
                            <img src="{{ pic.servingUrl|safe }}" {% if pic.altText %}alt="{{ pic.altText|safe }}" {% endif %}>
                        {% endfor %}
                    {% endif %}
                </div>
            {% endif %}
            <div class="banner_content">
                {% if banner.title %}
                    <h4 class="title">{{ banner.title|safe }}</h4>
                {% endif %}
                {% if banner.description %}
                    <div class="text">{{ banner.description|safe }}</div>
                {% endif %}
                {% if banner.linkUrl %}
                    <a class="btn_personalized_1 bordered" href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %} target="_blank"{% endif %}>
                        {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_saber_mas }}{% endif %}
                    </a>
                {% endif %}
                {% if banner.icons %}
                    <div class="icons_wrapper">
                        {% for icon in banner.icons %}
                            <span class="tooltip">
                                <img src="{{ icon.servingUrl|safe }}" class="icon_img">
                                {% if icon.description %}
                                    <span class="tooltiptext">{{ icon.description|safe }}</span>
                                {% endif %}
                            </span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}
    </div>
</div>
<script>
    $(window).load(function () {
        const x2_carousel = $(".banner_x2_wrapper .owl-carousel");
        if (x2_carousel.length) {
            x2_carousel.owlCarousel({
                loop: true,
                nav: false,
                dots: true,
                items: 1,
                navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
                margin: 0,
                autoHeight: false,
                autoplay: false
            });
        }
    })
</script>