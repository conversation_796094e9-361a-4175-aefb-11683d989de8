.modal_wrapper {
  display: none;
  opacity: 0;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1005;
  width: 100vw;
  height: 100vh;
  background: rgba($black, .8);
  @include transition(opacity, 1s);

  &.active {
    display: block;
    opacity: 1;
  }

  .modal {
    @include center_xy;
    background: white;
    border-radius: 10px;
    padding: 20px;
    //max-height: calc(100vh - 220px);
    max-width: calc(100vw - 60px);
    box-sizing: border-box;

    .close_modal {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 20px;
      padding: 10px 10px 10px 15px;
      color: white;
      background: $corporate_2;
      border-radius: 0 10px 0 50%;
      @include transition(all, .6s);

      &:hover {
        background: $corporate_1;
      }
    }

    .content {
      //max-height: calc(100vh - 330px);
      overflow: auto;
      padding: 5px;
      letter-spacing: 1px!important;
    }
  }
}

.open_modal, .close_modal {
  cursor: pointer;
}