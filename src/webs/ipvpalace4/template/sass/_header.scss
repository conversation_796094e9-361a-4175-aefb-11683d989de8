header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1002;
  min-width: 1140px;
  padding: 20px 60px !important;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  @include transition(all, .6s);
  
  &.fixed {
    background: $black;
    padding: 10px 20px !important;

    .menu_toggle {
      margin-top: 10px;
    }
    
    #logoDiv {
      height: 56px;
    }
    
    .hotel_info_header {
      max-width: 400px;
    }
    
    #lang {
      opacity: 0;
      padding-bottom: 0;
      margin-top: -20px;
    }
    
    #top-sections {
      .booking_button {
        opacity: 1;
        visibility: visible;
        width: auto;
        margin-left: 20px;
        padding: 15px 30px;
      }
    }
  }
  
  .popup_phone {
    @include full_size;
    position: fixed;
    z-index: 100;
    background: rgba($black, .8);
    
    .close_popup_phone {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      left: 20px;
      cursor: pointer;
      
      &:before, &:after {
        background: white;
      }
    }
    
    .content {
      background: white;
      border-radius: 10px;
      color: $corporate_1;
      padding: 20px;
      
      .subtitle {
        @include banner_title_styles_2;
      }
      
      .icon_list {
        text-align: left;
        
        li {
          display: block;
          padding: 10px 0;
          
          i {
            font-size: 22px !important;
            display: inline-block;
            vertical-align: middle;
          }
          
          a {
            color: $corporate_1 !important;
            display: inline-block;
            vertical-align: middle;
            padding-left: 10px;
          }
        }
      }
    }
  }
  
  .menu_toggle {
    display: inline-block;
    vertical-align: middle;
    width: 46px;
    height: 54px;
    margin: 0 30px 0 0;
    cursor: pointer;
    position: relative;
    @include transition(all, .6s);
    
    span {
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 2px;
      background: white;
      @include transition(all, .6s);
      
      &:nth-child(2) {
        top: 13px;
        left: auto;
        right: 0;
        width: 70%;
      }
      
      &:nth-child(3) {
        top: 26px;
      }
    }
    
    .menu_label {
      text-transform: uppercase;
      color: white;
      font-size: 13px;
      font-family: $title_family;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 1.6px;
      position: absolute;
      bottom: 0;
      @include transition(all, .6s);
    }
  }
  
  #logoDiv {
    display: inline-block;
    vertical-align: middle;
    max-width: 300px;
    white-space: nowrap;
    padding: 0;
    
    img {
      vertical-align: middle;
      opacity: 1;
      width: 100%;
      height: 100%;
      object-fit: contain;
      @include transition(all, .6s);
    }
  }
  
  .hotel_info_header {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    max-width: 0;
    overflow: hidden;
    @include transition(all, .6s);
    
    .stars i {
      display: inline-block;
      vertical-align: top;
      font-size: 10px;
      padding-top: 3px;
    }
  }
  
  #lang {
    text-align: right;
    @include text_styles(white);
    padding-bottom: 7px;
    @include transition(all, .6s);
    
    .currency, .lang_selector {
      display: inline-block;
      vertical-align: middle;
      
      a {
        color: white;
      }
    }
  }
  
  #top-sections {
    text-align: right;
    
    i.fa-info-circle {
      color: white;
    }
    
    .separator {
      display: inline-block;
      vertical-align: middle;
      height: 20px;
      width: 1px;
      background: white;
      margin: 0 15px;
    }
    
    .phone, .search, .user, .language_icon, .inline_phone {
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      color: white;
      cursor: pointer;
      padding-left: 7px;
      
      i {
        font-size: 20px;
      }
    }
    .inline_phone {
      font-weight: 500;
      line-height: 1;
      margin-right: 10px;
      text-align: right;
    }
    .phone {
      padding-right: 10px;
      
      i {
        font-size: 30px;
      }
    }
    
    .language_icon {
      padding-right: 10px;
      
      i {
        font-size: 30px;
      }
      
      span {
        padding: 3px;
        
        a {
          display: block;
          padding: 3px 7px;
          font-weight: bold;
          font-size: 14px;
          color: $corporate_1;
          
          &:hover {
            background: $corporate_1;
            color: white;
          }
        }
      }
    }
    
    .user {
      position: relative;
      
      i {
        cursor: pointer;
        font-size: 30px;
      }
      
      &.active {
        label {
          display: block;
        }
      }
      
      label {
        display: none;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        position: absolute;
        top: -7px;
        right: -7px;
        background: #E75354;
        color: white;
        border: 1px solid white;
        font-weight: 700;
        
        span {
          @include center_xy;
          font-size: 12px;
        }
      }
      
      .popup {
        width: 254px;
        border-radius: 10px;
        position: absolute;
        overflow: hidden;
        cursor: default;
        right: 0;
        top: calc(100% + 20px);
        
        .close {
          display: block;
          position: absolute;
          top: 10px;
          right: 10px;
          width: 15px;
          height: 15px;
          cursor: pointer;
          
          &:before, &:after {
            background: white;
          }
        }
        
        .content {
          @include text_styles(white);
          background-color: $black;
          padding: 20px 40px;
          text-align: center;
          
          .img_title {
            display: inline-block;
            max-width: 120px;
            max-height: 80px;
            overflow: hidden;
            
            img {
              max-height: 100%;
              max-width: 100%;
            }
          }
          
          .text {
            display: block;
            text-align: center;
            color: white;
            font-size: 15px;
            line-height: 22px;
            letter-spacing: .8px;
            font-weight: 300;
          }
          
          .btn_personalized_1 {
            font-size: 14px;
            letter-spacing: .8px;
            line-height: 20px;
            padding: 10px 20px 10px 42px;
            width: 100%;
            
            &:before {
              left: 30px;
              right: auto;
              top: 22px;
            }
            
            &:after {
              max-width: 49px;
              height: 49px;
              left: 10px;
            }
            
            &:hover {
              padding: 10px 42px 10px 20px;
              
              &:before {
                right: 30px;
                left: auto;
              }
              
              &:after {
                max-width: 100% !important;
              }
            }
          }
        }
      }
    }
    
    a {
      display: inline-block;
      vertical-align: middle;
      color: white;
      font-size: 18px;
      
      &.header_info_link {
        padding-right: 20px;
        @include transition(all, .6s);
        
        &:hover {
          color: $corporate_1;
        }
        
        i {
          font-size: 30px;
          display: inline-block;
          vertical-align: middle;
        }
        
        span {
          display: inline-block;
          vertical-align: middle;
          text-align: left;
          padding-left: 7px;
          font-size: 15px;
          letter-spacing: .8px;
          
          b {
            font-weight: 500;
            font-size: 18px;
            letter-spacing: 1px;
          }
        }
      }
    }
    
    .booking_button {
      @include btn_styles_2;
      opacity: 0;
      visibility: hidden;
      padding: 0;
      width: 0;
      overflow: hidden;
      margin: 0;
    }
  }
  
  .float_left {
    width: 45%;
    padding-top: 5px;
  }
  
  .float_right {
    width: 52%;
  }
}