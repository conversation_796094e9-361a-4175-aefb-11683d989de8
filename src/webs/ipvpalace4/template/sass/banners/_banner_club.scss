.banner_club_wrapper {
  overflow: visible;
  width: 100%;
  margin-top: 50px;

  .banner_club_bg {
    height: 524px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
  }
  
  .banner_club_text {
    position: absolute;
    top: -30px;
    left: calc((100% - 1140px) / 2);
    z-index: 1;
    overflow: hidden;
    display: inline-block;
    text-align: center;
    width: 503px;
    padding: 40px;
    background-color: $black;

    .text_bg_pic {
      position: absolute;
      top: -100px;
      right: -100px;
      width: 70%;
      z-index: -1;
      -webkit-filter: grayscale(100%);
      filter: grayscale(100%);
      @include transform(rotate(-45deg));
      opacity: .6;
    }

    .banner_club_title_pic {
      max-height: 120px;
      overflow: hidden;
      position: relative;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .text {
      @include text_styles(white);
      display: block;
      padding-top: 20px;
    }
  }
}