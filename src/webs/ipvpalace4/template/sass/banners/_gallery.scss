.gallery_filter_wrapper {
  @include base_banner_styles;
  
  .gallery_block {
    @include display_flex;
    width: 100%;
    margin-bottom: 50px;
    
    .gallery_title {
      padding: 15px 0;
      text-transform: uppercase;
      font-family: $title_family;
      font-size: 34px;
      font-weight: 400;
      color: $black;
      display: block;
      width: 100%;
    }
    
    .image_full_wrapper {
      column-count: 3;
      width: 100%;
      
      .image_wrapper {
        width: 100%;
        height: 350px;
        display: block;
        vertical-align: top;
        transform: none;
        position: relative;
        margin-bottom: 20px;
        overflow: hidden;
        
        img {
          @include cover_image;
        }
        
        &:nth-of-type(1), &:nth-of-type(3n + 2) {
          height: 550px;
        }
      }
    }
    
    /*.gallery_group_wrapper {
      display: inline-block;
      width: calc((100% / 3) - 20px);
      margin-bottom: 30px;

      &:not(:nth-of-type(3n)) {
        margin-right: 30px;
      }

      .image_wrapper {
        position: relative;
        display: inline-block;
        width: 100%;
        height: 260px;
        overflow: hidden;
        cursor: pointer;

        &:after {
          content: '';
          @include full_size;
          background-color: rgba($corporate_1, .8);
          z-index: 5;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
        }

        &:not(:last-of-type) {
          margin-bottom: 30px;
        }

        img {
          @include cover_image;
        }

        &:hover {
          &:after, .banner_info {
            opacity: 1;
            visibility: visible;
          }
        }
      }

      &:nth-of-type(1), &:nth-of-type(3n + 1) {
        .image_wrapper:nth-of-type(2) {
          height: 550px;
        }
      }

      &:nth-of-type(2), &:nth-of-type(3n + 2) {
        .image_wrapper:nth-of-type(1) {
          height: 550px;
        }
      }

      &:nth-of-type(3n) {
        .image_wrapper:nth-of-type(3) {
          height: 550px;
        }
      }
    }*/
  }
}