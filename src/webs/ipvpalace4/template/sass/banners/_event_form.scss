.events_form_wrapper {
  @include base_banner_styles;

  .contact_subtitle {
    @include title_styles();
    margin-bottom: 30px;
    text-align: center;
    text-transform: uppercase;
    font-family: $corporate_2 !important;
  }

  .title_info {
    @include title_styles();
    font-size: 20px;
    line-height: 20px;
    padding-left: 20px;
    padding-bottom: 10px;
  }

  .content_title {
    @include title_styles;
    text-align: center;
    padding-bottom: 30px;
  }

  #contact {
    display: flex;
    flex-direction: column;
    margin: auto;
    width: 100%;
    position: relative;

    .info {
      display: table;
      position: relative;
    }

    .input_block {
      display: flex;
    }

    
    .contInput {
      display: inline-block;
      float: none;
      padding: 10px 0 10px 20px;
      position: relative;
      width: calc((100% - 6px) / 2);

      &.area, &.select {
        width: 100%;
        margin-right: 0;
      }

      &:nth-of-type(3n) {
        margin-right: 0;
      }

      input:not([type="checkbox"]), textarea, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 14px;
        border-width: 0;
        width: 100%;
        margin-bottom: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
      }

      textarea {
        height: 150px;
      }

      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity {
        @include checkbox_styles;
      }

      input#uploadFile {
        box-shadow: none;
        padding-bottom: 0;
        padding-top: 0;
        margin-bottom: 10px;
      }

      label.error {
        color: red;
      }
    }

    .contInput_radio {
      display: inline-block;
      float: left;
      padding: 10px 0 10px 20px;
      position: relative;
      width: calc((100% - 5px) / 2);
    }

    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 10px 0 40px;
    }

    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $black;
    }

    #contact-button {
      @include btn_styles;
      position: absolute;
      right: 0;
      bottom: 20px;
    }
  }

  .form_bottom {
    margin-top: 20px;
  }

  .info_event {
    margin-top: 30px;
  }
  &.newsletter {
    .title_info {
      display: none;
    }
  }
}