.newsletter_wrapper {
  @include base_banner_styles;
  padding-top: 90px;
  background-position: center;
  background-repeat: repeat;
  z-index: 1;
  text-align: left;
  
  .newsletter_container {
    position: relative;
    @include display_flex;
    width: 100%;
  }
  
  .newsletter_title {
    @include title_styles;
    text-transform: none;
    text-align: left;
    width: 100%;
    padding: 0;
    font-size: 54px;
    
    small {
      font-size: 200px;
      width: 60px;
      margin-left: -30px;
      margin-right: 30px;
      position: relative;
      z-index: -1;
      color: rgba(166, 136, 45, 0.7);
    }
  }
  
  .newsletter_description {
    @include text_styles;
    padding: 10px 80px 60px 0;
    display: block;
    width: 40%;
  }
  
  .newsletter_form {
    text-align: left;
    position: relative;
    display: inline-block;
    width: 60%;
    
    &:before {
      content: "\f0e0";
      font-family: "Font Awesome 5 Pro";
      font-weight: 300;
      color: $corporate_1;
      position: absolute;
      top: 71px;
      z-index: 5;
      left: 20px;
      font-size: 24px;
    }
    
    input#suscEmail,
    input#suscSurname,
    input#suscName {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      background-color: white;
      border: none;
      border-bottom: 1px solid $corporate_1;
      font-size: 17px;
      font-family: $text_family;
      font-weight: 400;
      padding: 25px 25px 25px 80px;
      width: 424px;
      height: 56px;
      margin-right: 30px;
      margin-bottom: 40px;
      color: $black;
      
      &::placeholder {
        color: rgba($black, .8);
      }
      
      &:focus {
        outline-color: $corporate_2;
      }
    }

    input#suscSurname,
    input#suscName {
      width: 200px;
      margin-right: 20px;
      margin-bottom: 2px;
    }
    
    .button_newsletter {
      @include btn_styles;
      font-size: 17px;
      line-height: 24px;
      margin: 0 0 40px;
      
      &:after {
        max-width: 57px;
        height: 57px;
      }
    }
    
    .check_newsletter {
      display: block;
      width: 100%;
      
      a, label {
        font-size: 14px;
        font-weight: 300;
        display: inline-block;
        vertical-align: top;
        max-width: 400px;
        color: $color_text;
      }
      
      .newsletter_checkbox {
        input.check_privacy {
          -webkit-appearance: none;
          border: solid 1px $black;
          border-radius: 0;
          height: 9px;
          width: 9px;
          display: inline-block;
          vertical-align: top;
          margin: 6px 5px 0 0;
          cursor: pointer;
          
          &:checked {
            background-color: $corporate_1;
          }
          
          &:focus {
            outline: 0;
          }
        }
      }
    }
  }
  
  .social_newsletter {
    position: absolute;
    left: 0;
    bottom: 0;
    
    a {
      margin: 0 10px;
      color: $corporate_1;
      font-size: 34px;
      @include transition(all, .6s);
      
      &:hover {
        color: $corporate_2;
      }
    }
  }
}