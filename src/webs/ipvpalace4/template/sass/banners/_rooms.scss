.rooms_wrapper {
  background-color: white;

  .room {
    @include display_flex;
    width: 100%;
    align-items: center;

    &:nth-of-type(odd) {
      flex-direction: row-reverse;

      .room_gallery {
        .owl-nav {
          .owl-prev {
            color: $corporate_1;
            left: 0;
          }

          .owl-next {
            right: 50px;
            color: white;
          }
        }
      }
    }

    &:not(:last-of-type) {
      margin-bottom: 60px;
    }

    .room_gallery {
      display: inline-block;
      width: 50%;
      height: 634px;
      overflow: visible;
      position: relative;

      .owl-stage-outer, .owl-stage, .owl-item {
        height: 100%;
      }

      .room_img {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        img {
          @include cover_image;
        }
      }

      .owl-nav {
        display: block;
        position: absolute;
        bottom: 50px;
        left: 0;
        right: 0;
        height: 0;

        .owl-prev, .owl-next {
          @include owl_nav_styles;
          @include center_y;
          left: 60px;
          @include transform(translateX(-100%));
          margin: 0;
        }

        .owl-prev {
          color: white;
        }

        .owl-next {
          left: auto;
          right: 0;
          @include transform(translateX(100%));

          &:after {
            left: auto;
            right: 98%;
          }
        }
      }
    }

    .room_content {
      display: inline-block;
      width: 50%;
      text-align: center;
      padding: 30px 80px;

      .title {
        @include banner_title_styles_2;
        text-transform: none;
      }

      .text {
        @include text_styles;
        padding: 20px 0 30px;
      }

      .room_icons {
        @include display_flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding-bottom: 30px;

        .tooltip {
          display: inline-flex;
          align-items: center;
          padding-right: 15px;
          position: relative;

          .icon_img {
            display: inline-block;
            max-width: 60px;
            max-height: 60px;
            margin-right: 5px;
            -webkit-filter: grayscale(100%);
            filter: grayscale(100%);
            @include transition(all, .6s);

            img {
              max-width: 100%;
              max-height: 100%;
              width: auto;
            }
          }

          .tooltiptext {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -100%);
            font-size: 15px;
            line-height: 14px;
            letter-spacing: .8px;
            background: $black;
            max-width: 160px;
            padding: 10px;
            border-radius: 5px;
            color: white;
            opacity: 0;
            z-index: 1;
            @include transition(all, .6s);
            
            &::before {
              position: absolute;
              content: '';
              width: 20px;
              height: 20px;
              border-radius: 2px;
              bottom: 0;
              left: 50%;
              transform: rotate(45deg) translate(-25%, 50%);
              background: $black;
              z-index: -1;
            }
          }

          &:hover {
            .icon_img {
              -webkit-filter: grayscale(0);
              filter: grayscale(0);
            }
            
            .tooltiptext {
              opacity: 1;
              top: -20px;
            }
          }
        }
      }
    }
  }
}