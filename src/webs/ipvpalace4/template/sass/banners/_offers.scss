.offers_full_wrapper {
  
  .buttons_filter_wrapper {
    @include display_flex;
    justify-content: center;
    width: 1140px;
    margin: 0 auto;

    .offers_filter {
      display: inline-block;
      font-size: 22px;
      line-height: 31px;
      letter-spacing: 1.2px;
      color: $color_text;
      border-top: 1px solid $lightgrey;
      border-bottom: 1px solid $lightgrey;
      padding: 15px 30px;
      text-align: center;
      text-transform: capitalize;
      cursor: pointer;

      i {
        font-size: 24px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 15px;
      }

      &.active {
        color: $corporate_1;
        border-top-color: $corporate_1;
        border-bottom-color: $corporate_1;
      }
    }
  }

  .offers_wrapper {
    @include base_banner_styles;
    @include display_flex;
    width: 100%;

    .offer {
      display: inline-flex;
      flex-wrap: wrap;
      width: calc((100% / 3) - 20px);
      margin: 0 10px 20px;
      background-color: $lightgrey;

      .offer_img {
        position: relative;
        display: block;
        width: 100%;
        height: 263px;
        overflow: visible;

        .offer_discount {
          @include display_flex;
          align-items: center;
          justify-content: center;
          width: 63px;
          height: 63px;
          background-image: linear-gradient(-170deg, $corporate_2 0,  $corporate_2 80%, rgba(0, 0, 0, 0) 80%, rgba(0, 0, 0, 0) 100%);
          position: absolute;
          z-index: 5;
          top: -5px;
          right: 10px;
          color: white;
          font-size: 20px;
          line-height: 29px;
          letter-spacing: 1.1px;
          font-weight: 700;
        }

        img {
          @include cover_image;
        }
      }


      .offer_content {
        text-align: left;
        padding: 30px;
        @include display_flex;
        width: 100%;

        .title {
          @include banner_title_styles_2;
          width: 100%;
        }

        .text_wrapper {
          display: block;
          width: 100%;
          padding-bottom: 30px;

          .text {
            @include text_styles;
            position: relative;
            max-height: 140px;
            overflow: hidden;

            &:before {
              content: "";
              position: absolute;
              z-index: 1;
              height: 40px;
              left: 0;
              right: 0;
              bottom: 0;
              background-image: linear-gradient(to top, rgba($lightgrey, .8), rgba($lightgrey, 0));
            }
          }

          .offer_link {
            display: inline-block;
            font-size: 17px;
            line-height: 24px;
            letter-spacing: .9px;
            font-weight: 700;
            color: $color_text;
            text-decoration: underline;
            margin-top: 30px;
            @include transition(all, .6s);

            &:hover {
              opacity: .8;
            }
          }
        }

        .offer_button {
          display: inline-flex;
          height: 68px;
          align-self: flex-end;
          align-items: center;
          justify-self: center;
        }
      }

      &.with_calendar {
        .offer_content {
          .offer_button:not(.active) {
            font-size: 18px;
            line-height: 21px;
            letter-spacing: 1px;

            i {
              margin-right: 10px;
              font-size: 34px;
              display: inline-block;
              vertical-align: middle;
              width: 20%;
            }

            span {
              display: inline-block;
              vertical-align: middle;
              width: 65%;
            }
          }
        }
      }

      &.entry {
        .offer_img .offer_discount {
          opacity: 0;
          visibility: hidden;
        }

        .calendar_offer.sd {
          opacity: 1;
          visibility: visible;
        }
      }

      &.departure {
        .offer_img .offer_discount {
          opacity: 0;
          visibility: hidden;
        }

        .calendar_offer.ed {
          opacity: 1;
          visibility: visible;
        }
      }

      .calendar_offer {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        display: block;
        z-index: 5;
        opacity: 0;
        visibility: hidden;
        @include transition(all, .6s);

        &.sd {
          z-index: 10;
        }

        .ui-datepicker {
          width: 100%;
          height: 100%;
          padding: 0;
          border: none;
          border-bottom: 1px solid $corporate_1;

          .ui-datepicker-header {
            background: transparent;
            border: 0;
            border-bottom: 1px solid $corporate_1;

            .ui-datepicker-prev, .ui-datepicker-next {
              cursor: pointer;

              .ui-icon {
                &:before {
                  font-family: "Font Awesome 5 Pro";
                }
              }

              &.ui-state-hover {
                border: 0;
              }
            }

            .ui-datepicker-title {
              color: $corporate_1;
            }
          }

          .ui-datepicker-calendar {
            tbody {
              tr {
                td {
                  .ui-state-hover {
                    background: #4b4b4b !important;
                    color: white !important;
                  }

                  .ui-state-default {
                    position: relative;
                    z-index: 1;
                    border: none;
                    color: $corporate_2;
                    background: transparent;
                    font-size: 20px;
                    font-weight: 400;
                    text-align: center;
                    cursor: pointer;
                  }

                  &.ui-datepicker-unselectable {
                    .ui-state-default {
                      color: $corporate_1;
                      font-weight: 300;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  html[lang=de] & {
    .offer_button {
      font-size: 14px!important;
    }
  }
}