.lateral_options_block {
  padding-bottom: 70px;
  
  .container12 {
    display: flex;
    flex-flow: row nowrap;
    
    .lateral_options_left {
      width: 30%;
      
      .option_element {
        border-bottom: solid 1px $grey;
        
        .title_wrapper {
          position: relative;
          font-size: $title_family;
          font-weight: 500;
          padding: 10px 10px 10px 45px;
          cursor: pointer;
          color: $black;
          
          i {
            position: absolute;
            top: 14px;
            left: 10px;
          }
        }
        
        &.active,
        &:hover {
          border-bottom: solid 1px $corporate_1;
          
          .title_wrapper {
            color: $corporate_1;
          }
        }
      }
    }
    
    .lateral_options_right {
      width: 70%;
      padding-left: 50px;
      
      .option_content {
        display: none;
        
        .title {
          @include title_styles;
          font-size: 30px;
          line-height: 35px;
          text-align: left;
          color: $corporate_1;
        }
        
        .desc {
          ul {
            list-style: none;
            padding: 0;
            margin: 0;
            
            li {
              position: relative;
              padding: 0 0 10px 30px;
              
              &::before {
                position: absolute;
                content: '\f00c';
                font-family: 'Font Awesome 5 Pro';
                top: 4px;
                left: 0;
                color: $corporate_2;
              }
            }
          }
        }
        
        &.showed {
          display: block;
        }
      }
    }
  }
}