.news_widget_wrapper {
  @include base_banner_styles;

  .entry_widget {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: calc((100% / 3) - 20px);
    margin: 15px 5px;
    border: 1px solid #fafafa;

    &.big_entry {
      width: calc(100% - 10px);

      .image {
        display: inline-block;
        vertical-align: top;
        width: 50%;

        img {
          vertical-align: middle;
        }
      }

      .content {
        display: inline-block;
        vertical-align: top;
        width: 50%;

        .title {
          font-size: 30px;
        }

        .tags {
          left: 50%;
        }
      }
    }

    .content {
      padding: 20px 20px 50px;

      .date {
        @include text_styles;
        margin-bottom: 15px;
        color: $gray-1;
        text-transform: uppercase;
        font-size: 12px;
      }

      .title {
        @include banner_title_styles_2;
      }

      .entry_desc {
        @include text_styles;
        margin-top: 15px;
      }

      .read_more {
        @include btn_styles;
      }

      .tags {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 20px;
        color: $corporate_1;
        letter-spacing: 2px;
        margin-top: 15px;
        text-transform: uppercase;
        font-size: 12px;
      }
    }
  }
}

.entry_picture {
  position: relative;
  margin: 50px calc((100% - 1140px) / 2) 0;

  img {
    width: 100%;
    vertical-align: middle;
  }

  .entry_info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 50px 30px;
    background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));

    .entry_title {
      font-size: 30px;
      font-family: $title_family;
      font-weight: bold;
      color: white;
    }
  }
}

article.entry {
  display: inline-block;
  vertical-align: top;
  width: 70%;
  padding-left: calc((100% - 1140px) / 2);
  padding-top: 10px;

  .tags_wrapper {
    margin-bottom: 15px;

    .date, .tags, .author {
      display: inline-block;
      padding: 5px 7px;
      text-transform: uppercase;
      font-size: 12px;
      color: $gray-2;
    }

    .date, .tags {
      border-right: 1px solid #fafafa;
    }

    .tags {
      color: $corporate_1;
      letter-spacing: 2px;
      margin-top: 15px;
    }

    .author {
      color: $black;
    }
  }

  .entry_content {
    @include text_styles;
    padding: 0 7px 15px;
  }

  .entry_share {
    paddgin-bottom: 15px;
  }

  .entry_comments {
    h3 {
      display: inline-block;
      padding: 5px 7px;
      text-transform: uppercase;
      color: $gray-2;
    }

    .comment_form {
      margin: 0 10px;
      position: relative;
      padding-bottom: 30px;

      label {
        display: inline-block;
        padding: 5px 7px;
        text-transform: uppercase;
        color: $gray-2;
        font-size: 12px;
      }

      input:not([type="checkbox"]), textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 16px;
        border-width: 0;
        width: 100%;
        margin-bottom: 20px;
        border-bottom: 1px solid $corporate_1;
      }

      textarea {
        height: 150px;
      }

      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      input[type=checkbox] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: transparent;
        border: 1px solid $corporate_1;
        border-radius: 0;
        width: 14px;
        height: 14px;
        margin-right: 15px;
        display: inline-block;
        vertical-align: middle;
        position: relative;
        outline: none;

        &:checked {
          &:before {
            content: '';
            width: 8px;
            height: 8px;
            background: $black;
            @include center_xy;
          }
        }
      }

      a {
        color: $corporate_1;
        font-size: 12px;

        &:hover {
          text-decoration: underline;
        }
      }

      #popup_form_button {
        @include btn_styles;
      }
    }
  }
}

aside.right_column {
  display: inline-block;
  vertical-align: top;
  width: 30%;
  padding-top: 15px;
  padding-right: calc((100% - 1140px) / 2);
  padding-left: 15px;
  border-left: 1px solid #fafafa;

  .widget_title {
    display: inline-block;
    padding: 5px 7px;
    text-transform: uppercase;
    font-size: 12px;
    color: $gray-2;
  }

  .news_right_widget {
    .news_widget_entry {
      margin-bottom: 15px;

      &:hover {
        .content .title a {
          color: $corporate_1;
        }
      }

      .image {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;

        img {
          @include center_image;
        }
      }

      .content {
        padding-top: 10px;

        .title {
          font-size: 16px;
          font-family: $title_family;
          font-weight: bold;
          color: $black;

          a {
            color: $black;
          }
        }
      }
    }
  }

  .social_widget {
    padding: 10px 0;
    text-align: center;

    a {
      color: $corporate_1;
      display: inline-block;
      padding: 0 5px;

      &:hover {
        color: $black;
      }
    }
  }
}