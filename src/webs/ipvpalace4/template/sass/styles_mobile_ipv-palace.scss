$fontawesome5: true;
$is_mobile: true;
$mobile_padding: 20px;

@mixin base_mobile_styles() {
  position: relative;
  padding: $mobile_padding;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  * {
    box-sizing: border-box;
  }
}

@import "defaults";
@import "styles_mobile/2/2";


body {
  font-family: $text_family;
  
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  
  a {
    text-decoration: none;
    color: $corporate_2;
  }
  
  strong, b {
    font-weight: 700;
  }
  
  .main_menu {
    background-color: $black;
    
    .social_menu {
      a:not(.mailto) {
        i {
          background-color: transparent;
        }
      }
    }
  }
  
  nav a {
    color: $black;
  }
  
  #full_wrapper_booking {
    .entry_label_calendar, .departure_label_calendar {
      background-color: $corporate_1;
    }
    
    .wrapper_booking_button {
      .submit_button {
        background-color: $corporate_1;
        font-family: $title_family !important;
      }
    }
    
    .dates_selector_personalized .start_end_date_wrapper {
      .start_date_personalized, .end_date_personalized {
        padding: 15px 10px 15px;
        
        .day {
          font-size: 50px;
          line-height: 60px;
        }
        
        .month {
          font-size: 22px;
        }
        
        .year {
          color: #666;
          font-size: 19px;
        }
      }
    }
  }
  
  .main-owlslider {
    .owl-item {
      &:before {
        content: '';
        @include full_size;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
      }
    }
    
    .description_text {
      @include center_xy;
      top: 40%;
      width: 80%;
      z-index: 30;
      font-family: $title_family;
      font-size: 26px;
      line-height: 32px;
      font-weight: 900;
      letter-spacing: 1.5px;
      color: white;
      text-align: center;
      
      .subtitle {
        display: block;
        font-family: $subtitle_family;
        font-size: 32px;
        line-height: 38px;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
  }
  
  .section_content {
    .location_content .section-title {
      display: none;
    }
    
    > h1, h1.section-title, .content_subtitle_title, .section_title, h2.section_title, .location_content .section-subtitle {
      @include title_styles;
      text-align: center;
      padding: 20px 20px 0;
      
      span {
        color: inherit;
      }
    }
    
    div.content,
    div.content_subtitle_description,
    .section-content,
    .contact_content_element,
    .default_reservation_text,
    #contentInPage {
      @include text_styles;
      text-align: center;
      width: auto;
      padding: 20px 20px 0;
    }

    .contact_content_element {
      margin-right: auto;
      margin-left: auto;
    }
  }
  
  .btn_personalized_1 {
    @include btn_styles;
  }
  
  .btn_personalized_2 {
    @include btn_styles_2;
  }
  
  @import "modal";
  @import "mobile/banner_icons";
  @import "mobile/banner_club";
  @import "mobile/banner_carousel";
  @import "mobile/banner_image";
  @import "mobile/banner_logos";
  @import "mobile/banner_x3";
  @import "mobile/banner_icons_img";
  @import "mobile/banner_gallery";
  @import "mobile/banner_x2";
  @import "mobile/banner_salones_img";
  @import "mobile/cycle_banner";
  @import "mobile/news_mobile";
  @import "mobile/form_contact_cv";
  @import "mobile/events_form";
  
  .tour_virual_iframe {
    width: 100% !important;
  }
  
  .rooms_background {
    -webkit-filter: none;
    filter: none;
    background-color: transparent;
  }
  
  .lateral_options_block {
    padding-bottom: 70px;
    
    .container12 {
      
      .lateral_options_left {
        width: 100%;
        padding: 20px 20px 90px;
        
        .option_element {
          border-bottom: solid 1px $grey;
          text-align: left;
          
          .title_wrapper {
            position: relative;
            font-size: $title_family;
            font-weight: 500;
            padding: 10px 10px 10px 45px;
            cursor: pointer;
            color: $black;
            
            i {
              position: absolute;
              top: 14px;
              left: 10px;
            }
          }
          
          &.active,
          &.hover {
            border-bottom: solid 1px $corporate_1;
            
            .title_wrapper {
              color: $corporate_1;
            }
          }
        }
      }
      
        width: 100%;
      .lateral_options_right {
        background-color: #f9f9f9;
        padding: 140px 20px 20px;
        
        .option_content {
          display: none;
          
          .title {
            @include title_styles;
            font-size: 30px;
            line-height: 35px;
            text-align: left;
            color: $corporate_1;
          }
          
          .desc {
            ul {
              list-style: none;
              padding: 0;
              margin: 0;
              
              li {
                position: relative;
                padding: 0 0 10px 30px;
                text-align: left;
                
                &::before {
                  position: absolute;
                  content: '\f00c';
                  font-family: 'Font Awesome 5 Pro';
                  top: 4px;
                  left: 0;
                  color: $corporate_2;
                }
              }
            }
          }
          
          &.showed {
            display: block;
          }
        }
      }
    }
  }
  
  .offers_full_wrapper {
    
    .buttons_filter_wrapper {
      @include display_flex;
      justify-content: center;
      width: 100%;
      margin: 0 auto;
      
      .offers_filter {
        display: inline-block;
        font-size: 20px;
        line-height: 31px;
        letter-spacing: 1.2px;
        color: $color_text;
        border-top: 1px solid $lightgrey;
        border-bottom: 1px solid $lightgrey;
        padding: 35px 10px 15px;
        text-align: center;
        text-transform: capitalize;
        cursor: pointer;
        
        i {
          font-size: 24px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 15px;
        }
        
        &.active {
          color: $corporate_1;
          border-top-color: $corporate_1;
          border-bottom-color: $corporate_1;
        }
      }
    }
    
    .offers_wrapper {
      @include base_banner_styles;
      @include display_flex;
      width: 100%;
      
      .offer {
        display: inline-flex;
        flex-wrap: wrap;
        width: 100%;
        margin: 0 10px 20px;
        background-color: $lightgrey;
        
        .offer_img {
          position: relative;
          display: block;
          width: 100%;
          height: 263px;
          overflow: visible;
          
          .offer_discount {
            @include display_flex;
            align-items: center;
            justify-content: center;
            width: 63px;
            height: 63px;
            background-image: linear-gradient(-170deg, $corporate_2 0, $corporate_2 80%, rgba(0, 0, 0, 0) 80%, rgba(0, 0, 0, 0) 100%);
            position: absolute;
            z-index: 5;
            top: -5px;
            right: 10px;
            color: white;
            font-size: 20px;
            line-height: 29px;
            letter-spacing: 1.1px;
            font-weight: 700;
          }
          
          img {
            @include cover_image;
          }
        }
        
        
        .offer_content {
          text-align: left;
          padding: 30px;
          @include display_flex;
          width: 100%;
          
          .title {
            @include banner_title_styles_2;
            width: 100%;
          }
          
          .text_wrapper {
            display: block;
            width: 100%;
            padding-bottom: 30px;
            
            .text {
              @include text_styles;
              position: relative;
              max-height: 140px;
              overflow: hidden;
              
              &:before {
                content: "";
                position: absolute;
                z-index: 1;
                height: 40px;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: linear-gradient(to top, rgba($lightgrey, .8), rgba(0, 0, 0, 0));
              }
            }
            
            .offer_link {
              display: inline-block;
              font-size: 17px;
              line-height: 24px;
              letter-spacing: .9px;
              font-weight: 700;
              color: $color_text;
              text-decoration: underline;
              margin-top: 30px;
              @include transition(all, .6s);
              
              &:hover {
                opacity: .8;
              }
            }
          }
          
          .offer_button {
            display: inline-flex;
            height: 68px;
            align-self: flex-end;
            align-items: center;
            justify-self: center;
          }
        }
        
        &.with_calendar {
          .offer_content {
            .offer_button:not(.active) {
              font-size: 15px;
              line-height: 21px;
              letter-spacing: 1px;
              
              i {
                margin-right: 10px;
                font-size: 34px;
                display: inline-block;
                vertical-align: middle;
                width: 20%;
              }
              
              span {
                display: inline-block;
                vertical-align: middle;
                width: 65%;
              }
            }
          }
        }
        
        &.entry {
          .offer_img .offer_discount {
            opacity: 0;
            visibility: hidden;
          }
          
          .calendar_offer.sd {
            opacity: 1;
            visibility: visible;
          }
        }
        
        &.departure {
          .offer_img .offer_discount {
            opacity: 0;
            visibility: hidden;
          }
          
          .calendar_offer.ed {
            opacity: 1;
            visibility: visible;
          }
        }
        
        .calendar_offer {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 100%;
          display: block;
          z-index: 5;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
          
          &.sd {
            z-index: 10;
          }
          
          .ui-datepicker {
            width: 100%;
            height: 100%;
            padding: 0;
            border: none;
            border-bottom: 1px solid $corporate_1;
            
            .ui-datepicker-header {
              background: transparent;
              border: 0;
              border-bottom: 1px solid $corporate_1;
              
              .ui-datepicker-prev, .ui-datepicker-next {
                cursor: pointer;
                
                .ui-icon {
                  &:before {
                    font-family: "Font Awesome 5 Pro";
                  }
                }
                
                &.ui-state-hover {
                  border: 0;
                }
              }
              
              .ui-datepicker-title {
                color: $corporate_1;
              }
            }
            
            .ui-datepicker-calendar {
              tbody {
                tr {
                  td {
                    .ui-state-hover {
                      background: #4b4b4b !important;
                      color: white !important;
                    }
                    
                    .ui-state-default {
                      position: relative;
                      z-index: 1;
                      border: none;
                      color: $corporate_2;
                      background: transparent;
                      font-size: 20px;
                      font-weight: 400;
                      text-align: center;
                      cursor: pointer;
                    }
                    
                    &.ui-datepicker-unselectable {
                      .ui-state-default {
                        color: $corporate_1;
                        font-weight: 300;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    html[lang=de] & {
      .offer_button {
        font-size: 14px !important;
      }
    }
  }
  
  .rooms_wrapper .room_block, .promotions_wrapper {
    .room_info, .offer_content {
      h1, h3 {
        @include title_styles;
        text-align: center;
        font-size: 22px;
        padding: 26px 10px 20px;
        line-height: 28px;
        
        .subtitle {
          font-size: 16px;
          line-height: 22px;
        }
      }
      
      .room_description, .desc {
        @include text_styles;
        line-height: 20px;
        font-size: 13px;
      }
    }
    
    .buttons a:not(.button-promotion) {
      background-color: $black;
    }
  }
  
  .location_wrapper {
    display: flex;
    flex-flow: column nowrap;
    
    .map {
      order: 3;
    }
  }
  
  .breadcrumbs {
    a {
      letter-spacing: 1px;
    }
  }
  
  .gallery_divided_title span {
    font-family: $title_family;
    font-size: 20px;
    font-weight: 400;
    bottom: 23px;
  }
  
  .minigallery_wrapper {
    .owl-prev, .owl-next {
      background: rgba($corporate_2, .8);
    }
  }
  
  .form_subtitle {
    @include title_styles;
    margin-top: 20px;
  }
}

.minigallery_wrapper {
  
  .owl-nav {
    z-index: 10;
    
    .owl-prev {
      background: rgb(0, 0, 0);
      background: linear-gradient(90deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
    }
    
    .owl-next {
      background: rgb(0, 0, 0);
      background: linear-gradient(270deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
    }
  }
}

.mobile_engine {
  
  .booking_engine_mobile {
    
    .ui-datepicker.ui-widget {
      font-family: $title_family !important;
      
      .ui-state-default {
        font-family: $title_family !important;
        font-weight: 500;
      }
    }
    
    #full_wrapper_booking {
      font-family: $title_family !important;
      background-color: $black;
      
      .booking_form_title {
        padding-left: 0;
        margin-left: -15px;
        
        .booking_title_1, .booking_title_2, .best_price {
          font-weight: 700;
          color: white;
          font-family: $title_family;
        }
      }
      
      .destination_wrapper {
        background: white !important;
        margin-bottom: 10px;
        width: 100%;
        position: relative;
        
        &:before {
          content: '\f279';
          display: block;
          font-family: "Font Awesome 5 Pro", sans-serif;
          font-size: 14px;
          color: #666;
          @include center_y;
          left: 7px;
          z-index: 2;
        }
        
        &:after {
          content: '\f078';
          display: block;
          font-family: "Font Awesome 5 Pro", sans-serif;
          font-size: 18px;
          color: #666;
          @include center_y;
          right: 7px;
          z-index: 2;
        }
        
        select {
          width: 100%;
          height: 45px;
          padding-left: 35px;
          box-sizing: border-box;
        }
      }
      
      .dates_selector_personalized {
        width: calc(100% - 5px);
        
        .dates_selector_label {
          position: absolute;
          top: 2px;
          font-weight: 700;
          color: white;
          font-family: $title_family !important;
        }
        
        .start_end_date_wrapper {
          
          .day {
            font-size: 60px;
            line-height: 60px;
            margin-bottom: 4px;
          }
          
          .month, .year {
            font-size: 13px;
          }
          
          .day, .month, .year {
            color: $black;
            font-family: $title_family !important;
          }
          
          .start_date_personalized {
            width: 50%;
            left: 0;
          }
          
          .end_date_personalized {
            width: 50%;
            right: 0;
          }
        }
      }
      
      .wrapper_booking_button {
        
        .guest_selector {
          width: 50%;
        }
        
        .promocode_wrapper {
          width: 50%;
          background-color: white;
          
          .promocode_label {
          }
          
          input.promocode_input {
            color: $black;
            border: dashed 2px $grey;
            
            &::placeholder {
              color: $black;
            }
          }
        }
        
        .submit_button {
          width: 100%;
          background-color: $corporate_1;
          position: relative;
          font-size: 12px;
          font-weight: 700;
          text-transform: uppercase;
          font-family: $title_family !important;
        }
      }
      
      .room_list_wrapper {
        background-color: $black;
        
        .rooms_wrapper {
          width: 100%;
          
          .room_list_wrapper_close {
            background-color: $corporate_2;
          }
        }
      }
    }
  }
  
  .mobile_engine_action {
    width: calc(100% - 40px);
    border-radius: 60px;
    box-shadow: none;
    bottom: 70px;
    font-size: 22px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  
  &.open {
    .mobile_engine_action {
      background-color: $black;
      color: white;
      left: auto;
      right: 10px;
      bottom: 295px;
      z-index: 10;
      
      &::after {
        display: none;
      }
    }
  }
}

#contentInPage {
  h1 {
    @include banner_title_styles_2;
  }
}

.modal_wrapper {
  .modal {
    padding: 0 0 10px;
    width: 100%;
  }
}

#entry_date_popup .buttons_bottom, #departure_date_popup .buttons_bottom {
  justify-content: space-evenly;
}
#entry_date_popup .buttons_bottom .step_label, #departure_date_popup .buttons_bottom .step_label {
  width: 45%;
}
#booking_engine_popup .mobile_engine_v2 #full_wrapper_booking .wrapper_booking_button .submit_button {
  background: $corporate_1 !important;
}