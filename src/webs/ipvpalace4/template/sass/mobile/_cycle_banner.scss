.cycle_banner_wrapper {
  @include base_mobile_styles;
  padding: 0;
  background-color: $lightgrey;
  .banner {
    &:not(:last-of-type) {
      margin-bottom: 20px;
    }

    .banner_gallery {
      display: block;
      width: 100%;
      height: 250px;
      overflow: visible;
      position: relative;

      .owl-stage-outer, .owl-stage, .owl-item {
        height: 100%;
      }

      .banner_img {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        img {
          @include cover_image;
        }
      }

      .owl-nav {
        display: block;
        position: absolute;
        bottom: 70px;
        left: 0;
        right: 0;
        height: 0;

        .owl-prev, .owl-next {
          @include owl_nav_styles;
          @include center_y;
          left: 40px;
          color: white;
          @include transform(translateX(-100%));
        }

        .owl-next {
          left: auto;
          right: 40px;
          @include transform(translateX(100%));

          &:after {
            left: auto;
            right: 98%;
          }
        }
      }
    }

    .banner_content {
      text-align: center;
      padding: 20px;

      .title {
        @include banner_title_styles_2;
      }

      .text {
        @include text_styles;
        padding: 20px 0;
      }

      .salones_icons {
        @include display_flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding-bottom: 20px;

        .icon {
          display: inline-block;

          .icon_img {
            display: block;
            height: 50px;
            img {
              max-height: 50px;
            }
          }

          .icon_text {
            display: block;
            padding-top: 10px;
            font-size: 17px;
            line-height: 14px;
            font-weight: 700;
            letter-spacing: .9px;
            color: $corporate_1;
          }
        }
      }
    }
  }
}