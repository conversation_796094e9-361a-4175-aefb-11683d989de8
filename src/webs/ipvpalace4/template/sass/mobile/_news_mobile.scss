.news_wrapper {
  .entry_widget {
    position: relative;
    .tags {
      top: 0;
      right: 0;
      .tags_title {
        display: none;
      }
    }
    .title {
      h2 {
        font-family: $title_family;
        font-weight: bold;
        display: block;
        position: relative;
        font-size: 27px;
        padding: 0 10px 10px;
        &:before,
        &:after {
          display: none;
        }
      }
      font-family: $title_family;
      font-weight: bold;
    }
    .date {
      .fa {
        font-family: "Font Awesome 5 Pro";
        font-weight: 300;
      }
    }
    .content {
      @include text_styles;
    }
  }
}
.entry_picture {
  position: relative;
  img {
    width: 100%;
    vertical-align: middle;
  }
  .entry_info {
    position: absolute;
    bottom:0;
    left: 0;
    right: 0;
    text-align: left;
    padding: 30px 20px;
    background: linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0.8));
    .entry_title {
      font-size: 20px;
      font-family: $title_family;
      font-weight: bold;
      color: white;
    }
  }
}
article.entry {
  text-align: left;
  padding-top: 10px;
  padding: 0 20px;
  .tags_wrapper {
    margin-bottom: 15px;
    .date, .tags, .author {
      display: inline-block;
      padding: 5px 7px;
      text-transform: uppercase;
      font-size: 12px;
      color: $grey2;
    }
    .date, .tags {
      border-right: 1px solid #fafafa;
    }
    .tags {
      color: $corporate_1;
      letter-spacing: 2px;
      margin-top: 15px;
    }
    .author {
      color: $black;
    }
  }
  .entry_content {
    @include text_styles;
    padding: 0 7px 15px;
  }
  .entry_share {
    paddgin-bottom: 15px;
  }
  .entry_comments {
    h3 {
      display: inline-block;
      padding: 5px 7px;
      text-transform: uppercase;
      color: $grey2;
    }
    .comment_form {
      margin: 0 10px;
      position:relative;
      label {
        display: inline-block;
        padding: 5px 7px;
        text-transform: uppercase;
        color: $grey2;
        font-size: 12px;
      }
      input:not([type="checkbox"]), textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 16px;
        border-width: 0;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 20px;
        border-bottom: 1px solid $corporate_1;
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }
      input[type=checkbox] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: transparent;
        border: 1px solid $corporate_1;
        border-radius: 0;
        width: 14px;
        height: 14px;
        margin-right: 15px;
        display: inline-block;
        vertical-align: middle;
        position: relative;
        outline: none;
        &:checked {
          &:before {
            content: '';
            width: 8px;
            height: 8px;
            background: $black;
            @include center_xy;
          }
        }
      }
      a {
        color: $corporate_1;
        font-size: 12px;
        &:hover {
          text-decoration: underline;
        }
      }
      #popup_form_button {
        @include btn_styles;
        margin-top: 15px;
      }
    }
  }
}
aside.right_column {
  padding-top: 15px;
  .widget_title {
    display: inline-block;
    padding: 5px 7px;
    text-transform: uppercase;
    font-size: 12px;
    color: $grey2;
  }
  .news_right_widget {
    .news_widget_entry {
      margin-bottom: 15px;
      &:hover {
        .content .title a {
          color: $corporate_1;
        }
      }
      .image {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .content {
        padding-top: 10px;
        .title {
          font-size: 16px;
          font-family: $title_family;
          font-weight: bold;
          color: $black;
          a {
            color:$black;
          }
        }
      }
    }
  }
  .social_widget {
    padding: 10px 0;
    text-align: center;
    a {
      color: $corporate_1;
      display: inline-block;
      padding: 0 5px;
      &:hover {
        color: $black;
      }
    }
  }
}