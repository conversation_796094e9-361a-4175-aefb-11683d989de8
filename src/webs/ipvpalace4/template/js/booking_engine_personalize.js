$(window).load(function () {
    fade_out_widget();
    prepare_guests_selector();
    set_occupancy_number();
    room_selector_dates();
});

function prepare_guests_selector() {
   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

   $(".remove_room_element").click(function(){
      var actual_room_numbers = $("select.rooms_number").val();
      if (actual_room_numbers > 1){
         var target_room_number = parseInt(actual_room_numbers) - 1;
         $("select.rooms_number option").removeAttr('selected');
         $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
         $(".room" + actual_room_numbers).hide();
         $("select.rooms_number").val(target_room_number);
         $("select.rooms_number").selectric("refresh");
      }
      set_occupancy_number()
   });

   var add_room_html = "<div class='add_room'><i class='far fa-plus'></i></div>",
       remove_room_html = "<div class='remove_room'><i class='fal fa-trash-alt'></i></div>",
       close_btn = "<div class='icon-xcross close_guest_selector'></div>";
   $(".room_list_wrapper").append(close_btn);
   $(".room_list_wrapper .room_list").append(add_room_html);
   $(".room_list_wrapper .room_list .room2").append(remove_room_html);
   $(".room_list_wrapper .room_list .room3").append(remove_room_html);
   $(".add_room").click(add_room);
   $(".remove_room").click(remove_room);

   $(document).on("mousedown", ".close_guest_selector", function(){
      $(".booking_steps .step_3").removeClass("current_step");
      $(".room_list_wrapper").slideUp();
      $("#full_wrapper_booking").removeClass("fixed");
   });

   $(".adults_selector .room_selector .label").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",8,0,1);
   });
   $(".adults_selector .room_selector .button").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",9,1,-1);
   });
   $(".children_selector .room_selector .label").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",4,-1,1);
   });
   $(".children_selector .room_selector .button").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",5,0,-1);
   });
   $(".babies_selector .room_selector .label").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",2,-1,1);
   });
   $(".babies_selector .room_selector .button").click(function(){
      change_selectric_rooms($(this),".room_selector", "select.room_selector",3,0,-1);
   });
   $(document).on("mousedown", "#full-booking-engine-html-7 .selector_adultos .selectric .label", function(){
      change_selectric_rooms($(this),".selector_adultos", "select.selector_adultos",8,0,1);
   });
   $(document).on("mousedown", "#full-booking-engine-html-7 .selector_adultos .selectric .button", function(){
      change_selectric_rooms($(this),".selector_adultos", "select.selector_adultos",9,1,-1);
   });
   $(document).on("mousedown", "#full-booking-engine-html-7 .selector_ninos .selectric .label", function(){
      change_selectric_rooms($(this),".selector_ninos", "select.selector_ninos",4,-1,1);
   });
   $(document).on("mousedown", "#full-booking-engine-html-7 .selector_ninos .selectric .button", function(){
      change_selectric_rooms($(this),".selector_ninos", "select.selector_ninos",5,0,-1);
   });
}

function change_selectric_rooms(element, parent_class, select, max, min, operator) {
    var selectric_element = element.closest(parent_class).find(select);
  if(parseInt(selectric_element.val()) > min &&
     parseInt(selectric_element.val()) < max){
    var new_select_val = parseInt(selectric_element.val()) + operator;
    selectric_element.val(new_select_val);
    selectric_element.selectric('refresh');
    set_occupancy_number();
  }
}
function add_room(){
   var number_rooms = parseInt($("select.rooms_number").val());
   if (number_rooms < 3) {
       $($(".rooms_number .selectricItems li").get(number_rooms)).trigger("click");
       set_occupancy_number();
   }
   if(number_rooms === 2) {
      $(".add_room").hide();
   }
}
function remove_room(){
   var number_rooms = parseInt($("select.rooms_number").val());
   if (number_rooms > 1) {
       $($(".rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
       set_occupancy_number();
   }
   if(number_rooms < 4) {
      $(".add_room").show();
   }
}
function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
      console.log("showing");
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = $("select[name='numRooms']").val(),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
             actual_select_babies = $("select[name='babiesRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
            babies_number += parseInt(actual_select_babies);
         }
      }
   }

   var room_tag = $("#room_tag").val(),
       rooms_tag = $("#rooms_tag").val(),
       room_target_tag = number_of_rooms == 1 ? room_tag : rooms_tag,
       target_placeholder = $(".guest_selector .placeholder_text"),
       placeholder_string = number_of_rooms + " " + room_target_tag + " / ";


   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);
   babies_number = parseInt(babies_number);

   placeholder_string += adults_number;

   if(!$(".adults_only_selector").length){
      placeholder_string += " - " + kids_number;
      if (babies_number) {
         placeholder_string += " - " + babies_number;
      }
   }

   target_placeholder.html(placeholder_string);
}

function room_selector_dates() {
   var room_list_wrapper = $('.room_list_wrapper .room_list'),
       dates_wrapper = $('<div class="room_info_wrapper"><div class="hotel_name_rooms"></div><div class="dates_wrapper"></div></div>');

   if (room_list_wrapper.length) {
      room_list_wrapper.prepend(dates_wrapper);
   }
   $('.dates_wrapper').on('click', function () {
      $(".booking_steps .step_2").trigger('click');
   });
}