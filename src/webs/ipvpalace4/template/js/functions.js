$(window).load(function () {

    var getSeeMoreLink = $('.content_subtitle_description').find('.see_more');
    var getSeelessLink = $('.content_subtitle_description').find('.see_less');
    var getHideItem = $('.content_subtitle_description').find('.hide');

    if (getHideItem.length) {
        $('.content_subtitle_description .links_see_more').show();
    } else {
        $('.content_subtitle_description .links_see_more').hide();
    }

    getSeeMoreLink.click(function () {
        $('.content_subtitle_description .hide').slideDown();
        $(this).hide();
        getSeelessLink.show();
    });

    getSeelessLink.click(function () {
        $('.content_subtitle_description .hide').slideUp();
        $(this).hide();
        getSeeMoreLink.show();
    });

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");

        }
        smartDatas($(this));
    });
    $(".offer_button").click(function () {
        smartDatas($(this));
    });
    $(".close_main_menu_button").click(function () {
       $("#main_menu").toggleClass("active");
       $("header").toggleClass("opened");
    });
    $(".menu_toggle").click(function () {
       $("#main_menu").toggleClass("active");
       $("header").toggleClass("opened");
    });

    $(".user .popup .close").click(function () {
        $(".user .popup").fadeOut();
        //$(".user label").fadeIn();
        $(".user").addClass('active');
    });
    $(".user i").click(function () {
        $(".user .popup").fadeIn();
        //$(".user label").detach();
        $(".user").removeClass('active');
    });
    $(".open_popup_phone").click(function (e) {
         if($(e.target).closest(".content").length < 1) {
            $(".popup_phone").slideToggle();
         }
    });
    $(".open_modal").click(function (e) {
        e.preventDefault();
        let target_content = $(this).attr("href");
        $(".modal_wrapper .modal .content").html($(target_content).html());
        $(".modal_wrapper").addClass("active");
    });
    $(".close_modal").click(function (e) {
        e.preventDefault();
        $(".modal_wrapper .modal .content").html("");
        $(".modal_wrapper").removeClass("active");
    });
    $(document).click(function (e) {
        if($(e.target).closest(".modal").length < 1 &&
           $(e.target).closest(".open_modal").length < 1) {
            $(".modal_wrapper .modal .content").html("");
            $(".modal_wrapper").removeClass("active");
        }
    });

    ;(function($, win) {
      $.fn.inViewport = function(cb) {
         return this.each(function(i,el){
           function visPx(){
             var H = $(this).height(),
                 r = el.getBoundingClientRect(), t=r.top, b=r.bottom;
             return cb.call(el, Math.max(0, t>0? H-t : (b<H?b:H)));
           } visPx();
           $(win).on("resize scroll", visPx);
         });
      };
    }(jQuery, window));

    var classInView = $(".animate_reveal_on_scroll")

    classInView.inViewport(function(px){
        if (px) {
            $(this).addClass("inview_class");
        }
    });

     $('.cursor_left').click(function () {
        $('.tp-leftarrow').click();
    });

    $('.cursor_right').click(function () {
        $('.tp-rightarrow').click();
    });

    showMenu();

    $('.preloading').fadeOut();

});

$(window).scroll(showMenu);

function showMenu() {
    let actual_position = $(window).scrollTop();
    let slider_height = $("#slider_container").height();
    slider_height = slider_height / 2;
    let menu_showed = $("header").hasClass('fixed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("header").addClass('fixed');
        $("#main_menu").addClass('fixed');
        $(".scroll_nav").addClass('fixed');
        $(".user .popup").fadeOut();
        $(".user label").fadeIn();
        $("#full_wrapper_booking").fadeOut();
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("header").removeClass("fixed");
        $("#main_menu").removeClass("fixed");
        $(".scroll_nav").removeClass("fixed");
        $("#full_wrapper_booking").fadeIn();
    }
}

function smartDatas(selector) {
    var promocode = selector.attr("data-promocode"),
        smart_promocode = selector.attr('data-smartpromocode'),
        data_ini = selector.attr("data-smartdateini"),
        data_fin = selector.attr("data-smartdatefin"),
        namespace = selector.attr("namespace"),
        data_package_orders = selector.attr('data-smartpackageorder');

    if (namespace){
        $(".hotel_element_available[namespace='" + namespace + "'").trigger('click');
    }

    if (promocode) {
        $(".paraty-booking-form").each(function () {
            $(this).find("input[name=promocode]").val(promocode)
        });
    }

    if (smart_promocode) {
         $(".paraty-booking-form").each(function () {
            $(this).find("input[name=promocode]").val(smart_promocode)
        });
    }

    if(data_ini) {
        $("#data input[name=startDate]").val(data_ini);

        if (data_fin) {
            $("#data input[name=endDate]").val(data_fin);
        } else {
            var end_date = $.datepicker.parseDate("dd/mm/yy", data_ini);
            end_date.setDate(end_date.getDate() + 1);
            end_date = $.datepicker.formatDate("dd/mm/yy", end_date);
            $("#data input[name=endDate]").val(end_date);
            $("#data input[name=endDate]").datepicker("option", "minDate", end_date);
        }
    }
    var package_preselection = selector.attr('data-smartpackage');
    if (package_preselection) {
        $(".paraty-booking-form").each(function () {
            var package_preselection = $("<input type='hidden' name='package_preselection' value='true' class='smart_packages'></input>");
            package_preselection.addClass('hidden_package_preselection');
            $(this).append(package_preselection);
        });
    } else {
        $(".paraty-booking-form").each(function () {
            var package_preselection = $(".smart_packages");
                package_preselection.remove();
        });
    }

   var packages_orders_input = $("input[name='packages_order']");
    if (!packages_orders_input.length) {
        $(".paraty-booking-form").each(function(){
            $(this).append($("<input type='hidden' name='packages_order' value=''></input>"));
        });
        packages_orders_input = $("input[name='packages_order']");
    }
    if (data_package_orders) {
        packages_orders_input.val(data_package_orders);
    } else {
        packages_orders_input.val("");
    }
}