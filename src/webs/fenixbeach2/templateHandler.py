# -*- coding: utf-8 -*-
from booking_process.libs.communication import directDataProvider
from booking_process.constants.advance_configs_names import CONTACT_PHONES
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getLogotypes
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_title, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
from collections import OrderedDict

import os

from webs.fenixbeach2.extra_terms import ENGLISH_EXTRA, SPANISH_EXTRA, FRENCH_EXTRA, GERMAN_EXTRA

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "feni2"



typeApartamento = {
	'label': "Apartamentos",
	'name': 'Apartamento',
	'roomfilter':'.*dormitorio.*|.*presiden.*'

}

typeHabitacion = {
	'label': "Hotel",
	'name': 'Habitacion',
	'roomfilter':'^(?!(.*dormitorio.*|.*presiden.*)).*'

}

roomtype_list = [typeHabitacion,typeApartamento ]


class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		params['custom_caption_submit_book'] = get_web_dictionary(language)['T_reserva_ahora']

		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		if user_agent_is_mobile():
			options['selectOptions'] = self.get_widget_hotels(language)
		else:
			options['hotels_list'] = self.get_widget_hotels(language)

		return options

	def get_revolution_initial_height(self):
		return "600"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_onHoverStop(self):
		return "off"

	def get_revolution_loadingbar(self):
		'''By default is off'''
		status = 'off'
		return status

	def get_name_hotel(self, language):
		host = os.environ.get('HTTP_HOST')
		name = ""
		if "santacruz" in host or "6-dot" in host:
			name = "santacruz"

		if "montblanc" in host or "15-dot" in host:
			name = "montblanc"

		return name


	def buildRoomsFromSections(self, seccion_bloque , language):

		all_rooms = get_pictures_from_section_name(seccion_bloque, language)

		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures'] = get_pictures_from_section_name(sect_gallery, language)
			room['name'] = room['title']

		return all_rooms

	def buildRevolutionSliderInnersSections(self, language):
		myPictures = self.getMainGallery(language)

		params = {
			'pictures': myPictures,
			'initialHeight': self.get_revolution_initial_height(),
			"startWidth": self.get_revolution_start_width(),
			"fullWidth": self.get_revolution_full_width(),
			"fullScreen": "off",
			"onHoverStop": "on"
		}

		return self.buildTemplate("general/slider_revolution.html", params, allowMobile=False)

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get("cycle_banner"):
			result["cycle_banner"] = self.getPicturesProperties(language, advance_properties.get("cycle_banner"), ['link_text', 'external_link'])

		if advance_properties.get("banner_ventajas"):
			bannerVentajasName = advance_properties.get("banner_ventajas")

			if self.getPicturesProperties(language, bannerVentajasName, ["hide", "text_hide"]):
				result["banner_ventajas_section"] = get_section_from_section_spanish_name(bannerVentajasName, language)
				banner_ventajas_pic = self.getPicturesProperties(language, bannerVentajasName, ["hide", "text_hide"])
				result["banner_ventajas"] = list(filter(lambda x: x.get('altText') != '1', banner_ventajas_pic))
				result["banner_ventajas_icos"] = list(filter(lambda x: x.get('altText') == '1', banner_ventajas_pic))

		return result

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''

		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']
			section_name_key=sectionToUse.get('key','')

		location_html = get_section_from_section_spanish_name(u"Localización", language)

		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'language_selected': get_language_title(language)[:3],
							  'phones': get_config_property_value(CONTACT_PHONES).split(";"),
							  'location_html': location_html,
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'newsletter_info': get_section_from_section_spanish_name("Newsletter", language),
							  'name_hotel': self.get_name_hotel(language),
							  'work_with_us_link': get_section_from_section_spanish_name("Trabaja con nosotros", language)
							  }

		if get_section_from_section_spanish_name("Aviso legal", language):
			result_params_dict["has_legal"] = True

		result_params_dict['extra_policies_sections'] = get_sections_from_type('LOPD', language)


		#GENERICS SECTIONS
		sections_with_generics = {
			"inicio": False,
			"eventos": False,
			u"localización": False,
			"mis reservas": False
		}

		#automatic sections (currently only gallery)
		automaticSections = {
				'Galeria de Imagenes': True,
				'Mis Reservas': True,
				'Mis Reservas Corp': True
		}
		automatic_content = automaticSections.get(section_type, False)
		result_params_dict['automatic_content'] = automatic_content

		result_params_dict['hidden_logo'] = getLogotypes('mobile')

		generic_section = sections_with_generics.get(section_name, True)
		result_params_dict['generic_section'] = generic_section

		if generic_section:
			result_params_dict['generic_description'] = get_section_from_section_spanish_name(section_name, language).get("content","")

		#we try to get a minigallery for any sections,
		if section_name_key:
			mini_gallery_name = ''
			carousel_section = ''

			all_properties_section = directDataProvider.get("WebPageProperty", {"entityKey": section_name_key, "languageKey": language}, limit=50)

			for property in all_properties_section:
				if property.value:
					if property.mainKey == "minigaleria":
						mini_gallery_name = property.value

					if property.mainKey == "carousel":
						carousel_section = property.value

			if mini_gallery_name:
				result_params_dict['mini_gallery'] = get_pictures_from_section_name(mini_gallery_name, language)
			if carousel_section:
				result_params_dict['carousel_section'] = get_pictures_from_section_name(carousel_section, language)

		section_with_bannersLines = {
			"hotel": True,
			"inicio": True
		}
		section_with_bannersLine = section_with_bannersLines.get(section_name, False)
		result_params_dict['section_with_bannersLine'] = section_with_bannersLine

		if section_with_bannersLine:
			line_banners1 = self.getPromotionBanners('banners1', language)
			line_banners2 = self.getPromotionBanners('banners2', language)
			line_banners3 = self.getPromotionBanners('banners3', language)
			line_banners4 = self.getPromotionBanners('banners4', language)
			line_banners5 = self.getPromotionBanners('banners5', language)
			line_banners6 = self.getPromotionBanners('banners6', language)

			line_banners_list = [line_banners1, line_banners2, line_banners3,line_banners4,line_banners5,line_banners6]

			result_params_dict['line_banners_list'] = line_banners_list

		sections_with_bannersx2 = {
			"hotel": True,
			"inicio": True
		}
		section_with_bannersx2 = sections_with_bannersx2.get(section_name, False)
		result_params_dict['section_with_bannersx2'] = section_with_bannersx2
		if section_with_bannersx2:
			bannerx2_1 = self.getPromotionBanners('underBookingPictures',language) #debajo busqueda
			bannerx2_2 = self.getPromotionBanners('upperRightBanner',language) #derecha superior
			bannerx2_3 = self.getPromotionBanners('lowerRightBanner', language) #derecha inferior

			x2_banners_list = [bannerx2_1, bannerx2_2, bannerx2_3]
			result_params_dict['x2_banners_list'] = x2_banners_list
		else:
			result_params_dict['innersSlider'] = self.buildRevolutionSliderInnersSections(language)

		sections_with_bannersFooter = {
			"hotel": True,
			'inicio': True
		}
		section_with_bannersFooter = sections_with_bannersFooter.get(section_name, False)
		result_params_dict['section_with_bannersFooter'] = section_with_bannersFooter
		if section_with_bannersFooter:
			result_params_dict['hoteles_banners'] = get_pictures_from_section_name("hoteles_banners", language)
			result_params_dict['hoteles_banners_title'] = get_section_from_section_spanish_name("hoteles_banners", language)


		if section_name == "inicio" or section_type == 'Inicio':
			result_params_dict['cartela_home'] = get_section_from_section_spanish_name("cartela portada", language).get("content","")
			forfait_section = get_section_from_section_spanish_name("forfait", language)
			if forfait_section:
				advance_forfait = self.getSectionAdvanceProperties(forfait_section, language)
				forfait_section['forfait_text'] = advance_forfait.get("link_forfait")
				result_params_dict['forfait_section'] = forfait_section
			result_params_dict['under_booking'] = get_pictures_from_section_name("_under_booking_links", language)

		elif section_name == "eventos":
			all_events =  get_pictures_from_section_name("eventos blocks", language)

			for event in all_events:
				dayEvent = ""
				monthEvent = ""
				yearEvent = ""

				if event['title']:
					splitDate = event['title'].split("/")

					if len(splitDate)==3:
						dayEvent = splitDate[0]
						monthEvent = splitDate[1]
						yearEvent = splitDate[2]

						dictionary = eval('datesDictionary.dates_%s' % language)
						monthNamesShort = dictionary.get("monthNamesShort","")

						if monthNamesShort:
							monthEvent=monthNamesShort[int(monthEvent)-1]

				event['dayEvent'] = dayEvent
				event['monthEvent'] = monthEvent
				event['yearEvent'] = yearEvent

			result_params_dict['all_events'] = all_events

		elif section_name == u'localización':
			additionalParams4Contact = {}
			additionalParams4Contact['language'] = get_language_code(language)
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['privacy_checkbox'] = True
			mySectionParams['content']='' #we dont need here the description. We've got it above
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			iframe_google_map=get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form=sectionToUse['subtitle']

			result_params_dict['contact_html'] = contact_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form

			result_params_dict['special_down'] = True

		elif section_name == "hotel":
			#Only for corporate
			#all_rooms = super(TemplateHandler, self).buildRoomInfo(language)
			all_rooms = self.buildRoomsFromSections("habitaciones blocks",language)

			result_params_dict['rooms'] = all_rooms
			result_params_dict['tipo_ico'] = "ico_H.png"

		elif section_name == "apartamentos" or section_type == 'Habitaciones':
			#Only for corporate
			#all_rooms = super(TemplateHandler, self).buildRoomInfo(language)
			all_rooms = self.buildRoomsFromSections("apartamentos blocks",language)

			result_params_dict['rooms'] = all_rooms
			result_params_dict['tipo_ico'] = "ico_habitaciones.png" if self.get_name_hotel(language) == "santacruz" else "ico_A.png"

		elif section_type == "Ofertas":
			#all_promos =  get_pictures_from_section_name("ofertas blocks", language)
			#all_packs =  get_pictures_from_section_name("paquetes blocks", language)

			#all_promos = all_promos
			#result_params_dict['all_packs'] = all_packs

			#promopacks_list = {"promos":all_promos, "packs":all_packs}
			result_params_dict['promopacks_list'] = super(TemplateHandler, self).buildPromotionsInfo(language)

			#result_params_dict['ofertas_label'] = get_section_from_section_spanish_name("ofertas blocks", language).get("subtitle","")
			#result_params_dict['packs_label'] = get_section_from_section_spanish_name("paquetes blocks", language).get("subtitle","")

		elif section_type == "Galeria de Imagenes":
			result_params_dict['pictures'] = get_pictures_from_section_name("slider_imagenes", language)

		elif section_name == "trabaja con nosotros":
			result_params_dict['work_with_us'] = True
			if language == "SPANISH":
				extra_dict = SPANISH_EXTRA
				news = 'noticias'
			elif language == "ENGLISH":
				extra_dict = ENGLISH_EXTRA
			elif language == "GERMAN":
				extra_dict = GERMAN_EXTRA
			elif language == "FRENCH":
				extra_dict = FRENCH_EXTRA
			else:
				extra_dict = SPANISH_EXTRA
			result_params_dict = dict(list(result_params_dict.items()) + list(extra_dict.items()))

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			mini_dict = {'minigallery': minigallery_images,'num_items': 5}
			minigallery_html = self.buildTemplate_2("_minigallery.html", mini_dict, False, 'fenixbeach2')
			result_params_dict["minigallery"] = minigallery_html
			result_params_dict['minigallery_content'] = get_section_from_section_spanish_name(advance_properties.get("minigallery"), language)

		if advance_properties.get("custom_form_text"):
			result_params_dict['custom_form_text'] = advance_properties.get("custom_form_text")

			if advance_properties.get("custom_email"):
				result_params_dict['custom_email'] = advance_properties.get("custom_email")

		result_params_dict['popup_inicio_automatico'] = get_pictures_from_section_name('popup inicio', language)

		result_params_dict.update(self.getExtraBanners(sectionToUse, language))

		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Normal':
			result['use_h1'] = True

		if section['sectionType'] == 'Mis Reservas Corp':
			result['selectOptions'] = self.get_widget_hotels(language)
			result['modify_reservation'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation_content'] = True

		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			result['use_h1'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:
				nameFilter = x.get('title', "")
				if not nameFilter:
					nameFilter = ""

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [x['servingUrl']]
				else:
					filters[nameFilter].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = templateSectionsDict.get(sectionType,
		                                    super(TemplateHandler, self).getTemplateForSectionType(sectionType,
		                                                                                           sectionTemplate))
		return template

	# Mobile Version

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		advance_properties = self.getSectionAdvanceProperties(section, language)
		language_dict = get_web_dictionary(language)
		result = "<div class='section_content'>"

		if extra_banners.get("cycle_banner"):
			args = {
				"cycle_banner": extra_banners['cycle_banner']
			}
			args.update(language_dict)

			result += self.buildTemplate_2("banners/_cycle_banner.html", args, False, "fenixbeach2")

		if advance_properties.get("custom_form_text"):
			form_dict = {'custom_form_text': advance_properties.get("custom_form_text")}

			if advance_properties.get("custom_email"):
				form_dict['custom_email'] = advance_properties.get("custom_email")

			form_dict = dict(list(form_dict.items()) + list(get_web_dictionary(language).items()))
			form_dict['language'] = get_language_code(language)

			result += self.buildTemplate("../webs/fenixbeach2/template/_custom_form.html", form_dict, False)

		if advance_properties.get('homecarousel'):
			if get_pictures_from_section_name(advance_properties.get("homecarousel"), language):
				banner_ventajas_pic = get_pictures_from_section_name(advance_properties.get("homecarousel"), language)
				home_carousel = {
					'banner_ventajas_section': get_section_from_section_spanish_name(advance_properties.get("homecarousel"), language),
					'banner_ventajas': filter(lambda x: x.get('altText') != '1', banner_ventajas_pic),
					'banner_ventajas_icos': filter(lambda x: x.get('altText') == '1', banner_ventajas_pic)
				}
				result += self.buildTemplate_2("mobile/_home_carousel_mobile.html", home_carousel, False, 'fenixbeach2')


		result += "</div>"

		return result

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)
		base_path = os.path.dirname(__file__)
		result_params_dict = {}
		additionalParams['custom_elements'] = ''
		additionalParams['use_h1'] = True

		if user_agent_is_mobile():
			functions_mobile = "<script src='/js/%s/functions_mobile.js?v=1.00'></script>" % (base_web)

			section_name = ""
			if 'sectionName' in currentSection:
				section_name = currentSection['sectionName'].lower().strip()

			section_type = ""
			if 'sectionType' in currentSection:
				section_type = currentSection['sectionType']

			if section_name == 'hotel':
				result_params_dict['rooms_information'] = get_pictures_from_section_name('habitaciones blocks', language)
				for x in result_params_dict['rooms_information']:
					x['gallery'] = x['pictures']
				result_params_dict['title'] = self.getSectionParams(sectionFriendlyUrl, language)
				result_params_dict['more_pictures'] = True
				result_params_dict['default'] = True

				fullPath = os.path.join(base_path, '../../templates/mobile/secciones/rooms_new/rooms_3.html')
				result = dict(list(result_params_dict.items()) + list(get_web_dictionary(language).items()))

				return buildTemplate(fullPath, result)

			if section_name == 'apartamentos' or section_type == 'Habitaciones':
				result_params_dict['rooms_information'] = get_pictures_from_section_name('apartamentos blocks', language)
				for x in result_params_dict['rooms_information']:
					x['gallery'] = x['pictures']
				result_params_dict['title'] = self.getSectionParams(sectionFriendlyUrl, language)
				result_params_dict['more_pictures'] = True
				result_params_dict['default'] = True
				result_params_dict['use_h1'] = True

				fullPath = os.path.join(base_path, '../../templates/mobile/secciones/rooms_new/rooms_3.html')
				result = dict(list(result_params_dict.items()) + list(get_web_dictionary(language).items()))

				return buildTemplate(fullPath, result)

			if section_name == 'ofertas':
				additionalParams['elements'] = self.buildPromotionsInfo(language)
				mySectionParams = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()))
				fullPath = os.path.join(base_path, '../../templates/mobile/secciones/promotions_new/promotions_3.html')

				return buildTemplate(fullPath, mySectionParams)

			if section_name == 'ofertas y paquetes':
				listado = get_pictures_from_section_name("paquetes blocks", language) + get_pictures_from_section_name('ofertas blocks', language)

				result_params_dict['hotel'] = listado
				result_params_dict['title'] = self.getSectionParams(sectionFriendlyUrl, language)
				result_params_dict['default'] = True

				fullPath = os.path.join(base_path, 'template/mobile_template.html')
				result = dict(list(result_params_dict.items()) + list(get_web_dictionary(language).items()))

				return buildTemplate(fullPath, result)


			additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

			additionalParams['custom_elements'] += functions_mobile

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def get_widget_hotels(self, language):
		widget_options = self.getPicturesProperties(language, "_widget_selector", ['url_booking'])
		for x in widget_options:
			x['name'] = x.get("title")
			x['value'] = x.get("title")
			x['namespace'] = x.get("namespace")
			x['id'] = x.get("namespace")
			split_url = x.get("url_booking", "").split('/')
			only_host = split_url[0]
			x['domain'] = "https://" + x.get("namespace") + only_host

		if user_agent_is_mobile():
			for x in widget_options:
				x['id'] = "https://" + x.get("namespace", "") + x.get("url_booking", "")
				x['value'] = x.get("title")

		return widget_options

