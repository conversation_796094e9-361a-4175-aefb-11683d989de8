

$(document).ready(function(){


     if (typeof $.i18n == 'object') {
        if (typeof messages == 'object') {
            $.i18n.load(messages);
        }
    }


    jQuery.validator.addMethod("phone", function(phone_number, element) {
        phone_number = phone_number.replace(/\s+/g, "");
      return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
        phone_number.match(/^[0-9 \+]\d+$/);
    }, "Please specify a valid phone number");

    /****************** Work with us *********************/

    $("#work-with-us").validate({
      rules: {
        name: "required",
        email: {
          required: true,
          email: true
        },
        privacy: "required"

      },
        highlight: function(element) {
            $(element).addClass('input-error');
        }, unhighlight: function(element) {
            $(element).removeClass('input-error');
        }

    });

    function envia_form_work(id_form,clase_form){

        var url_for_upload = "";

        $(clase_form + " " + ".btn-corporate").css("display","none");

      
        $.ajax({
                    url: "/get_upload_url",
                    type: 'GET',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                     url_for_upload = returndata;
                    }
                  });


        if ( $(id_form).valid() ) {


            var url_cv_download = "";


           var confirma_no_cv=1;

            if ($(clase_form + " " + "#file_cv").length && url_for_upload && $(clase_form + " " + "#file_cv").val()) {
                //upload the file to get a url
                confirma_no_cv = 0;


                //grab all form data
                var formData = new FormData($(id_form)[0]);


                $.ajax({
                    url: url_for_upload,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {


                        url_cv_download = returndata;
                    }
                });


            }


            if (!confirma_no_cv && (!url_cv_download || url_cv_download=="NO_FILE_UPLOAD")){
                alert("ERROR UPLOADING CURRICULUM VITAE");
                window.location.reload();

                return false;
            }




            var send_email = 1;
            if (confirma_no_cv )  {
                if (confirm($.i18n._("confirm_no_cv"))   ){

                }
                else{
                    send_email = 0;
                }
            }


            if (send_email){

                 $.post(
                  "/utils/?action=work_with_us",
                  {
                    'section': $(clase_form+" "+"#subject_email").val(),
                    'destination_email':  $(clase_form+" "+"#destination_email").val(),
                    'name': $(clase_form+" "+"#name").val(),
                    'telephone': $(clase_form+" "+"#telephone").val(),
                    'email': $(clase_form+" "+"#email").val(),
                    'city': $(clase_form+" "+"#city").val(),
                    'address': $(clase_form+" "+"#address").val(),
                    'postal_code': $(clase_form+" "+"#cp").val(),
                    'formation': $(clase_form+" "+"#comments").val(),
                    'experience': $(clase_form+" "+"#experience").val(),
                    'aviability': $(clase_form+" "+"#aviability").val(),
                    'own_car':$(clase_form+" "+"#own_car").val(),
                    'vacant_posts': $(clase_form+" "+"#vacant_posts").val(),
                    'url_file_download': url_cv_download

                  },

                  function(data) {
                    alert($.i18n._("gracias_contacto"));
                    window.location.reload();
                    $(clase_form+" "+"#name").val("");
                    $(clase_form+" "+"#telephone").val("");
                    $(clase_form+" "+"#email").val("");
                    $(clase_form+" "+"#city").val("");
                    $(clase_form+" "+"#address").val("");
                    $(clase_form+" "+"#cp").val("");
                    $(clase_form+" "+"#own_car").val("");
                    $(clase_form+" "+"#vacant_posts").val("");
                    $(clase_form+" "+"#comments").val("")

                  }
                );

            }


          }
          else{
              $("label.error").css("display", "none");
          }

          $(clase_form + " " + ".btn-corporate").css("display","");


    }



    $("#work-button").click(function(){
        //id form,class form
      envia_form_work('#work-with-us','.form_unete');

    });


});