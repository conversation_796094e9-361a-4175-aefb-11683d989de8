{% if line_banners_list %}

    {% for banner_line in line_banners_list %}


        {% if banner_line %}


            <div id="wrapper-main-banners" class="class-zoom-{{ forloop.counter }}">


                {% for banner in banner_line %}

                <div class="arrow-wrapper">

                    <div id="{% cycle 'triangulo-right' 'triangulo-left' %}" style="border-{% cycle 'left' 'right' %}: 43px solid {% if banner.title|safe|upper == "BACKGROUND" %}transparent {% else %}#{{ banner.title }}{% endif %}"></div>

                <div class="wrapper-for-hide">

                    <a class="banner-link" href="{% if banner.linkUrl == 'None' %}/{% else %}{{ banner.linkUrl }}{% endif %}" {% if 'http' in banner.linkUrl|safe %}target="_blank"{% endif %}>

                        {% if banner.title|safe|upper == "BACKGROUND" %}
                            <div id="js_target-{{ forloop.counter }}" class="wrapper-big-banner square-background square-{{ forloop.counter }}" style="background: url('{{ banner.servingUrl|safe }}=s1200') top center no-repeat;background-size: cover;">
                        {% else %}
                            <div id="js_target-{{ forloop.counter }}" class="wrapper-big-banner square-icono square-{{ forloop.counter }}" style="background-color:#{{ banner.title|safe }}">
                        {% endif %}





                        <div class="container-text-banner"
                             style="position: absolute;top: 39%;bottom: 0px;vertical-align: middle;left: 0px;right: 0px;text-align: center;">

                            {% if not banner.title|safe|upper == "BACKGROUND" %}
                                <img class="icono-bigbanner" src="{{ banner.servingUrl|safe }}">
                                <span class="span-underline"></span>
                                <h4 class="big-banner-title">{{ banner.description|safe }}</h4>
                                <span class="span-underline"></span>

                            {% else %}
                                <h4 class="big-banner-title">{{ banner.description|safe }}</h4>

                            {% endif %}


                        </div>



                        </div>


                    </a>

                </div>
                </div>
                {% endfor %}


            </div>



        {% endif %}

    {% endfor %}

{% endif %}