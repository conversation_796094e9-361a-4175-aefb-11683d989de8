<div class="minigallery_wrapper {% if effect_sass %} effects_sass" sass_effect="{{ effect_sass }}{% endif %}">
    <div class="minigallery_content owl-carousel">
        {% for x in minigallery %}
            <a class="slider_element {% if x.description %}minigallery_desc{% endif %}" {% if x.linkUrl %}href="{{ x.linkUrl|safe }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% else %}href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[minigallery]"{% endif %}>
                <img class="center_image" src="{{ x.servingUrl|safe }}" alt="{{ x.name|safe }}" />
                {% if x.description %}
                    <span>
                        {% if x.title %}<i class="fa icon-booking {{ x.title|safe }}"></i>{% endif %}
                        {{ x.description|safe }}
                    </span>
                {% endif %}
            </a>
        {% endfor %}

    </div>
</div>
<script>
    $(window).load(function () {
        owl_params = {
            loop: true,
            nav: true,
            dots: false,
            items: 5,
            navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
            margin: 0,
            autoplay: true
        };

        $(".minigallery_content").owlCarousel(owl_params);
    })
</script>