<header>
    <div class="overlay_slider"></div>
    <div id="wrapper-header" class="container12">


        <div id="top-sections" class="column4">

            <span>{{ T_reservas|upper }}: {{ phones.0|safe }}</span>
            {% for section in top_sections %}
                <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                    <span>{{ section.title|safe }}</span>
                </a>
                {% if not forloop.last %}<a>|</a>{% endif %}
            {% endfor %}
                <a>|</a> <a href="{{ location_html.friendlyUrlInternational|safe }}">
                <span>{{ location_html.subtitle|safe }}</span>
        </a>

        </div>


        <div id="logoDiv" class="column4">
            <a href="{{ host|safe }}/">
                <img src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>


        <div class="column4" id="lang-wrapper">


            <div id="lang">
                <span id="selected-language">{{ language_selected }}</span>
                <span class="arrow"></span>

                <ul id="language-selector-options">
                    {% for key, language in language_codes.items %}
                        <li class="language-option-flag">
                            <a hreflang="{{ key }}" href="{% if key == 'es' %}/{% else %}{{ hostWithoutLanguage }}/{{ key }}/{% endif %}">{{ language }}</a>
                        </li>
                    {% endfor %}
                </ul>
            </div>

            <div class="weather"></div>

            <div id="social">
                {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>


        </div>

    </div>


    <nav id="main_menu">
        <div id="mainMenuDiv" class="container12">
            {% include "main_div.html" %}
        </div>
    </nav>


    <section id="horizontal_booking_container">
        <section id="booking-horizontal" class="container12">
            <div class="boking_widget_inline">
                {{ booking_engine }}
            </div>
            
            {% if forfait_section %}
                <a href="{{host|safe}}/{{seoLinkString}}{{forfait_section.friendlyUrlInternational}}" class="forfait_link">
                    <span>{{ forfait_section.forfait_text|safe }}</span>
                </a>
            {% endif %}

            {% for link in under_booking %}
                <a href="{{ link.linkUrl }}" class="forfait_link" {% if 'http:' in link.linkUrl %}target="_blank"{% endif %} style="margin-left: 10px;{% if link.altText %}background-color:{{ link.altText|safe }};{% endif %}">
                    <span>{{ link.title|safe }}</span>
                </a>
            {% endfor %}


        </section>

        {{ cartela_home|safe }}


    </section>


    {#Hide Menu#}

    <div class="hidden-menu">

        <div class="container12">
            <div id="logoDiv" class="column4">
                <a href="{{ host|safe }}/">
                    <img src="{{ hidden_logo.0|safe }}" alt="{{ hotel_name|safe }}"
                         title="{{ hotel_name|safe }}"/>
                </a>
            </div>


            <div class="lang-booking">

                <div id="lang" class="lang2">
                    <span id="selected-language" class="selected-language2">{{ language_selected }}</span>
                    <span class="arrow"></span>

                    <ul id="language-selector-options" class="language-selector-options2">
                        {% for key, language in language_codes.items %}
                            <li class="language-option-flag">
                                <a hreflang="{{ key }}"
                                   href="{{ hostWithoutLanguage|safe }}/{% if key != default_language_code %}{{ key }}/{% endif %}">{{ language }}</a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="booking-button">
                    <a href="#data" class="booking_top_button button-promotion">
                        {{ T_reservar }}
                    </a>
                </div>
            </div>


            <div id="main-sections">
                <ul id="main-sections-inner" class="container">
                    {% for section in main_sections %}
                        <div class="main-section-div-wrapper"
                             {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                            {% if section.subsections %}

                                <a>{{ section.title|safe }}</a>
                            {% else %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrl }}">
                                    {{ section.title|safe }}
                                </a>
                            {% endif %}

                            {% if section.subsections %}
                                <ul>
                                    {% for subsection in section.subsections %}
                                        <li class="main-section-subsection {{ subsection.title|lower }}">
                                            <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrl }}"
                                               {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                                {{ subsection.title|safe }}
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    {% endfor %}
                </ul>
            </div>


        </div>

    </div>

    {#Fin de Hide Menu#}


</header>