@font-face {
  font-family: 'FontAwesome';
  src: url("/static_1/fonts/fontawesome/fontawesome/fontawesome-webfont.eot?v=4.7.0");
  src: url("/static_1/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("/static_1/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("/static_1/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("/static_1/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-3x {
  font-size: 3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-4x {
  font-size: 4em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-5x {
  font-size: 5em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ul > li {
  position: relative;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-li.fa-lg {
  left: -1.85714286em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.fa-pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-right {
  float: right;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.pull-left {
  float: left;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-left {
  margin-right: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa.pull-right {
  margin-left: .3em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-1x {
  line-height: inherit;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-2x {
  font-size: 2em;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inverse {
  color: #fff;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glass:before {
  content: "\f000";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-music:before {
  content: "\f001";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search:before {
  content: "\f002";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-o:before {
  content: "\f003";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart:before {
  content: "\f004";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star:before {
  content: "\f005";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-o:before {
  content: "\f006";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user:before {
  content: "\f007";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-film:before {
  content: "\f008";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-large:before {
  content: "\f009";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th:before {
  content: "\f00a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-th-list:before {
  content: "\f00b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check:before {
  content: "\f00c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-plus:before {
  content: "\f00e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-search-minus:before {
  content: "\f010";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-power-off:before {
  content: "\f011";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signal:before {
  content: "\f012";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash-o:before {
  content: "\f014";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-home:before {
  content: "\f015";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-o:before {
  content: "\f016";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clock-o:before {
  content: "\f017";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-road:before {
  content: "\f018";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-download:before {
  content: "\f019";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-inbox:before {
  content: "\f01c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle-o:before {
  content: "\f01d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-refresh:before {
  content: "\f021";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-alt:before {
  content: "\f022";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lock:before {
  content: "\f023";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag:before {
  content: "\f024";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-headphones:before {
  content: "\f025";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-off:before {
  content: "\f026";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-down:before {
  content: "\f027";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-up:before {
  content: "\f028";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qrcode:before {
  content: "\f029";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-barcode:before {
  content: "\f02a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tag:before {
  content: "\f02b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tags:before {
  content: "\f02c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-book:before {
  content: "\f02d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark:before {
  content: "\f02e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-print:before {
  content: "\f02f";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera:before {
  content: "\f030";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-font:before {
  content: "\f031";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bold:before {
  content: "\f032";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-italic:before {
  content: "\f033";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-height:before {
  content: "\f034";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-text-width:before {
  content: "\f035";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-left:before {
  content: "\f036";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-center:before {
  content: "\f037";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-right:before {
  content: "\f038";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-align-justify:before {
  content: "\f039";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list:before {
  content: "\f03a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-indent:before {
  content: "\f03c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-video-camera:before {
  content: "\f03d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil:before {
  content: "\f040";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-marker:before {
  content: "\f041";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adjust:before {
  content: "\f042";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tint:before {
  content: "\f043";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square-o:before {
  content: "\f045";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square-o:before {
  content: "\f046";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows:before {
  content: "\f047";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-backward:before {
  content: "\f048";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-backward:before {
  content: "\f049";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-backward:before {
  content: "\f04a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play:before {
  content: "\f04b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause:before {
  content: "\f04c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop:before {
  content: "\f04d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forward:before {
  content: "\f04e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fast-forward:before {
  content: "\f050";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-step-forward:before {
  content: "\f051";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eject:before {
  content: "\f052";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-left:before {
  content: "\f053";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-right:before {
  content: "\f054";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-circle:before {
  content: "\f055";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-circle:before {
  content: "\f056";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle:before {
  content: "\f057";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle:before {
  content: "\f058";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle:before {
  content: "\f059";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info-circle:before {
  content: "\f05a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crosshairs:before {
  content: "\f05b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-circle-o:before {
  content: "\f05c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-circle-o:before {
  content: "\f05d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ban:before {
  content: "\f05e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-left:before {
  content: "\f060";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-right:before {
  content: "\f061";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-up:before {
  content: "\f062";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-down:before {
  content: "\f063";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expand:before {
  content: "\f065";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compress:before {
  content: "\f066";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus:before {
  content: "\f067";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus:before {
  content: "\f068";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asterisk:before {
  content: "\f069";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation-circle:before {
  content: "\f06a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gift:before {
  content: "\f06b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leaf:before {
  content: "\f06c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire:before {
  content: "\f06d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye:before {
  content: "\f06e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eye-slash:before {
  content: "\f070";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plane:before {
  content: "\f072";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar:before {
  content: "\f073";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-random:before {
  content: "\f074";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment:before {
  content: "\f075";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magnet:before {
  content: "\f076";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-up:before {
  content: "\f077";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-down:before {
  content: "\f078";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-retweet:before {
  content: "\f079";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-cart:before {
  content: "\f07a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder:before {
  content: "\f07b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open:before {
  content: "\f07c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-v:before {
  content: "\f07d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-h:before {
  content: "\f07e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter-square:before {
  content: "\f081";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-square:before {
  content: "\f082";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-camera-retro:before {
  content: "\f083";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-key:before {
  content: "\f084";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments:before {
  content: "\f086";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-up:before {
  content: "\f087";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-o-down:before {
  content: "\f088";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half:before {
  content: "\f089";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heart-o:before {
  content: "\f08a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-out:before {
  content: "\f08b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin-square:before {
  content: "\f08c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumb-tack:before {
  content: "\f08d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link:before {
  content: "\f08e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sign-in:before {
  content: "\f090";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trophy:before {
  content: "\f091";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-square:before {
  content: "\f092";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-upload:before {
  content: "\f093";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lemon-o:before {
  content: "\f094";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone:before {
  content: "\f095";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square-o:before {
  content: "\f096";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bookmark-o:before {
  content: "\f097";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-phone-square:before {
  content: "\f098";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitter:before {
  content: "\f099";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github:before {
  content: "\f09b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock:before {
  content: "\f09c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card:before {
  content: "\f09d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hdd-o:before {
  content: "\f0a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullhorn:before {
  content: "\f0a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell:before {
  content: "\f0f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-certificate:before {
  content: "\f0a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-right:before {
  content: "\f0a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-left:before {
  content: "\f0a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-up:before {
  content: "\f0a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-o-down:before {
  content: "\f0a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-left:before {
  content: "\f0a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-right:before {
  content: "\f0a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-up:before {
  content: "\f0aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-down:before {
  content: "\f0ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-globe:before {
  content: "\f0ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wrench:before {
  content: "\f0ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tasks:before {
  content: "\f0ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-filter:before {
  content: "\f0b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-briefcase:before {
  content: "\f0b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrows-alt:before {
  content: "\f0b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud:before {
  content: "\f0c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flask:before {
  content: "\f0c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paperclip:before {
  content: "\f0c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-square:before {
  content: "\f0c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ul:before {
  content: "\f0ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-list-ol:before {
  content: "\f0cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-strikethrough:before {
  content: "\f0cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-underline:before {
  content: "\f0cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-table:before {
  content: "\f0ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-magic:before {
  content: "\f0d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-truck:before {
  content: "\f0d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest:before {
  content: "\f0d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-square:before {
  content: "\f0d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-square:before {
  content: "\f0d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus:before {
  content: "\f0d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-money:before {
  content: "\f0d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-down:before {
  content: "\f0d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-up:before {
  content: "\f0d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-left:before {
  content: "\f0d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-caret-right:before {
  content: "\f0da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-columns:before {
  content: "\f0db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope:before {
  content: "\f0e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linkedin:before {
  content: "\f0e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comment-o:before {
  content: "\f0e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-comments-o:before {
  content: "\f0e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sitemap:before {
  content: "\f0e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-umbrella:before {
  content: "\f0e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lightbulb-o:before {
  content: "\f0eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exchange:before {
  content: "\f0ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-download:before {
  content: "\f0ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cloud-upload:before {
  content: "\f0ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-md:before {
  content: "\f0f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stethoscope:before {
  content: "\f0f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-suitcase:before {
  content: "\f0f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-o:before {
  content: "\f0a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-coffee:before {
  content: "\f0f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cutlery:before {
  content: "\f0f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text-o:before {
  content: "\f0f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building-o:before {
  content: "\f0f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hospital-o:before {
  content: "\f0f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ambulance:before {
  content: "\f0f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medkit:before {
  content: "\f0fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fighter-jet:before {
  content: "\f0fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-beer:before {
  content: "\f0fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-h-square:before {
  content: "\f0fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square:before {
  content: "\f0fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-left:before {
  content: "\f100";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-right:before {
  content: "\f101";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-up:before {
  content: "\f102";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-double-down:before {
  content: "\f103";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-left:before {
  content: "\f104";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-right:before {
  content: "\f105";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-up:before {
  content: "\f106";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angle-down:before {
  content: "\f107";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-desktop:before {
  content: "\f108";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-laptop:before {
  content: "\f109";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tablet:before {
  content: "\f10a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o:before {
  content: "\f10c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-left:before {
  content: "\f10d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quote-right:before {
  content: "\f10e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spinner:before {
  content: "\f110";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle:before {
  content: "\f111";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-github-alt:before {
  content: "\f113";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-o:before {
  content: "\f114";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-folder-open-o:before {
  content: "\f115";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-smile-o:before {
  content: "\f118";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-frown-o:before {
  content: "\f119";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meh-o:before {
  content: "\f11a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gamepad:before {
  content: "\f11b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-keyboard-o:before {
  content: "\f11c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-o:before {
  content: "\f11d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flag-checkered:before {
  content: "\f11e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-terminal:before {
  content: "\f120";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code:before {
  content: "\f121";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-location-arrow:before {
  content: "\f124";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-crop:before {
  content: "\f125";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-code-fork:before {
  content: "\f126";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question:before {
  content: "\f128";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-info:before {
  content: "\f129";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-exclamation:before {
  content: "\f12a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superscript:before {
  content: "\f12b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subscript:before {
  content: "\f12c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eraser:before {
  content: "\f12d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-puzzle-piece:before {
  content: "\f12e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone:before {
  content: "\f130";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microphone-slash:before {
  content: "\f131";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shield:before {
  content: "\f132";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-o:before {
  content: "\f133";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fire-extinguisher:before {
  content: "\f134";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rocket:before {
  content: "\f135";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-maxcdn:before {
  content: "\f136";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-left:before {
  content: "\f137";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-right:before {
  content: "\f138";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-up:before {
  content: "\f139";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chevron-circle-down:before {
  content: "\f13a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-html5:before {
  content: "\f13b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-css3:before {
  content: "\f13c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-anchor:before {
  content: "\f13d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-unlock-alt:before {
  content: "\f13e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bullseye:before {
  content: "\f140";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-h:before {
  content: "\f141";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ellipsis-v:before {
  content: "\f142";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rss-square:before {
  content: "\f143";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-play-circle:before {
  content: "\f144";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ticket:before {
  content: "\f145";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square:before {
  content: "\f146";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-minus-square-o:before {
  content: "\f147";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-up:before {
  content: "\f148";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-level-down:before {
  content: "\f149";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-check-square:before {
  content: "\f14a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pencil-square:before {
  content: "\f14b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-external-link-square:before {
  content: "\f14c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-square:before {
  content: "\f14d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-compass:before {
  content: "\f14e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gbp:before {
  content: "\f154";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-won:before, .fa-krw:before {
  content: "\f159";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file:before {
  content: "\f15b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-text:before {
  content: "\f15c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-asc:before {
  content: "\f15d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-alpha-desc:before {
  content: "\f15e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-asc:before {
  content: "\f160";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-amount-desc:before {
  content: "\f161";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-asc:before {
  content: "\f162";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sort-numeric-desc:before {
  content: "\f163";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-up:before {
  content: "\f164";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thumbs-down:before {
  content: "\f165";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-square:before {
  content: "\f166";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube:before {
  content: "\f167";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing:before {
  content: "\f168";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-xing-square:before {
  content: "\f169";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-youtube-play:before {
  content: "\f16a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dropbox:before {
  content: "\f16b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-overflow:before {
  content: "\f16c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-instagram:before {
  content: "\f16d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-flickr:before {
  content: "\f16e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-adn:before {
  content: "\f170";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket:before {
  content: "\f171";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bitbucket-square:before {
  content: "\f172";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr:before {
  content: "\f173";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tumblr-square:before {
  content: "\f174";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-down:before {
  content: "\f175";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-up:before {
  content: "\f176";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-left:before {
  content: "\f177";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-long-arrow-right:before {
  content: "\f178";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-apple:before {
  content: "\f179";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-windows:before {
  content: "\f17a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-android:before {
  content: "\f17b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linux:before {
  content: "\f17c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dribbble:before {
  content: "\f17d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skype:before {
  content: "\f17e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-foursquare:before {
  content: "\f180";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trello:before {
  content: "\f181";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-female:before {
  content: "\f182";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-male:before {
  content: "\f183";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sun-o:before {
  content: "\f185";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-moon-o:before {
  content: "\f186";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-archive:before {
  content: "\f187";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bug:before {
  content: "\f188";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vk:before {
  content: "\f189";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-weibo:before {
  content: "\f18a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-renren:before {
  content: "\f18b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pagelines:before {
  content: "\f18c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stack-exchange:before {
  content: "\f18d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-arrow-circle-o-left:before {
  content: "\f190";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dot-circle-o:before {
  content: "\f192";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair:before {
  content: "\f193";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo-square:before {
  content: "\f194";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plus-square-o:before {
  content: "\f196";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-space-shuttle:before {
  content: "\f197";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slack:before {
  content: "\f198";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-square:before {
  content: "\f199";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wordpress:before {
  content: "\f19a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-openid:before {
  content: "\f19b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yahoo:before {
  content: "\f19e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google:before {
  content: "\f1a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit:before {
  content: "\f1a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-square:before {
  content: "\f1a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stumbleupon:before {
  content: "\f1a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-delicious:before {
  content: "\f1a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-digg:before {
  content: "\f1a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-pp:before {
  content: "\f1a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper-alt:before {
  content: "\f1a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drupal:before {
  content: "\f1a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-joomla:before {
  content: "\f1aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-language:before {
  content: "\f1ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fax:before {
  content: "\f1ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-building:before {
  content: "\f1ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-child:before {
  content: "\f1ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paw:before {
  content: "\f1b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spoon:before {
  content: "\f1b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cube:before {
  content: "\f1b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cubes:before {
  content: "\f1b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance:before {
  content: "\f1b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-behance-square:before {
  content: "\f1b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam:before {
  content: "\f1b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-steam-square:before {
  content: "\f1b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-recycle:before {
  content: "\f1b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tree:before {
  content: "\f1bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-spotify:before {
  content: "\f1bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deviantart:before {
  content: "\f1bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soundcloud:before {
  content: "\f1be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-database:before {
  content: "\f1c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-pdf-o:before {
  content: "\f1c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-word-o:before {
  content: "\f1c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-excel-o:before {
  content: "\f1c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-file-code-o:before {
  content: "\f1c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vine:before {
  content: "\f1ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codepen:before {
  content: "\f1cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-jsfiddle:before {
  content: "\f1cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-o-notch:before {
  content: "\f1ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git-square:before {
  content: "\f1d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-git:before {
  content: "\f1d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tencent-weibo:before {
  content: "\f1d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-qq:before {
  content: "\f1d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-history:before {
  content: "\f1da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-circle-thin:before {
  content: "\f1db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-header:before {
  content: "\f1dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paragraph:before {
  content: "\f1dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sliders:before {
  content: "\f1de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt:before {
  content: "\f1e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-share-alt-square:before {
  content: "\f1e1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bomb:before {
  content: "\f1e2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tty:before {
  content: "\f1e4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-binoculars:before {
  content: "\f1e5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-plug:before {
  content: "\f1e6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-slideshare:before {
  content: "\f1e7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-twitch:before {
  content: "\f1e8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yelp:before {
  content: "\f1e9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-newspaper-o:before {
  content: "\f1ea";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wifi:before {
  content: "\f1eb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calculator:before {
  content: "\f1ec";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paypal:before {
  content: "\f1ed";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-wallet:before {
  content: "\f1ee";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-visa:before {
  content: "\f1f0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-mastercard:before {
  content: "\f1f1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-discover:before {
  content: "\f1f2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-amex:before {
  content: "\f1f3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-paypal:before {
  content: "\f1f4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-stripe:before {
  content: "\f1f5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash:before {
  content: "\f1f6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bell-slash-o:before {
  content: "\f1f7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trash:before {
  content: "\f1f8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-copyright:before {
  content: "\f1f9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-at:before {
  content: "\f1fa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eyedropper:before {
  content: "\f1fb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-paint-brush:before {
  content: "\f1fc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-birthday-cake:before {
  content: "\f1fd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-area-chart:before {
  content: "\f1fe";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pie-chart:before {
  content: "\f200";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-line-chart:before {
  content: "\f201";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm:before {
  content: "\f202";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-lastfm-square:before {
  content: "\f203";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-off:before {
  content: "\f204";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-toggle-on:before {
  content: "\f205";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bicycle:before {
  content: "\f206";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bus:before {
  content: "\f207";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ioxhost:before {
  content: "\f208";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-angellist:before {
  content: "\f209";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc:before {
  content: "\f20a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meanpath:before {
  content: "\f20c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-buysellads:before {
  content: "\f20d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-connectdevelop:before {
  content: "\f20e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-dashcube:before {
  content: "\f210";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-forumbee:before {
  content: "\f211";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-leanpub:before {
  content: "\f212";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sellsy:before {
  content: "\f213";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shirtsinbulk:before {
  content: "\f214";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-simplybuilt:before {
  content: "\f215";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-skyatlas:before {
  content: "\f216";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-plus:before {
  content: "\f217";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cart-arrow-down:before {
  content: "\f218";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-diamond:before {
  content: "\f219";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ship:before {
  content: "\f21a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-secret:before {
  content: "\f21b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-motorcycle:before {
  content: "\f21c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-street-view:before {
  content: "\f21d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-heartbeat:before {
  content: "\f21e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus:before {
  content: "\f221";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars:before {
  content: "\f222";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mercury:before {
  content: "\f223";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-transgender-alt:before {
  content: "\f225";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-double:before {
  content: "\f226";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-double:before {
  content: "\f227";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-venus-mars:before {
  content: "\f228";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke:before {
  content: "\f229";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-v:before {
  content: "\f22a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mars-stroke-h:before {
  content: "\f22b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-neuter:before {
  content: "\f22c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-genderless:before {
  content: "\f22d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-facebook-official:before {
  content: "\f230";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pinterest-p:before {
  content: "\f231";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-whatsapp:before {
  content: "\f232";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-server:before {
  content: "\f233";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-plus:before {
  content: "\f234";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-times:before {
  content: "\f235";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viacoin:before {
  content: "\f237";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-train:before {
  content: "\f238";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-subway:before {
  content: "\f239";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-medium:before {
  content: "\f23a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-optin-monster:before {
  content: "\f23c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opencart:before {
  content: "\f23d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-expeditedssl:before {
  content: "\f23e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mouse-pointer:before {
  content: "\f245";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-i-cursor:before {
  content: "\f246";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-group:before {
  content: "\f247";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-object-ungroup:before {
  content: "\f248";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note:before {
  content: "\f249";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-sticky-note-o:before {
  content: "\f24a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-jcb:before {
  content: "\f24b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-cc-diners-club:before {
  content: "\f24c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-clone:before {
  content: "\f24d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-balance-scale:before {
  content: "\f24e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-o:before {
  content: "\f250";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hourglass:before {
  content: "\f254";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-scissors-o:before {
  content: "\f257";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-lizard-o:before {
  content: "\f258";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-spock-o:before {
  content: "\f259";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-pointer-o:before {
  content: "\f25a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hand-peace-o:before {
  content: "\f25b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-trademark:before {
  content: "\f25c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-registered:before {
  content: "\f25d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-creative-commons:before {
  content: "\f25e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg:before {
  content: "\f260";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gg-circle:before {
  content: "\f261";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tripadvisor:before {
  content: "\f262";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki:before {
  content: "\f263";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-odnoklassniki-square:before {
  content: "\f264";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-get-pocket:before {
  content: "\f265";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wikipedia-w:before {
  content: "\f266";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-safari:before {
  content: "\f267";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-chrome:before {
  content: "\f268";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-firefox:before {
  content: "\f269";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-opera:before {
  content: "\f26a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-internet-explorer:before {
  content: "\f26b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-contao:before {
  content: "\f26d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-500px:before {
  content: "\f26e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-amazon:before {
  content: "\f270";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-plus-o:before {
  content: "\f271";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-minus-o:before {
  content: "\f272";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-times-o:before {
  content: "\f273";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-calendar-check-o:before {
  content: "\f274";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-industry:before {
  content: "\f275";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-pin:before {
  content: "\f276";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-signs:before {
  content: "\f277";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map-o:before {
  content: "\f278";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-map:before {
  content: "\f279";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting:before {
  content: "\f27a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-commenting-o:before {
  content: "\f27b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-houzz:before {
  content: "\f27c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vimeo:before {
  content: "\f27d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-black-tie:before {
  content: "\f27e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fonticons:before {
  content: "\f280";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-reddit-alien:before {
  content: "\f281";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-edge:before {
  content: "\f282";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-credit-card-alt:before {
  content: "\f283";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-codiepie:before {
  content: "\f284";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-modx:before {
  content: "\f285";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fort-awesome:before {
  content: "\f286";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-usb:before {
  content: "\f287";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-product-hunt:before {
  content: "\f288";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-mixcloud:before {
  content: "\f289";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-scribd:before {
  content: "\f28a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle:before {
  content: "\f28b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pause-circle-o:before {
  content: "\f28c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle:before {
  content: "\f28d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-stop-circle-o:before {
  content: "\f28e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-bag:before {
  content: "\f290";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shopping-basket:before {
  content: "\f291";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-hashtag:before {
  content: "\f292";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth:before {
  content: "\f293";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bluetooth-b:before {
  content: "\f294";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-percent:before {
  content: "\f295";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-gitlab:before {
  content: "\f296";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpbeginner:before {
  content: "\f297";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpforms:before {
  content: "\f298";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envira:before {
  content: "\f299";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-universal-access:before {
  content: "\f29a";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wheelchair-alt:before {
  content: "\f29b";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-question-circle-o:before {
  content: "\f29c";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-blind:before {
  content: "\f29d";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-audio-description:before {
  content: "\f29e";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-volume-control-phone:before {
  content: "\f2a0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-braille:before {
  content: "\f2a1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide:before {
  content: "\f2a5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-glide-g:before {
  content: "\f2a6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-low-vision:before {
  content: "\f2a8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo:before {
  content: "\f2a9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-viadeo-square:before {
  content: "\f2aa";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat:before {
  content: "\f2ab";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-ghost:before {
  content: "\f2ac";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snapchat-square:before {
  content: "\f2ad";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-pied-piper:before {
  content: "\f2ae";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-first-order:before {
  content: "\f2b0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-yoast:before {
  content: "\f2b1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-themeisle:before {
  content: "\f2b2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-handshake-o:before {
  content: "\f2b5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open:before {
  content: "\f2b6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-envelope-open-o:before {
  content: "\f2b7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-linode:before {
  content: "\f2b8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book:before {
  content: "\f2b9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-address-book-o:before {
  content: "\f2ba";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle:before {
  content: "\f2bd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-circle-o:before {
  content: "\f2be";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-user-o:before {
  content: "\f2c0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-id-badge:before {
  content: "\f2c1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-quora:before {
  content: "\f2c4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-free-code-camp:before {
  content: "\f2c5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-telegram:before {
  content: "\f2c6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-shower:before {
  content: "\f2cc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-podcast:before {
  content: "\f2ce";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-maximize:before {
  content: "\f2d0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-minimize:before {
  content: "\f2d1";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-window-restore:before {
  content: "\f2d2";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-bandcamp:before {
  content: "\f2d5";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-grav:before {
  content: "\f2d6";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-etsy:before {
  content: "\f2d7";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-imdb:before {
  content: "\f2d8";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-ravelry:before {
  content: "\f2d9";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-eercast:before {
  content: "\f2da";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-microchip:before {
  content: "\f2db";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-snowflake-o:before {
  content: "\f2dc";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-superpowers:before {
  content: "\f2dd";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-wpexplorer:before {
  content: "\f2de";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.fa-meetup:before {
  content: "\f2e0";
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* line 7, ../../../../sass/plugins/_fontawesomemin.scss */
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 David Deutsch
 * Licensed under  ()
 */
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel, .owl-carousel .owl-item {
  -webkit-tap-highlight-color: transparent;
  position: relative;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel {
  display: none;
  width: 100%;
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage {
  position: relative;
  -ms-touch-action: pan-Y;
  -moz-backface-visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage:after {
  content: ".";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-stage-outer {
  position: relative;
  overflow: hidden;
  -webkit-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item, .owl-carousel .owl-wrapper {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item {
  min-height: 1px;
  float: left;
  -webkit-backface-visibility: hidden;
  -webkit-touch-callout: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img {
  display: block;
  width: 100%;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.no-js .owl-carousel, .owl-carousel.owl-loaded {
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-dot, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
  cursor: pointer;
  cursor: hand;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-loading {
  opacity: 0;
  display: block;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-hidden {
  opacity: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-refresh .owl-item {
  visibility: hidden;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-drag .owl-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-grab {
  cursor: move;
  cursor: grab;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl {
  direction: rtl;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel.owl-rtl .owl-item {
  float: right;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-in {
  z-index: 0;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-animated-out {
  z-index: 1;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-height {
  transition: height .5s ease-in-out;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url(owl.video.play.png) no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform .1s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-play-icon:hover {
  -ms-transform: scale(1.3, 1.3);
  transform: scale(1.3, 1.3);
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-playing .owl-video-play-icon, .owl-carousel .owl-video-playing .owl-video-tn {
  display: none;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity .4s ease;
}

/* line 6, ../../../../sass/plugins/_owlcarousel.scss */
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}

/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #34667c;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #34667c url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #34667c;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #34667c;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #34667c;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #34667c;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

/* line 387, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 389, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 392, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide > * {
  padding: 0 !important;
  background-color: transparent;
}
/* line 396, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .fancybox-slide .calendar_popup_wrapper {
  background-color: transparent;
}
/* line 400, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .full_screen_engine .container_popup_booking {
  background-color: transparent !important;
  padding: 24px !important;
}
/* line 405, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  color: gray;
}
/* line 408, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 1, ../../../../sass/plugins/spiners/_default_line_loading.scss */
.fancybox-wrap.fancy-booking-search_v2 .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading {
  width: 50px;
  height: 50px;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  bottom: 0;
}
/* line 10, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:before {
  content: '';
  width: 50px;
  height: 5px;
  background: #000;
  opacity: 0.3;
  position: absolute;
  top: 59px;
  left: 0;
  border-radius: 50%;
  animation: box_shadow .5s linear infinite;
}
/* line 22, ../../../../sass/plugins/spiners/_box_jumping.scss */
.fancybox-wrap.fancy-booking-search_v2 .boxLoading:after {
  content: '';
  width: 50px;
  height: 50px;
  background: white;
  animation: box_animate .5s linear infinite;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 3px;
}
@keyframes box_animate {
  17% {
    border-bottom-right-radius: 3px;
  }
  25% {
    transform: translateY(9px) rotate(22.5deg);
  }
  50% {
    transform: translateY(18px) scale(1, 0.9) rotate(45deg);
    border-bottom-right-radius: 40px;
  }
  75% {
    transform: translateY(9px) rotate(67.5deg);
  }
  100% {
    transform: translateY(0) rotate(90deg);
  }
}
@keyframes box_shadow {
  0%, 100% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.2, 1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader {
  padding: 60px 0 70px;
}
/* line 3, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot {
  width: 24px;
  height: 24px;
  margin: 0 3px;
  background: #000;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}
/* line 11, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(1) {
  animation-delay: 0.1s;
}
/* line 14, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(2) {
  animation-delay: 0.2s;
}
/* line 17, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(3) {
  animation-delay: 0.3s;
}
/* line 20, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(4) {
  animation-delay: 0.4s;
}
/* line 23, ../../../../sass/plugins/spiners/_dots_loader.scss */
.fancybox-wrap.fancy-booking-search_v2 .dots_loader .dot:nth-child(5) {
  animation-delay: 0.5s;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/* line 1, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube {
  margin: 20px auto;
  width: 40px;
  height: 40px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
/* line 7, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube {
  float: left;
  width: 50%;
  height: 50%;
  position: relative;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
/* line 15, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk-cube:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
  animation: sk-foldCubeAngle 2.4s infinite linear both;
  -webkit-transform-origin: 100% 100%;
  -ms-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}
/* line 30, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2 {
  -webkit-transform: scale(1.1) rotateZ(90deg);
  transform: scale(1.1) rotateZ(90deg);
}
/* line 34, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3 {
  -webkit-transform: scale(1.1) rotateZ(270deg);
  transform: scale(1.1) rotateZ(270deg);
}
/* line 38, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4 {
  -webkit-transform: scale(1.1) rotateZ(180deg);
  transform: scale(1.1) rotateZ(180deg);
}
/* line 43, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube2:before {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
/* line 49, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube3:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}
/* line 55, ../../../../sass/plugins/spiners/_sk_folding_cube.scss */
.fancybox-wrap.fancy-booking-search_v2 .sk_folding_cube .sk_cube4:before {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}
@-webkit-keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
@keyframes sk-foldCubeAngle {
  0%, 10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%, 75% {
    -webkit-transform: perspective(140px) rotateX(0deg);
    transform: perspective(140px) rotateX(0deg);
    opacity: 1;
  }
  90%, 100% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}
/* line 1, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots {
  padding: 40px 0;
  text-align: center;
  animation: dots-rotate 2s linear infinite;
}
/* line 5, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  content: '';
  animation: dots-margin 2s linear infinite;
}
/* line 9, ../../../../sass/plugins/spiners/_rotating_dots.scss */
.fancybox-wrap.fancy-booking-search_v2 .rotating_dots:before, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots span, .fancybox-wrap.fancy-booking-search_v2 .rotating_dots:after {
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
  margin: 5px;
}
@keyframes dots-margin {
  0% {
    margin: 5px;
  }
  12% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  25% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  50% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  62% {
    margin: 0 30px;
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
  }
  75% {
    margin: 5px;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    margin: 5px;
  }
}
@keyframes dots-rotate {
  0% {
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  25% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* line 415, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 423, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
}
/* line 428, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 439, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 447, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 452, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 457, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 466, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 470, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 483, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 487, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 490, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #34667c;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #34667c url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/* line 1, ../../../../sass/gallerys/_gallery_1.scss */
.gallery_1 li {
  float: left;
}

/* line 5, ../../../../sass/gallerys/_gallery_1.scss */
.gallery_1 li .crop {
  width: 285px;
  height: 190px;
  overflow: hidden;
}

/* line 11, ../../../../sass/gallerys/_gallery_1.scss */
.gallery_1 li .crop img {
  max-width: none;
  min-height: 190px;
  min-width: 285px;
  width: 285px;
  -webkit-transform: scale(1, 1);
  -webkit-transition-duration: 500ms;
  -webkit-transition-timing-function: ease-out;
  -moz-transform: scale(1, 1);
  -moz-transition-duration: 500ms;
  -moz-transition-timing-function: ease-out;
  -ms-transform: scale(1, 1);
  -ms-transition-duration: 500ms;
  -ms-transition-timing-function: ease-out;
}

/* line 31, ../../../../sass/gallerys/_gallery_1.scss */
.gallery_1 li .crop img:hover {
  max-width: none;
  -webkit-transform: scale(1.1, 1.1);
  -webkit-transition-duration: 500ms;
  -webkit-transition-timing-function: ease-out;
  -moz-transform: scale(1.1, 1.1);
  -moz-transition-duration: 500ms;
  -moz-transition-timing-function: ease-out;
  -ms-transform: scale(1.1, 1.1);
  -ms-transition-duration: 500ms;
  -ms-transition-timing-function: ease-out;
}

/* line 4, ../sass/_location_and_contact.scss */
#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}
/* line 10, ../sass/_location_and_contact.scss */
#wrapper_content_contact .location-info, #wrapper_content_contact .form-contact {
  width: 520px !important;
}

/* line 16, ../sass/_location_and_contact.scss */
.location-info-and-form-wrapper h1,
.form-contact h2 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  color: #32398D;
  padding-bottom: 3px;
}

/* line 30, ../sass/_location_and_contact.scss */
.location-info h1 {
  padding-bottom: 0px !important;
}

/* line 36, ../sass/_location_and_contact.scss */
.location-info-and-form-wrapper p, .location-info-and-form-wrapper strong {
  color: dimgrey;
}

/* line 42, ../sass/_location_and_contact.scss */
.location-info p {
  margin-bottom: 10px;
  font-weight: 300;
}
/* line 46, ../sass/_location_and_contact.scss */
.location-info strong {
  font-weight: bold;
}

/* line 51, ../sass/_location_and_contact.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 55, ../sass/_location_and_contact.scss */
.iframe-google-maps-wrapper {
  margin: 20px;
  text-align: center;
}
/* line 59, ../sass/_location_and_contact.scss */
.iframe-google-maps-wrapper iframe {
  margin-bottom: 25px;
}

/* line 66, ../sass/_location_and_contact.scss */
.form-contact #title {
  display: none !important;
}

/* line 70, ../sass/_location_and_contact.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 74, ../sass/_location_and_contact.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 78, ../sass/_location_and_contact.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
  text-color: dimgrey !important;
}

/* line 86, ../sass/_location_and_contact.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;
  font-size: 16px;
}

/* line 94, ../sass/_location_and_contact.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #d1d1d1;
  color: dimgrey;
  padding-left: 3px;
  font-size: 16px;
}

/* line 106, ../sass/_location_and_contact.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #a0a0a0;
  color: dimgrey;
  text-align: center;
  padding-left: 3px;
}

/* line 118, ../sass/_location_and_contact.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

/* line 123, ../sass/_location_and_contact.scss */
.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #32398D !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

/* line 139, ../sass/_location_and_contact.scss */
div#wrapper_content {
  margin: 50px auto 0px;
}

/* line 143, ../sass/_location_and_contact.scss */
.location-info {
  color: black;
  font-size: 14px;
  font-weight: lighter;
  line-height: 28px;
}

/* line 150, ../sass/_location_and_contact.scss */
.form-contact #contact-button:hover {
  background: #765a39 !important;
}

/* line 154, ../sass/_location_and_contact.scss */
.location-info-and-form-wrapper h1,
.form-contact h2 {
  font-family: nexabold;
}

/* line 159, ../sass/_location_and_contact.scss */
.form-contact #contact .contInput textarea {
  background-color: #D1D1D1;
  color: dimgrey;
  text-align: left;
  font-size: 16px;
}

/* line 166, ../sass/_location_and_contact.scss */
.form-contact .check_privacy {
  width: auto !important;
  margin-left: 0px !important;
  margin-right: 10px !important;
}
/* line 170, ../sass/_location_and_contact.scss */
.form-contact .check_privacy + .title {
  text-decoration: underline !important;
}
/* line 172, ../sass/_location_and_contact.scss */
.form-contact .check_privacy + .title a {
  color: dimgrey !important;
  font-size: 15px;
}

/* line 1, ../sass/_room_type_selector.scss */
.roomtype_selector {
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  -moz-box-shadow: #414141 1px 1px 7px;
  -ms-box-shadow: #414141 1px 1px 7px;
  -o-box-shadow: #414141 1px 1px 7px;
  -webkit-box-shadow: #414141 1px 1px 7px;
  box-shadow: #414141 1px 1px 7px;
  position: absolute;
  padding: 10px;
  padding-bottom: 0;
  display: none;
  width: 355px;
  left: 290px;
  top: 80px;
  background: white;
  z-index: 1000;
}
/* line 15, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_inner {
  position: relative;
}
/* line 19, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_inner .close_roomtype_selector {
  -moz-border-radius-topright: 5px;
  -ms-border-top-right-radius: 5px;
  -o-border-top-right-radius: 5px;
  -webkit-border-top-right-radius: 5px;
  border-top-right-radius: 5px;
  -moz-border-radius-bottomright: 5px;
  -ms-border-bottom-right-radius: 5px;
  -o-border-bottom-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  border-bottom-right-radius: 5px;
  position: absolute;
  top: -10px;
  right: -10px;
  background: #32398D url(/static_1/images/booking/flecha_motor_izq.png) no-repeat center center;
  height: 156px;
  width: 20px;
}
/* line 31, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option {
  width: 168px;
  height: 146px;
  float: left;
  cursor: pointer;
}
/* line 37, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option.right {
  margin-left: 10px;
}
/* line 41, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option.class-Habitacion {
  background: url(/img/feni2/room_selectors/motor_hotel.jpg) no-repeat top center;
}
/* line 48, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option.class-Apartamento {
  background: url(/img/feni2/room_selectors/motor_apart.jpg) no-repeat top center;
}
/* line 57, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option_name h3 {
  font-family: nexabold;
  margin-top: 89px;
  background: #e6e6e6;
  color: #32398D;
  line-height: 35px;
  text-transform: uppercase;
  padding-left: 37px;
}
/* line 66, ../sass/_room_type_selector.scss */
.roomtype_selector .roomtype_selector_option_name h3 span {
  font-family: nexaregular;
}
/* line 72, ../sass/_room_type_selector.scss */
.roomtype_selector .title_selector {
  position: relative;
  width: 101px;
  padding-right: 46px !important;
  top: 72px;
  left: 6px;
  height: 60px;
  background-color: black;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#000, endColorstr=#000);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
  background: rgba(0, 0, 0, 0.3);
  padding-left: 10px;
  padding-top: 5px;
  padding-right: -126px;
  color: white;
  font-size: 16px;
}

/* line 97, ../sass/_room_type_selector.scss */
.destination_wrapper .right_arrow {
  background: url(/static_1/images/booking/flecha_motor.png) no-repeat center center;
  right: 25px;
  top: 20px;
  height: 35px;
  width: 35px;
  border-radius: 0px;
  border-left: 1px solid white;
}

/********************* ROOMS ****************************/
/* line 3, ../sass/_rooms.scss */
.top-content-section {
  overflow: hidden;
  text-align: center;
  line-height: 24px;
}
/* line 8, ../sass/_rooms.scss */
.top-content-section h4 {
  color: #32398D;
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 16px;
  text-transform: uppercase;
}

/* line 17, ../sass/_rooms.scss */
.rooms {
  overflow: hidden;
  clear: both;
  margin-bottom: 50px;
  text-align: center;
}
/* line 23, ../sass/_rooms.scss */
.rooms .block-1 {
  margin-right: 10px !important;
}
/* line 26, ../sass/_rooms.scss */
.rooms .block-3 {
  margin-left: 10px !important;
}

/* line 31, ../sass/_rooms.scss */
.rooms .column4 {
  margin: 0px;
  width: 373px !important;
}
/* line 35, ../sass/_rooms.scss */
.rooms .column4 .gallery-img {
  height: 370px;
  width: 373px;
  overflow: hidden;
  margin-bottom: 3px;
  position: relative;
}
/* line 42, ../sass/_rooms.scss */
.rooms .column4 .gallery-img img {
  height: 370px;
}

/* line 48, ../sass/_rooms.scss */
.icons-room {
  text-align: center;
  z-index: 21;
  width: 100%;
  margin-top: 15px;
}
/* line 54, ../sass/_rooms.scss */
.icons-room img {
  margin: 0px 5px;
}
/* line 58, ../sass/_rooms.scss */
.icons-room img:hover {
  opacity: 0.6;
}

/* line 63, ../sass/_rooms.scss */
.room-block {
  position: relative;
}

/* line 67, ../sass/_rooms.scss */
.wrapper-transparent-container {
  position: absolute;
  width: 100%;
  top: 18%;
  z-index: 22;
}

/* line 74, ../sass/_rooms.scss */
.wrapper-transparent {
  display: table;
  margin: 0 auto;
  text-align: center;
  height: 210px;
  width: 58%;
}

/* line 82, ../sass/_rooms.scss */
.transparent-container-room {
  background: rgba(0, 0, 0, 0.5);
  display: table-cell;
  z-index: 22;
  vertical-align: middle;
}

/* line 89, ../sass/_rooms.scss */
.separator {
  border-bottom: 3px solid white;
  width: 20%;
  margin: 0px auto;
  padding: 0 !important;
}

/* line 96, ../sass/_rooms.scss */
img.ico-type {
  margin-bottom: 15px;
}

/* line 100, ../sass/_rooms.scss */
.room-block h3 {
  color: white;
  font-size: 24px;
  font-weight: normal;
  padding: 15px 0;
  text-align: center;
}

/* line 108, ../sass/_rooms.scss */
.room-block .buttons {
  width: 100%;
}
/* line 111, ../sass/_rooms.scss */
.room-block .buttons .section-box {
  width: 100%;
  background: #32398D;
  height: 50px;
  float: left;
  text-align: center;
  display: table;
}
/* line 119, ../sass/_rooms.scss */
.room-block .buttons .section-box a {
  font-size: 18px;
  color: white;
  display: table-cell;
  vertical-align: middle;
  text-transform: uppercase;
}
/* line 126, ../sass/_rooms.scss */
.room-block .buttons .section-box a:hover {
  background: #252a67;
}

/* line 133, ../sass/_rooms.scss */
.popup-rooms h3 {
  text-align: center;
  color: #af8553;
  font-size: 26px;
}
/* line 138, ../sass/_rooms.scss */
.popup-rooms .imagen {
  width: 100%;
  height: 250px;
  background: yellow;
  margin-bottom: 20px;
  overflow: hidden;
  position: relative;
}
/* line 146, ../sass/_rooms.scss */
.popup-rooms h4 {
  text-align: center;
  color: #32398D;
  font-size: 18px;
  margin-bottom: 10px;
}
/* line 152, ../sass/_rooms.scss */
.popup-rooms p {
  text-align: center;
  line-height: 24px;
  font-size: 14px;
}
/* line 158, ../sass/_rooms.scss */
.popup-rooms .imagen .flex-direction-nav li {
  display: inline;
}
/* line 161, ../sass/_rooms.scss */
.popup-rooms .imagen .flex-direction-nav li .flex-prev {
  position: absolute;
  top: 37%;
  left: 0px;
}
/* line 166, ../sass/_rooms.scss */
.popup-rooms .imagen .flex-direction-nav li .flex-next {
  position: absolute;
  top: 37%;
  right: 0px;
}
/* line 174, ../sass/_rooms.scss */
.popup-rooms .imagen .slides li {
  width: 650px !important;
  height: 355px !important;
  overflow: hidden;
  float: left;
  margin-bottom: 5px;
  position: relative;
}
/* line 182, ../sass/_rooms.scss */
.popup-rooms .imagen .slides li img {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -150%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

/* line 1, ../sass/_offers.scss */
.offer-wrapper {
  width: 375px !important;
  position: relative;
  margin-bottom: 50px;
  margin-left: 2.5px;
  margin-right: 2.5px;
}
/* line 8, ../sass/_offers.scss */
.offer-wrapper .image_wrapper {
  position: relative;
  overflow: hidden;
  height: 250px;
}
/* line 14, ../sass/_offers.scss */
.offer-wrapper img.offer-main-image {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  margin: auto;
  max-width: 435px;
}
/* line 28, ../sass/_offers.scss */
.offer-wrapper .offer-button-wrapper {
  width: 100%;
  position: absolute;
  text-align: center;
  margin-top: -32px;
}
/* line 34, ../sass/_offers.scss */
.offer-wrapper .offer-button-wrapper a.button-promotion {
  cursor: pointer;
  background: #af8553 !important;
  border-radius: 0px !important;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  color: white;
  text-transform: uppercase;
  padding: 21px 39px 21px;
  font-size: 17px;
}
/* line 45, ../sass/_offers.scss */
.offer-wrapper .offer-button-wrapper a.button-promotion:hover {
  background-color: #946c4a !important;
}
/* line 50, ../sass/_offers.scss */
.offer-wrapper .offer-button-wrapper a img {
  vertical-align: middle;
  height: 60px;
  width: 60px;
  padding-bottom: 4px;
}
/* line 56, ../sass/_offers.scss */
.offer-wrapper .offer-button-wrapper a img:hover {
  opacity: 0.8;
}
/* line 61, ../sass/_offers.scss */
.offer-wrapper .offer-text-wrapper {
  height: 193px;
  padding: 42px 30px;
  background: #ededed;
  box-sizing: border-box;
  font-size: 17px;
  text-align: center;
  color: #787878;
  overflow: hidden;
}
/* line 71, ../sass/_offers.scss */
.offer-wrapper .offer-text-wrapper p.titulo-oferta {
  color: #af8553;
  text-transform: uppercase;
  font-family: nexabold;
}
/* line 77, ../sass/_offers.scss */
.offer-wrapper .offer-text-wrapper div.entradilla-oferta {
  line-height: 26px;
  height: 75px;
  overflow: hidden;
}

/* line 85, ../sass/_offers.scss */
.filter-promotions {
  font-size: 40px;
  text-align: center;
}
/* line 89, ../sass/_offers.scss */
.filter-promotions li {
  cursor: pointer;
  background: #b48952;
  width: 49.75%;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 24px;
  padding: 16px 0px;
}
/* line 98, ../sass/_offers.scss */
.filter-promotions li:hover {
  background: #8c6b40;
}
/* line 103, ../sass/_offers.scss */
.filter-promotions .active {
  background: #8c6b40;
}
/* line 107, ../sass/_offers.scss */
.filter-promotions .filter-promos {
  margin-right: 0.5%;
  float: left;
}
/* line 112, ../sass/_offers.scss */
.filter-promotions .filter-packs {
  float: left;
}

/* line 118, ../sass/_offers.scss */
.offer-popup h4 {
  text-align: center;
  font-size: 25px;
  margin-bottom: 10px;
  color: #af8551;
}
/* line 124, ../sass/_offers.scss */
.offer-popup p {
  line-height: 27px;
  font-size: 17px;
}

/* line 1, ../sass/_minigallery.scss */
.minigallery_content_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 10px;
}
/* line 6, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigalery_content {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 30px;
}
/* line 11, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigalery_content .minigallery_title {
  font-size: 55px;
  text-transform: uppercase;
  font-weight: bolder;
  color: #32398D;
  font-family: inherit;
}
/* line 19, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigalery_content .minigallery_description {
  margin-top: 40px;
  font-size: 17px;
  line-height: inherit;
}
/* line 27, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item {
  background-color: #32398D;
  height: 300px;
  overflow: hidden;
}
/* line 32, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item img {
  width: auto;
  opacity: 1;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
}
/* line 37, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item span {
  display: block;
  width: 90%;
  color: white;
  font-size: 20px;
  text-align: center;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.6);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
/* line 45, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item span i.fa {
  display: block;
  text-align: center;
  font-size: 25px;
}
/* line 53, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item:hover img {
  opacity: .4;
}
/* line 57, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item:hover .minigallery_desc img {
  opacity: .8;
}
/* line 63, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-item .minigallery_desc img {
  opacity: .4;
}
/* line 70, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-nav > div {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
  color: white;
  cursor: pointer;
  font-size: 32px;
}
/* line 77, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-nav .owl-prev {
  left: 15px;
}
/* line 81, ../sass/_minigallery.scss */
.minigallery_content_wrapper .minigallery_wrapper .owl-nav .owl-next {
  right: 15px;
}

/*FONFS*/
@font-face {
  font-family: 'nexaregular';
  src: url("/css/feni2/webfont/nexa-light.otf");
  src: url("/css/feni2/webfont/nexa-light.otf?#iefix") format("opentype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'nexabold';
  src: url("/css/feni2/webfont/nexa-bold.otf");
  src: url("/css/feni2/webfont/nexa-bold.otf?#iefix") format("opentype");
  font-weight: normal;
  font-style: normal;
}
/* line 28, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .header_datepicker {
  background: #32398D;
}
/* line 32, ../sass/_template_specific.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active {
  background: #c76710 !important;
  color: white !important;
}
/* line 38, ../sass/_template_specific.scss */
body #booking-horizontal .boking_widget_inline .room {
  margin-bottom: 20px;
}
/* line 41, ../sass/_template_specific.scss */
body #booking-horizontal .boking_widget_inline .room .children_selector label {
  display: flex;
  justify-content: center;
}

/* line 56, ../sass/_template_specific.scss */
body {
  position: relative;
  font-family: 'nexaregular';
  font-size: 12px;
  background-color: white;
}
/* line 63, ../sass/_template_specific.scss */
body a {
  text-decoration: none;
}

/* line 70, ../sass/_template_specific.scss */
.boking_widget_inline .wrapper-new-web-support {
  display: block !important;
  font-weight: lighter;
  opacity: 1;
  border-top: 1px solid;
  width: 400px;
  margin: 0 auto;
  margin-top: 10px;
}

/* line 80, ../sass/_template_specific.scss */
.title-section-wrapper {
  color: #32398D;
  text-align: center;
  font-size: 55px;
  font-weight: bolder;
  text-transform: uppercase;
  margin: 50px auto;
}
/* line 88, ../sass/_template_specific.scss */
.title-section-wrapper .subtitle1 {
  font-family: nexabold;
  line-height: 59px;
}
/* line 93, ../sass/_template_specific.scss */
.title-section-wrapper .subtitle2 {
  font-weight: 100;
}

/* line 98, ../sass/_template_specific.scss */
#main_content p {
  padding: 10px 0px;
}

/* line 102, ../sass/_template_specific.scss */
.description-section-wrapper {
  color: #8e8d8d;
  text-align: center;
  font-size: 17px;
  margin-bottom: 50px;
}
/* line 108, ../sass/_template_specific.scss */
.description-section-wrapper p {
  font-weight: lighter;
  line-height: 29px;
}

/* line 114, ../sass/_template_specific.scss */
p.negrita {
  font-size: 18px;
  line-height: 29px;
  font-family: nexabold;
  color: #515151;
}

/* line 121, ../sass/_template_specific.scss */
.fancy-title {
  text-align: center;
  font-size: 25px;
  margin-bottom: 10px;
  color: #af8551;
}

/* line 128, ../sass/_template_specific.scss */
.popup_info_es, .popup_info_en {
  display: none;
}

/* line 132, ../sass/_template_specific.scss */
.wrapper-new-web-support {
  line-height: 16px;
}

/* line 140, ../sass/_template_specific.scss */
header {
  position: absolute;
  top: 0px;
  z-index: 100;
  width: 100%;
  text-align: center;
  min-height: 280px;
  padding-top: 60px;
}

/* line 151, ../sass/_template_specific.scss */
#logoDiv {
  margin-top: -15px;
}

/* line 155, ../sass/_template_specific.scss */
#top-sections {
  text-align: left;
  font-family: nexaregular, sans-serif;
  font-size: 12px;
  color: white;
}
/* line 161, ../sass/_template_specific.scss */
#top-sections > span {
  display: inline-block;
  background: white;
  padding: 16px 10px;
  margin-right: 5px;
  color: #32398D;
  font-family: "nexabold";
  font-weight: bold;
}
/* line 171, ../sass/_template_specific.scss */
#top-sections a {
  color: white;
}

/* line 182, ../sass/_template_specific.scss */
#lang {
  float: right;
  cursor: pointer;
  width: 90px;
}
/* line 187, ../sass/_template_specific.scss */
#lang #selected-language {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 17px 10px 17px 7px;
  box-sizing: border-box;
  width: 45px;
  height: 45px;
  display: inline-block;
  color: white;
  font-size: 15px;
  font-family: nexabold;
  letter-spacing: 1px;
  text-transform: uppercase;
}
/* line 203, ../sass/_template_specific.scss */
#lang .arrow {
  display: inline-block;
  background: white url(/img/feni2/flecha_dorada_lang.png) no-repeat center center;
  float: right;
  width: 45px;
  height: 45px;
  margin-top: 0px;
}
/* line 213, ../sass/_template_specific.scss */
#lang ul li {
  background: #ffffff;
  text-align: center;
  width: 80px;
  font-size: 16px;
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 225, ../sass/_template_specific.scss */
#lang ul li:hover {
  border-bottom: 1px solid rgba(128, 128, 128, 0.33);
  background: #f0f0f0;
  width: 80px;
}
/* line 231, ../sass/_template_specific.scss */
#lang ul li a {
  color: #666 !important;
  text-decoration: none !important;
}

/* line 238, ../sass/_template_specific.scss */
#language-selector-options {
  display: none;
}
/* line 241, ../sass/_template_specific.scss */
#language-selector-options a {
  color: #787878;
}
/* line 244, ../sass/_template_specific.scss */
#language-selector-options a:hover {
  color: #32398D;
}

/* line 251, ../sass/_template_specific.scss */
.weather {
  margin-right: 10px;
  width: 90px;
  float: right;
}
/* line 257, ../sass/_template_specific.scss */
.weather .grados {
  float: right;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 17px 10px 17px 3px;
  box-sizing: border-box;
  width: 45px;
  height: 45px;
  display: inline-block;
  color: white;
  font-size: 14px;
  font-family: nexabold;
  letter-spacing: 1px;
}
/* line 270, ../sass/_template_specific.scss */
.weather .img_weather {
  float: right;
  background-color: white;
  width: 45px;
  height: 45px;
  padding-top: 4px;
  box-sizing: border-box;
}

/* A few more changes in the new booking engine */
/* line 294, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline .room_list_wrapper {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
/* line 301, ../sass/_template_specific.scss */
#booking-horizontal .date_box .date_year, #booking-horizontal .date_box .date_day {
  color: white !important;
}
/* line 306, ../sass/_template_specific.scss */
#booking-horizontal .date_box {
  background-color: transparent;
  border-radius: 0px;
  border: 2px solid white;
}
/* line 312, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline .stay_selection .entry_date_wrapper label, #booking-horizontal .boking_widget_inline .stay_selection .departure_date_wrapper label, #booking-horizontal .boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: white !important;
  font-size: 12px !important;
}
/* line 317, ../sass/_template_specific.scss */
#booking-horizontal .selectric {
  background-color: transparent;
  color: white;
  border-radius: 0px;
  border: 2px solid white;
}
/* line 323, ../sass/_template_specific.scss */
#booking-horizontal .selectric .label {
  color: white !important;
}
/* line 327, ../sass/_template_specific.scss */
#booking-horizontal .selectric .button {
  border-left: 1px solid white;
  border-radius: 0px;
  background-color: transparent !important;
}
/* line 335, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline .wrapper_booking_button {
  margin-top: 1px;
  float: left;
  margin-left: 20px;
}
/* line 342, ../sass/_template_specific.scss */
#booking-horizontal .wrapper_booking_button button {
  cursor: pointer;
  background: #F3D132;
  height: 44px;
  padding: 10px;
  border-radius: 0px;
  border: 2px solid white;
}
/* line 352, ../sass/_template_specific.scss */
#booking-horizontal .wrapper_booking_button button:hover {
  background: #e5bf0d;
}
/* line 358, ../sass/_template_specific.scss */
#booking-horizontal .wrapper_booking_button .promocode_input {
  background-color: transparent !important;
  width: 155px !important;
  border-radius: 0px;
  border: 2px solid white;
  color: white !important;
}
/* line 367, ../sass/_template_specific.scss */
#booking-horizontal .promocode_input::-webkit-input-placeholder {
  color: white !important;
}
/* line 370, ../sass/_template_specific.scss */
#booking-horizontal .promocode_input::-moz-placeholder {
  color: white !important;
}
/* line 373, ../sass/_template_specific.scss */
#booking-horizontal .promocode_input:-moz-placeholder {
  color: white !important;
}
/* line 376, ../sass/_template_specific.scss */
#booking-horizontal .promocode_input:-ms-input-placeholder {
  color: white !important;
}
/* line 382, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline {
  background-color: transparent !important;
}
/* line 386, ../sass/_template_specific.scss */
#booking-horizontal .booking_form_title {
  background-color: transparent !important;
}
/* line 390, ../sass/_template_specific.scss */
#booking-horizontal .booking_form {
  background-color: transparent !important;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
  justify-content: center;
}
/* line 396, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline .room .room_title, #booking-horizontal .boking_widget_inline .room .adults_selector label, #booking-horizontal .boking_widget_inline .room .children_selector label {
  color: white !important;
  font-size: 12px;
}
/* line 401, ../sass/_template_specific.scss */
#booking-horizontal .boking_widget_inline .stay_selection {
  margin-left: 0 !important;
  margin-top: 5px;
}

/* line 410, ../sass/_template_specific.scss */
.destination_wrapper {
  margin-top: 5px;
  margin-right: -9px;
  margin-left: 22px;
  display: inline-block;
}
/* line 416, ../sass/_template_specific.scss */
.destination_wrapper .right_arrow {
  cursor: pointer;
}
/* line 420, ../sass/_template_specific.scss */
.destination_wrapper div#placeholder {
  color: white;
}
/* line 424, ../sass/_template_specific.scss */
.destination_wrapper input {
  color: white;
}
/* line 428, ../sass/_template_specific.scss */
.destination_wrapper input::-webkit-input-placeholder {
  color: white;
}
/* line 431, ../sass/_template_specific.scss */
.destination_wrapper input:-moz-placeholder {
  color: white;
}
/* line 434, ../sass/_template_specific.scss */
.destination_wrapper input::-moz-placeholder {
  color: white;
}
/* line 437, ../sass/_template_specific.scss */
.destination_wrapper input:-ms-input-placeholder {
  color: white;
}

/* line 443, ../sass/_template_specific.scss */
.destination_wrapper input {
  height: 38px;
  width: 175px;
  padding-left: 10px;
  background: transparent;
  margin-right: 20px;
  border: 2px solid white;
  border-radius: 0px;
  cursor: pointer;
}
/* line 453, ../sass/_template_specific.scss */
.destination_wrapper input div#placeholder {
  color: white !important;
}

/* line 460, ../sass/_template_specific.scss */
.destination_wrapper .destination_field .destination {
  width: 200px;
}
/* line 463, ../sass/_template_specific.scss */
.destination_wrapper label {
  color: white;
  margin-right: 15px;
}

/* line 469, ../sass/_template_specific.scss */
.hotel_selector {
  position: absolute;
  display: none;
  top: 47px;
  z-index: 1;
  cursor: pointer;
}
/* line 476, ../sass/_template_specific.scss */
.hotel_selector ul li {
  border: 2px solid white;
  border-bottom-width: 0;
  display: block;
  padding: 5px 10px;
  color: white;
  cursor: pointer;
  font-size: 11px;
  text-transform: uppercase;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 487, ../sass/_template_specific.scss */
.hotel_selector ul li:hover {
  opacity: .8;
}
/* line 490, ../sass/_template_specific.scss */
.hotel_selector ul li:last-of-type {
  border-bottom-width: 2px;
}

/* line 497, ../sass/_template_specific.scss */
.roomtype_selector {
  left: 168px;
  top: 71px;
}

/* line 502, ../sass/_template_specific.scss */
.roomtype_selector .title_selector {
  background: rgba(0, 0, 0, 0.53);
  padding-right: 27px !important;
  padding-left: 29px;
}

/* line 508, ../sass/_template_specific.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: -75px;
  bottom: 24px;
}

/* line 516, ../sass/_template_specific.scss */
.fancybox-inner {
  overflow: visible !important;
}
/* line 519, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper {
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
}
/* line 523, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper input::-webkit-input-placeholder {
  color: #af8553;
}
/* line 526, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper input:-moz-placeholder {
  color: #af8553;
}
/* line 529, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper input::-moz-placeholder {
  color: #af8553;
}
/* line 532, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper input:-ms-input-placeholder {
  color: #af8553;
}
/* line 536, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper label {
  color: #838383;
}
/* line 540, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper input {
  width: 94% !important;
  background: white;
  color: #af8553;
}
/* line 547, ../sass/_template_specific.scss */
.fancybox-inner .destination_wrapper .right_arrow {
  background: #af8652 url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center !important;
  margin-right: -22px;
}
/* line 554, ../sass/_template_specific.scss */
.fancybox-inner .promocode_input {
  width: 120px;
  font-size: 12px;
}
/* line 558, ../sass/_template_specific.scss */
.fancybox-inner .wrapper_booking_button button {
  font-size: 13px;
  width: 125px;
}
/* line 563, ../sass/_template_specific.scss */
.fancybox-inner .booking_form {
  background: #ededed !important;
}
/* line 567, ../sass/_template_specific.scss */
.fancybox-inner .selectric {
  border-radius: 0px !important;
}
/* line 571, ../sass/_template_specific.scss */
.fancybox-inner .date_box {
  border-radius: 0px !important;
}
/* line 573, ../sass/_template_specific.scss */
.fancybox-inner .date_box .date_year {
  color: #b08757;
}
/* line 578, ../sass/_template_specific.scss */
.fancybox-inner .wrapper_booking_button .promocode_input {
  border-radius: 0px !important;
}
/* line 582, ../sass/_template_specific.scss */
.fancybox-inner .wrapper_booking_button button {
  border-radius: 0px !important;
}
/* line 586, ../sass/_template_specific.scss */
.fancybox-inner .button {
  border-radius: 0px !important;
}

/** CALENDAR DATEPICKER**/
/* line 595, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #32398D;
}

/* line 599, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #32398D;
  color: white;
}

/* line 604, ../sass/_template_specific.scss */
.date_box .date_year {
  color: white;
}

/* line 609, ../sass/_template_specific.scss */
#wrapper_booking_fancybox .date_box .date_year {
  color: grey;
}
/* line 612, ../sass/_template_specific.scss */
#wrapper_booking_fancybox .date_day {
  margin-top: 4px;
}

/* line 620, ../sass/_template_specific.scss */
.tp-leftarrow.default {
  background: url(/img/feni2/left_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}

/* line 626, ../sass/_template_specific.scss */
.tp-rightarrow.default {
  background: url(/img/feni2/right_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}

/* line 632, ../sass/_template_specific.scss */
.tp-bullets {
  display: none;
}

/* line 636, ../sass/_template_specific.scss */
.overlay_slider {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, transparent 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, transparent 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, rgba(0, 0, 0, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#a6000000', endColorstr='#00000000', GradientType=0);
  z-index: -1;
}

/* line 649, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
  margin-bottom: 10px;
}
/* line 653, ../sass/_template_specific.scss */
#slider_container .tp-bgimg {
  position: relative;
  z-index: -1;
}

/* line 659, ../sass/_template_specific.scss */
.slider_inner {
  height: 600px !important;
  overflow: hidden;
}
/* line 663, ../sass/_template_specific.scss */
.slider_inner .slider_image {
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  max-width: initial;
}

/* line 676, ../sass/_template_specific.scss */
.forcefullwidth_wrapper_tp_banner {
  min-height: 650px !important;
}

/* line 680, ../sass/_template_specific.scss */
.tp-banner-container {
  min-height: 650px;
}

/* line 684, ../sass/_template_specific.scss */
#botton-slider {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 30;
  position: absolute;
  bottom: 0px;
  display: block;
  width: 205px;
  height: 45px;
  box-sizing: border-box;
  left: 50%;
  text-align: center;
  padding-top: 20px;
  margin-left: -103px;
}

/* line 704, ../sass/_template_specific.scss */
.cartela_home {
  position: relative;
  margin-top: 70px;
}
/* line 708, ../sass/_template_specific.scss */
.cartela_home .slider-text-1, .cartela_home .slider-text-2, .cartela_home .slider-text-3, .cartela_home .slider-text-4 {
  position: absolute;
  width: 100%;
}
/* line 713, ../sass/_template_specific.scss */
.cartela_home .slider-text-1 {
  top: 29px;
}
/* line 716, ../sass/_template_specific.scss */
.cartela_home .slider-text-2 {
  top: 90px;
}
/* line 719, ../sass/_template_specific.scss */
.cartela_home .slider-text-3 {
  top: 177px;
  left: 358px;
  width: 19%;
}
/* line 724, ../sass/_template_specific.scss */
.cartela_home .slider-text-4 {
  top: 177px;
  right: 355px;
  width: 19%;
}

/* MAIN DISTRIBUTED MENU */
/* line 735, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-family: nexaregular, sans-serif;
  font-size: 13px;
  z-index: 99;
  position: relative;
  top: 27px;
  clear: both;
  width: 915px;
}

/* line 748, ../sass/_template_specific.scss */
#mainMenuDiv a {
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  color: white;
  display: inline-block;
  font-size: 14px;
}

/* line 757, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  border-top: 2px solid white !important;
  padding: 3px 8px 7px;
}

/* line 762, ../sass/_template_specific.scss */
#section-active a {
  padding: 3px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  border-top: 2px solid white !important;
  display: inline-block;
}

/* line 770, ../sass/_template_specific.scss */
.main-section-div-wrapper a:hover {
  border-top: 2px solid white !important;
}

/* line 774, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 778, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 782, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 786, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 790, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 795, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
}

/* line 799, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 806, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/* line 810, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 38px;
  text-transform: uppercase;
}

/* BIG BANNERS LINES */
/* line 819, ../sass/_template_specific.scss */
#wrapper_main_content {
  display: block;
  overflow: visible;
}

/* line 824, ../sass/_template_specific.scss */
#wrapper-main-banners {
  margin-bottom: 10px;
  overflow: auto;
}

/* line 829, ../sass/_template_specific.scss */
.wrapper-big-banner {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%;
}

/* line 839, ../sass/_template_specific.scss */
.wrapper-big-banner:before {
  content: '';
  display: block;
  padding-top: 100%;
  /* initial ratio of 1:1*/
}

/* line 845, ../sass/_template_specific.scss */
.big-banner-title {
  margin-top: 10px;
  line-height: 60px;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 40px;
  text-transform: uppercase;
}

/* line 854, ../sass/_template_specific.scss */
.square-icono h4.big-banner-title {
  font-weight: 100;
}

/* line 858, ../sass/_template_specific.scss */
.big-banner-moreinfo {
  color: white;
  text-decoration: none;
  font-size: 19px;
  margin-top: 15px;
  display: block;
}

/* line 866, ../sass/_template_specific.scss */
.span-underline {
  width: 60px;
  border-bottom: 3px solid rgba(255, 255, 255, 0.5);
  text-align: center;
  display: block;
  margin: 0 auto;
}

/* line 874, ../sass/_template_specific.scss */
.banner-special-wrapper {
  margin-bottom: 10px;
  overflow: auto;
}

/* line 879, ../sass/_template_specific.scss */
img.icono-bigbanner {
  margin-bottom: 20px;
}

/* line 883, ../sass/_template_specific.scss */
#triangulo-left {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-right: 60px solid #af8652;
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  margin-left: -9.0%;
  z-index: 4;
}

/* line 896, ../sass/_template_specific.scss */
#triangulo-right {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-left: 60px solid black;
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  right: -9.0%;
  z-index: 4;
}

/* line 909, ../sass/_template_specific.scss */
.banner-link {
  z-index: 1 !important;
}

/* line 913, ../sass/_template_specific.scss */
.wrapper-big-banner.square-background {
  z-index: 3;
}

/* line 917, ../sass/_template_specific.scss */
.container-text-banner {
  position: absolute;
  top: 39%;
  bottom: 0px;
  vertical-align: middle;
  left: 0px;
  right: 0px;
  text-align: center;
}

/* line 927, ../sass/_template_specific.scss */
.square-icono .container-text-banner {
  padding: 0px 40px;
  top: 20% !important;
}

/* line 932, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 {
  margin-bottom: 10px;
  overflow: auto;
}
/* line 936, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 .wrapper-bannerX2 {
  width: 50%;
  float: left;
  position: relative;
  overflow: hidden;
}
/* line 942, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 .wrapper-bannerX2:before {
  content: "";
  display: block;
  padding-top: 50%;
}
/* line 949, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 .banner-text-inside {
  position: absolute;
  top: 15%;
  vertical-align: middle;
  right: 63px;
  text-align: center;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.59);
  width: 37%;
  height: 63%;
  padding: 0px 15px;
  color: white;
}
/* line 962, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 .banner-text-inside .big-banner-title {
  font-size: 40px;
}
/* line 966, ../sass/_template_specific.scss */
div#wrapper-main-bannersX2 .banner-text-inside .big-banner-description {
  margin-top: 8px;
  font-size: 33px;
  font-weight: 100;
  line-height: 40px;
}

/* line 975, ../sass/_template_specific.scss */
.title-banners-cycle {
  text-align: center;
  font-size: 30px;
  text-transform: uppercase;
  margin: 60px 0px 30px;
  color: #32398D;
}

/* line 983, ../sass/_template_specific.scss */
.content-cycle-banner {
  position: relative !important;
  z-index: 1 !important;
}

/* line 988, ../sass/_template_specific.scss */
.cycle-description {
  position: absolute !important;
  left: 0px;
  color: white;
  top: 80px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  font-size: 24px;
  display: block;
  text-align: center;
  text-transform: uppercase;
  width: 300px;
  background: rgba(0, 0, 0, 0.6);
  padding: 20px;
  box-sizing: border-box;
}
/* line 1006, ../sass/_template_specific.scss */
.cycle-description .bold {
  font-family: nexabold;
}

/* line 1011, ../sass/_template_specific.scss */
.cycle-pager {
  text-align: center;
  font-size: 90px;
  color: #bebebe;
}
/* line 1016, ../sass/_template_specific.scss */
.cycle-pager span {
  cursor: pointer;
}
/* line 1019, ../sass/_template_specific.scss */
.cycle-pager span.cycle-pager-active {
  color: #32398D;
}

/******************FOOTER***************/
/* line 1027, ../sass/_template_specific.scss */
footer {
  background-color: #1f265e;
  clear: both;
}

/* line 1032, ../sass/_template_specific.scss */
.wrapper_footer_columns {
  margin: 0px auto;
  padding-top: 20px;
}

/* line 1037, ../sass/_template_specific.scss */
.footer_column {
  font-family: nexaregular, sans-serif;
  font-size: 13px;
  line-height: 23px;
  color: white;
  width: 285px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  height: auto;
  border-left: 1px solid white;
  text-align: center;
  box-sizing: border-box;
  padding: 0 15px;
}

/* line 1052, ../sass/_template_specific.scss */
.footer_column a {
  font-size: 14px;
  line-height: 23px;
  color: white;
  text-decoration: none;
  font-weight: 100;
}
/* line 1059, ../sass/_template_specific.scss */
.footer_column a.link_work {
  text-transform: uppercase;
  text-decoration: underline;
}

/* line 1065, ../sass/_template_specific.scss */
#footer_column_description {
  margin-bottom: 10px;
}

/* line 1069, ../sass/_template_specific.scss */
.footer_column a:hover {
  color: #cdcdcd;
}

/* line 1073, ../sass/_template_specific.scss */
footer .last {
  border-left: 1px solid white;
  width: 282px !important;
  text-align: left;
  padding: 0px 26px;
}

/* line 1080, ../sass/_template_specific.scss */
.footer_column_title {
  font-weight: 500;
  color: white;
  font-size: 18px;
  margin-bottom: 5px;
}

/* line 1087, ../sass/_template_specific.scss */
.copyright-footer {
  line-height: 46px;
}

/* line 1091, ../sass/_template_specific.scss */
#title_newsletter {
  font-weight: 500;
  color: white;
  font-size: 18px;
}

/* line 1097, ../sass/_template_specific.scss */
label#suscEmailLabel {
  font-size: 14px;
  font-weight: 100;
}

/* line 1102, ../sass/_template_specific.scss */
#suscEmail {
  width: 98%;
  background-color: white;
  border: none;
  height: 24px;
  color: #32398D;
  border-radius: 3px;
  font-size: 14px;
  margin: 10px 0;
}

/* line 1113, ../sass/_template_specific.scss */
.button_newsletter {
  width: 90px;
  height: 23px;
  text-transform: uppercase;
  background: #32398D;
  outline: none;
  font-weight: bolder;
  margin-left: 81px;
  padding-top: 2px;
  border: none;
  color: white;
  font-size: 14px;
}

/* line 1127, ../sass/_template_specific.scss */
div.button_newsletter:hover {
  background: #252a67;
  cursor: pointer;
}

/* line 1132, ../sass/_template_specific.scss */
.newsletter_wrapper label.error {
  margin-left: 100px;
}

/* line 1136, ../sass/_template_specific.scss */
.newsletter_container {
  width: auto;
}

/* line 1140, ../sass/_template_specific.scss */
.newsletter_checkbox {
  font-size: 12px;
}
/* line 1143, ../sass/_template_specific.scss */
.newsletter_checkbox a {
  text-decoration: underline;
  font-size: 12px;
}

/* line 1149, ../sass/_template_specific.scss */
input#promotions, input#privacy {
  float: left;
}

/* line 1153, ../sass/_template_specific.scss */
label.error {
  margin-bottom: 8px;
  font-size: 16px;
  margin-top: 3px;
}

/* line 1160, ../sass/_template_specific.scss */
label.error[for="privacy"] {
  position: absolute;
  margin-top: 25px;
}

/* line 1165, ../sass/_template_specific.scss */
#form_events .styled-select label.error {
  background: white !important;
}

/* line 1169, ../sass/_template_specific.scss */
#social {
  margin-top: 20px;
}
/* line 1172, ../sass/_template_specific.scss */
#social a {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  background: white;
  vertical-align: middle;
}
/* line 1181, ../sass/_template_specific.scss */
#social a:hover .fa {
  color: #1f265e;
}
/* line 1186, ../sass/_template_specific.scss */
#social a .fa {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: #32398D;
  font-size: 12px;
}

/* line 1194, ../sass/_template_specific.scss */
#social span {
  position: relative;
  font-weight: 100;
  display: inline-block;
  vertical-align: middle;
}

/* line 1201, ../sass/_template_specific.scss */
#social img {
  margin-left: 6px;
  padding-bottom: 2px;
  height: 28px;
  width: 28px;
}
/* line 1207, ../sass/_template_specific.scss */
#social img:hover {
  opacity: 0.5;
}

/* line 1212, ../sass/_template_specific.scss */
.footer_column h3 {
  font-size: 19px;
  color: white;
}

/* line 1217, ../sass/_template_specific.scss */
#footer {
  color: white;
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  padding-bottom: 20px;
}
/* line 1224, ../sass/_template_specific.scss */
#footer p {
  text-align: center;
}

/* line 1229, ../sass/_template_specific.scss */
#footer a {
  text-decoration: none;
  color: white;
}

/* line 1234, ../sass/_template_specific.scss */
#footer_bottom_text {
  font-size: 14px;
  line-height: 14px;
}

/* line 1239, ../sass/_template_specific.scss */
.copyright-footer img {
  margin: 0 5px;
}

/* line 1243, ../sass/_template_specific.scss */
#google_plus_one {
  text-align: center;
}

/* line 1247, ../sass/_template_specific.scss */
#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

/* line 1252, ../sass/_template_specific.scss */
#facebook_like {
  text-align: center;
  margin-top: 10px;
}

/* line 1257, ../sass/_template_specific.scss */
#facebook_like iframe {
  height: 21px;
  width: 103px;
}

/* line 1265, ../sass/_template_specific.scss */
.know-more-text {
  line-height: 27px;
  font-size: 17px;
}

/* line 1270, ../sass/_template_specific.scss */
.event_wrapper {
  color: black;
  margin-bottom: 40px;
  margin-top: 10px;
  padding-bottom: 40px;
  border-bottom: 1px solid #ededed;
}
/* line 1277, ../sass/_template_specific.scss */
.event_wrapper .event_img {
  width: 281px !important;
  margin: 0px 5px;
}
/* line 1280, ../sass/_template_specific.scss */
.event_wrapper .event_img .event_img img {
  width: 281px;
  height: 281px;
}
/* line 1286, ../sass/_template_specific.scss */
.event_wrapper .event_date {
  width: 120px;
  margin: 0px;
}
/* line 1290, ../sass/_template_specific.scss */
.event_wrapper .event_date .event_day, .event_wrapper .event_date .event_month, .event_wrapper .event_date .event_year {
  background: #7b7b7b;
  margin-bottom: 5px;
  text-align: center;
  color: white;
  padding: 13px 0px;
  font-size: 22px;
}
/* line 1300, ../sass/_template_specific.scss */
.event_wrapper .event_main_info {
  width: 678px;
  background: #f3f3f3;
  height: 281px;
  padding: 30px;
  box-sizing: border-box;
  position: relative;
  margin: 0.5px 5px;
}
/* line 1309, ../sass/_template_specific.scss */
.event_wrapper .event_main_info .event-title {
  text-transform: uppercase;
  color: #af8553;
  font-size: 19px;
  margin-bottom: 15px;
  font-weight: bolder;
}
/* line 1317, ../sass/_template_specific.scss */
.event_wrapper .event_main_info .event-description {
  line-height: 28px;
  font-size: 16px;
  font-weight: lighter;
  color: gray;
}
/* line 1324, ../sass/_template_specific.scss */
.event_wrapper .event_main_info .event-buttons-wrappers {
  position: absolute;
  bottom: 40px;
}
/* line 1328, ../sass/_template_specific.scss */
.event_wrapper .event_main_info .event-buttons-wrappers a {
  background: #af8553;
  padding: 19px 46px;
  font-size: 17px;
}
/* line 1333, ../sass/_template_specific.scss */
.event_wrapper .event_main_info .event-buttons-wrappers a:hover {
  opacity: 0.7;
}

/* line 1347, ../sass/_template_specific.scss */
.filter-offers {
  font-size: 40px;
  text-align: center;
}
/* line 1351, ../sass/_template_specific.scss */
.filter-offers .active {
  background: #252a67;
}
/* line 1355, ../sass/_template_specific.scss */
.filter-offers .filter-hotel {
  margin-right: 0.5%;
  float: left;
}
/* line 1360, ../sass/_template_specific.scss */
.filter-offers .filter-apartamentos {
  float: left;
}
/* line 1364, ../sass/_template_specific.scss */
.filter-offers li {
  cursor: pointer;
  background: #32398D;
  width: 49.75%;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 24px;
  padding: 16px 0px;
  font-weight: bold;
}
/* line 1373, ../sass/_template_specific.scss */
.filter-offers li:hover {
  background: #252a67;
}

/* line 1380, ../sass/_template_specific.scss */
.gallery-images {
  margin-bottom: 60px;
}

/* line 1384, ../sass/_template_specific.scss */
ul.gallery_1 li {
  padding: 2px 1px !important;
  width: 25% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  cursor: pointer;
}
/* line 1392, ../sass/_template_specific.scss */
ul.gallery_1 li .crop {
  height: 280px !important;
}

/* line 1405, ../sass/_template_specific.scss */
.my-booking-text {
  color: black;
  text-align: center;
  line-height: 29px;
  font-size: 16px;
}

/* line 1412, ../sass/_template_specific.scss */
form#my-bookings-form {
  margin: 50px auto;
  text-align: center;
}
/* line 1416, ../sass/_template_specific.scss */
form#my-bookings-form label {
  color: black;
  text-align: left;
  font-size: 16px;
}
/* line 1422, ../sass/_template_specific.scss */
form#my-bookings-form input {
  text-align: center;
  margin-bottom: 15px;
  margin-top: 5px;
  font-size: 16px;
  display: initial;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
}
/* line 1435, ../sass/_template_specific.scss */
form#my-bookings-form button#my-bookings-form-search-button, form#my-bookings-form button#cancelButton {
  cursor: pointer;
  background: #af8553;
  border-radius: 0px !important;
  border: 2px solid white;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  color: white;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 110px;
  padding: 9px 14px 10px;
}
/* line 1449, ../sass/_template_specific.scss */
form#my-bookings-form button#my-bookings-form-search-button:hover, form#my-bookings-form button#cancelButton:hover {
  background: #7c5a3e;
}
/* line 1454, ../sass/_template_specific.scss */
form#my-bookings-form button#cancelButton {
  width: 215px;
  display: none;
}

/* line 1460, ../sass/_template_specific.scss */
.fResumenReserva {
  border: 3px solid #AF8553 !important;
  background: rgba(175, 133, 83, 0.59) !important;
  padding: 4px 10px 20px 10px !important;
}

/* line 1467, ../sass/_template_specific.scss */
form#my-bookings-form button#cancelButton {
  width: 213px !important;
  margin: 22px auto !important;
}

/* line 1472, ../sass/_template_specific.scss */
.alpha {
  margin-left: -104px !important;
}
/* line 1475, ../sass/_template_specific.scss */
.alpha .txtCosteTotal {
  color: #6c563c !important;
}

/* Mis reservas corpo */
/* line 1483, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget {
  margin: auto;
  margin-top: 40px;
  margin-bottom: 0;
  padding: 20px;
}
/* line 1489, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #info_ninos {
  display: none !important;
}
/* line 1493, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones {
  margin: 0 auto 10px;
}
/* line 1496, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_opciones .ninos-con-babies {
  margin-right: 15px;
}
/* line 1501, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas {
  text-align: center;
}
/* line 1504, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas #fecha_entrada, #my-bookings-form #reservation .modify_reservation_widget #contenedor_fechas #fecha_salida {
  display: inline-block;
  float: none;
}
/* line 1510, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones {
  text-align: center;
}
/* line 1513, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones label {
  display: inline-block;
  float: none;
}
/* line 1518, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #contenedor_habitaciones select {
  display: inline-block;
  float: none;
}
/* line 1524, ../sass/_template_specific.scss */
#my-bookings-form #reservation .modify_reservation_widget #envio {
  text-align: center;
}
/* line 1529, ../sass/_template_specific.scss */
#my-bookings-form #reservation .my-bookings-booking-info {
  margin: 40px auto 0;
}
/* line 1532, ../sass/_template_specific.scss */
#my-bookings-form #reservation .my-bookings-booking-info .fResumenReserva {
  margin: auto;
}
/* line 1537, ../sass/_template_specific.scss */
#my-bookings-form #modify-button-container {
  display: none;
}
/* line 1542, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields label {
  display: block;
  text-align: center;
  text-transform: uppercase;
  color: #4B4B4B;
  font-weight: 100;
}
/* line 1550, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields input, #my-bookings-form #my-bookings-form-fields select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: block;
  width: 350px;
  margin: 10px auto;
  height: 40px;
  border-radius: 0;
  text-align: center;
  font-size: 14px;
  border: 1px solid #DDD;
}
/* line 1564, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields select {
  padding: 0 0 0 15px;
}
/* line 1568, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul {
  text-align: center;
  margin-top: 30px;
}
/* line 1572, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}
/* line 1577, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li button {
  height: 40px;
  text-transform: uppercase;
  font-size: 16px;
  color: white;
  border: 0;
  cursor: pointer;
  width: 100%;
  font-weight: 400;
  background: #32398D;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 1589, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li button.modify-reservation {
  background: #32398D;
}
/* line 1592, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li button.modify-reservation:hover {
  background: #252a67;
}
/* line 1597, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li button.searchForReservation {
  background: #1f265e;
}
/* line 1600, ../sass/_template_specific.scss */
#my-bookings-form #my-bookings-form-fields ul li button.searchForReservation:hover {
  background: #121638;
}
/* line 1609, ../sass/_template_specific.scss */
#my-bookings-form #cancelButton {
  display: none;
  background: #1f265e;
  height: 40px;
  text-transform: uppercase;
  font-size: 16px;
  color: white;
  border: 0;
  cursor: pointer;
  width: 200px;
  font-weight: 100;
  margin: 40px auto 0;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 1623, ../sass/_template_specific.scss */
#my-bookings-form #cancelButton:hover {
  background: #121638;
}

/* line 1631, ../sass/_template_specific.scss */
.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;
}
/* line 1639, ../sass/_template_specific.scss */
.gallery-mosaic-item img {
  width: 100%;
  height: 137%;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 1656, ../sass/_template_specific.scss */
.gallery-mosaic-item img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 1666, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;
}
/* line 1672, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img {
  width: 100%;
  height: 97%;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 1689, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 1699, ../sass/_template_specific.scss */
.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

/* line 1704, ../sass/_template_specific.scss */
.gallery-big {
  width: 396px;
  height: 360px;
}

/* line 1710, ../sass/_template_specific.scss */
.gallery-mosaic {
  margin: 0px auto 70px;
}

@media (max-width: 1760px) {
  /* line 1719, ../sass/_template_specific.scss */
  .big-banner-title {
    font-size: 30px;
  }

  /* line 1725, ../sass/_template_specific.scss */
  .banner-text-inside .big-banner-title {
    font-size: 27px !important;
    line-height: 42px;
  }
  /* line 1730, ../sass/_template_specific.scss */
  .banner-text-inside .big-banner-description {
    font-size: 22px !important;
  }
}
@media (max-width: 1480px) {
  /* line 1737, ../sass/_template_specific.scss */
  div#wrapper-main-bannersX2 .banner-text-inside .big-banner-description {
    line-height: 25px;
  }
}
@media (max-width: 1370px) {
  /* line 1750, ../sass/_template_specific.scss */
  .big-banner-title {
    font-size: 20px;
    line-height: 40px;
  }
}
@media (max-width: 1233px) {
  /* line 1763, ../sass/_template_specific.scss */
  .banner-text-inside .big-banner-title {
    line-height: 30px;
  }
}
@media (min-width: 1920px) {
  /* line 1769, ../sass/_template_specific.scss */
  #triangulo-left {
    margin-left: -6%;
  }

  /* line 1773, ../sass/_template_specific.scss */
  #triangulo-right {
    margin-right: 2%;
  }
}
/* line 1780, ../sass/_template_specific.scss */
#bannerx2-opac {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}

/* line 1788, ../sass/_template_specific.scss */
#bannerx2-opac:hover {
  opacity: 0.5;
}

/* line 1792, ../sass/_template_specific.scss */
.arrow-wrapper {
  position: relative;
  overflow: visible;
  float: left;
  width: 25%;
}

/* line 1799, ../sass/_template_specific.scss */
.arrow-wrapper:before {
  content: "";
  display: block;
}

/* line 1804, ../sass/_template_specific.scss */
.wrapper-big-banner {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
  overflow: hidden;
}

/* line 1813, ../sass/_template_specific.scss */
.zoom {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 1821, ../sass/_template_specific.scss */
.wrapper-for-hide {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%;
  /* desired width */
  overflow-y: hidden;
  overflow-x: hidden;
}

/* line 1832, ../sass/_template_specific.scss */
.wrapper-for-hide:before {
  content: "";
  display: block;
}

/* line 1837, ../sass/_template_specific.scss */
.hidden-menu {
  z-index: 999;
  display: none;
  background: white;
  position: fixed;
  width: 100%;
  min-width: 1140px;
  top: 0px;
  height: 90px;
  box-shadow: 0px 1px 7px black;
}
/* line 1848, ../sass/_template_specific.scss */
.hidden-menu #logoDiv {
  width: 115px;
  margin-top: -6px;
}
/* line 1853, ../sass/_template_specific.scss */
.hidden-menu #main-sections {
  margin-top: 20px;
}
/* line 1856, ../sass/_template_specific.scss */
.hidden-menu #main-sections #main-sections-inner {
  width: 1000px;
}
/* line 1860, ../sass/_template_specific.scss */
.hidden-menu #main-sections .main-section-div-wrapper a {
  color: #32398D;
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  display: inline-block;
  border-top: 2px solid white !important;
}
/* line 1867, ../sass/_template_specific.scss */
.hidden-menu #main-sections .main-section-div-wrapper a:hover {
  border-top: 2px solid #32398D !important;
}
/* line 1873, ../sass/_template_specific.scss */
.hidden-menu #main-sections #section-active a {
  color: #32398D;
  border-top: 2px solid #32398D !important;
}
/* line 1879, ../sass/_template_specific.scss */
.hidden-menu #lang {
  margin-top: 20px;
  margin-left: 20px;
}
/* line 1883, ../sass/_template_specific.scss */
.hidden-menu #lang .arrow {
  display: inline-block;
  background: #32398D;
  float: right;
  width: 45px;
  height: 45.4px;
  margin-top: 0px;
  position: relative;
}
/* line 1892, ../sass/_template_specific.scss */
.hidden-menu #lang .arrow:before {
  content: "\f107";
  font-family: "FontAwesome";
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 22px;
}
/* line 1901, ../sass/_template_specific.scss */
.hidden-menu #lang #selected-language {
  background: rgba(0, 0, 0, 0.2);
}
/* line 1905, ../sass/_template_specific.scss */
.hidden-menu #lang ul li {
  background: #ffffff;
  text-align: center;
  width: 80px;
  font-size: 16px;
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 1917, ../sass/_template_specific.scss */
.hidden-menu #lang ul li:hover {
  border-bottom: 1px solid rgba(128, 128, 128, 0.33);
  background: #f0f0f0;
  width: 80px;
}
/* line 1923, ../sass/_template_specific.scss */
.hidden-menu #lang ul li a {
  color: #666 !important;
  text-decoration: none !important;
}
/* line 1929, ../sass/_template_specific.scss */
.hidden-menu .booking_top_button {
  cursor: pointer;
  background: #32398D;
  border-radius: 0px !important;
  border: 2px solid white;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  color: white;
  float: right;
  font-size: 16px;
  text-transform: uppercase;
  height: 21px;
  width: 110px;
  margin-top: 18px;
  padding: 15px 14px 10px;
  margin-left: 20px;
}
/* line 1946, ../sass/_template_specific.scss */
.hidden-menu .booking_top_button:hover {
  background: #252a67;
}

/* line 1953, ../sass/_template_specific.scss */
.vertical-align {
  vertical-align: middle;
  display: table-cell;
}

/* line 1958, ../sass/_template_specific.scss */
.vertical-align-wrapper {
  height: 100%;
  width: 100%;
  display: table;
}

/******************** tiny carousel ********************/
/* line 1966, ../sass/_template_specific.scss */
.carousel {
  overflow: hidden;
  clear: both;
  text-align: center;
  height: 100%;
  margin: 0 auto;
  width: 1140px;
}

/* line 1975, ../sass/_template_specific.scss */
.carousel #carousel_title {
  color: #1f265e;
  padding: 5px;
  margin-bottom: 10px;
  font-size: 35px;
  margin-top: 30px;
}

/* line 1983, ../sass/_template_specific.scss */
.carousel .viewport {
  width: 1080px;
  height: 175px;
  overflow: hidden;
  position: relative;
  float: left;
  margin-left: -2px;
}

/* line 1992, ../sass/_template_specific.scss */
.carousel .disable {
  visibility: hidden;
}

/* line 1996, ../sass/_template_specific.scss */
.carousel .overview {
  list-style: none;
  position: absolute;
  left: 0;
  top: 0;
  padding: 0;
  margin: 0;
}

/* line 2005, ../sass/_template_specific.scss */
.carousel .overview li {
  float: left;
  margin: 0 1px;
  height: 100%;
  text-align: center;
  font-size: 12px;
  width: 214px;
  position: relative;
}
/* line 2014, ../sass/_template_specific.scss */
.carousel .overview li:hover img {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

/* line 2020, ../sass/_template_specific.scss */
.carousel .overview li img {
  height: 175px;
  width: 358px;
  margin-bottom: -6px;
}

/* line 2026, ../sass/_template_specific.scss */
.carousel .overview li img:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

/* line 2031, ../sass/_template_specific.scss */
.carousel .buttons {
  float: left;
  text-indent: -999em;
  width: 29px;
  height: 175px;
  overflow: hidden;
  position: relative;
  margin: 0;
}

/* line 2041, ../sass/_template_specific.scss */
.carousel .prev {
  background: #32398D url("/img/parka/flecha_izquierda.png") no-repeat center;
  margin-right: 3px !important;
  margin-left: 0px;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;
}
/* line 2051, ../sass/_template_specific.scss */
.carousel .prev:hover {
  background: #1f265e url("/img/parka/flecha_izquierda.png") no-repeat center;
}

/* line 2056, ../sass/_template_specific.scss */
.carousel .next {
  background: #32398D url("/img/parka/flecha_derecha.png") no-repeat center;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;
}
/* line 2064, ../sass/_template_specific.scss */
.carousel .next:hover {
  background: #1f265e url("/img/parka/flecha_derecha.png") no-repeat center;
}

/* line 2069, ../sass/_template_specific.scss */
.carousel .disable {
  visibility: visible;
}

/* line 2073, ../sass/_template_specific.scss */
.bannerTitle {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.65);
  text-align: center;
  padding: 15px 15px;
  font-size: 14px;
  height: auto;
}

/* line 2086, ../sass/_template_specific.scss */
.gallery-carousel {
  margin-bottom: 50px;
}

/* line 2092, ../sass/_template_specific.scss */
html[lang="fr"] #booking-horizontal .wrapper_booking_button button, html[lang="fr"] .hidden-menu .booking_top_button {
  font-size: 12px;
}

/* line 2098, ../sass/_template_specific.scss */
.work-with-us .form-general {
  padding: 20px 20px 0;
  background: #f6f7f8;
  overflow: hidden;
  margin-bottom: 20px;
  text-align: left;
}
/* line 2105, ../sass/_template_specific.scss */
.work-with-us .form-general h3 {
  margin-bottom: 20px;
}
/* line 2109, ../sass/_template_specific.scss */
.work-with-us .form-general li {
  display: inline-block;
  width: 268px;
  margin-bottom: 10px;
  float: left;
}
/* line 2115, ../sass/_template_specific.scss */
.work-with-us .form-general li label {
  display: block;
  font-size: 12px;
  color: #8e8d8d;
}
/* line 2120, ../sass/_template_specific.scss */
.work-with-us .form-general li input, .work-with-us .form-general li textarea {
  border: none;
  width: 254px;
  padding: 10px 5px;
}
/* line 2126, ../sass/_template_specific.scss */
.work-with-us .form-general li select {
  width: 265px;
  background-color: white;
  padding: 10px 5px;
  height: 35px;
  border: none;
  -webkit-appearance: none;
  border-radius: 0;
}
/* line 2135, ../sass/_template_specific.scss */
.work-with-us .form-general li select#fechatotal {
  height: 75px;
}
/* line 2138, ../sass/_template_specific.scss */
.work-with-us .form-general li select#fechatotal option {
  margin-bottom: 2px;
}
/* line 2142, ../sass/_template_specific.scss */
.work-with-us .form-general li textarea {
  height: 13px;
  width: 524px;
}
/* line 2147, ../sass/_template_specific.scss */
.work-with-us .form-general li #check_from, .work-with-us .form-general li #check_till {
  background: white url("/img/holiy/calendar.png") no-repeat 235px;
}
/* line 2150, ../sass/_template_specific.scss */
.work-with-us .form-general li #check_date, .work-with-us .form-general li #end_date {
  background: white url("/img/holiy/calendar.png") no-repeat 100px;
}
/* line 2154, ../sass/_template_specific.scss */
.work-with-us .form-general li.comment-box {
  width: 100%;
}
/* line 2157, ../sass/_template_specific.scss */
.work-with-us .form-general .short {
  width: 132px;
}
/* line 2160, ../sass/_template_specific.scss */
.work-with-us .form-general .short input {
  width: 118px;
}
/* line 2164, ../sass/_template_specific.scss */
.work-with-us .form-general a {
  color: white;
  margin-top: 10px;
  display: inline-block;
}
/* line 2169, ../sass/_template_specific.scss */
.work-with-us .form-general .btn-corporate {
  font-size: 14px;
  padding: 5px 10px 2px;
  border-radius: 3px;
  cursor: pointer;
  float: right;
  background: #32398D;
}
/* line 2177, ../sass/_template_specific.scss */
.work-with-us .form-general span a {
  color: #32398D;
  font-size: 12px;
}
/* line 2181, ../sass/_template_specific.scss */
.work-with-us .form-general .form-bottom {
  display: inline-block;
  width: 400px;
}
/* line 2185, ../sass/_template_specific.scss */
.work-with-us .form-general .form-bottom p {
  margin-bottom: 0;
  line-height: 15px;
}
/* line 2189, ../sass/_template_specific.scss */
.work-with-us .form-general .form-bottom label.error {
  display: none !important;
}
/* line 2193, ../sass/_template_specific.scss */
.work-with-us .form-general .last {
  margin-top: 38px;
}
/* line 2196, ../sass/_template_specific.scss */
.work-with-us .form-general .last .form-bottom {
  margin-top: 10px;
}
/* line 2201, ../sass/_template_specific.scss */
.work-with-us .form-general .double_li_form {
  width: 536px;
}
/* line 2204, ../sass/_template_specific.scss */
.work-with-us .form-general .double_li_form input {
  width: 526px;
}
/* line 2212, ../sass/_template_specific.scss */
.work-with-us .form-general label.error {
  display: none !important;
}
/* line 2216, ../sass/_template_specific.scss */
.work-with-us .input-error {
  outline: 2px solid red !important;
}
/* line 2220, ../sass/_template_specific.scss */
.work-with-us .block-left {
  width: 540px;
  float: left;
  padding-right: 10px;
  margin-bottom: 20px;
}
/* line 2227, ../sass/_template_specific.scss */
.work-with-us .block-right {
  width: 540px;
  float: right;
  padding-left: 10px;
  margin-bottom: 20px;
}
/* line 2234, ../sass/_template_specific.scss */
.work-with-us .form-general.form-opinion li {
  display: block !important;
}
/* line 2238, ../sass/_template_specific.scss */
.work-with-us .form-general.form-opinion .btn-corporate {
  float: none !important;
  margin-bottom: 20px;
}
/* line 2243, ../sass/_template_specific.scss */
.work-with-us #explica {
  font-size: 11px;
}

/* line 2249, ../sass/_template_specific.scss */
header #social {
  display: inline-block;
  margin-top: 0;
}
/* line 2253, ../sass/_template_specific.scss */
header #social a {
  display: inline-block;
  vertical-align: middle;
  width: 45px;
  height: 45px;
  background: white;
  position: relative;
}
/* line 2262, ../sass/_template_specific.scss */
header #social a:hover .fa {
  color: #1f265e;
}
/* line 2267, ../sass/_template_specific.scss */
header #social a .fa {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: #32398D;
  font-size: 20px;
}

/* line 2276, ../sass/_template_specific.scss */
.custom_form_wrapper {
  display: block;
  width: 400px;
  margin: 0 auto 40px;
}
/* line 2282, ../sass/_template_specific.scss */
.custom_form_wrapper form input:not([type=checkbox]), .custom_form_wrapper form textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  border: 2px solid #888;
  display: inline-block;
  height: 40px;
  margin-bottom: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px;
  font-size: 16px;
}
/* line 2297, ../sass/_template_specific.scss */
.custom_form_wrapper form input:not([type=checkbox]).error, .custom_form_wrapper form textarea.error {
  border-color: red;
}
/* line 2302, ../sass/_template_specific.scss */
.custom_form_wrapper form label.error {
  display: none !important;
}
/* line 2306, ../sass/_template_specific.scss */
.custom_form_wrapper form .policy_form_wrapper {
  margin-bottom: 10px;
}
/* line 2309, ../sass/_template_specific.scss */
.custom_form_wrapper form .policy_form_wrapper input[type=checkbox] {
  width: auto;
  height: auto;
  display: inline-block;
  vertical-align: middle;
}
/* line 2316, ../sass/_template_specific.scss */
.custom_form_wrapper form .policy_form_wrapper a {
  display: inline-block;
  vertical-align: middle;
}
/* line 2322, ../sass/_template_specific.scss */
.custom_form_wrapper form textarea {
  height: 200px;
  resize: none;
}
/* line 2327, ../sass/_template_specific.scss */
.custom_form_wrapper form button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  background: #888;
  text-transform: uppercase;
  color: white;
  border: 0;
  height: 40px;
  font-size: 18px;
  cursor: pointer;
  -webkit-transition: opacity 0.4s;
  -moz-transition: opacity 0.4s;
  -ms-transition: opacity 0.4s;
  -o-transition: opacity 0.4s;
  transition: opacity 0.4s;
}
/* line 2341, ../sass/_template_specific.scss */
.custom_form_wrapper form button:hover {
  opacity: .8;
}

/* line 2348, ../sass/_template_specific.scss */
body.fenix-beach .babies_selector {
  width: auto;
}
/* line 2350, ../sass/_template_specific.scss */
body.fenix-beach .babies_selector label {
  color: white !important;
}

/* line 24, ../sass/styles_citymar-ciervo.scss */
#top-sections {
  width: 395px;
  margin-left: 0px;
}

/* line 29, ../sass/styles_citymar-ciervo.scss */
#lang-wrapper {
  width: 334px;
}

/* line 33, ../sass/styles_citymar-ciervo.scss */
.form-contact #contact-button:hover {
  background: #1f265e !important;
}

/* line 37, ../sass/styles_citymar-ciervo.scss */
form#my-bookings-form button#my-bookings-form-search-button,
form#my-bookings-form button#cancelButton {
  background: #32398D;
}
/* line 41, ../sass/styles_citymar-ciervo.scss */
form#my-bookings-form button#my-bookings-form-search-button:hover,
form#my-bookings-form button#cancelButton:hover {
  background: #1f265e;
}

/* line 46, ../sass/styles_citymar-ciervo.scss */
.wrapper_booking_button button {
  height: 45px;
}

/* line 50, ../sass/styles_citymar-ciervo.scss */
div#generic-wrapper {
  color: #8e8d8d;
  text-align: center;
  font-size: 17px;
  margin-bottom: 50px;
}

/* line 57, ../sass/styles_citymar-ciervo.scss */
.fResumenReserva {
  background: rgba(236, 236, 236, 0.59) !important;
  border: 3px solid #32398D !important;
}

/* line 62, ../sass/styles_citymar-ciervo.scss */
.alpha .txtCosteTotal {
  color: #32398D !important;
  font-family: inherit;
}

/* line 68, ../sass/styles_citymar-ciervo.scss */
body .datepicker_wrapper_element .header_datepicker {
  background: #F3D132;
}

/* line 72, ../sass/styles_citymar-ciervo.scss */
body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active {
  background: #F3D132 !important;
}

/* line 76, ../sass/styles_citymar-ciervo.scss */
.hidden-menu .booking_top_button, .hidden-menu #lang .arrow {
  background-color: #F3D132;
}

/* line 80, ../sass/styles_citymar-ciervo.scss */
.hidden-menu .booking_top_button {
  background: #F3D132;
}
/* line 83, ../sass/styles_citymar-ciervo.scss */
.hidden-menu .booking_top_button:hover {
  background: #f6dc62;
}

/* line 88, ../sass/styles_citymar-ciervo.scss */
footer {
  background: #636363;
}

/* line 92, ../sass/styles_citymar-ciervo.scss */
.offer-wrapper .offer-text-wrapper div.entradilla-oferta {
  height: 80px;
}

/* line 96, ../sass/styles_citymar-ciervo.scss */
#mainMenuDiv {
  width: 1015px;
}

/* line 100, ../sass/styles_citymar-ciervo.scss */
.forfait_link {
  background: #7b5e88;
  color: white;
  border-radius: 50%;
  height: 100px;
  width: 100px;
  position: relative;
  display: inline-block;
  float: right;
  margin-left: 110px;
  margin-top: 10;
  box-shadow: 0 0 6px 0 black;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -ms-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
/* line 114, ../sass/styles_citymar-ciervo.scss */
.forfait_link:hover {
  background: #662483;
}
/* line 118, ../sass/styles_citymar-ciervo.scss */
.forfait_link span {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 124, ../sass/styles_citymar-ciervo.scss */
.cycle-slideshow .content-cycle-banner {
  display: inline-block;
  width: calc(100%/3);
  height: 455px;
  float: left;
  overflow: hidden;
}
/* line 131, ../sass/styles_citymar-ciervo.scss */
.cycle-slideshow .content-cycle-banner img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
  vertical-align: middle;
}
