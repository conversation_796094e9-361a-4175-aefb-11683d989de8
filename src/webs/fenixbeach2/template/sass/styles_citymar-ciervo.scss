@import "compass";
@import "defaults";

$corporate_1: #32398D;
$corporate_2: #1f265e;

@import "plugins/fontawesomemin";
@import "plugins/mixins";
@import "plugins/owlcarousel";
@import "booking/booking_engine";
@import "booking/selectric";

@import "gallerys/gallery_1";
@import "location_and_contact";
@import "room_type_selector";

@import "rooms";
@import "offers";
@import "minigallery";
@import "fonts";

@import "template_specific";

#top-sections {
  width: 395px;
  margin-left: 0px;
}

#lang-wrapper {
  width: 334px;
}

.form-contact #contact-button:hover {
  background: $corporate_2 !important;
}

form#my-bookings-form button#my-bookings-form-search-button,
form#my-bookings-form button#cancelButton {
  background: $corporate-1;

  &:hover {
    background: $corporate-2;
  }
}

.wrapper_booking_button button {
  height: 45px;
}

div#generic-wrapper {
  color: #8e8d8d;
  text-align: center;
  font-size: 17px;
  margin-bottom: 50px;
}

.fResumenReserva {
  background: rgba(236, 236, 236, 0.59) !important;
  border: 3px solid $corporate_1 !important;
}

.alpha .txtCosteTotal {
  color: $corporate_1 !important;
  font-family: inherit;
}


body .datepicker_wrapper_element .header_datepicker {
  background: #F3D132;
}

body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active {
  background: #F3D132!important;
}

.hidden-menu .booking_top_button, .hidden-menu #lang .arrow {
  background-color: #F3D132;
}

.hidden-menu .booking_top_button {
  background: #F3D132;

  &:hover {
    background: lighten(#F3D132, 10%);
  }
}

footer {
  background: #636363;
}

.offer-wrapper .offer-text-wrapper div.entradilla-oferta {
  height: 80px;
}

#mainMenuDiv {
  width: 1015px;
}

.forfait_link {
  background: #7b5e88;
  color: white;
  border-radius: 50%;
  height: 100px;
  width: 100px;
  position: relative;
  display: inline-block;
  float: right;
  margin-left: 110px;
  margin-top: 10;
  box-shadow: 0 0 6px 0 black;
  @include transition(background, .4s);

  &:hover {
    background: #662483;
  }

  span {
    @include center_xy;
  }
}

.cycle-slideshow {
  .content-cycle-banner {
    display: inline-block;
    width: calc(100%/3);
    height: 455px;
    float: left;
    overflow: hidden;

    img {
      @include center_image;
      vertical-align: middle;
    }
  }
}