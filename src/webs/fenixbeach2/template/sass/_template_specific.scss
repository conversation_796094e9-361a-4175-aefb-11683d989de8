//body:not(.citymar-montblanc){
//.ui-widget-content{
//    background:white;
//  }
//  .ui-state-default{
//    border: 1px solid white !important;
//  }
//  .ui-datepicker-title{
//    color: white!important;
//  }
//  .ui-widget-header {
//    background: $corporate-1 !important;
//  }
//
//  .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
//    background: $corporate-1 !important;
//    color: white;
//  }
//
//  .ui-datepicker .ui-datepicker-prev,
//  .ui-datepicker .ui-datepicker-next{
//    background:rgb(184, 202, 209);
//  }
//}

body {

  .datepicker_wrapper_element .header_datepicker {
    background: $corporate_1;
  }

  .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active {
    background: #c76710 !important;
    color: white !important;
  }


    #booking-horizontal .boking_widget_inline .room {
      margin-bottom: 20px;

        .children_selector label{

            display: flex;
            justify-content: center;
        }
      }



}

//////////////////////////////////////////////
//GRAL STYLING
//////////////////////////////////////////////

body {
  position: relative;
  font-family: 'nexaregular';
  font-size: 12px;
  background-color: white;
  //color: white;

  a {
    //color: white;
    text-decoration: none;
  }

}

.boking_widget_inline .wrapper-new-web-support {
  display: block !important;
  font-weight: lighter;
  opacity: 1;
  border-top: 1px solid;
  width: 400px;
  margin: 0 auto;
  margin-top: 10px;
}

.title-section-wrapper {
  color: $corporate-1;
  text-align: center;
  font-size: 55px;
  font-weight: bolder;
  text-transform: uppercase;
  margin: 50px auto;

  .subtitle1 {
    font-family: nexabold;
    line-height: 59px;
  }

  .subtitle2 {
    font-weight: 100;
  }
}

#main_content p {
  padding: 10px 0px;
}

.description-section-wrapper {
  color: #8e8d8d;
  text-align: center;
  font-size: 17px;
  margin-bottom: 50px;

  p {
    font-weight: lighter;
    line-height: 29px;
  }
}

p.negrita {
  font-size: 18px;
  line-height: 29px;
  font-family: nexabold;
  color: rgb(81, 81, 81);
}

.fancy-title {
  text-align: center;
  font-size: 25px;
  margin-bottom: 10px;
  color: #af8551;
}

.popup_info_es, .popup_info_en {
  display: none;
}

.wrapper-new-web-support {
  line-height: 16px;
}

//////////////////////////////////////////////
//HEADER
//////////////////////////////////////////////

header {
  position: absolute;
  top: 0px;
  z-index: 100;
  width: 100%;
  text-align: center;
  min-height: 280px;
  padding-top: 60px;

}

#logoDiv {
  margin-top: -15px;
}

#top-sections {
  text-align: left;
  font-family: nexaregular, sans-serif;
  font-size: 12px;
  color: white;

  & > span {
    display: inline-block;
    background: white;
    padding: 16px 10px;
    margin-right: 5px;
    color: $corporate_1;
    font-family: "nexabold";
    font-weight: bold;
  }

  a {
    color: white;
  }

}

#lang-wrapper {

}

//LANGUAGE SELECT
#lang {
  float: right;
  cursor: pointer;
  width: 90px;

  #selected-language {

    background-color: rgba(255, 255, 255, 0.20);
    padding: 17px 10px 17px 7px;
    box-sizing: border-box;
    width: 45px;
    height: 45px;
    display: inline-block;
    color: white;
    font-size: 15px;
    font-family: nexabold;
    letter-spacing: 1px;
    text-transform: uppercase;

  }

  .arrow {
    display: inline-block;
    background: rgba(255, 255, 255, 1) url(/img/feni2/flecha_dorada_lang.png) no-repeat center center;
    float: right;
    width: 45px;
    height: 45px;
    margin-top: 0px;

  }

  ul li {
    background: #ffffff;
    text-align: center;
    width: 80px;
    font-size: 16px;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}

#language-selector-options {
  display: none;

  a {
    color: $gray-2;

    &:hover {
      color: $corporate-1;
    }
  }
}

//WEATHER
.weather {
  margin-right: 10px;

  width: 90px;
  float: right;

  .grados {
    float: right;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 17px 10px 17px 3px;
    box-sizing: border-box;
    width: 45px;
    height: 45px;
    display: inline-block;
    color: white;
    font-size: 14px;
    font-family: nexabold;
    letter-spacing: 1px;
  }
  .img_weather {
    float: right;
    background-color: rgba(255, 255, 255, 1);
    width: 45px;
    height: 45px;
    padding-top: 4px;
    box-sizing: border-box;

  }

}

//////////////////////////////////////////////
//HORIZONTAL BOOKING ENGINE
//////////////////////////////////////////////

#horizontal_booking_container {

}

/* A few more changes in the new booking engine */

#booking-horizontal {

  .boking_widget_inline .room_list_wrapper {

    margin-left: 0px !important;
    margin-right: 0px !important;

  }

  .date_box .date_year, .date_box .date_day {
    color: white !important;

  }

  .date_box {
    background-color: transparent;
    border-radius: 0px;
    border: 2px solid white;
  }

  .boking_widget_inline .stay_selection .entry_date_wrapper label, .boking_widget_inline .stay_selection .departure_date_wrapper label, .boking_widget_inline .stay_selection .rooms_number_wrapper label {
    color: white !important;
    font-size: 12px !important;
  }

  .selectric {
    background-color: transparent;
    color: white;
    border-radius: 0px;
    border: 2px solid white;

    .label {
      color: white !important;
    }

    .button {
      border-left: 1px solid white;
      border-radius: 0px;
      background-color: transparent !important;
    }

  }

  .boking_widget_inline .wrapper_booking_button {

    margin-top: 1px;
    float: left;
    margin-left: 20px;
  }

  .wrapper_booking_button button {
    cursor: pointer;
    background: #F3D132;
    height: 44px;
    padding: 10px;
    border-radius: 0px;
    border: 2px solid white;

  }

  .wrapper_booking_button button:hover {

    background: darken(#F3D132, 10%);

  }

  .wrapper_booking_button .promocode_input {
    background-color: transparent !important;
    width: 155px !important;
    border-radius: 0px;
    border: 2px solid white;
    color: white !important;

  }

  .promocode_input::-webkit-input-placeholder {
    color: white !important;
  }
  .promocode_input::-moz-placeholder {
    color: white !important;
  }
  .promocode_input:-moz-placeholder {
    color: white !important;
  }
  .promocode_input:-ms-input-placeholder {
    color: white !important;
  }

  //A few more changes for the booking engine HORIZONTAL

  .boking_widget_inline {
    background-color: transparent !important;
  }

  .booking_form_title {
    background-color: transparent !important;
  }

  .booking_form {
    background-color: transparent !important;
    @include display_flex;
    justify-content: center;
  }

  .boking_widget_inline .room .room_title, .boking_widget_inline .room .adults_selector label, .boking_widget_inline .room .children_selector label {
    color: white !important;
    font-size: 12px;
  }

  .boking_widget_inline .stay_selection {
    //with type room selector must be: margin-left 0px
    //margin-left: 0px !important;
    margin-left: 0 !important;
    margin-top: 5px;
  }

}

.destination_wrapper {
  margin-top: 5px;
  margin-right: -9px;
  margin-left: 22px;
  display: inline-block;

  .right_arrow {
    cursor: pointer;
  }

  div#placeholder {
    color: white;
  }

  input {
    color: white;
  }

  input::-webkit-input-placeholder {
    color: white;
  }
  input:-moz-placeholder {
    color: white;
  }
  input::-moz-placeholder {
    color: white;
  }
  input:-ms-input-placeholder {
    color: white;
  }

}

.destination_wrapper input {
  height: 38px;
  width: 175px;
  padding-left: 10px;
  background: transparent;
  margin-right: 20px;
  border: 2px solid white;
  border-radius: 0px;
  cursor: pointer;

  div#placeholder {
    color: white !important;
  }
}


.destination_wrapper {
  .destination_field .destination {
    width: 200px;
  }
  label {
    color: white;
    margin-right: 15px;
  }
}

.hotel_selector {
  position: absolute;
  display: none;
  top: 47px;
  z-index: 1;
  cursor: pointer;
  ul {
    li {
      border: 2px solid white;
      border-bottom-width: 0;
      display: block;
      padding: 5px 10px;
      color: white;
      cursor: pointer;
      font-size: 11px;
      text-transform: uppercase;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }
      &:last-of-type {
        border-bottom-width: 2px;
      }
    }
  }
}

.roomtype_selector {
  left: 168px;
  top: 71px;
}

.roomtype_selector .title_selector {
  background: rgba(0, 0, 0, 0.53);
  padding-right: 27px !important;
  padding-left: 29px;
}

.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: -75px;
  bottom: 24px;
}

//booking engine in fancybox (for selects!)

.fancybox-inner {
  overflow: visible !important;

  .destination_wrapper {
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    input::-webkit-input-placeholder {
      color: rgb(175, 133, 83);
    }
    input:-moz-placeholder {
      color: rgb(175, 133, 83);
    }
    input::-moz-placeholder {
      color: rgb(175, 133, 83);
    }
    input:-ms-input-placeholder {
      color: rgb(175, 133, 83);
    }

    label {
      color: rgb(131, 131, 131);
    }

    input {
      width: 94% !important;
      background: white;
      color: rgb(175, 133, 83);

    }

    .right_arrow {
      background: rgb(175, 134, 82) url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center !important;
      margin-right: -22px;
    }

  }

  .promocode_input {
    width: 120px;
    font-size: 12px;
  }
  .wrapper_booking_button button {
    font-size: 13px;
    width: 125px;
  }

  .booking_form {
    background: #ededed !important;
  }

  .selectric {
    border-radius: 0px !important;
  }

  .date_box {
    border-radius: 0px !important;
    .date_year {
      color: rgb(176, 135, 87);
    }
  }

  .wrapper_booking_button .promocode_input {
    border-radius: 0px !important;
  }

  .wrapper_booking_button button {
    border-radius: 0px !important;
  }

  .button {
    border-radius: 0px !important;
  }
}

//////////////////////////////

/** CALENDAR DATEPICKER**/

.ui-widget-header {
  background: $corporate-1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate-1;
  color: white;
}

.date_box .date_year {
  color: white;
}

#wrapper_booking_fancybox {
  .date_box .date_year {
    color: grey;
  }
  .date_day {
    margin-top: 4px;
  }
}

//////////////////////////////////////////////
//Slider revolution new arrows
//////////////////////////////////////////////
.tp-leftarrow.default {
  background: url(/img/#{$base_web}/left_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}

.tp-rightarrow.default {
  background: url(/img/#{$base_web}/right_flexslider.png) no-Repeat 0 0 !important;
  width: 80px !important;
  height: 80px !important;
}

.tp-bullets {
  display: none;
}

.overlay_slider {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, transparent 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, transparent 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6) 30%, rgba(0, 0, 0, 0.6) 50%, rgba(0, 0, 0, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#a6000000', endColorstr='#00000000', GradientType=0);
  z-index: -1;
}

#slider_container {
  position: relative;
  margin-bottom: 10px;

  .tp-bgimg {
    position: relative;
    z-index: -1;
  }
}

.slider_inner {
  height: 600px !important;
  overflow: hidden;

  .slider_image {
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    max-width: initial;
  }
}

.forcefullwidth_wrapper_tp_banner {
  min-height: 650px !important;
}

.tp-banner-container {
  min-height: 650px;
}

#botton-slider {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 30;
  position: absolute;
  bottom: 0px;

  display: block;
  width: 205px;
  height: 45px;
  box-sizing: border-box;
  left: 50%;
  text-align: center;
  padding-top: 20px;
  margin-left: -103px;

  img {

  }
}

.cartela_home {
  position: relative;
  margin-top: 70px;

  .slider-text-1, .slider-text-2, .slider-text-3, .slider-text-4 {
    position: absolute;
    width: 100%;
  }

  .slider-text-1 {
    top: 29px;
  }
  .slider-text-2 {
    top: 90px;
  }
  .slider-text-3 {
    top: 177px;
    left: 358px;
    width: 19%;
  }
  .slider-text-4 {
    top: 177px;
    right: 355px;
    width: 19%;
  }
}

//////////////////////////////////////////////
/* MAIN DISTRIBUTED MENU */
//////////////////////////////////////////////

#mainMenuDiv {

  font-family: nexaregular, sans-serif;
  font-size: 13px;

  z-index: 99;
  position: relative;
  top: 27px;
  clear: both;

  width: 915px;
}

#mainMenuDiv a {
  padding: 5px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  color: $white;
  display: inline-block;
  font-size: 14px;
}

#mainMenuDiv a:hover {
  border-top: 2px solid white !important;
  padding: 3px 8px 7px;
}

#section-active a {
  padding: 3px 8px 7px;
  text-decoration: none;
  text-transform: uppercase;
  border-top: 2px solid white !important;
  display: inline-block;
}

.main-section-div-wrapper a:hover {
  border-top: 2px solid white !important;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper a {
  line-height: 38px;
  text-transform: uppercase;

}

/////////////////////////////////////
/* BIG BANNERS LINES */
/////////////////////////////////////
#wrapper_main_content {
  display: block;
  overflow: visible;
}

#wrapper-main-banners {
  margin-bottom: 10px;
  overflow: auto;
}

.wrapper-big-banner {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%;

}

.wrapper-big-banner:before {
  content: '';
  display: block;
  padding-top: 100%; /* initial ratio of 1:1*/
}

.big-banner-title {
  margin-top: 10px;
  line-height: 60px;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 40px;
  text-transform: uppercase;
}

.square-icono h4.big-banner-title {
  font-weight: 100;
}

.big-banner-moreinfo {
  color: white;
  text-decoration: none;
  font-size: 19px;
  margin-top: 15px;
  display: block;
}

.span-underline {
  width: 60px;
  border-bottom: 3px solid rgba(255, 255, 255, 0.50);;
  text-align: center;
  display: block;
  margin: 0 auto;
}

.banner-special-wrapper {
  margin-bottom: 10px;
  overflow: auto;
}

img.icono-bigbanner {
  margin-bottom: 20px;
}

#triangulo-left {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-right: 60px solid rgb(175, 134, 82);
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  margin-left: -9.0%;
  z-index: 4;
}

#triangulo-right {
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-left: 60px solid black;
  border-bottom: 40px solid transparent;
  position: absolute;
  top: 40%;
  bottom: 0px;
  right: -9.0%;
  z-index: 4;
}

.banner-link {
  z-index: 1 !important;
}

.wrapper-big-banner.square-background {
  z-index: 3;
}

.container-text-banner {
  position: absolute;
  top: 39%;
  bottom: 0px;
  vertical-align: middle;
  left: 0px;
  right: 0px;
  text-align: center;
}

.square-icono .container-text-banner {
  padding: 0px 40px;
  top: 20% !important;
}

div#wrapper-main-bannersX2 {
  margin-bottom: 10px;
  overflow: auto;

  .wrapper-bannerX2 {
    width: 50%;
    float: left;
    position: relative;
    overflow: hidden;

    &:before {
      content: "";
      display: block;
      padding-top: 50%;
    }
  }

  .banner-text-inside {
    position: absolute;
    top: 15%;
    vertical-align: middle;
    right: 63px;
    text-align: center;
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.59);
    width: 37%;
    height: 63%;
    padding: 0px 15px;
    color: white;

    .big-banner-title {
      font-size: 40px;
    }

    .big-banner-description {
      margin-top: 8px;
      font-size: 33px;
      font-weight: 100;
      line-height: 40px;
    }
  }
}

.title-banners-cycle {
  text-align: center;
  font-size: 30px;
  text-transform: uppercase;
  margin: 60px 0px 30px;
  color: $corporate-1;
}

.content-cycle-banner {
  position: relative !important;
  z-index: 1 !important;
}

.cycle-description {
  position: absolute !important;
  left: 0px;
  color: white;
  top: 80px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  font-size: 24px;
  display: block;
  text-align: center;
  text-transform: uppercase;
  width: 300px;
  background: rgba(black, .6);
  padding: 20px;
  box-sizing: border-box;

  .bold {
    font-family: nexabold;
  }
}

.cycle-pager {
  text-align: center;
  font-size: 90px;
  color: $gray-3;

  span {
    cursor: pointer;
  }
  span.cycle-pager-active {
    color: $corporate-1;
  }
}

/////////////////////////////////////
/******************FOOTER***************/
/////////////////////////////////////
footer {
  background-color: $corporate-2;
  clear: both;
}

.wrapper_footer_columns {
  margin: 0px auto;
  padding-top: 20px;
}

.footer_column {
  font-family: nexaregular, sans-serif;
  font-size: 13px;
  line-height: 23px;
  color: white;
  width: 285px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  height: auto;
  border-left: 1px solid white;
  text-align: center;
  box-sizing: border-box;
  padding: 0 15px;
}

.footer_column a {
  font-size: 14px;
  line-height: 23px;
  color: white;
  text-decoration: none;
  font-weight: 100;

  &.link_work {
    text-transform: uppercase;
    text-decoration: underline;
  }
}

#footer_column_description {
  margin-bottom: 10px;
}

.footer_column a:hover {
  color: darken($gray-4, 10%);
}

footer .last {
  border-left: 1px solid white;
  width: 282px !important;
  text-align: left;
  padding: 0px 26px;
}

.footer_column_title {
  font-weight: 500;
  color: white;
  font-size: 18px;
  margin-bottom: 5px;
}

.copyright-footer {
  line-height: 46px;
}

#title_newsletter {
  font-weight: 500;
  color: white;
  font-size: 18px;
}

label#suscEmailLabel {
  font-size: 14px;
  font-weight: 100;
}

#suscEmail {
  width: 98%;
  background-color: white;
  border: none;
  height: 24px;
  color: $corporate-1;
  border-radius: 3px;
  font-size: 14px;
  margin: 10px 0;
}

.button_newsletter {
  width: 90px;
  height: 23px;
  text-transform: uppercase;
  background: $corporate-1;
  outline: none;
  font-weight: bolder;
  margin-left: 81px;
  padding-top: 2px;
  border: none;
  color: white;
  font-size: 14px;
}

div.button_newsletter:hover {
  background: darken($corporate-1, 10%);
  cursor: pointer;
}

.newsletter_wrapper label.error {
  margin-left: 100px;
}

.newsletter_container {
  width: auto;
}

.newsletter_checkbox {
  font-size: 12px;

  a {
    text-decoration: underline;
    font-size: 12px;
  }
}

input#promotions, input#privacy {
  float: left;
}

label.error {
  //position: absolute;
  margin-bottom: 8px;
  font-size: 16px;
  margin-top: 3px;
}

label.error[for="privacy"] {
  position: absolute;
  margin-top: 25px;
}

#form_events .styled-select label.error {
  background: white !important;
}

#social {
  margin-top: 20px;

  a {
    display: inline-block;
    position: relative;
    width: 20px;
    height: 20px;
    background: white;
    vertical-align: middle;

    &:hover {
      .fa {
        color: $corporate_2;
      }
    }

    .fa {
      @include center_xy;
      color: $corporate_1;
      font-size: 12px;
    }
  }
}

#social span {
  position: relative;
  font-weight: 100;
  display: inline-block;
  vertical-align: middle;
}

#social img {
  margin-left: 6px;
  padding-bottom: 2px;
  height: 28px;
  width: 28px;

  &:hover {
    opacity: 0.5;
  }
}

.footer_column h3 {
  font-size: 19px;
  color: white;
}

#footer {
  color: white;
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  padding-bottom: 20px;

  p {
    text-align: center;
  }
}

#footer a {
  text-decoration: none;
  color: white;
}

#footer_bottom_text {
  font-size: 14px;
  line-height: 14px;
}

.copyright-footer img {
  margin: 0 5px;
}

#google_plus_one {
  text-align: center;
}

#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

#facebook_like {
  text-align: center;
  margin-top: 10px;
}

#facebook_like iframe {
  height: 21px;
  width: 103px;
}

//////////////
//Events
//////////////
.know-more-text {
  line-height: 27px;
  font-size: 17px;
}

.event_wrapper {
  color: black;
  margin-bottom: 40px;
  margin-top: 10px;
  padding-bottom: 40px;
  border-bottom: 1px solid rgb(237, 237, 237);

  .event_img {
    width: 281px !important;
    margin: 0px 5px;
    .event_img img {
      width: 281px;
      height: 281px;
    }
  }

  .event_date {
    width: 120px;
    margin: 0px;

    .event_day, .event_month, .event_year {
      background: #7b7b7b;
      margin-bottom: 5px;
      text-align: center;
      color: white;
      padding: 13px 0px;
      font-size: 22px;
    }
  }

  .event_main_info {
    width: 678px;
    background: #f3f3f3;
    height: 281px;
    padding: 30px;
    box-sizing: border-box;
    position: relative;
    margin: 0.5px 5px;

    .event-title {
      text-transform: uppercase;
      color: rgb(175, 133, 83);
      font-size: 19px;
      margin-bottom: 15px;
      font-weight: bolder;
    }

    .event-description {
      line-height: 28px;
      font-size: 16px;
      font-weight: lighter;
      color: gray;
    }

    .event-buttons-wrappers {
      position: absolute;
      bottom: 40px;

      a {
        background: #af8553;
        padding: 19px 46px;
        font-size: 17px;

        &:hover {
          opacity: 0.7;
        }
      }
    }

  }

}

//////////////////////////
////Galeria
//////////////////////////

.filter-offers {
  font-size: 40px;
  text-align: center;

  .active {
    background: darken($corporate-1, 10%);
  }

  .filter-hotel {
    margin-right: 0.5%;
    float: left;
  }

  .filter-apartamentos {
    float: left;
  }

  li {
    cursor: pointer;
    background: $corporate-1;
    width: 49.75%;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 24px;
    padding: 16px 0px;
    font-weight: bold;
    &:hover {
      background: darken($corporate-1, 10%);
    }
  }

}

.gallery-images {
  margin-bottom: 60px;
}

ul.gallery_1 li {
  padding: 2px 1px !important;
  width: 25% !important;
  //height: 280px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  cursor: pointer;

  .crop {
    height: 280px !important;

    a img {
      //height: 280px !important;
    }
  }
}

//////////////////////////////
////Mis Reservas
/////////////////////////////

.my-booking-text {
  color: black;
  text-align: center;
  line-height: 29px;
  font-size: 16px;
}

form#my-bookings-form {
  margin: 50px auto;
  text-align: center;

  label {
    color: black;
    text-align: left;
    font-size: 16px;
  }

  input {
    text-align: center;
    margin-bottom: 15px;
    margin-top: 5px;
    font-size: 16px;
    display: initial;
    width: 100%;
    border: 0px !important;
    height: 30px;
    background-color: #e1e1e1;
    color: dimgrey;
  }

  button#my-bookings-form-search-button, button#cancelButton {
    cursor: pointer;
    background: #af8553;
    border-radius: 0px !important;
    border: 2px solid white;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    color: white;
    font-size: 16px;
    text-transform: uppercase;
    height: 40px;
    width: 110px;
    padding: 9px 14px 10px;

    &:hover {
      background: #7c5a3e;
    }
  }

  button#cancelButton {
    width: 215px;
    display: none;
  }
}

.fResumenReserva {
  border: 3px solid #AF8553 !important;
  background: rgba(175, 133, 83, 0.59) !important;
  padding: 4px 10px 20px 10px !important;

}

form#my-bookings-form button#cancelButton {
  width: 213px !important;
  margin: 22px auto !important;
}

.alpha {
  margin-left: -104px !important;

  .txtCosteTotal {
    color: rgb(108, 86, 60) !important;
  }
}

/* Mis reservas corpo */
#my-bookings-form {
  #reservation {
    .modify_reservation_widget {
      margin: auto;
      margin-top: 40px;
      margin-bottom: 0;
      padding: 20px;

      #info_ninos {
        display: none !important;
      }

      #contenedor_opciones {
        margin: 0 auto 10px;

        .ninos-con-babies {
          margin-right: 15px;
        }
      }

      #contenedor_fechas {
        text-align: center;

        #fecha_entrada, #fecha_salida {
          display: inline-block;
          float: none;
        }
      }

      #contenedor_habitaciones {
        text-align: center;

        label {
          display: inline-block;
          float: none;
        }

        select {
          display: inline-block;
          float: none;
        }
      }

      #envio {
        text-align: center;
      }
    }

    .my-bookings-booking-info {
      margin: 40px auto 0;

      .fResumenReserva {
        margin: auto;
      }
    }
  }
  #modify-button-container {
    display: none;
  }

  #my-bookings-form-fields {
    label {
      display: block;
      text-align: center;
      text-transform: uppercase;
      color: #4B4B4B;
      font-weight: 100;
    }

    input, select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      display: block;
      width: 350px;
      margin: 10px auto;
      height: 40px;
      border-radius: 0;
      text-align: center;
      font-size: 14px;
      border: 1px solid #DDD;
    }

    select {
      padding: 0 0 0 15px;
    }

    ul {
      text-align: center;
      margin-top: 30px;

      li {
        display: inline-block;
        width: 200px;
        vertical-align: middle;

        button {
          height: 40px;
          text-transform: uppercase;
          font-size: 16px;
          color: white;
          border: 0;
          cursor: pointer;
          width: 100%;
          font-weight: 400;
          background: $corporate_1;
          @include transition(background, .4s);

          &.modify-reservation {
            background: $corporate_1;

            &:hover {
              background: darken($corporate_1, 10%);
            }
          }

          &.searchForReservation {
            background: $corporate_2;

            &:hover {
              background: darken($corporate_2, 10%);
            }
          }
        }
      }
    }
  }

  #cancelButton {
    display: none;
    background: $corporate_2;
    height: 40px;
    text-transform: uppercase;
    font-size: 16px;
    color: white;
    border: 0;
    cursor: pointer;
    width: 200px;
    font-weight: 100;
    margin: 40px auto 0;
    @include transition(background, .4s);

    &:hover {
      background: darken($corporate_2, 10%);
    }
  }
}

////*Gallery Mosaic*///////

.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 137%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;

  img {
    width: 100%;
    height: 97%;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 0px auto 70px;
}

///////////////////////////
//Media Querys
///////////////////////////

@media (max-width: 1760px) {
  .big-banner-title {
    font-size: 30px;
  }

  .banner-text-inside {

    .big-banner-title {
      font-size: 27px !important;
      line-height: 42px;
    }

    .big-banner-description {
      font-size: 22px !important;
    }
  }
}

@media (max-width: 1480px) {
  div#wrapper-main-bannersX2 .banner-text-inside .big-banner-description {
    line-height: 25px;
  }
  .banner-text-inside {
  }
}

@media(max-width: 1360px) {
  .banner-text-inside {
  }
}

@media (max-width: 1370px) {
  .big-banner-title {
    font-size: 20px;
    line-height: 40px;
  }

  .banner-text-inside {
  }

  div#wrapper-main-bannersX2 .banner-text-inside .big-banner-title {
  }
}

@media (max-width: 1233px) {
  .banner-text-inside .big-banner-title {
    line-height: 30px;
  }
}

@media (min-width: 1920px) {
  #triangulo-left {
    margin-left: -6%;
  }

  #triangulo-right {
    margin-right: 2%;
  }
}

//Special Changes

#bannerx2-opac {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}

#bannerx2-opac:hover {
  opacity: 0.5;
}

.arrow-wrapper {
  position: relative;
  overflow: visible;
  float: left;
  width: 25%;
}

.arrow-wrapper:before {
  content: "";
  display: block;
}

.wrapper-big-banner {
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
  overflow: hidden;
}

.zoom {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.wrapper-for-hide {
  position: relative;
  min-height: 285px;
  min-width: 285px;
  float: left;
  color: white;
  width: 100%; /* desired width */
  overflow-y: hidden;
  overflow-x: hidden;
}

.wrapper-for-hide:before {
  content: "";
  display: block;
}

.hidden-menu {
  z-index: 999;
  display: none;
  background: white;
  position: fixed;
  width: 100%;
  min-width: 1140px;
  top: 0px;
  height: 90px;
  box-shadow: 0px 1px 7px black;

  #logoDiv {
    width: 115px;
    margin-top: -6px;
  }

  #main-sections {
    margin-top: 20px;

    #main-sections-inner {
      width: 1000px;
    }

    .main-section-div-wrapper a {
      color: $corporate-1;
      padding: 5px 8px 7px;
      text-decoration: none;
      text-transform: uppercase;
      display: inline-block;
      border-top: 2px solid white !important;
      &:hover {
        border-top: 2px solid $corporate-1 !important;

      }
    }

    #section-active a {
      color: $corporate-1;
      border-top: 2px solid $corporate-1 !important;
    }
  }

  #lang {
    margin-top: 20px;
    margin-left: 20px;

    .arrow {
      display: inline-block;
      background: $corporate-1;
      float: right;
      width: 45px;
      height: 45.4px;
      margin-top: 0px;
      position: relative;

      &:before {
        content: "\f107";
        font-family: "FontAwesome";
        color: white;
        @include center_xy;
        font-size: 22px;
      }
    }

    #selected-language {
      background: rgba(0, 0, 0, 0.2);
    }

    ul li {
      background: #ffffff;
      text-align: center;
      width: 80px;
      font-size: 16px;
      padding: 5px;
      cursor: pointer;
      display: block;
      border-bottom: 1px solid #EEE;
      color: #666;
      border-top: 1px solid #FFF;

      &:hover {
        border-bottom: 1px solid rgba(128, 128, 128, 0.33);
        background: #f0f0f0;
        width: 80px;
      }

      a {
        color: #666 !important;
        text-decoration: none !important;
      }
    }
  }
  .booking_top_button {
    cursor: pointer;
    background: $corporate-1;
    border-radius: 0px !important;
    border: 2px solid white;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    color: white;
    float: right;
    font-size: 16px;
    text-transform: uppercase;
    height: 21px;
    width: 110px;
    margin-top: 18px;
    padding: 15px 14px 10px;
    margin-left: 20px;

    &:hover {
      background: darken($corporate-1, 10%);
    }
  }

}

.vertical-align {
  vertical-align: middle;
  display: table-cell;
}

.vertical-align-wrapper {
  height: 100%;
  width: 100%;
  display: table;
}

/******************** tiny carousel ********************/

.carousel {
  overflow: hidden;
  clear: both;
  text-align: center;
  height: 100%;
  margin: 0 auto;
  width: 1140px;
}

.carousel #carousel_title {
  color: $corporate_2;
  padding: 5px;
  margin-bottom: 10px;
  font-size: 35px;
  margin-top: 30px;
}

.carousel .viewport {
  width: 1080px;
  height: 175px;
  overflow: hidden;
  position: relative;
  float: left;
  margin-left: -2px;
}

.carousel .disable {
  visibility: hidden;
}

.carousel .overview {
  list-style: none;
  position: absolute;
  left: 0;
  top: 0;
  padding: 0;
  margin: 0;
}

.carousel .overview li {
  float: left;
  margin: 0 1px;
  height: 100%;
  text-align: center;
  font-size: 12px;
  width: 214px;
  position: relative;

  &:hover img {
    opacity: 0.6;
    filter: alpha(opacity=60);
  }
}

.carousel .overview li img {
  height: 175px;
  width: 358px;
  margin-bottom: -6px;
}

.carousel .overview li img:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}

.carousel .buttons {
  float: left;
  text-indent: -999em;
  width: 29px;
  height: 175px;
  overflow: hidden;
  position: relative;
  margin: 0;
}

.carousel .prev {
  background: $corporate_1 url("/img/parka/flecha_izquierda.png") no-repeat center;
  margin-right: 3px !important;
  margin-left: 0px;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;

  &:hover {
    background: $corporate_2 url("/img/parka/flecha_izquierda.png") no-repeat center;
  }
}

.carousel .next {
  background: $corporate_1 url("/img/parka/flecha_derecha.png") no-repeat center;
  -webkit-transition: background .2s ease-in-out;
  -moz-transition: background .2s ease-in-out;
  -ms-transition: background .2s ease-in-out;
  -o-transition: background .2s ease-in-out;
  transition: background .2s ease-in-out;

  &:hover {
    background: $corporate_2 url("/img/parka/flecha_derecha.png") no-repeat center;
  }
}

.carousel .disable {
  visibility: visible;
}

.bannerTitle {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.65);
  text-align: center;
  padding: 15px 15px;
  font-size: 14px;
  height: auto;

}

.gallery-carousel {
  margin-bottom: 50px;
}

// Languages
html[lang="fr"] {
  #booking-horizontal .wrapper_booking_button button, .hidden-menu .booking_top_button {
    font-size: 12px;
  }
}

.work-with-us {
  .form-general {
    padding: 20px 20px 0;
    background: $corporate-4;
    overflow: hidden;
    margin-bottom: 20px;
    text-align: left;

    h3 {
      margin-bottom: 20px;
    }

    li {
      display: inline-block;
      width: 268px;
      margin-bottom: 10px;
      float: left;

      label {
        display: block;
        font-size: 12px;
        color: #8e8d8d;
      }
      input, textarea {
        border: none;
        width: 254px;
        padding: 10px 5px;
      }

      select {
        width: 265px;
        background-color: white;
        padding: 10px 5px;
        height: 35px;
        border: none;
        -webkit-appearance: none;
        border-radius: 0;
      }
      select#fechatotal {
        height: 75px;

        option {
          margin-bottom: 2px;
        }
      }
      textarea {
        height: 13px;
        width: 524px;
      }

      #check_from, #check_till {
        background: white url("/img/holiy/calendar.png") no-repeat 235px;
      }
      #check_date, #end_date {
        background: white url("/img/holiy/calendar.png") no-repeat 100px;
      }
    }
    li.comment-box {
      width: 100%;
    }
    .short {
      width: 132px;

      input {
        width: 118px;
      }
    }
    a {
      color: white;
      margin-top: 10px;
      display: inline-block;
    }
    .btn-corporate {
      font-size: 14px;
      padding: 5px 10px 2px;
      border-radius: 3px;
      cursor: pointer;
      float: right;
      background: $corporate_1;
    }
    span a {
      color: $corporate-1;
      font-size: 12px;
    }
    .form-bottom {
      display: inline-block;
      width: 400px;

      p {
        margin-bottom: 0;
        line-height: 15px;
      }
      label.error {
        display: none !important;
      }
    }
    .last {
      margin-top: 38px;

      .form-bottom {
        margin-top: 10px;
      }
    }

    .double_li_form {

      width: 536px;
      input {
        width: 526px;
      }

    }

  }

  .form-general label.error {
    display: none !important;
  }

  .input-error {
    outline: 2px solid red !important;
  }

  .block-left {
    width: 540px;
    float: left;
    padding-right: 10px;
    margin-bottom: 20px;
  }

  .block-right {
    width: 540px;
    float: right;
    padding-left: 10px;
    margin-bottom: 20px;
  }

  .form-general.form-opinion li {
    display: block !important;
  }

  .form-general.form-opinion .btn-corporate {
    float: none !important;
    margin-bottom: 20px;
  }

  #explica {
    font-size: 11px;
  }
}

header {
  #social {
    display: inline-block;
    margin-top: 0;

    a {
      display: inline-block;
      vertical-align: middle;
      width: 45px;
      height: 45px;
      background: white;
      position: relative;

      &:hover {
        .fa {
          color: $corporate_2;
        }
      }

      .fa {
        @include center_xy;
        color: $corporate_1;
        font-size: 20px;
      }
    }
  }
}

.custom_form_wrapper {
  display: block;
  width: 400px;
  margin: 0 auto 40px;

  form {
    input:not([type=checkbox]), textarea {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      width: 100%;
      border: 2px solid #888;
      display: inline-block;
      height: 40px;
      margin-bottom: 10px;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      padding: 10px;
      font-size: 16px;

      &.error {
        border-color: red;
      }
    }

    label.error {
      display: none !important;
    }

    .policy_form_wrapper {
      margin-bottom: 10px;

      input[type=checkbox] {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }

      a {
        display: inline-block;
        vertical-align: middle;
      }
    }

    textarea {
      height: 200px;
      resize: none;
    }

    button {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      width: 100%;
      background: #888;
      text-transform: uppercase;
      color: white;
      border: 0;
      height: 40px;
      font-size: 18px;
      cursor: pointer;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }
    }
  }
}
body.fenix-beach{
    .babies_selector{
        width: auto;
        label{
            color: white!important;
        }
    }
}