///////////////////////////////////////////
/////* LOCATION AND CONTACT SECTION*/////
///////////////////////////////////////////
#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;

  .location-info, .form-contact {
    width: 520px !important;
  }

}

.location-info-and-form-wrapper h1,
.form-contact h2 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px $gray-3 !important;
  margin-bottom: 30px;
  color: $corporate_1;
  padding-bottom: 3px;
}

.location-info{
  h1{
    padding-bottom: 0px!important;
  }
}

.location-info-and-form-wrapper {
  p, strong {
    color: dimgrey;
  }
}

.location-info {
  p {
    margin-bottom: 10px;
    font-weight: 300;
  }
  strong {
    font-weight: bold;
  }
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin: 20px;
  text-align: center;

  iframe {
    margin-bottom: 25px;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
  text-color: dimgrey !important;
  //text-decoration: underline !important;
    //text-color: $gray-4 !important;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;
  font-size: 16px;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #d1d1d1;
  color: dimgrey;
  padding-left: 3px;
  font-size: 16px;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #a0a0a0;
  color: dimgrey;
  text-align: center;
  padding-left: 3px;

}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

div#wrapper_content {
  margin: 50px auto 0px;
}

.location-info {
  color: black;
  font-size: 14px;
  font-weight: lighter;
  line-height: 28px;
}

.form-contact #contact-button:hover {
  background: rgb(118, 90, 57) !important;
}

.location-info-and-form-wrapper h1,
.form-contact h2 {
  font-family: nexabold;
}

.form-contact #contact .contInput textarea {
  background-color: #D1D1D1;
  color: dimgrey;
  text-align: left;
  font-size: 16px;
}

.form-contact .check_privacy {
    width: auto !important;
    margin-left: 0px !important;
    margin-right: 10px !important;
    &+.title {
        text-decoration: underline !important;
        a {
            color: dimgrey !important;
            font-size: 15px;
        }
    }
}