.cycle_banners_wrapper {
    display: inline-block;
    width: 100%;
    margin-top: 50px;
    padding: 60px;
    background-color: white;

  .cycle_element {
    display: block;
    width: 100%;
    height: 495px;
    position: relative;

    &:nth-child(odd) {
      .cycle_image {
        float: left;
      }

      .cycle_content {
        float: right;
      }
    }

    &:nth-child(even) {
      .cycle_image {
        float: right;
      }

      .cycle_content {
        float: left;
      }
    }

    .cycle_image {
      display: inline-block;
      width: 50%;
      height: 495px;
      position: relative;
      overflow: hidden;

      img {
        @include center_image;
        max-width: none;
        min-width: 100%;
        min-height: 100%;
        width: auto;
      }
    }

    .cycle_content {
      display: inline-block;
      width: 50%;
      height: 495px;
      background-color: white;
      box-sizing: border-box;
      padding: 25px;
      position: relative;

      .cycle_title {
        font-size: 23px;;
        color: $corporate_1;
        margin-bottom: 40px;
        font-weight: 400;
        text-align: center;
      }

      .cycle_description {
        font-size: 15px;
        line-height: 26px;
        text-align: center;
        font-weight: 300;
        color: #1f4a59;
      }

      .cycle_baner_see_more {
        text-align: center;
        a {
          position: relative;
          display: block;
          vertical-align: middle;
          margin: 30px auto;
          text-align: center;
          background-color: $corporate_2;
          font-size: 20px;
          text-transform: uppercase;
          color: white;
          padding: 10px 0;
          font-weight: 300;

          span {
            position: relative;
          }

          &:before {
            content: '';
            @include full_size;
            background-color: rgba(0,0,0,0.3);
            width: 0;
            @include transition(all, .6s);
            }

          &:hover {
            &:before {
              width: 100%;
            }
          }
        }
      }
    }
  }
}