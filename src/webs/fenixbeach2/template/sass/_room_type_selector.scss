.roomtype_selector {
  @include border-radius(5px);
  @include box-shadow(#414141 1px 1px 7px);

  position: absolute;
  padding: 10px;
  padding-bottom: 0;
  display: none;
  width: 355px;
  left: 290px;
  top: 80px;
  background: white;
  z-index: 1000;

  .roomtype_selector_inner {

    position: relative;

    .close_roomtype_selector {
      @include border-top-right-radius(5px);
      @include border-bottom-right-radius(5px);
      position: absolute;
      top: -10px;
      right: -10px;
      background: $corporate_1 url(/static_1/images/booking/flecha_motor_izq.png) no-repeat center center;
      height: 156px;
      width: 20px;
    }
  }

  .roomtype_selector_option {
    width: 168px;
    height: 146px;
    float: left;
    cursor: pointer;

    &.right {
      margin-left: 10px;
    }

    &.class-Habitacion {
      background: url(/img/#{$base_web}/room_selectors/motor_hotel.jpg) no-repeat top center;
      h3 {

      }
    }

    &.class-Apartamento {
      background: url(/img/#{$base_web}/room_selectors/motor_apart.jpg) no-repeat top center;
      h3 {

      }
    }

  }

  .roomtype_selector_option_name h3 {
    font-family: nexabold;
    margin-top: 89px;
    background: $gray-4;
    color: $corporate_1;
    line-height: 35px;
    text-transform: uppercase;
    padding-left: 37px;

    span {
      font-family: nexaregular;
    }

  }

  .title_selector {
    position: relative;

    width: 101px;
    padding-right: 46px !important;

    top: 72px;
    left: 6px;
    height: 60px;

    background-color: black;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#000, endColorstr=#000);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
    background: rgba(0, 0, 0, 0.30);

    padding-left: 10px;
    padding-top: 5px;
    padding-right: -126px;
    color: white;
    font-size: 16px;

  }

}

.destination_wrapper .right_arrow {
  background: url(/static_1/images/booking/flecha_motor.png) no-repeat center center;
  right: 25px;
  top: 20px;
  height: 35px;
  width: 35px;
  border-radius: 0px;
  border-left: 1px solid white;
}