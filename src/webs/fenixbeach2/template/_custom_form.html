<div class="custom_form_wrapper">
    <form action="" id="custom_form" method="postc">
        <input type="text" id="form_name" name="form_name" placeholder="{{T_nombre_y_apellidos}}">
        <input type="text" id="form_email" name="form_email" placeholder="{{T_email}}">
        <textarea type="text" id="form_message" name="form_message" placeholder="{{T_comentarios}}"></textarea>
        <div class="policy_form_wrapper">
            <input type="checkbox" name="form_policy" id="form_policy">
            <a href="/{{language}}/?sectionContent=politica-de-privacidad.html" class="myFancyPopup fancybox.iframe">{{T_lopd}}</a>
        </div>
        {% if custom_email %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ custom_email|safe }}">
        {% endif %}
        <button id="custom_form_send">{{T_enviar}}</button>
    </form>
</div>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script>
    $(window).load(function(){
        $("#custom_form").validate({
            rules: {
                form_name: "required",
                form_email: {
                    required: true,
                    email: true
                },
                form_message: "required",
                form_policy: "required"
            }
        });

        $("#custom_form_send").click(function(e){
            e.preventDefault();
            if ($("#custom_form").valid()) {
                $.post("/utils/?action=contact", {
                    name: $("#form_name").val(),
                    email: $("#form_email").val(),
                    section: "{{ sectionToUse.sectionName|safe }}",
                    {% if custom_email %}
                        destination_email: $("#custom_form #destination_email").val(),
                    {% endif %}
                    comments: $("#form_message").val()
                }, function() {
                    alert("{{ custom_form_text|safe }}");
                    $("#custom_form")[0].reset();
                })
            }
        });
    })
</script>