/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #0c7d9d;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #0c7d9d url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #0c7d9d;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #0c7d9d;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #0c7d9d;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #0c7d9d;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 405, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 408, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 412, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 418, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
/* line 431, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 439, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
  color: gray;
}
/* line 445, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 455, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 463, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 468, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 473, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 482, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 486, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 499, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 503, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 506, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #0c7d9d;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #0c7d9d url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/*========= General ========*/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'Roboto';
}

/* line 6, ../sass/_template_specific.scss */
#content {
  background: white;
}

/* line 10, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 14, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 18, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #0c7d9d !important;
}

/* line 22, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #0c7d9d !important;
  color: white;
}

/*========== Header =========*/
/* line 28, ../sass/_template_specific.scss */
header {
  background: white;
}

/* line 32, ../sass/_template_specific.scss */
#wrapper-header {
  position: relative;
}

/* line 36, ../sass/_template_specific.scss */
.free_wifi {
  position: absolute;
  right: 320px;
  top: 16px;
  font-weight: 300;
  font-size: 18px;
  color: #7B797A;
}
/* line 44, ../sass/_template_specific.scss */
.free_wifi strong {
  font-weight: 500;
}
/* line 48, ../sass/_template_specific.scss */
.free_wifi img {
  vertical-align: middle;
}

/* line 53, ../sass/_template_specific.scss */
.text_oficial {
  float: left;
  margin-top: 10px;
  color: white;
  padding-left: 19px;
  font-weight: 300;
  margin-right: 27px;
}
/* line 60, ../sass/_template_specific.scss */
.text_oficial strong {
  font-weight: 500;
}

/* line 66, ../sass/_template_specific.scss */
#logoDiv {
  z-index: 100;
  width: 255px;
  height: 78px;
  position: absolute;
  text-align: center;
  padding: 10px 0px;
  background-color: white;
}
/* line 75, ../sass/_template_specific.scss */
#logoDiv a {
  text-decoration: none;
}
/* line 79, ../sass/_template_specific.scss */
#logoDiv img {
  height: 100%;
}

/* line 84, ../sass/_template_specific.scss */
#social a img {
  width: 28px;
  height: 28px;
}

/* line 91, ../sass/_template_specific.scss */
.top-menu-wrapper-2-container {
  padding-top: 9px;
}

/* line 95, ../sass/_template_specific.scss */
div#top-menu-wrapper-2 {
  position: relative;
  height: 60px;
}
/* line 99, ../sass/_template_specific.scss */
div#top-menu-wrapper-2 #lang {
  font-size: 15px;
}

/* line 105, ../sass/_template_specific.scss */
#web_oficial, #lang, #profesionales_wrapper {
  float: right;
}

/* line 109, ../sass/_template_specific.scss */
#web_oficial {
  padding: 5px;
  position: absolute;
  right: 195px;
  color: #0c7d9d;
}
/* line 115, ../sass/_template_specific.scss */
#web_oficial strong {
  font-weight: bold;
  color: #0c7d9d;
}
/* line 119, ../sass/_template_specific.scss */
#web_oficial .txt_negrita {
  font-weight: bold;
}

/* line 126, ../sass/_template_specific.scss */
span#selected-language {
  padding-top: 3px;
  font-weight: 300;
  color: grey;
  display: block;
  float: left;
}

/* line 134, ../sass/_template_specific.scss */
#lang {
  display: inline-block;
  width: 150px;
  height: 35px;
  padding: 5px 1px 3px 8px;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.63);
  cursor: pointer;
  color: #949090 !important;
  background: #f3f1f2;
  margin-left: 20px;
  vertical-align: middle;
  text-align: left;
  right: 135px;
  position: absolute;
  margin-top: 8px;
}
/* line 151, ../sass/_template_specific.scss */
#lang .arrow {
  display: inline-block;
  background: #0c7d9d url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  float: right;
  width: 27px;
  height: 28px;
  margin-top: -2px;
  margin-right: 2px;
  border-radius: 3px;
}
/* line 161, ../sass/_template_specific.scss */
#lang .arrow #selected-language {
  color: black !important;
}

/* line 167, ../sass/_template_specific.scss */
#language-selector-options {
  display: none;
  margin-top: 1px;
}
/* line 171, ../sass/_template_specific.scss */
#language-selector-options a {
  color: #787878;
  text-decoration: none;
}
/* line 175, ../sass/_template_specific.scss */
#language-selector-options a:hover {
  color: #0c7d9d;
}
/* line 180, ../sass/_template_specific.scss */
#language-selector-options li.language-option-flag {
  background: white;
  font-weight: 300;
  padding: 5px 9px;
}
/* line 184, ../sass/_template_specific.scss */
#language-selector-options li.language-option-flag:hover {
  font-weight: 500;
}
/* line 188, ../sass/_template_specific.scss */
#language-selector-options li.language-option-flag:hover > a {
  color: #0c7d9d;
}

/* line 194, ../sass/_template_specific.scss */
ul#language-selector-options {
  z-index: 10;
  margin-left: -9px;
  width: 150px;
  position: relative;
  display: none;
  margin-top: 29px;
}

/* TICKS */
/* line 205, ../sass/_template_specific.scss */
#ticksContainer {
  float: left !important;
  display: block;
  width: 100%;
  text-align: right;
}

/* line 212, ../sass/_template_specific.scss */
#ticksContainer, #social, #mini-top-menu {
  float: right;
}

/* line 216, ../sass/_template_specific.scss */
#tick1 {
  background: url("/img/eurod/ticks/pago_icon.png") no-repeat 0;
}

/* line 220, ../sass/_template_specific.scss */
#tick2 {
  background: url("/img/eurod/ticks/gastos_icon.png") no-repeat 0;
}

/* line 224, ../sass/_template_specific.scss */
#tick3 {
  background: url("/img/eurod/ticks/segura_icon.png") no-repeat 0;
}

/* line 228, ../sass/_template_specific.scss */
.ticks {
  display: inline-block;
  text-align: left;
  line-height: 10px;
  text-transform: uppercase;
  padding-left: 36px;
  font-size: 9px;
  color: #0c7d9d;
  width: 73px;
  font-weight: bold;
  margin-top: 5px;
  margin-left: 10px;
  padding-top: 4px;
}

/* line 243, ../sass/_template_specific.scss */
#top-menu-wrapper-1 {
  height: 33px;
  width: 1140px;
  margin: auto;
  padding-left: 212px;
  box-sizing: border-box;
}
/* line 250, ../sass/_template_specific.scss */
#top-menu-wrapper-1 .top-menu-wrapper-1-inside {
  background: #787c7d;
  display: block;
  float: right;
  height: 35px;
}

/* line 258, ../sass/_template_specific.scss */
div#mini-top-menu {
  padding: 7px 0px;
  display: block;
  margin-left: 75px;
  height: 17px;
  padding-top: 8px;
}
/* line 265, ../sass/_template_specific.scss */
div#mini-top-menu a {
  text-decoration: none;
}
/* line 269, ../sass/_template_specific.scss */
div#mini-top-menu span:first-child {
  margin-right: 8px;
}
/* line 273, ../sass/_template_specific.scss */
div#mini-top-menu span {
  color: white;
  font-size: 13px;
  font-weight: 100;
  opacity: 1;
  text-transform: uppercase;
}
/* line 280, ../sass/_template_specific.scss */
div#mini-top-menu span:hover {
  opacity: 0.6;
}
/* line 285, ../sass/_template_specific.scss */
div#mini-top-menu .my_booking_header {
  margin-right: 15px !important;
}

/* line 291, ../sass/_template_specific.scss */
div#social {
  margin-left: 8px;
  margin-bottom: 0px;
  margin-top: 12px;
}
/* line 296, ../sass/_template_specific.scss */
div#social img:hover {
  opacity: 0.6;
}
/* line 300, ../sass/_template_specific.scss */
div#social a {
  text-decoration: none;
}

/* line 306, ../sass/_template_specific.scss */
.separator_line {
  border-left: 1px solid white;
  margin-right: 8px;
}

/* line 311, ../sass/_template_specific.scss */
div#mini-top-menu span.no_hover:hover {
  opacity: 0.8;
}

/*========= Banners x3 ========*/
/* line 317, ../sass/_template_specific.scss */
.bannersx3 {
  display: table;
  margin-bottom: 25px;
  margin-top: 40px;
}
/* line 322, ../sass/_template_specific.scss */
.bannersx3 .banner {
  height: 200px;
  overflow: hidden;
  position: relative;
  margin-bottom: 16px;
}
/* line 327, ../sass/_template_specific.scss */
.bannersx3 .banner img {
  width: 100%;
}
/* line 331, ../sass/_template_specific.scss */
.bannersx3 .banner .banner_title {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 80%;
  text-align: center;
  display: block;
  margin: auto;
  text-transform: uppercase;
  font-size: 19px;
  color: white;
  font-weight: 700;
  line-height: 20px;
  height: 52px;
}
/* line 348, ../sass/_template_specific.scss */
.bannersx3 .banner .banner_title div {
  text-transform: none;
  font-size: 14px;
  font-weight: 300;
}
/* line 355, ../sass/_template_specific.scss */
.bannersx3 .banner .separator {
  width: 155px;
  border-top: 3px solid white;
  margin: 4px auto;
}

/*========== Menu ===========*/
/* line 365, ../sass/_template_specific.scss */
#main_menu {
  background: #0c7d9d;
}

/* line 369, ../sass/_template_specific.scss */
div#main-sections {
  height: 37px;
}

/* line 373, ../sass/_template_specific.scss */
#mainMenuDiv {
  font-size: 12px;
  position: relative;
  clear: both;
  font-weight: 700;
}
/* line 379, ../sass/_template_specific.scss */
#mainMenuDiv a {
  text-decoration: none;
  text-transform: uppercase;
  color: white;
  display: inline-block;
  text-align: center;
}

/* line 391, ../sass/_template_specific.scss */
#mainMenuDiv a:hover {
  color: #0c7d9d;
}

/* line 395, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 399, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 403, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
  height: 30px;
  top: 37px;
  z-index: 30;
  left: 0px;
}

/* line 411, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 415, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 420, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
}

/* line 424, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 431, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/* line 435, ../sass/_template_specific.scss */
.main-section-div-wrapper {
  width: auto;
  text-align: center;
  padding: 0 10px;
  font-weight: lighter;
  position: relative;
}
/* line 442, ../sass/_template_specific.scss */
.main-section-div-wrapper:hover {
  background: white;
}
/* line 444, ../sass/_template_specific.scss */
.main-section-div-wrapper:hover a {
  color: #0c7d9d !important;
}
/* line 449, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 38px;
  text-transform: uppercase;
  font-weight: lighter;
  font-size: 14px;
}

/* line 457, ../sass/_template_specific.scss */
.main-section-div-wrapper.section-4 {
  width: 120px;
}

/* line 461, ../sass/_template_specific.scss */
.main-section-div-wrapper.reservar {
  padding-right: 0;
}

/* line 465, ../sass/_template_specific.scss */
.main-section-div-wrapper a
.section-6 a {
  width: 180px !important;
}
/* line 468, ../sass/_template_specific.scss */
.main-section-div-wrapper a
.section-6 a:hover {
  width: 180px !important;
}

/* line 473, ../sass/_template_specific.scss */
#section-active a {
  text-decoration: none;
  text-transform: uppercase;
  text-align: center;
  color: white;
  font-weight: bolder;
  display: inline-block;
}

/*====== Subsections ========*/
/* line 484, ../sass/_template_specific.scss */
.main-section-subsection a {
  min-width: 215px !important;
  display: block;
  color: #0c7d9d;
  background-color: white;
}

/* line 491, ../sass/_template_specific.scss */
.main-section-subsection a:hover {
  color: white;
  background-color: #0c7d9d;
}

/*======== Booking Widget =======*/
/* line 497, ../sass/_template_specific.scss */
.booking_form {
  background: rgba(0, 0, 0, 0.43);
}

/* line 501, ../sass/_template_specific.scss */
.booking_widget {
  top: 70px;
}

/* line 505, ../sass/_template_specific.scss */
.selectric .button {
  background: #1e5a65 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

/* line 509, ../sass/_template_specific.scss */
.wrapper_booking_button button {
  background: #f0b31a;
  width: 150px;
  font-size: 15px;
  cursor: pointer;
}
/* line 515, ../sass/_template_specific.scss */
.wrapper_booking_button button:hover {
  background: #787c7d;
}

/* line 520, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input {
  width: 100px;
  font-size: 9px;
}
/* line 524, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input::-webkit-input-placeholder {
  color: #1e5a65;
  font-weight: bolder;
}
/* line 529, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input:-moz-placeholder {
  color: #1e5a65;
  font-weight: bolder;
}
/* line 534, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input::-moz-placeholder {
  color: #1e5a65;
  font-weight: bolder;
}
/* line 539, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input:-ms-input-placeholder {
  color: #1e5a65;
  font-weight: bolder;
}

/* line 545, ../sass/_template_specific.scss */
.date_box .date_day {
  color: #1e5a65;
  font-size: 15px;
  padding-top: 2px;
}

/* line 551, ../sass/_template_specific.scss */
.date_box .date_year {
  color: gray;
}

/* line 555, ../sass/_template_specific.scss */
.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
  margin-bottom: 4px;
}

/* line 559, ../sass/_template_specific.scss */
.booking_form {
  padding: 10px 20px 20px 20px;
}

/* line 563, ../sass/_template_specific.scss */
.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  color: white;
  font-weight: 300;
}

/* line 568, ../sass/_template_specific.scss */
.selectric {
  background: none;
}

/* line 572, ../sass/_template_specific.scss */
.selectric .label {
  margin-left: 0px;
  text-align: center;
  background: white;
  width: 39px;
  border-radius: 5px;
  line-height: 41px;
}

/* line 581, ../sass/_template_specific.scss */
.selectric .button {
  border: 3px solid white;
  margin-top: 0px;
  border-radius: 6px;
}

/*======== Automatic Content ======*/
/* line 588, ../sass/_template_specific.scss */
.automatic_content {
  margin-bottom: 50px;
}
/* line 591, ../sass/_template_specific.scss */
.automatic_content h3.main-title {
  font-size: 30px;
  text-align: center;
  color: #0c7d9d;
  font-weight: 100;
  margin-bottom: 30px;
}
/* line 598, ../sass/_template_specific.scss */
.automatic_content h3.main-title strong {
  font-weight: bolder;
}
/* line 603, ../sass/_template_specific.scss */
.automatic_content .section-description {
  text-align: center;
  color: #787c7d;
  font-weight: 300;
  line-height: 30px;
}

/*======== Blocks x3 Wrapper ======*/
/* line 612, ../sass/_template_specific.scss */
.blocksx3_wrapper {
  background: #e0e2e1;
  padding: 40px 0px;
}
/* line 616, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element {
  background: white;
}
/* line 619, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element a {
  text-decoration: none;
  color: #787c7d;
}
/* line 624, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element img {
  width: 100%;
}
/* line 628, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element .block_description {
  text-align: center;
  font-size: 13px;
  color: #787c7d;
  font-weight: 300;
  padding: 10px 38px;
  min-height: 100px;
}
/* line 636, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element .block_description h4 {
  color: #0c7d9d;
  font-weight: 100;
  font-size: 15px;
  font-style: italic;
  margin-bottom: 5px;
}
/* line 643, ../sass/_template_specific.scss */
.blocksx3_wrapper .block_element .block_description h4 strong {
  font-weight: bolder;
  font-style: normal;
}

/*========== Footer ========*/
/* line 653, ../sass/_template_specific.scss */
footer {
  padding: 40px 0px 18px;
  background: white;
  color: #787c7d;
}
/* line 658, ../sass/_template_specific.scss */
footer h3.footer_column_title {
  font-size: 12px;
  font-weight: bold;
}
/* line 663, ../sass/_template_specific.scss */
footer #footer_column_description {
  font-size: 13px;
  font-weight: lighter;
  margin-top: 6px;
}
/* line 670, ../sass/_template_specific.scss */
footer #newsletter #title_newsletter {
  display: none;
}
/* line 673, ../sass/_template_specific.scss */
footer #newsletter #suscEmailLabel {
  display: none !important;
}
/* line 677, ../sass/_template_specific.scss */
footer #newsletter button#newsletter-button {
  background: #0c7d9d;
  color: white;
  border: 0px;
  padding: 5px;
  width: 78px;
  border-radius: 4px;
  cursor: pointer;
  text-transform: uppercase;
}
/* line 687, ../sass/_template_specific.scss */
footer #newsletter button#newsletter-button:hover {
  background: #787c7d;
}
/* line 691, ../sass/_template_specific.scss */
footer #newsletter .newsletter_checkbox {
  font-size: 11px;
}
/* line 693, ../sass/_template_specific.scss */
footer #newsletter .newsletter_checkbox a {
  color: #0c7d9d;
}
/* line 698, ../sass/_template_specific.scss */
footer #newsletter input#suscEmail {
  margin-top: 5px;
  background-color: #e0e2e1;
  border: 0px;
  height: 20px;
  width: 220px;
  margin-bottom: 6px;
  border-radius: 4px;
}
/* line 709, ../sass/_template_specific.scss */
footer .footer_column:not(.last) {
  border-right: 1px solid #787c7d;
  width: 263px !important;
  padding-left: 35px !important;
  box-sizing: border-box;
  min-height: 100px;
}
/* line 717, ../sass/_template_specific.scss */
footer .footer_column.last {
  padding-left: 25px !important;
  box-sizing: border-box;
}

/* line 723, ../sass/_template_specific.scss */
.footer-copyright {
  text-align: center;
  color: #787c7d;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}
/* line 730, ../sass/_template_specific.scss */
.footer-copyright a {
  text-decoration: none;
  color: #787c7d;
}
/* line 734, ../sass/_template_specific.scss */
.footer-copyright a:hover {
  opacity: 0.8;
}

/*========== Hoteles =======*/
/* line 741, ../sass/_template_specific.scss */
.hide_me {
  display: none;
}

/* line 745, ../sass/_template_specific.scss */
.hotel_content_wrapper {
  margin-top: 30px;
}

/* line 749, ../sass/_template_specific.scss */
.hotel {
  background: #F7F6F6;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
  min-height: 395px;
}
/* line 757, ../sass/_template_specific.scss */
.hotel .location_button {
  height: 29px;
  display: block;
  width: 29px;
  background: url(/img/eurod/location.png) no-repeat center;
  background-size: 29px;
  position: absolute;
  right: 91px;
  cursor: pointer;
  top: -7px;
}

/* line 770, ../sass/_template_specific.scss */
.hotel-description {
  padding: 20px 25px 0;
  font-size: 14px;
  color: #787c7d;
  font-weight: 300;
}

/* line 777, ../sass/_template_specific.scss */
.hotel h3 {
  line-height: 16px;
  text-align: left;
}

/* line 782, ../sass/_template_specific.scss */
.hotel-description .sub-description-hotel {
  margin-top: 10px;
}

/* line 786, ../sass/_template_specific.scss */
.hotel-description .description-hotel {
  margin-top: 10px;
}

/* line 790, ../sass/_template_specific.scss */
.hotel .hotel-links {
  position: absolute;
  right: 21px;
  top: 233px;
}
/* line 795, ../sass/_template_specific.scss */
.hotel .hotel-links a {
  color: white;
  background: #0c7d9d;
  text-decoration: none;
  padding: 7px 18px;
}
/* line 800, ../sass/_template_specific.scss */
.hotel .hotel-links a img {
  padding-right: 10px;
}

/* line 808, ../sass/_template_specific.scss */
.hotel img {
  width: 100%;
  height: 210px;
}
/* line 813, ../sass/_template_specific.scss */
.hotel h3.title-module {
  text-transform: uppercase;
  text-decoration: none;
  color: #0c7d9d;
  font-size: 17px !important;
  margin: 0px !important;
  margin-bottom: 15px !important;
  font-weight: 200;
  line-height: inherit !important;
  text-align: left !important;
}
/* line 825, ../sass/_template_specific.scss */
.hotel .destino {
  font-weight: 500;
}
/* line 829, ../sass/_template_specific.scss */
.hotel .more_pictures {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0px;
  right: 0px;
}

/*======= Ofertas ======*/
/* line 839, ../sass/_template_specific.scss */
#wrapper_rooms_hotels {
  margin-top: 40px;
}

/* line 843, ../sass/_template_specific.scss */
.rooms {
  margin-bottom: 10px;
  margin-left: 5px !important;
  margin-right: 5px !important;
  position: relative;
  background: #F6F7F7;
}

/* line 851, ../sass/_template_specific.scss */
.rooms .img-offer {
  float: left;
  width: 275px;
  height: 210px;
}

/* line 857, ../sass/_template_specific.scss */
.room_block_description {
  overflow: hidden;
  min-height: 160px;
  max-height: 160px;
}

/* line 863, ../sass/_template_specific.scss */
.hotel_room_title {
  font-size: 18px;
  font-weight: bold;
  margin: 5px 0 10px 20px;
  color: #0c7d9d;
}

/* line 870, ../sass/_template_specific.scss */
.hotel_room_description {
  padding: 0px 20px;
  font-weight: 300;
  font-size: 13px;
}

/* line 876, ../sass/_template_specific.scss */
.hotel_room_description h3 {
  font-weight: lighter;
  font-size: 19px;
  text-transform: uppercase;
  color: #0c7d9d;
  padding: 0 20px;
}

/* line 884, ../sass/_template_specific.scss */
.rooms_dates {
  margin-left: 20px;
  font-size: 14px;
  line-height: 17px;
  color: #bebebe;
}

/* line 891, ../sass/_template_specific.scss */
.rooms_dates span {
  text-transform: uppercase;
  color: #0c7d9d;
}

/* line 896, ../sass/_template_specific.scss */
.buttons_rooms {
  float: left;
  margin-top: 5px;
  position: absolute;
  right: 20px;
}

/* line 903, ../sass/_template_specific.scss */
.buttons_rooms .arrow_right {
  padding: 5px 5px 5px;
  border-radius: 2px;
  float: left;
  margin-left: 5px;
  margin-right: 5px;
}

/* line 911, ../sass/_template_specific.scss */
.buttons_rooms button {
  cursor: pointer;
  border-radius: 3px;
  color: white;
  border: none;
  text-transform: uppercase;
  float: left;
  margin-top: 5px;
  height: 32px;
  width: 100px;
  background-color: #0c7d9d;
}

/* line 924, ../sass/_template_specific.scss */
.buttons_rooms button:hover {
  background-color: #f0b31a;
}

/* line 928, ../sass/_template_specific.scss */
.button-promotions-inner {
  color: white;
  text-decoration: none;
}

/* line 933, ../sass/_template_specific.scss */
.hotel_room_description .txt-description {
  display: none;
}

/* line 937, ../sass/_template_specific.scss */
.title-full-promo {
  text-align: center;
  color: #0c7d9d;
  font-size: 30px;
  font-weight: bold;
}

/* line 945, ../sass/_template_specific.scss */
.full-promo-description {
  padding: 20px;
}
/* line 949, ../sass/_template_specific.scss */
.full-promo-description h3 {
  display: none;
}
/* line 953, ../sass/_template_specific.scss */
.full-promo-description .rooms_dates {
  display: none;
}

/* line 958, ../sass/_template_specific.scss */
.filter-promo {
  margin-top: 50px;
  background: rgba(58, 91, 106, 0.7);
  text-align: center;
  padding: 10px 0px;
}
/* line 964, ../sass/_template_specific.scss */
.filter-promo li {
  display: inline-block;
  margin: 0px 10px;
}
/* line 968, ../sass/_template_specific.scss */
.filter-promo a {
  text-decoration: none;
  color: #bebebe;
  font-size: 16px;
  padding: 5px;
}
/* line 974, ../sass/_template_specific.scss */
.filter-promo a:hover {
  color: white;
}
/* line 978, ../sass/_template_specific.scss */
.filter-promo a.one-night {
  background: url(/img/eurod/1_moon.png) no-repeat left center;
  padding-left: 25px;
}
/* line 982, ../sass/_template_specific.scss */
.filter-promo a.two-night {
  background: url(/img/eurod/2_moon.png) no-repeat left center;
  padding-left: 50px;
}
/* line 986, ../sass/_template_specific.scss */
.filter-promo a.more-night {
  background: url(/img/eurod/3_moon.png) no-repeat left center;
  padding-left: 75px;
}
/* line 990, ../sass/_template_specific.scss */
.filter-promo a.offers {
  background: url(/img/eurod/oferta.png) no-repeat left center;
  padding-left: 25px;
}
/* line 994, ../sass/_template_specific.scss */
.filter-promo .filtro-active {
  color: white;
}

/*========= Location and Contact ========*/
/* line 1000, ../sass/_template_specific.scss */
#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}
/* line 1006, ../sass/_template_specific.scss */
#wrapper_content_contact .location-info, #wrapper_content_contact .form-contact {
  width: 520px !important;
}

/* line 1012, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  color: #0c7d9d;
}

/* line 1024, ../sass/_template_specific.scss */
.location-info-and-form-wrapper p, .location-info-and-form-wrapper strong {
  color: dimgrey;
}

/* line 1029, ../sass/_template_specific.scss */
.location-info {
  text-align: left;
}
/* line 1031, ../sass/_template_specific.scss */
.location-info p {
  margin-bottom: 10px;
  font-weight: 300;
}
/* line 1035, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1040, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1044, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
}
/* line 1047, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper iframe {
  margin-bottom: 25px;
}

/* line 1054, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1058, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1062, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1066, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1071, ../sass/_template_specific.scss */
.form-contact #contactContent .bordeInput {
  width: auto;
  margin-right: 5px;
}

/* line 1076, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;
}

/* line 1084, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
  padding-left: 3px;
}

/* line 1095, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #e1e1e1;
  color: dimgrey;
  text-align: left;
  padding: 15px;
  box-sizing: border-box;
}

/* line 1108, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

/* line 1113, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: #0c7d9d !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

/* line 1129, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #f0b31a !important;
}

/* line 1133, ../sass/_template_specific.scss */
.iframe_maps {
  display: table;
  padding: 20px 0px;
}

/* line 1138, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  margin: 20px 0px;
  box-sizing: border-box;
  display: table;
  padding: 20px 0px;
}

/* line 1145, ../sass/_template_specific.scss */
.how-to-go {
  cursor: pointer;
  margin-bottom: 15px;
}
/* line 1149, ../sass/_template_specific.scss */
.how-to-go p {
  font-weight: bold;
  font-size: 14px;
  color: #0c7d9d;
}
/* line 1155, ../sass/_template_specific.scss */
.how-to-go:hover {
  opacity: 0.8;
}

/* line 1160, ../sass/_template_specific.scss */
ul.location_destiny {
  margin-top: 20px;
  list-style: circle;
  padding-left: 20px;
}

/*======= Mis reservas =======*/
/* line 1168, ../sass/_template_specific.scss */
form#my-bookings-form {
  margin-top: 30px;
}

/* line 1172, ../sass/_template_specific.scss */
#wrapper_services {
  display: none;
}

/* line 1176, ../sass/_template_specific.scss */
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

/* line 1181, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  text-align: center;
}
/* line 1184, ../sass/_template_specific.scss */
#my-bookings-form-fields label {
  display: block;
  color: #0c7d9d;
  text-transform: uppercase;
}
/* line 1189, ../sass/_template_specific.scss */
#my-bookings-form-fields input, #my-bookings-form-fields .bordeSelect {
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
  width: 250px;
  height: 15px;
  padding: 5px;
  text-align: center;
  color: black;
  border: none;
  background: #e6e6e6;
}
/* line 1202, ../sass/_template_specific.scss */
#my-bookings-form-fields .bordeSelect {
  -webkit-appearance: none;
  color: #787c7d;
  width: 263px !important;
  border-radius: 0px !important;
  height: 25px !important;
  background: #e6e6e6 url(/img/checn/select_down.png) no-repeat 240px;
}
/* line 1210, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button {
  width: 260px;
  color: white;
  background-color: #0c7d9d;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
}
/* line 1219, ../sass/_template_specific.scss */
#my-bookings-form-fields #my-bookings-form-search-button:hover {
  background-color: #f0b31a;
}

/* line 1225, ../sass/_template_specific.scss */
button#cancelButton {
  width: 260px;
  color: white;
  background-color: #0c7d9d;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
}
/* line 1234, ../sass/_template_specific.scss */
button#cancelButton:hover {
  background-color: #f0b31a;
}

/* line 1239, ../sass/_template_specific.scss */
#cancel-button-container {
  text-align: center;
}
/* line 1242, ../sass/_template_specific.scss */
#cancel-button-container #cancelButton {
  width: 260px;
  color: white;
  background-color: #0c7d9d;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;
  display: none;
  margin-left: 470px;
}
/* line 1253, ../sass/_template_specific.scss */
#cancel-button-container #cancelButton:hover {
  background-color: #f0b31a;
}

/* line 1259, ../sass/_template_specific.scss */
#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}

/* line 1263, ../sass/_template_specific.scss */
.section-title + div {
  color: #918f8f;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
  margin-top: 25px;
}

/* line 1272, ../sass/_template_specific.scss */
.content_element {
  margin-top: 25px;
}
/* line 1275, ../sass/_template_specific.scss */
.content_element .section-title {
  font-size: 30px;
  text-align: center;
  color: #0c7d9d;
  font-weight: 100;
  margin-bottom: 30px;
}

/*====== Gallery ======*/
/* line 1285, ../sass/_template_specific.scss */
.filter-offers {
  font-size: 40px;
  text-align: center;
}
/* line 1289, ../sass/_template_specific.scss */
.filter-offers .active {
  background: #f0b31a;
}
/* line 1293, ../sass/_template_specific.scss */
.filter-offers .filter-hotel {
  margin-right: 0.5%;
  float: left;
}
/* line 1298, ../sass/_template_specific.scss */
.filter-offers .filter-apartamentos {
  float: left;
}
/* line 1302, ../sass/_template_specific.scss */
.filter-offers li {
  cursor: pointer;
  background: #0c7d9d;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 24px;
  float: left;
  margin-right: 6px;
  padding: 16px 0px;
  font-weight: bold;
  width: 32.97%;
}
/* line 1314, ../sass/_template_specific.scss */
.filter-offers li.filter-village {
  margin-right: 0px;
}
/* line 1317, ../sass/_template_specific.scss */
.filter-offers li a {
  text-decoration: none;
  color: white;
}
/* line 1321, ../sass/_template_specific.scss */
.filter-offers li:hover {
  background: #f0b31a;
}

/* line 1328, ../sass/_template_specific.scss */
.gallery-images {
  margin-bottom: 60px;
}

/* line 1332, ../sass/_template_specific.scss */
ul.gallery_1 li {
  padding: 2px 1px !important;
  width: 25% !important;
  height: 280px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  cursor: pointer;
  float: left;
}
/* line 1341, ../sass/_template_specific.scss */
ul.gallery_1 li .crop {
  height: 280px !important;
}
/* line 1344, ../sass/_template_specific.scss */
ul.gallery_1 li .crop a img {
  height: 280px !important;
  max-width: none;
}

/*======= Slider ====*/
/* line 1352, ../sass/_template_specific.scss */
#slider_container {
  position: relative;
}

/* line 1356, ../sass/_template_specific.scss */
.tp-bullets {
  display: none !important;
}

/* line 1360, ../sass/_template_specific.scss */
.revolution_fixed {
  width: 100%;
  height: 430px;
  overflow: hidden;
  position: relative;
}
/* line 1366, ../sass/_template_specific.scss */
.revolution_fixed img {
  width: 100%;
  position: fixed;
  top: 0px;
  min-width: 1310px;
  z-index: -1;
}

/* line 1375, ../sass/_template_specific.scss */
.flex_description {
  position: absolute;
  left: 401px;
  right: 0px;
  top: 32px;
  bottom: 0px;
  width: 900px;
  height: 100px;
  z-index: 2;
  margin: auto;
  color: white;
  text-transform: uppercase;
  font-size: 77px;
  font-weight: 200;
  text-align: center;
  line-height: 65px;
}

/*======== Ticks ========*/
/* line 1394, ../sass/_template_specific.scss */
.tickets {
  height: 55px;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0px;
  z-index: 23;
  padding-top: 7px;
  box-sizing: border-box;
}
/* line 1404, ../sass/_template_specific.scss */
.tickets .center_tick {
  width: 510px;
  margin: auto;
}

/* line 1411, ../sass/_template_specific.scss */
.eur_tick {
  background: url("/img/eurod/ticks/eur_tick.png") no-repeat;
}

/* line 1415, ../sass/_template_specific.scss */
.pig_tick {
  background: url("/img/eurod/ticks/pig_tick.png") no-repeat;
}

/* line 1419, ../sass/_template_specific.scss */
.shield_tick {
  background: url("/img/eurod/ticks/shield_tick.png") no-repeat;
}

/* line 1423, ../sass/_template_specific.scss */
.tick {
  width: 114px;
  float: left;
  padding-left: 44px;
  text-transform: uppercase;
  font-size: 11px;
  color: white;
  margin-right: 12px;
  line-height: 11px;
  font-weight: 100;
  padding-top: 7px;
  margin-top: 4px;
  padding-bottom: 4px;
  background-size: 33px;
}
/* line 1438, ../sass/_template_specific.scss */
.tick:last-of-type {
  margin-right: 0px;
}

/*===== Facebook and plus one ====*/
/* line 1444, ../sass/_template_specific.scss */
div#facebook_like {
  width: 49%;
  float: left;
  text-align: right;
}

/* line 1450, ../sass/_template_specific.scss */
div#google_plus_one {
  width: 49%;
  float: right;
}

/* line 1455, ../sass/_template_specific.scss */
.social_widget_wrapper {
  width: 100%;
  display: block;
  margin: auto;
  margin-bottom: 8px;
  margin-top: 9px;
}

/*============== Bottom Pop-up ============*/
/* line 1464, ../sass/_template_specific.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: #0c7d9d;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 1474, ../sass/_template_specific.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 1480, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

/* line 1489, ../sass/_template_specific.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 1493, ../sass/_template_specific.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 1498, ../sass/_template_specific.scss */
button.bottom_popup_button {
  width: 120px;
  background: #bebebe;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;
}

/* line 1513, ../sass/_template_specific.scss */
#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/* line 1518, ../sass/_template_specific.scss */
.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
}
/* line 1522, ../sass/_template_specific.scss */
.popup_inicial .email, .popup_inicial .discount, .popup_inicial .compra {
  text-align: center;
}
/* line 1525, ../sass/_template_specific.scss */
.popup_inicial .compra {
  padding-top: 5px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 1531, ../sass/_template_specific.scss */
.popup_inicial .discount {
  padding-top: 7px;
  color: white;
  font-size: 47px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
}
/* line 1539, ../sass/_template_specific.scss */
.popup_inicial .email {
  padding-top: 39px;
  color: white;
  font-size: 22px;
  font-weight: lighter;
}
/* line 1545, ../sass/_template_specific.scss */
.popup_inicial form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 1548, ../sass/_template_specific.scss */
.popup_inicial form.form_popup li {
  text-align: center;
}
/* line 1551, ../sass/_template_specific.scss */
.popup_inicial form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 270px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: #0c7d9d;
}
/* line 1560, ../sass/_template_specific.scss */
.popup_inicial form.form_popup button.popup_button {
  margin: 7px auto 0px;
  width: 277px;
  height: 40px;
  background: #0c7d9d;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 1572, ../sass/_template_specific.scss */
.popup_inicial .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 1575, ../sass/_template_specific.scss */
.popup_inicial .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 20px;
  font-weight: lighter;
}

/*==== Firefox ====*/
@-moz-document url-prefix() {
  /* line 1586, ../sass/_template_specific.scss */
  .automatic_content {
    padding-top: 50px;
  }
}
/* line 1591, ../sass/_template_specific.scss */
#popup_checkboxes {
  font-size: 12px;
  margin-top: 2em;
  color: white;
}
/* line 1595, ../sass/_template_specific.scss */
#popup_checkboxes a {
  color: white;
}

/* line 1600, ../sass/_template_specific.scss */
.fancybox-lopd .fancybox-inner {
  width: 500px !important;
  height: 500px !important;
}
