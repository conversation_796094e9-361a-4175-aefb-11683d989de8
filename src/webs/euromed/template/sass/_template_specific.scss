/*========= General ========*/
body {
  font-family: 'Roboto';
}

#content {
  background: white;
}

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

/*========== Header =========*/
header {
  background: white;
}

#wrapper-header {
  position: relative;
}

.free_wifi {
  position: absolute;
  right: 320px;
  top: 16px;
  font-weight: 300;
  font-size: 18px;
  color: #7B797A;

  strong {
    font-weight: 500;
  }

  img {
    vertical-align: middle;
  }
}

.text_oficial {
  float: left;
  margin-top: 10px;
  color: white;
  padding-left: 19px;
  font-weight: 300;
  margin-right: 27px;
  strong {
    font-weight: 500;
  }
}

//LOGO
#logoDiv {
  z-index: 100;
  width: 255px;
  height: 78px;
  position: absolute;
  text-align: center;
  padding: 10px 0px;
  background-color: white;

  a {
    text-decoration: none;
  }

  img {
    height: 100%;
  }
}

#social a img {
  width: 28px;
  height: 28px;
}

//Language Select and Web Official

.top-menu-wrapper-2-container {
  padding-top: 9px;
}

div#top-menu-wrapper-2 {
  position: relative;
  height: 60px;

  #lang {
    font-size: 15px;
  }

}

#web_oficial, #lang, #profesionales_wrapper {
  float: right;
}

#web_oficial {
  padding: 5px;
  position: absolute;
  right: 195px;
  color: $corporate_1;

  strong {
    font-weight: bold;
    color: $corporate_1;
  }
  .txt_negrita {
    font-weight: bold;
  }
}

//Language

span#selected-language {
  padding-top: 3px;
  font-weight: 300;
  color: grey;
  display: block;
  float: left;
}

#lang {
  display: inline-block;
  width: 150px;
  height: 35px;
  padding: 5px 1px 3px 8px;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.63);
  cursor: pointer;
  color: #949090 !important;
  background: rgb(243, 241, 242);
  margin-left: 20px;
  vertical-align: middle;
  text-align: left;
  right: 135px;
  position: absolute;
  margin-top: 8px;

  .arrow {
    display: inline-block;
    background: $corporate_1 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
    float: right;
    width: 27px;
    height: 28px;
    margin-top: -2px;
    margin-right: 2px;
    border-radius: 3px;

    #selected-language {
      color: black !important;
    }
  }
}

#language-selector-options {
  display: none;
  margin-top: 1px;

  a {
    color: $gray-2;
    text-decoration: none;

    &:hover {
      color: $corporate_1;
    }
  }

  li.language-option-flag {
    background: white;
    font-weight: 300;
    padding: 5px 9px;
    &:hover {
      font-weight: 500;
    }

    &:hover > a {
      color: $corporate_1;
    }
  }
}

ul#language-selector-options {
  z-index: 10;
  margin-left: -9px;
  width: 150px;
  position: relative;
  display: none;
  margin-top: 29px;
}

/* TICKS */

#ticksContainer {
  float: left !important;
  display: block;
  width: 100%;
  text-align: right;
}

#ticksContainer, #social, #mini-top-menu {
  float: right;
}

#tick1 {
  background: url("/img/#{$base_web}/ticks/pago_icon.png") no-repeat 0;
}

#tick2 {
  background: url("/img/#{$base_web}/ticks/gastos_icon.png") no-repeat 0;
}

#tick3 {
  background: url("/img/#{$base_web}/ticks/segura_icon.png") no-repeat 0;
}

.ticks {
  display: inline-block;
  text-align: left;
  line-height: 10px;
  text-transform: uppercase;
  padding-left: 36px;
  font-size: 9px;
  color: $corporate_1;
  width: 73px;
  font-weight: bold;
  margin-top: 5px;
  margin-left: 10px;
  padding-top: 4px;
}

#top-menu-wrapper-1 {
  height: 33px;
  width: 1140px;
  margin: auto;
  padding-left: 212px;
  box-sizing: border-box;

  .top-menu-wrapper-1-inside {
    background: $corporate_3;
    display: block;
    float: right;
    height: 35px;
  }
}

div#mini-top-menu {
  padding: 7px 0px;
  display: block;
  margin-left: 75px;
  height: 17px;
  padding-top: 8px;

  a {
    text-decoration: none;
  }

  span:first-child {
    margin-right: 8px;
  }

  span {
    color: white;
    font-size: 13px;
    font-weight: 100;
    opacity: 1;
    text-transform: uppercase;

    &:hover {
      opacity: 0.6;

    }
  }
  .my_booking_header {
    margin-right: 15px !important;
  }

}

div#social {
  margin-left: 8px;
  margin-bottom: 0px;
  margin-top: 12px;

  img:hover {
    opacity: 0.6;
  }

  a {
    text-decoration: none;
  }

}

.separator_line {
  border-left: 1px solid white;
  margin-right: 8px;
}

div#mini-top-menu span.no_hover:hover {
  opacity: 0.8;
}

/*========= Banners x3 ========*/

.bannersx3 {
  display: table;
  margin-bottom: 25px;
  margin-top: 40px;

  .banner {
    height: 200px;
    overflow: hidden;
    position: relative;
    margin-bottom: 16px;
    img {
      width: 100%;
    }

    .banner_title {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      left: 0;
      width: 80%;
      text-align: center;
      display: block;
      margin: auto;
      text-transform: uppercase;
      font-size: 19px;
      color: white;
      font-weight: 700;
      line-height: 20px;
      height: 52px;

      div {
        text-transform: none;
        font-size: 14px;
        font-weight: 300;
      }
    }

    .separator {
      width: 155px;
      border-top: 3px solid white;
      margin: 4px auto;
    }
  }
}

/*========== Menu ===========*/

#main_menu {
  background: $corporate-1;
}

div#main-sections {
  height: 37px;
}

#mainMenuDiv {
  font-size: 12px;
  position: relative;
  clear: both;
  font-weight: 700;

  a {

    text-decoration: none;
    text-transform: uppercase;
    color: white;
    display: inline-block;
    text-align: center;

  }

}

#mainMenuDiv a:hover {
  color: $corporate_1;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
  height: 30px;
  top: 37px;
  z-index: 30;
  left: 0px;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper {
  width: auto;
  text-align: center;
  padding: 0 10px;
  font-weight: lighter;
  position: relative;

  &:hover {
    background: white;
    a {
      color: $corporate_1 !important;
    }
  }

  a {
    line-height: 38px;
    text-transform: uppercase;
    font-weight: lighter;
    font-size: 14px;
  }
}

.main-section-div-wrapper.section-4 {
  width: 120px;
}

.main-section-div-wrapper.reservar {
  padding-right: 0;
}

.main-section-div-wrapper a
.section-6 a {
  width: 180px !important;
  &:hover {
    width: 180px !important;
  }
}

#section-active a {
  text-decoration: none;
  text-transform: uppercase;
  text-align: center;
  color: white;
  font-weight: bolder;
  display: inline-block;
}

/*====== Subsections ========*/

.main-section-subsection a {
  min-width: 215px !important;
  display: block;
  color: $corporate_1;
  background-color: white;
}

.main-section-subsection a:hover {
  color: white;
  background-color: $corporate_1;
}

/*======== Booking Widget =======*/
.booking_form {
  background: rgba(0, 0, 0, 0.43)
}

.booking_widget {
  top: 70px;
}

.selectric .button {
  background: $corporate_4 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

.wrapper_booking_button button {
  background: $corporate_2;
  width: 150px;
  font-size: 15px;
  cursor: pointer;

  &:hover {
    background: $corporate_3;
  }
}

.wrapper_booking_button .promocode_input {
  width: 100px;
  font-size: 9px;

  &::-webkit-input-placeholder {
    color: $corporate_4;
    font-weight: bolder;
  }

  &:-moz-placeholder {
    color: $corporate_4;
    font-weight: bolder;
  }

  &::-moz-placeholder {
    color: $corporate_4;
    font-weight: bolder;
  }

  &:-ms-input-placeholder {
    color: $corporate_4;
    font-weight: bolder;
  }
}

.date_box .date_day {
  color: $corporate_4;
  font-size: 15px;
  padding-top: 2px;
}

.date_box .date_year {
  color: gray;
}

.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
  margin-bottom: 4px;
}

.booking_form {
  padding: 10px 20px 20px 20px;
}

.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  color: white;
  font-weight: 300;
}

.selectric {
  background: none;
}

.selectric .label {
  margin-left: 0px;
  text-align: center;
  background: white;
  width: 39px;
  border-radius: 5px;
  line-height: 41px;
}

.selectric .button {
  border: 3px solid white;
  margin-top: 0px;
  border-radius: 6px;
}

/*======== Automatic Content ======*/
.automatic_content {
  margin-bottom: 50px;

  h3.main-title {
    font-size: 30px;
    text-align: center;
    color: $corporate_1;
    font-weight: 100;
    margin-bottom: 30px;

    strong {
      font-weight: bolder;
    }
  }

  .section-description {
    text-align: center;
    color: $corporate_3;
    font-weight: 300;
    line-height: 30px;
  }
}

/*======== Blocks x3 Wrapper ======*/
.blocksx3_wrapper {
  background: rgb(224, 226, 225);
  padding: 40px 0px;

  .block_element {
    background: white;

    a{
      text-decoration: none;
      color:#787c7d;
    }

    img {
      width: 100%;
    }

    .block_description {
      text-align: center;
      font-size: 13px;
      color: #787c7d;
      font-weight: 300;
      padding: 10px 38px;
      min-height: 100px;

      h4 {
        color: $corporate_1;
        font-weight: 100;
        font-size: 15px;
        font-style: italic;
        margin-bottom: 5px;

        strong {
          font-weight: bolder;
          font-style: normal;
        }
      }
    }
  }
}

/*========== Footer ========*/
footer {
  padding: 40px 0px 18px;
  background: white;
  color: $corporate_3;

  h3.footer_column_title {
    font-size: 12px;
    font-weight: bold;
  }

  #footer_column_description {
    font-size: 13px;
    font-weight: lighter;
    margin-top: 6px;
  }

  #newsletter {
    #title_newsletter {
      display: none;
    }
    #suscEmailLabel {
      display: none !important;
    }

    button#newsletter-button {
      background: $corporate_1;
      color: white;
      border: 0px;
      padding: 5px;
      width: 78px;
      border-radius: 4px;
      cursor: pointer;
      text-transform: uppercase;

      &:hover {
        background: $corporate_3;
      }
    }
    .newsletter_checkbox {
        font-size: 11px;
        a {
            color: $corporate_1;
        }
    }

    input#suscEmail {
      margin-top: 5px;
      background-color: rgb(224, 226, 225);
      border: 0px;
      height: 20px;
      width: 220px;
      margin-bottom: 6px;
      border-radius: 4px;
    }
  }

  .footer_column:not(.last) {
    border-right: 1px solid $corporate_3;
    width: 263px !important;
    padding-left: 35px !important;
    box-sizing: border-box;
    min-height: 100px;
  }

  .footer_column.last {
    padding-left: 25px !important;
    box-sizing: border-box;
  }
}

.footer-copyright {
  text-align: center;
  color: #787c7d;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;

  a {
    text-decoration: none;
    color: #787c7d;

    &:hover {
      opacity: 0.8;
    }
  }
}

/*========== Hoteles =======*/
.hide_me {
  display: none;
}

.hotel_content_wrapper {
  margin-top: 30px;
}

.hotel {
  background: #F7F6F6;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
  min-height: 395px;

  .location_button {
    height: 29px;
    display: block;
    width: 29px;
    background: url(/img/eurod/location.png) no-repeat center;
    background-size: 29px;
    position: absolute;
    right: 91px;
    cursor: pointer;
    top: -7px;
  }
}

.hotel-description {
  padding: 20px 25px 0;
  font-size: 14px;
  color: $corporate_3;
  font-weight: 300;
}

.hotel h3 {
  line-height: 16px;
  text-align: left;
}

.hotel-description .sub-description-hotel {
  margin-top: 10px;
}

.hotel-description .description-hotel {
  margin-top: 10px;
}

.hotel .hotel-links {
  position: absolute;
  right: 21px;
  top: 233px;

  a {
    color: white;
    background: $corporate_1;
    text-decoration: none;
    padding: 7px 18px;
    img {
      padding-right: 10px;
    }
  }

}

.hotel {
  img {
    width: 100%;
    height: 210px;
  }

  h3.title-module {
    text-transform: uppercase;
    text-decoration: none;
    color: $corporate_1;
    font-size: 17px !important;
    margin: 0px !important;
    margin-bottom: 15px !important;
    font-weight: 200;
    line-height: inherit !important;
    text-align: left !important;
  }

  .destino {
    font-weight: 500;
  }

  .more_pictures {
    width: 60px;
    height: 60px;
    position: absolute;
    top: 0px;
    right: 0px;
  }
}

/*======= Ofertas ======*/
#wrapper_rooms_hotels {
  margin-top: 40px;
}

.rooms {
  margin-bottom: 10px;
  margin-left: 5px !important;
  margin-right: 5px !important;
  position: relative;
  background: #F6F7F7;
}

.rooms .img-offer {
  float: left;
  width: 275px;
  height: 210px;
}

.room_block_description {
  overflow: hidden;
  min-height: 160px;
  max-height: 160px;
}

.hotel_room_title {
  font-size: 18px;
  font-weight: bold;
  margin: 5px 0 10px 20px;
  color: $corporate_1;
}

.hotel_room_description {
  padding: 0px 20px;
  font-weight: 300;
  font-size: 13px;
}

.hotel_room_description h3 {
  font-weight: lighter;
  font-size: 19px;
  text-transform: uppercase;
  color: $corporate_1;
  padding: 0 20px;
}

.rooms_dates {
  margin-left: 20px;
  font-size: 14px;
  line-height: 17px;
  color: $gray-3;
}

.rooms_dates span {
  text-transform: uppercase;
  color: $corporate_1;
}

.buttons_rooms {
  float: left;
  margin-top: 5px;
  position: absolute;
  right: 20px;
}

.buttons_rooms .arrow_right {
  padding: 5px 5px 5px;
  border-radius: 2px;
  float: left;
  margin-left: 5px;
  margin-right: 5px;
}

.buttons_rooms button {
  cursor: pointer;
  border-radius: 3px;
  color: white;
  border: none;
  text-transform: uppercase;
  float: left;
  margin-top: 5px;
  height: 32px;
  width: 100px;
  background-color: $corporate_1;
}

.buttons_rooms button:hover {
  background-color: $corporate_2;
}

.button-promotions-inner {
  color: white;
  text-decoration: none;
}

.hotel_room_description .txt-description {
  display: none;
}

.title-full-promo {
  text-align: center;
  color: $corporate_1;
  font-size: 30px;
  font-weight: bold;

}

.full-promo-description {

  padding: 20px;

  h3 {
    display: none;
  }

  .rooms_dates {
    display: none;
  }
}

.filter-promo {
  margin-top: 50px;
  background: rgba(58, 91, 106, .7);
  text-align: center;
  padding: 10px 0px;

  li {
    display: inline-block;
    margin: 0px 10px;
  }
  a {
    text-decoration: none;
    color: $gray-3;
    font-size: 16px;
    padding: 5px;

    &:hover {
      color: white;
    }
  }
  a.one-night {
    background: url(/img/eurod/1_moon.png) no-repeat left center;
    padding-left: 25px;
  }
  a.two-night {
    background: url(/img/eurod/2_moon.png) no-repeat left center;
    padding-left: 50px;
  }
  a.more-night {
    background: url(/img/eurod/3_moon.png) no-repeat left center;
    padding-left: 75px;
  }
  a.offers {
    background: url(/img/eurod/oferta.png) no-repeat left center;
    padding-left: 25px;
  }
  .filtro-active {
    color: white;
  }
}

/*========= Location and Contact ========*/
#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;

  .location-info, .form-contact {
    width: 520px !important;
  }

}

.location-info-and-form-wrapper h1 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px $gray-3 !important;
  margin-bottom: 30px;
  color: $corporate_1;
}

.location-info-and-form-wrapper {
  p, strong {
    color: dimgrey;
  }
}

.location-info {
  text-align: left;
  p {
    margin-bottom: 10px;
    font-weight: 300;
  }
  strong {
    font-weight: bold;
  }
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;

  iframe {
    margin-bottom: 25px;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-right: 5px;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
  padding-left: 3px;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #e1e1e1;
  color: dimgrey;
  text-align: left;
  padding: 15px;
  box-sizing: border-box;

}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate-2 !important;
}

.iframe_maps {
  display: table;
  padding: 20px 0px;
}

.location-info-and-form-wrapper {
  margin: 20px 0px;
  box-sizing: border-box;
  display: table;
  padding: 20px 0px;
}

.how-to-go {
  cursor: pointer;
  margin-bottom: 15px;

  p {
    font-weight: bold;
    font-size: 14px;
    color: $corporate_1;
  }

  &:hover {
    opacity: 0.8;
  }
}

ul.location_destiny {
  margin-top: 20px;
  list-style: circle;
  padding-left: 20px;
}

/*======= Mis reservas =======*/

form#my-bookings-form {
  margin-top: 30px;
}

#wrapper_services {
  display: none;
}

.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate-1;
    text-transform: uppercase;
  }
  input, .bordeSelect {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    color: black;
    border: none;
    background: $gray-4;
  }

  .bordeSelect {
    -webkit-appearance: none;
    color: $corporate_3;
    width: 263px !important;
    border-radius: 0px !important;
    height: 25px !important;
    background: #e6e6e6 url(/img/checn/select_down.png) no-repeat 240px;
  }
  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

button#cancelButton {
  width: 260px;
  color: white;
  background-color: $corporate-1;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: $corporate-2;
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;
    margin-left: 470px;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}

.section-title + div {
  color: #918f8f;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
  margin-top: 25px;
}

.content_element {
  margin-top: 25px;

  .section-title {
    font-size: 30px;
    text-align: center;
    color: $corporate_1;
    font-weight: 100;
    margin-bottom: 30px;
  }
}

/*====== Gallery ======*/
.filter-offers {
  font-size: 40px;
  text-align: center;

  .active {
    background: $corporate_2;
  }

  .filter-hotel {
    margin-right: 0.5%;
    float: left;
  }

  .filter-apartamentos {
    float: left;
  }

  li {
    cursor: pointer;
    background: $corporate_1;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 24px;
    float: left;
    margin-right: 6px;
    padding: 16px 0px;
    font-weight: bold;
    width: 32.97%;

    &.filter-village {
      margin-right: 0px;
    }
    a {
      text-decoration: none;
      color: white;
    }
    &:hover {
      background: $corporate_2;
    }
  }

}

.gallery-images {
  margin-bottom: 60px;
}

ul.gallery_1 li {
  padding: 2px 1px !important;
  width: 25% !important;
  height: 280px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  cursor: pointer;
  float: left;

  .crop {
    height: 280px !important;

    a img {
      height: 280px !important;
      max-width: none;
    }
  }
}

/*======= Slider ====*/
#slider_container {
  position: relative;
}

.tp-bullets {
  display: none !important;
}

.revolution_fixed {
  width: 100%;
  height: 430px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    position: fixed;
    top: 0px;
    min-width: 1310px;
    z-index: -1;
  }
}

.flex_description {
  position: absolute;
  left: 401px;
  right: 0px;
  top: 32px;
  bottom: 0px;
  width: 900px;
  height: 100px;
  z-index: 2;
  margin: auto;
  color: white;
  text-transform: uppercase;
  font-size: 77px;
  font-weight: 200;
  text-align: center;
  line-height: 65px;
}

/*======== Ticks ========*/
.tickets {
  height: 55px;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0px;
  z-index: 23;
  padding-top: 7px;
  box-sizing: border-box;

  .center_tick {
    width: 510px;
    margin: auto;
  }

}

.eur_tick {
  background: url("/img/eurod/ticks/eur_tick.png") no-repeat;
}

.pig_tick {
  background: url("/img/eurod/ticks/pig_tick.png") no-repeat;
}

.shield_tick {
  background: url("/img/eurod/ticks/shield_tick.png") no-repeat;
}

.tick {
  width: 114px;
  float: left;
  padding-left: 44px;
  text-transform: uppercase;
  font-size: 11px;
  color: white;
  margin-right: 12px;
  line-height: 11px;
  font-weight: 100;
  padding-top: 7px;
  margin-top: 4px;
  padding-bottom: 4px;
  background-size: 33px;

  &:last-of-type {
    margin-right: 0px;
  }
}

/*===== Facebook and plus one ====*/
div#facebook_like {
  width: 49%;
  float: left;
  text-align: right;
}

div#google_plus_one {
  width: 49%;
  float: right;
}

.social_widget_wrapper {
  width: 100%;
  display: block;
  margin: auto;
  margin-bottom: 8px;
  margin-top: 9px;
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: $corporate-1;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  width: 120px;
  background: $gray-3;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }
    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}

/*==== Firefox ====*/

@-moz-document url-prefix() {
  .automatic_content {
    padding-top: 50px;
  }
}

#popup_checkboxes {
    font-size: 12px;
    margin-top: 2em;
    color: white;
    a {
        color: white;
    }
}
.fancybox-lopd {
    .fancybox-inner {
        width: 500px !important;
        height: 500px !important;
    }
}
