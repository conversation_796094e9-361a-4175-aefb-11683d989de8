

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default{
    border: 1px solid white!important;
}
.ui-datepicker-title{
  color: white!important;
}
.ui-widget-header {
  background: $corporate_1!important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1!important;
  color: white;
}

body{
  font-size:16px;
  line-height:24px;
  font-family: "Source Sans Pro", sans-serif;
}
strong{
  font-weight:bold;
}

header{
  background:$corporate-1;
}

.top-row{
  overflow:hidden;
  padding-top:10px;
  font-weight:200;
}

//LANGUAGE SELECT
#lang {
  cursor: pointer;
  width: 70px;
  height: 35px;
  display: inline-block;
  margin-top:7px;
  float: right;
  vertical-align: middle;
  margin-right: 20px;

  #selected-language {
    background: rgb(245, 246, 248);
    box-sizing: border-box;
    padding: 11px 10px 17px 10px;
    height: 35px;
    width: 50%;
    display: inline-block;
    font-family: 'Lato', sans-serif;
    color: rgb(164, 139, 104);
    font-size: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-align: center;
    line-height:17px;
  }

  .arrow {
    display: inline-block;
    background: $corporate_2 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
    float: right;
    width: 50%;
    height: 35px;
    margin-top: 0px;

  }

  ul li {
    background: #F0EADC;
    text-align: left;
    width: 71px;
    font-size: 13px;
    box-sizing: border-box;
    font-family: 'Lato', sans-serif;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    padding-left: 9px;
    color: $corporate_1;
    border-top: 1px solid #FFF;

    &:hover {
      opacity: 0.8;
    }

    a {
      color: #a48b68;
      font-weight: 500;
      text-decoration: none !important;
    }
  }
}

#language-selector-options {
  display: none;
  box-shadow: 0px 1px 1px #6D6C6C;
  position:absolute;
  z-index: 22;
}

.weather {
  margin-right: 10px;
  width: 90px;
  float: right;
  margin-top:7px;

  .grados {
    float: right;
    background-color: rgb(245, 246, 248);
    padding: 10px 0px 17px 3px;
    box-sizing: border-box;
    width: 40px;
    height: 35px;
    display: inline-block;
    color: #a48b68;
    font-size: 12px;
    font-family: 'Lato', sans-serif;
    text-align: center;
    letter-spacing: 1px;
    line-height:19px;
  }
  .img_weather {
    float: right;
    background-color: $corporate_2;
    width: 40px;
    height: 35px;
    padding-top: 4px;
    box-sizing: border-box;

    img {
      width: 23px;
      text-align: center;
      display: block;
      margin: auto;
      margin-top: 2px;
    }
  }
}

#top-sections{
  float:right;
  margin-top:14px;
  margin-right:40px;

  a{
    text-decoration:none;
    color:white;
  }
}
.reser-header{
  float: right;
  color:white;
  margin-top:14px;
  margin-right:40px;
}


/*============== MENU ==================*/

#main_menu{
  text-align: right;
  margin-top:10px;
}

ul#main-sections-inner{

}

ul#main-sections-inner .main-section-div-wrapper{
  display:inline-block;

  a{
    display:block;
    padding:14px 15px 3px;
    color:white;
    text-transform:uppercase;
    border-bottom:3px solid transparent;
    line-height:19px;
    text-decoration:none;
    font-size:14px;
    letter-spacing: 2px;
  }
  a:hover{
    border-bottom:3px solid $corporate-2;
  }
}

/*=============== Slider Revolution ==============*/

#slider_container{
  position:relative;
}

#slider_inner_container{
  position:relative;

  .image-slider-fixed{
    height:550px;
    background-position: 50% 50%;
    background-repeat:no-repeat;
    background-attachment:fixed;
    background-size:cover;
    position:relative;

    img{
      width: 100%;
      height: auto;
      position: fixed;
      top: 0px;
      z-index: -2;
      min-width: 1920px;
    }
  }
  .slogan-container{
    position:relative;
    height:550px;
  }
  .slider_text {
    display: block;
    box-sizing:border-box;
    width: 100%;
    text-align:center;
    color: white;
    font-size: 57px;
    line-height: 70px;
    text-transform: uppercase;
    font-weight: lighter;
    text-shadow: 1px 0 8px #323232;
    position:absolute;
    top:100px;
  }
}

.tp-bullets .bullet {
  background: url("/img/gala2/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/gala2/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

.offers_slider_wrapper {
    position: absolute;
    bottom: 0;
    z-index: 22;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px 0;

    h3.slider_offer_title {
      font-family: 'Lato';
      color: white;
      font-size: 18px;
      margin-bottom: 11px;
    }

    .slider_offer_description {
      color: white;
      font-weight: lighter;
      letter-spacing: 1px;
      font-size: 17px;
      font-family: 'Lato', serif;

      strong {
        font-weight: bolder;
      }
    }

    .slider_offer_element {
      position: relative;

      a {
        text-decoration: none;
        color: white;
        position: absolute;
        right: 23px;
        bottom: 15px;

        button {

          width: 211px;
          font-weight: 200;
          font-size: 16px;
          float: none;
          margin: 12px auto 0px;
          display: block;
          color: white;
          border: 0;
          height: 40px;
          padding-top: 3px;
          cursor: pointer;
          line-height: 2px;
          background:$corporate-2;
        }
      }
    }
    .flex-control-nav{
      display:none;
    }
  }



/*=============== Booking Widget ================*/

#slider_inner_container .booking_widget{
  top:50px;
}

.booking_widget{
  top:14px;
}
.date_box .date_day .day, .date_box .date_day .month, .date_box .date_year {

}
.date_box .date_day .day{
  font-size:40px;
  padding:10px 0px;
}

.booking_form_title:before {
  border-top: 8px solid $corporate_1;
}

h4.booking_title_2 {
  display: block !important;
  font-size: 30px;
  line-height: 30px;
  margin-top: 8px;
  margin-bottom: 10px;
  font-weight: lighter;
}

h4.best_price {
  display: block;
}

h4.booking_title_custom {
  font-weight: 100;
  margin-bottom: -15px;
  text-transform: uppercase;
}

.ui-widget-header {
  background: $corporate_1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1;
  color: white;
}



.booking_widget.interior {
  top: 210px;
}

.wrapper_booking_button .promocode_input {
  background-color: #edeef0;
  border-radius: 0px;
}

.promocode_input::-webkit-input-placeholder {
  color: transparent !important;
  &::before {
    content: "Promocode";
    color: #1b5360 !important;
    padding-left: 14px;
  }
}

.promocode_input::-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-moz-placeholder {
  color: #1b5360 !important;
}

.promocode_input:-ms-input-placeholder {
  color: #1b5360 !important;
}

//booking engine in fancybox (for selects!)
.fancybox-inner {
  overflow: visible !important;

}

.wrapper_booking_button button {
  background: $corporate_2;
  cursor: pointer;
  width: 100%;
  border-radius: 0px;
  font-weight: lighter;

  &:hover {
    background: #838587;
    color: white;
  }
}

.booking_form {
  background: rgb(228, 228, 228);
  width: 285px;
  box-sizing: border-box;
}

.booking_form_title {
  background: $corporate_1;
}

.date_box {
  border-radius: 0px;
  height:100px;
  .date_day {
    color: $corporate_2 !important;
  }
}

.selectric {
  border-radius: 0px;
  height:100px !important;

  .button {
    display: none;
  }
}

.selectricItems li {
  font-weight: 500;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAADAQMAAABs5if8AAAABlBMVEXOzs7y8vKNS741AAAADklEQVQI12M4wPSAWQAABoQBtgsaY5kAAAAASUVORK5CYII=);
  color: #a5a5a5;
  font-size: 25px;
  line-height: 40px;
  border-bottom: 1px solid #fff;
  padding: 10px 0px;
  width: 100%;
  text-align: center;
}

h4.best_price {
  font-size: 18px;
  font-weight:300;
}

.room_title {
  text-align: center;
}

.web_support_label_1 {
  font-size: 19px;
}

.adults_selector .button, .children_selector .button {
    display: block !important;
    background: url(/static_1/images/booking/arrow_down_big.png) no-repeat center center !important;
}


/*================== CONTENT ==================*/

#content{
  padding-top:60px;
  padding-bottom:40px;
  background:white;

  .content_subtitle_title{
    color:$corporate-2;
    font-size:32px;
    line-height:36px;
    text-align:center;
    margin-bottom:30px;
    font-family: 'Montserrat', sans-serif;
  }
  .content_subtitle_title.column4{
    text-align:left;
  }
}


/*Main Banners*/

#main-banners {
  background: black;
  padding: 40px 0px;
}

.main-banners .block-banner {
  overflow: hidden;
  background: white;
  margin: 10px 0px;
}

.column-left {
  float: right;
  width: 425px;
  padding: 40px 60px;
  text-align: center;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  #title {
    margin-bottom: 28px;
    font-family: 'Montserrat', sans-serif;
    font-size: 22px;
    font-weight: 400;
    text-transform: uppercase;
    color: $corporate-2;
  }
  p {
    text-align: center;
    margin-bottom: 20px;
    color: $gray-2;
  }
  span {
    display: block;
    width: 50px;
    border: 2px solid $corporate-1;
    margin: 0 auto 30px;
  }
}

.column-right {
  width: 715x;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  img {
    width: 700px;
    vertical-align: bottom;
  }
}

#reverse .column-left {
  float: left;
}

#reverse .column-right {
  float: right;
}

#section-banners {
  background: $gray-4;
  padding: 40px 0px;

  h3.title-banners {
    font-family: "Oswald", sans-serif;
    font-size: 24px;
    font-weight: 300;
    margin-bottom: 30px;
    text-align: center;
  }
  span {
    display: block;
    width: 50px;
    border: 2px solid $corporate-1;
    margin: 0 auto 30px;
  }
  .block-banner {
    background: white;
    text-align: center;
    position: relative;
    overflow: hidden;

  }
  .banner-description {
    padding: 20px 30px;
    font-size: 13px;
  }
  .banner-description h4 {
    color: $corporate-1;
    font-size: 24px;
    margin-bottom: 20px;
    font-family: 'Oswald', sans-serif;
  }
  .banner-image span {
    display: inline-block;
    background: $corporate-1;
    color: white;
    font-size: 18px;
    padding: 8px 15px;
    position: absolute;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    top: -44px;
    width: 100px;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -o-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px);
    -webkit-transition: opacity 1s, -webkit-transform 1s;
    transition: transform .5s;
  }
  .block-banner:hover .banner-image span {
    -webkit-transform: translateY(44px);
    -moz-transform: translateY(44px);
    -o-transform: translateY(44px);
    -ms-transform: translateY(44px);
    transform: translateY(44px);
    -webkit-transition: opacity 1s, -webkit-transform 1s;
    transition: transform .5s;
  }
}

/*Mosaic Gallery*/

.mosaic-gallery{
  padding:40px 0px;
  background:white;

  h3{
    color:black;
    font-size:36px;
    text-transform: uppercase;
    margin-bottom:40px;
    font-family: 'Montserrat', sans-serif;
  }
  .mosaic-block-left,
  .mosaic-block-right{
    margin:0px;
  }

  li{
    float: left;
    width:271px;
    height:160px;
    overflow:hidden;
    margin:2px;
    position: relative;
  }
  li img{
    vertical-align:bottom;
    position:absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-width: 100%;
    min-height: 100%;
  }
  li.big-picture{
    width:546px;
    height:300px;
  }
  li.text{
    color:white;
    background:$corporate-2;
    text-align:center;
    font-size:28px;
    display: table;

    .content{
      display: table-cell;
      vertical-align: middle;
    }
  }
}

/*Restaurante Banner*/

.destacado-banner{
  padding:20px 0px 40px;

  .destacado-full-title{
    background:white;
    color:black;
    font-size:36px;
    text-transform: uppercase;
    margin-bottom:40px;
    font-family: 'Montserrat', sans-serif;
  }
  .content-image img{
    vertical-align: bottom;
  }
  .destacado-banner-content{
    background:black;

    .destacado-content{
      color:white;
    }
    .destacado-content .subtitle{
      text-transform: uppercase;
      font-weight:200;
      font-size:22px;
      margin:20px 0px;
      line-height:28px;
    }
    .destacado-content span{
      display: block;
      border-top:1px solid white;
      width:100%;
      margin-bottom:20px;
    }
    a{
      color:white;
      text-decoration:none;
      display:inline-block;
      padding:10px 100px 7px 20px;
      background:rgb(146, 212, 211);
      font-size:18px;
      text-transform:uppercase;
      font-weight:300;
      margin-top:40px;
      margin-left:-20px;
    }
  }
}

/*=============== Bottom Banners ================*/
.banners-bottom-content{
  background:white;
}
.banners-bottom{
  overflow:hidden;
  padding:60px 0px;

  .banner{
    box-sizing: border-box;
    padding-left:30px;
  }
  h4{
    color:$corporate-2;
    font-size:28px;
    text-align:left;
    line-height:30px;
    font-weight:400;
    margin-bottom:20px;
    font-family: 'Montserrat', sans-serif;
  }
}


/*============ Rooms ===========*/

.rooms-wrapper {
  padding: 40px 0px 40px;
}

.rooms-wrapper .block-room {
  overflow: hidden;
  background: $gray-4;
  margin: 10px 0px;

  .column-left {
    float: left;
    width: 425px;
    padding: 40px 40px;
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;

    #title {
      margin-bottom: 30px;
      font-family: "Oswald", sans-serif;
      font-size: 24px;
      font-weight: 300;
    }
    p {
      text-align: center;
      margin-bottom: 20px;
      color: #676566;
    }
    span {
      display: block;
      width: 50px;
      border: 2px solid $corporate-1;
      margin: 0 auto 30px;
    }
    .desc {
      height: 100px;
      overflow: hidden;
    }
  }
  .column-right {
    float: right;
    width: 715x;
    height: 350px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;

    img {
      width: 700px;
      vertical-align: bottom;
      margin-top: -50px;
    }
  }

  .links-buttons {
    margin-top: 30px;
  }
  .links-buttons a {
    text-decoration: none;
    background: $corporate-1;
    color: white;
    display: inline-block;
    padding: 7px 15px;
    text-align: center;
    margin: 0px 1px;
    width: 80px;
  }
  .links-buttons a:hover {
    background: $corporate-2;
  }
  .links-buttons a.see_more {
    display: none;
  }

  &#reverse .column-left {
    float: right;
  }
  &#reverse .column-right {
    float: left;
  }
}


/************************* SCAPES/OFERTAS ************************/


.scapes-blocks {
  overflow: hidden;
  margin-top: 25px;
}

.scapes-blocks .block {
  background: #f6f7f8;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;

  a.enlace_offer{
    display: inline-block;
    height: 300px;
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  a img {
    margin-bottom: -5px;
    width: 100%;
    position: absolute;
    margin: auto;
    top: 0;
    bottom: 0;
  }

  .description {
    padding: 20px;
    position: relative;
    background-color: $gray-4;
    padding-right: 160px;

    .title-module {
      font-size: 20px;
      color: $corporate_1;
      font-weight: normal;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 200px;
      right: 0;
      top: 18px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline-block;
      }
      li a{
        background:$corporate-1;
        color:white;
        text-decoration:none;
        padding:8px 15px;
      }
      li a:hover{
        background:$corporate-2;
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_2;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}


//*************** Location and contact *****************//

#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;

  .location-info, .form-contact {
    width: 520px !important;
  }

}

.location-info-and-form-wrapper h1 {
  font-family: lato;
  font-weight: bold;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px rgb(190, 190, 190) !important;
  margin-bottom: 30px;
  color: $corporate_1;
}

.location-info-and-form-wrapper {
  p, strong {
    color: dimgrey;
  }
}

.location-info {
  p {
    margin-bottom: 10px;
    font-weight: 300;
  }
  strong {
    font-weight: bold;
  }
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;

  iframe {
    margin-bottom: 25px;
  }
}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-right: 5px;
}

#contactContent .info {
  margin-top: 30px;
  overflow: hidden;
}




.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: dimgrey;
  font-weight: 300;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #e1e1e1;
  color: dimgrey;
  padding-left: 3px;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #e1e1e1;
  color: dimgrey;
  text-align: left;
  font-size: 15px;
  padding: 5px;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate-2 !important;
}

.iframe_maps {
  display: table;
  padding: 20px 0px;
}

.location-info-and-form-wrapper {
  margin: 20px 0px;
  background: white;
  box-sizing: border-box;
  display: table;
  padding: 20px 0px;
}

#contactContent .contInput{
  width: 94%;
}

.form-contact #contact-button-wrapper{
  margin-right: 0px;
}


/*============= Mis Reservas  ===========*/
form#my-bookings-form {
  margin-top: 30px;
}

.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate-1;
    text-transform: uppercase;
  }
  input, .bordeSelect {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    color: black;
    border: none;
    background: $gray-4;
  }

  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

button#cancelButton {
  width: 260px;
  color: white;
  background-color: $corporate-1;
  padding: 10px 0;
  border: none;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: $corporate-2;
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;
    margin-left: 470px;

    &:hover {
      background-color: $corporate-2;
    }
  }
}

#reservation div.grid_12.alpha.my-bookings-booking-info {
  margin-left: 320px !important;
}

.section-title + div {
  color: #918f8f;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  text-align: center;
  margin-top: 25px;
}

.content-access h3.section-title {
  color: $corporate_1;
  font-weight: lighter;
  font-size: 34px;
  text-transform: uppercase;
  margin-bottom: 29px;
  text-align: center;

  strong {
    font-weight: bolder;
  }
}


/*=============== Footer ================*/

.prefooter{
  background:$corporate-2;
  padding:40px;
  color:white;
  overflow: hidden;
}
#title_newsletter{
  font-size:28px;
  text-transform: uppercase;
  display:inline-block;
  font-weight:300;
  margin-right:20px;
  vertical-align:top;
}
#suscEmailLabel{
  display:none !important;
}
#form-newsletter,
#suscEmail,
#newsletterButtonExternalDiv
{
  display: inline-block;
}
#suscEmail{
  width: 300px;
  padding: 7px;
  margin-right:5px;
  float: left;
  border: none;
  background-color: #f0f0f0;
  outline: none;
}

#newsletterButtonExternalDiv button{
  background: $corporate-1;
  padding: 7px 20px;
  border: none;
  color: white;
  cursor: pointer;
  width:120px;
  vertical-align:super;
}

.newsletter_checkbox {
  font-size: 12px;

  a {
    color: white;
  }
}
#social{
  img{
    vertical-align:bottom;
  }
  span{
    display:inline-block;
    vertical-align:text-bottom;
    margin-right:20px;
    font-size:26px;
    font-weight:300;
  }
}

footer{
  background:black;
  text-align:center;
  overflow: hidden;

  a{
    text-decoration:none;
    color:$gray-3;
  }

  .menu-footer{
    padding:20px;
  }
  .menu-footer li{
    display: inline-block;
  }
  .menu-footer a{
    text-decoration:none;
    color:$gray-2;
    padding:7px 15px;
    text-transform:uppercase;
    font-size:14px;

    &:hover{
      color:$gray-3;
    }
  }
}



/*====== Booking Widget Inline =====*/

.booking_widget.inline {
  position: relative;
  top: 0;
  height: auto;
  padding-bottom: 13px;
  width: 1140px;
  margin-top: 0px;
  //display: none;

  .border_booking{
    width:initial !important;
  }

  .destination_wrapper {
    position: absolute;
    width: 255px;
    left: 0;
    top: 75px;
  }

  .entry_date_wrapper {
    margin-right: 10px;
    width: 100px !important;
  }
  .departure_date_wrapper {
    margin-right: 10px;
    width: 100px !important;
  }

  .room_list_wrapper {
    position: relative;
    display: inline-block;
  }
  .room_list {
    min-height: 96px;
  }

  li.room.room2, li.room.room3, li.room.room4 {
    margin-top: 5px;
  }

  .room_title {
    float: left !important;
    width: auto !important;
    display: block !important;
    top: 47px !important;
    position: relative !important;
  }

  .room {
    padding-left: 0px;
    height: 96px;
  }

  .wrapper_booking_button {
    float: right;
    width: 350px;
    display: inline-block;
    padding-top: 13px;
    margin-top: 23px;
  }

  button.submit_button {
    height: 35px;
  }

  .wrapper_booking_button .promocode_text {
    color: white;
    position: relative;
    width: 138px;
    font-size: 13px;
    text-decoration: none;
    border-bottom: 1px solid white;
    padding-bottom: 4px;
    display: none;
  }

  .wrapper_booking_button .promocode_text strong {
    color: white;
  }

  .promocode_input {
    position: relative;
    height: 35px;
    float: left;
    display: inline-block !important;
    background: #eaebed;
    font-size: 13px;
    resize: none;
    width: 170px !important;
  }

  .hotel_selector {
    z-index: 20;
    left: 0px;
    top: 125px;
    border: 2px solid #C5C330;
  }

  .booking_form {
    background: none;
    float: right;
    padding-top: 0px;
    width: 860px;
    height: auto;
    margin-right: 12px !important;
    padding-bottom:0px;
  }

  .booking_form_title {
    width: auto;
    float: left;
    margin-top: 22px;

    &:before {
      display: none;
    }
  }

  h4.best_price {
    display: none;
  }

  .booking_title_custom {
    font-size: 22px;
    font-weight: 400;
    font-family: 'Roboto', serif;
  }

  .date_box {
    //height: 110px !important;
  }

  .date_box .date_year {
    margin-top: 2px;
  }

  .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
    width: 78px;

  }
  .stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label {
    color: white;
    text-align: center;
    margin: 0px 0px 13px;
  }

}

#wrapper_booking.inline {
  background: $corporate-2;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: auto;
  z-index: 1000;
  height: auto;
  display: none;

  .booking_widget.inline {

    .wrapper-new-web-support {
      margin-top: -15px !important;
    }
    .booking_form_title {
      background: none;
      margin-right: 40px;

      h4 {
        color: white;
        font-size: 20px;
        text-transform: uppercase;
        margin-top:15px;
        margin-left:40px;
      }
    }

    .date_box {
      background: #eaebed;
      position: relative;
      height:76px;

      &:before {
        display: none;
      }

      .date_day {
        .day {
          font-size: 22px;
          padding:0px;
        }

        .month {
          line-height: 22px;
          padding-bottom: 0px;
          margin-top: 2px;
        }
      }
    }
  }
  .selectric .label {
    display: block !important;
    top: 19px;
  }
  .selectric .less_room_button{
    bottom:5px;
  }
  .selectric .more_room_button{
    top:5px;
  }
  .booking_form .stay_selection {
    display: block;
    padding-top: 5px;
  }

  .selectricWrapper .selectric {
    background: #eaebed;
    height: 75px !important;
    width: 66px !important;
  }

  .booking_widget.inline .stay_selection .entry_date_wrapper label, .booking_widget.inline .stay_selection .departure_date_wrapper label, .booking_widget.inline .stay_selection .rooms_number_wrapper label, .booking_widget.inline .room .room_title, .booking_widget.inline .room .adults_selector label, .booking_widget.inline .room .children_selector label {
    color: white;
    margin-bottom: 2px !important;
  }

  .selectric:before {
    display: none !important;
  }

  .entry_date_wrapper, .departure_date_wrapper {
    width: 66px !important;
  }

  .stay_selection label {
    margin-bottom: 2px !important;
  }

  .booking_widget .rooms_number .selectric {
    height: 75px !important;
    width: 66px !important;
  }

  .stay_selection .rooms_number_wrapper {
    width: 65px;
  }

  .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper, .selectricWrapper {
    margin-right: 10px;
    width: 65px;
  }

  .room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector {
    width: 65px;
  }

  .room_title {

    padding-right: 15px;
    padding-left: 20px !important;
  }

  .booking_widget .selectric .button, #data .selectric .button {
    display: none !important;
  }

  .selectric .label {
    margin-left: auto !important;
    left: 0;
    right: 0;
    font-size: 24px !important;
  }

  .booking_form .wrapper_booking_button button {
    height: 37px;
    width: 150px;
    font-size: 18px;
    float: right;
    margin-left: 8px;
    background:$corporate-1;
  }

  .room .adults_selector {
    margin-right: 10px;
  }
  span#full-booking-engine-html {
    margin: auto !important;
  }
}

#ui-datepicker-div {
  //position: absolute !important;
  z-index: 1005 !important;
}

.popup-start .fancybox-outer {
  padding: 0px !important;
  background: none;
  box-shadow: none;
}

@-moz-document url-prefix() {

  .rooms_number .selectric .label {
    top: 19px;
  }
  .selectric .label {
    top: 0px;
  }

  .inline .rooms_number .selectric .label {
    top: 19px !important;
    left: 1px !important;
  }
  .inline .selectric .label {
    top: 18px !important;
  }
}

.info{

    .title{
        a{
             color: black;
        }
    }

}