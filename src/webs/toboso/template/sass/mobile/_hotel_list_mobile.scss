.hotel_list_wrapper {
  position: fixed;
  top:0;left: 0;bottom: 0;right: 0;
  z-index: 9000;
  background-color: rgba($corporate_1,0.8);
  .overlay {
    position: absolute;
    top:0;left: 0;bottom: 0;right: 0;
  }
  .selector_view {
    @include center_xy;
    width: 800px;
    max-height: 90%;
    background-color: white;
    .selector_view_footer {
      clear: both;
      display: table;
      width: 100%;
      box-sizing: border-box;
      padding: 10px;
      background-color: $corporate_1;
      .search_hotels {
        position: relative;
        width: 50%;
        box-sizing: border-box;
        padding: 0 30px 0 20px;
        display:inline-block;
        .search_hotels_selector {
          font-size: 14px;
          width: 100%;outline: 0;
          border-width: 0;
          color: $corporate_1;
          background-color: white;
          box-sizing: border-box;
          padding: 9px 10px 8px;
          &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
            color: $corporate_1;
          }
          &::-moz-placeholder { /* Firefox 19+ */
            color: $corporate_1;
          }
          &:-ms-input-placeholder { /* IE 10+ */
            color: $corporate_1;
          }
          &:-moz-placeholder { /* Firefox 18- */
            color: $corporate_1;
          }
        }
        &:after {
            content: '\f107';
            font-family: "fontawesome", sans-serif;
            display: block;
            background-color: $corporate_1;
            padding: 9px 12px;
            color: white;
            @include center_y;
            right: 30px;
          }
        .all_hotels_list_search {
          position: absolute;
          left: 20px;
          z-index: 3;
          padding: 10px;
          bottom: 35px;
          width: 340px;
          box-sizing: border-box;
          background-color: #F0F0F0;
          overflow: auto;
          a {
            position: relative;
            display: inline-block;
            cursor: pointer;
            color: $corporate_1;
            padding: 0 3px 3px;
            font-size: 11px;
            @include transition(all,0.6s);
            br { display: none; }
            strong { font-weight: normal;color: #ccc }
            &:hover {
             color: $corporate_2;
              strong {
                color: #ccc;
              }
            }
          }
        }
      }
      .close_hotel_selector {
        float: right;
        background-color: $corporate_1;
        padding: 7px 12px;
        color: white;
        cursor: pointer;
      }
    }
    .group {
      width: 50%;
      float: left;
      .group_image {
        position: relative;
        background-color: black;
        width: 100%;
        height: 100px;
        overflow: hidden;
        img {
          @include center_xy;
          opacity: .5;
        }
        h2 {
          @include center_xy;
          width: 80%;
          text-transform: uppercase;
          font-weight: lighter;
          text-align: center;
          border: 1px solid white;
          padding: 10px 0;
          color: white;
          font-size: 16px;
          .group_counter {
          }
        }
      }
      .group_destiny {
        width: 100%;
        margin-top: 20px;
        height: 405px;
        overflow: auto;
      }
      .destiny {
        padding:5px 30px;
        h3 {
          position: relative;
          text-align: center;
          border: 1px solid $corporate_1;
          padding: 5px;
          margin-bottom: 3px;
          font-size: 14px;
          color: $corporate_1;
        }
        a {
          position: relative;
          display: block;
          cursor: pointer;
          color: $corporate_2;
          padding: 2px;
          font-size: 12px;
          text-align: center;
          @include transition(all,0.6s);
          br { display: none; }
          &:hover {
           color: $corporate_1;
          }
        }
      }
    }
  }
}
.hotels_filter_wrapper {
  position: absolute;
  top:100px;
  left: 0;
  right: 0;
  padding-top: 10px;
  width: 100%;
  background: rgba(black,.8); /* For browsers that do not support gradients */
  background: linear-gradient(rgba(black,0), rgba(black,.8)); /* Standard syntax (must be last) */

  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: block;
    padding: 10px;
    margin: 5px auto;
    width: calc(100% - 2em);
    background-color: $corporate_3;
    color: white;
    text-align: center;
    border-width: 0;
  }
  a {
    display: inline-block;
    color: white;
    font-size: 0.588em;
    text-transform: uppercase;
    padding: 2em 0 1em;
    margin-right: 1em;
    border-bottom: 5px solid transparent;
    &.filter.active, &.filter:hover {
      border-bottom-color: $corporate_2;
    }
    &.right_link {
      position: relative;
      float: right;
      padding: 8px 35px 4px 0;
      .fa {
        position: absolute;
        top:0;right: 0;
        background-color: white;
        padding: 10px;
        color:$corporate_2;
      }
      &:hover, &.active {
        .fa {
          color: $corporate_1;
        }
      }
    }
  }
}

.map {
  margin: 75px 0;

  iframe {
    width: 100%;
  }
}
.all_hotels_map_wrapper {
  .gm-style-iw{
    strong {
      font-weight: bold;
    }
    a {
      margin-top: 10px;
      display: block;
      padding: 5px 10px;
      background-color: $corporate_2;
      color: white !important;
      text-decoration: none;
      text-align: center;
      @include transition(all,.6s);
      &:hover {
        background-color: $corporate_1;
      }
    }
  }

}
.hotels_wrapper {
  padding: 75px 0;
  .hotel {
    display: block;
    vertical-align: top;
    margin: 0 auto;
    width: 100%;
    .hotel_image {
      background-color: black;
      position: relative;
      width: 100%;
      height: 400px;
      overflow: hidden;
      @include transition(all, .6s);
      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
        @include transition(all, .6s);
      }
      .price {
        position: absolute;
        bottom: 6.250em;
        right: 20px;
        padding: 10px;
        background-color: rgba(0,0,0,0.6);
        font-size: 14px;
        color: white;
        span {
          font-size: 30px;
          font-weight: bold;
        }
      }
    }

    .hotel_info {
      background-color: white;
      padding: 20px;
      font-size: 12px;
      position: relative;
      text-align: left;
      color: #4b4b4b;
      margin: -6.250em 1.250em 1.875em 1.250em;
      @include transition(all, 1s);
      .new_icon {
        position: absolute;
        top: 20px;
        right: 20px;
      }
      h2 {
        color: $corporate_1;
        text-transform: uppercase;
        font-weight: lighter;
        font-size: 20px;
        margin-bottom:5px;
        br {display: none;}
      }
      h3 {
        color: #666;
        font-size: 16px;
        font-weight: normal;
        margin-bottom:10px;
      }
      p {
        font-size: 12px;
        font-family: "Open Sans", sans-serif;
      }
      .half {
        display: inline-block;
        vertical-align: bottom;
        width: calc(50% - 2px);
        &.servis {
           color: $corporate_1;
           a {
             color: $corporate_1;
             &:hover {
              text-decoration: underline;
             }
           }
          .fa {
            display: inline-block;
            vertical-align: middle;
            margin-right: 3px;
            margin-bottom: 3px;
            position: relative;
            width: 15px;
            height: 15px;
            border: 1px solid $corporate_3;
            color: $corporate_3;
            border-radius: 50%;
            &:before {
              @include center_xy;
            }
          }
        }
        .booking_button {
          display: block;
          padding: 5px 10px;
          text-align: center;
          text-transform: uppercase;
          color: white;
          margin-top: 10px;
          background-color: $corporate_1;
        }
      }
    }
    &.destacado {
      .hotel_info {
        background-color: $corporate_1;
        h2 {
          color: $corporate_1;
        }
        h3, p {
          color: #ddd;
        }
        .price {
          span {
            color: white;
          }
        }
        .button-promotion {
          &:hover {
            background-color: $corporate_1;
          }
        }
      }
    }
  }
}