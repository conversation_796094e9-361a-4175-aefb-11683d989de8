{# Content by Subtitle #}
{% if content_subtitle %}
    <div class="content_subtitle_wrapper {% if interior %}interior {% else %} effects_sass{% endif %} {% if content_full_size %}full_size{% endif %}"
         {% if not interior %}sass_effect="slide_up_effect"{% endif %}>
        <h3 class="subtitle_title">{{ content_subtitle.subtitle|safe }}</h3>

        <div class="subtitle_description">
            {{ content_subtitle.content|safe }}
        </div>
    </div>
{% endif %}


{# Content Access #}
{% if content_access %}
    <div class="content_access">
        {{ content }}
    </div>
{% endif %}


{# Individual Offers #}
{% if individual_offer %}
    <div class="individual_offer_wrapper">
        <div class="button-promotion" href="#data">{{ T_reservar }}</div>
        <div class="share-this">
            <p class="share">{{ Compartir }} </p>
            <!-- Social -->
            <div class="addthis_toolbox addthis_default_style" style="margin-top:40px;padding-top:5px;">
                <a href="http://www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                   class="addthis_button_compact" style="color:#5A5655">{{ T_compartir }}</a>
                <a class="addthis_button_google_plusone_share"></a>
                <a class="addthis_button_facebook"></a>
                <a class="addthis_button_google"></a>
                <a class="addthis_button_twitter"></a>
                <a class="addthis_button_favorites"></a>
            </div>
            <script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>
        </div>
    </div>
{% endif %}


{#Localizacion y contacto#}
{% if contact_html %}
    <div class="location-info-and-form-wrapper">
        <div class="location-info column6">
            {% if img_4_title %}
                <h1>{{ img_4_title|safe }}</h1>
                <span></span>
            {% else %}
                <h1>{{ location_html.title|safe }}</h1>
            {% endif %}

            {{ location_html.content|safe }}

        </div>

        <div class="form-contact column6">
            <h1>{{ subtitle_form|safe }}</h1>
            {{ contact_html }}
        </div>


    </div>

    {% if iframe_google_map %}
        <div class="iframe-google-maps-wrapper">
            {{ iframe_google_map.content|safe }}

        </div>
    {% endif %}

{% endif %}




{#Ofertas#}
{% if blocks %}
    <div class="scapes-blocks">
        {% for block in blocks %}
            <div class="block {% cycle 'row1' 'row2' %}">
                <div class="description">
                    <h3 class="title-module">{{ block.name|safe }}</h3>
                    <ul>
                        <li><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}"
                               class="button-promotion oferta-reserva" {% if block.smartDatasAttributes %}{{ block.smartDatasAttributes|safe }}{% endif %}>{{ T_reservar }}</a></li>
                        <li><a href="{{ block.link_section }}"
                               class="plus">{{ T_ver_mas }}</a></li>
                    </ul>
                </div>
                <a href="#event-modal-{{ forloop.counter }}" id="scapesmobileversion-{{ forloop.counter }}"
                   class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{% endif %} enlace_offer">
                    <img src="{{ block.picture }}=s1000">
                </a>

                <div id="event-modal-{{ forloop.counter }}"
                     class="mobiledescription event-modal-mobile-{{ forloop.counter }}"
                     style="display: none; padding: 10px">
                    {{ block.description|safe }}
                </div>
            </div>

        {% endfor %}
    </div>
{% endif %}


{#Habitaciones#}
{% if rooms %}
    <div class="room_wrapper">
        {% for room in rooms %}
            <div class="rooms column6 {% cycle 'blockleft-room' 'blockright-room' %}"
                 id="room_block_{{ forloop.counter }}">
                <a href="javascript:showGallery2([ {% for picture in room.gallery %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.newtitle|safe }}'}, {% endfor %} ]);"
                   class="rooms-img">
                    <img src="/img/{{ base_web }}/ico_fotos_blocks.png" class="ico_cam_room">
                    <img src="{{ room.gallery.0.servingUrl }}=s560" class="room_img">
                </a>

                <div class="rooms-description">
                    <h3 class="title-module"><span class="destino">{{ room.name|safe }}</span></h3>

                    <div class="room_description">
                        {{ room.description|safe }}
                    </div>
                    <div class="room-links">
                        <span class="btn-corporate"><a href="{% if i_am_mobile %}/{% else %}#data{% endif %}" data-hotelname="{{ room.name|striptags|safe }}" data-namespace="{{ room.namespace|safe }}" class="button-promotion">{{ T_reservar }}</a></span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <script async>
        $(function () {
            $(".room_description").each(function () {
                if ($(this).height() > 120) {
                    $(this).attr("real_height", $(this).height()).height(120);
                    $(this).parent().append("<span class='btn_vermas_room'>{{ T_ver_mas }}</span>");
                } else {
                    $(this).height(150);
                }
            })

            $(".btn_vermas_room").click(function () {
                button = $(this);
                hide_description = $(this).siblings(".room_description");

                if (button.hasClass("open")) {
                    hide_description.animate({height: 120});
                    button.removeClass("open");
                } else {
                    hide_description.animate({height: hide_description.attr("real_height")});
                    button.addClass("open");
                }
            })
        })
    </script>
{% endif %}

{# Services Blocks #}
{% if banners_cycle %}
    <div class="blocks_section_container">
        {% for block in banners_cycle %}
            <div class="block-activity {% cycle 'block-1' 'block-2' 'block-3' %}">

                <h4 class="block_activity_title">{{ block.title|safe }}</h4>

                <div class="activity_home_image">
                    {% if block.pictures %}
                        <a class="cboxelement" id="image_rooms"
                           href="javascript:showGallery([ {% for picture in block.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{ picture.name|safe }}'}, {% endfor %} ]);">
                            <img src="/img/{{ base_web }}/icono_fotos_habitaciones.png" class="room-img-lupa">
                            <img src="{{ block.servingUrl|safe }}=s370">
                        </a>
                    {% else %}
                        <img src="{{ block.servingUrl|safe }}=s370">
                    {% endif %}
                </div>

                <input type="hidden" name="descripcion_deployed" value="0" class="descripcion_deployed"
                       id="descripcion_deployed_{{ forloop.counter }}">

                <div class="room_home_description">
                    <span class="description_block">{{ block.description|safe }}</span>
                </div>

                {% if im_mobile %}
                    <div class="buttons-rooms">
                        <a href="{% if im_mobile %}/{% else %}#data{% endif %}" class="button-room-more">
                            <button>{{ T_ver_mas }}</button>
                        </a>
                    </div>
                {% endif %}

            </div>

        {% endfor %}
    </div>

    <script async>
        $(window).load(function(){
            $(".room_home_description .description_block").each(function(){
                if($(this).height() > 80){
                    $(this).attr("real_height", $(this).outerHeight()).height(75).parent().append("<div class='see_more'>{{ T_leer_mas }}</div>");
                } else {
                    $(this).height(104);
                }

            });

            $(".see_more").click(function(){
                button = $(this);
                description = $(this).parent().find(".description_block");

                if(button.hasClass("open")){
                    description.animate({height: 115 });
                    button.text("{{ T_leer_mas }}").removeClass("open");
                } else {
                    description.animate({ height: description.attr("real_height") });
                    button.text("{{ T_leer_menos }}").addClass("open");
                }
            })
        })
    </script>
{% endif %}

{# Cycle Blocks #}
{% if event_blocks %}
<div class="cycle_blocks_wrapper show_with_scroll">
    {% for block in event_blocks %}
        <div class="cycle_block">
            <div class="cycle_image">
                <img src="{{ block.servingUrl|safe }}">
            </div>
            <div class="cycle_content">
                <h2>{{ block.title|safe }}</h2>
                {{ block.description|safe }}
            </div>
            {% if block.linkUrl %}
            <div class="cycle_link">
                <a href="{{ block.linkUrl|safe }}">{{ T_ver_mas }}</a>
            </div>
            {% endif %}
        </div>
    {% endfor %}
</div>
{% endif %}

{% if banner_index %}
    <div class="banner_index_wrapper effects_sass" sass_effect="slide_up_effect">
        <div class="container12">
            <div class="banner_content">
                <div class="img_left">
                    <img src="{{ banner_index.pictures.0 }}" alt=""/>
                </div>
                <div class="text">
                    <div class="icon"><i class="fa fa-map-marker"></i></div>
                    <div class="title">{{ banner_index.subtitle|safe }}</div>
                    <div class="description">{{ banner_index.content|safe }}</div>
                </div>
                <div class="img_right">
                    <img src="{{ banner_index.pictures.1 }}" alt=""/>
                </div>
            </div>
        </div>
    </div>
{% endif %}

{% if news_entries %}{% include "banners/_blog.html" %}{% endif %}
{% if individual_news_entry %}{% include "banners/_blog_entry.html" %}{% endif %}

{% if mini_gallery %}
    <div class="minigallery_index_wrapper effects_sass" sass_effect="slide_up_effect">
        <div class="content">
            <div class="container12">
                <div class="title">{{ mini_gallery.subtitle|safe }}</div>

                <div class="control_gallery">
                    <div class="left_arrow arrow"><img src="/img/{{ base_web }}/next_arrow.png" alt=""/></div>
                    <div class="right_arrow arrow"><img src="/img/{{ base_web }}/next_arrow.png" alt=""/></div>
                </div>
            </div>
        </div>

        <div class="minigallery">
            <ul class="slides">
                {% for x in mini_gallery.pictures %}
                    <li class="minigallery_element"><a href="{{ x|safe }}=s1900" rel="lightbox[gallery]"><img src="{{ x|safe }}=s380-c" alt=""
                                                                                  rel="lightbox[gallery]"/></a>
                    </li>
                {% endfor %}

            </ul>
        </div>
    </div>

    <script async>
        $(window).load(function () {
             flex_params = {
                controlNav: false,
                directionNav: true,
                animation: "slide",
                itemWidth: ($(window).width() / 5),
                move: 1,
                minItems: 5,
                maxItems: 5
            }
            $('.minigallery_index_wrapper .minigallery').flexslider(flex_params);

            $(".minigallery_index_wrapper .control_gallery .left_arrow").click(function () {
                $(".minigallery_index_wrapper .minigallery .flex-direction-nav .flex-nav-prev .flex-prev").trigger('click');
            });

            $(".minigallery_index_wrapper .control_gallery .right_arrow").click(function () {
                $(".minigallery_index_wrapper .minigallery .flex-direction-nav .flex-nav-next .flex-next").trigger('click');
            });

            $(".minigallery .minigallery_element").height($(window).width()/5);
            $(window).resize(function() {
                $(".minigallery .minigallery_element").height($(window).width()/5);
            })
        })

    </script>
{% endif %}

